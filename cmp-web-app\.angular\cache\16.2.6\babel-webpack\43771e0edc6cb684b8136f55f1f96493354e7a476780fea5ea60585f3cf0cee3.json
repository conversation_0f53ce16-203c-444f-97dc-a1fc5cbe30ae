{"ast": null, "code": "export default {\n  label: {\n    config: {\n      provinceName: \"Tên tỉnh\",\n      provinceCode: \"<PERSON>ã tỉnh\",\n      email: \"Danh sách email\",\n      clue: \"Đầu mối\"\n    },\n    customerName: \"Họ và tên liên hệ\",\n    email: \"Email liên hệ\",\n    phone: \"<PERSON><PERSON> điện thoại liên hệ\",\n    content: \"Nội dung\",\n    type: \"Loại yêu cầu\",\n    createdDate: \"Ngày tạo\",\n    status: \"Trạng thái\",\n    typeRequest: \"Loại yêu cầu\",\n    changeSim: \"<PERSON><PERSON> thuê bao thay đổi\",\n    note: \"<PERSON><PERSON> chú\",\n    noteAdmin: \"<PERSON>hi chú của admin\",\n    createRequest: \"Tạo mới yêu cầu\",\n    updateRequest: \"Cập nhật trạng thái xử lý\",\n    transferProcessing: 'Chuyển xử lý',\n    province: 'Tỉnh/Thành phố',\n    fullName: '<PERSON><PERSON> và tên liên hệ',\n    requestConfigUpdate: \"<PERSON><PERSON><PERSON> đầu mối tỉnh\",\n    emailSearch: 'Email',\n    address: 'Địa chỉ',\n    quantity: '<PERSON><PERSON> lượng SIM',\n    listImsi: '<PERSON><PERSON>n IMSI',\n    orderAddress: 'Địa chỉ đặt hàng',\n    detailAddress: 'Địa chỉ chi tiết',\n    district: 'Quận/Huyện',\n    commune: 'Phường/Xã',\n    updateBy: 'Người cập nhật',\n    dateFrom: \"Từ ngày\",\n    dateTo: \"Đến ngày\",\n    imsi: \"IMSI\",\n    allocationDate: \"Ngày cấp phát\",\n    activedDate: \"Ngày kích hoạt\",\n    notActivated: \"Chưa kích hoạt\",\n    awaitingActivation: \"Đang chờ kích hoạt\",\n    activated: \"Đã kích hoạt\",\n    updateOrderSim: \"Cập nhật trạng thái yêu cầu\",\n    processingNotes: \"Ghi chú xử lý\",\n    orderHistory: \"Lịch sử đơn hàng\",\n    updatedDate: \"Ngày cập nhật\",\n    requestActiveSim: \"Tạo yêu cầu kích hoạt SIM\",\n    deliveryAddress: 'Địa chỉ nhận hàng',\n    viewOrderSim: 'Chi tiết yêu cầu đặt SIM',\n    viewActiveSim: 'Chi tiết yêu cầu kích hoạt SIM',\n    listNotes: \"Danh sách ghi chú xử lý\",\n    listImsis: \"Danh sách cấp SIM\",\n    listNote: \"Danh sách ghi chú xử lý\",\n    viewDetailReplaceSim: \"Chi tiết yêu cầu thay thế\",\n    viewDetailTestSim: \"Chi tiết yêu cầu thử nghiệm\",\n    time: \"Thời gian\",\n    implementer: \"Người thực hiện\",\n    historyOrder: \"Lịch sử theo dõi đơn hàng\",\n    generalInfo: \"Thông tin chung\",\n    enterImsi: \"Nhập thông tin IMSI\",\n    listactiveImsis: \"Danh sách IMSI\",\n    imsiByFile: \"Nhập IMSI bằng file\",\n    viewDetailDiagnose: \"Chi tiết yêu cầu chẩn đoán\"\n  },\n  menu: {\n    config: \"Danh sách cấu hình\",\n    requestMgmt: \"Quản lý yêu cầu\",\n    requestConfig: \"Cấu hình đầu mối tỉnh\",\n    requestList: \"Danh sách yêu cầu\",\n    detail: \"Xem chi tiết\",\n    testSim: 'Yêu cầu thử nghiệm',\n    replaceSim: 'Yêu cầu thay thế SIM',\n    orderSim: 'Yêu cầu đặt SIM',\n    activeSim: 'Yêu cầu kích hoạt SIM',\n    listIssuedSim: \"Danh sách SIM được cấp\",\n    errorNote: \"Nội dung lỗi\",\n    diagnose: \"Yêu cầu chẩn đoán\"\n  },\n  text: {\n    selectEmail: \"Chọn email\",\n    addImsi: \"Thêm Imsi\"\n  },\n  status: {\n    new: 'Mới',\n    received: 'Đã nhận',\n    inProgress: 'Đang xử lý',\n    reject: 'Từ chối',\n    done: 'Hoàn thành'\n  },\n  type: {\n    replaceSim: 'Thay thế SIM',\n    testSim: 'Test SIM',\n    orderSim: 'Đặt SIM',\n    activeSim: 'Kích hoạt SIM'\n  },\n  message: {\n    invalidPhone: 'Số điện thoại phải là số có đầu 0 (10-11 kí tự) hoặc 84 (11-12 kí tự)',\n    noteChangeSim: 'Được phép nhập nhiều số thuê bao, các số ngăn cách nhau bằng dấu phẩy',\n    minQuantity: 'Giá trị nhỏ nhất là 1',\n    invalidListImsi: 'Định dạng không chính xác, Dãy imsi ngăn cách nhau bởi dấu phẩy',\n    large: \"Danh sách file lơn hơn \\${limitRow}\\ dòng\",\n    empty: \"Danh sách đang trống\",\n    searchInfoNull: \"Hãy nhập thông tin tìm kiếm trước khi tải file\",\n    largeFile: \"Dung lượng file không được vượt quá 1MB\",\n    emptyFile: \"File trống thông tin\",\n    redundantColumns: \"File tải lên thừa cột\",\n    missingColumns: \"File tải lên thiếu cột\",\n    wrongSample: \"Sai định dạng file mẫu\",\n    wrongImsiFormat: \"IMSI sai định dạng, IMSI phải là ký tự số và không được vượt quá 18 ký tự\",\n    missingImsiInfo: \"Thiếu thông tin IMSI\",\n    imsiNotExist: \"IMSI không tồn tại\",\n    imsiIsActivated: \"IMSI đã kích hoạt\",\n    downloadFile: \"Tải file về\",\n    isError: \"Đã có lỗi xảy ra!\",\n    isDownloadMessage: \"File tải lên sai thông tin, vui lòng sửa lại!\",\n    uploadFile: \"Import file\",\n    maxQuantity: \"Trường này không được vượt quá 5 ký tự số\",\n    imsiMaxLength: \"Trường này không được vượt quá 18 ký tự số\",\n    imsiIsExist: \"IMSI đã tồn tại trong danh sách\",\n    hintValidPhone: \"Bạn có thể nhấn Enter hoặc dấu phẩy để thêm số điện thoại\"\n  },\n  diagnose: {\n    label: {\n      name: \"Họ và tên\",\n      email: \"Email\",\n      phone: \"Số điện thoại\",\n      content: \"Nội dung yêu cầu\",\n      number: \"Số thuê bao chẩn đoán\",\n      diagnoseNumber: \"Số điện thoại chẩn đoán\"\n    }\n  }\n};", "map": {"version": 3, "names": ["label", "config", "provinceName", "provinceCode", "email", "clue", "customerName", "phone", "content", "type", "createdDate", "status", "typeRequest", "changeSim", "note", "noteAdmin", "createRequest", "updateRequest", "transferProcessing", "province", "fullName", "requestConfigUpdate", "emailSearch", "address", "quantity", "listImsi", "orderAddress", "detail<PERSON><PERSON><PERSON>", "district", "commune", "updateBy", "dateFrom", "dateTo", "imsi", "allocationDate", "activedDate", "notActivated", "awaitingActivation", "activated", "updateOrderSim", "processingNotes", "orderHistory", "updatedDate", "requestActiveSim", "deliveryAddress", "viewOrderSim", "viewActiveSim", "listNotes", "listImsis", "listNote", "viewDetailReplaceSim", "viewDetailTestSim", "time", "implementer", "historyOrder", "generalInfo", "enterImsi", "listactiveImsis", "imsiByFile", "viewDetailDiagnose", "menu", "requestMgmt", "requestConfig", "requestList", "detail", "testSim", "<PERSON><PERSON><PERSON>", "orderSim", "activeSim", "listIssuedSim", "errorNote", "diagnose", "text", "selectEmail", "addImsi", "new", "received", "inProgress", "reject", "done", "message", "invalidPhone", "noteChangeSim", "minQuantity", "invalidListImsi", "large", "empty", "searchInfoNull", "largeFile", "emptyFile", "redundantColumns", "missingColumns", "wrongSample", "wrongImsiFormat", "missingImsiInfo", "imsiNotExist", "imsiIsActivated", "downloadFile", "isError", "isDownloadMessage", "uploadFile", "maxQuantity", "imsiMaxLength", "imsiIsExist", "hintValidPhone", "name", "number", "diagnoseN<PERSON>ber"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\vi\\ticket.ts"], "sourcesContent": ["export default {\r\n    label: {\r\n        config: {\r\n            provinceName: \"Tên tỉnh\",\r\n            provinceCode: \"<PERSON>ã tỉnh\",\r\n            email: \"<PERSON>h sách email\",\r\n            clue: \"Đầu mối\",\r\n        },\r\n        customerName : \"Họ và tên liên hệ\",\r\n        email : \"Email liên hệ\",\r\n        phone : \"Số điện thoại liên hệ\",\r\n        content : \"Nội dung\",\r\n        type : \"Loại yêu cầu\",\r\n        createdDate : \"Ngày tạo\",\r\n        status : \"Trạng thái\",\r\n        typeRequest : \"Loại yêu cầu\",\r\n        changeSim : \"<PERSON><PERSON> thuê bao thay đổi\",\r\n        note : \"<PERSON><PERSON> chú\",\r\n        noteAdmin: \"<PERSON>hi chú của admin\",\r\n        createRequest : \"Tạo mới yêu cầu\",\r\n        updateRequest : \"Cập nhật trạng thái xử lý\",\r\n        transferProcessing : 'Chuyển xử lý',\r\n        province: 'Tỉnh/Thành phố',\r\n        fullName : '<PERSON>ọ và tên liên hệ',\r\n        requestConfigUpdate : \"<PERSON><PERSON><PERSON> đầu mối tỉnh\",\r\n        emailSearch : 'Email',\r\n        address: 'Địa chỉ',\r\n        quantity: '<PERSON><PERSON> lượng SIM',\r\n        listImsi: '<PERSON><PERSON><PERSON> IMSI',\r\n        orderAddress: 'Địa chỉ đặt hàng',\r\n        detailAddress: 'Địa chỉ chi tiết',\r\n        district: 'Quận/Huyện',\r\n        commune: 'Phường/Xã',\r\n        updateBy: 'Người cập nhật',\r\n        dateFrom: \"Từ ngày\",\r\n        dateTo: \"Đến ngày\",\r\n        imsi: \"IMSI\",\r\n        allocationDate: \"Ngày cấp phát\",\r\n        activedDate: \"Ngày kích hoạt\",\r\n        notActivated: \"Chưa kích hoạt\",\r\n        awaitingActivation: \"Đang chờ kích hoạt\",\r\n        activated: \"Đã kích hoạt\",\r\n        updateOrderSim: \"Cập nhật trạng thái yêu cầu\",\r\n        processingNotes: \"Ghi chú xử lý\",\r\n        orderHistory: \"Lịch sử đơn hàng\",\r\n        updatedDate: \"Ngày cập nhật\",\r\n        requestActiveSim: \"Tạo yêu cầu kích hoạt SIM\",\r\n        deliveryAddress: 'Địa chỉ nhận hàng',\r\n        viewOrderSim: 'Chi tiết yêu cầu đặt SIM',\r\n        viewActiveSim: 'Chi tiết yêu cầu kích hoạt SIM',\r\n        listNotes: \"Danh sách ghi chú xử lý\",\r\n        listImsis: \"Danh sách cấp SIM\",\r\n        listNote : \"Danh sách ghi chú xử lý\",\r\n        viewDetailReplaceSim : \"Chi tiết yêu cầu thay thế\",\r\n        viewDetailTestSim : \"Chi tiết yêu cầu thử nghiệm\",\r\n        time : \"Thời gian\",\r\n        implementer :\"Người thực hiện\",\r\n        historyOrder : \"Lịch sử theo dõi đơn hàng\",\r\n        generalInfo: \"Thông tin chung\",\r\n        enterImsi: \"Nhập thông tin IMSI\",\r\n        listactiveImsis: \"Danh sách IMSI\",\r\n        imsiByFile: \"Nhập IMSI bằng file\",\r\n        viewDetailDiagnose: \"Chi tiết yêu cầu chẩn đoán\"\r\n    },\r\n    menu : {\r\n        config : \"Danh sách cấu hình\",\r\n        requestMgmt : \"Quản lý yêu cầu\",\r\n        requestConfig : \"Cấu hình đầu mối tỉnh\",\r\n        requestList :\"Danh sách yêu cầu\",\r\n        detail : \"Xem chi tiết\",\r\n        testSim : 'Yêu cầu thử nghiệm',\r\n        replaceSim : 'Yêu cầu thay thế SIM',\r\n        orderSim: 'Yêu cầu đặt SIM',\r\n        activeSim: 'Yêu cầu kích hoạt SIM',\r\n        listIssuedSim: \"Danh sách SIM được cấp\",\r\n        errorNote: \"Nội dung lỗi\",\r\n        diagnose: \"Yêu cầu chẩn đoán\",\r\n    },\r\n    text : {\r\n        selectEmail : \"Chọn email\",\r\n        addImsi: \"Thêm Imsi\",\r\n    },\r\n    status : {\r\n        new : 'Mới',\r\n        received : 'Đã nhận',\r\n        inProgress : 'Đang xử lý',\r\n        reject : 'Từ chối',\r\n        done : 'Hoàn thành'\r\n    },\r\n    type : {\r\n        replaceSim : 'Thay thế SIM',\r\n        testSim : 'Test SIM',\r\n        orderSim: 'Đặt SIM',\r\n        activeSim: 'Kích hoạt SIM',\r\n    },message : {\r\n        invalidPhone : 'Số điện thoại phải là số có đầu 0 (10-11 kí tự) hoặc 84 (11-12 kí tự)',\r\n        noteChangeSim : 'Được phép nhập nhiều số thuê bao, các số ngăn cách nhau bằng dấu phẩy',\r\n        minQuantity: 'Giá trị nhỏ nhất là 1',\r\n        invalidListImsi: 'Định dạng không chính xác, Dãy imsi ngăn cách nhau bởi dấu phẩy',\r\n        large: \"Danh sách file lơn hơn \\${limitRow}\\ dòng\",\r\n        empty: \"Danh sách đang trống\",\r\n        searchInfoNull: \"Hãy nhập thông tin tìm kiếm trước khi tải file\",\r\n        largeFile: \"Dung lượng file không được vượt quá 1MB\",\r\n        emptyFile: \"File trống thông tin\",\r\n        redundantColumns: \"File tải lên thừa cột\",\r\n        missingColumns: \"File tải lên thiếu cột\",\r\n        wrongSample: \"Sai định dạng file mẫu\",\r\n        wrongImsiFormat: \"IMSI sai định dạng, IMSI phải là ký tự số và không được vượt quá 18 ký tự\",\r\n        missingImsiInfo: \"Thiếu thông tin IMSI\",\r\n        imsiNotExist: \"IMSI không tồn tại\",\r\n        imsiIsActivated: \"IMSI đã kích hoạt\",\r\n        downloadFile: \"Tải file về\",\r\n        isError: \"Đã có lỗi xảy ra!\",\r\n        isDownloadMessage: \"File tải lên sai thông tin, vui lòng sửa lại!\",\r\n        uploadFile: \"Import file\",\r\n        maxQuantity: \"Trường này không được vượt quá 5 ký tự số\",\r\n        imsiMaxLength: \"Trường này không được vượt quá 18 ký tự số\",\r\n        imsiIsExist: \"IMSI đã tồn tại trong danh sách\",\r\n        hintValidPhone: \"Bạn có thể nhấn Enter hoặc dấu phẩy để thêm số điện thoại\",\r\n    },\r\n    diagnose: {\r\n      label: {\r\n          name: \"Họ và tên\",\r\n          email: \"Email\",\r\n          phone: \"Số điện thoại\",\r\n          content: \"Nội dung yêu cầu\",\r\n          number: \"Số thuê bao chẩn đoán\",\r\n          diagnoseNumber: \"Số điện thoại chẩn đoán\",\r\n      }\r\n    }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAE;IACHC,MAAM,EAAE;MACJC,YAAY,EAAE,UAAU;MACxBC,YAAY,EAAE,SAAS;MACvBC,KAAK,EAAE,iBAAiB;MACxBC,IAAI,EAAE;KACT;IACDC,YAAY,EAAG,mBAAmB;IAClCF,KAAK,EAAG,eAAe;IACvBG,KAAK,EAAG,uBAAuB;IAC/BC,OAAO,EAAG,UAAU;IACpBC,IAAI,EAAG,cAAc;IACrBC,WAAW,EAAG,UAAU;IACxBC,MAAM,EAAG,YAAY;IACrBC,WAAW,EAAG,cAAc;IAC5BC,SAAS,EAAG,sBAAsB;IAClCC,IAAI,EAAG,SAAS;IAChBC,SAAS,EAAE,mBAAmB;IAC9BC,aAAa,EAAG,iBAAiB;IACjCC,aAAa,EAAG,2BAA2B;IAC3CC,kBAAkB,EAAG,cAAc;IACnCC,QAAQ,EAAE,gBAAgB;IAC1BC,QAAQ,EAAG,mBAAmB;IAC9BC,mBAAmB,EAAG,kBAAkB;IACxCC,WAAW,EAAG,OAAO;IACrBC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,cAAc;IACxBC,QAAQ,EAAE,WAAW;IACrBC,YAAY,EAAE,kBAAkB;IAChCC,aAAa,EAAE,kBAAkB;IACjCC,QAAQ,EAAE,YAAY;IACtBC,OAAO,EAAE,WAAW;IACpBC,QAAQ,EAAE,gBAAgB;IAC1BC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,UAAU;IAClBC,IAAI,EAAE,MAAM;IACZC,cAAc,EAAE,eAAe;IAC/BC,WAAW,EAAE,gBAAgB;IAC7BC,YAAY,EAAE,gBAAgB;IAC9BC,kBAAkB,EAAE,oBAAoB;IACxCC,SAAS,EAAE,cAAc;IACzBC,cAAc,EAAE,6BAA6B;IAC7CC,eAAe,EAAE,eAAe;IAChCC,YAAY,EAAE,kBAAkB;IAChCC,WAAW,EAAE,eAAe;IAC5BC,gBAAgB,EAAE,2BAA2B;IAC7CC,eAAe,EAAE,mBAAmB;IACpCC,YAAY,EAAE,0BAA0B;IACxCC,aAAa,EAAE,gCAAgC;IAC/CC,SAAS,EAAE,yBAAyB;IACpCC,SAAS,EAAE,mBAAmB;IAC9BC,QAAQ,EAAG,yBAAyB;IACpCC,oBAAoB,EAAG,2BAA2B;IAClDC,iBAAiB,EAAG,6BAA6B;IACjDC,IAAI,EAAG,WAAW;IAClBC,WAAW,EAAE,iBAAiB;IAC9BC,YAAY,EAAG,2BAA2B;IAC1CC,WAAW,EAAE,iBAAiB;IAC9BC,SAAS,EAAE,qBAAqB;IAChCC,eAAe,EAAE,gBAAgB;IACjCC,UAAU,EAAE,qBAAqB;IACjCC,kBAAkB,EAAE;GACvB;EACDC,IAAI,EAAG;IACH3D,MAAM,EAAG,oBAAoB;IAC7B4D,WAAW,EAAG,iBAAiB;IAC/BC,aAAa,EAAG,uBAAuB;IACvCC,WAAW,EAAE,mBAAmB;IAChCC,MAAM,EAAG,cAAc;IACvBC,OAAO,EAAG,oBAAoB;IAC9BC,UAAU,EAAG,sBAAsB;IACnCC,QAAQ,EAAE,iBAAiB;IAC3BC,SAAS,EAAE,uBAAuB;IAClCC,aAAa,EAAE,wBAAwB;IACvCC,SAAS,EAAE,cAAc;IACzBC,QAAQ,EAAE;GACb;EACDC,IAAI,EAAG;IACHC,WAAW,EAAG,YAAY;IAC1BC,OAAO,EAAE;GACZ;EACD/D,MAAM,EAAG;IACLgE,GAAG,EAAG,KAAK;IACXC,QAAQ,EAAG,SAAS;IACpBC,UAAU,EAAG,YAAY;IACzBC,MAAM,EAAG,SAAS;IAClBC,IAAI,EAAG;GACV;EACDtE,IAAI,EAAG;IACHyD,UAAU,EAAG,cAAc;IAC3BD,OAAO,EAAG,UAAU;IACpBE,QAAQ,EAAE,SAAS;IACnBC,SAAS,EAAE;GACd;EAACY,OAAO,EAAG;IACRC,YAAY,EAAG,uEAAuE;IACtFC,aAAa,EAAG,uEAAuE;IACvFC,WAAW,EAAE,uBAAuB;IACpCC,eAAe,EAAE,iEAAiE;IAClFC,KAAK,EAAE,2CAA2C;IAClDC,KAAK,EAAE,sBAAsB;IAC7BC,cAAc,EAAE,gDAAgD;IAChEC,SAAS,EAAE,yCAAyC;IACpDC,SAAS,EAAE,sBAAsB;IACjCC,gBAAgB,EAAE,uBAAuB;IACzCC,cAAc,EAAE,wBAAwB;IACxCC,WAAW,EAAE,wBAAwB;IACrCC,eAAe,EAAE,2EAA2E;IAC5FC,eAAe,EAAE,sBAAsB;IACvCC,YAAY,EAAE,oBAAoB;IAClCC,eAAe,EAAE,mBAAmB;IACpCC,YAAY,EAAE,aAAa;IAC3BC,OAAO,EAAE,mBAAmB;IAC5BC,iBAAiB,EAAE,+CAA+C;IAClEC,UAAU,EAAE,aAAa;IACzBC,WAAW,EAAE,2CAA2C;IACxDC,aAAa,EAAE,4CAA4C;IAC3DC,WAAW,EAAE,iCAAiC;IAC9CC,cAAc,EAAE;GACnB;EACDjC,QAAQ,EAAE;IACRvE,KAAK,EAAE;MACHyG,IAAI,EAAE,WAAW;MACjBrG,KAAK,EAAE,OAAO;MACdG,KAAK,EAAE,eAAe;MACtBC,OAAO,EAAE,kBAAkB;MAC3BkG,MAAM,EAAE,uBAAuB;MAC/BC,cAAc,EAAE;;;CAGzB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}