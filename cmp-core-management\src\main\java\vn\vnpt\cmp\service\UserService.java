package vn.vnpt.cmp.service;

import java.util.*;
import java.util.stream.Collectors;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import cz.jirutka.rsql.parser.RSQLParser;
import cz.jirutka.rsql.parser.ast.Node;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import vn.vnpt.cmp.base.constants.Constants;
import vn.vnpt.cmp.base.constants.Constants.CMPService;
import vn.vnpt.cmp.base.constants.Constants.CustomerStatus;
import vn.vnpt.cmp.base.constants.Constants.Method;
import vn.vnpt.cmp.base.constants.Constants.RoleStatus;
import vn.vnpt.cmp.base.constants.Constants.UserStatus;
import vn.vnpt.cmp.base.constants.Constants.UserType;
import vn.vnpt.cmp.base.constants.MessageKeyConstant;
import vn.vnpt.cmp.base.constants.MessageKeyConstant.Validation;
import vn.vnpt.cmp.base.constants.ResponseCode;
import vn.vnpt.cmp.base.event.Event;
import vn.vnpt.cmp.base.jpa.constants.JpaConstants;
import vn.vnpt.cmp.base.jpa.dto.LoginInfo;
import vn.vnpt.cmp.base.jpa.dto.PageInfo;
import vn.vnpt.cmp.base.jpa.dto.req.*;
import vn.vnpt.cmp.base.jpa.dto.req.UpdateUserReq.CustomerDTO;
import vn.vnpt.cmp.base.jpa.dto.req.UpdateUserReq.ContractDTO;
import vn.vnpt.cmp.base.jpa.dto.resp.*;
import vn.vnpt.cmp.base.jpa.entity.*;
import vn.vnpt.cmp.base.redis.ObjectCache;
import vn.vnpt.cmp.base.rsql.CustomRsqlVisitor;
import vn.vnpt.cmp.base.utils.ObjectMapperUtil;
import vn.vnpt.cmp.config.ApplicationProperties;
import vn.vnpt.cmp.model.dto.IGetCountChildUser;
import vn.vnpt.cmp.model.dto.IGetUserDTO;
import vn.vnpt.cmp.model.dto.ISearchUserDTO;
import vn.vnpt.cmp.model.entity.IOTTenant;
import vn.vnpt.cmp.model.entity.IOTUser;
import vn.vnpt.cmp.repository.*;
import vn.vnpt.cmp.util.BaseController;

import java.util.concurrent.TimeUnit;

import static vn.vnpt.cmp.base.constants.Constants.IOTUserType.IOT_USER;
import static vn.vnpt.cmp.base.constants.Constants.IOTUserType.NOT_IOT_USER;
import static vn.vnpt.cmp.base.constants.Constants.OAuth2IoT.CLIENT_ID;
import static vn.vnpt.cmp.base.constants.Constants.OAuth2IoT.SECRET_ID;
import static vn.vnpt.cmp.base.constants.Constants.SynchronizeStatus.FAIL;
import static vn.vnpt.cmp.base.jpa.constants.JpaConstants.Method.GET_BY_KEY;

@Service
@Transactional
public class UserService {

    private static Logger logger = LoggerFactory.getLogger(UserService.class);

    private UserRepository userRepository;

    private final int authorizationCodeLength = 36;
    private final String SYSTEM = "system";
    @Autowired
    private ProvinceRepository provinceRepository;

    @Autowired
    private MailService mailService;

    @Autowired
    private UserRoleRepository userRoleRepository;

    @Autowired
    private UserPolicyRepository userPolicyRepository;

    @Autowired
    private UserCustomerRepository userCustomerRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private OAuth2Service oAuth2Util;

    @Autowired
    private CustomerRepository customerRepository;
    @Autowired
    private UserManageRepository userManageRepository;

    @Autowired
    private UserContractRepository userContractRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private TokenPermissionRepository tokenPermissionRepository;

    @Autowired
    private ApiEndPointRepository apiEndPointRepository;

    @Autowired
    private ClientAuthenticationRepository clientAuthenticationRepository;

    @Autowired
    private AuthorizationInfoRepository authorizationInfoRepository;


    public UserService(UserRepository userRepository, ApplicationProperties applicationProperties) {
        this.userRepository = userRepository;
        this.userRepository = userRepository;
        this.applicationProperties = applicationProperties;
    }

    private final ApplicationProperties applicationProperties;

    private SecurityService securityService;

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private ObjectCache systemCache;

    @Autowired
    public void setSecurityService(SecurityService securityService) {
        this.securityService = securityService;
    }

    @Autowired
    private ProfileApiRepository profileApiRepository;

    public Event process(Event event) {
        switch (event.method) {
            case Constants.Method.SEARCH_USER:
                return processSearchUser(event);
            case Method.GET_ONE_USER:
                return processGetOneUser(event);
            case Method.CHANGE_STATUS_USER:
                return processChangeStatusUser(event);
            case Method.DELETE_USER:
                return processDeleteUser(event);
            case Method.GET_PROVINCE:
                return processGetProvince(event);
            case Method.GET_PROVINCE_BY_CODE:
                return processGetProvinceByCode(event);
            case Method.CREATE_USER:
                return processCreateUser(event);
            case Method.UPDATE_USER:
                return processUpdateUser(event);
            case Method.EXIST_BY_EMAIL_OR_USERNAME:
                return processCheckExist(event);
            case Method.GET_LIST_CUSTOMER_CREATE:
                return processGetListCustomerCreate(event);
            case Method.GET_LIST_ROLE:
                return processGetListRole(event);
            case Method.LOGIN:
                return login(event);
            case Method.VALIDATE_PERMISSION:
                return validatePermission(event);
            case Method.VALIDATE_PERMISSION_TOKEN_PUBLIC:
                return validatePermissionTokenPublic(event);
            case Method.CURRENT_USER:
                return processCurrentUser(event);
            case Method.GET_LIST_CUSTOMER_CODE_SAME_PARENT:
                return getCustomerCodesForGroupSim(event);
            case Method.FORGOT_PASSWORD_INIT:
                return processForgotPasswordInit(event);
            case Method.FORGOT_PASSWORD_FINISH:
                return processForgotPasswordFinish(event);
            case Method.CHANGE_PASSWORD_USER:
                return processChangePassword(event);
            case Method.VALIDATE_TOKEN_MAIL:
                return processValidateTokenEmail(event);
            case Method.VIEW_PROFILE:
                return processViewProfile(event);
            case Method.UPDATE_PROFILE:
                return processUpdateProfile(event);
            case Constants.Method.GET_LIST_USER_CHILD:
                return processGetListUserChild(event);
            case Method.GET_LIST_CONFIRM_POLICY_HISTORY:
                return processGetListConfirmPolicyHistory(event);
            case Method.AGREE_POLICY:
                return processAgreePolicy(event);
            case Method.DISAGREE_POLICY:
                return processDisagreePolicy(event);
            case JpaConstants.Method.GET_ONE:
                return processGetOne(event);
            case Method.CHANGE_USER_FOR_MANAGER:
                return processChangeUserForManager(event);
            case Method.SEARCH_USER_CUSTOMER_MANAGE:
                return getManagedCustomerAccounts(event);
            case Method.SEARCH_USER_CUSTOMER_NO_ONE_MANAGE:
                return getNoOneManagedCustomerAccounts(event);
            case Method.SEARCH_USER_FOR_ACTIVITY_LOG:
                return processSearchUserForActivityLog(event);
            case Method.GET_LIST_ACTIVATED_ACCOUNT:
                return processGetListActivatedAccount(event);
            case Method.SEARCH_USER_FOR_RATING_PLAN:
                return processSearchUserForRatingPlan(event);
            case GET_BY_KEY:
                return processGetByKey(event);
            case Method.GET_USER_ASSIGNED_ON_RP:
                return processGetUserAssignedOnRatingPlan(event);
            case Method.GET_LIST_MODULE_3RD:
                return processGetListModule3rd(event);
            case Method.SEARCH_API_3RD:
                return processSearchApi3rd(event);
            case Method.SEARCH_3RD_FOR_CUSTOMER:
                return processSearchApi3rdForCustomerChild(event);
            case Method.GET_LIST_TOKEN_3RD:
                return processGetList3rdToken(event);
            case Method.GET_USER_SEARCH_API:
                return processGetUserForAPI(event);
            case Method.GET_LIST_API_BY_MODULE:
                return processSearchApi3rdByModule(event);
        }
        return event;
    }

    private Event processGetList3rdToken(Event event) {
        Long userId = (Long) event.payload;
        List<AuthorizationInfo> list = authorizationInfoRepository.findByUserId(userId);
        return event.createResponse(list, ResponseCode.OK, null);

    }


    private Event processGetUserAssignedOnRatingPlan(Event event) {
        Long ratingPlanId = (Long) event.payload;
        List<ISearchUserDTO> pageUser = userRepository.getUserAssignedOnRatingPlan(ratingPlanId);
        List<SearchUserResponseDTO> userResponseDTOList = pageUser.stream().map(userDTO -> {
            SearchUserResponseDTO userResponseDTO = new SearchUserResponseDTO();
            BeanUtils.copyProperties(userDTO, userResponseDTO);
            return userResponseDTO;
        }).collect(Collectors.toList());
        return event.createResponse(userResponseDTOList, ResponseCode.OK, null);
    }


    private Event processSearchUser(Event event) {
        SearchUserRequest searchUserRequest = (SearchUserRequest) event.payload;
        BaseController.ListRequest listRequest = new BaseController.ListRequest(searchUserRequest.getSize(), searchUserRequest.getPage(),
                searchUserRequest.getSortBy());
        List<String> lstProvinceCodeSearch = Arrays.asList(searchUserRequest.getProvinceCode().split(","));
        Page<ISearchUserDTO> pageUser = userRepository.getPageUser(searchUserRequest, event.userType, event.userId, event.provinceCode, lstProvinceCodeSearch, listRequest.getPageable());
        List<SearchUserResponseDTO> userResponseDTOList = pageUser.getContent().stream().map(userDTO -> {
            SearchUserResponseDTO userResponseDTO = new SearchUserResponseDTO();
            BeanUtils.copyProperties(userDTO, userResponseDTO);
            return userResponseDTO;
        }).collect(Collectors.toList());

        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(pageUser.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(userResponseDTOList));
        return event.createResponse(pageInfo, 200, null);
    }

    private Event processSearchUserForRatingPlan(Event event) {
        RatingPlanSearchUserDTO quickSearchUserDTO = (RatingPlanSearchUserDTO) event.payload;
        BaseController.ListRequest listRequest = new BaseController.ListRequest(quickSearchUserDTO.getSize(), quickSearchUserDTO.getPage(),
                quickSearchUserDTO.getSortBy());
        Page<ISearchUserDTO> pageUser = userRepository.getPageUserForRatingPlan(quickSearchUserDTO, event.userType, event.userId, event.provinceCode, listRequest.getPageable());
        List<SearchUserResponseDTO> userResponseDTOList = pageUser.getContent().stream().map(userDTO -> {
            SearchUserResponseDTO userResponseDTO = new SearchUserResponseDTO();
            BeanUtils.copyProperties(userDTO, userResponseDTO);
            return userResponseDTO;
        }).collect(Collectors.toList());

        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(pageUser.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(userResponseDTOList));
        return event.createResponse(pageInfo, 200, null);
    }

    private Event processGetOneUser(Event event) {
        try {
            Long id = (Long) event.payload;
            IGetUserDTO userDTO = userRepository.getOneUser(id);
            // trường hợp id không tồn tại
            if (Objects.isNull(userDTO)) {
                return event.createResponse(Arrays.asList("id", id), ResponseCode.NOT_FOUND, null);
            }
            if (userDTO.getStatus().intValue() == UserStatus.DEACTIVE) {
                return event.createResponse(Arrays.asList("id", id), ResponseCode.FORBIDDEN, null);
            }
            if (checkPermissionCreateUpdateUser(event.userType, userDTO.getType())) {
                // trường hợp tài khoản đăng nhập là tỉnh/ thành phố, GDV -> chỉ đc xem khách hàng, GDV theo tỉnh user login
                if (event.userType.equals(UserType.SUPERVISOR) || event.userType.equals(UserType.MANAGER)) {
                    if (!userDTO.getProvinceCode().equals(event.provinceCode)) {
                        return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
                    }
                    // trường hợp tài khoản đăng nhập là GDV -> chi xem dc khach hang con -- Không check đoạn này vì xem dc cả KH chưa ai quản lý
//                if(event.userType.equals(UserType.MANAGER) && userDTO.getType().equals(UserType.CUSTOMER)) {
//                    List<UserManage> list = userManageRepository.findAllByUserManageIdAndUserId(event.userId, userDTO.getId());
//                    if(list == null || list.size() == 0) {
//                        return event.createResponse(Arrays.asList("id"), ResponseCode.BAD_REQUEST, MessageKeyConstant.FORBIDDEN);
//                    }
//                }
//                [STC_CMP-997] giao dịch viên chỉ xem được tài khoản khách hàng đang quản lý và tài khoản chưa ai quản lý
                    if (event.userType.equals(UserType.MANAGER)) {
                        List<Long> listAccoutCustomerId = userRepository.getListAccountCustomerIdForManager(event.provinceCode, event.userId);
                        if (!listAccoutCustomerId.contains(id)) {
                            return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
                        }
                    }
                }
                // trường hợp tài khoản đăng nhập là khách hàng -> chi xem dc khach hang con
                if (event.userType.equals(UserType.CUSTOMER) && userDTO.getType().equals(UserType.CUSTOMER)) {
                    if (!isChildOfUser(id, event.userId) && !userDTO.getId().equals(event.userId)) {
                        return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
                    }
                }
                UserResponseDTO userResponseDTO = new UserResponseDTO();
                BeanUtils.copyProperties(userDTO, userResponseDTO);
                userResponseDTO.setIsHasChild(isRootCustomer(userResponseDTO.getId()));

                // lấy ds role
                if (!StringUtils.isBlank(userDTO.getRoleNames()) && !StringUtils.isBlank(userDTO.getRoleIds())) {
                    String[] roleNames = userDTO.getRoleNames().split(",");
                    String[] roleIds = userDTO.getRoleIds().split(",");

                    for (int i = 0; i < roleNames.length; i++) {
                        UserResponseDTO.RoleDTO roleDTO = new UserResponseDTO.RoleDTO(roleNames[i], Long.parseLong(roleIds[i]));
                        userResponseDTO.getRoles().add(roleDTO);
                    }
                }
                // lấy ds customer
                List<Long> customerIds = userCustomerRepository.findCustomerIdsByUserId(id);
                List<Customer> customerLst = customerRepository.findByIdIn(customerIds);
                for (Customer customer : customerLst) {
                    UserResponseDTO.CustomerDTO customerDTO = new UserResponseDTO.CustomerDTO(customer.getCustomerCode(), customer.getCustomerName(),
                            customer.getId());
                    userResponseDTO.getCustomers().add(customerDTO);
                }
                // Lấy danh sách user quản lý, nếu là manager thì lấy danh sách user quản lý, nếu là customer thì lấy GDV quản lý
                if (userResponseDTO.getType().equals(UserType.MANAGER) || userResponseDTO.getType().equals(UserType.CUSTOMER)) {
                    List<IGetUserDTO> listUserOfManagerDTO = userRepository.findAllByUserManageOrUser(id, userResponseDTO.getType());
                    List<UserResponseDTO.UserManageDTO> listUserOfManager = listUserOfManagerDTO.stream().map(user -> {
                        UserResponseDTO.UserManageDTO userResponse = new UserResponseDTO.UserManageDTO();
                        BeanUtils.copyProperties(user, userResponse);
                        if (userResponseDTO.getType().equals(UserType.MANAGER)) {
                            if (user.getHierarchicalRelation().startsWith("/" + user.getId())) {
                                userResponse.setRootCustomer(true);
                            } else userResponse.setRootCustomer(false);
                        }

                        return userResponse;
                    }).collect(Collectors.toList());
                    if (userResponseDTO.getType().equals(UserType.MANAGER))
                        userResponseDTO.setUserManages(listUserOfManager);
                    else if (userResponseDTO.getType().equals(UserType.CUSTOMER) && listUserOfManager.size() > 0)
                        userResponseDTO.setManager(listUserOfManager.get(0));
                }
                if (userDTO.getType().equals(UserType.CUSTOMER) && userDTO.getHierarchicalRelation() != null && userDTO.getHierarchicalRelation().startsWith("/" + userDTO.getId()))
                    userResponseDTO.setIsRootCustomer(true);
                else {
                    // lấy parent Id của khách hàng con
                    if(userDTO.getType().equals(UserType.CUSTOMER)) {
                        userResponseDTO.setIsRootCustomer(false);
                        String userId = getSecondLastElement(userDTO.getHierarchicalRelation());
                        userResponseDTO.setParentId(userId);
                    }
                }
                if (userDTO.getType().equals(UserType.CUSTOMER)) {
                    QuickSearchCustomerContractDTO search = new QuickSearchCustomerContractDTO();
                    search.setKeyword(" ");
                    search.setCustomerIds(customerIds);
                    search.setPage(0);
                    search.setSize(1000000);
                    search.setSortBy("customerName,asc");
                    search.setAccountRootId(userResponseDTO.getId());
                    BaseController.ListRequest listRequest = new BaseController.ListRequest(search.getSize(), search.getPage(), search.getSortBy());
                    //lấy ds hợp đồng của khách hàng để lưu vào selectedItem ở FE
                    insertSerialTemporary(search.getCustomerIds());
                    Page<ICustomerContractSumaryDTO> page = contractRepository.getContractsByCustomerIds(
                            search.getAccountRootId(),
                            search.getKeyword(),
                            listRequest.getPageable()
                    );

                    List<CustomerContractSumaryResponse> contractList = page.getContent().stream().map(ICustomerContractSumaryDTO -> {
                        CustomerContractSumaryResponse contract = new CustomerContractSumaryResponse();
                        contract.setId(ICustomerContractSumaryDTO.getId());
                        contract.setCustomerCode(ICustomerContractSumaryDTO.getCustomerCode());
                        contract.setCustomerName(ICustomerContractSumaryDTO.getCustomerName());
                        contract.setContractCode(ICustomerContractSumaryDTO.getContractCode());
                        return contract;
                    }).collect(Collectors.toList());
                    userResponseDTO.setContracts(contractList);
                }
                // lấy root acc để hiển thị
                if (userDTO.getType().equals(UserType.CUSTOMER) && !userResponseDTO.getIsRootCustomer()) {
                    try {
                        String hierarchicalRelation = userDTO.getHierarchicalRelation();
                        String[] parts = hierarchicalRelation.split("/");
                        if (parts.length > 1 && !parts[1].isEmpty()) {
                            Long firstId = Long.parseLong(parts[1]);
                            Optional optionalRootAccount = userRepository.findById(firstId);
                            if (optionalRootAccount.isPresent()) {
                                User rootAccount = (User) optionalRootAccount.get();
                                UserResponseDTO.UserManageDTO userManageDTO = new UserResponseDTO.UserManageDTO();
                                userManageDTO.setId(rootAccount.getId());
                                userManageDTO.setFullName(rootAccount.getFullName());
                                userManageDTO.setUsername(rootAccount.getUsername());
                                userManageDTO.setRootCustomer(true);
                                userResponseDTO.setRootAccount(userManageDTO);
                            }
                        }
                    } catch (Exception e) {
                        logger.error("Lỗi lấy thông tin account root customer", e);
                    }
                }
                List<TokenPermission> tokenPermissionList = tokenPermissionRepository.getAllByUserId(userResponseDTO.getId());
                if (tokenPermissionList != null && tokenPermissionList.size() > 0) {
                    userResponseDTO.setListApiId(tokenPermissionList.stream().map(t -> t.getApiId()).collect(Collectors.toList()));
                }
                List<ClientAuthentication> clientAuthenticationList = clientAuthenticationRepository.findAllByUserId(userResponseDTO.getId());

                if (clientAuthenticationList != null && clientAuthenticationList.size() > 0) {
                    String secretId = clientAuthenticationList.get(0).getSecretId();
                    userResponseDTO.setSecretId(secretId);
                    userResponseDTO.setStatusApi(clientAuthenticationList.get(0).getActive());
                }
                return event.createResponse(userResponseDTO, ResponseCode.OK, null);
            }
        } catch (Exception e) {
            logger.error("Exception get one User {}", e.getMessage(), e);
        }
        return event.createResponse(new ArrayList<String>(), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
    }

    public static String getSecondLastElement(String path) {
        // Loại bỏ dấu `/` đầu nếu có
        String[] parts = path.split("/");
        if (parts.length < 3) return null; // Cần ít nhất 2 số để lấy phần tử sát cuối
        return parts[parts.length - 2]; // Lấy phần tử trước phần tử cuối cùng
    }

    private Event processGetUserForAPI(Event event) {
        SearchUserRequest searchUserRequest = (SearchUserRequest) event.payload;
        List<String> lstProvinceCodeSearch = Arrays.asList("");
        List<ISearchUserDTO> pageUser = userRepository.getUserForApi(searchUserRequest, event.userType, event.userId, event.provinceCode, lstProvinceCodeSearch);
        List<SearchUserResponseDTO> userResponseDTOList = pageUser.stream().map(userDTO -> {
            SearchUserResponseDTO userResponseDTO = new SearchUserResponseDTO();
            BeanUtils.copyProperties(userDTO, userResponseDTO);
            return userResponseDTO;
        }).collect(Collectors.toList());

        return event.createResponse(ObjectMapperUtil.toJsonString(userResponseDTOList), 200, null);
    }


    @Autowired
    private JdbcTemplate jdbcTemplate;

    public void batchInsert(List<Long> entities, String sql) {
//        String sql = "INSERT INTO CUSTOMER_CODE_TEMP_TABLE (customer_code) VALUES (?)";
        jdbcTemplate.batchUpdate(sql, entities, 1000, (ps, entity) -> {
            ps.setLong(1, entity);
        });
    }

    public void insertSerialTemporary(List<Long> ids) {
        if (ids != null && !ids.isEmpty()) {
            batchInsert(ids, "INSERT INTO CUSTOMER_ID_TEMP (ID) VALUES (?)");
        }


    }

    private Event processChangeStatusUser(Event event) {
        Long id = (Long) event.payload;
        Optional<User> optUser = userRepository.findById(id);
        // trường hợp id không tồn tại
        if (!optUser.isPresent()) {
            return event.createResponse(Arrays.asList("id"), ResponseCode.NOT_FOUND, null);
        }
        User user = optUser.get();
        if (checkPermissionCreateUpdateUser(event.userType, user.getType())) {
            // với tài khoản login là khách hàng -> check xem tài khoản đó có phải là con của khách hàng không thì mới được chuyển trạng thái
            if (event.userType.equals(UserType.CUSTOMER)) {
                if (isChildOfUser(id, event.userId)) {
                    user.setStatus(user.getStatus() == UserStatus.ACTIVE ? UserStatus.INACTIVE : UserStatus.ACTIVE);
                    return event.createResponse(user, ResponseCode.OK, null);
                } else {
                    return event.createResponse(MessageKeyConstant.FORBIDDEN, ResponseCode.FORBIDDEN, null);
                }
            } else {
                // với các loại tài khoản khác -> chuyển trạng thái Không hoạt động <-> hoạt động
                user.setStatus(user.getStatus() == UserStatus.ACTIVE ? UserStatus.INACTIVE : UserStatus.ACTIVE);
                return event.createResponse(user, ResponseCode.OK, null);
            }
        }
        return event.createResponse(MessageKeyConstant.FORBIDDEN, ResponseCode.FORBIDDEN, null);
    }

    boolean isRootCustomer(Long userId) {
        List<IGetCountChildUser> iGetCountChildUsers = userRepository.getCountChildOfUser();
        for (IGetCountChildUser countChildUser : iGetCountChildUsers) {
            if (countChildUser.getId().equals(userId)) {
                if (countChildUser.getChildrenCount() > 0) return true;
            }
        }
        return false;
    }

    /*
     * Check Tài khoản loại khách hàng chỉ có quyền cập nhật tài khoản là cấp dưới của nó
     * */
    boolean isChildOfUser(Long userId, Long userIdLogin) {
        List<Long> listIdsChild = userRepository.getListUserHierarchy(userIdLogin);
        return listIdsChild.contains(userId);
    }

    private Event processDeleteUser(Event event) {
        Long id = (Long) event.payload;
        Optional<User> optUser = userRepository.findById(id);
        // trường hợp id không tồn tại
        if (!optUser.isPresent()) {
            return event.createResponse(Arrays.asList("id"), ResponseCode.NOT_FOUND, null);
        }
        User user = optUser.get();
        if (checkPermissionCreateUpdateUser(event.userType, user.getType())) {
            if (user.getType().equals(UserType.CUSTOMER) && event.userType.equals(UserType.CUSTOMER)) {
                // check khách hàng có phải con của user login không
                if (!isChildOfUser(user.getId(), event.userId)) {
                    return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
                }
//                // khách hàng có con thì sẽ k cho xóa
//                if (isRootCustomer(id)) {
//                    return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
//                }
                /**
                 * STC_CMP-998 Cho phép xóa khách hàng có con, sau khi xóa sẽ update lại HierarchicalRelation của user con
                 */
                List<Long> listUserChildId = userRepository.getListUserHierarchy(user.getId());
                if (!listUserChildId.isEmpty()) {
                    List<User> listUserChild = userRepository.getAllByIdIn(listUserChildId);
                    if (!listUserChild.isEmpty()) {
                        for (User uc : listUserChild) {
                            uc.setHierarchicalRelation(uc.getHierarchicalRelation().replace("/" + user.getId().toString(), ""));
                        }
                    }
                    listUserChild.size();
                    userRepository.saveAll(listUserChild);
                }

                // update trang thai user can xoa
                user.setStatus(UserStatus.DEACTIVE);
                userManageRepository.deleteByUserId(user.getId());
            } else if (user.getType().equals(UserType.CUSTOMER)) {
//                // khách hàng có con thì sẽ k cho xóa
//                if (isRootCustomer(id)) {
//                    return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
//                }
                user.setStatus(UserStatus.DEACTIVE);
                if (event.userType.equals(UserType.MANAGER)) { // Nếu tài khoản login là MANAGER thì check xem có quyền quản lý không xong xóa
                    int count = userManageRepository.findUserByUserManageIdAndNoOneManage(event.userId, user.getId());
                    if (count > 0) {
                        userManageRepository.deleteByUserId(user.getId());
                    } else {
                        return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
                    }
                }

                /**
                 * UAT 2.4 cho phép xóa acc root không có con
                 */
//                if (user.getHierarchicalRelation().length() - user.getHierarchicalRelation().replace("/", "").length() == 1) {
//                    return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
//                }

                /**
                 * STC_CMP-998 Cho phép xóa khách hàng có con, sau khi xóa sẽ update lại HierarchicalRelation của user con
                 */
                List<Long> listUserChildId = userRepository.getListUserHierarchy(user.getId());
                if (!listUserChildId.isEmpty()) {
                    List<User> listUserChild = userRepository.getAllByIdIn(listUserChildId);
                    if (!listUserChild.isEmpty()) {
                        for (User uc : listUserChild) {
                            uc.setHierarchicalRelation(uc.getHierarchicalRelation().replace("/" + user.getId().toString(), ""));
                        }
                        listUserChild.size();
                        userRepository.saveAll(listUserChild);
                    }
                } else if (event.userType.equals(UserType.SUPERVISOR) || event.userType.equals(UserType.ADMIN)) { // Nếu tài khoản login là ADMIN xóa luôn
                    userManageRepository.deleteByUserId(user.getId());
                }
            } else {
                // với các loại tài khoản khác chỉ cần cập nhật lại status
                // update trang thai user can xoa
                user.setStatus(UserStatus.DEACTIVE);
                if ((event.userType.equals(UserType.SUPERVISOR) || event.userType.equals(UserType.ADMIN)) && user.getType().equals(UserType.MANAGER)) { // Nếu xóa Manager thì xóa danh sách quản lý trong bảng user_manage
                    userManageRepository.deleteByUserManageId(user.getId());
                }
            }
            userRepository.save(user);
            userCustomerRepository.deleteByUserId(user.getId());
            userContractRepository.deleteAllByUserId(user.getId());
            return event.createResponse(user, ResponseCode.OK, null);
        }
        return event.createResponse(Arrays.asList("type"), ResponseCode.FORBIDDEN, null);
    }

    private Event processGetProvince(Event event) {
        return event.createResponse(provinceRepository.getProvince(event.userType, event.provinceCode), ResponseCode.OK, null);
    }

    private Event processGetProvinceByCode(Event event) {
        List<String> lstCode = (List<String>) event.payload;
        return event.createResponse(provinceRepository.getProvinceByCode(lstCode), ResponseCode.OK, null);
    }

    private Event processCreateUser(Event event) {
        try {
            //TODO Lấy user đăng nhập
            User userLogin = userRepository.findById(event.userId).get();

            CreateUserReq createUserReq = (CreateUserReq) event.payload;
            // check email hoặc username tồn tại
            if (!validateCreateUser(createUserReq)) {
                return event.createResponse(Arrays.asList("email", "username"), ResponseCode.CONFLICT, null);
            }

            if (checkPermissionCreateUpdateUser(userLogin.getType(), createUserReq.getType())) {
                User user = createUserRequestToUserEntity(createUserReq);
                String rawPassword = generatePassword(6);
                user.setEncryptedPassword(rawPassword);
                user.setCreatedDate(new Date());
                user.setCreatedBy(userLogin.getId());
                user.setStatus(UserStatus.ACTIVE);
                // tạo tk TỈNH/TP , tạo tk Quận/huyện GDV thì lưu luôn
                User saveUser = userRepository.save(user);
                // case 1: tạo tk ADMIN
                if (createUserReq.getType().equals(UserType.ADMIN)) {
                    saveUser.setProvinceCode(null);
                } else if (createUserReq.getType().equals(UserType.MANAGER)) {
                    // tài khoản tạo là tỉnh thành phố -> lấy province code theo tk đăng nhập
                    if (userLogin.getType().equals(UserType.SUPERVISOR)) {
                        saveUser.setProvinceCode(event.provinceCode);
                    }

                    if (createUserReq.getIdUserManageList() != null && createUserReq.getIdUserManageList().size() > 0) {
                        addUserManage(createUserReq.getIdUserManageList(), saveUser.getId());
                        addUserManage(getListChildOfListUser(createUserReq.getIdUserManageList()), saveUser.getId());
                    }
                    // case 4: tạo tk Khach hang
                } else if (createUserReq.getType().equals(UserType.CUSTOMER)) {
                    // TODO:check customerIdLst có hợp lệ không

                    // tài khoản tạo là ADMIN
                    if (userLogin.getType().equals(UserType.ADMIN)) {
                        saveUser.setProvinceCode(createUserReq.getProvinceCode());
                        //Nếu có account root thì lấy HierarchicalRelation theo root
                        if (createUserReq.getAccountRootId() != null) {
                            Optional optionalUser = userRepository.findById(createUserReq.getAccountRootId());
                            if (optionalUser.isPresent()) {
                                User accountRoot = (User) optionalUser.get();
                                String rootHierarchicalRelation = accountRoot.getHierarchicalRelation();
                                saveUser.setHierarchicalRelation(rootHierarchicalRelation + "/" + saveUser.getId());
//                            [STC_CMP-963] Gán gdv cho account root
                                if (createUserReq.getIdManager() != null) {
                                    addUserManage(List.of(accountRoot.getId()), createUserReq.getIdManager());
                                }
                            } else {
                                saveUser.setHierarchicalRelation("/" + saveUser.getId());
                            }
                        } else {
                            saveUser.setHierarchicalRelation("/" + saveUser.getId());
                        }

                        if (createUserReq.getIdManager() != null) {
                            addUserManage(List.of(saveUser.getId()), createUserReq.getIdManager());
//                        addUserManage(getListChildOfListUser(createUserReq.getIdUserManageList()), createUserReq.getIdManager()); Không cần vì tạo chưa có con
                        }
                    }
                    // tài khoản tạo là tỉnh thành phố hoặc GDV
                    if (userLogin.getType().equals(UserType.SUPERVISOR) || userLogin.getType().equals(UserType.MANAGER)) {
                        saveUser.setProvinceCode(userLogin.getProvinceCode());
                        //Nếu có account root thì lấy HierarchicalRelation theo root
                        if (createUserReq.getAccountRootId() != null) {
                            Optional optionalUser = userRepository.findById(createUserReq.getAccountRootId());
                            if (optionalUser.isPresent()) {
                                User accountRoot = (User) optionalUser.get();
                                String rootHierarchicalRelation = accountRoot.getHierarchicalRelation();
                                saveUser.setHierarchicalRelation(rootHierarchicalRelation + "/" + saveUser.getId());
                            } else {
                                saveUser.setHierarchicalRelation("/" + saveUser.getId());
                            }
                        } else {
                            saveUser.setHierarchicalRelation("/" + saveUser.getId());
                        }
                        if (userLogin.getType().equals(UserType.SUPERVISOR) && createUserReq.getIdManager() != null) // admin tỉnh thì saveUser do GDV được chọn quản lý
                            addUserManage(List.of(saveUser.getId()), createUserReq.getIdManager());
                        else if (userLogin.getType().equals(UserType.MANAGER))
                            addUserManage(List.of(saveUser.getId()), userLogin.getId()); // GDV thì gán luôn cho GDV đó quản lý
                        // Nếu là admin tỉnh bỏ trống GDV => ko add
                    }
                    // Tài khoản tạo là Khách hàng
                    if (userLogin.getType().equals(UserType.CUSTOMER)) {
                        saveUser.setProvinceCode(userLogin.getProvinceCode());
                        String hierarchicalRelation = userLogin.getHierarchicalRelation();
                        saveUser.setHierarchicalRelation(hierarchicalRelation + "/" + saveUser.getId());
                        List<UserManage> userManageList = userManageRepository.findAllByUserId(userLogin.getId());
                        userManageList.forEach(userManage -> {
                            addUserManage(List.of(saveUser.getId()), userManage.getUserManageId());
                        });
                    }
                    // lưu customer vào bảng user_customer
                    addUserCustomer(createUserReq.getCustomerIdLst(), saveUser.getId());
                    //lưu contract
                    addUserContract(createUserReq.getContractIdLst(), saveUser.getId());
                }
                // lưu role vào bảng role_user
                addRoleUser(createUserReq.getRoleLst(), saveUser.getId());
                // Thêm quyền với 3rd api
                if (createUserReq.getStatusApi() == null) {
                    addPermissionOnApiDefault(saveUser.getType(), saveUser.getId());
                } else {
                    addPermissionOnApi(new ChangeApiReqequest(createUserReq.getStatusApi(), createUserReq.getListApiId(), saveUser.getId(), saveUser.getType()));
                }
                // send email
                mailService.sendCreationEmail(saveUser, rawPassword);
                return event.createResponse(saveUser, ResponseCode.OK, null);
            }
        } catch (Exception e) {
            logger.error("Exception processCreateUser {} ", e.getMessage(), e);
        }
        return event.createResponse(null, ResponseCode.BAD_REQUEST, null);
    }

    boolean createIOTTenant(CreateUserReq createUserReq) {
        IOTTenant iotTenant = new IOTTenant();
        iotTenant.setName(createUserReq.getUsername());
        iotTenant.setCreatedBy(SYSTEM);
        IOTUser user = new IOTUser();
        user.setEmail(createUserReq.getEmail());
        String[] fullNameSplit = createUserReq.getFullName().split(" ");
        String firstName = fullNameSplit[fullNameSplit.length - 1];
        user.setFirstName(firstName);
        if (fullNameSplit.length > 1) {
            String lastName = createUserReq.getFullName().substring(0, createUserReq.getFullName().length() - 1 - firstName.length());
            user.setLastName(lastName);
        }
        user.setPhone(createUserReq.getPhone());
        user.setUserName(createUserReq.getUsername());
        user.setCreatedBy(SYSTEM);
        iotTenant.setTenantAdmin(user);
        return oAuth2Util.createIOTTenant(iotTenant) == HttpStatus.SC_OK;
    }

    boolean createIOTTenantUser(CreateUserReq createUserReq, String createdBy) {
        IOTUser user = new IOTUser();
        user.setEmail(createUserReq.getEmail());
        String[] fullNameSplit = createUserReq.getFullName().split(" ");
        String firstName = fullNameSplit[fullNameSplit.length - 1];
        user.setFirstName(firstName);
        if (fullNameSplit.length > 1) {
            String lastName = createUserReq.getFullName().substring(0, createUserReq.getFullName().length() - 1 - firstName.length());
            user.setLastName(lastName);
        }
        user.setPhone(createUserReq.getPhone());
        user.setUserName(createUserReq.getUsername());
        user.setCreatedBy(createdBy);
        List<String> roleIds = new ArrayList<>();
        roleIds.add("ROLE_USER");
        return oAuth2Util.handleUserIOT(user, "POST") == HttpStatus.SC_OK;
    }

    boolean updateIOTUser(UpdateUserReq updateUserReq, String userName, String updatedBy) {
        IOTUser user = new IOTUser();
        user.setEmail(updateUserReq.getEmail());
        String[] fullNameSplit = updateUserReq.getFullName().split(" ");
        String firstName = fullNameSplit[fullNameSplit.length - 1];
        user.setFirstName(firstName);
        if (fullNameSplit.length > 1) {
            String lastName = updateUserReq.getFullName().substring(0, updateUserReq.getFullName().length() - 1 - firstName.length());
            user.setLastName(lastName);
        }
        user.setPhone(updateUserReq.getPhone());
        user.setUserName(userName);
        user.setUpdatedBy(updatedBy);
        return oAuth2Util.handleUserIOT(user, "PUT") == HttpStatus.SC_OK;
    }

    void createSynchronizedIoTUser(User parent, User user, CreateUserReq createUserReq) {
        // Nếu parent user không phải customer +> user tạo là KH mức 1 => tạo tenant
        if (!Objects.equals(parent.getType(), UserType.CUSTOMER)) {
            if (createIOTTenant(createUserReq)) {
                user.setIotUser(IOT_USER);
            } else {
                user.setSynchronizeStatus(FAIL);
            }
            // else là KH mức 2 => tạo tenant user
        } else {
            if (createIOTTenantUser(createUserReq, parent.getUsername())) {
                user.setIotUser(IOT_USER);
            } else {
                user.setSynchronizeStatus(FAIL);
            }
        }
    }

    boolean isHasChild(Long userId) {
        List<Long> lstChildUser = userRepository.getListUserHierarchy(userId);
        if (lstChildUser.isEmpty()) {
            return false;
        }
        return true;
    }

    boolean checkPermissionCreateUpdateUser(Integer typeUserLogin, Integer typeCreateUserReq) {
        // ADMIN sẽ có quyền tạo, sửa tất cả user
        // KH cũng có thể tạo KH
        // các role còn lại sẽ có quyền tạo tất cả user cấp dưới
        if (typeUserLogin.equals(UserType.ADMIN)) {
            return true;
        } else if (typeUserLogin < typeCreateUserReq) {
            return true;
        } else {
            return typeUserLogin.equals(UserType.CUSTOMER) && typeUserLogin <= typeCreateUserReq;
        }
    }

    private User createUserRequestToUserEntity(CreateUserReq createUserReq) {
        User user = new User();
        user.setUsername(createUserReq.getUsername());
        user.setFullName(createUserReq.getFullName());
        user.setEmail(createUserReq.getEmail());
        user.setDescription(createUserReq.getDescription());
        user.setPhone(createUserReq.getPhone());
        user.setType(createUserReq.getType());
        user.setProvinceCode(createUserReq.getProvinceCode());
        return user;
    }

    public static String generatePassword(int length) {
        String charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder password = new StringBuilder();
        Random random = new Random();

        for (int i = 0; i < length; i++) {
            int index = random.nextInt(charset.length());
            password.append(charset.charAt(index));
        }

        return password.toString();
    }

    private void addRoleUser(List<Long> roleLst, Long userId) {
        List<UserRole> userRoles = new ArrayList<>();
        for (Long roleId : roleLst) {
            UserRole userRole = new UserRole();
            userRole.setRoleId(roleId);
            userRole.setUserId(userId);
            userRoles.add(userRole);
        }
        userRoleRepository.saveAll(userRoles);
    }

    private void addUserCustomer(List<Long> customerIdLst, Long userId) {
        List<UserCustomer> userCustomers = new ArrayList<>();
        for (Long roleId : customerIdLst) {
            UserCustomer userCustomer = new UserCustomer();
            userCustomer.setCustomerId(roleId);
            userCustomer.setUserId(userId);
            userCustomers.add(userCustomer);
        }
        userCustomerRepository.saveAll(userCustomers);
    }

    private void addUserContract(List<Long> contractIdLst, Long userId) {
        List<UserContract> userContracts = new ArrayList<>();
        for (Long ContractId : contractIdLst) {
            UserContract userContract = new UserContract();
            userContract.setContractId(ContractId);
            userContract.setUserId(userId);
            userContracts.add(userContract);
        }
        userContractRepository.saveAll(userContracts);
    }

    private void addUserManage(List<Long> userIdLst, Long userMangeId) {
        List<UserManage> userManages = new ArrayList<>();
        if (userIdLst != null && userIdLst.size() > 0) {
            // Tìm những user đang dc quản lý bởi userMangeId
            List<UserManage> oldUserManage = userManageRepository.findDistinctByUserManageId(userMangeId);
            List<Long> userIdOldList = oldUserManage.stream().map(dto -> dto.getUserId()).collect(Collectors.toList());
//            Set<Long> noDuplicateUserIdOldList = new HashSet<Long>(userManages);

            for (Long userId : userIdLst) {
                if (!userIdOldList.contains(userId)) {
                    UserManage userCustomer = new UserManage();
                    userCustomer.setUserManageId(userMangeId);
                    userCustomer.setUserId(userId);
                    userManages.add(userCustomer);
                }
            }
            if (userManages.size() > 0) userManageRepository.saveAll(userManages);
        }
    }

    // Lấy full danh sách children của List user
    private List<Long> getListChildOfListUser(List<Long> userIds) {
        List<Long> listFinal = new ArrayList<>();
        for (Long id : userIds) {
            listFinal.addAll(userRepository.getListUserHierarchy(id));
        }
        return listFinal;
    }

    boolean validateCreateUser(CreateUserReq createUserReq) {
        List<User> checkUsers = userRepository.findByEmailOrUsername(createUserReq.getEmail(), createUserReq.getUsername());
        if (!checkUsers.isEmpty()) {
            return false;
        }
        return true;
    }
    private List<TokenPermission> addPermissionOnApi(ChangeApiReqequest changeApiReqequest) {
        List<TokenPermission> tokenPermissionList = new ArrayList<>();
        if (changeApiReqequest.getStatusApi() != null) {
            tokenPermissionList = new ArrayList<>();
            for (Long apiId: changeApiReqequest.getListApiId()) {
                    TokenPermission tokenPermission = new TokenPermission();
                    tokenPermission.setApiId(apiId);
                    tokenPermission.setUserId(changeApiReqequest.getAccountId());
                    tokenPermission.setStatus(changeApiReqequest.getStatusApi());
                    tokenPermission.setUnit("SECONDS");
                    tokenPermission.setLimit(1L);
                    tokenPermissionList.add(tokenPermission);
            }
            tokenPermissionRepository.saveAll(tokenPermissionList);
        }
        return tokenPermissionList;
    }

    /**
     * Dùng khi k chọn gì ở giao diện
     * @param userType
     * @param accountId
     * @return
     */
    private List<TokenPermission> addPermissionOnApiDefault(Integer userType, Long accountId) {
        List<TokenPermission> tokenPermissionList = new ArrayList<>();
        List<ProfileApi> profileApiList = profileApiRepository.findAllByUserType(userType);
        if (profileApiList != null && !profileApiList.isEmpty()) {
            for (ProfileApi profileApi : profileApiList) {
                TokenPermission tokenPermission = new TokenPermission();
                tokenPermission.setApiId(profileApi.getApiId());
                tokenPermission.setUserId(accountId);
                tokenPermission.setLimit(profileApi.getLimit());
                tokenPermission.setUnit(profileApi.getUnit());
                tokenPermission.setStatus(1);
                tokenPermissionList.add(tokenPermission);
            }
            tokenPermissionRepository.saveAll(tokenPermissionList);
        }
        return tokenPermissionList;
    }

    private List<TokenPermission> updatePermissionOnApi(Long userId, ChangeApiReqequest req) {
        List<TokenPermission> list = new ArrayList<>();
        if (req.getStatusApi() != null) {
            List<Long> old = tokenPermissionRepository.findAllApiIdsByUserId(req.getAccountId());
            List<Long> remove = new ArrayList<>(old);
            remove.removeAll(req.getListApiId());

            List<Long> add = new ArrayList<>(req.getListApiId());
            add.removeAll(old);
            adjustPermissionApiChild(userId, req.getListApiId(), old);

            tokenPermissionRepository.deleteByUserIdAndApiIdIn(req.getAccountId(), remove);

            addPermissionOnApi(new ChangeApiReqequest(req.getStatusApi(), add, req.getAccountId(), req.getUserType()));

            list = tokenPermissionRepository.getAllByUserId(req.getAccountId());
            if (list != null && list.size() > 0 && req.getStatusApi() != null) {
                for (TokenPermission tokenPermission : list) {
                    tokenPermission.setStatus(req.getStatusApi());
                }
                tokenPermissionRepository.saveAll(list);
            }
        }
        return list;
    }
    List<ClientAuthentication> updateClientAuthentication(Long userId, String secretId, Integer status, String userName, Long updateByUserId) {
        List<ClientAuthentication> list = clientAuthenticationRepository.findAllByUserId(userId);
        if (list != null && list.size() > 0 && status != null) {
            for (ClientAuthentication clientAuthentication : list) {
                clientAuthentication.setSecretId(secretId);
                clientAuthentication.setActive(status);
                clientAuthentication.setClientId(userName);
                clientAuthentication.setExpirationTime(100000l);
            }
            clientAuthenticationRepository.saveAll(list);
        } else {
            ClientAuthentication clientAuthentication = new ClientAuthentication();
            clientAuthentication.setUserId(userId);
            clientAuthentication.setSecretId(secretId);
            clientAuthentication.setActive(status != null ? status : 0);
            clientAuthentication.setClientId(userName);
            clientAuthentication.setCreatedDate(new Date());
            clientAuthentication.setCreatedBy(updateByUserId);
            clientAuthentication.setExpirationTime(100000l);
            clientAuthenticationRepository.save(clientAuthentication);
        }
        return list;
    }

    public Event processUpdateUser(Event event) {
        UpdateUserReq updateUserReq = (UpdateUserReq) event.payload;
        // check email tồn tại
        if (!validateUpdateUser(updateUserReq)) {
            return event.createResponse(Arrays.asList("email"), ResponseCode.CONFLICT, Validation.EXISTS);
        }
        // check userId của UpdateUserReq
        Optional<User> optUpdateUser = userRepository.findById(updateUserReq.getId());
        if (!optUpdateUser.isPresent()) {
            return event.createResponse(Arrays.asList("id"), ResponseCode.NOT_FOUND, MessageKeyConstant.NOT_FOUND);
        }
        User updateUser = optUpdateUser.get();
        // k cho update super admin
        if (updateUser.getCreatedBy() == 0) {
            return event.createResponse(Arrays.asList(""), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
        }

        // kiểm tra xem có quyền create hoặc update hay không ?
        if (checkPermissionCreateUpdateUser(event.userType, updateUser.getType())) {

            // trường hợp tài khoản đăng nhập là tỉnh/ thành phố, GDV -> chỉ đc update khách hàng, GDV theo tỉnh user login
            if (event.userType.equals(UserType.SUPERVISOR) || event.userType.equals(UserType.MANAGER)) {
                if (!updateUserReq.getProvinceCode().equals(event.provinceCode)) {
                    return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
                }
            }
            if (updateUser.getType().equals(UserType.CUSTOMER) && event.userType.equals(UserType.CUSTOMER)) {
                if (!isChildOfUser(updateUser.getId(), event.userId)) {
                    return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
                }
            }
            // Lấy danh sách customer
            List<CustomerDTO> customerDTOS = updateUserReq.getCustomerLst();

            List<Long> customerIdsDelete = new ArrayList<>();
            List<Long> customerIdsAdd = new ArrayList<>();

            for (CustomerDTO customerDTO : customerDTOS) {
                if (customerDTO.getType() == 1) { // thêm
                    customerIdsAdd.add(customerDTO.getCustomerId());
                } else if (customerDTO.getType() == -1) { //xóa
                    customerIdsDelete.add(customerDTO.getCustomerId());
                }
            }

            // Lấy danh sách contract
            List<ContractDTO> contractDTOS = updateUserReq.getContractLst();

            List<Long> contractIdsDelete = new ArrayList<>();
            List<Long> contractIdsAdd = new ArrayList<>();

            for (ContractDTO contractDTO : contractDTOS) {
                if (contractDTO.getType() == 1) { // thêm
                    contractIdsAdd.add(contractDTO.getContractId());
                } else if (contractDTO.getType() == -1) { //xóa
                    contractIdsDelete.add(contractDTO.getContractId());
                }
            }


            if (updateUser.getType().equals(UserType.MANAGER)) {  // Nếu tài khoản được cập nhật là GDV
                if (event.userType.equals(UserType.SUPERVISOR) || event.userType.equals(UserType.ADMIN)) { // Nếu tài khoản login là SUPERVISOR or ADMIN
                    userManageRepository.deleteByUserManageId(updateUserReq.getId());

                    if (updateUserReq.getIdUserManageList() != null && updateUserReq.getIdUserManageList().size() > 0) {
                        List<Long> listUserUpdate = new ArrayList<>();
                        listUserUpdate.addAll(updateUserReq.getIdUserManageList());
                        listUserUpdate.addAll(getListChildOfListUser(updateUserReq.getIdUserManageList()));
                        addUserManage(listUserUpdate, updateUserReq.getId());
                    }
                }
            } else if (updateUser.getType().equals(UserType.CUSTOMER)) { // Nếu tài khoản được cập nhật là CUSTOMER => Xóa bản ghi chứa user_GDV cũ, thêm mới user_GDV mới
                if (updateUser.getHierarchicalRelation().startsWith("/" + updateUser.getId()) && (event.userType.equals(UserType.SUPERVISOR) || event.userType.equals(UserType.ADMIN))) {
                    List<Long> listUserUpdate = new ArrayList<>();
                    listUserUpdate.add(updateUser.getId());
                    listUserUpdate.addAll(getListChildOfListUser(List.of(updateUser.getId())));
                    userManageRepository.deleteByUserIdIn(listUserUpdate);
                    if (updateUserReq.getIdManager() != null) {
                        addUserManage(listUserUpdate, updateUserReq.getIdManager());
                    }
                }
            }
            updateUser.setFullName(updateUserReq.getFullName());
            updateUser.setDescription(updateUserReq.getDescription());
            updateUser.setEmail(updateUserReq.getEmail());
            updateUser.setPhone(updateUserReq.getPhone());
            // không cho update tỉnh với tk khách hàng
            if (updateUser.getType().equals(UserType.CUSTOMER)) {
                if (updateUser.getProvinceCode().equals(updateUserReq.getProvinceCode())) {
                    updateRoleUser(updateUserReq.getRoleLst(), updateUser.getId());
                    updateUserCustomer(customerIdsAdd, customerIdsDelete, updateUser.getId());
                    updateUserCustomerChild(updateUserReq.getId(), customerIdsDelete);
                    updateUserContract(contractIdsAdd, contractIdsDelete, updateUser.getId());
                    updateUserContractChild(updateUserReq.getId(), contractIdsDelete);
                } else {
                    return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, Validation.CAN_NOT_UPDATE_PROVINCE_CUSTOMER);
                }
                User userLogin = userRepository.findById(event.userId).get();
                if (!Objects.isNull(updateUserReq.getIotUser()) && Objects.equals(updateUserReq.getIotUser(), IOT_USER)) {
                    // Nếu trước chưa đồng bộ => tạo mới
                    if (Objects.isNull(updateUser.getIotUser()) || Objects.equals(updateUser.getIotUser(), NOT_IOT_USER)) {
                        CreateUserReq createUserReq = new CreateUserReq();
                        BeanUtils.copyProperties(updateUserReq, createUserReq);
                        createUserReq.setUsername(updateUser.getUsername());
                        Optional<User> optParentUser = userRepository.findById(updateUser.getCreatedBy());
                        if (optParentUser.isPresent()) {
                            User parent = optParentUser.get();
                            createSynchronizedIoTUser(parent, updateUser, createUserReq);
                        }
                        // Trước đã đồng bộ => update
                    } else {
                        if (updateIOTUser(updateUserReq, updateUser.getUsername(), userLogin.getUsername())) {
                            updateUser.setIotUser(IOT_USER);
                        } else {
                            updateUser.setSynchronizeStatus(FAIL);
                        }
                    }
                }
            } else {
                updateUser.setProvinceCode(updateUserReq.getProvinceCode());
                updateRoleUser(updateUserReq.getRoleLst(), updateUser.getId());
                updateUserCustomer(customerIdsAdd, customerIdsDelete, updateUser.getId());
            }
            updateClientAuthentication(updateUserReq.getId(), updateUserReq.getSecretId(), updateUserReq.getStatusApi(), updateUser.getUsername(), event.userId);
            updatePermissionOnApi(updateUserReq.getId() ,new ChangeApiReqequest(updateUserReq.getStatusApi(), updateUserReq.getListApiId(), updateUser.getId(), updateUser.getType()));
            return event.createResponse(updateUser, ResponseCode.OK, null);
        }
        return event.createResponse(new ArrayList<>(), ResponseCode.FORBIDDEN, null);
    }

    /**
     * Hàm cập nhật lại danh sách API có quyền của child user khi sửa parent user
     * **/
    void adjustPermissionApiChild(Long userId, List<Long> listApiId, List<Long> oldApis) {
        List<Long> listChildId = userRepository.getListUserHierarchy(userId);
        if(listChildId.size() > 0) {
            // Lọc ra những API k có ở trong root account và xóa ở child
            List<Long> apiNotExistInRoot = oldApis.stream().filter(el -> !listApiId.contains(el)).collect(Collectors.toList());
            if(apiNotExistInRoot.size() >0){
                tokenPermissionRepository.deleteByUserIdInAndApiIdIn(listChildId, apiNotExistInRoot);
            }
        }
    }

    void updateUserCustomerChild(Long userId, List<Long> listCustomerDelete) {
        List<Long> listChildId = userRepository.getListUserHierarchy(userId);
        userCustomerRepository.deleteByUserIdInAndCustomerIdIn(listChildId, listCustomerDelete);
    }

    boolean validateUpdateUser(UpdateUserReq updateUserReq) {
        List<User> checkUsers = userRepository.findByEmailAndIdNot(updateUserReq.getEmail(), updateUserReq.getId());
        if (!checkUsers.isEmpty()) {
            return false;
        }
        return true;
    }

    void updateRoleUser(List<Long> lstRole, Long userId) {
        userRoleRepository.deleteByUserId(userId);
        addRoleUser(lstRole, userId);
    }

    void updateUserCustomer(List<Long> lstCustomerIdAdd, List<Long> lstCustomerIdDelete, Long userId) {
        userCustomerRepository.deleteByUserIdAndCustomerIdIn(userId, lstCustomerIdDelete);
        addUserCustomer(lstCustomerIdAdd, userId);
    }

    void updateUserContract(List<Long> lstContractIdAdd, List<Long> lstContractIdDelete, Long userId) {
        userContractRepository.deleteByUserIdAndContractIdIn(userId, lstContractIdDelete);
        addUserContract(lstContractIdAdd, userId);
    }

    void updateHierarchicalRelationAndManager(User saveUser, CreateUserReq createUserReq) {
        saveUser.setHierarchicalRelation("/" + saveUser.getId());
        if (createUserReq.getIdManager() != null) {
            addUserManage(List.of(saveUser.getId()), createUserReq.getIdManager());
        }
    }

    void updateUserContractChild(Long userId, List<Long> listContractDelete) {
        List<Long> listChildId = userRepository.getListUserHierarchy(userId);
        userContractRepository.deleteByUserIdInAndContractIdIn(listChildId, listContractDelete);
    }

    void updateUserManage(List<Long> lstCustomerIdAdd, Long userId) {
        userManageRepository.deleteByUserManageId(userId);
        addUserManage(lstCustomerIdAdd, userId);
    }

    private Event processCheckExist(Event event) {
        CheckExistUserReq existUserReq = (CheckExistUserReq) event.payload;
        String email = existUserReq.getEmail();
        String username = existUserReq.getUsername();
        List<User> userLst = userRepository.findByEmailOrUsername(email, username);
        return event.createResponse(userLst.size(), ResponseCode.OK, null);
    }

    // danh sách khách hàng ở select box -- màn tạo tài khoản
    private Event processGetListCustomerCreate(Event event) {
        String provinceCode;
        List<Customer> customerList = new ArrayList<>();
        // admin -> lấy theo provinceCode FE gửi lên
        if (event.userType.equals(UserType.ADMIN)) {
            provinceCode = (String) event.payload;
            customerList = customerRepository.findByProvinceCodeSortByCustomerName(provinceCode);
        } else {
            provinceCode = event.provinceCode;
            // với tài khoản tạo còn lại lấy theo province code của user login
            // trường hợp KH tạo tk KH con -> lấy ds kh theo kh cha
            if (event.userType.equals(UserType.CUSTOMER)) {
                List<Long> customerIds = userCustomerRepository.findCustomerIdsByUserId(event.userId);
                customerList = customerRepository.findByIdInAndStatusNotSortByCustomerName(customerIds, CustomerStatus.INACTIVE);
                return event.createResponse(customerList, ResponseCode.OK, null);
            }
            customerList = customerRepository.findByProvinceCodeSortByCustomerName(provinceCode);
        }
        return event.createResponse(customerList, ResponseCode.OK, null);
    }

    private Event processGetListRole(Event event) {
        final Integer roleTypeAll = 0;
        SearchRoleCreateAccountReq search = (SearchRoleCreateAccountReq) event.payload;
        List<Long> userIdList = new ArrayList<>();
        userIdList.add(-1L);
        if (event.userType == UserType.CUSTOMER) {
            search.setAccountRootId(event.userId);
        }
        if (search.getAccountRootId() != null && search.getAccountRootId() != -1) {
            // thêm tài khoản con và chính nó vào list
            userIdList = getListChildOfListUser(List.of(search.getAccountRootId()));
            userIdList.add(search.getAccountRootId());
        }
        List<Role> roles = roleRepository.findByTypeInAndStatus(Arrays.asList((Integer) search.getType(), roleTypeAll), RoleStatus.ACTIVE, search.getAccountRootId(), userIdList);
        return event.createResponse(roles, ResponseCode.OK, null);
    }


    private Event login(Event event) {
        LoginInfo loginInfo = (LoginInfo) event.payload;
        User user = this.findByEmail(loginInfo.getEmail());
        if (user == null) {
            return event.createResponse(null, ResponseCode.NOT_FOUND, Validation.EMAIL_INCORRECT);
        }
        if (Boolean.FALSE.equals(user.authenticate(loginInfo.getPassword()))) {
            return event.createResponse(null, ResponseCode.NOT_FOUND, Validation.PASSWORD_INCORRECT);

        }

        if (user.getStatus() == null || !user.getStatus().equals(1)) {
            return event.createResponse(null, ResponseCode.NOT_FOUND, Validation.ACCOUNT_INACTIVE);
        }
        List<Role> lstRoleActive = roleRepository.getRolesByUserId(user.getId());
        if (lstRoleActive.isEmpty()) {
            return event.createResponse(null, ResponseCode.NOT_FOUND, Validation.ROLE_NOT_WORKING);
        }
        logger.info("Generate token for user: {}", loginInfo.getEmail());
        Date validity;
        long now = (new Date()).getTime();
        if (Boolean.TRUE.equals(loginInfo.getRememberMe())) {
            validity = new Date(now + this.applicationProperties.getTokenTime().getRemember() * 1000);
        } else {
            validity = new Date(now + this.applicationProperties.getTokenTime().getNoRemember() * 1000);
        }

        String token = Jwts.builder()
                .setSubject(user.getEmail())
                .setExpiration(validity)
                .claim(CMPService.JWT_USER_ID, user.getId())
                .claim(CMPService.JWT_SCOPE, getAuthorities(user))
                .signWith(SignatureAlgorithm.HS512, CMPService.JWT_SECRET)
                .compact();
        logger.info("Token generated for user {}, token: {}", loginInfo.getEmail(), token);
        AuthResponseWithTokenAndErrorCodeDTO response = new AuthResponseWithTokenAndErrorCodeDTO();
        response.setAccessToken(token);
        response.setExp(validity.getTime() / 1000);
        response.setNbf(now / 1000);
        if (
                getPeriodExpirePassword(
                        Objects.isNull(user.getUpdatedPasswordDate()) ?
                                user.getCreatedDate() :
                                user.getUpdatedPasswordDate()
                ) >= 6
        ) {
            return event.createResponse(response, ResponseCode.EXPIRED_PASSWORD, Validation.EXPIRED_PASSWORD);
        }

        return event.createResponse(response, ResponseCode.OK, null);

    }

    public User findByEmail(String email) {
        User user = this.userRepository.findOneByEmailIgnoreCase(email);
        if (user == null) {
            return this.userRepository.findOneByUsernameIgnoreCase(email);
        }
        return user;
    }

    public List<String> getAuthorities(User user) {
        List<Long> roleIds = userRoleRepository.getRoleIdByUserId(user.getId());
        List<String> authorities = roleRepository.findByIdInAndStatus(roleIds, RoleStatus.ACTIVE)
                .stream()
                .map(Role::getName)
                .collect(Collectors.toList());
        authorities.addAll(permissionRepository.getListPermissionKeyByRoleIds(roleIds));
        return authorities;
    }

    private Event validatePermission(Event event) {
        String token = (String) event.payload;
        if (token.length() == authorizationCodeLength) {
            Auth2RequestInit auth2RequestInit = new Auth2RequestInit();
            auth2RequestInit.setClient_id(CLIENT_ID);
            auth2RequestInit.setClient_secret(SECRET_ID);
            auth2RequestInit.setGrant_type("authorization_code");
            auth2RequestInit.setCode(token);
            Auth2Response auth2Response = oAuth2Util.oauth2Login(auth2RequestInit);
            String[] chunks = auth2Response.getIdToken().split("\\.");
            Base64.Decoder decoder = Base64.getUrlDecoder();
            String payload = new String(decoder.decode(chunks[1]));
            JsonObject jsonObject = new Gson().fromJson(payload, JsonObject.class);
            String userName = jsonObject.get("userName").isJsonNull() ? " " : jsonObject.get("userName").getAsString();
            String email = jsonObject.get("email").getAsString();
            AuthResponseWithTokenAndErrorCodeDTO authResponseWithTokenAndErrorCodeDTO = createTokenEmail(email, userName);
            if (authResponseWithTokenAndErrorCodeDTO != null) {
                token = authResponseWithTokenAndErrorCodeDTO.getAccessToken();
            }
        }
        if (securityService.validateToken(token)) {
            event.respStatusCode = ResponseCode.OK;
            Claims claims = securityService.parseToken((token));
            User user = current(claims);
            if (user == null){
                return event.createResponse(null, ResponseCode.INVALID_TOKEN, null);
            }
            user.setTokenType(1);
            return event.createResponse(user, ResponseCode.OK, null);
        } else {
            return event.createResponse(null, ResponseCode.INVALID_TOKEN, null);
        }
    }

    private Event validatePermissionTokenPublic(Event event) {
        String token = (String) event.payload;
        if (securityService.validateToken(token)) {
            event.respStatusCode = ResponseCode.OK;
            Claims claims = securityService.parseToken((token));
            User user = currentTokenPublic(claims);
            if (user == null){
                return event.createResponse(null, ResponseCode.INVALID_TOKEN, null);
            }
            user.setTokenType(2);
            return event.createResponse(user, ResponseCode.OK, null);
        } else {
            return event.createResponse(null, ResponseCode.INVALID_TOKEN, null);
        }
    }

    private AuthResponseWithTokenAndErrorCodeDTO createTokenEmail(String email, String userName) {
        User user = userRepository.findOneByUsernameIgnoreCase(userName);
        if (user == null) {
            user = this.findByEmail(email);
        }
        if (user == null) {
            return null;
        }
        if (user.getStatus() == null || !user.getStatus().equals(UserStatus.ACTIVE) || user.getIotUser() == null || Objects.equals(user.getIotUser(), NOT_IOT_USER)) {
            return null;
        }
        List<Role> lstRoleActive = roleRepository.getRolesByUserId(user.getId());
        if (lstRoleActive.isEmpty()) {
            return null;
        }
        Date validity;
        long now = (new Date()).getTime();
        validity = new Date(now + this.applicationProperties.getTokenTime().getRemember() * 1000);

        String token = Jwts.builder()
                .setSubject(user.getEmail())
                .setExpiration(validity)
                .claim(CMPService.JWT_USER_ID, user.getId())
                .claim(CMPService.JWT_SCOPE, getAuthorities(user))
                .signWith(SignatureAlgorithm.HS512, CMPService.JWT_SECRET)
                .compact();
//        logger.info("Token generated for user {}, token: {}", loginInfo.getEmail(), token);
        AuthResponseWithTokenAndErrorCodeDTO response = new AuthResponseWithTokenAndErrorCodeDTO();
        response.setAccessToken(token);
        response.setExp(validity.getTime() / 1000);
        response.setNbf(now / 1000);
        if (
                getPeriodExpirePassword(
                        Objects.isNull(user.getUpdatedPasswordDate()) ?
                                user.getCreatedDate() :
                                user.getUpdatedPasswordDate()
                ) >= 6
        ) {
            return null;
        }

        return response;

    }


    public User current(Claims claims) {
        String email = claims.getSubject();
        User user = this.userRepository.findOneByEmailIgnoreCase(email);
        if (user == null) {
            return null;
        }
        List<Long> roleIds = userRoleRepository.getRoleIdByUserId(user.getId());
        user.setRoles(roleRepository.findByIdInAndStatus(roleIds, RoleStatus.ACTIVE)
                .stream()
                .map(Role::getName)
                .collect(Collectors.toList()));
        user.setAuthorities(permissionRepository.getListPermissionKeyByRoleIds(roleIds));
        return user;
    }

    public User currentTokenPublic(Claims claims) {
        String userName = claims.getSubject();
        User user = userRepository.findOneByUsernameIgnoreCaseAndStatus(userName, UserStatus.ACTIVE);
        if (user == null) {
            return null;
        }
        List<Long> userIds = tokenPermissionRepository.findAllApiIdsByUserIdAndStatus(user.getId(), Constants.RoleStatus.ACTIVE);
        List<String> apis = apiEndPointRepository.findByIdIn(userIds)
                .stream()
                .map(ApiEndpoint::getName)
                .collect(Collectors.toList());
        user.setAuthorities(apis);
        return user;
    }

    public User currentByMail(String email) {
        User user = this.userRepository.findOneByEmailIgnoreCase(email);
        if (user == null) {
            return null;
        }
        List<Long> roleIds = userRoleRepository.getRoleIdByUserId(user.getId());
        user.setRoles(roleRepository.findByIdInAndStatus(roleIds, RoleStatus.ACTIVE)
                .stream()
                .map(Role::getName)
                .collect(Collectors.toList()));
        user.setAuthorities(permissionRepository.getListPermissionKeyByRoleIds(roleIds));
        return user;
    }

    private Event processCurrentUser(Event event) {
        String token = (String) event.payload;
        if (token.length() == authorizationCodeLength) {
            Auth2RequestInit auth2RequestInit = new Auth2RequestInit();
            auth2RequestInit.setClient_id(CLIENT_ID);
            auth2RequestInit.setClient_secret(SECRET_ID);
            auth2RequestInit.setGrant_type("authorization_code");
            auth2RequestInit.setCode(token);
            Auth2Response auth2Response = oAuth2Util.oauth2Login(auth2RequestInit);
            if (auth2Response != null) {
                String[] chunks = auth2Response.getIdToken().split("\\.");
                Base64.Decoder decoder = Base64.getUrlDecoder();
                String payload = new String(decoder.decode(chunks[1]));
                JsonObject jsonObject = new Gson().fromJson(payload, JsonObject.class);
                String userName = jsonObject.get("userName").isJsonNull() ? " " : jsonObject.get("userName").getAsString();
                String email = jsonObject.get("email").getAsString();
                AuthResponseWithTokenAndErrorCodeDTO authResponseWithTokenAndErrorCodeDTO = createTokenEmail(email, userName);
                if (authResponseWithTokenAndErrorCodeDTO != null) {
                    token = authResponseWithTokenAndErrorCodeDTO.getAccessToken();
                }
            }
        }
        if (securityService.validateToken(token)) {
            Claims claims = securityService.parseToken(token);
            User user = current(claims);
            user.setTokenOauth2(token);
            return event.createResponse(user, ResponseCode.OK, null);
        }
        return event.createResponse(null, ResponseCode.INVALID_TOKEN, null);
    }

    private Event getCustomerCodesForGroupSim(Event event) {
        Long userId = (Long) event.payload;
        List<Long> customerIds = null;
        if (event.userType == UserType.CUSTOMER) {
            customerIds = userCustomerRepository.findCustomerIdsByUserId(userId);
        } else if (event.userType == UserType.MANAGER) {
            customerIds = userCustomerRepository.findCustomerIdsByUserGdv(userId);
        }
        List<String> customerCodes = customerRepository.findCustomerCodeByIdIn(customerIds);
        return event.createResponse(customerCodes, ResponseCode.OK, null);
    }

    private Event processForgotPasswordInit(Event event) {
        ForgotPasswordInfo info = (ForgotPasswordInfo) event.payload;
        User userEntity = this.userRepository.findOneByEmailIgnoreCase(info.getEmail());
        if (userEntity == null) {
            return handlerNoneEmail(event);
        }
        String token = UUID.randomUUID().toString();
        try {
            Long expirePeriodResetPassword = applicationProperties.getActivation().getExpirePeriodResetPassword();
            systemCache.put(userEntity.getUsername(), token);
            systemCache.expire(userEntity.getUsername(), expirePeriodResetPassword, TimeUnit.SECONDS, String.class);
        } catch (Throwable e) {
            logger.error("Error push key #{} to redis", userEntity.getEmail(), e);
            return event.createResponse(null, ResponseCode.BAD_REQUEST, null);
        }
        if (applicationProperties.getActivation().isEnableMail()) {
            try {
                mailService.sendPasswordResetMail(userEntity, token);
            } catch (Exception e) {
                e.printStackTrace();
            }
            logger.debug("Request forgot password user: #{}", userEntity.getEmail());
        }

        return event.createResponse(null, ResponseCode.OK, null);
    }

    @SuppressWarnings("Duplicates")
    private Event processForgotPasswordFinish(Event event) {
        ResetPasswordInfo resetPasswordInfo = (ResetPasswordInfo) event.payload;
        User userEntity = userRepository.findOneByEmailIgnoreCase(resetPasswordInfo.getEmail());
        if (userEntity == null) {
            return handlerNoneEmail(event);
        }
        if (!resetPasswordInfo.getNewPassword().equals(resetPasswordInfo.getConfirmPassword())) {
            return handlerNotMatchConfirmPassword(event);
        }

        try {
            // check token
            String token = (String) systemCache.get(userEntity.getUsername(), String.class);
            if (token == null || !token.equals(resetPasswordInfo.getForgotPasswordToken())) {
                return handlerInvalidToken(event);
            }
            systemCache.remove(userEntity.getUsername(), String.class);
            userEntity.setEncryptedPassword(resetPasswordInfo.getNewPassword());
            userRepository.save(userEntity);
            logger.debug("Change forgot password user: #{}", userEntity.getEmail());
            return event.createResponse(null, ResponseCode.OK, null);
        } catch (Throwable e) {
            logger.error("Error get,remove key #{} from redis", userEntity.getEmail(), e);
            return event.createResponse(null, ResponseCode.BAD_REQUEST, null);
        }
    }

    private Event processValidateTokenEmail(Event event) {
        ResetPasswordInfo resetPasswordInfo = (ResetPasswordInfo) event.payload;
        User userEntity = userRepository.findOneByEmailIgnoreCase(resetPasswordInfo.getEmail());
        if (userEntity == null) {
            return handlerNoneEmail(event);
        }
        try {
            // check token
            String token = (String) systemCache.get(userEntity.getUsername(), String.class);
            if (token == null || !token.equals(resetPasswordInfo.getForgotPasswordToken())) {
                return handlerInvalidToken(event);
            }
            return event.createResponse(null, ResponseCode.OK, null);
        } catch (Throwable e) {
            return event.createResponse(null, ResponseCode.BAD_REQUEST, null);
        }
    }

    private Event processUpdateProfile(Event event) {
        //TODO Lấy user đăng nhập
        UpdateUserReq updateUserReq = (UpdateUserReq) event.payload;
        // check email hoặc username tồn tại
        if (!validateUpdateUser(updateUserReq)) {
            return event.createResponse(Arrays.asList("email"), ResponseCode.CONFLICT, null);
        }
        Optional<User> optUpdateUser = userRepository.findById(event.userId);

        if (!optUpdateUser.isPresent()) {
            return event.createResponse(Arrays.asList("id"), ResponseCode.NOT_FOUND, null);
        }
        User updateUser = optUpdateUser.get();
        updateUser.setFullName(updateUserReq.getFullName());
        updateUser.setDescription(updateUserReq.getDescription());
        updateUser.setEmail(updateUserReq.getEmail());
        updateUser.setPhone(updateUserReq.getPhone());
        userRepository.save(updateUser);
        if (!Objects.isNull(updateUser.getIotUser()) && Objects.equals(updateUser.getIotUser(), IOT_USER)) {
            if (updateUser.getType().equals(UserType.CUSTOMER)) {
                if (Objects.isNull(updateUser.getIotUser()) || Objects.equals(updateUser.getIotUser(), NOT_IOT_USER)) {
                    CreateUserReq createUserReq = new CreateUserReq();
                    BeanUtils.copyProperties(updateUserReq, createUserReq);
                    createUserReq.setUsername(updateUser.getUsername());
                    Optional<User> optParentUser = userRepository.findById(updateUser.getCreatedBy());
                    if (optParentUser.isPresent()) {
                        User parent = optParentUser.get();
                        createSynchronizedIoTUser(parent, updateUser, createUserReq);
                    }
                } else {
                    if (updateIOTUser(updateUserReq, updateUser.getUsername(), updateUser.getUsername())) {
                        updateUser.setIotUser(IOT_USER);
                    } else {
                        updateUser.setSynchronizeStatus(FAIL);
                    }
                }
            }
        }
        // update status api
        List<ClientAuthentication> clientAuthenticationList = clientAuthenticationRepository.findAllByUserId(updateUser.getId());
        if (clientAuthenticationList != null && clientAuthenticationList.size() > 0 && updateUserReq.getStatusApi() != null) {
            clientAuthenticationRepository.updateStatusGrantApiUser(updateUser.getId(), updateUserReq.getStatusApi(),updateUserReq.getSecretId());
        }
        return event.createResponse(updateUser, ResponseCode.OK, null);
    }

    Event processChangePassword(Event event) {
        //TODO Lấy user đăng nhập
        User userLogin = userRepository.findById(event.userId).get();

        ChangePasswordInfo changePasswordInfo = (ChangePasswordInfo) event.payload;

        if (Boolean.FALSE.equals(userLogin.authenticate(changePasswordInfo.getOldPassword()))) {
            return event.createResponse(Validation.INVALID_PASSWORD, ResponseCode.BAD_REQUEST, null);
        }
        if (!changePasswordInfo.getNewPassword().equals(changePasswordInfo.getConfirmPassword())) {
            return handlerNotMatchConfirmPassword(event);
        }

        userLogin.setEncryptedPassword(changePasswordInfo.getNewPassword());
        userLogin.setUpdatedPasswordDate(new Date());
        userRepository.save(userLogin);
        return event.createResponse(null, ResponseCode.OK, null);
    }

    Event processGetOne(Event event) {
        Long id = (Long) event.payload;
        Optional<User> userOptional = userRepository.findById(id);
        if (userOptional.isPresent()) {
            return event.createResponse(userOptional.get(), ResponseCode.OK, null);
        } else {
            return event.createResponse(null, ResponseCode.NOT_FOUND, null);
        }
    }

    Event processViewProfile(Event event) {
        IGetUserDTO userDTO = userRepository.getOneUser(event.userId);
        // trường hợp id không tồn tại
        if (Objects.isNull(userDTO)) {
            return event.createResponse(Arrays.asList("id"), ResponseCode.NOT_FOUND, null);
        }
        UserResponseDTO userResponseDTO = new UserResponseDTO();
        BeanUtils.copyProperties(userDTO, userResponseDTO);
        // lấy ds role
        if (!StringUtils.isBlank(userDTO.getRoleNames()) && !StringUtils.isBlank(userDTO.getRoleIds())) {
            String[] roleNames = userDTO.getRoleNames().split(",");
            String[] roleIds = userDTO.getRoleIds().split(",");

            for (int i = 0; i < roleNames.length; i++) {
                UserResponseDTO.RoleDTO roleDTO = new UserResponseDTO.RoleDTO(roleNames[i], Long.parseLong(roleIds[i]));
                userResponseDTO.getRoles().add(roleDTO);
            }
        }
        // lấy ds customer
        List<Long> customerIds = userCustomerRepository.findCustomerIdsByUserId(event.userId);
        List<Customer> customerLst = customerRepository.findByIdIn(customerIds);
        for (Customer customer : customerLst) {
            UserResponseDTO.CustomerDTO customerDTO = new UserResponseDTO.CustomerDTO(customer.getCustomerCode(), customer.getCustomerName(),
                    customer.getId());
            userResponseDTO.getCustomers().add(customerDTO);
        }
        List<TokenPermission> tokenPermissionList = tokenPermissionRepository.getAllByUserId(userResponseDTO.getId());
        if (tokenPermissionList != null && tokenPermissionList.size() > 0) {
            userResponseDTO.setListApiId(tokenPermissionList.stream().map(t -> t.getApiId()).collect(Collectors.toList()));
        }
        List<ClientAuthentication> clientAuthenticationList = clientAuthenticationRepository.findAllByUserId(userResponseDTO.getId());
        if (clientAuthenticationList != null && clientAuthenticationList.size() > 0) {
            String secretId = clientAuthenticationList.get(0).getSecretId();
            userResponseDTO.setSecretId(secretId);
            userResponseDTO.setStatusApi(clientAuthenticationList.get(0).getActive());
        }
        return event.createResponse(userResponseDTO, ResponseCode.OK, null);
    }

    private Event handlerNoneEmail(Event event) {
        return event.createResponse(MessageKeyConstant.Validation.INVALID_EMAIL, ResponseCode.BAD_REQUEST, null);
    }

    private Event handlerNotMatchConfirmPassword(Event event) {
        return event.createResponse(Validation.PASSWORD_DO_NOT_MATCH, ResponseCode.BAD_REQUEST, null);
    }

    private Event handlerInvalidToken(Event event) {
        return event.createResponse(Validation.INVALID_TOKEN, ResponseCode.BAD_REQUEST, null);
    }

    public Event processGetListUserChild(Event event) {
        List<Long> lstUserId = userRepository.getListUserIdByUserLoginIncludeSameLevel(event.userType, event.userId, event.provinceCode);
        return event.createResponse(lstUserId, ResponseCode.OK, null);
    }

    private Event processDisagreePolicy(Event event) {
        try {
            JsonObject data = (JsonObject) event.payload;
            UserPolicy userPolicy = userPolicyRepository.getUserPolicyByUserIdAndPolicyId(data.get("userId").getAsLong(), data.get("policyId").getAsInt());

            if (userPolicy == null) {
                userPolicy = new UserPolicy();
                userPolicy.setUserId(data.get("userId").getAsLong());
                userPolicy.setPolicyId(data.get("policyId").getAsInt());
                userPolicy.setInfo(data.get("info").getAsString());
                userPolicy.setStatus(Constants.POLICY_STATUS.REJECT);
            } else if (userPolicy.getStatus().intValue() == Constants.POLICY_STATUS.AGREE.intValue()) {
                userPolicy.setStatus(Constants.POLICY_STATUS.DISAGREE);
                userPolicy.setInfo(data.get("info").getAsString());
            }
            userPolicy = userPolicyRepository.save(userPolicy);
            return event.createResponse(userPolicy, ResponseCode.OK, null);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return event.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, null);
    }

    private Event processAgreePolicy(Event event) {
        try {
            JsonObject data = (JsonObject) event.payload;
            UserPolicy userPolicy = userPolicyRepository.getUserPolicyByUserIdAndPolicyId(data.get("userId").getAsLong(), data.get("policyId").getAsInt());
            if (userPolicy == null) {
                userPolicy = new UserPolicy();
                userPolicy.setUserId(data.get("userId").getAsLong());
                userPolicy.setPolicyId(data.get("policyId").getAsInt());
            }
            userPolicy.setStatus(Constants.POLICY_STATUS.AGREE);
            userPolicy.setInfo(data.get("info").getAsString());
            userPolicy = userPolicyRepository.save(userPolicy);
            return event.createResponse(userPolicy, ResponseCode.OK, null);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return event.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, null);
    }

    private Event processGetListConfirmPolicyHistory(Event event) {
        try {
            Long userId = (Long) event.payload;
            List<UserPolicy> userPolicies = userPolicyRepository.findByUserId(userId);
            return event.createResponse(userPolicies, ResponseCode.OK, null);
        } catch (Exception e) {
        }
        return event.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, null);
    }

    private int getPeriodExpirePassword(Date dateCreated) {
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();

        Date date1 = dateCreated;
        Date date2 = new Date();

        c1.setTime(date1);
        c2.setTime(date2);

        long periodDay = (c2.getTime().getTime() - c1.getTime().getTime()) / (24 * 3600 * 1000);

        int periodMonth = Math.round((int) periodDay / 30);

        return periodMonth;
    }

    Event processChangeUserForManager(Event event) {
        //TODO Lấy user đăng nhập

        ChangeUserForManagerReq changeUserForManagerReq = (ChangeUserForManagerReq) event.payload;
        if (changeUserForManagerReq != null && changeUserForManagerReq.getOldUserId() != null && changeUserForManagerReq.getNewUserId() != null) {
            User userChanged = userRepository.findById(changeUserForManagerReq.getNewUserId()).get();
            User userOld = userRepository.findById(changeUserForManagerReq.getOldUserId()).get();
            if (!userChanged.getProvinceCode().equals(userOld.getProvinceCode()))
                return event.createResponse(null, ResponseCode.FORBIDDEN, null);
            if (changeUserForManagerReq.getAccountCustomerIds() != null) {
                Set<Long> userIdsSave = new HashSet<>(changeUserForManagerReq.getAccountCustomerIds());
                List<Long> userIdsLoop = changeUserForManagerReq.getAccountCustomerIds();
                if (userIdsLoop != null) {
                    userIdsLoop.forEach(dto -> {
                        List<Long> userChildUserIds = userRepository.getListUserHierarchy(dto);
                        if (userChildUserIds != null && userChildUserIds.size() > 0) {
                            userIdsSave.addAll(userChildUserIds);
                        }
                    });
                }

                if (userIdsSave.size() > 0)
                    userManageRepository.updateUserManageByUserManageIdAndUserIdIn(changeUserForManagerReq.getOldUserId(), changeUserForManagerReq.getNewUserId(), userIdsSave.stream().toList());
            } else {
                userManageRepository.updateUserManageByUserManageIdAndUserIdIn(changeUserForManagerReq.getOldUserId(), changeUserForManagerReq.getNewUserId(), List.of(-1L));
            }

            return event.createResponse(null, ResponseCode.OK, null);
        } else
            return event.createResponse(null, ResponseCode.NOT_FOUND, null);

    }

    private Event getManagedCustomerAccounts(Event event) {
        SearchUserCustomerManaged searchUserRequest = (SearchUserCustomerManaged) event.payload;
        BaseController.ListRequest listRequest = new BaseController.ListRequest(searchUserRequest.getSize(), searchUserRequest.getPage(),
                searchUserRequest.getSortBy());
//        List<String> lstProvinceCodeSearch = Arrays.asList(searchUserRequest.getProvinceCode().split(","));
        Page<ISearchUserDTO> pageUser = userRepository.getPageUserCustomerManaged(searchUserRequest, searchUserRequest.getUserManageId(), listRequest.getPageable());
        List<SearchUserResponseDTO> userResponseDTOList = pageUser.getContent().stream().map(userDTO -> {
            SearchUserResponseDTO userResponseDTO = new SearchUserResponseDTO();
            BeanUtils.copyProperties(userDTO, userResponseDTO);
//            if (isRootCustomer(userResponseDTO.getId())){
            return userResponseDTO;
//            }else return null;
        }).collect(Collectors.toList());
        userResponseDTOList.removeIf(e -> e == null);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(pageUser.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(userResponseDTOList));
        return event.createResponse(pageInfo, 200, null);
    }

    private Event getNoOneManagedCustomerAccounts(Event event) {
        SearchUserRequest searchUserRequest = (SearchUserRequest) event.payload;
        BaseController.ListRequest listRequest = new BaseController.ListRequest(searchUserRequest.getSize(), searchUserRequest.getPage(),
                searchUserRequest.getSortBy());
        Page<ISearchUserDTO> pageUser = userRepository.getPageUserNoOneManaged(searchUserRequest, event.userType, event.provinceCode, listRequest.getPageable());
        List<SearchUserResponseDTO> userResponseDTOList = pageUser.getContent().stream().map(userDTO -> {
            SearchUserResponseDTO userResponseDTO = new SearchUserResponseDTO();
            BeanUtils.copyProperties(userDTO, userResponseDTO);
            return userResponseDTO;
        }).collect(Collectors.toList());

        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(pageUser.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(userResponseDTOList));
        return event.createResponse(pageInfo, 200, null);
    }

    private Event processSearchUserForActivityLog(Event event) {
        SearchUserRequest searchUserRequest = (SearchUserRequest) event.payload;
        BaseController.ListRequest listRequest = new BaseController.ListRequest(searchUserRequest.getSize(), searchUserRequest.getPage(),
                searchUserRequest.getSortBy());
        List<String> lstProvinceCodeSearch = Arrays.asList(searchUserRequest.getProvinceCode().split(","));
        Page<ISearchUserDTO> pageUser = userRepository.getPageUserForActivityLog(searchUserRequest, event.userType, event.userId, event.provinceCode, lstProvinceCodeSearch, listRequest.getPageable());
        List<SearchUserResponseDTO> userResponseDTOList = pageUser.getContent().stream().map(userDTO -> {
            SearchUserResponseDTO userResponseDTO = new SearchUserResponseDTO();
            BeanUtils.copyProperties(userDTO, userResponseDTO);
            return userResponseDTO;
        }).collect(Collectors.toList());

        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(pageUser.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(userResponseDTOList));
        return event.createResponse(pageInfo, 200, null);
    }

    private Event processGetListActivatedAccount(Event event) {
        try {
            List<Long> listAccountId = (List<Long>) event.payload;
            List<Long> listActivatedAccountId = userRepository.getListActivatedAccount(listAccountId, UserStatus.ACTIVE);
            return event.createResponse(listActivatedAccountId, ResponseCode.OK, null);
        } catch (Exception e) {
        }
        return event.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, null);
    }

    public Event processGetByKey(Event event) {
        KeyValuePair keyValuePair = (KeyValuePair) event.payload;
        String query = keyValuePair.getKey() + "==" + keyValuePair.getValue();
        Node rootNode = new RSQLParser().parse(query);
        Specification<User> spec = rootNode.accept(new CustomRsqlVisitor<User>());
        return event.createResponse(userRepository.findAll(spec), ResponseCode.OK, null);
    }

    private Event processGetListModule3rd(Event event) {
        try {
            return event.createResponse(apiEndPointRepository.getDistinctModule(), ResponseCode.OK, null);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return event.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, null);
    }

    private Event processSearchApi3rd(Event event) {
        try {
            SearchApi3rdRequest request = (SearchApi3rdRequest) event.payload;
            BaseController.ListRequest listRequest = new BaseController.ListRequest(request.getSize(), request.getPage(),
                    request.getSortBy());
            Page<IApiEndpoint> page = apiEndPointRepository.findAllByApiPathAndModule(request, listRequest.getPageable());
            List<ApiEndpointDto> apiEndpointDtoList = page.getContent().stream().map(e -> {
                ApiEndpointDto dto = new ApiEndpointDto();
                BeanUtils.copyProperties(e, dto);

                return dto;
            }).collect(Collectors.toList());
            PageInfo pageInfo = new PageInfo();
            pageInfo.setTotalCount(page.getTotalElements());
            pageInfo.setData(ObjectMapperUtil.toJsonString(apiEndpointDtoList));
            return event.createResponse(pageInfo, 200, null);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return event.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, null);
    }

    private Event processSearchApi3rdByModule(Event event) {
        try {
            String request = (String) event.payload;
            List<IApiEndpoint> list = new ArrayList<>();
            if(request.trim().equals("")) {
                list = apiEndPointRepository.findAllByModule(null);
            }else{
                list = apiEndPointRepository.findAllByModule(request);
            }
            List<ApiEndpointDto> apiList = list.stream().map(e -> {
                ApiEndpointDto dto = new ApiEndpointDto();
                BeanUtils.copyProperties(e, dto);

                return dto;
            }).collect(Collectors.toList());
            return event.createResponse(apiList, 200, null);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return event.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, null);
    }

    private Event processSearchApi3rdForCustomerChild(Event event) {
        try {
            SearchApi3rdRequest request = (SearchApi3rdRequest) event.payload;
            BaseController.ListRequest listRequest = new BaseController.ListRequest(request.getSize(), request.getPage(),
                    request.getSortBy());

            Page<IApiEndpoint> page = apiEndPointRepository.findAllByApiPathAndModuleForCustomerChild(request, listRequest.getPageable());
            List<ApiEndpointDto> apiEndpointDtoList = page.getContent().stream().map(e -> {
                ApiEndpointDto dto = new ApiEndpointDto();
                BeanUtils.copyProperties(e, dto);

                return dto;
            }).collect(Collectors.toList());
            PageInfo pageInfo = new PageInfo();
            pageInfo.setTotalCount(page.getTotalElements());
            pageInfo.setData(ObjectMapperUtil.toJsonString(apiEndpointDtoList));
            return event.createResponse(pageInfo, 200, null);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return event.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, null);
    }
}
