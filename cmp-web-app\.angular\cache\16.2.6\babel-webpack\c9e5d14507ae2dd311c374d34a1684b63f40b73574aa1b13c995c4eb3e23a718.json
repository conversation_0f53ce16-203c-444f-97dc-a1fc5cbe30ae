{"ast": null, "code": "import { Validators } from \"@angular/forms\";\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\nimport { ComponentBase } from \"src/app/component.base\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/service/rating-plan/RatingPlanService\";\nimport * as i2 from \"src/app/service/customer/CustomerService\";\nimport * as i3 from \"src/app/service/group-sim/GroupSimService\";\nimport * as i4 from \"src/app/service/sim/SimService\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../../../service/account/AccountService\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"primeng/breadcrumb\";\nimport * as i10 from \"primeng/tooltip\";\nimport * as i11 from \"primeng/api\";\nimport * as i12 from \"primeng/inputtext\";\nimport * as i13 from \"primeng/button\";\nimport * as i14 from \"../../common-module/table/table.component\";\nimport * as i15 from \"../../common-module/input-file/input.file.component\";\nimport * as i16 from \"../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i17 from \"primeng/splitbutton\";\nimport * as i18 from \"primeng/calendar\";\nimport * as i19 from \"primeng/dropdown\";\nimport * as i20 from \"primeng/card\";\nimport * as i21 from \"primeng/dialog\";\nimport * as i22 from \"primeng/inputswitch\";\nimport * as i23 from \"primeng/radiobutton\";\nimport * as i24 from \"primeng/table\";\nimport * as i25 from \"primeng/togglebutton\";\nimport * as i26 from \"primeng/panel\";\nfunction AppRegisterPlanListComponent_p_splitButton_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-splitButton\", 57);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"global.button.registerRatingPlan\"))(\"model\", ctx_r0.itemRegisters);\n  }\n}\nconst _c0 = function () {\n  return [\"/plans/registers/history\"];\n};\nfunction AppRegisterPlanListComponent_p_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-button\", 58);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", ctx_r1.tranService.translate(\"global.button.historyRegisterPlan\"))(\"routerLink\", i0.ɵɵpureFunction0(2, _c0));\n  }\n}\nfunction AppRegisterPlanListComponent_p_dialog_48_span_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 76);\n    i0.ɵɵtext(1, \"ON\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppRegisterPlanListComponent_p_dialog_48_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 77);\n    i0.ɵɵtext(1, \"OFF\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppRegisterPlanListComponent_p_dialog_48_span_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 77);\n    i0.ɵɵtext(1, \"NOT FOUND\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = function () {\n  return {\n    width: \"980px\"\n  };\n};\nfunction AppRegisterPlanListComponent_p_dialog_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dialog\", 34);\n    i0.ɵɵlistener(\"visibleChange\", function AppRegisterPlanListComponent_p_dialog_48_Template_p_dialog_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.isShowModalDetailSim = $event);\n    });\n    i0.ɵɵelementStart(1, \"div\", 59)(2, \"div\", 60)(3, \"p-card\", 61)(4, \"div\", 62)(5, \"div\", 63)(6, \"div\", 47)(7, \"span\", 64);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 37);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 65)(12, \"span\", 64);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 66);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 65)(17, \"span\", 64);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 37);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 65)(22, \"span\", 64);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 37);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 65)(27, \"span\", 64);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"span\", 37);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 65)(32, \"span\", 64);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, AppRegisterPlanListComponent_p_dialog_48_span_34_Template, 2, 0, \"span\", 67);\n    i0.ɵɵtemplate(35, AppRegisterPlanListComponent_p_dialog_48_span_35_Template, 2, 0, \"span\", 68);\n    i0.ɵɵtemplate(36, AppRegisterPlanListComponent_p_dialog_48_span_36_Template, 2, 0, \"span\", 68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 63)(38, \"div\", 47)(39, \"span\", 64);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"span\", 37);\n    i0.ɵɵtext(42);\n    i0.ɵɵpipe(43, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"div\", 65)(45, \"span\", 64);\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"span\", 66);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(49, \"p-card\", 69)(50, \"div\", 47)(51, \"div\", 70)(52, \"p-toggleButton\", 71);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_p_dialog_48_Template_p_toggleButton_ngModelChange_52_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.detailStatusSim.statusData = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"div\");\n    i0.ɵɵtext(54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 70)(56, \"p-toggleButton\", 71);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_p_dialog_48_Template_p_toggleButton_ngModelChange_56_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.detailStatusSim.statusReceiveCall = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"div\");\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(59, \"div\", 70)(60, \"p-toggleButton\", 71);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_p_dialog_48_Template_p_toggleButton_ngModelChange_60_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.detailStatusSim.statusSendCall = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"div\");\n    i0.ɵɵtext(62);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(63, \"div\", 47)(64, \"div\", 70)(65, \"p-toggleButton\", 71);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_p_dialog_48_Template_p_toggleButton_ngModelChange_65_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.detailStatusSim.statusWorldCall = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"div\");\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 70)(69, \"p-toggleButton\", 71);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_p_dialog_48_Template_p_toggleButton_ngModelChange_69_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.detailStatusSim.statusReceiveSms = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(70, \"div\");\n    i0.ɵɵtext(71);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(72, \"div\", 70)(73, \"p-toggleButton\", 71);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_p_dialog_48_Template_p_toggleButton_ngModelChange_73_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.detailStatusSim.statusSendSms = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"div\");\n    i0.ɵɵtext(75);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(76, \"p-card\", 72)(77, \"div\", 47)(78, \"span\", 73);\n    i0.ɵɵtext(79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(80, \"span\", 37);\n    i0.ɵɵtext(81);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(82, \"div\", 65)(83, \"span\", 73);\n    i0.ɵɵtext(84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(85, \"span\", 37);\n    i0.ɵɵtext(86);\n    i0.ɵɵpipe(87, \"number\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(88, \"div\", 60)(89, \"p-card\", 61)(90, \"div\", 74)(91, \"span\", 73);\n    i0.ɵɵtext(92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(93, \"span\", 37);\n    i0.ɵɵtext(94);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(95, \"div\", 65)(96, \"span\", 73);\n    i0.ɵɵtext(97);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(98, \"span\", 37);\n    i0.ɵɵtext(99);\n    i0.ɵɵpipe(100, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(101, \"div\", 65)(102, \"span\", 73);\n    i0.ɵɵtext(103);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(104, \"span\", 75);\n    i0.ɵɵtext(105);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(106, \"div\", 65)(107, \"span\", 73);\n    i0.ɵɵtext(108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(109, \"span\", 37);\n    i0.ɵɵtext(110);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(111, \"div\", 65)(112, \"span\", 73);\n    i0.ɵɵtext(113);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(114, \"span\", 37);\n    i0.ɵɵtext(115);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(116, \"div\", 65)(117, \"span\", 73);\n    i0.ɵɵtext(118);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(119, \"span\", 37);\n    i0.ɵɵtext(120);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(121, \"div\", 65)(122, \"span\", 73);\n    i0.ɵɵtext(123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(124, \"span\", 75);\n    i0.ɵɵtext(125);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(126, \"div\", 65)(127, \"span\", 73);\n    i0.ɵɵtext(128);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(129, \"span\", 37);\n    i0.ɵɵtext(130);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(131, \"div\", 65)(132, \"span\", 73);\n    i0.ɵɵtext(133);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(134, \"span\", 37);\n    i0.ɵɵtext(135);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(136, \"p-card\", 72)(137, \"div\", 47)(138, \"span\", 73);\n    i0.ɵɵtext(139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(140, \"span\", 37);\n    i0.ɵɵtext(141);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(142, \"div\", 65)(143, \"span\", 73);\n    i0.ɵɵtext(144);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(145, \"span\", 37);\n    i0.ɵɵtext(146);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(85, _c1));\n    i0.ɵɵproperty(\"header\", ctx_r2.tranService.translate(\"sim.text.detailSim\"))(\"visible\", ctx_r2.isShowModalDetailSim)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"header\", ctx_r2.tranService.translate(\"sim.text.simInfo\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.sothuebao\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.detailSim.msisdn);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.trangthaisim\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r2.getClassStatus(ctx_r2.detailSim.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.getNameStatus(ctx_r2.detailSim.status));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.imsi\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.detailSim.imsi);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.imeiDevice\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.detailSim.imei);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.maapn\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.detailSim.apnId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.trangthaiketnoi\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.detailSim.connectionStatus !== undefined && ctx_r2.detailSim.connectionStatus !== null && ctx_r2.detailSim.connectionStatus !== \"0\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.detailSim.connectionStatus === \"0\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.detailSim.connectionStatus === undefined || ctx_r2.detailSim.connectionStatus === null);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.startDate\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(43, 77, ctx_r2.detailSim.startDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.serviceType\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getServiceType(ctx_r2.detailSim.serviceType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"header\", ctx_r2.tranService.translate(\"sim.text.simStatusInfo\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.detailStatusSim.statusData)(\"disabled\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.status.service.data\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.detailStatusSim.statusReceiveCall)(\"disabled\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.status.service.callReceived\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.detailStatusSim.statusSendCall)(\"disabled\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.status.service.callSent\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.detailStatusSim.statusWorldCall)(\"disabled\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.status.service.callWorld\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.detailStatusSim.statusReceiveSms)(\"disabled\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.status.service.smsReceived\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.detailStatusSim.statusSendSms)(\"disabled\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.status.service.smsSent\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"header\", ctx_r2.tranService.translate(\"sim.text.ratingPlanInfo\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.tengoicuoc\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.detailSim.ratingPlanName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.dataUseInMonth\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind1(87, 80, ctx_r2.utilService.bytesToMegabytes(ctx_r2.detailRatingPlan == null ? null : ctx_r2.detailRatingPlan.dataUseInMonth)), \" \", (ctx_r2.detailRatingPlan == null ? null : ctx_r2.detailRatingPlan.unit) ? ctx_r2.detailRatingPlan.unit : \"MB\", \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"header\", ctx_r2.tranService.translate(\"sim.text.contractInfo\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.mahopdong\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.detailContract.contractCode);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.ngaylamhopdong\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(100, 82, ctx_r2.detailContract.contractDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.nguoilamhopdong\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.detailContract.contractorInfo);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.matrungtam\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.detailContract.centerCode);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.dienthoailienhe\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.detailContract.contactPhone);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.diachilienhe\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.detailContract.contactAddress);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.paymentName\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.detailContract.paymentName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.paymentAddress\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.detailContract.paymentAddress);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.routeCode\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.detailContract.routeCode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"header\", ctx_r2.tranService.translate(\"sim.text.customerInfo\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.khachhang\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.detailCustomer.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"sim.label.customerCode\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.detailCustomer.code);\n  }\n}\nfunction AppRegisterPlanListComponent_p_dialog_50_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"span\", 81);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 94);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r21.tranService.translate(\"ratingPlan.label.province\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r21.myProvices);\n  }\n}\nconst _c2 = function () {\n  return {\n    \"width\": \"calc(100% + 16px)\"\n  };\n};\nfunction AppRegisterPlanListComponent_p_dialog_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dialog\", 34);\n    i0.ɵɵlistener(\"visibleChange\", function AppRegisterPlanListComponent_p_dialog_50_Template_p_dialog_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.isShowModalDetailPlan = $event);\n    });\n    i0.ɵɵelementStart(1, \"p-card\", 78)(2, \"div\", 79)(3, \"div\", 80)(4, \"div\", 47)(5, \"span\", 81);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 82);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 47)(10, \"span\", 81);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 82);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 47)(15, \"span\", 81);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 83)(18, \"span\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 47)(21, \"span\", 81);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 82);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 47)(26, \"span\", 81);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\", 82);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 47)(31, \"span\", 81);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"span\", 82);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 80)(36, \"div\", 47)(37, \"span\", 73);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"span\", 82);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 47)(42, \"span\", 82)(43, \"span\")(44, \"p-radioButton\", 84);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_p_dialog_50_Template_p_radioButton_ngModelChange_44_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.ratingPlanInfo.subscriptionType = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(45, \" \\u00A0 \");\n    i0.ɵɵelementStart(46, \"span\");\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(48, \"span\", 85)(49, \"span\")(50, \"p-radioButton\", 86);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_p_dialog_50_Template_p_radioButton_ngModelChange_50_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.ratingPlanInfo.subscriptionType = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(51, \" \\u00A0 \");\n    i0.ɵɵelementStart(52, \"span\");\n    i0.ɵɵtext(53);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(54, \"div\", 47)(55, \"span\", 73);\n    i0.ɵɵtext(56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"span\", 82);\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(59, AppRegisterPlanListComponent_p_dialog_50_div_59_Template, 5, 2, \"div\", 87);\n    i0.ɵɵelementStart(60, \"div\", 47)(61, \"span\", 73);\n    i0.ɵɵtext(62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"span\", 82);\n    i0.ɵɵtext(64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(65, \"div\", 47)(66, \"span\", 73);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"span\", 82);\n    i0.ɵɵtext(69);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(70, \"div\", 47)(71, \"span\", 73);\n    i0.ɵɵtext(72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"div\", 88)(74, \"p-inputSwitch\", 89);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_p_dialog_50_Template_p_inputSwitch_ngModelChange_74_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.checkedReload = $event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(75, \"div\", 65)(76, \"span\", 90);\n    i0.ɵɵtext(77);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(78, \"div\", 91)(79, \"div\", 80)(80, \"div\", 47)(81, \"span\", 81);\n    i0.ɵɵtext(82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(83, \"span\", 82);\n    i0.ɵɵtext(84);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(85, \"div\", 65)(86, \"span\", 81);\n    i0.ɵɵtext(87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"span\", 82);\n    i0.ɵɵtext(89);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(90, \"div\", 80)(91, \"div\", 47)(92, \"span\", 81);\n    i0.ɵɵtext(93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(94, \"span\", 82);\n    i0.ɵɵtext(95);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(96, \"div\", 65)(97, \"span\", 90);\n    i0.ɵɵtext(98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(99, \"div\", 92)(100, \"p-inputSwitch\", 89);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_p_dialog_50_Template_p_inputSwitch_ngModelChange_100_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.checkedFlexible = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(101, \"div\", 93)(102, \"div\", 80)(103, \"div\", 47)(104, \"span\", 81);\n    i0.ɵɵtext(105);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(106, \"span\", 82);\n    i0.ɵɵtext(107);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(108, \"div\", 47)(109, \"span\", 81);\n    i0.ɵɵtext(110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(111, \"span\", 82);\n    i0.ɵɵtext(112);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(113, \"div\", 47)(114, \"span\", 81);\n    i0.ɵɵtext(115);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(116, \"span\", 82);\n    i0.ɵɵtext(117);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(118, \"div\", 47)(119, \"span\", 81);\n    i0.ɵɵtext(120);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(121, \"span\", 82);\n    i0.ɵɵtext(122);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(123, \"div\", 47)(124, \"span\", 73);\n    i0.ɵɵtext(125);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(126, \"span\", 82);\n    i0.ɵɵtext(127);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(128, \"div\", 80)(129, \"div\", 47)(130, \"span\", 81);\n    i0.ɵɵtext(131);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(132, \"div\", 47)(133, \"span\", 81);\n    i0.ɵɵtext(134);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(67, _c1));\n    i0.ɵɵproperty(\"header\", ctx_r3.tranService.translate(\"global.menu.detailplan\"))(\"visible\", ctx_r3.isShowModalDetailPlan)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(68, _c2));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.planCode\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.code);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.planName\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.status\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r3.getClassStatus(ctx_r3.ratingPlanInfo.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.getNameStatus(ctx_r3.ratingPlanInfo.status));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.dispatchCode\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.dispatchCode);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.customerType\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getNameCustomerType(ctx_r3.ratingPlanInfo.customerType));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.description\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.description);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.subscriptionFee\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\"\", ctx_r3.ratingPlanInfo.subscriptionFee, \"\\u00A0 \\u00A0 \\u00A0 \\u00A0 \", ctx_r3.tranService.translate(\"ratingPlan.text.textDong\"), \"\\u00A0\", ctx_r3.tranService.translate(\"ratingPlan.text.vat\"), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", true)(\"value\", ctx_r3.subscriptionTypes[0].ip)(\"ngModel\", ctx_r3.ratingPlanInfo.subscriptionType);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.subscriptionType.post\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", true)(\"value\", ctx_r3.subscriptionTypes[1].ip)(\"ngModel\", ctx_r3.ratingPlanInfo.subscriptionType);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.subscriptionType.pre\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.ratingScope\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getRatingScope(ctx_r3.ratingPlanInfo.ratingScope));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.ratingPlanInfo.ratingScope == ctx_r3.planScopes.CUSTOMER);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.cycle\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getCycleTimeUnit(ctx_r3.ratingPlanInfo.cycleTimeUnit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.cycleInterval\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.cycleInterval);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.reload\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.checkedReload)(\"disabled\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.flat\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.limitDataUsage\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.limitDataUsage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.limitSmsOutside\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.limitSmsOutside);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.limitSmsInside\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.limitSmsInside);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.flexible\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.checkedFlexible)(\"disabled\", true);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.feePerDataUnit\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.feePerDataUnit);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.squeezedSpeed\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.downSpeed);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.feeSmsInside\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.feeSmsInside);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.feeSmsOutside\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.feeSmsOutside);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.maximumFee\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.maximumFee);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"/\\u00A0 \\u00A0 \\u00A0 \", ctx_r3.ratingPlanInfo.dataRoundUnit, \" \\u00A0 \\u00A0 \\u00A0 \\u00A0 KB\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"/\\u00A0 \\u00A0 \\u00A0 \", ctx_r3.ratingPlanInfo.squeezedSpeed, \"\");\n  }\n}\nfunction AppRegisterPlanListComponent_small_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.messageErrorUpload);\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_82_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 100);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 100);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 101);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 102);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r29.tranService.translate(\"sim.label.sothuebao\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r29.tranService.translate(\"sim.label.tengoicuoc\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r29.tranService.translate(\"sim.label.description\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r29.tranService.translate(\"global.text.action\"));\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_82_ng_template_3_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 112);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_p_table_82_ng_template_3_ng_template_3_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const simImport_r31 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(simImport_r31.msisdn = $event);\n    })(\"ngModelChange\", function AppRegisterPlanListComponent_p_table_82_ng_template_3_ng_template_3_Template_input_ngModelChange_0_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const simImport_r31 = i0.ɵɵnextContext().$implicit;\n      const ctx_r47 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r47.checkValueSimImportChange(simImport_r31));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const simImport_r31 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", simImport_r31.msisdn);\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_82_ng_template_3_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const simImport_r31 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate1(\" \", simImport_r31.msisdn, \" \");\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_82_ng_template_3_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r53 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 113);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_p_table_82_ng_template_3_ng_template_7_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r53);\n      const simImport_r31 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(simImport_r31.ratingPlanName = $event);\n    })(\"ngModelChange\", function AppRegisterPlanListComponent_p_table_82_ng_template_3_ng_template_7_Template_input_ngModelChange_0_listener() {\n      i0.ɵɵrestoreView(_r53);\n      const simImport_r31 = i0.ɵɵnextContext().$implicit;\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r54.checkValueSimImportChange(simImport_r31));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const simImport_r31 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", simImport_r31.ratingPlanName);\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_82_ng_template_3_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const simImport_r31 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate1(\" \", simImport_r31.ratingPlanName, \" \");\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_82_ng_template_3_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const simImport_r31 = i0.ɵɵnextContext().$implicit;\n    const ctx_r38 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r38.tranService.translate(simImport_r31.description));\n  }\n}\nconst _c3 = function (a0) {\n  return {\n    field: a0\n  };\n};\nfunction AppRegisterPlanListComponent_p_table_82_ng_template_3_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r39.tranService.translate(\"global.message.requiredField\", i0.ɵɵpureFunction1(1, _c3, ctx_r39.tranService.translate(\"sim.label.sothuebao\"))), \" \");\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_82_ng_template_3_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r40.tranService.translate(\"global.message.requiredField\", i0.ɵɵpureFunction1(1, _c3, ctx_r40.tranService.translate(\"sim.label.tengoicuoc\"))), \" \");\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_82_ng_template_3_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r41.tranService.translate(\"global.message.required\"), \" \");\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_82_ng_template_3_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r42.tranService.translate(\"global.message.invalidSubsciption\"), \" \");\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_82_ng_template_3_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r43 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r43.tranService.translate(\"global.message.formatContainVN\"), \" \");\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_82_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 103)(1, \"td\", 104)(2, \"p-cellEditor\");\n    i0.ɵɵtemplate(3, AppRegisterPlanListComponent_p_table_82_ng_template_3_ng_template_3_Template, 1, 1, \"ng-template\", 105);\n    i0.ɵɵtemplate(4, AppRegisterPlanListComponent_p_table_82_ng_template_3_ng_template_4_Template, 1, 1, \"ng-template\", 106);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\", 107)(6, \"p-cellEditor\");\n    i0.ɵɵtemplate(7, AppRegisterPlanListComponent_p_table_82_ng_template_3_ng_template_7_Template, 1, 1, \"ng-template\", 105);\n    i0.ɵɵtemplate(8, AppRegisterPlanListComponent_p_table_82_ng_template_3_ng_template_8_Template, 1, 1, \"ng-template\", 106);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\", 108);\n    i0.ɵɵtemplate(10, AppRegisterPlanListComponent_p_table_82_ng_template_3_span_10_Template, 2, 1, \"span\", 109);\n    i0.ɵɵtemplate(11, AppRegisterPlanListComponent_p_table_82_ng_template_3_span_11_Template, 2, 3, \"span\", 109);\n    i0.ɵɵtemplate(12, AppRegisterPlanListComponent_p_table_82_ng_template_3_span_12_Template, 2, 3, \"span\", 109);\n    i0.ɵɵtemplate(13, AppRegisterPlanListComponent_p_table_82_ng_template_3_span_13_Template, 2, 1, \"span\", 109);\n    i0.ɵɵtemplate(14, AppRegisterPlanListComponent_p_table_82_ng_template_3_span_14_Template, 2, 1, \"span\", 109);\n    i0.ɵɵtemplate(15, AppRegisterPlanListComponent_p_table_82_ng_template_3_span_15_Template, 2, 1, \"span\", 109);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 110)(17, \"span\", 111);\n    i0.ɵɵlistener(\"click\", function AppRegisterPlanListComponent_p_table_82_ng_template_3_Template_span_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r60);\n      const simImport_r31 = restoredCtx.$implicit;\n      const i_r33 = restoredCtx.rowIndex;\n      const ctx_r59 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r59.removeItemSimImport(simImport_r31, i_r33));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const simImport_r31 = ctx.$implicit;\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r30.mapFormSimImports[simImport_r31.keyForm]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"pEditableColumn\", simImport_r31.msisdn);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"pEditableColumn\", simImport_r31.ratingPlanName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r30.mapFormSimImports[simImport_r31.keyForm].invalid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.mapFormSimImports[simImport_r31.keyForm].controls.msisdn.hasError(\"required\") && !ctx_r30.mapFormSimImports[simImport_r31.keyForm].controls.ratingPlanName.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r30.mapFormSimImports[simImport_r31.keyForm].controls.msisdn.hasError(\"required\") && ctx_r30.mapFormSimImports[simImport_r31.keyForm].controls.ratingPlanName.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.mapFormSimImports[simImport_r31.keyForm].controls.msisdn.hasError(\"required\") && ctx_r30.mapFormSimImports[simImport_r31.keyForm].controls.ratingPlanName.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.mapFormSimImports[simImport_r31.keyForm].controls.msisdn.errors == null ? null : ctx_r30.mapFormSimImports[simImport_r31.keyForm].controls.msisdn.errors.pattern);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.mapFormSimImports[simImport_r31.keyForm].controls.ratingPlanName.errors == null ? null : ctx_r30.mapFormSimImports[simImport_r31.keyForm].controls.ratingPlanName.errors.pattern);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"pTooltip\", ctx_r30.tranService.translate(\"global.button.delete\"));\n  }\n}\nconst _c4 = function () {\n  return {\n    \"min-width\": \"100%\"\n  };\n};\nconst _c5 = function () {\n  return [5, 10, 20];\n};\nconst _c6 = function () {\n  return {\n    \"min-width\": \"50rem\"\n  };\n};\nfunction AppRegisterPlanListComponent_p_table_82_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r62 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-table\", 96, 97);\n    i0.ɵɵlistener(\"onPage\", function AppRegisterPlanListComponent_p_table_82_Template_p_table_onPage_0_listener($event) {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r61 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r61.pagingResultSimImport($event));\n    });\n    i0.ɵɵtemplate(2, AppRegisterPlanListComponent_p_table_82_ng_template_2_Template, 9, 4, \"ng-template\", 98);\n    i0.ɵɵtemplate(3, AppRegisterPlanListComponent_p_table_82_ng_template_3_Template, 18, 10, \"ng-template\", 99);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"paginator\", true)(\"rows\", ctx_r5.pageSizeSimImport)(\"first\", ctx_r5.rowFirstSimImport)(\"showCurrentPageReport\", true)(\"tableStyle\", i0.ɵɵpureFunction0(13, _c4))(\"currentPageReportTemplate\", ctx_r5.tranService.translate(\"global.text.templateTextPagination\"))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(14, _c5))(\"styleClass\", \"p-datatable-sm\")(\"totalRecords\", ctx_r5.simImportsOrigin == null ? null : ctx_r5.simImportsOrigin.length)(\"lazy\", true)(\"scrollHeight\", \"400px\")(\"value\", ctx_r5.simImports)(\"tableStyle\", i0.ɵɵpureFunction0(15, _c6));\n  }\n}\nfunction AppRegisterPlanListComponent_p_button_85_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 114);\n    i0.ɵɵlistener(\"click\", function AppRegisterPlanListComponent_p_button_85_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r63 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r63.registerForFile());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r6.checkValidListImport())(\"label\", ctx_r6.tranService.translate(\"global.button.save\"));\n  }\n}\nfunction AppRegisterPlanListComponent_small_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.messageErrorUpload);\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_96_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 100);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 100);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 101);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 102);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r66 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r66.tranService.translate(\"sim.label.sothuebao\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r66.tranService.translate(\"sim.label.tengoicuoc\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r66.tranService.translate(\"sim.label.description\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r66.tranService.translate(\"global.text.action\"));\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_96_ng_template_3_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r83 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 112);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_p_table_96_ng_template_3_ng_template_3_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r83);\n      const simImport_r68 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(simImport_r68.msisdn = $event);\n    })(\"ngModelChange\", function AppRegisterPlanListComponent_p_table_96_ng_template_3_ng_template_3_Template_input_ngModelChange_0_listener() {\n      i0.ɵɵrestoreView(_r83);\n      const simImport_r68 = i0.ɵɵnextContext().$implicit;\n      const ctx_r84 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r84.checkValueSimImportChange(simImport_r68));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const simImport_r68 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", simImport_r68.msisdn);\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_96_ng_template_3_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const simImport_r68 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate1(\" \", simImport_r68.msisdn, \" \");\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_96_ng_template_3_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r90 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 113);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_p_table_96_ng_template_3_ng_template_7_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r90);\n      const simImport_r68 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(simImport_r68.ratingPlanName = $event);\n    })(\"ngModelChange\", function AppRegisterPlanListComponent_p_table_96_ng_template_3_ng_template_7_Template_input_ngModelChange_0_listener() {\n      i0.ɵɵrestoreView(_r90);\n      const simImport_r68 = i0.ɵɵnextContext().$implicit;\n      const ctx_r91 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r91.checkValueSimImportChange(simImport_r68));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const simImport_r68 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", simImport_r68.ratingPlanName);\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_96_ng_template_3_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const simImport_r68 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵtextInterpolate1(\" \", simImport_r68.ratingPlanName, \" \");\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_96_ng_template_3_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const simImport_r68 = i0.ɵɵnextContext().$implicit;\n    const ctx_r75 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r75.tranService.translate(simImport_r68.description));\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_96_ng_template_3_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r76 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r76.tranService.translate(\"global.message.requiredField\", i0.ɵɵpureFunction1(1, _c3, ctx_r76.tranService.translate(\"sim.label.sothuebao\"))), \" \");\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_96_ng_template_3_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r77 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r77.tranService.translate(\"global.message.requiredField\", i0.ɵɵpureFunction1(1, _c3, ctx_r77.tranService.translate(\"sim.label.tengoicuoc\"))), \" \");\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_96_ng_template_3_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r78 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r78.tranService.translate(\"global.message.required\"), \" \");\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_96_ng_template_3_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r79 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r79.tranService.translate(\"global.message.invalidSubsciption\"), \" \");\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_96_ng_template_3_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r80 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r80.tranService.translate(\"global.message.formatContainVN\"), \" \");\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_96_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r97 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 103)(1, \"td\", 104)(2, \"p-cellEditor\");\n    i0.ɵɵtemplate(3, AppRegisterPlanListComponent_p_table_96_ng_template_3_ng_template_3_Template, 1, 1, \"ng-template\", 105);\n    i0.ɵɵtemplate(4, AppRegisterPlanListComponent_p_table_96_ng_template_3_ng_template_4_Template, 1, 1, \"ng-template\", 106);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\", 107)(6, \"p-cellEditor\");\n    i0.ɵɵtemplate(7, AppRegisterPlanListComponent_p_table_96_ng_template_3_ng_template_7_Template, 1, 1, \"ng-template\", 105);\n    i0.ɵɵtemplate(8, AppRegisterPlanListComponent_p_table_96_ng_template_3_ng_template_8_Template, 1, 1, \"ng-template\", 106);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\", 108);\n    i0.ɵɵtemplate(10, AppRegisterPlanListComponent_p_table_96_ng_template_3_span_10_Template, 2, 1, \"span\", 109);\n    i0.ɵɵtemplate(11, AppRegisterPlanListComponent_p_table_96_ng_template_3_span_11_Template, 2, 3, \"span\", 109);\n    i0.ɵɵtemplate(12, AppRegisterPlanListComponent_p_table_96_ng_template_3_span_12_Template, 2, 3, \"span\", 109);\n    i0.ɵɵtemplate(13, AppRegisterPlanListComponent_p_table_96_ng_template_3_span_13_Template, 2, 1, \"span\", 109);\n    i0.ɵɵtemplate(14, AppRegisterPlanListComponent_p_table_96_ng_template_3_span_14_Template, 2, 1, \"span\", 109);\n    i0.ɵɵtemplate(15, AppRegisterPlanListComponent_p_table_96_ng_template_3_span_15_Template, 2, 1, \"span\", 109);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 110)(17, \"span\", 111);\n    i0.ɵɵlistener(\"click\", function AppRegisterPlanListComponent_p_table_96_ng_template_3_Template_span_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r97);\n      const simImport_r68 = restoredCtx.$implicit;\n      const i_r70 = restoredCtx.rowIndex;\n      const ctx_r96 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r96.removeItemSimImport(simImport_r68, i_r70));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const simImport_r68 = ctx.$implicit;\n    const ctx_r67 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r67.mapFormSimImports[simImport_r68.keyForm]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"pEditableColumn\", simImport_r68.msisdn);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"pEditableColumn\", simImport_r68.ratingPlanName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r67.mapFormSimImports[simImport_r68.keyForm].invalid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r67.mapFormSimImports[simImport_r68.keyForm].controls.msisdn.hasError(\"required\") && !ctx_r67.mapFormSimImports[simImport_r68.keyForm].controls.ratingPlanName.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r67.mapFormSimImports[simImport_r68.keyForm].controls.msisdn.hasError(\"required\") && ctx_r67.mapFormSimImports[simImport_r68.keyForm].controls.ratingPlanName.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r67.mapFormSimImports[simImport_r68.keyForm].controls.msisdn.hasError(\"required\") && ctx_r67.mapFormSimImports[simImport_r68.keyForm].controls.ratingPlanName.hasError(\"required\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r67.mapFormSimImports[simImport_r68.keyForm].controls.msisdn.errors == null ? null : ctx_r67.mapFormSimImports[simImport_r68.keyForm].controls.msisdn.errors.pattern);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r67.mapFormSimImports[simImport_r68.keyForm].controls.ratingPlanName.errors == null ? null : ctx_r67.mapFormSimImports[simImport_r68.keyForm].controls.ratingPlanName.errors.pattern);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"pTooltip\", ctx_r67.tranService.translate(\"global.button.delete\"));\n  }\n}\nfunction AppRegisterPlanListComponent_p_table_96_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r99 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-table\", 96, 97);\n    i0.ɵɵlistener(\"onPage\", function AppRegisterPlanListComponent_p_table_96_Template_p_table_onPage_0_listener($event) {\n      i0.ɵɵrestoreView(_r99);\n      const ctx_r98 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r98.pagingResultSimImport($event));\n    });\n    i0.ɵɵtemplate(2, AppRegisterPlanListComponent_p_table_96_ng_template_2_Template, 9, 4, \"ng-template\", 98);\n    i0.ɵɵtemplate(3, AppRegisterPlanListComponent_p_table_96_ng_template_3_Template, 18, 10, \"ng-template\", 99);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"paginator\", true)(\"rows\", ctx_r8.pageSizeSimImport)(\"first\", ctx_r8.rowFirstSimImport)(\"showCurrentPageReport\", true)(\"tableStyle\", i0.ɵɵpureFunction0(13, _c4))(\"currentPageReportTemplate\", ctx_r8.tranService.translate(\"global.text.templateTextPagination\"))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(14, _c5))(\"styleClass\", \"p-datatable-sm\")(\"totalRecords\", ctx_r8.simImportsOrigin == null ? null : ctx_r8.simImportsOrigin.length)(\"lazy\", true)(\"scrollHeight\", \"400px\")(\"value\", ctx_r8.simImports)(\"tableStyle\", i0.ɵɵpureFunction0(15, _c6));\n  }\n}\nfunction AppRegisterPlanListComponent_p_button_99_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r101 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 114);\n    i0.ɵɵlistener(\"click\", function AppRegisterPlanListComponent_p_button_99_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r101);\n      const ctx_r100 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r100.registerForFile());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", !ctx_r9.checkValidListImport())(\"label\", ctx_r9.tranService.translate(\"global.button.save\"));\n  }\n}\nconst _c7 = function (a0, a1) {\n  return [a0, a1];\n};\nconst _c8 = function (a0) {\n  return [a0];\n};\nconst _c9 = function () {\n  return {\n    type: \"ratingPlan\"\n  };\n};\nconst _c10 = function () {\n  return {\n    type: \"customer\"\n  };\n};\nconst _c11 = function () {\n  return {\n    type: \"contract\"\n  };\n};\nconst _c12 = function () {\n  return {\n    width: \"500px\"\n  };\n};\nconst _c13 = function () {\n  return {\n    width: \"1000px\"\n  };\n};\nexport class AppRegisterPlanListComponent extends ComponentBase {\n  constructor(ratingPlanService, customerService, groupSimService, simService, formBuilder, accountService, injector) {\n    super(injector);\n    this.ratingPlanService = ratingPlanService;\n    this.customerService = customerService;\n    this.groupSimService = groupSimService;\n    this.simService = simService;\n    this.formBuilder = formBuilder;\n    this.accountService = accountService;\n    this.listRatingPlan = [];\n    this.listRatingPlanOrigin = [];\n    this.selectItems = [];\n    this.maxDateFrom = new Date();\n    this.minDateTo = null;\n    this.maxDateTo = new Date();\n    this.isShowDialogRegisterSim = false;\n    this.isShowDialogRegisterGroupSim = false;\n    this.isShowDialogResultRegisterGroupSim = false;\n    this.isShowDialogResultRegisterFile = false;\n    this.pageSizeSimImport = 10;\n    this.rowFirstSimImport = 0;\n    this.isShowErrorUpload = false;\n    this.isShowErrorGroup = false;\n    this.allPermissions = CONSTANTS.PERMISSIONS;\n    this.isShowModalDetailSim = false;\n    this.detailSim = {};\n    this.detailStatusSim = {};\n    this.detailCustomer = {};\n    this.detailRatingPlan = {};\n    this.detailContract = {};\n    this.detailAPN = {};\n    this.isShowModalDetailPlan = false;\n    this.subscriptionTypes = [];\n    this.response = {};\n    this.planScopes = CONSTANTS.RATING_PLAN_SCOPE;\n    this.typeRegisterForSim = \"register\";\n    this.CONSTANTS = CONSTANTS;\n  }\n  ngOnInit() {\n    let me = this;\n    this.userType = this.sessionService.userInfo.type;\n    this.items = [{\n      label: this.tranService.translate(\"global.menu.ratingplanmgmt\")\n    }, {\n      label: this.tranService.translate(\"global.menu.registerplan\")\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.itemRegisters = [{\n      label: this.tranService.translate(\"global.button.registerPlanForGroup\"),\n      command: () => {\n        me.isShowDialogRegisterGroupSim = true;\n      },\n      visible: this.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_BY_LIST])\n    }, {\n      label: this.tranService.translate(\"global.button.registerPlanByFile\"),\n      command: () => {\n        me.isShowDialogResultRegisterFile = true;\n        //clear input file\n        me.observableService.next(CONSTANTS.OBSERVABLE.KEY_INPUT_FILE_VNPT, {});\n        me.simImportsOrigin = undefined;\n        me.isShowErrorUpload = false;\n      },\n      visible: this.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_BY_FILE])\n    }];\n    this.detailSim = {};\n    this.ratingPlanInfo = {\n      id: null,\n      code: null,\n      name: null,\n      status: null,\n      dispatchCode: null,\n      customerType: null,\n      subscriptionFee: null,\n      subscriptionType: null,\n      ratingScope: null,\n      cycleTimeUnit: null,\n      cycleInterval: null,\n      reload: null,\n      flat: null,\n      limitDataUsage: null,\n      limitSmsOutside: null,\n      limitSmsInside: null,\n      flexible: null,\n      feePerDataUnit: null,\n      squeezedSpeed: null,\n      feeSmsInside: null,\n      feeSmsOutside: null,\n      maximumFee: null,\n      dataRoundUnit: null,\n      downSpeed: null,\n      provinceCode: null,\n      description: null\n    };\n    this.listStatus = [\n    // {\n    //     value: CONSTANTS.SIM_STATUS.READY,\n    //     name: this.tranService.translate(\"sim.status.ready\")\n    // },\n    {\n      value: [CONSTANTS.SIM_STATUS.ACTIVATED],\n      name: this.tranService.translate(\"sim.status.activated\")\n    }, {\n      value: [CONSTANTS.SIM_STATUS.INACTIVED],\n      name: this.tranService.translate(\"sim.status.inactivated\")\n    }, {\n      value: [CONSTANTS.SIM_STATUS.DEACTIVATED],\n      name: this.tranService.translate(\"sim.status.deactivated\")\n    }, {\n      value: [CONSTANTS.SIM_STATUS.PURGED],\n      name: this.tranService.translate(\"sim.status.purged\")\n    }, {\n      value: [15 + CONSTANTS.SIM_STATUS.ACTIVATED, 15 + CONSTANTS.SIM_STATUS.READY],\n      name: this.tranService.translate(\"sim.status.processingChangePlan\")\n    }, {\n      value: [10 + CONSTANTS.SIM_STATUS.ACTIVATED, 10 + CONSTANTS.SIM_STATUS.READY],\n      name: this.tranService.translate(\"sim.status.processingRegisterPlan\")\n    }, {\n      value: [20 + CONSTANTS.SIM_STATUS.ACTIVATED, 20 + CONSTANTS.SIM_STATUS.READY],\n      name: this.tranService.translate(\"sim.status.waitingCancelPlan\")\n    }];\n    this.searchInfo = {\n      msisdn: null,\n      status: null,\n      imei: null,\n      ratingPlanId: null,\n      customer: null,\n      contractCode: null,\n      dateFrom: null,\n      dateTo: null\n    };\n    this.formSearch = this.formBuilder.group(this.searchInfo);\n    this.selectItems = [];\n    this.pageNumber = 0;\n    this.pageSize = 10;\n    // this.sort = \"createdDate,asc\";\n    this.sort = \"ratingPlanName,asc\";\n    this.optionTable = {\n      hasClearSelected: true,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false,\n      action: [{\n        icon: \"pi pi-file\",\n        tooltip: this.tranService.translate(\"global.button.registerRatingPlan\"),\n        func: function (id, item) {\n          me.simSelected = item;\n          me.headerDialogRegisterForSim = me.tranService.translate(\"global.button.registerRatingPlan\");\n          me.typeRegisterForSim = \"register\";\n          me.getListRatingPlanByCustomerCode(item.customerCode);\n          // me.listRatingPlan = [...me.listRatingPlanOrigin];\n          me.isShowDialogRegisterSim = true;\n        },\n        funcAppear: function (id, item) {\n          return me.listRatingPlanOrigin && (item.status == CONSTANTS.SIM_STATUS.ACTIVATED || item.status == CONSTANTS.SIM_STATUS.READY) && item.ratingPlanId == null && me.listRatingPlan.length > 0 && me.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_PLAN]);\n        }\n      }, {\n        icon: \"pi pi-sync\",\n        tooltip: this.tranService.translate(\"global.button.changeRatingPlan\"),\n        func: function (id, item) {\n          me.simSelected = item;\n          me.headerDialogRegisterForSim = me.tranService.translate(\"global.button.changeRatingPlan\");\n          me.typeRegisterForSim = \"change\";\n          me.getListRatingPlanByCustomerCode(item.customerCode);\n          me.listRatingPlanOrigin = me.listRatingPlanOrigin.filter(el => el.id != item.ratingPlanId);\n          me.isShowDialogRegisterSim = true;\n        },\n        funcAppear: function (id, item) {\n          return me.listRatingPlanOrigin && item.ratingPlanId != null && (item.status == CONSTANTS.SIM_STATUS.ACTIVATED || item.status == CONSTANTS.SIM_STATUS.READY) && me.listRatingPlan.length > 0 && me.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_PLAN]);\n        }\n      }, {\n        icon: \"pi pi-times\",\n        tooltip: this.tranService.translate(\"global.button.cancelRatingPlan\"),\n        func: function (id, item) {\n          me.simSelected = item;\n          me.typeRegisterForSim = \"change\";\n          me.messageCommonService.confirm(me.tranService.translate(\"global.button.cancelRatingPlan\"), me.tranService.translate(\"global.message.confirmCancelPlan\", {\n            planName: item.ratingPlanName,\n            msisdn: item.msisdn\n          }), {\n            ok: () => {\n              me.ratingPlanService.cancelPlanForSubcriber(item.msisdn, item.ratingPlanId, () => {\n                me.messageCommonService.success(me.tranService.translate(\"ratingPlan.text.textCancelSuccess\"));\n                me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n              });\n            }\n          });\n        },\n        funcAppear: function (id, item) {\n          return me.listRatingPlanOrigin && item.ratingPlanId != null && (item.status == CONSTANTS.SIM_STATUS.ACTIVATED || item.status == CONSTANTS.SIM_STATUS.READY) && me.listRatingPlan.length > 0 && me.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_PLAN]);\n        }\n      }]\n    }, this.columns = [{\n      name: this.tranService.translate(\"sim.label.sothuebao\"),\n      key: \"msisdn\",\n      size: \"200px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      style: {\n        cursor: \"pointer\",\n        color: \"var(--mainColorText)\"\n      },\n      funcClick(id, item) {\n        me.simId = item?.msisdn.toString();\n        me.getDetailSim();\n        me.isShowModalDetailSim = true;\n      }\n    }, {\n      name: this.tranService.translate(\"sim.label.trangthaisim\"),\n      key: \"status\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcGetClassname: value => {\n        if (value == 0) {\n          return ['p-2', \"border-round\", \"border-400\", \"text-color\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.READY) {\n          // return ['p-1', \"bg-blue-600\", \"border-round\",\"inline-block\"];\n          return ['p-2', 'text-green-800', \"bg-green-100\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\n          return ['p-2', 'text-green-800', \"bg-green-100\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\n          return ['p-2', 'text-yellow-800', \"bg-yellow-100\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\n          return ['p-2', 'text-indigo-600', \"bg-indigo-100\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\n          return ['p-2', 'text-red-700', \"bg-red-100\", \"border-round\", \"inline-block\"];\n        } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\n          return ['p-2', 'text-cyan-800', \"bg-cyan-100\", \"border-round\", \"inline-block\"];\n        } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\n          return ['p-2', 'text-teal-800', \"bg-teal-100\", \"border-round\", \"inline-block\"];\n        } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\n          return ['p-2', 'text-orange-700', \"bg-orange-100\", \"border-round\", \"inline-block\"];\n        }\n        return [];\n      },\n      funcConvertText: value => {\n        if (value == 0) {\n          return me.tranService.translate(\"sim.status.inventory\");\n        } else if (value == CONSTANTS.SIM_STATUS.READY) {\n          // return me.tranService.translate(\"sim.status.ready\");\n          return me.tranService.translate(\"sim.status.activated\");\n        } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\n          return me.tranService.translate(\"sim.status.activated\");\n        } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\n          return me.tranService.translate(\"sim.status.deactivated\");\n        } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\n          return me.tranService.translate(\"sim.status.purged\");\n        } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\n          return me.tranService.translate(\"sim.status.inactivated\");\n        } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\n          return this.tranService.translate(\"sim.status.processingChangePlan\");\n        } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\n          return this.tranService.translate(\"sim.status.processingRegisterPlan\");\n        } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\n          return this.tranService.translate(\"sim.status.waitingCancelPlan\");\n        }\n        return \"\";\n      },\n      style: {\n        color: \"white\"\n      }\n    }, {\n      name: this.tranService.translate(\"sim.label.goicuoc\"),\n      key: \"ratingPlanName\",\n      size: \"200px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      style: {\n        cursor: \"pointer\",\n        color: \"var(--mainColorText)\"\n      },\n      funcClick(id, item) {\n        me.planId = item?.ratingPlanId;\n        me.getDetailPLan();\n        me.checkSubscriptionType();\n        me.isShowModalDetailPlan = true;\n      },\n      className: \"white-space-normal\"\n    }, {\n      name: this.tranService.translate(\"sim.label.imeiDevice\"),\n      key: \"imei\",\n      size: \"175px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"sim.label.khachhang\"),\n      key: \"customerName\",\n      size: \"250px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      className: \"white-space-normal\"\n    }, {\n      name: this.tranService.translate(\"sim.label.mahopdong\"),\n      key: \"contractCode\",\n      size: \"200px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"sim.label.ngaylamhopdong\"),\n      key: \"contractDate\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        return me.utilService.convertLongDateToString(value);\n      }\n    }];\n    this.getListRatingPlan();\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n    this.columnsResultRegisterGroup = [{\n      name: this.tranService.translate(\"sim.label.sothuebao\"),\n      key: \"msisdn\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      style: {\n        cursor: \"pointer\",\n        color: \"var(--mainColorText)\"\n      },\n      funcGetRouting(item) {\n        return [`/sims/detail/${item.imsi}`];\n      }\n    }, {\n      name: this.tranService.translate(\"sim.label.trangthaisim\"),\n      key: \"status\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcGetClassname: value => {\n        if (value == 0) {\n          return ['p-1', \"border-round\", \"border-400\", \"text-color\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.READY) {\n          // return ['p-1', \"bg-blue-600\", \"border-round\",\"inline-block\"];\n          return ['p-1', \"bg-green-600\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\n          return ['p-1', \"bg-green-600\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\n          return ['p-1', \"bg-yellow-500\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\n          return ['p-1', \"bg-indigo-600\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\n          return ['p-1', \"bg-red-500\", \"border-round\", \"inline-block\"];\n        } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\n          return ['p-2', 'text-cyan-800', \"bg-cyan-100\", \"border-round\", \"inline-block\"];\n        } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\n          return ['p-2', 'text-teal-800', \"bg-teal-100\", \"border-round\", \"inline-block\"];\n        } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\n          return ['p-2', 'text-orange-700', \"bg-orange-100\", \"border-round\", \"inline-block\"];\n        }\n        return [];\n      },\n      funcConvertText: value => {\n        if (value == 0) {\n          return me.tranService.translate(\"sim.status.inventory\");\n        } else if (value == CONSTANTS.SIM_STATUS.READY) {\n          // return me.tranService.translate(\"sim.status.ready\");\n          return me.tranService.translate(\"sim.status.activated\");\n        } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\n          return me.tranService.translate(\"sim.status.activated\");\n        } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\n          return me.tranService.translate(\"sim.status.deactivated\");\n        } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\n          return me.tranService.translate(\"sim.status.purged\");\n        } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\n          return me.tranService.translate(\"sim.status.inactivated\");\n        } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\n          return this.tranService.translate(\"sim.status.processingChangePlan\");\n        } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\n          return this.tranService.translate(\"sim.status.processingRegisterPlan\");\n        } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\n          return this.tranService.translate(\"sim.status.waitingCancelPlan\");\n        }\n        return \"\";\n      },\n      style: {\n        color: \"white\"\n      }\n    }, {\n      name: this.tranService.translate(\"sim.label.khachhang\"),\n      key: \"customerName\",\n      size: \"250px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcGetClassname: value => {\n        return ['uppercase', 'white-space-normal'];\n      }\n    }, {\n      name: this.tranService.translate(\"sim.label.mahopdong\"),\n      key: \"contractCode\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }];\n    this.optionTableResultRegisterGroup = {\n      hasClearSelected: true,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false,\n      hasShowJumpPage: false\n    };\n    this.optionInputFile = {\n      type: ['xls', 'xlsx'],\n      messageErrorType: this.tranService.translate(\"global.message.wrongFileExcel\"),\n      maxSize: 10,\n      unit: \"MB\",\n      required: true,\n      isShowButtonUpload: true,\n      actionUpload: this.uploadFile.bind(this),\n      disabled: false\n    };\n  }\n  ngAfterContentChecked() {\n    if (this.isShowDialogRegisterSim == false) {\n      this.planSelected = null;\n    }\n    if (this.isShowDialogRegisterGroupSim == false) {\n      this.planGroupSelected = null;\n      this.groupSimSelected = null;\n    }\n  }\n  onSubmitSearch() {\n    this.pageNumber = 0;\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n  }\n  search(page, limit, sort, params) {\n    let me = this;\n    this.pageNumber = page;\n    this.pageSize = limit;\n    this.sort = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    this.updateParams(dataParams);\n    this.dataSet = {\n      content: [],\n      total: 0\n    };\n    me.messageCommonService.onload();\n    this.simService.search(dataParams, response => {\n      // console.log(dataParams)\n      me.dataSet = {\n        content: response.content,\n        total: response.totalElements\n      };\n      // console.log(this.dataSet)\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  updateParams(dataParams) {\n    let me = this;\n    Object.keys(this.searchInfo).forEach(key => {\n      if (this.searchInfo[key] != null) {\n        if (key == \"dateFrom\") {\n          dataParams[\"contractDateFrom\"] = this.searchInfo.dateFrom.getTime();\n        } else if (key == \"dateTo\") {\n          dataParams[\"contractDateTo\"] = this.searchInfo.dateTo.getTime();\n        } else if (key == \"contractCode\") {\n          dataParams[\"contractCode\"] = me.utilService.stringToStrBase64(this.searchInfo.contractCode);\n        } else {\n          dataParams[key] = this.searchInfo[key];\n        }\n      }\n    });\n  }\n  getListRatingPlan() {\n    let me = this;\n    this.ratingPlanService.getAllRatingPlanPushForUser(response => {\n      me.listRatingPlan = (response || []).map(el => {\n        return {\n          ...el,\n          display: `${el.name || 'unknown'} - ${el.code || 'unknown'}`\n        };\n      });\n      me.listRatingPlan.sort((a, b) => (a.name || \"\").toUpperCase().localeCompare((b.name || \"\").toUpperCase()) > 0 ? 1 : -1);\n      // me.listRatingPlanOrigin = [...this.listRatingPlan];\n    });\n  }\n\n  getListRatingPlanByCustomerCode(customerCode) {\n    let me = this;\n    let param = {\n      \"customerCode\": customerCode\n    };\n    this.ratingPlanService.getListRatingPlanByCustomerCode(param, response => {\n      me.listRatingPlanOrigin = (response || []).map(el => {\n        return {\n          ...el,\n          display: `${el.name || 'unknown'} - ${el.code || 'unknown'}`\n        };\n      });\n    });\n  }\n  onChangeDateFrom(value) {\n    if (value) {\n      this.minDateTo = value;\n    } else {\n      this.minDateTo = null;\n    }\n  }\n  onChangeDateTo(value) {\n    if (value) {\n      this.maxDateFrom = value;\n    } else {\n      this.maxDateFrom = new Date();\n    }\n  }\n  clearFileCallback() {\n    this.isShowErrorUpload = false;\n  }\n  downloadTemplate() {\n    this.ratingPlanService.downloadTemplate();\n  }\n  uploadFile(objectFile) {\n    let me = this;\n    me.messageCommonService.onload();\n    this.ratingPlanService.uploadRegisterByFile(objectFile, resApi => {\n      me.excuteResponseRegisterFileOrList(resApi);\n    });\n  }\n  excuteResponseRegisterFileOrList(resApi) {\n    let me = this;\n    me.simImportsOrigin = undefined;\n    me.isShowErrorUpload = false;\n    this.optionInputFile.disabled = true;\n    let response = resApi.listRatingError || [];\n    if (resApi.total == 0) {\n      me.messageCommonService.error(me.tranService.translate(resApi.message));\n      me.isShowDialogResultRegisterFile = false;\n      me.isShowDialogResultRegisterGroupSim = false;\n      me.isShowDialogRegisterGroupSim = false;\n      return;\n    }\n    if (resApi.message.toUpperCase() == \"ok\".toUpperCase()) {\n      me.messageCommonService.warning(me.tranService.translate(\"ratingPlan.text.textResultRegisterByFile\", {\n        error: resApi.total - resApi.error,\n        total: resApi.total\n      }));\n    } else {\n      me.messageCommonService.error(me.tranService.translate(resApi.message));\n    }\n    if (response.length == 0) {\n      me.isShowDialogResultRegisterFile = false;\n      me.isShowDialogResultRegisterGroupSim = false;\n      me.isShowDialogRegisterGroupSim = false;\n      return;\n    }\n    //0 normal, 1 error imsi, 2 error planName\n    let index = 0;\n    me.mapFormSimImports = {};\n    me.mapPlanNameError = {};\n    me.mapImsiError = {};\n    let excludeDescription = ['error.invalid.isdn.empty', \"error.invalid.isdn.not.format\", \"error.invalid.rating.empty\", \"error.invalid.rating.not.format\"];\n    response.forEach(el => {\n      if (!excludeDescription.includes(el.description)) {\n        if (parseInt(el.type) == 0) {\n          if (el.msisdn != null && el.msisdn != \"\" && /^(\\+?84)[1-9][0-9]{8,9}$/.test(el.msisdn)) {\n            if ((el.description || \"\") != \"\") {\n              if (el.description in me.mapImsiError) {\n                me.mapImsiError[el.description].push(el.msisdn);\n              } else {\n                me.mapImsiError[el.description] = [el.msisdn];\n              }\n            }\n          }\n        } else if (parseInt(el.type) == 1) {\n          if (el.ratingPlanName != null && el.ratingPlanName != \"\" && /^[a-zA-Z0-9\\-_]*$/.test(el.ratingPlanName)) {\n            if ((el.description || \"\") != \"\") {\n              if (el.description in me.mapPlanNameError) {\n                me.mapPlanNameError[el.description].push(el.planName);\n              } else {\n                me.mapPlanNameError[el.description] = [el.planName];\n              }\n            }\n          }\n        }\n      } else {\n        el.description = \"\";\n      }\n      el['keyForm'] = `keyForm${index++}`;\n      me.mapFormSimImports[el['keyForm']] = me.formBuilder.group(el);\n      me.mapFormSimImports[el['keyForm']].controls['msisdn'].setValidators([Validators.required, Validators.pattern('^(\\\\+?84)[1-9][0-9]{8,9}$')]);\n      me.mapFormSimImports[el['keyForm']].controls['msisdn'].updateValueAndValidity();\n      me.mapFormSimImports[el['keyForm']].controls['ratingPlanName'].setValidators([Validators.required, Validators.pattern('^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$')]);\n      me.mapFormSimImports[el['keyForm']].controls['ratingPlanName'].updateValueAndValidity();\n    });\n    me.rowFirstSimImport = 0;\n    me.pageSizeSimImport = 10;\n    me.simImportsOrigin = [...response];\n    me.simImports = me.simImportsOrigin.slice(0, 10);\n  }\n  removeItemSimImport(item, index) {\n    // console.log(index);\n    this.simImportsOrigin.splice(index, 1);\n    this.simImports = this.simImportsOrigin.slice(this.rowFirstSimImport, this.rowFirstSimImport + this.pageSizeSimImport);\n    delete this.mapFormSimImports[item['keyForm']];\n    if (this.simImportsOrigin.length == 0) {\n      this.isShowDialogResultRegisterFile = false;\n    }\n  }\n  checkValidListImport() {\n    if (this.simImports.length == 0) {\n      return false;\n    }\n    let keys = Object.keys(this.mapFormSimImports);\n    for (let i = 0; i < keys.length; i++) {\n      let key = keys[i];\n      if (this.mapFormSimImports[key].invalid) {\n        return false;\n      }\n    }\n    for (let i = 0; i < this.simImports.length; i++) {\n      if (this.simImports[i].description != null && this.simImports[i].description != \"\") {\n        return false;\n      }\n    }\n    return true;\n  }\n  checkValueSimImportChange(item) {\n    if (item.ratingPlanName != null && item.msisdn != null) {\n      let description = \"\";\n      let keyImsis = Object.keys(this.mapImsiError);\n      let keyPlans = Object.keys(this.mapPlanNameError);\n      for (let i = 0; i < keyImsis.length; i++) {\n        if (this.mapImsiError[keyImsis[i]].includes(item.msisdn)) {\n          description = keyImsis[i];\n          break;\n        }\n      }\n      if (description == \"\") {\n        for (let i = 0; i < keyPlans.length; i++) {\n          if (this.mapPlanNameError[keyPlans[i]].includes(item.ratingPlanName)) {\n            description = keyPlans[i];\n            break;\n          }\n        }\n      }\n      // console.log(description);\n      if (description.indexOf(\"duplicated\") >= 0) {\n        let len = this.simImportsOrigin.map(el => el.msisdn).filter(el => el == item.msisdn).length;\n        if (len == 1) {\n          description = \"\";\n          this.simImportsOrigin.forEach(el => {\n            if (el.description.indexOf(\"duplicated\") >= 0 && el.msisdn == item) {\n              el.description = \"\";\n            }\n          });\n        }\n      }\n      item.description = description;\n    }\n  }\n  registerForSim() {\n    if (this.planSelected == null || this.planSelected == undefined) return;\n    let dataRequest = {\n      \"userId\": 1,\n      \"msisdn\": this.simSelected.msisdn,\n      \"ratingPlanId\": this.planSelected\n    };\n    let me = this;\n    me.messageCommonService.onload();\n    this.ratingPlanService.registerPlanForSim(dataRequest, response => {\n      if (me.typeRegisterForSim == \"register\") {\n        me.messageCommonService.success(me.tranService.translate(\"ratingPlan.text.textRegisterSuccess\"));\n      } else {\n        me.messageCommonService.success(me.tranService.translate(\"ratingPlan.text.textChangeSuccess\"));\n      }\n      me.isShowDialogRegisterSim = false;\n      me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n    });\n  }\n  registerForGroupSim() {\n    if (this.planGroupSelected == null || this.planGroupSelected == undefined || this.groupSimSelected == null || this.groupSimSelected == undefined) return;\n    let dataRequest = {\n      \"userId\": 1,\n      \"groupSimId\": this.groupSimSelected,\n      \"ratingPlanId\": this.planGroupSelected\n    };\n    let me = this;\n    me.messageCommonService.onload();\n    this.ratingPlanService.registerPlanForGroupSim(dataRequest, response => {\n      // me.messageCommonService.success(me.tranService.translate(\"global.message.success\"));\n      // me.isShowDialogRegisterSim = false;\n      // me.search(me.pageNumber,me.pageSize, me.sort, me.searchInfo);\n      //\n      // me.dataResultRegisterGroupOrigin = [{\n      //     imsi: \"841388100826\",\n      //     status: CONSTANTS.SIM_STATUS.ACTIVATED,\n      //     customerInfo: \"UBND Thị trấn Vân Đình\",\n      //     contractCode: \"HNI-LD/01060626\"\n      // },{\n      //     imsi: \"84842138660\",\n      //     status: CONSTANTS.SIM_STATUS.PURGED,\n      //     customerInfo: \"Nguyễn Văn A\",\n      //     contractCode: \"HNI-LD/00661242\"\n      // },{\n      //     imsi: \"84842139301\",\n      //     status: CONSTANTS.SIM_STATUS.ACTIVATED,\n      //     customerInfo: \"Tổng công ty Điện Lực Thành phố Hà Nội\",\n      //     contractCode: \"HNI-LD/01060638\"\n      // },{\n      //     imsi: \"841388100826\",\n      //     status: CONSTANTS.SIM_STATUS.ACTIVATED,\n      //     customerInfo: \"UBND Thị trấn Vân Đình\",\n      //     contractCode: \"HNI-LD/01060626\"\n      // },{\n      //     imsi: \"84842138660\",\n      //     status: CONSTANTS.SIM_STATUS.PURGED,\n      //     customerInfo: \"Nguyễn Văn A\",\n      //     contractCode: \"HNI-LD/00661242\"\n      // },{\n      //     imsi: \"84842139301\",\n      //     status: CONSTANTS.SIM_STATUS.ACTIVATED,\n      //     customerInfo: \"Tổng công ty Điện Lực Thành phố Hà Nội\",\n      //     contractCode: \"HNI-LD/01060638\"\n      // },{\n      //     imsi: \"841388100826\",\n      //     status: CONSTANTS.SIM_STATUS.ACTIVATED,\n      //     customerInfo: \"UBND Thị trấn Vân Đình\",\n      //     contractCode: \"HNI-LD/01060626\"\n      // },{\n      //     imsi: \"84842138660\",\n      //     status: CONSTANTS.SIM_STATUS.PURGED,\n      //     customerInfo: \"Nguyễn Văn A\",\n      //     contractCode: \"HNI-LD/00661242\"\n      // },{\n      //     imsi: \"84842139301\",\n      //     status: CONSTANTS.SIM_STATUS.ACTIVATED,\n      //     customerInfo: \"Tổng công ty Điện Lực Thành phố Hà Nội\",\n      //     contractCode: \"HNI-LD/01060638\"\n      // }]\n      //\n      // me.totalResultRegisterGroup = response.total;\n      // me.totalSuccessResultRegisterGroup = response.inact1 + response.inact2;\n      // me.totalFailResultRegisterGroup = response.active + response.purge;\n      // me.dataResultRegisterGroupOrigin = response.simInGroupResponses;\n      // me.pageNumberResultRegister = 0;\n      // me.pageSizeResultRegister = 10;\n      // me.pagingResultRegisterGroup(0,10,null, null)\n      // me.isShowDialogRegisterGroupSim = false;\n      // me.isShowDialogResultRegisterGroupSim = true;\n      me.excuteResponseRegisterForGroupSim(response);\n      me.messageCommonService.offload();\n    }, error => {\n      me.messageCommonService.error(me.tranService.translate(error.error.error.message));\n      me.isShowDialogRegisterGroupSim = false;\n      me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n    });\n  }\n  excuteResponseRegisterForGroupSim(resApi) {\n    let me = this;\n    me.simImportsOrigin = undefined;\n    me.isShowErrorGroup = false;\n    let response = resApi.listRatingError || [];\n    if (resApi.total == 0) {\n      me.messageCommonService.error(me.tranService.translate(resApi.message));\n      me.isShowDialogResultRegisterGroupSim = false;\n      me.isShowDialogRegisterGroupSim = false;\n      return;\n    }\n    if (resApi.message.toUpperCase() == \"ok\".toUpperCase()) {\n      me.messageCommonService.warning(me.tranService.translate(\"ratingPlan.text.textResultRegisterGroupSim\", {\n        error: resApi.total - resApi.error,\n        total: resApi.total\n      }));\n    } else {\n      me.messageCommonService.error(me.tranService.translate(resApi.message));\n    }\n    if (response.length == 0) {\n      me.isShowDialogResultRegisterGroupSim = false;\n      me.isShowDialogRegisterGroupSim = false;\n      return;\n    }\n    me.isShowDialogResultRegisterGroupSim = true;\n    //0 normal, 1 error imsi, 2 error planName\n    let index = 0;\n    me.mapFormSimImports = {};\n    me.mapPlanNameError = {};\n    me.mapImsiError = {};\n    let excludeDescription = [\"error.invalid.msisdn.not.active\", 'error.invalid.isdn.empty', \"error.invalid.isdn.not.format\", \"error.invalid.rating.empty\", \"error.invalid.rating.not.format\"];\n    response.forEach(el => {\n      if (!excludeDescription.includes(el.description)) {\n        if (parseInt(el.type) == 0) {\n          if (el.msisdn != null && el.msisdn != \"\" && /^(\\+?84)[1-9][0-9]{8,9}$/.test(el.msisdn)) {\n            if ((el.description || \"\") != \"\") {\n              if (el.description in me.mapImsiError) {\n                me.mapImsiError[el.description].push(el.msisdn);\n              } else {\n                me.mapImsiError[el.description] = [el.msisdn];\n              }\n            }\n          }\n        } else if (parseInt(el.type) == 1) {\n          if (el.ratingPlanName != null && el.ratingPlanName != \"\" && /^[a-zA-Z0-9\\-_]*$/.test(el.ratingPlanName)) {\n            if ((el.description || \"\") != \"\") {\n              if (el.description in me.mapPlanNameError) {\n                me.mapPlanNameError[el.description].push(el.planName);\n              } else {\n                me.mapPlanNameError[el.description] = [el.planName];\n              }\n            }\n          }\n        }\n      } else {\n        el.description = me.tranService.translate(el.description);\n      }\n      el['keyForm'] = `keyForm${index++}`;\n      me.mapFormSimImports[el['keyForm']] = me.formBuilder.group(el);\n      me.mapFormSimImports[el['keyForm']].controls['msisdn'].setValidators([Validators.required, Validators.pattern('^(\\\\+?84)[1-9][0-9]{8,9}$')]);\n      me.mapFormSimImports[el['keyForm']].controls['msisdn'].updateValueAndValidity();\n      me.mapFormSimImports[el['keyForm']].controls['ratingPlanName'].setValidators([Validators.required, Validators.pattern('^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$')]);\n      me.mapFormSimImports[el['keyForm']].controls['ratingPlanName'].updateValueAndValidity();\n    });\n    me.rowFirstSimImport = 0;\n    me.pageSizeSimImport = 10;\n    me.simImportsOrigin = [...response];\n    me.simImports = me.simImportsOrigin.slice(0, 10);\n  }\n  registerForFile() {\n    if (!this.checkValidListImport()) return;\n    let me = this;\n    let data = {\n      listRegisterRatingPlan: this.simImportsOrigin,\n      fileName: \"\"\n    };\n    me.messageCommonService.onload();\n    this.ratingPlanService.uploadRegisterByList(data, resApi => {\n      me.excuteResponseRegisterFileOrList(resApi);\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  pagingResultRegisterGroup(page, size, sort, params) {\n    this.dataSetResultRegisterGroup = {\n      content: this.dataResultRegisterGroupOrigin.slice(page * size, page * size + size),\n      total: this.dataResultRegisterGroupOrigin.length\n    };\n  }\n  pagingResultSimImport(event) {\n    let first = event.first;\n    let size = event.rows;\n    this.rowFirstSimImport = first;\n    this.pageSizeSimImport = size;\n    this.simImports = this.simImportsOrigin.slice(first, first + size);\n  }\n  getDetailSim() {\n    let me = this;\n    this.messageCommonService.onload();\n    me.simService.getById(me.simId, response => {\n      me.detailSim = {\n        ...response\n      };\n      me.getStatusSim();\n      me.getDetailCustomer();\n      me.getDetailRatingPlan();\n      me.getDetailContract();\n      me.getDetailApn();\n      me.simService.getConnectionStatus([me.simId], resp => {\n        me.detailSim.connectionStatus = resp[0]?.userstate;\n      }, () => {});\n    }, null, () => {\n      this.messageCommonService.offload();\n    });\n  }\n  getStatusSim() {\n    let me = this;\n    this.simService.getDetailStatus(this.detailSim.msisdn, response => {\n      me.detailStatusSim = {\n        statusData: response.gprsStatus == 1,\n        statusReceiveCall: response.icStatus == 1,\n        statusSendCall: response.ocStatus == 1,\n        statusWorldCall: response.iddStatus == 1,\n        statusReceiveSms: response.smtStatus == 1,\n        statusSendSms: response.smoStatus == 1\n      };\n    }, () => {});\n  }\n  getDetailCustomer() {\n    this.detailCustomer = {\n      name: this.detailSim.customerName,\n      code: this.detailSim.customerCode\n    };\n  }\n  getDetailRatingPlan() {\n    this.simService.getDetailPlanSim(this.detailSim.msisdn, response => {\n      this.detailRatingPlan = {\n        ...response\n      };\n    }, () => {});\n  }\n  getDetailContract() {\n    this.simService.getDetailContract(this.utilService.stringToStrBase64(this.detailSim.contractCode), response => {\n      this.detailContract = response;\n    }, () => {});\n  }\n  getDetailApn() {\n    this.detailAPN = {\n      code: this.detailSim.apnCode,\n      type: \"Kết nối bằng 3G\",\n      ip: 0,\n      rangeIp: this.detailSim.ip\n    };\n  }\n  getNameStatus(value) {\n    if (value == 0) {\n      return this.tranService.translate(\"sim.status.inventory\");\n    } else if (value == CONSTANTS.SIM_STATUS.READY) {\n      // return this.tranService.translate(\"sim.status.ready\");\n      return this.tranService.translate(\"sim.status.activated\");\n    } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\n      return this.tranService.translate(\"sim.status.activated\");\n    } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\n      return this.tranService.translate(\"sim.status.deactivated\");\n    } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\n      return this.tranService.translate(\"sim.status.purged\");\n    } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\n      return this.tranService.translate(\"sim.status.inactivated\");\n    } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\n      return this.tranService.translate(\"sim.status.processingChangePlan\");\n    } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\n      return this.tranService.translate(\"sim.status.processingRegisterPlan\");\n    } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\n      return this.tranService.translate(\"sim.status.waitingCancelPlan\");\n    }\n    return \"\";\n  }\n  getClassStatus(value) {\n    if (value == 0) {\n      return ['p-1', \"border-round\", \"border-400\", \"text-color\", \"inline-block\"];\n    } else if (value == CONSTANTS.SIM_STATUS.READY) {\n      // return ['p-1', \"bg-blue-600\", \"border-round\",\"inline-block\"];\n      return ['p-2', \"text-green-800\", \"bg-green-100\", \"border-round\", \"inline-block\"];\n    } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\n      return ['p-2', 'text-green-800', \"bg-green-100\", \"border-round\", \"inline-block\"];\n    } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\n      return ['p-2', 'text-yellow-800', \"bg-yellow-100\", \"border-round\", \"inline-block\"];\n    } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\n      return ['p-2', 'text-indigo-600', \"bg-indigo-100\", \"border-round\", \"inline-block\"];\n    } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\n      return ['p-2', 'text-red-700', \"bg-red-100\", \"border-round\", \"inline-block\"];\n    } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\n      return ['p-2', 'text-cyan-800', \"bg-cyan-100\", \"border-round\", \"inline-block\"];\n    } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\n      return ['p-2', 'text-teal-800', \"bg-teal-100\", \"border-round\", \"inline-block\"];\n    } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\n      return ['p-2', 'text-orange-700', \"bg-orange-100\", \"border-round\", \"inline-block\"];\n    }\n    return [];\n  }\n  getServiceType(value) {\n    if (value == CONSTANTS.SERVICE_TYPE.PREPAID) return this.tranService.translate(\"sim.serviceType.prepaid\");else if (value == CONSTANTS.SERVICE_TYPE.POSTPAID) return this.tranService.translate(\"sim.serviceType.postpaid\");else return \"\";\n  }\n  getDetailPLan() {\n    let me = this;\n    this.messageCommonService.onload();\n    me.ratingPlanService.getById(Number(me.planId), response => {\n      me.response = response;\n      me.ratingPlanInfo.id = response.id;\n      me.ratingPlanInfo.code = response.code;\n      me.ratingPlanInfo.name = response.name;\n      me.ratingPlanInfo.status = response.status;\n      me.ratingPlanInfo.dispatchCode = response.dispatchCode;\n      me.ratingPlanInfo.customerType = response.customerType;\n      me.ratingPlanInfo.subscriptionFee = response.subscriptionFee;\n      me.ratingPlanInfo.subscriptionType = response.paidType;\n      me.ratingPlanInfo.ratingScope = response.ratingScope;\n      me.ratingPlanInfo.cycleTimeUnit = response.cycleTimeUnit;\n      me.ratingPlanInfo.cycleInterval = response.cycleInterval;\n      me.ratingPlanInfo.reload = response.reload;\n      me.ratingPlanInfo.flat = response.flat;\n      me.ratingPlanInfo.limitDataUsage = response.limitDataUsage;\n      me.ratingPlanInfo.limitSmsOutside = response.limitSmsOutside;\n      me.ratingPlanInfo.limitSmsInside = response.limitSmsInside;\n      me.ratingPlanInfo.flexible = response.flexible;\n      me.ratingPlanInfo.feePerDataUnit = response.feePerDataUnit;\n      me.ratingPlanInfo.squeezedSpeed = response.squeezedSpeed;\n      me.ratingPlanInfo.feeSmsInside = response.feeSmsInside;\n      me.ratingPlanInfo.feeSmsOutside = response.feeSmsOutside;\n      me.ratingPlanInfo.maximumFee = response.maximumFee;\n      me.ratingPlanInfo.dataRoundUnit = response.dataRoundUnit;\n      me.ratingPlanInfo.downSpeed = response.downSpeed;\n      me.ratingPlanInfo.provinceCode = response.provinceCode;\n      me.ratingPlanInfo.description = response.description;\n      me.getReload(me.ratingPlanInfo.reload);\n      me.getFlexible(me.ratingPlanInfo.flexible);\n      me.myProvices = \"\";\n      me.accountService.getListProvince(data => {\n        me.provinces = data.map(el => {\n          return {\n            code: el.code,\n            name: `${el.name}`\n          };\n        });\n        me.provinces.forEach(el => {\n          if (me.ratingPlanInfo.provinceCode.includes(el.code)) {\n            me.myProvices += `${el.name}, `;\n          }\n        });\n        if (me.myProvices.length > 0) {\n          me.myProvices = me.myProvices.substring(0, me.myProvices.length - 2);\n        }\n      });\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  getReload(value) {\n    if (value == CONSTANTS.RELOAD.YES) {\n      return this.checkedReload = true;\n    } else if (value == CONSTANTS.RELOAD.NO) {\n      return this.checkedReload = false;\n    }\n    return \"\";\n  }\n  getFlexible(value) {\n    if (value == CONSTANTS.FLEXIBLE.YES) {\n      return this.checkedFlexible = true;\n    } else if (value == CONSTANTS.FLEXIBLE.NO) {\n      return this.checkedFlexible = false;\n    }\n    return \"\";\n  }\n  getNameCustomerType(value) {\n    if (value == CONSTANTS.CUSTOMER_TYPE.PERSONAL) {\n      return this.tranService.translate(\"ratingPlan.customerType.personal\");\n    } else if (value == CONSTANTS.CUSTOMER_TYPE.INTERPRISE) {\n      return this.tranService.translate(\"ratingPlan.customerType.enterprise\");\n    } else if (value == CONSTANTS.CUSTOMER_TYPE.AGENCY) {\n      return this.tranService.translate(\"ratingPlan.customerType.agency\");\n    }\n    return \"\";\n  }\n  checkSubscriptionType() {\n    this.subscriptionTypes = [{\n      type: this.tranService.translate(\"ratingPlan.subscriptionType.post\"),\n      ip: CONSTANTS.SUBSCRIPTION_TYPE.POSTPAID\n    }, {\n      type: this.tranService.translate(\"ratingPlan.subscriptionType.pre\"),\n      ip: CONSTANTS.SUBSCRIPTION_TYPE.PREPAID\n    }];\n  }\n  getCycleTimeUnit(value) {\n    if (value == CONSTANTS.CYCLE_TIME_UNITS.DAY) {\n      return this.tranService.translate(\"ratingPlan.cycle.day\");\n    } else if (value == CONSTANTS.CYCLE_TIME_UNITS.MONTH) {\n      return this.tranService.translate(\"ratingPlan.cycle.month\");\n    }\n    return \"\";\n  }\n  getRatingScope(value) {\n    if (value == CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE) {\n      return this.tranService.translate(\"ratingPlan.ratingScope.nativeWide\");\n    } else if (value == CONSTANTS.RATING_PLAN_SCOPE.CUSTOMER) {\n      return this.tranService.translate(\"ratingPlan.ratingScope.customer\");\n    }\n    return \"\";\n  }\n  static {\n    this.ɵfac = function AppRegisterPlanListComponent_Factory(t) {\n      return new (t || AppRegisterPlanListComponent)(i0.ɵɵdirectiveInject(i1.RatingPlanService), i0.ɵɵdirectiveInject(i2.CustomerService), i0.ɵɵdirectiveInject(i3.GroupSimService), i0.ɵɵdirectiveInject(i4.SimService), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i6.AccountService), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppRegisterPlanListComponent,\n      selectors: [[\"app-register-plan-list\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 100,\n      vars: 132,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\", \"responsive-container\"], [\"styleClass\", \"mr-2 p-button-success equal-button-with-margin\", 3, \"label\", \"model\", 4, \"ngIf\"], [\"styleClass\", \"p-button-info equal-button-with-margin\", \"icon\", \"\", \"routerLinkActive\", \"router-link-active\", 3, \"label\", \"routerLink\", 4, \"ngIf\"], [1, \"pb-2\", \"pt-3\", \"vnpt-field-set\", 3, \"formGroup\", \"ngSubmit\"], [3, \"toggleable\", \"header\"], [1, \"grid\", \"search-grid-3\"], [1, \"col-3\"], [1, \"p-float-label\"], [\"pInputText\", \"\", \"pInputText\", \"\", \"id\", \"msisdn\", \"formControlName\", \"msisdn\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"msisdn\"], [\"pInputText\", \"\", \"pInputText\", \"\", \"id\", \"imei\", \"formControlName\", \"imei\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"imei\"], [1, \"relative\"], [\"objectKey\", \"dropdownListSim\", \"paramKey\", \"name\", \"keyReturn\", \"id\", \"displayPattern\", \"${name} - ${code}\", \"typeValue\", \"primitive\", 1, \"w-full\", 3, \"value\", \"placeholder\", \"paramDefault\", \"isMultiChoice\", \"floatLabel\", \"valueChange\"], [\"styleClass\", \"w-full\", \"id\", \"status\", \"formControlName\", \"status\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"ngModelChange\"], [\"for\", \"status\", 1, \"label-dropdown\"], [\"objectKey\", \"dropdownListSim\", \"paramKey\", \"name\", \"keyReturn\", \"customerCode\", \"displayPattern\", \"${customerName} - ${customerCode}\", \"typeValue\", \"primitive\", 1, \"w-full\", 3, \"value\", \"placeholder\", \"paramDefault\", \"isMultiChoice\", \"floatLabel\", \"valueChange\"], [\"objectKey\", \"dropdownListSim\", \"paramKey\", \"name\", \"keyReturn\", \"contractCode\", \"displayPattern\", \"${contractCode}\", \"typeValue\", \"primitive\", 1, \"w-full\", 3, \"value\", \"placeholder\", \"paramDefault\", \"floatLabel\", \"isMultiChoice\", \"valueChange\"], [1, \"col-3\", \"pb-0\", \"date-filter\"], [\"styleClass\", \"w-full\", \"id\", \"dateFrom\", \"formControlName\", \"dateFrom\", \"dateFormat\", \"dd/mm/yy\", 3, \"ngModel\", \"showIcon\", \"showClear\", \"maxDate\", \"ngModelChange\", \"onSelect\", \"onInput\"], [\"htmlFor\", \"dateFrom\", 1, \"label-calendar\"], [1, \"col-3\", \"pb-0\", \"date-filter\", 2, \"width\", \"calc(25% - 70px)\"], [\"styleClass\", \"w-full\", \"id\", \"dateTo\", \"formControlName\", \"dateTo\", \"dateFormat\", \"dd/mm/yy\", 3, \"ngModel\", \"showIcon\", \"showClear\", \"minDate\", \"maxDate\", \"ngModelChange\", \"onSelect\", \"onInput\"], [\"htmlFor\", \"dateTo\", 1, \"label-calendar\"], [1, \"col\", \"pb-0\", 2, \"width\", \"5% !important\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"submit\"], [1, \"flex\", \"justify-content-center\", \"dialog-vnpt\"], [3, \"header\", \"visible\", \"modal\", \"style\", \"draggable\", \"resizable\", \"visibleChange\", 4, \"ngIf\"], [3, \"fieldId\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"pageNumber\", \"pageSize\", \"sort\", \"params\", \"labelTable\", \"selectItemsChange\"], [1, \"flex\", \"justify-content-center\", \"dialog-push-group\"], [3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"w-full\", \"field\", \"grid\"], [\"htmlFor\", \"planSelected\", 1, \"col-fixed\", 2, \"width\", \"100px\"], [1, \"col\"], [\"styleClass\", \"w-full\", \"optionLabel\", \"display\", \"filterBy\", \"name\", \"optionValue\", \"id\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"filter\", \"placeholder\", \"ngModelChange\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"align-items-center\"], [\"styleClass\", \"mr-2 p-button-secondary p-button-outlined\", 3, \"label\", \"click\"], [\"styleClass\", \"p-button-info\", 3, \"label\", \"disabled\", \"click\"], [\"styleClass\", \"dialog-upload-device\", 3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"w-full\", \"field\", \"grid\", \"dialog-grid\"], [\"htmlFor\", \"groupSim\", 1, \"col-fixed\", 2, \"width\", \"100px\"], [1, \"col\", \"input-div\", 2, \"max-width\", \"calc(100% - 100px)\"], [\"objectKey\", \"groupSim\", \"paramKey\", \"name\", \"keyReturn\", \"id\", \"displayPattern\", \"${name} - ${groupKey}\", \"typeValue\", \"primitive\", 1, \"w-full\", 3, \"value\", \"placeholder\", \"isMultiChoice\", \"valueChange\"], [1, \"grid\"], [1, \"col\", \"pt-0\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [\"dataKey\", \"id\", 3, \"paginator\", \"rows\", \"first\", \"showCurrentPageReport\", \"tableStyle\", \"currentPageReportTemplate\", \"rowsPerPageOptions\", \"styleClass\", \"totalRecords\", \"lazy\", \"scrollHeight\", \"value\", \"onPage\", 4, \"ngIf\"], [\"styleClass\", \"p-button-info\", 3, \"disabled\", \"label\", \"click\", 4, \"ngIf\"], [\"styleClass\", \"dialog-file\", 3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"col-10\", \"flex\", \"flex-row\", \"justify-content-start\", \"align-items-center\"], [1, \"w-full\", \"upload-device-file\", 3, \"fileObject\", \"clearFileCallback\", \"options\", \"fileObjectChange\"], [1, \"col-2\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [\"icon\", \"pi pi-download\", \"styleClass\", \"p-button-outlined p-button-secondary\", 3, \"pTooltip\", \"click\"], [\"styleClass\", \"mr-2 p-button-success equal-button-with-margin\", 3, \"label\", \"model\"], [\"styleClass\", \"p-button-info equal-button-with-margin\", \"icon\", \"\", \"routerLinkActive\", \"router-link-active\", 3, \"label\", \"routerLink\"], [1, \"grid\", \"grid-1\", \"mt-1\", \"h-auto\", 2, \"width\", \"calc(100% + 16px)\"], [1, \"col\", \"sim-detail\", \"pr-0\"], [3, \"header\"], [1, \"flex\", \"flex-row\", \"justify-content-between\", \"custom-card\"], [1, \"w-6\"], [1, \"inline-block\", \"col-fixed\", 2, \"min-width\", \"150px\", \"max-width\", \"200px\"], [1, \"mt-1\", \"grid\"], [1, \"w-auto\", \"ml-3\"], [\"class\", \"ml-3 p-2 text-green-800 bg-green-100 border-round inline-block\", 4, \"ngIf\"], [\"class\", \"ml-3 p-2 text-50 surface-500 border-round inline-block\", 4, \"ngIf\"], [\"styleClass\", \"mt-3 sim-status\", 3, \"header\"], [1, \"col-4\", \"text-center\"], [\"onLabel\", \"ON\", \"offLabel\", \"OFF\", 3, \"ngModel\", \"disabled\", \"ngModelChange\"], [\"styleClass\", \"mt-3\", 3, \"header\"], [1, \"inline-block\", \"col-fixed\", 2, \"min-width\", \"200px\", \"max-width\", \"200px\"], [1, \"grid\", \"mt-0\"], [1, \"col\", \"uppercase\"], [1, \"ml-3\", \"p-2\", \"text-green-800\", \"bg-green-100\", \"border-round\", \"inline-block\"], [1, \"ml-3\", \"p-2\", \"text-50\", \"surface-500\", \"border-round\", \"inline-block\"], [\"styleClass\", \"h-full\"], [1, \"col\", \"ratingPlan-detail\", \"custom-rating-detail\", \"pr-0\", \"flex\", 2, \"border\", \"1px solid black\", \"margin-bottom\", \"20px\"], [1, \"flex-1\"], [1, \"inline-block\", \"col-fixed\", 2, \"min-width\", \"200px\", \"max-width\", \"200px\", \"padding-bottom\", \"5px\"], [1, \"inline-block\", \"col-fixed\"], [1, \"text-white\", \"w-auto\", \"col\"], [\"inputId\", \"typeIp1\", 3, \"disabled\", \"value\", \"ngModel\", \"ngModelChange\"], [1, \"inline-block\", \"col-fixed\", \"radioButton2\", 2, \"padding-left\", \"108px\"], [\"inputId\", \"typeIp2\", 3, \"disabled\", \"value\", \"ngModel\", \"ngModelChange\"], [\"class\", \"grid\", 4, \"ngIf\"], [2, \"padding-top\", \"10px\", \"padding-left\", \"13px\"], [3, \"ngModel\", \"disabled\", \"ngModelChange\"], [1, \"inline-block\", \"col-fixed\", 2, \"font-size\", \"20px\", \"margin-left\", \"10px\"], [\"id\", \"name\", 1, \"col\", \"ratingPlan-detail\", \"custom-rating-detail-limit\", \"pr-0\", \"flex\", 2, \"border\", \"1px solid black\", \"margin-bottom\", \"20px\"], [2, \"padding-top\", \"18px\"], [\"id\", \"flexible\", 1, \"col\", \"ratingPlan-detail\", \"pr-0\", \"flex\", 2, \"border\", \"1px solid black\"], [1, \"inline-block\", \"col-fixed\", 2, \"width\", \"fit-content !important\"], [1, \"text-red-500\"], [\"dataKey\", \"id\", 3, \"paginator\", \"rows\", \"first\", \"showCurrentPageReport\", \"tableStyle\", \"currentPageReportTemplate\", \"rowsPerPageOptions\", \"styleClass\", \"totalRecords\", \"lazy\", \"scrollHeight\", \"value\", \"onPage\"], [\"dataTable\", \"\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [2, \"min-width\", \"150px\", \"max-width\", \"150px\"], [2, \"min-width\", \"250px\", \"max-width\", \"250px\"], [2, \"min-width\", \"70px\", \"max-width\", \"70px\", \"text-align\", \"center\"], [3, \"formGroup\"], [\"pEditableColumnField\", \"msisdn\", 2, \"min-width\", \"150px\", \"max-width\", \"150px\", 3, \"pEditableColumn\"], [\"pTemplate\", \"input\"], [\"pTemplate\", \"output\"], [\"pEditableColumnField\", \"ratingPlanName\", 2, \"min-width\", \"150px\", \"max-width\", \"150px\", 3, \"pEditableColumn\"], [2, \"min-width\", \"200px\", \"max-width\", \"200px\"], [4, \"ngIf\"], [2, \"min-width\", \"100px\", \"max-width\", \"100px\", \"text-align\", \"center\"], [1, \"pi\", \"pi-trash\", 3, \"pTooltip\", \"click\"], [\"formControlName\", \"msisdn\", \"pInputText\", \"\", \"type\", \"text\", \"required\", \"\", \"maxlength\", \"20\", \"pattern\", \"^(\\\\+?84)[1-9][0-9]{8,9}$\", 2, \"min-width\", \"150px\", \"max-width\", \"150px\", 3, \"ngModel\", \"ngModelChange\"], [\"formControlName\", \"ratingPlanName\", \"pInputText\", \"\", \"type\", \"text\", \"required\", \"\", \"maxlength\", \"50\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$\", 2, \"min-width\", \"150px\", \"max-width\", \"150px\", 3, \"ngModel\", \"ngModelChange\"], [\"styleClass\", \"p-button-info\", 3, \"disabled\", \"label\", \"click\"]],\n      template: function AppRegisterPlanListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵtemplate(6, AppRegisterPlanListComponent_p_splitButton_6_Template, 1, 2, \"p-splitButton\", 5);\n          i0.ɵɵtemplate(7, AppRegisterPlanListComponent_p_button_7_Template, 1, 3, \"p-button\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"form\", 7);\n          i0.ɵɵlistener(\"ngSubmit\", function AppRegisterPlanListComponent_Template_form_ngSubmit_8_listener() {\n            return ctx.onSubmitSearch();\n          });\n          i0.ɵɵelementStart(9, \"p-panel\", 8)(10, \"div\", 9)(11, \"div\", 10)(12, \"span\", 11)(13, \"input\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_Template_input_ngModelChange_13_listener($event) {\n            return ctx.searchInfo.msisdn = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"label\", 13);\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 10)(17, \"span\", 11)(18, \"input\", 14);\n          i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_Template_input_ngModelChange_18_listener($event) {\n            return ctx.searchInfo.imei = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"label\", 15);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 10)(22, \"div\", 16)(23, \"vnpt-select\", 17);\n          i0.ɵɵlistener(\"valueChange\", function AppRegisterPlanListComponent_Template_vnpt_select_valueChange_23_listener($event) {\n            return ctx.searchInfo.ratingPlanId = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"div\", 10)(25, \"span\", 11)(26, \"p-dropdown\", 18);\n          i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_Template_p_dropdown_ngModelChange_26_listener($event) {\n            return ctx.searchInfo.status = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"label\", 19);\n          i0.ɵɵtext(28);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 10)(30, \"div\", 16)(31, \"vnpt-select\", 20);\n          i0.ɵɵlistener(\"valueChange\", function AppRegisterPlanListComponent_Template_vnpt_select_valueChange_31_listener($event) {\n            return ctx.searchInfo.customer = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"div\", 10)(33, \"div\", 16)(34, \"vnpt-select\", 21);\n          i0.ɵɵlistener(\"valueChange\", function AppRegisterPlanListComponent_Template_vnpt_select_valueChange_34_listener($event) {\n            return ctx.searchInfo.contractCode = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 22)(36, \"span\", 11)(37, \"p-calendar\", 23);\n          i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_Template_p_calendar_ngModelChange_37_listener($event) {\n            return ctx.searchInfo.dateFrom = $event;\n          })(\"onSelect\", function AppRegisterPlanListComponent_Template_p_calendar_onSelect_37_listener() {\n            return ctx.onChangeDateFrom(ctx.searchInfo.dateFrom);\n          })(\"onInput\", function AppRegisterPlanListComponent_Template_p_calendar_onInput_37_listener() {\n            return ctx.onChangeDateFrom(ctx.searchInfo.dateFrom);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"label\", 24);\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(40, \"div\", 25)(41, \"span\", 11)(42, \"p-calendar\", 26);\n          i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_Template_p_calendar_ngModelChange_42_listener($event) {\n            return ctx.searchInfo.dateTo = $event;\n          })(\"onSelect\", function AppRegisterPlanListComponent_Template_p_calendar_onSelect_42_listener() {\n            return ctx.onChangeDateTo(ctx.searchInfo.dateTo);\n          })(\"onInput\", function AppRegisterPlanListComponent_Template_p_calendar_onInput_42_listener() {\n            return ctx.onChangeDateTo(ctx.searchInfo.dateTo);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"label\", 27);\n          i0.ɵɵtext(44);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"div\", 28);\n          i0.ɵɵelement(46, \"p-button\", 29);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(47, \"div\", 30);\n          i0.ɵɵtemplate(48, AppRegisterPlanListComponent_p_dialog_48_Template, 147, 86, \"p-dialog\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 30);\n          i0.ɵɵtemplate(50, AppRegisterPlanListComponent_p_dialog_50_Template, 135, 69, \"p-dialog\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"table-vnpt\", 32);\n          i0.ɵɵlistener(\"selectItemsChange\", function AppRegisterPlanListComponent_Template_table_vnpt_selectItemsChange_51_listener($event) {\n            return ctx.selectItems = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"div\", 33)(53, \"p-dialog\", 34);\n          i0.ɵɵlistener(\"visibleChange\", function AppRegisterPlanListComponent_Template_p_dialog_visibleChange_53_listener($event) {\n            return ctx.isShowDialogRegisterSim = $event;\n          });\n          i0.ɵɵelementStart(54, \"div\", 35)(55, \"label\", 36);\n          i0.ɵɵtext(56);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 37)(58, \"p-dropdown\", 38);\n          i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_Template_p_dropdown_ngModelChange_58_listener($event) {\n            return ctx.planSelected = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(59, \"div\", 39)(60, \"p-button\", 40);\n          i0.ɵɵlistener(\"click\", function AppRegisterPlanListComponent_Template_p_button_click_60_listener() {\n            return ctx.isShowDialogRegisterSim = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"p-button\", 41);\n          i0.ɵɵlistener(\"click\", function AppRegisterPlanListComponent_Template_p_button_click_61_listener() {\n            return ctx.registerForSim();\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(62, \"div\", 33)(63, \"p-dialog\", 42);\n          i0.ɵɵlistener(\"visibleChange\", function AppRegisterPlanListComponent_Template_p_dialog_visibleChange_63_listener($event) {\n            return ctx.isShowDialogRegisterGroupSim = $event;\n          });\n          i0.ɵɵelementStart(64, \"div\", 43)(65, \"label\", 44);\n          i0.ɵɵtext(66);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"div\", 45)(68, \"vnpt-select\", 46);\n          i0.ɵɵlistener(\"valueChange\", function AppRegisterPlanListComponent_Template_vnpt_select_valueChange_68_listener($event) {\n            return ctx.groupSimSelected = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(69, \"div\", 43)(70, \"label\", 36);\n          i0.ɵɵtext(71);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"div\", 45)(73, \"p-dropdown\", 38);\n          i0.ɵɵlistener(\"ngModelChange\", function AppRegisterPlanListComponent_Template_p_dropdown_ngModelChange_73_listener($event) {\n            return ctx.planGroupSelected = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(74, \"div\", 39)(75, \"p-button\", 40);\n          i0.ɵɵlistener(\"click\", function AppRegisterPlanListComponent_Template_p_button_click_75_listener() {\n            return ctx.isShowDialogRegisterGroupSim = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"p-button\", 41);\n          i0.ɵɵlistener(\"click\", function AppRegisterPlanListComponent_Template_p_button_click_76_listener() {\n            return ctx.registerForGroupSim();\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(77, \"div\", 33)(78, \"p-dialog\", 34);\n          i0.ɵɵlistener(\"visibleChange\", function AppRegisterPlanListComponent_Template_p_dialog_visibleChange_78_listener($event) {\n            return ctx.isShowDialogResultRegisterGroupSim = $event;\n          });\n          i0.ɵɵelementStart(79, \"div\", 47)(80, \"div\", 48);\n          i0.ɵɵtemplate(81, AppRegisterPlanListComponent_small_81_Template, 2, 1, \"small\", 49);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(82, AppRegisterPlanListComponent_p_table_82_Template, 4, 16, \"p-table\", 50);\n          i0.ɵɵelementStart(83, \"div\", 39)(84, \"p-button\", 40);\n          i0.ɵɵlistener(\"click\", function AppRegisterPlanListComponent_Template_p_button_click_84_listener() {\n            return ctx.isShowDialogResultRegisterGroupSim = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(85, AppRegisterPlanListComponent_p_button_85_Template, 1, 2, \"p-button\", 51);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(86, \"div\", 33)(87, \"p-dialog\", 52);\n          i0.ɵɵlistener(\"visibleChange\", function AppRegisterPlanListComponent_Template_p_dialog_visibleChange_87_listener($event) {\n            return ctx.isShowDialogResultRegisterFile = $event;\n          });\n          i0.ɵɵelementStart(88, \"div\", 35)(89, \"div\", 53)(90, \"input-file-vnpt\", 54);\n          i0.ɵɵlistener(\"fileObjectChange\", function AppRegisterPlanListComponent_Template_input_file_vnpt_fileObjectChange_90_listener($event) {\n            return ctx.fileObject = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(91, \"div\", 55)(92, \"p-button\", 56);\n          i0.ɵɵlistener(\"click\", function AppRegisterPlanListComponent_Template_p_button_click_92_listener() {\n            return ctx.downloadTemplate();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(93, \"div\", 47)(94, \"div\", 48);\n          i0.ɵɵtemplate(95, AppRegisterPlanListComponent_small_95_Template, 2, 1, \"small\", 49);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(96, AppRegisterPlanListComponent_p_table_96_Template, 4, 16, \"p-table\", 50);\n          i0.ɵɵelementStart(97, \"div\", 39)(98, \"p-button\", 40);\n          i0.ɵɵlistener(\"click\", function AppRegisterPlanListComponent_Template_p_button_click_98_listener() {\n            return ctx.isShowDialogResultRegisterFile = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(99, AppRegisterPlanListComponent_p_button_99_Template, 1, 2, \"p-button\", 51);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.menu.registerplan\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkAuthen(i0.ɵɵpureFunction2(120, _c7, ctx.allPermissions.RATING_PLAN_SIM.REGISTER_BY_FILE, ctx.allPermissions.RATING_PLAN_SIM.REGISTER_BY_LIST)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkAuthen(i0.ɵɵpureFunction1(123, _c8, ctx.CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_HISTORY)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formSearch);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"toggleable\", true)(\"header\", ctx.tranService.translate(\"global.text.filter\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.msisdn);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.sothuebao\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.imei);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.imeiDevice\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"value\", ctx.searchInfo.ratingPlanId)(\"placeholder\", ctx.tranService.translate(\"sim.label.goicuoc\"))(\"paramDefault\", i0.ɵɵpureFunction0(125, _c9))(\"isMultiChoice\", false)(\"floatLabel\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.searchInfo.status)(\"options\", ctx.listStatus);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.status\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"value\", ctx.searchInfo.customer)(\"placeholder\", ctx.tranService.translate(\"sim.label.khachhang\"))(\"paramDefault\", i0.ɵɵpureFunction0(126, _c10))(\"isMultiChoice\", false)(\"floatLabel\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"value\", ctx.searchInfo.contractCode)(\"placeholder\", ctx.tranService.translate(\"sim.label.mahopdong\"))(\"paramDefault\", i0.ɵɵpureFunction0(127, _c11))(\"floatLabel\", true)(\"isMultiChoice\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.dateFrom)(\"showIcon\", true)(\"showClear\", true)(\"maxDate\", ctx.maxDateFrom);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.ngaylamhopdongtu\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.dateTo)(\"showIcon\", true)(\"showClear\", true)(\"minDate\", ctx.minDateTo)(\"maxDate\", ctx.maxDateTo);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.ngaylamhopdongden\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowModalDetailSim);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowModalDetailPlan);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"fieldId\", \"id\")(\"selectItems\", ctx.selectItems)(\"columns\", ctx.columns)(\"dataSet\", ctx.dataSet)(\"options\", ctx.optionTable)(\"loadData\", ctx.search.bind(ctx))(\"pageNumber\", ctx.pageNumber)(\"pageSize\", ctx.pageSize)(\"sort\", ctx.sort)(\"params\", ctx.searchInfo)(\"labelTable\", ctx.tranService.translate(\"global.menu.registerplan\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(128, _c12));\n          i0.ɵɵproperty(\"header\", ctx.headerDialogRegisterForSim)(\"visible\", ctx.isShowDialogRegisterSim)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.goicuoc\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.planSelected)(\"options\", ctx.listRatingPlanOrigin)(\"filter\", true)(\"placeholder\", ctx.tranService.translate(\"sim.text.selectRatingPlan\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.cancel\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.save\"))(\"disabled\", ctx.planSelected == null || ctx.planSelected == undefined);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(129, _c12));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"global.button.registerPlanForGroup\"))(\"visible\", ctx.isShowDialogRegisterGroupSim)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.nhomsim\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.groupSimSelected)(\"placeholder\", ctx.tranService.translate(\"sim.text.selectGroupSim\"))(\"isMultiChoice\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"sim.label.goicuoc\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.planGroupSelected)(\"options\", ctx.listRatingPlan)(\"filter\", true)(\"placeholder\", ctx.tranService.translate(\"sim.text.selectRatingPlan\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.cancel\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.save\"))(\"disabled\", ctx.planGroupSelected == null || ctx.planGroupSelected == undefined || ctx.groupSimSelected == null || ctx.groupSimSelected == undefined);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(130, _c13));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"global.button.registerPlanForGroup\"))(\"visible\", ctx.isShowDialogResultRegisterGroupSim)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowErrorGroup);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.simImportsOrigin);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.cancel\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.simImports);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(131, _c13));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"global.button.registerPlanByFile\"))(\"visible\", ctx.isShowDialogResultRegisterFile)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"fileObject\", ctx.fileObject)(\"clearFileCallback\", ctx.clearFileCallback.bind(ctx))(\"options\", ctx.optionInputFile);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"pTooltip\", ctx.tranService.translate(\"global.button.downloadTemp\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowErrorUpload);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.simImportsOrigin);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.cancel\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.simImports);\n        }\n      },\n      dependencies: [i7.NgIf, i8.RouterLink, i8.RouterLinkActive, i9.Breadcrumb, i10.Tooltip, i11.PrimeTemplate, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.MaxLengthValidator, i5.PatternValidator, i5.NgModel, i5.FormGroupDirective, i5.FormControlName, i12.InputText, i13.Button, i14.TableVnptComponent, i15.InputFileVnptComponent, i16.VnptCombobox, i17.SplitButton, i18.Calendar, i19.Dropdown, i20.Card, i21.Dialog, i22.InputSwitch, i23.RadioButton, i24.Table, i24.EditableColumn, i24.CellEditor, i25.ToggleButton, i26.Panel, i7.DecimalPipe, i7.DatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "CONSTANTS", "ComponentBase", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "tranService", "translate", "itemRegisters", "ctx_r1", "ɵɵpureFunction0", "_c0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "AppRegisterPlanListComponent_p_dialog_48_Template_p_dialog_visibleChange_0_listener", "$event", "ɵɵrestoreView", "_r14", "ctx_r13", "ɵɵnextContext", "ɵɵresetView", "isShowModalDetailSim", "ɵɵtemplate", "AppRegisterPlanListComponent_p_dialog_48_span_34_Template", "AppRegisterPlanListComponent_p_dialog_48_span_35_Template", "AppRegisterPlanListComponent_p_dialog_48_span_36_Template", "AppRegisterPlanListComponent_p_dialog_48_Template_p_toggleButton_ngModelChange_52_listener", "ctx_r15", "detailStatusSim", "statusData", "AppRegisterPlanListComponent_p_dialog_48_Template_p_toggleButton_ngModelChange_56_listener", "ctx_r16", "statusReceiveCall", "AppRegisterPlanListComponent_p_dialog_48_Template_p_toggleButton_ngModelChange_60_listener", "ctx_r17", "statusSendCall", "AppRegisterPlanListComponent_p_dialog_48_Template_p_toggleButton_ngModelChange_65_listener", "ctx_r18", "statusWorldCall", "AppRegisterPlanListComponent_p_dialog_48_Template_p_toggleButton_ngModelChange_69_listener", "ctx_r19", "statusReceiveSms", "AppRegisterPlanListComponent_p_dialog_48_Template_p_toggleButton_ngModelChange_73_listener", "ctx_r20", "statusSendSms", "ɵɵstyleMap", "_c1", "ctx_r2", "ɵɵadvance", "ɵɵtextInterpolate", "detailSim", "msisdn", "ɵɵclassMap", "getClassStatus", "status", "getNameStatus", "imsi", "imei", "apnId", "connectionStatus", "undefined", "ɵɵpipeBind2", "startDate", "getServiceType", "serviceType", "ratingPlanName", "ɵɵtextInterpolate2", "ɵɵpipeBind1", "utilService", "bytesToMegabytes", "detailRatingPlan", "dataUseInMonth", "unit", "detailContract", "contractCode", "contractDate", "contractorInfo", "centerCode", "contactPhone", "contactAddress", "paymentName", "paymentAddress", "routeCode", "detailCustomer", "name", "code", "ctx_r21", "myProvices", "AppRegisterPlanListComponent_p_dialog_50_Template_p_dialog_visibleChange_0_listener", "_r23", "ctx_r22", "isShowModalDetailPlan", "AppRegisterPlanListComponent_p_dialog_50_Template_p_radioButton_ngModelChange_44_listener", "ctx_r24", "ratingPlanInfo", "subscriptionType", "AppRegisterPlanListComponent_p_dialog_50_Template_p_radioButton_ngModelChange_50_listener", "ctx_r25", "AppRegisterPlanListComponent_p_dialog_50_div_59_Template", "AppRegisterPlanListComponent_p_dialog_50_Template_p_inputSwitch_ngModelChange_74_listener", "ctx_r26", "checkedReload", "AppRegisterPlanListComponent_p_dialog_50_Template_p_inputSwitch_ngModelChange_100_listener", "ctx_r27", "checkedFlexible", "ctx_r3", "_c2", "dispatchCode", "getNameCustomerType", "customerType", "description", "ɵɵtextInterpolate3", "subscriptionFee", "subscriptionTypes", "ip", "getRatingScope", "ratingScope", "planScopes", "CUSTOMER", "getCycleTimeUnit", "cycleTimeUnit", "cycleInterval", "limitDataUsage", "limitSmsOutside", "limitSmsInside", "feePerDataUnit", "downSpeed", "feeSmsInside", "feeSmsOutside", "maximumFee", "ɵɵtextInterpolate1", "dataRoundUnit", "squeezedSpeed", "ctx_r4", "messageErrorUpload", "ctx_r29", "AppRegisterPlanListComponent_p_table_82_ng_template_3_ng_template_3_Template_input_ngModelChange_0_listener", "_r46", "simImport_r31", "$implicit", "ctx_r47", "checkValueSimImportChange", "AppRegisterPlanListComponent_p_table_82_ng_template_3_ng_template_7_Template_input_ngModelChange_0_listener", "_r53", "ctx_r54", "ctx_r38", "ctx_r39", "ɵɵpureFunction1", "_c3", "ctx_r40", "ctx_r41", "ctx_r42", "ctx_r43", "AppRegisterPlanListComponent_p_table_82_ng_template_3_ng_template_3_Template", "AppRegisterPlanListComponent_p_table_82_ng_template_3_ng_template_4_Template", "AppRegisterPlanListComponent_p_table_82_ng_template_3_ng_template_7_Template", "AppRegisterPlanListComponent_p_table_82_ng_template_3_ng_template_8_Template", "AppRegisterPlanListComponent_p_table_82_ng_template_3_span_10_Template", "AppRegisterPlanListComponent_p_table_82_ng_template_3_span_11_Template", "AppRegisterPlanListComponent_p_table_82_ng_template_3_span_12_Template", "AppRegisterPlanListComponent_p_table_82_ng_template_3_span_13_Template", "AppRegisterPlanListComponent_p_table_82_ng_template_3_span_14_Template", "AppRegisterPlanListComponent_p_table_82_ng_template_3_span_15_Template", "AppRegisterPlanListComponent_p_table_82_ng_template_3_Template_span_click_17_listener", "restoredCtx", "_r60", "i_r33", "rowIndex", "ctx_r59", "removeItemSimImport", "ctx_r30", "mapFormSimImports", "keyForm", "invalid", "controls", "<PERSON><PERSON><PERSON><PERSON>", "errors", "pattern", "AppRegisterPlanListComponent_p_table_82_Template_p_table_onPage_0_listener", "_r62", "ctx_r61", "pagingResultSimImport", "AppRegisterPlanListComponent_p_table_82_ng_template_2_Template", "AppRegisterPlanListComponent_p_table_82_ng_template_3_Template", "ctx_r5", "pageSizeSimImport", "rowFirstSimImport", "_c4", "_c5", "simImportsOrigin", "length", "simImports", "_c6", "AppRegisterPlanListComponent_p_button_85_Template_p_button_click_0_listener", "_r64", "ctx_r63", "registerForFile", "ctx_r6", "checkValidListImport", "ctx_r7", "ctx_r66", "AppRegisterPlanListComponent_p_table_96_ng_template_3_ng_template_3_Template_input_ngModelChange_0_listener", "_r83", "simImport_r68", "ctx_r84", "AppRegisterPlanListComponent_p_table_96_ng_template_3_ng_template_7_Template_input_ngModelChange_0_listener", "_r90", "ctx_r91", "ctx_r75", "ctx_r76", "ctx_r77", "ctx_r78", "ctx_r79", "ctx_r80", "AppRegisterPlanListComponent_p_table_96_ng_template_3_ng_template_3_Template", "AppRegisterPlanListComponent_p_table_96_ng_template_3_ng_template_4_Template", "AppRegisterPlanListComponent_p_table_96_ng_template_3_ng_template_7_Template", "AppRegisterPlanListComponent_p_table_96_ng_template_3_ng_template_8_Template", "AppRegisterPlanListComponent_p_table_96_ng_template_3_span_10_Template", "AppRegisterPlanListComponent_p_table_96_ng_template_3_span_11_Template", "AppRegisterPlanListComponent_p_table_96_ng_template_3_span_12_Template", "AppRegisterPlanListComponent_p_table_96_ng_template_3_span_13_Template", "AppRegisterPlanListComponent_p_table_96_ng_template_3_span_14_Template", "AppRegisterPlanListComponent_p_table_96_ng_template_3_span_15_Template", "AppRegisterPlanListComponent_p_table_96_ng_template_3_Template_span_click_17_listener", "_r97", "i_r70", "ctx_r96", "ctx_r67", "AppRegisterPlanListComponent_p_table_96_Template_p_table_onPage_0_listener", "_r99", "ctx_r98", "AppRegisterPlanListComponent_p_table_96_ng_template_2_Template", "AppRegisterPlanListComponent_p_table_96_ng_template_3_Template", "ctx_r8", "AppRegisterPlanListComponent_p_button_99_Template_p_button_click_0_listener", "_r101", "ctx_r100", "ctx_r9", "AppRegisterPlanListComponent", "constructor", "ratingPlanService", "customerService", "groupSimService", "simService", "formBuilder", "accountService", "injector", "listRatingPlan", "listRatingPlanOrigin", "selectItems", "maxDateFrom", "Date", "minDateTo", "maxDateTo", "isShowDialogRegisterSim", "isShowDialogRegisterGroupSim", "isShowDialogResultRegisterGroupSim", "isShowDialogResultRegisterFile", "isShowErrorUpload", "isShowErrorGroup", "allPermissions", "PERMISSIONS", "detailAPN", "response", "RATING_PLAN_SCOPE", "typeRegisterForSim", "ngOnInit", "me", "userType", "sessionService", "userInfo", "type", "items", "label", "home", "icon", "routerLink", "command", "visible", "<PERSON><PERSON><PERSON><PERSON>", "RATING_PLAN_SIM", "REGISTER_BY_LIST", "observableService", "next", "OBSERVABLE", "KEY_INPUT_FILE_VNPT", "REGISTER_BY_FILE", "id", "reload", "flat", "flexible", "provinceCode", "listStatus", "value", "SIM_STATUS", "ACTIVATED", "INACTIVED", "DEACTIVATED", "PURGED", "READY", "searchInfo", "ratingPlanId", "customer", "dateFrom", "dateTo", "formSearch", "group", "pageNumber", "pageSize", "sort", "optionTable", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "action", "tooltip", "func", "item", "simSelected", "headerDialogRegisterForSim", "getListRatingPlanByCustomerCode", "customerCode", "funcAppear", "REGISTER_PLAN", "filter", "el", "messageCommonService", "confirm", "planName", "ok", "cancelPlanForSubcriber", "success", "search", "columns", "key", "size", "align", "isShow", "isSort", "style", "cursor", "color", "funcClick", "simId", "toString", "getDetailSim", "funcGetClassname", "funcConvertText", "planId", "getDetailPLan", "checkSubscriptionType", "className", "convertLongDateToString", "getListRatingPlan", "columnsResultRegisterGroup", "funcGetRouting", "optionTableResultRegisterGroup", "hasShowJumpPage", "optionInputFile", "messageErrorType", "maxSize", "required", "isShowButtonUpload", "actionUpload", "uploadFile", "bind", "disabled", "ngAfterContentChecked", "planSelected", "planGroupSelected", "groupSimSelected", "onSubmitSearch", "page", "limit", "params", "dataParams", "updateParams", "dataSet", "content", "total", "onload", "totalElements", "offload", "Object", "keys", "for<PERSON>ach", "getTime", "stringToStrBase64", "getAllRatingPlanPushForUser", "map", "display", "a", "b", "toUpperCase", "localeCompare", "param", "onChangeDateFrom", "onChangeDateTo", "clearFileCallback", "downloadTemplate", "objectFile", "uploadRegisterByFile", "resApi", "excuteResponseRegisterFileOrList", "listRatingError", "error", "message", "warning", "index", "mapPlanNameError", "mapImsiError", "excludeDescription", "includes", "parseInt", "test", "push", "setValidators", "updateValueAndValidity", "slice", "splice", "i", "keyImsis", "keyPlans", "indexOf", "len", "registerForSim", "dataRequest", "registerPlanForSim", "registerForGroupSim", "registerPlanForGroupSim", "excuteResponseRegisterForGroupSim", "data", "listRegisterRatingPlan", "fileName", "uploadRegisterByList", "pagingResultRegisterGroup", "dataSetResultRegisterGroup", "dataResultRegisterGroupOrigin", "event", "first", "rows", "getById", "getStatusSim", "getDetailCustomer", "getDetailRatingPlan", "getDetailContract", "getDetailApn", "getConnectionStatus", "resp", "userstate", "getDetailStatus", "gprsStatus", "icStatus", "ocStatus", "iddStatus", "smtStatus", "smoStatus", "customerName", "getDetailPlanSim", "apnCode", "rangeIp", "SERVICE_TYPE", "PREPAID", "POSTPAID", "Number", "paidType", "getReload", "getFlexible", "getListProvince", "provinces", "substring", "RELOAD", "YES", "NO", "FLEXIBLE", "CUSTOMER_TYPE", "PERSONAL", "INTERPRISE", "AGENCY", "SUBSCRIPTION_TYPE", "CYCLE_TIME_UNITS", "DAY", "MONTH", "NATION_WIDE", "ɵɵdirectiveInject", "i1", "RatingPlanService", "i2", "CustomerService", "i3", "GroupSimService", "i4", "SimService", "i5", "FormBuilder", "i6", "AccountService", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "AppRegisterPlanListComponent_Template", "rf", "ctx", "AppRegisterPlanListComponent_p_splitButton_6_Template", "AppRegisterPlanListComponent_p_button_7_Template", "AppRegisterPlanListComponent_Template_form_ngSubmit_8_listener", "AppRegisterPlanListComponent_Template_input_ngModelChange_13_listener", "AppRegisterPlanListComponent_Template_input_ngModelChange_18_listener", "AppRegisterPlanListComponent_Template_vnpt_select_valueChange_23_listener", "AppRegisterPlanListComponent_Template_p_dropdown_ngModelChange_26_listener", "AppRegisterPlanListComponent_Template_vnpt_select_valueChange_31_listener", "AppRegisterPlanListComponent_Template_vnpt_select_valueChange_34_listener", "AppRegisterPlanListComponent_Template_p_calendar_ngModelChange_37_listener", "AppRegisterPlanListComponent_Template_p_calendar_onSelect_37_listener", "AppRegisterPlanListComponent_Template_p_calendar_onInput_37_listener", "AppRegisterPlanListComponent_Template_p_calendar_ngModelChange_42_listener", "AppRegisterPlanListComponent_Template_p_calendar_onSelect_42_listener", "AppRegisterPlanListComponent_Template_p_calendar_onInput_42_listener", "AppRegisterPlanListComponent_p_dialog_48_Template", "AppRegisterPlanListComponent_p_dialog_50_Template", "AppRegisterPlanListComponent_Template_table_vnpt_selectItemsChange_51_listener", "AppRegisterPlanListComponent_Template_p_dialog_visibleChange_53_listener", "AppRegisterPlanListComponent_Template_p_dropdown_ngModelChange_58_listener", "AppRegisterPlanListComponent_Template_p_button_click_60_listener", "AppRegisterPlanListComponent_Template_p_button_click_61_listener", "AppRegisterPlanListComponent_Template_p_dialog_visibleChange_63_listener", "AppRegisterPlanListComponent_Template_vnpt_select_valueChange_68_listener", "AppRegisterPlanListComponent_Template_p_dropdown_ngModelChange_73_listener", "AppRegisterPlanListComponent_Template_p_button_click_75_listener", "AppRegisterPlanListComponent_Template_p_button_click_76_listener", "AppRegisterPlanListComponent_Template_p_dialog_visibleChange_78_listener", "AppRegisterPlanListComponent_small_81_Template", "AppRegisterPlanListComponent_p_table_82_Template", "AppRegisterPlanListComponent_Template_p_button_click_84_listener", "AppRegisterPlanListComponent_p_button_85_Template", "AppRegisterPlanListComponent_Template_p_dialog_visibleChange_87_listener", "AppRegisterPlanListComponent_Template_input_file_vnpt_fileObjectChange_90_listener", "fileObject", "AppRegisterPlanListComponent_Template_p_button_click_92_listener", "AppRegisterPlanListComponent_small_95_Template", "AppRegisterPlanListComponent_p_table_96_Template", "AppRegisterPlanListComponent_Template_p_button_click_98_listener", "AppRegisterPlanListComponent_p_button_99_Template", "ɵɵpureFunction2", "_c7", "_c8", "REGISTER_HISTORY", "_c9", "_c10", "_c11", "_c12", "_c13"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\rating-plan-management\\list-register-plan\\app.registerplan.list.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\rating-plan-management\\list-register-plan\\app.registerplan.list.component.html"], "sourcesContent": ["import {Component, Injector, OnInit} from \"@angular/core\";\r\nimport {<PERSON><PERSON><PERSON><PERSON>, Validators} from \"@angular/forms\";\r\nimport {MenuItem} from \"primeng/api\";\r\nimport {CONSTANTS} from \"src/app/service/comon/constants\";\r\nimport {RatingPlanService} from \"src/app/service/rating-plan/RatingPlanService\";\r\nimport {ColumnInfo, OptionTable} from \"../../common-module/table/table.component\";\r\nimport {CustomerService} from \"src/app/service/customer/CustomerService\";\r\nimport {GroupSimService} from \"src/app/service/group-sim/GroupSimService\";\r\nimport {OptionInputFile} from \"../../common-module/input-file/input.file.component\";\r\nimport {SimService} from \"src/app/service/sim/SimService\";\r\nimport {ComponentBase} from \"src/app/component.base\";\r\nimport {AccountService} from \"../../../service/account/AccountService\";\r\n\r\n@Component({\r\n    selector: \"app-register-plan-list\",\r\n    templateUrl: \"./app.registerplan.list.component.html\",\r\n})\r\nexport class AppRegisterPlanListComponent extends ComponentBase implements OnInit {\r\n    searchInfo: {\r\n        msisdn: string | null\r\n        imei: string | null\r\n        ratingPlanId: number | null\r\n        status: any\r\n        contractCode: string | null\r\n        dateFrom: Date | null\r\n        dateTo: Date | null\r\n        customer: string | null\r\n    }\r\n    listStatus: Array<any>;\r\n    items: MenuItem[];\r\n    home: MenuItem;\r\n    itemRegisters: MenuItem[];\r\n    listCustomer: Array<any>;\r\n    listRatingPlan: Array<any> = [];\r\n    listRatingPlanOrigin: Array<any> = [];\r\n    listGroupSim: Array<any>;\r\n    selectItems: Array<any> = [];\r\n    columns: Array<ColumnInfo>;\r\n    dataSet: {\r\n        content: Array<any>,\r\n        total: number\r\n    };\r\n    optionTable: OptionTable;\r\n    pageNumber: number;\r\n    pageSize: number;\r\n    sort: string;\r\n    formSearch: any;\r\n    maxDateFrom: Date | number | string | null = new Date();\r\n    minDateTo: Date | number | string | null = null;\r\n    maxDateTo: Date | number | string | null = new Date();\r\n    //register plan\r\n    headerDialogRegisterForSim: string | null;\r\n    isShowDialogRegisterSim: boolean = false;\r\n    planSelected: number | null;\r\n    planGroupSelected: number | null;\r\n    simSelected: any | null;\r\n\r\n    isShowDialogRegisterGroupSim: boolean = false;\r\n    groupSimSelected: number | null;\r\n\r\n    isShowDialogResultRegisterGroupSim: boolean = false;\r\n    dataSetResultRegisterGroup: {\r\n        content: Array<any>,\r\n        total: number\r\n    }\r\n    dataResultRegisterGroupOrigin: Array<any>;\r\n    columnsResultRegisterGroup: Array<ColumnInfo>;\r\n    optionTableResultRegisterGroup: OptionTable;\r\n    pageNumberResultRegister: number | null;\r\n    pageSizeResultRegister: number | null;\r\n    totalResultRegisterGroup: number;\r\n    totalSuccessResultRegisterGroup: number;\r\n    totalFailResultRegisterGroup: number;\r\n\r\n    isShowDialogResultRegisterFile: boolean = false;\r\n    optionInputFile: OptionInputFile;\r\n    fileObject: any;\r\n\r\n\r\n    simImports: Array<any>;\r\n    simImportsOrigin: Array<any>;\r\n    mapFormSimImports: any;\r\n    mapPlanNameError: any;\r\n    mapImsiError: any;\r\n    pageSizeSimImport: number = 10;\r\n    rowFirstSimImport: number = 0;\r\n    isShowErrorUpload: boolean = false;\r\n    isShowErrorGroup: boolean = false;\r\n    messageErrorUpload: string | null;\r\n    userType: number;\r\n    allPermissions = CONSTANTS.PERMISSIONS;\r\n    isShowModalDetailSim: boolean = false;\r\n    detailSim: any = {};\r\n    detailStatusSim: any = {};\r\n    detailCustomer: any = {};\r\n    detailRatingPlan: any = {};\r\n    detailContract: any = {};\r\n    detailAPN: any = {};\r\n    simId: string;\r\n    isShowModalDetailPlan: boolean = false;\r\n    ratingPlanInfo: {\r\n        id: number | null;\r\n        code: string | null,\r\n        name: string | null,\r\n        status: number | null,\r\n        dispatchCode: string | null,\r\n        customerType: string | null,\r\n        subscriptionFee: string | null,\r\n        subscriptionType: string | null,\r\n        ratingScope: number | null,\r\n        cycleTimeUnit: string | null,\r\n        cycleInterval: string | null,\r\n        reload: string | null,\r\n        flat: string | null,\r\n        limitDataUsage: string | null,\r\n        limitSmsOutside: string | null,\r\n        limitSmsInside: string | null,\r\n        flexible: string | null,\r\n        feePerDataUnit: string | null,\r\n        squeezedSpeed: string | null,\r\n        feeSmsInside: string | null,\r\n        feeSmsOutside: string | null,\r\n        maximumFee: string | null,\r\n        dataRoundUnit: string | null,\r\n        downSpeed: string | null,\r\n        provinceCode: Array<string> | null,\r\n        description: string | null\r\n    };\r\n    provinces: any[] | undefined;\r\n    myProvices: string | null;\r\n    subscriptionTypes: any = [];\r\n    planId: number;\r\n    checkedReload: boolean;\r\n    checkedFlexible: boolean;\r\n    response: any = {};\r\n    planScopes = CONSTANTS.RATING_PLAN_SCOPE;\r\n    typeRegisterForSim: \"register\" | \"change\" | \"cancel\" = \"register\"\r\n\r\n    constructor(private ratingPlanService: RatingPlanService,\r\n                private customerService: CustomerService,\r\n                private groupSimService: GroupSimService,\r\n                private simService: SimService,\r\n                private formBuilder: FormBuilder,\r\n                private accountService: AccountService,\r\n                injector: Injector) {\r\n        super(injector);\r\n    }\r\n\r\n    ngOnInit(): void {\r\n        let me = this;\r\n        this.userType = this.sessionService.userInfo.type;\r\n        this.items = [{label: this.tranService.translate(\"global.menu.ratingplanmgmt\")}, {label: this.tranService.translate(\"global.menu.registerplan\")}];\r\n        this.home = {icon: 'pi pi-home', routerLink: '/'};\r\n        this.itemRegisters = [\r\n            {\r\n                label: this.tranService.translate(\"global.button.registerPlanForGroup\"),\r\n                command: () => {\r\n                    me.isShowDialogRegisterGroupSim = true;\r\n                },\r\n                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_BY_LIST])\r\n            },\r\n            {\r\n                label: this.tranService.translate(\"global.button.registerPlanByFile\"),\r\n                command: () => {\r\n                    me.isShowDialogResultRegisterFile = true;\r\n                    //clear input file\r\n                    me.observableService.next(CONSTANTS.OBSERVABLE.KEY_INPUT_FILE_VNPT, {});\r\n                    me.simImportsOrigin = undefined;\r\n                    me.isShowErrorUpload = false;\r\n                },\r\n                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_BY_FILE])\r\n            }\r\n        ];\r\n        this.detailSim = {};\r\n        this.ratingPlanInfo = {\r\n            id: null,\r\n            code: null,\r\n            name: null,\r\n            status: null,\r\n            dispatchCode: null,\r\n            customerType: null,\r\n            subscriptionFee: null,\r\n            subscriptionType: null,\r\n            ratingScope: null,\r\n            cycleTimeUnit: null,\r\n            cycleInterval: null,\r\n            reload: null,\r\n            flat: null,\r\n            limitDataUsage: null,\r\n            limitSmsOutside: null,\r\n            limitSmsInside: null,\r\n            flexible: null,\r\n            feePerDataUnit: null,\r\n            squeezedSpeed: null,\r\n            feeSmsInside: null,\r\n            feeSmsOutside: null,\r\n            maximumFee: null,\r\n            dataRoundUnit: null,\r\n            downSpeed: null,\r\n            provinceCode: null,\r\n            description: null\r\n        };\r\n        this.listStatus = [\r\n            // {\r\n            //     value: CONSTANTS.SIM_STATUS.READY,\r\n            //     name: this.tranService.translate(\"sim.status.ready\")\r\n            // },\r\n            {\r\n                value: [CONSTANTS.SIM_STATUS.ACTIVATED],\r\n                name: this.tranService.translate(\"sim.status.activated\")\r\n            },\r\n            {\r\n                value: [CONSTANTS.SIM_STATUS.INACTIVED],\r\n                name: this.tranService.translate(\"sim.status.inactivated\")\r\n            },\r\n            {\r\n                value: [CONSTANTS.SIM_STATUS.DEACTIVATED],\r\n                name: this.tranService.translate(\"sim.status.deactivated\")\r\n            },\r\n            {\r\n                value: [CONSTANTS.SIM_STATUS.PURGED],\r\n                name: this.tranService.translate(\"sim.status.purged\")\r\n            },\r\n            {\r\n                value: [15 + CONSTANTS.SIM_STATUS.ACTIVATED, 15 + CONSTANTS.SIM_STATUS.READY],\r\n                name: this.tranService.translate(\"sim.status.processingChangePlan\")\r\n            },\r\n            {\r\n                value: [10 + CONSTANTS.SIM_STATUS.ACTIVATED, 10 + CONSTANTS.SIM_STATUS.READY],\r\n                name: this.tranService.translate(\"sim.status.processingRegisterPlan\")\r\n            },\r\n            {\r\n                value: [20 + CONSTANTS.SIM_STATUS.ACTIVATED, 20 + CONSTANTS.SIM_STATUS.READY],\r\n                name: this.tranService.translate(\"sim.status.waitingCancelPlan\")\r\n            },\r\n        ]\r\n\r\n        this.searchInfo = {\r\n            msisdn: null,\r\n            status: null,\r\n            imei: null,\r\n            ratingPlanId: null,\r\n            customer: null,\r\n            contractCode: null,\r\n            dateFrom: null,\r\n            dateTo: null\r\n        }\r\n        this.formSearch = this.formBuilder.group(this.searchInfo);\r\n        this.selectItems = [];\r\n        this.pageNumber = 0;\r\n        this.pageSize = 10;\r\n        // this.sort = \"createdDate,asc\";\r\n        this.sort = \"ratingPlanName,asc\";\r\n\r\n        this.optionTable = {\r\n            hasClearSelected: true,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n            action: [\r\n                {\r\n                    icon: \"pi pi-file\",\r\n                    tooltip: this.tranService.translate(\"global.button.registerRatingPlan\"),\r\n                    func: function (id, item) {\r\n                        me.simSelected = item;\r\n                        me.headerDialogRegisterForSim = me.tranService.translate(\"global.button.registerRatingPlan\");\r\n                        me.typeRegisterForSim = \"register\";\r\n                        me.getListRatingPlanByCustomerCode(item.customerCode);\r\n                        // me.listRatingPlan = [...me.listRatingPlanOrigin];\r\n                        me.isShowDialogRegisterSim = true;\r\n                    },\r\n                    funcAppear: function (id, item) {\r\n                        return me.listRatingPlanOrigin && (item.status == CONSTANTS.SIM_STATUS.ACTIVATED || item.status == CONSTANTS.SIM_STATUS.READY) && (item.ratingPlanId == null)\r\n                            && me.listRatingPlan.length > 0 && me.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_PLAN]);\r\n                    }\r\n                },\r\n                {\r\n                    icon: \"pi pi-sync\",\r\n                    tooltip: this.tranService.translate(\"global.button.changeRatingPlan\"),\r\n                    func: function (id, item) {\r\n                        me.simSelected = item;\r\n                        me.headerDialogRegisterForSim = me.tranService.translate(\"global.button.changeRatingPlan\");\r\n                        me.typeRegisterForSim = \"change\"\r\n                        me.getListRatingPlanByCustomerCode(item.customerCode);\r\n                        me.listRatingPlanOrigin = me.listRatingPlanOrigin.filter(el => el.id != item.ratingPlanId);\r\n                        me.isShowDialogRegisterSim = true;\r\n                    },\r\n                    funcAppear: function (id, item) {\r\n                        return me.listRatingPlanOrigin && item.ratingPlanId != null && (item.status == CONSTANTS.SIM_STATUS.ACTIVATED || item.status == CONSTANTS.SIM_STATUS.READY) && me.listRatingPlan.length > 0 && me.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_PLAN]);\r\n                    }\r\n                },\r\n                {\r\n                    icon: \"pi pi-times\",\r\n                    tooltip: this.tranService.translate(\"global.button.cancelRatingPlan\"),\r\n                    func: function (id, item) {\r\n                        me.simSelected = item;\r\n                        me.typeRegisterForSim = \"change\"\r\n                        me.messageCommonService.confirm(me.tranService.translate(\"global.button.cancelRatingPlan\"), me.tranService.translate(\"global.message.confirmCancelPlan\", {\r\n                            planName: item.ratingPlanName,\r\n                            msisdn: item.msisdn\r\n                        }), {\r\n                            ok: () => {\r\n                                me.ratingPlanService.cancelPlanForSubcriber(item.msisdn, item.ratingPlanId, () => {\r\n                                    me.messageCommonService.success(me.tranService.translate(\"ratingPlan.text.textCancelSuccess\"));\r\n                                    me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\r\n                                });\r\n                            }\r\n                        })\r\n                    },\r\n                    funcAppear: function (id, item) {\r\n                        return me.listRatingPlanOrigin && item.ratingPlanId != null && (item.status == CONSTANTS.SIM_STATUS.ACTIVATED || item.status == CONSTANTS.SIM_STATUS.READY) && me.listRatingPlan.length > 0 && me.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_PLAN]);\r\n                    }\r\n                }\r\n            ]\r\n        },\r\n            this.columns = [\r\n                {\r\n                    name: this.tranService.translate(\"sim.label.sothuebao\"),\r\n                    key: \"msisdn\",\r\n                    size: \"200px\",\r\n                    align: \"left\",\r\n                    isShow: true,\r\n                    isSort: true,\r\n                    style: {\r\n                        cursor: \"pointer\",\r\n                        color: \"var(--mainColorText)\"\r\n                    },\r\n                    funcClick(id, item) {\r\n                        me.simId = item?.msisdn.toString();\r\n                        me.getDetailSim();\r\n                        me.isShowModalDetailSim = true;\r\n                    },\r\n                },\r\n                {\r\n                    name: this.tranService.translate(\"sim.label.trangthaisim\"),\r\n                    key: \"status\",\r\n                    size: \"150px\",\r\n                    align: \"left\",\r\n                    isShow: true,\r\n                    isSort: true,\r\n                    funcGetClassname: (value) => {\r\n                        if (value == 0) {\r\n                            return ['p-2', \"border-round\", \"border-400\", \"text-color\", \"inline-block\"];\r\n                        } else if (value == CONSTANTS.SIM_STATUS.READY) {\r\n                            // return ['p-1', \"bg-blue-600\", \"border-round\",\"inline-block\"];\r\n                            return ['p-2', 'text-green-800', \"bg-green-100\", \"border-round\", \"inline-block\"];\r\n                        } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\r\n                            return ['p-2', 'text-green-800', \"bg-green-100\", \"border-round\", \"inline-block\"];\r\n                        } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\r\n                            return ['p-2', 'text-yellow-800', \"bg-yellow-100\", \"border-round\", \"inline-block\"];\r\n                        } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\r\n                            return ['p-2', 'text-indigo-600', \"bg-indigo-100\", \"border-round\", \"inline-block\"];\r\n                        } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\r\n                            return ['p-2', 'text-red-700', \"bg-red-100\", \"border-round\", \"inline-block\"];\r\n                        } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\r\n                            return ['p-2', 'text-cyan-800', \"bg-cyan-100\", \"border-round\", \"inline-block\"];\r\n                        } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\r\n                            return ['p-2', 'text-teal-800', \"bg-teal-100\", \"border-round\", \"inline-block\"];\r\n                        } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\r\n                            return ['p-2', 'text-orange-700', \"bg-orange-100\", \"border-round\", \"inline-block\"];\r\n                        }\r\n                        return [];\r\n                    },\r\n                    funcConvertText: (value) => {\r\n                        if (value == 0) {\r\n                            return me.tranService.translate(\"sim.status.inventory\");\r\n                        } else if (value == CONSTANTS.SIM_STATUS.READY) {\r\n                            // return me.tranService.translate(\"sim.status.ready\");\r\n                            return me.tranService.translate(\"sim.status.activated\");\r\n                        } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\r\n                            return me.tranService.translate(\"sim.status.activated\");\r\n                        } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\r\n                            return me.tranService.translate(\"sim.status.deactivated\");\r\n                        } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\r\n                            return me.tranService.translate(\"sim.status.purged\");\r\n                        } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\r\n                            return me.tranService.translate(\"sim.status.inactivated\");\r\n                        } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\r\n                            return this.tranService.translate(\"sim.status.processingChangePlan\");\r\n                        } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\r\n                            return this.tranService.translate(\"sim.status.processingRegisterPlan\");\r\n                        } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\r\n                            return this.tranService.translate(\"sim.status.waitingCancelPlan\");\r\n                        }\r\n                        return \"\";\r\n                    },\r\n                    style: {\r\n                        color: \"white\"\r\n                    }\r\n                },\r\n                {\r\n                    name: this.tranService.translate(\"sim.label.goicuoc\"),\r\n                    key: \"ratingPlanName\",\r\n                    size: \"200px\",\r\n                    align: \"left\",\r\n                    isShow: true,\r\n                    isSort: true,\r\n                    style: {\r\n                        cursor: \"pointer\",\r\n                        color: \"var(--mainColorText)\"\r\n                    },\r\n                    funcClick(id, item) {\r\n                        me.planId = item?.ratingPlanId;\r\n                        me.getDetailPLan();\r\n                        me.checkSubscriptionType();\r\n                        me.isShowModalDetailPlan = true;\r\n                    },\r\n                    className: \"white-space-normal\"\r\n                },\r\n                {\r\n                    name: this.tranService.translate(\"sim.label.imeiDevice\"),\r\n                    key: \"imei\",\r\n                    size: \"175px\",\r\n                    align: \"left\",\r\n                    isShow: true,\r\n                    isSort: true,\r\n                },\r\n                {\r\n                    name: this.tranService.translate(\"sim.label.khachhang\"),\r\n                    key: \"customerName\",\r\n                    size: \"250px\",\r\n                    align: \"left\",\r\n                    isShow: true,\r\n                    isSort: true,\r\n                    className: \"white-space-normal\"\r\n                },\r\n                {\r\n                    name: this.tranService.translate(\"sim.label.mahopdong\"),\r\n                    key: \"contractCode\",\r\n                    size: \"200px\",\r\n                    align: \"left\",\r\n                    isShow: true,\r\n                    isSort: true,\r\n                },\r\n                {\r\n                    name: this.tranService.translate(\"sim.label.ngaylamhopdong\"),\r\n                    key: \"contractDate\",\r\n                    size: \"150px\",\r\n                    align: \"left\",\r\n                    isShow: true,\r\n                    isSort: true,\r\n                    funcConvertText(value) {\r\n                        return me.utilService.convertLongDateToString(value);\r\n                    },\r\n                }\r\n            ]\r\n        this.getListRatingPlan();\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n\r\n        this.columnsResultRegisterGroup = [\r\n            {\r\n                name: this.tranService.translate(\"sim.label.sothuebao\"),\r\n                key: \"msisdn\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                style: {\r\n                    cursor: \"pointer\",\r\n                    color: \"var(--mainColorText)\"\r\n                },\r\n                funcGetRouting(item) {\r\n                    return [`/sims/detail/${item.imsi}`]\r\n                },\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"sim.label.trangthaisim\"),\r\n                key: \"status\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcGetClassname: (value) => {\r\n                    if (value == 0) {\r\n                        return ['p-1', \"border-round\", \"border-400\", \"text-color\", \"inline-block\"];\r\n                    } else if (value == CONSTANTS.SIM_STATUS.READY) {\r\n                        // return ['p-1', \"bg-blue-600\", \"border-round\",\"inline-block\"];\r\n                        return ['p-1', \"bg-green-600\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\r\n                        return ['p-1', \"bg-green-600\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\r\n                        return ['p-1', \"bg-yellow-500\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\r\n                        return ['p-1', \"bg-indigo-600\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\r\n                        return ['p-1', \"bg-red-500\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\r\n                        return ['p-2', 'text-cyan-800', \"bg-cyan-100\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\r\n                        return ['p-2', 'text-teal-800', \"bg-teal-100\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\r\n                        return ['p-2', 'text-orange-700', \"bg-orange-100\", \"border-round\", \"inline-block\"];\r\n                    }\r\n                    return [];\r\n                },\r\n                funcConvertText: (value) => {\r\n                    if (value == 0) {\r\n                        return me.tranService.translate(\"sim.status.inventory\");\r\n                    } else if (value == CONSTANTS.SIM_STATUS.READY) {\r\n                        // return me.tranService.translate(\"sim.status.ready\");\r\n                        return me.tranService.translate(\"sim.status.activated\");\r\n                    } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\r\n                        return me.tranService.translate(\"sim.status.activated\");\r\n                    } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\r\n                        return me.tranService.translate(\"sim.status.deactivated\");\r\n                    } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\r\n                        return me.tranService.translate(\"sim.status.purged\");\r\n                    } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\r\n                        return me.tranService.translate(\"sim.status.inactivated\");\r\n                    } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\r\n                        return this.tranService.translate(\"sim.status.processingChangePlan\");\r\n                    } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\r\n                        return this.tranService.translate(\"sim.status.processingRegisterPlan\");\r\n                    } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\r\n                        return this.tranService.translate(\"sim.status.waitingCancelPlan\");\r\n                    }\r\n                    return \"\";\r\n                },\r\n                style: {\r\n                    color: \"white\"\r\n                }\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"sim.label.khachhang\"),\r\n                key: \"customerName\",\r\n                size: \"250px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcGetClassname: (value) => {\r\n                    return ['uppercase', 'white-space-normal'];\r\n                }\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"sim.label.mahopdong\"),\r\n                key: \"contractCode\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n            },\r\n        ]\r\n\r\n        this.optionTableResultRegisterGroup = {\r\n            hasClearSelected: true,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n            hasShowJumpPage: false\r\n        }\r\n\r\n        this.optionInputFile = {\r\n            type: ['xls', 'xlsx'],\r\n            messageErrorType: this.tranService.translate(\"global.message.wrongFileExcel\"),\r\n            maxSize: 10,\r\n            unit: \"MB\",\r\n            required: true,\r\n            isShowButtonUpload: true,\r\n            actionUpload: this.uploadFile.bind(this),\r\n            disabled: false\r\n        }\r\n    }\r\n\r\n    ngAfterContentChecked(): void {\r\n        if (this.isShowDialogRegisterSim == false) {\r\n            this.planSelected = null;\r\n        }\r\n        if (this.isShowDialogRegisterGroupSim == false) {\r\n            this.planGroupSelected = null;\r\n            this.groupSimSelected = null;\r\n        }\r\n    }\r\n\r\n    onSubmitSearch() {\r\n        this.pageNumber = 0;\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n    }\r\n\r\n    search(page, limit, sort, params) {\r\n        let me = this;\r\n        this.pageNumber = page;\r\n        this.pageSize = limit;\r\n        this.sort = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        this.updateParams(dataParams);\r\n        this.dataSet = {\r\n            content: [],\r\n            total: 0\r\n        }\r\n        me.messageCommonService.onload();\r\n        this.simService.search(dataParams, (response) => {\r\n            // console.log(dataParams)\r\n            me.dataSet = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n            // console.log(this.dataSet)\r\n        }, null, () => {\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    updateParams(dataParams) {\r\n        let me = this;\r\n        Object.keys(this.searchInfo).forEach(key => {\r\n            if (this.searchInfo[key] != null) {\r\n                if (key == \"dateFrom\") {\r\n                    dataParams[\"contractDateFrom\"] = this.searchInfo.dateFrom.getTime();\r\n                } else if (key == \"dateTo\") {\r\n                    dataParams[\"contractDateTo\"] = this.searchInfo.dateTo.getTime();\r\n                } else if (key == \"contractCode\") {\r\n                    dataParams[\"contractCode\"] = me.utilService.stringToStrBase64(this.searchInfo.contractCode);\r\n                } else {\r\n                    dataParams[key] = this.searchInfo[key];\r\n                }\r\n            }\r\n        })\r\n    }\r\n\r\n\r\n    getListRatingPlan(){\r\n        let me = this;\r\n        this.ratingPlanService.getAllRatingPlanPushForUser((response)=>{\r\n            me.listRatingPlan = (response || []).map(el => {\r\n                return {\r\n                    ...el,\r\n                    display: `${el.name||'unknown'} - ${el.code||'unknown'}`\r\n                }\r\n            });\r\n            me.listRatingPlan.sort((a,b) => (a.name || \"\").toUpperCase().localeCompare((b.name || \"\").toUpperCase()) > 0 ? 1: -1);\r\n            // me.listRatingPlanOrigin = [...this.listRatingPlan];\r\n        })\r\n    }\r\n    getListRatingPlanByCustomerCode(customerCode) {\r\n        let me = this;\r\n        let param = {\r\n            \"customerCode\": customerCode\r\n        }\r\n        this.ratingPlanService.getListRatingPlanByCustomerCode(param, (response) => {\r\n            me.listRatingPlanOrigin = (response || []).map(el => {\r\n                return {\r\n                    ...el,\r\n                    display: `${el.name || 'unknown'} - ${el.code || 'unknown'}`\r\n                }\r\n            });\r\n        })\r\n    }\r\n\r\n\r\n    onChangeDateFrom(value) {\r\n        if (value) {\r\n            this.minDateTo = value;\r\n        } else {\r\n            this.minDateTo = null\r\n        }\r\n    }\r\n\r\n    onChangeDateTo(value) {\r\n        if (value) {\r\n            this.maxDateFrom = value;\r\n        } else {\r\n            this.maxDateFrom = new Date();\r\n        }\r\n    }\r\n\r\n    clearFileCallback() {\r\n        this.isShowErrorUpload = false;\r\n    }\r\n\r\n    downloadTemplate() {\r\n        this.ratingPlanService.downloadTemplate();\r\n    }\r\n\r\n    uploadFile(objectFile: any) {\r\n        let me = this;\r\n        me.messageCommonService.onload();\r\n        this.ratingPlanService.uploadRegisterByFile(objectFile, (resApi) => {\r\n            me.excuteResponseRegisterFileOrList(resApi);\r\n        })\r\n    }\r\n\r\n    excuteResponseRegisterFileOrList(resApi) {\r\n        let me = this;\r\n        me.simImportsOrigin = undefined;\r\n        me.isShowErrorUpload = false;\r\n        this.optionInputFile.disabled = true;\r\n        let response = resApi.listRatingError || [];\r\n        if (resApi.total == 0) {\r\n            me.messageCommonService.error(me.tranService.translate(resApi.message));\r\n            me.isShowDialogResultRegisterFile = false;\r\n            me.isShowDialogResultRegisterGroupSim = false;\r\n            me.isShowDialogRegisterGroupSim = false;\r\n            return;\r\n        }\r\n        if (resApi.message.toUpperCase() == \"ok\".toUpperCase()) {\r\n            me.messageCommonService.warning(me.tranService.translate(\"ratingPlan.text.textResultRegisterByFile\", {\r\n                error: resApi.total - resApi.error,\r\n                total: resApi.total\r\n            }));\r\n        } else {\r\n            me.messageCommonService.error(me.tranService.translate(resApi.message))\r\n        }\r\n        if (response.length == 0) {\r\n            me.isShowDialogResultRegisterFile = false;\r\n            me.isShowDialogResultRegisterGroupSim = false;\r\n            me.isShowDialogRegisterGroupSim = false;\r\n            return;\r\n        }\r\n        //0 normal, 1 error imsi, 2 error planName\r\n        let index = 0;\r\n        me.mapFormSimImports = {};\r\n        me.mapPlanNameError = {};\r\n        me.mapImsiError = {};\r\n        let excludeDescription = ['error.invalid.isdn.empty', \"error.invalid.isdn.not.format\", \"error.invalid.rating.empty\", \"error.invalid.rating.not.format\"];\r\n        response.forEach(el => {\r\n            if (!excludeDescription.includes(el.description)) {\r\n                if (parseInt(el.type) == 0) {\r\n                    if (el.msisdn != null && el.msisdn != \"\" && /^(\\+?84)[1-9][0-9]{8,9}$/.test(el.msisdn)) {\r\n                        if ((el.description || \"\") != \"\") {\r\n                            if (el.description in me.mapImsiError) {\r\n                                me.mapImsiError[el.description].push(el.msisdn);\r\n                            } else {\r\n                                me.mapImsiError[el.description] = [el.msisdn];\r\n                            }\r\n                        }\r\n                    }\r\n                } else if (parseInt(el.type) == 1) {\r\n                    if (el.ratingPlanName != null && el.ratingPlanName != \"\" && /^[a-zA-Z0-9\\-_]*$/.test(el.ratingPlanName)) {\r\n                        if ((el.description || \"\") != \"\") {\r\n                            if (el.description in me.mapPlanNameError) {\r\n                                me.mapPlanNameError[el.description].push(el.planName);\r\n                            } else {\r\n                                me.mapPlanNameError[el.description] = [el.planName];\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            } else {\r\n                el.description = \"\"\r\n            }\r\n            el['keyForm'] = `keyForm${index++}`;\r\n            me.mapFormSimImports[el['keyForm']] = me.formBuilder.group(el);\r\n            me.mapFormSimImports[el['keyForm']].controls['msisdn'].setValidators([Validators.required, Validators.pattern('^(\\\\+?84)[1-9][0-9]{8,9}$')]);\r\n            me.mapFormSimImports[el['keyForm']].controls['msisdn'].updateValueAndValidity();\r\n            me.mapFormSimImports[el['keyForm']].controls['ratingPlanName'].setValidators([Validators.required, Validators.pattern('^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$')]);\r\n            me.mapFormSimImports[el['keyForm']].controls['ratingPlanName'].updateValueAndValidity();\r\n        });\r\n        me.rowFirstSimImport = 0;\r\n        me.pageSizeSimImport = 10;\r\n        me.simImportsOrigin = [...response];\r\n        me.simImports = me.simImportsOrigin.slice(0, 10);\r\n    }\r\n\r\n    removeItemSimImport(item, index) {\r\n        // console.log(index);\r\n        this.simImportsOrigin.splice(index, 1);\r\n        this.simImports = this.simImportsOrigin.slice(this.rowFirstSimImport, this.rowFirstSimImport + this.pageSizeSimImport)\r\n        delete this.mapFormSimImports[item['keyForm']];\r\n        if (this.simImportsOrigin.length == 0) {\r\n            this.isShowDialogResultRegisterFile = false;\r\n        }\r\n    }\r\n\r\n    checkValidListImport() {\r\n        if (this.simImports.length == 0) {\r\n            return false;\r\n        }\r\n        let keys = Object.keys(this.mapFormSimImports);\r\n        for (let i = 0; i < keys.length; i++) {\r\n            let key = keys[i];\r\n            if (this.mapFormSimImports[key].invalid) {\r\n                return false;\r\n            }\r\n        }\r\n        for (let i = 0; i < this.simImports.length; i++) {\r\n            if (this.simImports[i].description != null && this.simImports[i].description != \"\") {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    checkValueSimImportChange(item) {\r\n        if (item.ratingPlanName != null && item.msisdn != null) {\r\n            let description = \"\";\r\n            let keyImsis = Object.keys(this.mapImsiError);\r\n            let keyPlans = Object.keys(this.mapPlanNameError);\r\n            for (let i = 0; i < keyImsis.length; i++) {\r\n                if (this.mapImsiError[keyImsis[i]].includes(item.msisdn)) {\r\n                    description = keyImsis[i];\r\n                    break;\r\n                }\r\n            }\r\n            if (description == \"\") {\r\n                for (let i = 0; i < keyPlans.length; i++) {\r\n                    if (this.mapPlanNameError[keyPlans[i]].includes(item.ratingPlanName)) {\r\n                        description = keyPlans[i];\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n            // console.log(description);\r\n            if (description.indexOf(\"duplicated\") >= 0) {\r\n                let len = this.simImportsOrigin.map(el => el.msisdn).filter(el => el == item.msisdn).length;\r\n                if (len == 1) {\r\n                    description = \"\";\r\n                    this.simImportsOrigin.forEach(el => {\r\n                        if (el.description.indexOf(\"duplicated\") >= 0 && el.msisdn == item) {\r\n                            el.description = \"\";\r\n                        }\r\n                    })\r\n                }\r\n            }\r\n            item.description = description;\r\n        }\r\n    }\r\n\r\n    registerForSim() {\r\n        if (this.planSelected == null || this.planSelected == undefined) return;\r\n        let dataRequest = {\r\n            \"userId\": 1,\r\n            \"msisdn\": this.simSelected.msisdn,\r\n            \"ratingPlanId\": this.planSelected\r\n        }\r\n        let me = this;\r\n        me.messageCommonService.onload();\r\n        this.ratingPlanService.registerPlanForSim(dataRequest, (response) => {\r\n            if (me.typeRegisterForSim == \"register\") {\r\n                me.messageCommonService.success(me.tranService.translate(\"ratingPlan.text.textRegisterSuccess\"));\r\n            } else {\r\n                me.messageCommonService.success(me.tranService.translate(\"ratingPlan.text.textChangeSuccess\"));\r\n            }\r\n            me.isShowDialogRegisterSim = false;\r\n            me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\r\n        })\r\n\r\n    }\r\n\r\n    registerForGroupSim() {\r\n        if (this.planGroupSelected == null || this.planGroupSelected == undefined || this.groupSimSelected == null || this.groupSimSelected == undefined) return;\r\n        let dataRequest = {\r\n            \"userId\": 1,\r\n            \"groupSimId\": this.groupSimSelected,\r\n            \"ratingPlanId\": this.planGroupSelected\r\n        }\r\n        let me = this;\r\n        me.messageCommonService.onload();\r\n        this.ratingPlanService.registerPlanForGroupSim(dataRequest, (response) => {\r\n            // me.messageCommonService.success(me.tranService.translate(\"global.message.success\"));\r\n            // me.isShowDialogRegisterSim = false;\r\n            // me.search(me.pageNumber,me.pageSize, me.sort, me.searchInfo);\r\n            //\r\n            // me.dataResultRegisterGroupOrigin = [{\r\n            //     imsi: \"841388100826\",\r\n            //     status: CONSTANTS.SIM_STATUS.ACTIVATED,\r\n            //     customerInfo: \"UBND Thị trấn Vân Đình\",\r\n            //     contractCode: \"HNI-LD/01060626\"\r\n            // },{\r\n            //     imsi: \"84842138660\",\r\n            //     status: CONSTANTS.SIM_STATUS.PURGED,\r\n            //     customerInfo: \"Nguyễn Văn A\",\r\n            //     contractCode: \"HNI-LD/00661242\"\r\n            // },{\r\n            //     imsi: \"84842139301\",\r\n            //     status: CONSTANTS.SIM_STATUS.ACTIVATED,\r\n            //     customerInfo: \"Tổng công ty Điện Lực Thành phố Hà Nội\",\r\n            //     contractCode: \"HNI-LD/01060638\"\r\n            // },{\r\n            //     imsi: \"841388100826\",\r\n            //     status: CONSTANTS.SIM_STATUS.ACTIVATED,\r\n            //     customerInfo: \"UBND Thị trấn Vân Đình\",\r\n            //     contractCode: \"HNI-LD/01060626\"\r\n            // },{\r\n            //     imsi: \"84842138660\",\r\n            //     status: CONSTANTS.SIM_STATUS.PURGED,\r\n            //     customerInfo: \"Nguyễn Văn A\",\r\n            //     contractCode: \"HNI-LD/00661242\"\r\n            // },{\r\n            //     imsi: \"84842139301\",\r\n            //     status: CONSTANTS.SIM_STATUS.ACTIVATED,\r\n            //     customerInfo: \"Tổng công ty Điện Lực Thành phố Hà Nội\",\r\n            //     contractCode: \"HNI-LD/01060638\"\r\n            // },{\r\n            //     imsi: \"841388100826\",\r\n            //     status: CONSTANTS.SIM_STATUS.ACTIVATED,\r\n            //     customerInfo: \"UBND Thị trấn Vân Đình\",\r\n            //     contractCode: \"HNI-LD/01060626\"\r\n            // },{\r\n            //     imsi: \"84842138660\",\r\n            //     status: CONSTANTS.SIM_STATUS.PURGED,\r\n            //     customerInfo: \"Nguyễn Văn A\",\r\n            //     contractCode: \"HNI-LD/00661242\"\r\n            // },{\r\n            //     imsi: \"84842139301\",\r\n            //     status: CONSTANTS.SIM_STATUS.ACTIVATED,\r\n            //     customerInfo: \"Tổng công ty Điện Lực Thành phố Hà Nội\",\r\n            //     contractCode: \"HNI-LD/01060638\"\r\n            // }]\r\n            //\r\n            // me.totalResultRegisterGroup = response.total;\r\n            // me.totalSuccessResultRegisterGroup = response.inact1 + response.inact2;\r\n            // me.totalFailResultRegisterGroup = response.active + response.purge;\r\n            // me.dataResultRegisterGroupOrigin = response.simInGroupResponses;\r\n            // me.pageNumberResultRegister = 0;\r\n            // me.pageSizeResultRegister = 10;\r\n            // me.pagingResultRegisterGroup(0,10,null, null)\r\n            // me.isShowDialogRegisterGroupSim = false;\r\n            // me.isShowDialogResultRegisterGroupSim = true;\r\n            me.excuteResponseRegisterForGroupSim(response);\r\n            me.messageCommonService.offload();\r\n\r\n        }, (error) => {\r\n            me.messageCommonService.error(me.tranService.translate(error.error.error.message));\r\n            me.isShowDialogRegisterGroupSim = false;\r\n            me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\r\n        })\r\n    }\r\n\r\n    excuteResponseRegisterForGroupSim(resApi) {\r\n        let me = this;\r\n        me.simImportsOrigin = undefined;\r\n        me.isShowErrorGroup = false;\r\n        let response = resApi.listRatingError || [];\r\n        if (resApi.total == 0) {\r\n            me.messageCommonService.error(me.tranService.translate(resApi.message));\r\n            me.isShowDialogResultRegisterGroupSim = false;\r\n            me.isShowDialogRegisterGroupSim = false;\r\n            return;\r\n        }\r\n        if (resApi.message.toUpperCase() == \"ok\".toUpperCase()) {\r\n            me.messageCommonService.warning(me.tranService.translate(\"ratingPlan.text.textResultRegisterGroupSim\", {\r\n                error: resApi.total - resApi.error,\r\n                total: resApi.total\r\n            }));\r\n        } else {\r\n            me.messageCommonService.error(me.tranService.translate(resApi.message))\r\n        }\r\n        if (response.length == 0) {\r\n            me.isShowDialogResultRegisterGroupSim = false;\r\n            me.isShowDialogRegisterGroupSim = false;\r\n            return;\r\n        }\r\n        me.isShowDialogResultRegisterGroupSim = true;\r\n        //0 normal, 1 error imsi, 2 error planName\r\n        let index = 0;\r\n        me.mapFormSimImports = {};\r\n        me.mapPlanNameError = {};\r\n        me.mapImsiError = {};\r\n        let excludeDescription = [\"error.invalid.msisdn.not.active\", 'error.invalid.isdn.empty', \"error.invalid.isdn.not.format\", \"error.invalid.rating.empty\", \"error.invalid.rating.not.format\"];\r\n        response.forEach(el => {\r\n            if (!excludeDescription.includes(el.description)) {\r\n                if (parseInt(el.type) == 0) {\r\n                    if (el.msisdn != null && el.msisdn != \"\" && /^(\\+?84)[1-9][0-9]{8,9}$/.test(el.msisdn)) {\r\n                        if ((el.description || \"\") != \"\") {\r\n                            if (el.description in me.mapImsiError) {\r\n                                me.mapImsiError[el.description].push(el.msisdn);\r\n                            } else {\r\n                                me.mapImsiError[el.description] = [el.msisdn];\r\n                            }\r\n                        }\r\n                    }\r\n                } else if (parseInt(el.type) == 1) {\r\n                    if (el.ratingPlanName != null && el.ratingPlanName != \"\" && /^[a-zA-Z0-9\\-_]*$/.test(el.ratingPlanName)) {\r\n                        if ((el.description || \"\") != \"\") {\r\n                            if (el.description in me.mapPlanNameError) {\r\n                                me.mapPlanNameError[el.description].push(el.planName);\r\n                            } else {\r\n                                me.mapPlanNameError[el.description] = [el.planName];\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            } else {\r\n                el.description = me.tranService.translate(el.description)\r\n            }\r\n            el['keyForm'] = `keyForm${index++}`;\r\n            me.mapFormSimImports[el['keyForm']] = me.formBuilder.group(el);\r\n            me.mapFormSimImports[el['keyForm']].controls['msisdn'].setValidators([Validators.required, Validators.pattern('^(\\\\+?84)[1-9][0-9]{8,9}$')]);\r\n            me.mapFormSimImports[el['keyForm']].controls['msisdn'].updateValueAndValidity();\r\n            me.mapFormSimImports[el['keyForm']].controls['ratingPlanName'].setValidators([Validators.required, Validators.pattern('^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$')]);\r\n            me.mapFormSimImports[el['keyForm']].controls['ratingPlanName'].updateValueAndValidity();\r\n        });\r\n        me.rowFirstSimImport = 0;\r\n        me.pageSizeSimImport = 10;\r\n        me.simImportsOrigin = [...response];\r\n        me.simImports = me.simImportsOrigin.slice(0, 10);\r\n\r\n    }\r\n\r\n    registerForFile() {\r\n        if (!this.checkValidListImport()) return;\r\n        let me = this;\r\n        let data = {\r\n            listRegisterRatingPlan: this.simImportsOrigin,\r\n            fileName: \"\"\r\n        }\r\n        me.messageCommonService.onload();\r\n        this.ratingPlanService.uploadRegisterByList(data, (resApi) => {\r\n            me.excuteResponseRegisterFileOrList(resApi);\r\n        }, null, () => {\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    pagingResultRegisterGroup(page, size, sort, params) {\r\n        this.dataSetResultRegisterGroup = {\r\n            content: this.dataResultRegisterGroupOrigin.slice(page * size, page * size + size),\r\n            total: this.dataResultRegisterGroupOrigin.length\r\n        }\r\n    }\r\n\r\n    pagingResultSimImport(event) {\r\n        let first = event.first;\r\n        let size = event.rows;\r\n        this.rowFirstSimImport = first;\r\n        this.pageSizeSimImport = size;\r\n        this.simImports = this.simImportsOrigin.slice(first, first + size);\r\n    }\r\n\r\n    getDetailSim() {\r\n        let me = this;\r\n        this.messageCommonService.onload();\r\n        me.simService.getById(me.simId, (response) => {\r\n            me.detailSim = {\r\n                ...response\r\n            }\r\n            me.getStatusSim();\r\n            me.getDetailCustomer();\r\n            me.getDetailRatingPlan();\r\n            me.getDetailContract();\r\n            me.getDetailApn();\r\n            me.simService.getConnectionStatus([me.simId], (resp) => {\r\n                me.detailSim.connectionStatus = resp[0]?.userstate\r\n            }, () => {\r\n            })\r\n        }, null, () => {\r\n            this.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    getStatusSim() {\r\n        let me = this;\r\n        this.simService.getDetailStatus(this.detailSim.msisdn, (response) => {\r\n            me.detailStatusSim = {\r\n                statusData: response.gprsStatus == 1,\r\n                statusReceiveCall: response.icStatus == 1,\r\n                statusSendCall: response.ocStatus == 1,\r\n                statusWorldCall: response.iddStatus == 1,\r\n                statusReceiveSms: response.smtStatus == 1,\r\n                statusSendSms: response.smoStatus == 1\r\n            };\r\n        }, () => {\r\n        })\r\n    }\r\n\r\n    getDetailCustomer() {\r\n        this.detailCustomer = {\r\n            name: this.detailSim.customerName,\r\n            code: this.detailSim.customerCode\r\n        }\r\n    }\r\n\r\n    getDetailRatingPlan() {\r\n        this.simService.getDetailPlanSim(this.detailSim.msisdn, (response) => {\r\n            this.detailRatingPlan = {\r\n                ...response\r\n            }\r\n        }, () => {\r\n        })\r\n\r\n    }\r\n\r\n    getDetailContract() {\r\n        this.simService.getDetailContract(this.utilService.stringToStrBase64(this.detailSim.contractCode), (response) => {\r\n            this.detailContract = response;\r\n        }, () => {\r\n        })\r\n    }\r\n\r\n    getDetailApn() {\r\n        this.detailAPN = {\r\n            code: this.detailSim.apnCode,\r\n            type: \"Kết nối bằng 3G\",\r\n            ip: 0,\r\n            rangeIp: this.detailSim.ip\r\n        }\r\n    }\r\n\r\n    getNameStatus(value) {\r\n        if (value == 0) {\r\n            return this.tranService.translate(\"sim.status.inventory\");\r\n        } else if (value == CONSTANTS.SIM_STATUS.READY) {\r\n            // return this.tranService.translate(\"sim.status.ready\");\r\n            return this.tranService.translate(\"sim.status.activated\");\r\n        } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\r\n            return this.tranService.translate(\"sim.status.activated\");\r\n        } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\r\n            return this.tranService.translate(\"sim.status.deactivated\");\r\n        } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\r\n            return this.tranService.translate(\"sim.status.purged\");\r\n        } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\r\n            return this.tranService.translate(\"sim.status.inactivated\");\r\n        } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\r\n            return this.tranService.translate(\"sim.status.processingChangePlan\");\r\n        } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\r\n            return this.tranService.translate(\"sim.status.processingRegisterPlan\");\r\n        } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\r\n            return this.tranService.translate(\"sim.status.waitingCancelPlan\");\r\n        }\r\n        return \"\";\r\n    }\r\n\r\n    getClassStatus(value) {\r\n        if (value == 0) {\r\n            return ['p-1', \"border-round\", \"border-400\", \"text-color\", \"inline-block\"];\r\n        } else if (value == CONSTANTS.SIM_STATUS.READY) {\r\n            // return ['p-1', \"bg-blue-600\", \"border-round\",\"inline-block\"];\r\n            return ['p-2', \"text-green-800\", \"bg-green-100\", \"border-round\", \"inline-block\"];\r\n        } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {\r\n            return ['p-2', 'text-green-800', \"bg-green-100\", \"border-round\", \"inline-block\"];\r\n        } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {\r\n            return ['p-2', 'text-yellow-800', \"bg-yellow-100\", \"border-round\", \"inline-block\"];\r\n        } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {\r\n            return ['p-2', 'text-indigo-600', \"bg-indigo-100\", \"border-round\", \"inline-block\"];\r\n        } else if (value == CONSTANTS.SIM_STATUS.PURGED) {\r\n            return ['p-2', 'text-red-700', \"bg-red-100\", \"border-round\", \"inline-block\"];\r\n        } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {\r\n            return ['p-2', 'text-cyan-800', \"bg-cyan-100\", \"border-round\", \"inline-block\"];\r\n        } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {\r\n            return ['p-2', 'text-teal-800', \"bg-teal-100\", \"border-round\", \"inline-block\"];\r\n        } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {\r\n            return ['p-2', 'text-orange-700', \"bg-orange-100\", \"border-round\", \"inline-block\"];\r\n        }\r\n        return [];\r\n    };\r\n\r\n    getServiceType(value) {\r\n        if (value == CONSTANTS.SERVICE_TYPE.PREPAID) return this.tranService.translate(\"sim.serviceType.prepaid\")\r\n        else if (value == CONSTANTS.SERVICE_TYPE.POSTPAID) return this.tranService.translate(\"sim.serviceType.postpaid\")\r\n        else return \"\"\r\n    };\r\n\r\n    getDetailPLan() {\r\n        let me = this;\r\n        this.messageCommonService.onload();\r\n        me.ratingPlanService.getById(Number(me.planId), (response) => {\r\n            me.response = response\r\n\r\n            me.ratingPlanInfo.id = response.id\r\n            me.ratingPlanInfo.code = response.code\r\n            me.ratingPlanInfo.name = response.name\r\n            me.ratingPlanInfo.status = response.status\r\n            me.ratingPlanInfo.dispatchCode = response.dispatchCode\r\n            me.ratingPlanInfo.customerType = response.customerType\r\n            me.ratingPlanInfo.subscriptionFee = response.subscriptionFee\r\n            me.ratingPlanInfo.subscriptionType = response.paidType\r\n            me.ratingPlanInfo.ratingScope = response.ratingScope\r\n            me.ratingPlanInfo.cycleTimeUnit = response.cycleTimeUnit\r\n            me.ratingPlanInfo.cycleInterval = response.cycleInterval\r\n            me.ratingPlanInfo.reload = response.reload\r\n            me.ratingPlanInfo.flat = response.flat\r\n            me.ratingPlanInfo.limitDataUsage = response.limitDataUsage\r\n            me.ratingPlanInfo.limitSmsOutside = response.limitSmsOutside\r\n            me.ratingPlanInfo.limitSmsInside = response.limitSmsInside\r\n            me.ratingPlanInfo.flexible = response.flexible\r\n            me.ratingPlanInfo.feePerDataUnit = response.feePerDataUnit\r\n            me.ratingPlanInfo.squeezedSpeed = response.squeezedSpeed\r\n            me.ratingPlanInfo.feeSmsInside = response.feeSmsInside\r\n            me.ratingPlanInfo.feeSmsOutside = response.feeSmsOutside\r\n            me.ratingPlanInfo.maximumFee = response.maximumFee\r\n            me.ratingPlanInfo.dataRoundUnit = response.dataRoundUnit\r\n            me.ratingPlanInfo.downSpeed = response.downSpeed\r\n            me.ratingPlanInfo.provinceCode = response.provinceCode\r\n            me.ratingPlanInfo.description = response.description;\r\n\r\n            me.getReload(me.ratingPlanInfo.reload)\r\n            me.getFlexible(me.ratingPlanInfo.flexible)\r\n            me.myProvices = \"\"\r\n\r\n            me.accountService.getListProvince((data) => {\r\n                me.provinces = data.map(el => {\r\n                    return {\r\n                        code: el.code,\r\n                        name: `${el.name}`\r\n                    }\r\n                })\r\n                me.provinces.forEach(el => {\r\n                    if (me.ratingPlanInfo.provinceCode.includes(el.code)) {\r\n                        me.myProvices += `${el.name}, `;\r\n\r\n                    }\r\n                })\r\n                if (me.myProvices.length > 0) {\r\n                    me.myProvices = me.myProvices.substring(0, me.myProvices.length - 2);\r\n                }\r\n            })\r\n\r\n        }, null, () => {\r\n            me.messageCommonService.offload();\r\n        })\r\n    };\r\n\r\n    getReload(value) {\r\n        if (value == CONSTANTS.RELOAD.YES) {\r\n            return this.checkedReload = true;\r\n        } else if (value == CONSTANTS.RELOAD.NO) {\r\n            return this.checkedReload = false\r\n        }\r\n        return \"\";\r\n    }\r\n\r\n    getFlexible(value) {\r\n        if (value == CONSTANTS.FLEXIBLE.YES) {\r\n            return this.checkedFlexible = true;\r\n        } else if (value == CONSTANTS.FLEXIBLE.NO) {\r\n            return this.checkedFlexible = false\r\n        }\r\n        return \"\";\r\n    };\r\n\r\n    getNameCustomerType(value) {\r\n        if (value == CONSTANTS.CUSTOMER_TYPE.PERSONAL) {\r\n            return this.tranService.translate(\"ratingPlan.customerType.personal\");\r\n        } else if (value == CONSTANTS.CUSTOMER_TYPE.INTERPRISE) {\r\n            return this.tranService.translate(\"ratingPlan.customerType.enterprise\");\r\n        } else if (value == CONSTANTS.CUSTOMER_TYPE.AGENCY) {\r\n            return this.tranService.translate(\"ratingPlan.customerType.agency\");\r\n        }\r\n        return \"\";\r\n    };\r\n\r\n    checkSubscriptionType() {\r\n        this.subscriptionTypes = [{\r\n            type: this.tranService.translate(\"ratingPlan.subscriptionType.post\"),\r\n            ip: CONSTANTS.SUBSCRIPTION_TYPE.POSTPAID,\r\n        },\r\n            {\r\n                type: this.tranService.translate(\"ratingPlan.subscriptionType.pre\"),\r\n                ip: CONSTANTS.SUBSCRIPTION_TYPE.PREPAID,\r\n            }]\r\n    };\r\n\r\n    getCycleTimeUnit(value) {\r\n        if (value == CONSTANTS.CYCLE_TIME_UNITS.DAY) {\r\n            return this.tranService.translate(\"ratingPlan.cycle.day\");\r\n        } else if (value == CONSTANTS.CYCLE_TIME_UNITS.MONTH) {\r\n            return this.tranService.translate(\"ratingPlan.cycle.month\");\r\n        }\r\n        return \"\";\r\n    };\r\n\r\n    getRatingScope(value) {\r\n        if (value == CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE) {\r\n            return this.tranService.translate(\"ratingPlan.ratingScope.nativeWide\");\r\n        } else if (value == CONSTANTS.RATING_PLAN_SCOPE.CUSTOMER) {\r\n            return this.tranService.translate(\"ratingPlan.ratingScope.customer\");\r\n        }\r\n        return \"\";\r\n    };\r\n\r\n\r\n    protected readonly CONSTANTS = CONSTANTS;\r\n}\r\n", "<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{tranService.translate(\"global.menu.registerplan\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"col-5 flex flex-row justify-content-end align-items-center responsive-container\">\r\n        <p-splitButton *ngIf=\"checkAuthen([allPermissions.RATING_PLAN_SIM.REGISTER_BY_FILE,allPermissions.RATING_PLAN_SIM.REGISTER_BY_LIST])\" styleClass=\"mr-2 p-button-success equal-button-with-margin\" [label]=\"tranService.translate('global.button.registerRatingPlan')\" [model]=\"itemRegisters\"></p-splitButton>\r\n        <p-button styleClass=\"p-button-info equal-button-with-margin\" [label]=\"tranService.translate('global.button.historyRegisterPlan')\" *ngIf=\"checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_HISTORY])\" icon=\"\" [routerLink]=\"['/plans/registers/history']\" routerLinkActive=\"router-link-active\" ></p-button>\r\n    </div>\r\n</div>\r\n\r\n<form [formGroup]=\"formSearch\" (ngSubmit)=\"onSubmitSearch()\" class=\"pb-2 pt-3 vnpt-field-set\">\r\n    <p-panel [toggleable]=\"true\" [header]=\"tranService.translate('global.text.filter')\">\r\n        <div class=\"grid search-grid-3\">\r\n            <!-- So thue bao -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input pInputText\r\n                           class=\"w-full\"\r\n                           pInputText id=\"msisdn\"\r\n                           [(ngModel)]=\"searchInfo.msisdn\"\r\n                           formControlName=\"msisdn\"\r\n                    />\r\n                    <label htmlFor=\"msisdn\">{{tranService.translate(\"sim.label.sothuebao\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- IMEI Thiet bi -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input pInputText\r\n                            class=\"w-full\"\r\n                            pInputText id=\"imei\"\r\n                            [(ngModel)]=\"searchInfo.imei\"\r\n                            formControlName=\"imei\"\r\n                    />\r\n                    <label htmlFor=\"imei\">{{tranService.translate(\"sim.label.imeiDevice\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- goi cuoc -->\r\n            <div class=\"col-3\">\r\n                <div class=\"relative\">\r\n                    <vnpt-select\r\n                        class=\"w-full\"\r\n                        [(value)]=\"searchInfo.ratingPlanId\"\r\n                        [placeholder]=\"tranService.translate('sim.label.goicuoc')\"\r\n                        objectKey=\"dropdownListSim\"\r\n                        paramKey=\"name\"\r\n                        keyReturn=\"id\"\r\n                        displayPattern=\"${name} - ${code}\"\r\n                        typeValue=\"primitive\"\r\n                        [paramDefault]=\"{type: 'ratingPlan'}\"\r\n                        [isMultiChoice]=\"false\"\r\n                        [floatLabel]=\"true\"\r\n                    ></vnpt-select>\r\n                </div>\r\n            </div>\r\n            <!-- Trang thai -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\" [showClear]=\"true\"\r\n                                id=\"status\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"searchInfo.status\"\r\n                                formControlName=\"status\"\r\n                                [options]=\"listStatus\"\r\n                                optionLabel=\"name\"\r\n                                optionValue=\"value\"\r\n                    ></p-dropdown>\r\n                    <label class=\"label-dropdown\" for=\"status\">{{tranService.translate(\"ratingPlan.label.status\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- khach hang -->\r\n            <div class=\"col-3\">\r\n                <div class=\"relative\">\r\n                    <vnpt-select\r\n                        class=\"w-full\"\r\n                        [(value)]=\"searchInfo.customer\"\r\n                        [placeholder]=\"tranService.translate('sim.label.khachhang')\"\r\n                        objectKey=\"dropdownListSim\"\r\n                        paramKey=\"name\"\r\n                        keyReturn=\"customerCode\"\r\n                        displayPattern=\"${customerName} - ${customerCode}\"\r\n                        typeValue=\"primitive\"\r\n                        [paramDefault]=\"{type: 'customer'}\"\r\n                        [isMultiChoice]=\"false\"\r\n                        [floatLabel]=\"true\"\r\n                    ></vnpt-select>\r\n                </div>\r\n            </div>\r\n            <!-- ma hop dong -->\r\n            <div class=\"col-3\">\r\n                <div class=\"relative\">\r\n                    <vnpt-select\r\n                        class=\"w-full\"\r\n                        [(value)]=\"searchInfo.contractCode\"\r\n                        [placeholder]=\"tranService.translate('sim.label.mahopdong')\"\r\n                        objectKey=\"dropdownListSim\"\r\n                        paramKey=\"name\"\r\n                        keyReturn=\"contractCode\"\r\n                        displayPattern=\"${contractCode}\"\r\n                        typeValue=\"primitive\"\r\n                        [paramDefault]=\"{type: 'contract'}\"\r\n                        [floatLabel]=\"true\"\r\n                        [isMultiChoice]=\"false\"\r\n                    ></vnpt-select>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-3 pb-0 date-filter\">\r\n                <span class=\"p-float-label\">\r\n                    <p-calendar styleClass=\"w-full\"\r\n                            id=\"dateFrom\"\r\n                            [(ngModel)]=\"searchInfo.dateFrom\"\r\n                            formControlName=\"dateFrom\"\r\n                            [showIcon]=\"true\"\r\n                            [showClear]=\"true\"\r\n                            dateFormat=\"dd/mm/yy\"\r\n                            [maxDate]=\"maxDateFrom\"\r\n                            (onSelect)=\"onChangeDateFrom(searchInfo.dateFrom)\"\r\n                            (onInput)=\"onChangeDateFrom(searchInfo.dateFrom)\"\r\n                    ></p-calendar>\r\n                    <label class=\"label-calendar\" htmlFor=\"dateFrom\">{{tranService.translate(\"sim.label.ngaylamhopdongtu\")}}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-3 pb-0 date-filter\" style=\"width: calc(25% - 70px)\">\r\n                <span class=\"p-float-label\">\r\n                    <p-calendar styleClass=\"w-full\"\r\n                            id=\"dateTo\"\r\n                            [(ngModel)]=\"searchInfo.dateTo\"\r\n                            formControlName=\"dateTo\"\r\n                            [showIcon]=\"true\"\r\n                            [showClear]=\"true\"\r\n                            dateFormat=\"dd/mm/yy\"\r\n                            [minDate]=\"minDateTo\"\r\n                            [maxDate]=\"maxDateTo\"\r\n                            (onSelect)=\"onChangeDateTo(searchInfo.dateTo)\"\r\n                            (onInput)=\"onChangeDateTo(searchInfo.dateTo)\"\r\n                    />\r\n                    <label class=\"label-calendar\" htmlFor=\"dateTo\">{{tranService.translate(\"sim.label.ngaylamhopdongden\")}}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col pb-0\" style=\"width: 5% !important;\">\r\n                <p-button icon=\"pi pi-search\"\r\n                          styleClass=\"p-button-rounded p-button-secondary p-button-text button-search\"\r\n                          type=\"submit\"\r\n                ></p-button>\r\n            </div>\r\n        </div>\r\n    </p-panel>\r\n</form>\r\n\r\n<div class=\"flex justify-content-center dialog-vnpt\">\r\n    <p-dialog [header]=\"tranService.translate('sim.text.detailSim')\" [(visible)]=\"isShowModalDetailSim\" [modal]=\"true\" [style]=\"{ width: '980px' }\" [draggable]=\"false\" [resizable]=\"false\" *ngIf=\"isShowModalDetailSim\">\r\n        <div class=\"grid grid-1 mt-1 h-auto\" style=\"width: calc(100% + 16px);\">\r\n            <div class=\"col sim-detail pr-0\">\r\n                <p-card [header]=\"tranService.translate('sim.text.simInfo')\">\r\n                    <div class=\"flex flex-row justify-content-between custom-card\">\r\n                        <div class=\"w-6\">\r\n                            <div class=\"grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.sothuebao\")}}</span>\r\n                                <span class=\"col\">{{detailSim.msisdn}}</span>\r\n                            </div>\r\n                            <div class=\"mt-1 grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.trangthaisim\")}}</span>\r\n                                <span class=\"w-auto ml-3\" [class]=\"getClassStatus(detailSim.status)\">{{getNameStatus(detailSim.status)}}</span>\r\n                            </div>\r\n                            <div class=\"mt-1 grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.imsi\")}}</span>\r\n                                <span class=\"col\">{{detailSim.imsi}}</span>\r\n                            </div>\r\n                            <div class=\"mt-1 grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.imeiDevice\")}}</span>\r\n                                <span class=\"col\">{{detailSim.imei}}</span>\r\n                            </div>\r\n                            <div class=\"mt-1 grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.maapn\")}}</span>\r\n                                <span class=\"col\">{{detailSim.apnId}}</span>\r\n                            </div>\r\n                            <div class=\"mt-1 grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.trangthaiketnoi\")}}</span>\r\n                                <span *ngIf=\"detailSim.connectionStatus!==undefined && detailSim.connectionStatus!==null && detailSim.connectionStatus!=='0' \" class=\"ml-3 p-2 text-green-800 bg-green-100 border-round inline-block\">ON</span>\r\n                                <span *ngIf=\"detailSim.connectionStatus==='0'\" class=\"ml-3 p-2 text-50 surface-500 border-round inline-block\">OFF</span>\r\n                                <span *ngIf=\"detailSim.connectionStatus===undefined || detailSim.connectionStatus=== null \" class=\"ml-3 p-2 text-50 surface-500 border-round inline-block\">NOT FOUND</span>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"w-6\">\r\n                            <div class=\"grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.startDate\")}}</span>\r\n                                <span class=\"col\">{{detailSim.startDate | date:'dd/MM/yyyy'}}</span>\r\n                            </div>\r\n                            <div class=\"mt-1 grid\">\r\n                                <span style=\"min-width: 150px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.serviceType\")}}</span>\r\n                                <span class=\"w-auto ml-3\">{{getServiceType(detailSim.serviceType)}}</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </p-card>\r\n                <p-card [header]=\"tranService.translate('sim.text.simStatusInfo')\" styleClass=\"mt-3 sim-status\">\r\n                    <div class=\"grid\">\r\n                        <div class=\"col-4 text-center\">\r\n                            <p-toggleButton [(ngModel)]=\"detailStatusSim.statusData\" [disabled]=\"true\" onLabel=\"ON\" offLabel=\"OFF\"></p-toggleButton>\r\n                            <div>{{tranService.translate(\"sim.status.service.data\")}}</div>\r\n                        </div>\r\n                        <div class=\"col-4 text-center\">\r\n                            <p-toggleButton [(ngModel)]=\"detailStatusSim.statusReceiveCall\" [disabled]=\"true\" onLabel=\"ON\" offLabel=\"OFF\"></p-toggleButton>\r\n                            <div>{{tranService.translate(\"sim.status.service.callReceived\")}}</div>\r\n                        </div>\r\n                        <div class=\"col-4 text-center\">\r\n                            <p-toggleButton [(ngModel)]=\"detailStatusSim.statusSendCall\" [disabled]=\"true\" onLabel=\"ON\" offLabel=\"OFF\"></p-toggleButton>\r\n                            <div>{{tranService.translate(\"sim.status.service.callSent\")}}</div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"grid\">\r\n                        <div class=\"col-4 text-center\">\r\n                            <p-toggleButton [(ngModel)]=\"detailStatusSim.statusWorldCall\" [disabled]=\"true\" onLabel=\"ON\" offLabel=\"OFF\"></p-toggleButton>\r\n                            <div>{{tranService.translate(\"sim.status.service.callWorld\")}}</div>\r\n                        </div>\r\n                        <div class=\"col-4 text-center\">\r\n                            <p-toggleButton [(ngModel)]=\"detailStatusSim.statusReceiveSms\" [disabled]=\"true\" onLabel=\"ON\" offLabel=\"OFF\"></p-toggleButton>\r\n                            <div>{{tranService.translate(\"sim.status.service.smsReceived\")}}</div>\r\n                        </div>\r\n                        <div class=\"col-4 text-center\">\r\n                            <p-toggleButton [(ngModel)]=\"detailStatusSim.statusSendSms\" [disabled]=\"true\" onLabel=\"ON\" offLabel=\"OFF\"></p-toggleButton>\r\n                            <div>{{tranService.translate(\"sim.status.service.smsSent\")}}</div>\r\n                        </div>\r\n                    </div>\r\n                </p-card>\r\n                <!-- goi cuoc -->\r\n                <p-card [header]=\"tranService.translate('sim.text.ratingPlanInfo')\" styleClass=\"mt-3\">\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.tengoicuoc\")}}</span>\r\n                        <span class=\"col\">{{detailSim.ratingPlanName}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.dataUseInMonth\")}}</span>\r\n                        <span class=\"col\">{{this.utilService.bytesToMegabytes(detailRatingPlan?.dataUseInMonth) | number }} {{detailRatingPlan?.unit?detailRatingPlan.unit:\"MB\"}}</span>\r\n                    </div>\r\n                </p-card>\r\n            </div>\r\n            <div class=\"col sim-detail pr-0\">\r\n                <!-- hop dong -->\r\n                <p-card [header]=\"tranService.translate('sim.text.contractInfo')\">\r\n                    <div class=\"grid mt-0\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.mahopdong\")}}</span>\r\n                        <span class=\"col\">{{detailContract.contractCode}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.ngaylamhopdong\")}}</span>\r\n                        <span class=\"col\">{{detailContract.contractDate | date:'dd/MM/yyyy'}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.nguoilamhopdong\")}}</span>\r\n                        <span class=\"col uppercase\">{{detailContract.contractorInfo}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.matrungtam\")}}</span>\r\n                        <span class=\"col\">{{detailContract.centerCode}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.dienthoailienhe\")}}</span>\r\n                        <span class=\"col\">{{detailContract.contactPhone}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.diachilienhe\")}}</span>\r\n                        <span class=\"col\">{{detailContract.contactAddress}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.paymentName\")}}</span>\r\n                        <span class=\"col uppercase\">{{detailContract.paymentName}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.paymentAddress\")}}</span>\r\n                        <span class=\"col\">{{detailContract.paymentAddress}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.routeCode\")}}</span>\r\n                        <span class=\"col\">{{detailContract.routeCode}}</span>\r\n                    </div>\r\n                </p-card>\r\n                <!-- customer -->\r\n                <p-card [header]=\"tranService.translate('sim.text.customerInfo')\" styleClass=\"mt-3\">\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.khachhang\")}}</span>\r\n                        <span class=\"col\">{{detailCustomer.name}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"sim.label.customerCode\")}}</span>\r\n                        <span class=\"col\">{{detailCustomer.code}}</span>\r\n                    </div>\r\n                </p-card>\r\n            </div>\r\n        </div>\r\n    </p-dialog>\r\n</div>\r\n\r\n<div class=\"flex justify-content-center dialog-vnpt\">\r\n    <p-dialog [header]=\"tranService.translate('global.menu.detailplan')\" [(visible)]=\"isShowModalDetailPlan\" [modal]=\"true\" [style]=\"{ width: '980px' }\" [draggable]=\"false\" [resizable]=\"false\" *ngIf=\"isShowModalDetailPlan\">\r\n        <p-card  styleClass=\"h-full\" [style]=\"{'width': 'calc(100% + 16px)'}\">\r\n            <div class=\"col ratingPlan-detail custom-rating-detail pr-0 flex\" style=\"border:1px solid black; margin-bottom: 20px\">\r\n                <div class=\"flex-1\">\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.planCode\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.code}}</span>\r\n                    </div>\r\n                    <div class=\" grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.planName\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.name}}</span>\r\n                    </div>\r\n                    <div class=\" grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.status\")}}</span>\r\n                        <div  class=\"text-white w-auto col\">\r\n                            <span [class]=\"getClassStatus(ratingPlanInfo.status)\">{{getNameStatus(ratingPlanInfo.status)}}</span>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\" grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.dispatchCode\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.dispatchCode}}</span>\r\n                    </div>\r\n                    <div class=\" grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.customerType\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{getNameCustomerType(ratingPlanInfo.customerType)}}</span>\r\n                    </div>\r\n                    <div class=\" grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.description\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.description}}</span>\r\n                    </div>\r\n                    <!--                    <div class=\"mt-1 grid\">-->\r\n                    <!--                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.motachuaco\")}}</span>-->\r\n                    <!--                    </div>-->\r\n                </div>\r\n                <div class=\"flex-1\">\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.subscriptionFee\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.subscriptionFee}}&nbsp; &nbsp; &nbsp; &nbsp; {{tranService.translate(\"ratingPlan.text.textDong\")}}&nbsp;{{tranService.translate(\"ratingPlan.text.vat\")}}</span>\r\n                    </div>\r\n                    <div class=\"grid\">\r\n                        <span class=\"inline-block col-fixed\">\r\n                            <span>\r\n                                <p-radioButton [disabled]=\"true\" [value]=\"subscriptionTypes[0].ip\" [(ngModel)]=\"ratingPlanInfo.subscriptionType\" inputId=\"typeIp1\"></p-radioButton>\r\n                                &nbsp;\r\n                                <span>{{tranService.translate(\"ratingPlan.subscriptionType.post\")}}</span>\r\n                            </span>\r\n                        </span>\r\n                        <span class=\"inline-block col-fixed radioButton2\" style=\"padding-left: 108px\">\r\n                            <span>\r\n                                <p-radioButton [disabled]=\"true\" [value]=\"subscriptionTypes[1].ip\" [(ngModel)]=\"ratingPlanInfo.subscriptionType\" inputId=\"typeIp2\"></p-radioButton>\r\n                                &nbsp;\r\n                                <span>{{tranService.translate(\"ratingPlan.subscriptionType.pre\")}}</span>\r\n                            </span>\r\n                        </span>\r\n                    </div>\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.ratingScope\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{getRatingScope(ratingPlanInfo.ratingScope)}}</span>\r\n                    </div>\r\n                    <div class=\"grid\" *ngIf=\"ratingPlanInfo.ratingScope == planScopes.CUSTOMER\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.province\")}}</span>\r\n                        <span class=\"inline-block col-fixed\" style=\"width: fit-content !important;\">{{myProvices}}</span>\r\n                    </div>\r\n\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.cycle\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{getCycleTimeUnit(ratingPlanInfo.cycleTimeUnit)}}</span>\r\n                    </div>\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.cycleInterval\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.cycleInterval}}</span>\r\n                    </div>\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.reload\")}}</span>\r\n                        <div style=\"padding-top: 10px; padding-left: 13px\">\r\n                            <p-inputSwitch [(ngModel)]=\"checkedReload\" [disabled]=\"true\"></p-inputSwitch>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"mt-1 grid\">\r\n                <span style=\"font-size: 20px;margin-left: 10px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.flat\")}}</span>\r\n            </div>\r\n            <div class=\"col ratingPlan-detail custom-rating-detail-limit pr-0 flex\"  style=\"border:1px solid black; margin-bottom: 20px\" id = \"name\">\r\n                <div class=\"flex-1\">\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.limitDataUsage\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.limitDataUsage}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.limitSmsOutside\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.limitSmsOutside}}</span>\r\n                    </div>\r\n                </div>\r\n                <div class=\"flex-1\">\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.limitSmsInside\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.limitSmsInside}}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"mt-1 grid\">\r\n                <span style=\"font-size: 20px;margin-left: 10px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.flexible\")}}</span>\r\n                <div style=\"padding-top: 18px\">\r\n                    <p-inputSwitch [(ngModel)]=\"checkedFlexible\" [disabled]=\"true\"></p-inputSwitch>\r\n                </div>\r\n            </div>\r\n            <div class=\"col ratingPlan-detail pr-0 flex\"  style=\"border:1px solid black;\" id = \"flexible\">\r\n                <div class=\"flex-1\">\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.feePerDataUnit\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.feePerDataUnit}}</span>\r\n                    </div>\r\n                    <div class=\" grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.squeezedSpeed\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.downSpeed}}</span>\r\n                    </div>\r\n                    <div class=\" grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.feeSmsInside\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.feeSmsInside}}</span>\r\n                    </div>\r\n                    <div class=\" grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.feeSmsOutside\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.feeSmsOutside}}</span>\r\n                    </div>\r\n                    <div class=\" grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.maximumFee\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.maximumFee}}</span>\r\n                    </div>\r\n                </div>\r\n                <div class=\"flex-1\">\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">/&nbsp; &nbsp; &nbsp; {{ratingPlanInfo.dataRoundUnit}} &nbsp; &nbsp; &nbsp; &nbsp;   KB</span>\r\n                    </div>\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">/&nbsp; &nbsp; &nbsp; {{ratingPlanInfo.squeezedSpeed}}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </p-card>\r\n    </p-dialog>\r\n</div>\r\n<table-vnpt\r\n    [fieldId]=\"'id'\"\r\n    [(selectItems)]=\"selectItems\"\r\n    [columns]=\"columns\"\r\n    [dataSet]=\"dataSet\"\r\n    [options]=\"optionTable\"\r\n    [loadData]=\"search.bind(this)\"\r\n    [pageNumber]=\"pageNumber\"\r\n    [pageSize]=\"pageSize\"\r\n    [sort]=\"sort\"\r\n    [params]=\"searchInfo\"\r\n    [labelTable]=\"tranService.translate('global.menu.registerplan')\"\r\n></table-vnpt>\r\n\r\n<!-- dialog register for sim -->\r\n<div class=\"flex justify-content-center dialog-push-group\">\r\n    <p-dialog [header]=\"headerDialogRegisterForSim\" [(visible)]=\"isShowDialogRegisterSim\" [modal]=\"true\" [style]=\"{ width: '500px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n        <div class=\"w-full field grid\">\r\n            <label htmlFor=\"planSelected\" class=\"col-fixed\" style=\"width:100px\">{{tranService.translate(\"sim.label.goicuoc\")}}</label>\r\n            <div class=\"col\">\r\n                <p-dropdown styleClass=\"w-full\"\r\n                        [showClear]=\"true\"\r\n                        [autoDisplayFirst]=\"false\"\r\n                        [(ngModel)]=\"planSelected\"\r\n                        [options]=\"listRatingPlanOrigin\"\r\n                        optionLabel=\"display\"\r\n                        [filter]=\"true\" filterBy=\"name\"\r\n                        optionValue=\"id\"\r\n                        [placeholder]=\"tranService.translate('sim.text.selectRatingPlan')\"\r\n                ></p-dropdown>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex flex-row justify-content-center align-items-center\">\r\n            <p-button styleClass=\"mr-2 p-button-secondary p-button-outlined\" [label]=\"tranService.translate('global.button.cancel')\" (click)=\"isShowDialogRegisterSim = false\"></p-button>\r\n            <p-button styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.save')\" (click)=\"registerForSim()\" [disabled]=\"planSelected == null || planSelected == undefined\"></p-button>\r\n        </div>\r\n    </p-dialog>\r\n</div>\r\n\r\n<!-- dialog register for group sim -->\r\n<div class=\"flex justify-content-center dialog-push-group\">\r\n    <p-dialog [header]=\"tranService.translate('global.button.registerPlanForGroup')\" [(visible)]=\"isShowDialogRegisterGroupSim\" [modal]=\"true\" [style]=\"{ width: '500px' }\" [draggable]=\"false\" [resizable]=\"false\" styleClass=\"dialog-upload-device\">\r\n        <div class=\"w-full field grid dialog-grid\">\r\n            <label htmlFor=\"groupSim\" class=\"col-fixed\" style=\"width:100px\">{{tranService.translate(\"sim.label.nhomsim\")}}</label>\r\n            <div class=\"col input-div\" style=\"max-width: calc(100% - 100px)\">\r\n                <vnpt-select\r\n                    class=\"w-full\"\r\n                    [(value)]=\"groupSimSelected\"\r\n                    [placeholder]=\"tranService.translate('sim.text.selectGroupSim')\"\r\n                    objectKey=\"groupSim\"\r\n                    paramKey=\"name\"\r\n                    keyReturn=\"id\"\r\n                    displayPattern=\"${name} - ${groupKey}\"\r\n                    typeValue=\"primitive\"\r\n                    [isMultiChoice]=\"false\"\r\n                ></vnpt-select>\r\n            </div>\r\n        </div>\r\n        <div class=\"w-full field grid dialog-grid\">\r\n            <label htmlFor=\"planSelected\" class=\"col-fixed\" style=\"width:100px\">{{tranService.translate(\"sim.label.goicuoc\")}}</label>\r\n            <div class=\"col input-div\" style=\"max-width: calc(100% - 100px)\">\r\n                <p-dropdown styleClass=\"w-full\"\r\n                        [showClear]=\"true\"\r\n                        [autoDisplayFirst]=\"false\"\r\n                        [(ngModel)]=\"planGroupSelected\"\r\n                        [options]=\"listRatingPlan\"\r\n                        optionLabel=\"display\"\r\n                        [filter]=\"true\" filterBy=\"name\"\r\n                        optionValue=\"id\"\r\n                        [placeholder]=\"tranService.translate('sim.text.selectRatingPlan')\"\r\n                ></p-dropdown>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex flex-row justify-content-center align-items-center\">\r\n            <p-button styleClass=\"mr-2 p-button-secondary p-button-outlined\" [label]=\"tranService.translate('global.button.cancel')\" (click)=\"isShowDialogRegisterGroupSim = false\"></p-button>\r\n            <p-button styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.save')\" (click)=\"registerForGroupSim()\" [disabled]=\"planGroupSelected == null || planGroupSelected == undefined || groupSimSelected == null || groupSimSelected == undefined\"></p-button>\r\n        </div>\r\n    </p-dialog>\r\n</div>\r\n\r\n\r\n<!-- dialog result register plan for group sim -->\r\n<!--<div class=\"flex justify-content-center dialog-push-group\">-->\r\n<!--    <p-dialog [header]=\"tranService.translate('global.text.resultRegister')\" [(visible)]=\"isShowDialogResultRegisterGroupSim\" [modal]=\"true\" [style]=\"{ width: '700px' }\" [draggable]=\"false\" [resizable]=\"false\">-->\r\n<!--        <div class=\"w-full field grid\">-->\r\n<!--            <label htmlFor=\"groupSim\" class=\"col-fixed col text-primary-500 font-bold white-space-normal\">{{tranService.translate(\"ratingPlan.text.textResultSuccess\", {total: totalResultRegisterGroup, success: totalSuccessResultRegisterGroup})}}</label>-->\r\n<!--        </div>-->\r\n<!--        <div class=\"w-full field grid\">-->\r\n<!--            <label class=\"col-fixed justify-content-end text-red-500\" style=\"width:70px\"><i class=\"pi pi-exclamation-triangle text-6xl\"></i></label>-->\r\n<!--            <label class=\"col\">{{tranService.translate(\"ratingPlan.text.textResultFail\", {total: totalResultRegisterGroup, fail: totalFailResultRegisterGroup})}}</label>-->\r\n<!--        </div>-->\r\n<!--        <table-vnpt-->\r\n<!--            [fieldId]=\"'msisdn'\"-->\r\n<!--            [pageNumber]=\"pageNumberResultRegister\"-->\r\n<!--            [pageSize]=\"pageSizeResultRegister\"-->\r\n<!--            [(selectItems)]=\"selectItems\"-->\r\n<!--            [columns]=\"columnsResultRegisterGroup\"-->\r\n<!--            [dataSet]=\"dataSetResultRegisterGroup\"-->\r\n<!--            [options]=\"optionTableResultRegisterGroup\"-->\r\n<!--            [loadData]=\"pagingResultRegisterGroup.bind(this)\"-->\r\n<!--            [rowsPerPageOptions]=\"[5,10,20]\"-->\r\n<!--            [scrollHeight]=\"'400px'\"-->\r\n<!--        ></table-vnpt>-->\r\n<!--        <div class=\"flex flex-row justify-content-center align-items-center\">-->\r\n<!--            <p-button styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.agree')\" (click)=\"isShowDialogResultRegisterGroupSim = false\"></p-button>-->\r\n<!--        </div>-->\r\n<!--    </p-dialog>-->\r\n<!--</div>-->\r\n\r\n<div class=\"flex justify-content-center dialog-push-group\">\r\n    <p-dialog [header]=\"tranService.translate('global.button.registerPlanForGroup')\" [(visible)]=\"isShowDialogResultRegisterGroupSim\" [modal]=\"true\" [style]=\"{ width: '1000px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n<!--        <div class=\"w-full field grid\">-->\r\n<!--            <div class=\"col-10 flex flex-row justify-content-start align-items-center\">-->\r\n<!--                <input-file-vnpt class=\"w-full\" [(fileObject)]=\"fileObject\" [clearFileCallback]=\"clearFileCallback.bind(this)\"-->\r\n<!--                                 [options]=\"optionInputFile\"-->\r\n<!--                ></input-file-vnpt>-->\r\n<!--            </div>-->\r\n<!--            <div class=\"col-2 flex flex-row justify-content-end align-items-center\">-->\r\n<!--                <p-button icon=\"pi pi-download\" [pTooltip]=\"tranService.translate('global.button.downloadTemp')\" styleClass=\"p-button-outlined p-button-secondary\" (click)=\"downloadTemplate()\"></p-button>-->\r\n<!--            </div>-->\r\n<!--        </div>-->\r\n        <div class=\"grid\"><div class=\"col pt-0\"><small class=\"text-red-500\" *ngIf=\"isShowErrorGroup\">{{messageErrorUpload}}</small></div></div>\r\n        <p-table\r\n            [paginator]=\"true\"\r\n            [rows]=\"pageSizeSimImport\"\r\n            [first]=\"rowFirstSimImport\"\r\n            [showCurrentPageReport]=\"true\"\r\n            [tableStyle]=\"{ 'min-width': '100%' }\"\r\n            [currentPageReportTemplate]=\"tranService.translate('global.text.templateTextPagination')\"\r\n            (onPage)=\"pagingResultSimImport($event)\"\r\n            [rowsPerPageOptions]=\"[5,10,20]\"\r\n            [styleClass]=\"'p-datatable-sm'\"\r\n            [totalRecords]=\"simImportsOrigin?.length\"\r\n            [lazy]=\"true\"\r\n            #dataTable\r\n            [scrollHeight]=\"'400px'\"\r\n            [value]=\"simImports\"\r\n            dataKey=\"id\"\r\n            [tableStyle]=\"{ 'min-width': '50rem' }\"\r\n            *ngIf=\"simImportsOrigin\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th style=\"min-width:150px;max-width:150px\">{{tranService.translate(\"sim.label.sothuebao\")}}</th>\r\n                    <th style=\"min-width:150px;max-width:150px\">{{tranService.translate(\"sim.label.tengoicuoc\")}}</th>\r\n                    <th style=\"min-width: 250px;max-width: 250px;\">{{tranService.translate(\"sim.label.description\")}}</th>\r\n                    <th style=\"min-width:70px;max-width:70px; text-align: center;\">{{tranService.translate(\"global.text.action\")}}</th>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"body\" let-simImport let-editing=\"editing\" let-i=\"rowIndex\">\r\n                <tr [formGroup]=\"mapFormSimImports[simImport.keyForm]\">\r\n                    <td style=\"min-width:150px;max-width:150px\" [pEditableColumn]=\"simImport.msisdn\" pEditableColumnField=\"msisdn\">\r\n                        <p-cellEditor>\r\n                            <ng-template pTemplate=\"input\">\r\n                                <input formControlName=\"msisdn\"\r\n                                       style=\"min-width:150px;max-width:150px\"\r\n                                       pInputText type=\"text\"\r\n                                       [(ngModel)]=\"simImport.msisdn\"\r\n                                       required\r\n                                       maxlength=\"20\"\r\n                                       pattern=\"^(\\+?84)[1-9][0-9]{8,9}$\"\r\n                                       (ngModelChange)=\"checkValueSimImportChange(simImport)\"\r\n                                />\r\n                            </ng-template>\r\n                            <ng-template pTemplate=\"output\">\r\n                                {{ simImport.msisdn }}\r\n                            </ng-template>\r\n                        </p-cellEditor>\r\n                    </td>\r\n                    <td style=\"min-width:150px;max-width:150px\" [pEditableColumn]=\"simImport.ratingPlanName\" pEditableColumnField=\"ratingPlanName\">\r\n                        <p-cellEditor>\r\n                            <ng-template pTemplate=\"input\">\r\n                                <input formControlName=\"ratingPlanName\"\r\n                                       style=\"min-width:150px;max-width:150px\"\r\n                                       pInputText type=\"text\"\r\n                                       [(ngModel)]=\"simImport.ratingPlanName\"\r\n                                       required\r\n                                       maxlength=\"50\"\r\n                                       pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$\"\r\n                                       (ngModelChange)=\"checkValueSimImportChange(simImport)\"\r\n                                />\r\n                            </ng-template>\r\n                            <ng-template pTemplate=\"output\">\r\n                                {{ simImport.ratingPlanName }}\r\n                            </ng-template>\r\n                        </p-cellEditor>\r\n                    </td>\r\n                    <td style=\"min-width:200px;max-width:200px\">\r\n                            <span *ngIf=\"!mapFormSimImports[simImport.keyForm].invalid\"\r\n                            >{{ tranService.translate(simImport.description) }}</span>\r\n                        <!-- chi so thue bao trong -->\r\n                        <span *ngIf=\"(mapFormSimImports[simImport.keyForm].controls.msisdn.hasError('required') && !mapFormSimImports[simImport.keyForm].controls.ratingPlanName.hasError('required'))\"\r\n                        >\r\n                                {{tranService.translate(\"global.message.requiredField\",{field: tranService.translate(\"sim.label.sothuebao\")})}}\r\n                            </span>\r\n                        <!-- chi ten goi cuoc trong -->\r\n                        <span *ngIf=\"(!mapFormSimImports[simImport.keyForm].controls.msisdn.hasError('required') && mapFormSimImports[simImport.keyForm].controls.ratingPlanName.hasError('required'))\"\r\n                        >\r\n                                {{tranService.translate(\"global.message.requiredField\",{field: tranService.translate(\"sim.label.tengoicuoc\")})}}\r\n                            </span>\r\n                        <!-- ca hai cung trong -->\r\n                        <span *ngIf=\"(mapFormSimImports[simImport.keyForm].controls.msisdn.hasError('required') && mapFormSimImports[simImport.keyForm].controls.ratingPlanName.hasError('required'))\"\r\n                        >\r\n                                {{tranService.translate(\"global.message.required\")}}\r\n                            </span>\r\n                        <!-- format so thue bao -->\r\n                        <span *ngIf=\"mapFormSimImports[simImport.keyForm].controls.msisdn.errors?.pattern\"\r\n                        >\r\n                                {{tranService.translate(\"global.message.invalidSubsciption\")}}\r\n                            </span>\r\n                        <!-- format ten goi cuoc -->\r\n                        <span *ngIf=\"mapFormSimImports[simImport.keyForm].controls.ratingPlanName.errors?.pattern\"\r\n                        >\r\n                                {{tranService.translate(\"global.message.formatContainVN\")}}\r\n                            </span>\r\n                    </td>\r\n                    <td style=\"min-width:100px;max-width:100px;text-align: center;\">\r\n                        <span [pTooltip]=\"tranService.translate('global.button.delete')\" class=\"pi pi-trash\" (click)=\"removeItemSimImport(simImport,i)\"></span>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n        <div class=\"flex flex-row justify-content-center align-items-center\">\r\n            <p-button styleClass=\"mr-2 p-button-secondary p-button-outlined\" [label]=\"tranService.translate('global.button.cancel')\" (click)=\"isShowDialogResultRegisterGroupSim = false\"></p-button>\r\n            <p-button [disabled]=\"!checkValidListImport()\" *ngIf=\"simImports\" styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.save')\" (click)=\"registerForFile()\"></p-button>\r\n        </div>\r\n    </p-dialog>\r\n</div>\r\n\r\n\r\n\r\n<!-- dialog result register plan by file -->\r\n<div class=\"flex justify-content-center dialog-push-group\">\r\n    <p-dialog [header]=\"tranService.translate('global.button.registerPlanByFile')\" [(visible)]=\"isShowDialogResultRegisterFile\" [modal]=\"true\" [style]=\"{ width: '1000px' }\" [draggable]=\"false\" [resizable]=\"false\" styleClass=\"dialog-file\">\r\n        <div class=\"w-full field grid\">\r\n            <div class=\"col-10 flex flex-row justify-content-start align-items-center\">\r\n                <input-file-vnpt class=\"w-full upload-device-file\" [(fileObject)]=\"fileObject\" [clearFileCallback]=\"clearFileCallback.bind(this)\"\r\n                    [options]=\"optionInputFile\"\r\n                ></input-file-vnpt>\r\n            </div>\r\n            <div class=\"col-2 flex flex-row justify-content-end align-items-center\">\r\n                <p-button icon=\"pi pi-download\" [pTooltip]=\"tranService.translate('global.button.downloadTemp')\" styleClass=\"p-button-outlined p-button-secondary\" (click)=\"downloadTemplate()\"></p-button>\r\n            </div>\r\n        </div>\r\n        <div class=\"grid\"><div class=\"col pt-0\"><small class=\"text-red-500\" *ngIf=\"isShowErrorUpload\">{{messageErrorUpload}}</small></div></div>\r\n        <p-table\r\n            [paginator]=\"true\"\r\n            [rows]=\"pageSizeSimImport\"\r\n            [first]=\"rowFirstSimImport\"\r\n            [showCurrentPageReport]=\"true\"\r\n            [tableStyle]=\"{ 'min-width': '100%' }\"\r\n            [currentPageReportTemplate]=\"tranService.translate('global.text.templateTextPagination')\"\r\n            (onPage)=\"pagingResultSimImport($event)\"\r\n            [rowsPerPageOptions]=\"[5,10,20]\"\r\n            [styleClass]=\"'p-datatable-sm'\"\r\n            [totalRecords]=\"simImportsOrigin?.length\"\r\n            [lazy]=\"true\"\r\n            #dataTable\r\n            [scrollHeight]=\"'400px'\"\r\n            [value]=\"simImports\"\r\n            dataKey=\"id\"\r\n            [tableStyle]=\"{ 'min-width': '50rem' }\"\r\n            *ngIf=\"simImportsOrigin\">\r\n            <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                    <th style=\"min-width:150px;max-width:150px\">{{tranService.translate(\"sim.label.sothuebao\")}}</th>\r\n                    <th style=\"min-width:150px;max-width:150px\">{{tranService.translate(\"sim.label.tengoicuoc\")}}</th>\r\n                    <th style=\"min-width: 250px;max-width: 250px;\">{{tranService.translate(\"sim.label.description\")}}</th>\r\n                    <th style=\"min-width:70px;max-width:70px; text-align: center;\">{{tranService.translate(\"global.text.action\")}}</th>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"body\" let-simImport let-editing=\"editing\" let-i=\"rowIndex\">\r\n                    <tr [formGroup]=\"mapFormSimImports[simImport.keyForm]\">\r\n                        <td style=\"min-width:150px;max-width:150px\" [pEditableColumn]=\"simImport.msisdn\" pEditableColumnField=\"msisdn\">\r\n                            <p-cellEditor>\r\n                                <ng-template pTemplate=\"input\">\r\n                                    <input formControlName=\"msisdn\"\r\n                                        style=\"min-width:150px;max-width:150px\"\r\n                                        pInputText type=\"text\"\r\n                                        [(ngModel)]=\"simImport.msisdn\"\r\n                                        required\r\n                                        maxlength=\"20\"\r\n                                        pattern=\"^(\\+?84)[1-9][0-9]{8,9}$\"\r\n                                        (ngModelChange)=\"checkValueSimImportChange(simImport)\"\r\n                                    />\r\n                                </ng-template>\r\n                                <ng-template pTemplate=\"output\">\r\n                                    {{ simImport.msisdn }}\r\n                                </ng-template>\r\n                            </p-cellEditor>\r\n                        </td>\r\n                        <td style=\"min-width:150px;max-width:150px\" [pEditableColumn]=\"simImport.ratingPlanName\" pEditableColumnField=\"ratingPlanName\">\r\n                            <p-cellEditor>\r\n                                <ng-template pTemplate=\"input\">\r\n                                    <input formControlName=\"ratingPlanName\"\r\n                                        style=\"min-width:150px;max-width:150px\"\r\n                                        pInputText type=\"text\"\r\n                                        [(ngModel)]=\"simImport.ratingPlanName\"\r\n                                        required\r\n                                        maxlength=\"50\"\r\n                                        pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$\"\r\n                                        (ngModelChange)=\"checkValueSimImportChange(simImport)\"\r\n                                    />\r\n                                </ng-template>\r\n                                <ng-template pTemplate=\"output\">\r\n                                    {{ simImport.ratingPlanName }}\r\n                                </ng-template>\r\n                            </p-cellEditor>\r\n                        </td>\r\n                        <td style=\"min-width:200px;max-width:200px\">\r\n                            <span *ngIf=\"!mapFormSimImports[simImport.keyForm].invalid\"\r\n                            >{{ tranService.translate(simImport.description) }}</span>\r\n                            <!-- chi so thue bao trong -->\r\n                            <span *ngIf=\"(mapFormSimImports[simImport.keyForm].controls.msisdn.hasError('required') && !mapFormSimImports[simImport.keyForm].controls.ratingPlanName.hasError('required'))\"\r\n                            >\r\n                                {{tranService.translate(\"global.message.requiredField\",{field: tranService.translate(\"sim.label.sothuebao\")})}}\r\n                            </span>\r\n                            <!-- chi ten goi cuoc trong -->\r\n                            <span *ngIf=\"(!mapFormSimImports[simImport.keyForm].controls.msisdn.hasError('required') && mapFormSimImports[simImport.keyForm].controls.ratingPlanName.hasError('required'))\"\r\n                            >\r\n                                {{tranService.translate(\"global.message.requiredField\",{field: tranService.translate(\"sim.label.tengoicuoc\")})}}\r\n                            </span>\r\n                            <!-- ca hai cung trong -->\r\n                            <span *ngIf=\"(mapFormSimImports[simImport.keyForm].controls.msisdn.hasError('required') && mapFormSimImports[simImport.keyForm].controls.ratingPlanName.hasError('required'))\"\r\n                            >\r\n                                {{tranService.translate(\"global.message.required\")}}\r\n                            </span>\r\n                            <!-- format so thue bao -->\r\n                            <span *ngIf=\"mapFormSimImports[simImport.keyForm].controls.msisdn.errors?.pattern\"\r\n                            >\r\n                                {{tranService.translate(\"global.message.invalidSubsciption\")}}\r\n                            </span>\r\n                            <!-- format ten goi cuoc -->\r\n                            <span *ngIf=\"mapFormSimImports[simImport.keyForm].controls.ratingPlanName.errors?.pattern\"\r\n                            >\r\n                                {{tranService.translate(\"global.message.formatContainVN\")}}\r\n                            </span>\r\n                        </td>\r\n                        <td style=\"min-width:100px;max-width:100px;text-align: center;\">\r\n                            <span [pTooltip]=\"tranService.translate('global.button.delete')\" class=\"pi pi-trash\" (click)=\"removeItemSimImport(simImport,i)\"></span>\r\n                        </td>\r\n                    </tr>\r\n            </ng-template>\r\n        </p-table>\r\n        <div class=\"flex flex-row justify-content-center align-items-center\">\r\n            <p-button styleClass=\"mr-2 p-button-secondary p-button-outlined\" [label]=\"tranService.translate('global.button.cancel')\" (click)=\"isShowDialogResultRegisterFile = false\"></p-button>\r\n            <p-button [disabled]=\"!checkValidListImport()\" *ngIf=\"simImports\" styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.save')\" (click)=\"registerForFile()\"></p-button>\r\n        </div>\r\n    </p-dialog>\r\n</div>\r\n"], "mappings": "AACA,SAAqBA,UAAU,QAAO,gBAAgB;AAEtD,SAAQC,SAAS,QAAO,iCAAiC;AAOzD,SAAQC,aAAa,QAAO,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICJ5CC,EAAA,CAAAC,SAAA,wBAA8S;;;;IAA5GD,EAAA,CAAAE,UAAA,UAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,qCAAmE,UAAAF,MAAA,CAAAG,aAAA;;;;;;;;IACrQN,EAAA,CAAAC,SAAA,mBAAuT;;;;IAAzPD,EAAA,CAAAE,UAAA,UAAAK,MAAA,CAAAH,WAAA,CAAAC,SAAA,sCAAoE,eAAAL,EAAA,CAAAQ,eAAA,IAAAC,GAAA;;;;;IA2K1GT,EAAA,CAAAU,cAAA,eAAsM;IAAAV,EAAA,CAAAW,MAAA,SAAE;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;;IAC/MZ,EAAA,CAAAU,cAAA,eAA8G;IAAAV,EAAA,CAAAW,MAAA,UAAG;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;;IACxHZ,EAAA,CAAAU,cAAA,eAA2J;IAAAV,EAAA,CAAAW,MAAA,gBAAS;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;;;;;;;;IA9BvMZ,EAAA,CAAAU,cAAA,mBAAqN;IAApJV,EAAA,CAAAa,UAAA,2BAAAC,oFAAAC,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAAF,OAAA,CAAAG,oBAAA,GAAAN,MAAA;IAAA,EAAkC;IAC/Ff,EAAA,CAAAU,cAAA,cAAuE;IAMiCV,EAAA,CAAAW,MAAA,GAAgD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACvIZ,EAAA,CAAAU,cAAA,eAAkB;IAAAV,EAAA,CAAAW,MAAA,IAAoB;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAEjDZ,EAAA,CAAAU,cAAA,eAAuB;IAC6DV,EAAA,CAAAW,MAAA,IAAmD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC1IZ,EAAA,CAAAU,cAAA,gBAAqE;IAAAV,EAAA,CAAAW,MAAA,IAAmC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAEnHZ,EAAA,CAAAU,cAAA,eAAuB;IAC6DV,EAAA,CAAAW,MAAA,IAA2C;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAClIZ,EAAA,CAAAU,cAAA,gBAAkB;IAAAV,EAAA,CAAAW,MAAA,IAAkB;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAE/CZ,EAAA,CAAAU,cAAA,eAAuB;IAC6DV,EAAA,CAAAW,MAAA,IAAiD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACxIZ,EAAA,CAAAU,cAAA,gBAAkB;IAAAV,EAAA,CAAAW,MAAA,IAAkB;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAE/CZ,EAAA,CAAAU,cAAA,eAAuB;IAC6DV,EAAA,CAAAW,MAAA,IAA4C;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACnIZ,EAAA,CAAAU,cAAA,gBAAkB;IAAAV,EAAA,CAAAW,MAAA,IAAmB;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAEhDZ,EAAA,CAAAU,cAAA,eAAuB;IAC6DV,EAAA,CAAAW,MAAA,IAAsD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC7IZ,EAAA,CAAAsB,UAAA,KAAAC,yDAAA,mBAA+M;IAC/MvB,EAAA,CAAAsB,UAAA,KAAAE,yDAAA,mBAAwH;IACxHxB,EAAA,CAAAsB,UAAA,KAAAG,yDAAA,mBAA2K;IAC/KzB,EAAA,CAAAY,YAAA,EAAM;IAEVZ,EAAA,CAAAU,cAAA,eAAiB;IAEuEV,EAAA,CAAAW,MAAA,IAAgD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACvIZ,EAAA,CAAAU,cAAA,gBAAkB;IAAAV,EAAA,CAAAW,MAAA,IAA2C;;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAExEZ,EAAA,CAAAU,cAAA,eAAuB;IAC6DV,EAAA,CAAAW,MAAA,IAAkD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACzIZ,EAAA,CAAAU,cAAA,gBAA0B;IAAAV,EAAA,CAAAW,MAAA,IAAyC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAK1FZ,EAAA,CAAAU,cAAA,kBAAgG;IAGpEV,EAAA,CAAAa,UAAA,2BAAAa,2FAAAX,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,IAAA;MAAA,MAAAU,OAAA,GAAA3B,EAAA,CAAAmB,aAAA;MAAA,OAAanB,EAAA,CAAAoB,WAAA,CAAAO,OAAA,CAAAC,eAAA,CAAAC,UAAA,GAAAd,MAAA,CAAkC;IAAA,EAAP;IAA+Cf,EAAA,CAAAY,YAAA,EAAiB;IACxHZ,EAAA,CAAAU,cAAA,WAAK;IAAAV,EAAA,CAAAW,MAAA,IAAoD;IAAAX,EAAA,CAAAY,YAAA,EAAM;IAEnEZ,EAAA,CAAAU,cAAA,eAA+B;IACXV,EAAA,CAAAa,UAAA,2BAAAiB,2FAAAf,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,IAAA;MAAA,MAAAc,OAAA,GAAA/B,EAAA,CAAAmB,aAAA;MAAA,OAAanB,EAAA,CAAAoB,WAAA,CAAAW,OAAA,CAAAH,eAAA,CAAAI,iBAAA,GAAAjB,MAAA,CAAyC;IAAA,EAAP;IAA+Cf,EAAA,CAAAY,YAAA,EAAiB;IAC/HZ,EAAA,CAAAU,cAAA,WAAK;IAAAV,EAAA,CAAAW,MAAA,IAA4D;IAAAX,EAAA,CAAAY,YAAA,EAAM;IAE3EZ,EAAA,CAAAU,cAAA,eAA+B;IACXV,EAAA,CAAAa,UAAA,2BAAAoB,2FAAAlB,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,IAAA;MAAA,MAAAiB,OAAA,GAAAlC,EAAA,CAAAmB,aAAA;MAAA,OAAanB,EAAA,CAAAoB,WAAA,CAAAc,OAAA,CAAAN,eAAA,CAAAO,cAAA,GAAApB,MAAA,CAAsC;IAAA,EAAP;IAA+Cf,EAAA,CAAAY,YAAA,EAAiB;IAC5HZ,EAAA,CAAAU,cAAA,WAAK;IAAAV,EAAA,CAAAW,MAAA,IAAwD;IAAAX,EAAA,CAAAY,YAAA,EAAM;IAG3EZ,EAAA,CAAAU,cAAA,eAAkB;IAEMV,EAAA,CAAAa,UAAA,2BAAAuB,2FAAArB,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,IAAA;MAAA,MAAAoB,OAAA,GAAArC,EAAA,CAAAmB,aAAA;MAAA,OAAanB,EAAA,CAAAoB,WAAA,CAAAiB,OAAA,CAAAT,eAAA,CAAAU,eAAA,GAAAvB,MAAA,CAAuC;IAAA,EAAP;IAA+Cf,EAAA,CAAAY,YAAA,EAAiB;IAC7HZ,EAAA,CAAAU,cAAA,WAAK;IAAAV,EAAA,CAAAW,MAAA,IAAyD;IAAAX,EAAA,CAAAY,YAAA,EAAM;IAExEZ,EAAA,CAAAU,cAAA,eAA+B;IACXV,EAAA,CAAAa,UAAA,2BAAA0B,2FAAAxB,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,IAAA;MAAA,MAAAuB,OAAA,GAAAxC,EAAA,CAAAmB,aAAA;MAAA,OAAanB,EAAA,CAAAoB,WAAA,CAAAoB,OAAA,CAAAZ,eAAA,CAAAa,gBAAA,GAAA1B,MAAA,CAAwC;IAAA,EAAP;IAA+Cf,EAAA,CAAAY,YAAA,EAAiB;IAC9HZ,EAAA,CAAAU,cAAA,WAAK;IAAAV,EAAA,CAAAW,MAAA,IAA2D;IAAAX,EAAA,CAAAY,YAAA,EAAM;IAE1EZ,EAAA,CAAAU,cAAA,eAA+B;IACXV,EAAA,CAAAa,UAAA,2BAAA6B,2FAAA3B,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,IAAA;MAAA,MAAA0B,OAAA,GAAA3C,EAAA,CAAAmB,aAAA;MAAA,OAAanB,EAAA,CAAAoB,WAAA,CAAAuB,OAAA,CAAAf,eAAA,CAAAgB,aAAA,GAAA7B,MAAA,CAAqC;IAAA,EAAP;IAA+Cf,EAAA,CAAAY,YAAA,EAAiB;IAC3HZ,EAAA,CAAAU,cAAA,WAAK;IAAAV,EAAA,CAAAW,MAAA,IAAuD;IAAAX,EAAA,CAAAY,YAAA,EAAM;IAK9EZ,EAAA,CAAAU,cAAA,kBAAsF;IAEEV,EAAA,CAAAW,MAAA,IAAiD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACxIZ,EAAA,CAAAU,cAAA,gBAAkB;IAAAV,EAAA,CAAAW,MAAA,IAA4B;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAEzDZ,EAAA,CAAAU,cAAA,eAAuB;IAC6DV,EAAA,CAAAW,MAAA,IAAqD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC5IZ,EAAA,CAAAU,cAAA,gBAAkB;IAAAV,EAAA,CAAAW,MAAA,IAAuI;;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAI5KZ,EAAA,CAAAU,cAAA,eAAiC;IAI2DV,EAAA,CAAAW,MAAA,IAAgD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACvIZ,EAAA,CAAAU,cAAA,gBAAkB;IAAAV,EAAA,CAAAW,MAAA,IAA+B;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAE5DZ,EAAA,CAAAU,cAAA,eAAuB;IAC6DV,EAAA,CAAAW,MAAA,IAAqD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC5IZ,EAAA,CAAAU,cAAA,gBAAkB;IAAAV,EAAA,CAAAW,MAAA,IAAmD;;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAEhFZ,EAAA,CAAAU,cAAA,gBAAuB;IAC6DV,EAAA,CAAAW,MAAA,KAAsD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC7IZ,EAAA,CAAAU,cAAA,iBAA4B;IAAAV,EAAA,CAAAW,MAAA,KAAiC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAExEZ,EAAA,CAAAU,cAAA,gBAAuB;IAC6DV,EAAA,CAAAW,MAAA,KAAiD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACxIZ,EAAA,CAAAU,cAAA,iBAAkB;IAAAV,EAAA,CAAAW,MAAA,KAA6B;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAE1DZ,EAAA,CAAAU,cAAA,gBAAuB;IAC6DV,EAAA,CAAAW,MAAA,KAAsD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC7IZ,EAAA,CAAAU,cAAA,iBAAkB;IAAAV,EAAA,CAAAW,MAAA,KAA+B;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAE5DZ,EAAA,CAAAU,cAAA,gBAAuB;IAC6DV,EAAA,CAAAW,MAAA,KAAmD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC1IZ,EAAA,CAAAU,cAAA,iBAAkB;IAAAV,EAAA,CAAAW,MAAA,KAAiC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAE9DZ,EAAA,CAAAU,cAAA,gBAAuB;IAC6DV,EAAA,CAAAW,MAAA,KAAkD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACzIZ,EAAA,CAAAU,cAAA,iBAA4B;IAAAV,EAAA,CAAAW,MAAA,KAA8B;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAErEZ,EAAA,CAAAU,cAAA,gBAAuB;IAC6DV,EAAA,CAAAW,MAAA,KAAqD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC5IZ,EAAA,CAAAU,cAAA,iBAAkB;IAAAV,EAAA,CAAAW,MAAA,KAAiC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAE9DZ,EAAA,CAAAU,cAAA,gBAAuB;IAC6DV,EAAA,CAAAW,MAAA,KAAgD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACvIZ,EAAA,CAAAU,cAAA,iBAAkB;IAAAV,EAAA,CAAAW,MAAA,KAA4B;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAI7DZ,EAAA,CAAAU,cAAA,mBAAoF;IAEIV,EAAA,CAAAW,MAAA,KAAgD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACvIZ,EAAA,CAAAU,cAAA,iBAAkB;IAAAV,EAAA,CAAAW,MAAA,KAAuB;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAEpDZ,EAAA,CAAAU,cAAA,gBAAuB;IAC6DV,EAAA,CAAAW,MAAA,KAAmD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC1IZ,EAAA,CAAAU,cAAA,iBAAkB;IAAAV,EAAA,CAAAW,MAAA,KAAuB;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IAvI+CZ,EAAA,CAAA6C,UAAA,CAAA7C,EAAA,CAAAQ,eAAA,KAAAsC,GAAA,EAA4B;IAArI9C,EAAA,CAAAE,UAAA,WAAA6C,MAAA,CAAA3C,WAAA,CAAAC,SAAA,uBAAsD,YAAA0C,MAAA,CAAA1B,oBAAA;IAG5CrB,EAAA,CAAAgD,SAAA,GAAoD;IAApDhD,EAAA,CAAAE,UAAA,WAAA6C,MAAA,CAAA3C,WAAA,CAAAC,SAAA,qBAAoD;IAIoCL,EAAA,CAAAgD,SAAA,GAAgD;IAAhDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,wBAAgD;IAC9GL,EAAA,CAAAgD,SAAA,GAAoB;IAApBhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAAG,SAAA,CAAAC,MAAA,CAAoB;IAG0CnD,EAAA,CAAAgD,SAAA,GAAmD;IAAnDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,2BAAmD;IACzGL,EAAA,CAAAgD,SAAA,GAA0C;IAA1ChD,EAAA,CAAAoD,UAAA,CAAAL,MAAA,CAAAM,cAAA,CAAAN,MAAA,CAAAG,SAAA,CAAAI,MAAA,EAA0C;IAACtD,EAAA,CAAAgD,SAAA,GAAmC;IAAnChD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAAQ,aAAA,CAAAR,MAAA,CAAAG,SAAA,CAAAI,MAAA,EAAmC;IAGxBtD,EAAA,CAAAgD,SAAA,GAA2C;IAA3ChD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,mBAA2C;IACzGL,EAAA,CAAAgD,SAAA,GAAkB;IAAlBhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAAG,SAAA,CAAAM,IAAA,CAAkB;IAG4CxD,EAAA,CAAAgD,SAAA,GAAiD;IAAjDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,yBAAiD;IAC/GL,EAAA,CAAAgD,SAAA,GAAkB;IAAlBhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAAG,SAAA,CAAAO,IAAA,CAAkB;IAG4CzD,EAAA,CAAAgD,SAAA,GAA4C;IAA5ChD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,oBAA4C;IAC1GL,EAAA,CAAAgD,SAAA,GAAmB;IAAnBhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAAG,SAAA,CAAAQ,KAAA,CAAmB;IAG2C1D,EAAA,CAAAgD,SAAA,GAAsD;IAAtDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,8BAAsD;IAC/HL,EAAA,CAAAgD,SAAA,GAAqH;IAArHhD,EAAA,CAAAE,UAAA,SAAA6C,MAAA,CAAAG,SAAA,CAAAS,gBAAA,KAAAC,SAAA,IAAAb,MAAA,CAAAG,SAAA,CAAAS,gBAAA,aAAAZ,MAAA,CAAAG,SAAA,CAAAS,gBAAA,SAAqH;IACrH3D,EAAA,CAAAgD,SAAA,GAAsC;IAAtChD,EAAA,CAAAE,UAAA,SAAA6C,MAAA,CAAAG,SAAA,CAAAS,gBAAA,SAAsC;IACtC3D,EAAA,CAAAgD,SAAA,GAAkF;IAAlFhD,EAAA,CAAAE,UAAA,SAAA6C,MAAA,CAAAG,SAAA,CAAAS,gBAAA,KAAAC,SAAA,IAAAb,MAAA,CAAAG,SAAA,CAAAS,gBAAA,UAAkF;IAKT3D,EAAA,CAAAgD,SAAA,GAAgD;IAAhDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,wBAAgD;IAC9GL,EAAA,CAAAgD,SAAA,GAA2C;IAA3ChD,EAAA,CAAAiD,iBAAA,CAAAjD,EAAA,CAAA6D,WAAA,SAAAd,MAAA,CAAAG,SAAA,CAAAY,SAAA,gBAA2C;IAGmB9D,EAAA,CAAAgD,SAAA,GAAkD;IAAlDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,0BAAkD;IACxGL,EAAA,CAAAgD,SAAA,GAAyC;IAAzChD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAAgB,cAAA,CAAAhB,MAAA,CAAAG,SAAA,CAAAc,WAAA,EAAyC;IAK3EhE,EAAA,CAAAgD,SAAA,GAA0D;IAA1DhD,EAAA,CAAAE,UAAA,WAAA6C,MAAA,CAAA3C,WAAA,CAAAC,SAAA,2BAA0D;IAGtCL,EAAA,CAAAgD,SAAA,GAAwC;IAAxChD,EAAA,CAAAE,UAAA,YAAA6C,MAAA,CAAAnB,eAAA,CAAAC,UAAA,CAAwC;IACnD7B,EAAA,CAAAgD,SAAA,GAAoD;IAApDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,4BAAoD;IAGzCL,EAAA,CAAAgD,SAAA,GAA+C;IAA/ChD,EAAA,CAAAE,UAAA,YAAA6C,MAAA,CAAAnB,eAAA,CAAAI,iBAAA,CAA+C;IAC1DhC,EAAA,CAAAgD,SAAA,GAA4D;IAA5DhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,oCAA4D;IAGjDL,EAAA,CAAAgD,SAAA,GAA4C;IAA5ChD,EAAA,CAAAE,UAAA,YAAA6C,MAAA,CAAAnB,eAAA,CAAAO,cAAA,CAA4C;IACvDnC,EAAA,CAAAgD,SAAA,GAAwD;IAAxDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,gCAAwD;IAK7CL,EAAA,CAAAgD,SAAA,GAA6C;IAA7ChD,EAAA,CAAAE,UAAA,YAAA6C,MAAA,CAAAnB,eAAA,CAAAU,eAAA,CAA6C;IACxDtC,EAAA,CAAAgD,SAAA,GAAyD;IAAzDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,iCAAyD;IAG9CL,EAAA,CAAAgD,SAAA,GAA8C;IAA9ChD,EAAA,CAAAE,UAAA,YAAA6C,MAAA,CAAAnB,eAAA,CAAAa,gBAAA,CAA8C;IACzDzC,EAAA,CAAAgD,SAAA,GAA2D;IAA3DhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,mCAA2D;IAGhDL,EAAA,CAAAgD,SAAA,GAA2C;IAA3ChD,EAAA,CAAAE,UAAA,YAAA6C,MAAA,CAAAnB,eAAA,CAAAgB,aAAA,CAA2C;IACtD5C,EAAA,CAAAgD,SAAA,GAAuD;IAAvDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,+BAAuD;IAKhEL,EAAA,CAAAgD,SAAA,GAA2D;IAA3DhD,EAAA,CAAAE,UAAA,WAAA6C,MAAA,CAAA3C,WAAA,CAAAC,SAAA,4BAA2D;IAEqBL,EAAA,CAAAgD,SAAA,GAAiD;IAAjDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,yBAAiD;IAC/GL,EAAA,CAAAgD,SAAA,GAA4B;IAA5BhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAAG,SAAA,CAAAe,cAAA,CAA4B;IAGkCjE,EAAA,CAAAgD,SAAA,GAAqD;IAArDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,6BAAqD;IACnHL,EAAA,CAAAgD,SAAA,GAAuI;IAAvIhD,EAAA,CAAAkE,kBAAA,KAAAlE,EAAA,CAAAmE,WAAA,SAAApB,MAAA,CAAAqB,WAAA,CAAAC,gBAAA,CAAAtB,MAAA,CAAAuB,gBAAA,kBAAAvB,MAAA,CAAAuB,gBAAA,CAAAC,cAAA,UAAAxB,MAAA,CAAAuB,gBAAA,kBAAAvB,MAAA,CAAAuB,gBAAA,CAAAE,IAAA,IAAAzB,MAAA,CAAAuB,gBAAA,CAAAE,IAAA,YAAuI;IAMzJxE,EAAA,CAAAgD,SAAA,GAAyD;IAAzDhD,EAAA,CAAAE,UAAA,WAAA6C,MAAA,CAAA3C,WAAA,CAAAC,SAAA,0BAAyD;IAEuBL,EAAA,CAAAgD,SAAA,GAAgD;IAAhDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,wBAAgD;IAC9GL,EAAA,CAAAgD,SAAA,GAA+B;IAA/BhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA0B,cAAA,CAAAC,YAAA,CAA+B;IAG+B1E,EAAA,CAAAgD,SAAA,GAAqD;IAArDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,6BAAqD;IACnHL,EAAA,CAAAgD,SAAA,GAAmD;IAAnDhD,EAAA,CAAAiD,iBAAA,CAAAjD,EAAA,CAAA6D,WAAA,UAAAd,MAAA,CAAA0B,cAAA,CAAAE,YAAA,gBAAmD;IAGW3E,EAAA,CAAAgD,SAAA,GAAsD;IAAtDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,8BAAsD;IAC1GL,EAAA,CAAAgD,SAAA,GAAiC;IAAjChD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA0B,cAAA,CAAAG,cAAA,CAAiC;IAGmB5E,EAAA,CAAAgD,SAAA,GAAiD;IAAjDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,yBAAiD;IAC/GL,EAAA,CAAAgD,SAAA,GAA6B;IAA7BhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA0B,cAAA,CAAAI,UAAA,CAA6B;IAGiC7E,EAAA,CAAAgD,SAAA,GAAsD;IAAtDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,8BAAsD;IACpHL,EAAA,CAAAgD,SAAA,GAA+B;IAA/BhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA0B,cAAA,CAAAK,YAAA,CAA+B;IAG+B9E,EAAA,CAAAgD,SAAA,GAAmD;IAAnDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,2BAAmD;IACjHL,EAAA,CAAAgD,SAAA,GAAiC;IAAjChD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA0B,cAAA,CAAAM,cAAA,CAAiC;IAG6B/E,EAAA,CAAAgD,SAAA,GAAkD;IAAlDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,0BAAkD;IACtGL,EAAA,CAAAgD,SAAA,GAA8B;IAA9BhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA0B,cAAA,CAAAO,WAAA,CAA8B;IAGsBhF,EAAA,CAAAgD,SAAA,GAAqD;IAArDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,6BAAqD;IACnHL,EAAA,CAAAgD,SAAA,GAAiC;IAAjChD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA0B,cAAA,CAAAQ,cAAA,CAAiC;IAG6BjF,EAAA,CAAAgD,SAAA,GAAgD;IAAhDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,wBAAgD;IAC9GL,EAAA,CAAAgD,SAAA,GAA4B;IAA5BhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA0B,cAAA,CAAAS,SAAA,CAA4B;IAI9ClF,EAAA,CAAAgD,SAAA,GAAyD;IAAzDhD,EAAA,CAAAE,UAAA,WAAA6C,MAAA,CAAA3C,WAAA,CAAAC,SAAA,0BAAyD;IAEuBL,EAAA,CAAAgD,SAAA,GAAgD;IAAhDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,wBAAgD;IAC9GL,EAAA,CAAAgD,SAAA,GAAuB;IAAvBhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAAoC,cAAA,CAAAC,IAAA,CAAuB;IAGuCpF,EAAA,CAAAgD,SAAA,GAAmD;IAAnDhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAA3C,WAAA,CAAAC,SAAA,2BAAmD;IACjHL,EAAA,CAAAgD,SAAA,GAAuB;IAAvBhD,EAAA,CAAAiD,iBAAA,CAAAF,MAAA,CAAAoC,cAAA,CAAAE,IAAA,CAAuB;;;;;IAoE7CrF,EAAA,CAAAU,cAAA,cAA4E;IAC2BV,EAAA,CAAAW,MAAA,GAAsD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAChKZ,EAAA,CAAAU,cAAA,eAA4E;IAAAV,EAAA,CAAAW,MAAA,GAAc;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IADEZ,EAAA,CAAAgD,SAAA,GAAsD;IAAtDhD,EAAA,CAAAiD,iBAAA,CAAAqC,OAAA,CAAAlF,WAAA,CAAAC,SAAA,8BAAsD;IAC7EL,EAAA,CAAAgD,SAAA,GAAc;IAAdhD,EAAA,CAAAiD,iBAAA,CAAAqC,OAAA,CAAAC,UAAA,CAAc;;;;;;;;;;;IA7D9GvF,EAAA,CAAAU,cAAA,mBAA2N;IAAtJV,EAAA,CAAAa,UAAA,2BAAA2E,oFAAAzE,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAyE,IAAA;MAAA,MAAAC,OAAA,GAAA1F,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAAsE,OAAA,CAAAC,qBAAA,GAAA5E,MAAA;IAAA,EAAmC;IACpGf,EAAA,CAAAU,cAAA,iBAAsE;IAI6CV,EAAA,CAAAW,MAAA,GAAsD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAChKZ,EAAA,CAAAU,cAAA,eAAqC;IAAAV,EAAA,CAAAW,MAAA,GAAuB;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAEvEZ,EAAA,CAAAU,cAAA,cAAmB;IACoFV,EAAA,CAAAW,MAAA,IAAsD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAChKZ,EAAA,CAAAU,cAAA,gBAAqC;IAAAV,EAAA,CAAAW,MAAA,IAAuB;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAEvEZ,EAAA,CAAAU,cAAA,eAAmB;IACoFV,EAAA,CAAAW,MAAA,IAAoD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC9JZ,EAAA,CAAAU,cAAA,eAAoC;IACsBV,EAAA,CAAAW,MAAA,IAAwC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAG7GZ,EAAA,CAAAU,cAAA,eAAmB;IACoFV,EAAA,CAAAW,MAAA,IAA0D;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACpKZ,EAAA,CAAAU,cAAA,gBAAqC;IAAAV,EAAA,CAAAW,MAAA,IAA+B;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAE/EZ,EAAA,CAAAU,cAAA,eAAmB;IACoFV,EAAA,CAAAW,MAAA,IAA0D;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACpKZ,EAAA,CAAAU,cAAA,gBAAqC;IAAAV,EAAA,CAAAW,MAAA,IAAoD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAEpGZ,EAAA,CAAAU,cAAA,eAAmB;IACoFV,EAAA,CAAAW,MAAA,IAAyD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACnKZ,EAAA,CAAAU,cAAA,gBAAqC;IAAAV,EAAA,CAAAW,MAAA,IAA8B;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAMlFZ,EAAA,CAAAU,cAAA,eAAoB;IAEmEV,EAAA,CAAAW,MAAA,IAA6D;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACnJZ,EAAA,CAAAU,cAAA,gBAAqC;IAAAV,EAAA,CAAAW,MAAA,IAAyK;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAEzNZ,EAAA,CAAAU,cAAA,eAAkB;IAG6DV,EAAA,CAAAa,UAAA,2BAAA+E,0FAAA7E,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAyE,IAAA;MAAA,MAAAI,OAAA,GAAA7F,EAAA,CAAAmB,aAAA;MAAA,OAAanB,EAAA,CAAAoB,WAAA,CAAAyE,OAAA,CAAAC,cAAA,CAAAC,gBAAA,GAAAhF,MAAA,CAAuC;IAAA,EAAP;IAAmBf,EAAA,CAAAY,YAAA,EAAgB;IACnJZ,EAAA,CAAAW,MAAA,gBACA;IAAAX,EAAA,CAAAU,cAAA,YAAM;IAAAV,EAAA,CAAAW,MAAA,IAA6D;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAGlFZ,EAAA,CAAAU,cAAA,gBAA8E;IAEHV,EAAA,CAAAa,UAAA,2BAAAmF,0FAAAjF,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAyE,IAAA;MAAA,MAAAQ,OAAA,GAAAjG,EAAA,CAAAmB,aAAA;MAAA,OAAanB,EAAA,CAAAoB,WAAA,CAAA6E,OAAA,CAAAH,cAAA,CAAAC,gBAAA,GAAAhF,MAAA,CAAuC;IAAA,EAAP;IAAmBf,EAAA,CAAAY,YAAA,EAAgB;IACnJZ,EAAA,CAAAW,MAAA,gBACA;IAAAX,EAAA,CAAAU,cAAA,YAAM;IAAAV,EAAA,CAAAW,MAAA,IAA4D;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAIrFZ,EAAA,CAAAU,cAAA,eAAkB;IACiEV,EAAA,CAAAW,MAAA,IAAyD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC/IZ,EAAA,CAAAU,cAAA,gBAAqC;IAAAV,EAAA,CAAAW,MAAA,IAA8C;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAE9FZ,EAAA,CAAAsB,UAAA,KAAA4E,wDAAA,kBAGM;IAENlG,EAAA,CAAAU,cAAA,eAAkB;IACiEV,EAAA,CAAAW,MAAA,IAAmD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACzIZ,EAAA,CAAAU,cAAA,gBAAqC;IAAAV,EAAA,CAAAW,MAAA,IAAkD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAElGZ,EAAA,CAAAU,cAAA,eAAkB;IACiEV,EAAA,CAAAW,MAAA,IAA2D;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACjJZ,EAAA,CAAAU,cAAA,gBAAqC;IAAAV,EAAA,CAAAW,MAAA,IAAgC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAEhFZ,EAAA,CAAAU,cAAA,eAAkB;IACkEV,EAAA,CAAAW,MAAA,IAAoD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC3IZ,EAAA,CAAAU,cAAA,eAAmD;IAChCV,EAAA,CAAAa,UAAA,2BAAAsF,0FAAApF,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAyE,IAAA;MAAA,MAAAW,OAAA,GAAApG,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAAgF,OAAA,CAAAC,aAAA,GAAAtF,MAAA;IAAA,EAA2B;IAAmBf,EAAA,CAAAY,YAAA,EAAgB;IAM7FZ,EAAA,CAAAU,cAAA,eAAuB;IAC4DV,EAAA,CAAAW,MAAA,IAAkD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAE5IZ,EAAA,CAAAU,cAAA,eAAyI;IAG1BV,EAAA,CAAAW,MAAA,IAA4D;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACtKZ,EAAA,CAAAU,cAAA,gBAAqC;IAAAV,EAAA,CAAAW,MAAA,IAAiC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAEjFZ,EAAA,CAAAU,cAAA,eAAuB;IACgFV,EAAA,CAAAW,MAAA,IAA6D;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACvKZ,EAAA,CAAAU,cAAA,gBAAqC;IAAAV,EAAA,CAAAW,MAAA,IAAkC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAGtFZ,EAAA,CAAAU,cAAA,eAAoB;IAEuFV,EAAA,CAAAW,MAAA,IAA4D;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACtKZ,EAAA,CAAAU,cAAA,gBAAqC;IAAAV,EAAA,CAAAW,MAAA,IAAiC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAKzFZ,EAAA,CAAAU,cAAA,eAAuB;IAC4DV,EAAA,CAAAW,MAAA,IAAsD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC5IZ,EAAA,CAAAU,cAAA,eAA+B;IACZV,EAAA,CAAAa,UAAA,2BAAAyF,2FAAAvF,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAyE,IAAA;MAAA,MAAAc,OAAA,GAAAvG,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAAmF,OAAA,CAAAC,eAAA,GAAAzF,MAAA;IAAA,EAA6B;IAAmBf,EAAA,CAAAY,YAAA,EAAgB;IAGvFZ,EAAA,CAAAU,cAAA,gBAA8F;IAGiBV,EAAA,CAAAW,MAAA,KAA4D;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACtKZ,EAAA,CAAAU,cAAA,iBAAqC;IAAAV,EAAA,CAAAW,MAAA,KAAiC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAEjFZ,EAAA,CAAAU,cAAA,gBAAmB;IACoFV,EAAA,CAAAW,MAAA,KAA2D;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACrKZ,EAAA,CAAAU,cAAA,iBAAqC;IAAAV,EAAA,CAAAW,MAAA,KAA4B;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAE5EZ,EAAA,CAAAU,cAAA,gBAAmB;IACoFV,EAAA,CAAAW,MAAA,KAA0D;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACpKZ,EAAA,CAAAU,cAAA,iBAAqC;IAAAV,EAAA,CAAAW,MAAA,KAA+B;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAE/EZ,EAAA,CAAAU,cAAA,gBAAmB;IACoFV,EAAA,CAAAW,MAAA,KAA2D;IAAAX,EAAA,CAAAY,YAAA,EAAO;IACrKZ,EAAA,CAAAU,cAAA,iBAAqC;IAAAV,EAAA,CAAAW,MAAA,KAAgC;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAEhFZ,EAAA,CAAAU,cAAA,gBAAmB;IACiEV,EAAA,CAAAW,MAAA,KAAwD;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAC/IZ,EAAA,CAAAU,cAAA,iBAAqC;IAAAV,EAAA,CAAAW,MAAA,KAA6B;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAGjFZ,EAAA,CAAAU,cAAA,gBAAoB;IAEuFV,EAAA,CAAAW,MAAA,KAAuF;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAErMZ,EAAA,CAAAU,cAAA,gBAAkB;IACqFV,EAAA,CAAAW,MAAA,KAAsD;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IAzI5DZ,EAAA,CAAA6C,UAAA,CAAA7C,EAAA,CAAAQ,eAAA,KAAAsC,GAAA,EAA4B;IAA1I9C,EAAA,CAAAE,UAAA,WAAAuG,MAAA,CAAArG,WAAA,CAAAC,SAAA,2BAA0D,YAAAoG,MAAA,CAAAd,qBAAA;IACnC3F,EAAA,CAAAgD,SAAA,GAAwC;IAAxChD,EAAA,CAAA6C,UAAA,CAAA7C,EAAA,CAAAQ,eAAA,KAAAkG,GAAA,EAAwC;IAI8C1G,EAAA,CAAAgD,SAAA,GAAsD;IAAtDhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,8BAAsD;IACpHL,EAAA,CAAAgD,SAAA,GAAuB;IAAvBhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAAX,cAAA,CAAAT,IAAA,CAAuB;IAGuCrF,EAAA,CAAAgD,SAAA,GAAsD;IAAtDhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,8BAAsD;IACpHL,EAAA,CAAAgD,SAAA,GAAuB;IAAvBhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAAX,cAAA,CAAAV,IAAA,CAAuB;IAGuCpF,EAAA,CAAAgD,SAAA,GAAoD;IAApDhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,4BAAoD;IAE7IL,EAAA,CAAAgD,SAAA,GAA+C;IAA/ChD,EAAA,CAAAoD,UAAA,CAAAqD,MAAA,CAAApD,cAAA,CAAAoD,MAAA,CAAAX,cAAA,CAAAxC,MAAA,EAA+C;IAACtD,EAAA,CAAAgD,SAAA,GAAwC;IAAxChD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAAlD,aAAA,CAAAkD,MAAA,CAAAX,cAAA,CAAAxC,MAAA,EAAwC;IAICtD,EAAA,CAAAgD,SAAA,GAA0D;IAA1DhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,kCAA0D;IACxHL,EAAA,CAAAgD,SAAA,GAA+B;IAA/BhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAAX,cAAA,CAAAa,YAAA,CAA+B;IAG+B3G,EAAA,CAAAgD,SAAA,GAA0D;IAA1DhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,kCAA0D;IACxHL,EAAA,CAAAgD,SAAA,GAAoD;IAApDhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAAG,mBAAA,CAAAH,MAAA,CAAAX,cAAA,CAAAe,YAAA,EAAoD;IAGU7G,EAAA,CAAAgD,SAAA,GAAyD;IAAzDhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,iCAAyD;IACvHL,EAAA,CAAAgD,SAAA,GAA8B;IAA9BhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAAX,cAAA,CAAAgB,WAAA,CAA8B;IAQY9G,EAAA,CAAAgD,SAAA,GAA6D;IAA7DhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,qCAA6D;IACvGL,EAAA,CAAAgD,SAAA,GAAyK;IAAzKhD,EAAA,CAAA+G,kBAAA,KAAAN,MAAA,CAAAX,cAAA,CAAAkB,eAAA,kCAAAP,MAAA,CAAArG,WAAA,CAAAC,SAAA,wCAAAoG,MAAA,CAAArG,WAAA,CAAAC,SAAA,4BAAyK;IAKvLL,EAAA,CAAAgD,SAAA,GAAiB;IAAjBhD,EAAA,CAAAE,UAAA,kBAAiB,UAAAuG,MAAA,CAAAQ,iBAAA,IAAAC,EAAA,aAAAT,MAAA,CAAAX,cAAA,CAAAC,gBAAA;IAE1B/F,EAAA,CAAAgD,SAAA,GAA6D;IAA7DhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,qCAA6D;IAKpDL,EAAA,CAAAgD,SAAA,GAAiB;IAAjBhD,EAAA,CAAAE,UAAA,kBAAiB,UAAAuG,MAAA,CAAAQ,iBAAA,IAAAC,EAAA,aAAAT,MAAA,CAAAX,cAAA,CAAAC,gBAAA;IAE1B/F,EAAA,CAAAgD,SAAA,GAA4D;IAA5DhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,oCAA4D;IAKKL,EAAA,CAAAgD,SAAA,GAAyD;IAAzDhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,iCAAyD;IACnGL,EAAA,CAAAgD,SAAA,GAA8C;IAA9ChD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAAU,cAAA,CAAAV,MAAA,CAAAX,cAAA,CAAAsB,WAAA,EAA8C;IAEpEpH,EAAA,CAAAgD,SAAA,GAAuD;IAAvDhD,EAAA,CAAAE,UAAA,SAAAuG,MAAA,CAAAX,cAAA,CAAAsB,WAAA,IAAAX,MAAA,CAAAY,UAAA,CAAAC,QAAA,CAAuD;IAMStH,EAAA,CAAAgD,SAAA,GAAmD;IAAnDhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,2BAAmD;IAC7FL,EAAA,CAAAgD,SAAA,GAAkD;IAAlDhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAAc,gBAAA,CAAAd,MAAA,CAAAX,cAAA,CAAA0B,aAAA,EAAkD;IAGRxH,EAAA,CAAAgD,SAAA,GAA2D;IAA3DhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,mCAA2D;IACrGL,EAAA,CAAAgD,SAAA,GAAgC;IAAhChD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAAX,cAAA,CAAA2B,aAAA,CAAgC;IAGWzH,EAAA,CAAAgD,SAAA,GAAoD;IAApDhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,4BAAoD;IAEjHL,EAAA,CAAAgD,SAAA,GAA2B;IAA3BhD,EAAA,CAAAE,UAAA,YAAAuG,MAAA,CAAAJ,aAAA,CAA2B;IAOyBrG,EAAA,CAAAgD,SAAA,GAAkD;IAAlDhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,0BAAkD;IAKtBL,EAAA,CAAAgD,SAAA,GAA4D;IAA5DhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,oCAA4D;IAC1HL,EAAA,CAAAgD,SAAA,GAAiC;IAAjChD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAAX,cAAA,CAAA4B,cAAA,CAAiC;IAG6B1H,EAAA,CAAAgD,SAAA,GAA6D;IAA7DhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,qCAA6D;IAC3HL,EAAA,CAAAgD,SAAA,GAAkC;IAAlChD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAAX,cAAA,CAAA6B,eAAA,CAAkC;IAK4B3H,EAAA,CAAAgD,SAAA,GAA4D;IAA5DhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,oCAA4D;IAC1HL,EAAA,CAAAgD,SAAA,GAAiC;IAAjChD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAAX,cAAA,CAAA8B,cAAA,CAAiC;IAMC5H,EAAA,CAAAgD,SAAA,GAAsD;IAAtDhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,8BAAsD;IAElHL,EAAA,CAAAgD,SAAA,GAA6B;IAA7BhD,EAAA,CAAAE,UAAA,YAAAuG,MAAA,CAAAD,eAAA,CAA6B;IAM2DxG,EAAA,CAAAgD,SAAA,GAA4D;IAA5DhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,oCAA4D;IAC1HL,EAAA,CAAAgD,SAAA,GAAiC;IAAjChD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAAX,cAAA,CAAA+B,cAAA,CAAiC;IAG6B7H,EAAA,CAAAgD,SAAA,GAA2D;IAA3DhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,mCAA2D;IACzHL,EAAA,CAAAgD,SAAA,GAA4B;IAA5BhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAAX,cAAA,CAAAgC,SAAA,CAA4B;IAGkC9H,EAAA,CAAAgD,SAAA,GAA0D;IAA1DhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,kCAA0D;IACxHL,EAAA,CAAAgD,SAAA,GAA+B;IAA/BhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAAX,cAAA,CAAAiC,YAAA,CAA+B;IAG+B/H,EAAA,CAAAgD,SAAA,GAA2D;IAA3DhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,mCAA2D;IACzHL,EAAA,CAAAgD,SAAA,GAAgC;IAAhChD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAAX,cAAA,CAAAkC,aAAA,CAAgC;IAGWhI,EAAA,CAAAgD,SAAA,GAAwD;IAAxDhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAArG,WAAA,CAAAC,SAAA,gCAAwD;IACnGL,EAAA,CAAAgD,SAAA,GAA6B;IAA7BhD,EAAA,CAAAiD,iBAAA,CAAAwD,MAAA,CAAAX,cAAA,CAAAmC,UAAA,CAA6B;IAKiCjI,EAAA,CAAAgD,SAAA,GAAuF;IAAvFhD,EAAA,CAAAkI,kBAAA,2BAAAzB,MAAA,CAAAX,cAAA,CAAAqC,aAAA,oCAAuF;IAGvFnI,EAAA,CAAAgD,SAAA,GAAsD;IAAtDhD,EAAA,CAAAkI,kBAAA,2BAAAzB,MAAA,CAAAX,cAAA,CAAAsC,aAAA,KAAsD;;;;;IAgIjIpI,EAAA,CAAAU,cAAA,gBAAqD;IAAAV,EAAA,CAAAW,MAAA,GAAsB;IAAAX,EAAA,CAAAY,YAAA,EAAQ;;;;IAA9BZ,EAAA,CAAAgD,SAAA,GAAsB;IAAtBhD,EAAA,CAAAiD,iBAAA,CAAAoF,MAAA,CAAAC,kBAAA,CAAsB;;;;;IAoB3GtI,EAAA,CAAAU,cAAA,SAAI;IAC4CV,EAAA,CAAAW,MAAA,GAAgD;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACjGZ,EAAA,CAAAU,cAAA,cAA4C;IAAAV,EAAA,CAAAW,MAAA,GAAiD;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAClGZ,EAAA,CAAAU,cAAA,cAA+C;IAAAV,EAAA,CAAAW,MAAA,GAAkD;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACtGZ,EAAA,CAAAU,cAAA,cAA+D;IAAAV,EAAA,CAAAW,MAAA,GAA+C;IAAAX,EAAA,CAAAY,YAAA,EAAK;;;;IAHvEZ,EAAA,CAAAgD,SAAA,GAAgD;IAAhDhD,EAAA,CAAAiD,iBAAA,CAAAsF,OAAA,CAAAnI,WAAA,CAAAC,SAAA,wBAAgD;IAChDL,EAAA,CAAAgD,SAAA,GAAiD;IAAjDhD,EAAA,CAAAiD,iBAAA,CAAAsF,OAAA,CAAAnI,WAAA,CAAAC,SAAA,yBAAiD;IAC9CL,EAAA,CAAAgD,SAAA,GAAkD;IAAlDhD,EAAA,CAAAiD,iBAAA,CAAAsF,OAAA,CAAAnI,WAAA,CAAAC,SAAA,0BAAkD;IAClCL,EAAA,CAAAgD,SAAA,GAA+C;IAA/ChD,EAAA,CAAAiD,iBAAA,CAAAsF,OAAA,CAAAnI,WAAA,CAAAC,SAAA,uBAA+C;;;;;;IAQlGL,EAAA,CAAAU,cAAA,iBAQE;IALKV,EAAA,CAAAa,UAAA,2BAAA2H,4GAAAzH,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAyH,IAAA;MAAA,MAAAC,aAAA,GAAA1I,EAAA,CAAAmB,aAAA,GAAAwH,SAAA;MAAA,OAAa3I,EAAA,CAAAoB,WAAA,CAAAsH,aAAA,CAAAvF,MAAA,GAAApC,MAAA,CAC/C;IAAA,EADgE,2BAAAyH,4GAAA;MAAAxI,EAAA,CAAAgB,aAAA,CAAAyH,IAAA;MAAA,MAAAC,aAAA,GAAA1I,EAAA,CAAAmB,aAAA,GAAAwH,SAAA;MAAA,MAAAC,OAAA,GAAA5I,EAAA,CAAAmB,aAAA;MAAA,OAIbnB,EAAA,CAAAoB,WAAA,CAAAwH,OAAA,CAAAC,yBAAA,CAAAH,aAAA,CAAoC;IAAA,EAJvB;IAHrC1I,EAAA,CAAAY,YAAA,EAQE;;;;IALKZ,EAAA,CAAAE,UAAA,YAAAwI,aAAA,CAAAvF,MAAA,CAA8B;;;;;IAQrCnD,EAAA,CAAAW,MAAA,GACJ;;;;IADIX,EAAA,CAAAkI,kBAAA,MAAAQ,aAAA,CAAAvF,MAAA,MACJ;;;;;;IAMInD,EAAA,CAAAU,cAAA,iBAQE;IALKV,EAAA,CAAAa,UAAA,2BAAAiI,4GAAA/H,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAA+H,IAAA;MAAA,MAAAL,aAAA,GAAA1I,EAAA,CAAAmB,aAAA,GAAAwH,SAAA;MAAA,OAAa3I,EAAA,CAAAoB,WAAA,CAAAsH,aAAA,CAAAzE,cAAA,GAAAlD,MAAA,CAC/C;IAAA,EADwE,2BAAA+H,4GAAA;MAAA9I,EAAA,CAAAgB,aAAA,CAAA+H,IAAA;MAAA,MAAAL,aAAA,GAAA1I,EAAA,CAAAmB,aAAA,GAAAwH,SAAA;MAAA,MAAAK,OAAA,GAAAhJ,EAAA,CAAAmB,aAAA;MAAA,OAIrBnB,EAAA,CAAAoB,WAAA,CAAA4H,OAAA,CAAAH,yBAAA,CAAAH,aAAA,CAAoC;IAAA,EAJf;IAH7C1I,EAAA,CAAAY,YAAA,EAQE;;;;IALKZ,EAAA,CAAAE,UAAA,YAAAwI,aAAA,CAAAzE,cAAA,CAAsC;;;;;IAQ7CjE,EAAA,CAAAW,MAAA,GACJ;;;;IADIX,EAAA,CAAAkI,kBAAA,MAAAQ,aAAA,CAAAzE,cAAA,MACJ;;;;;IAIAjE,EAAA,CAAAU,cAAA,WACC;IAAAV,EAAA,CAAAW,MAAA,GAAkD;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;;IAAzDZ,EAAA,CAAAgD,SAAA,GAAkD;IAAlDhD,EAAA,CAAAiD,iBAAA,CAAAgG,OAAA,CAAA7I,WAAA,CAAAC,SAAA,CAAAqI,aAAA,CAAA5B,WAAA,EAAkD;;;;;;;;;;IAEvD9G,EAAA,CAAAU,cAAA,WACC;IACOV,EAAA,CAAAW,MAAA,GACJ;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IADHZ,EAAA,CAAAgD,SAAA,GACJ;IADIhD,EAAA,CAAAkI,kBAAA,MAAAgB,OAAA,CAAA9I,WAAA,CAAAC,SAAA,iCAAAL,EAAA,CAAAmJ,eAAA,IAAAC,GAAA,EAAAF,OAAA,CAAA9I,WAAA,CAAAC,SAAA,+BACJ;;;;;IAEJL,EAAA,CAAAU,cAAA,WACC;IACOV,EAAA,CAAAW,MAAA,GACJ;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IADHZ,EAAA,CAAAgD,SAAA,GACJ;IADIhD,EAAA,CAAAkI,kBAAA,MAAAmB,OAAA,CAAAjJ,WAAA,CAAAC,SAAA,iCAAAL,EAAA,CAAAmJ,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAjJ,WAAA,CAAAC,SAAA,gCACJ;;;;;IAEJL,EAAA,CAAAU,cAAA,WACC;IACOV,EAAA,CAAAW,MAAA,GACJ;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IADHZ,EAAA,CAAAgD,SAAA,GACJ;IADIhD,EAAA,CAAAkI,kBAAA,MAAAoB,OAAA,CAAAlJ,WAAA,CAAAC,SAAA,iCACJ;;;;;IAEJL,EAAA,CAAAU,cAAA,WACC;IACOV,EAAA,CAAAW,MAAA,GACJ;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IADHZ,EAAA,CAAAgD,SAAA,GACJ;IADIhD,EAAA,CAAAkI,kBAAA,MAAAqB,OAAA,CAAAnJ,WAAA,CAAAC,SAAA,2CACJ;;;;;IAEJL,EAAA,CAAAU,cAAA,WACC;IACOV,EAAA,CAAAW,MAAA,GACJ;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IADHZ,EAAA,CAAAgD,SAAA,GACJ;IADIhD,EAAA,CAAAkI,kBAAA,MAAAsB,OAAA,CAAApJ,WAAA,CAAAC,SAAA,wCACJ;;;;;;IAhEZL,EAAA,CAAAU,cAAA,cAAuD;IAG3CV,EAAA,CAAAsB,UAAA,IAAAmI,4EAAA,2BAUc;IACdzJ,EAAA,CAAAsB,UAAA,IAAAoI,4EAAA,2BAEc;IAClB1J,EAAA,CAAAY,YAAA,EAAe;IAEnBZ,EAAA,CAAAU,cAAA,cAA+H;IAEvHV,EAAA,CAAAsB,UAAA,IAAAqI,4EAAA,2BAUc;IACd3J,EAAA,CAAAsB,UAAA,IAAAsI,4EAAA,2BAEc;IAClB5J,EAAA,CAAAY,YAAA,EAAe;IAEnBZ,EAAA,CAAAU,cAAA,cAA4C;IACpCV,EAAA,CAAAsB,UAAA,KAAAuI,sEAAA,oBAC0D;IAE9D7J,EAAA,CAAAsB,UAAA,KAAAwI,sEAAA,oBAGW;IAEX9J,EAAA,CAAAsB,UAAA,KAAAyI,sEAAA,oBAGW;IAEX/J,EAAA,CAAAsB,UAAA,KAAA0I,sEAAA,oBAGW;IAEXhK,EAAA,CAAAsB,UAAA,KAAA2I,sEAAA,oBAGW;IAEXjK,EAAA,CAAAsB,UAAA,KAAA4I,sEAAA,oBAGW;IACflK,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAU,cAAA,eAAgE;IACyBV,EAAA,CAAAa,UAAA,mBAAAsJ,sFAAA;MAAA,MAAAC,WAAA,GAAApK,EAAA,CAAAgB,aAAA,CAAAqJ,IAAA;MAAA,MAAA3B,aAAA,GAAA0B,WAAA,CAAAzB,SAAA;MAAA,MAAA2B,KAAA,GAAAF,WAAA,CAAAG,QAAA;MAAA,MAAAC,OAAA,GAAAxK,EAAA,CAAAmB,aAAA;MAAA,OAASnB,EAAA,CAAAoB,WAAA,CAAAoJ,OAAA,CAAAC,mBAAA,CAAA/B,aAAA,EAAA4B,KAAA,CAAgC;IAAA,EAAC;IAACtK,EAAA,CAAAY,YAAA,EAAO;;;;;IAnE3IZ,EAAA,CAAAE,UAAA,cAAAwK,OAAA,CAAAC,iBAAA,CAAAjC,aAAA,CAAAkC,OAAA,EAAkD;IACN5K,EAAA,CAAAgD,SAAA,GAAoC;IAApChD,EAAA,CAAAE,UAAA,oBAAAwI,aAAA,CAAAvF,MAAA,CAAoC;IAkBpCnD,EAAA,CAAAgD,SAAA,GAA4C;IAA5ChD,EAAA,CAAAE,UAAA,oBAAAwI,aAAA,CAAAzE,cAAA,CAA4C;IAmBzEjE,EAAA,CAAAgD,SAAA,GAAmD;IAAnDhD,EAAA,CAAAE,UAAA,UAAAwK,OAAA,CAAAC,iBAAA,CAAAjC,aAAA,CAAAkC,OAAA,EAAAC,OAAA,CAAmD;IAGvD7K,EAAA,CAAAgD,SAAA,GAAuK;IAAvKhD,EAAA,CAAAE,UAAA,SAAAwK,OAAA,CAAAC,iBAAA,CAAAjC,aAAA,CAAAkC,OAAA,EAAAE,QAAA,CAAA3H,MAAA,CAAA4H,QAAA,iBAAAL,OAAA,CAAAC,iBAAA,CAAAjC,aAAA,CAAAkC,OAAA,EAAAE,QAAA,CAAA7G,cAAA,CAAA8G,QAAA,aAAuK;IAKvK/K,EAAA,CAAAgD,SAAA,GAAuK;IAAvKhD,EAAA,CAAAE,UAAA,UAAAwK,OAAA,CAAAC,iBAAA,CAAAjC,aAAA,CAAAkC,OAAA,EAAAE,QAAA,CAAA3H,MAAA,CAAA4H,QAAA,gBAAAL,OAAA,CAAAC,iBAAA,CAAAjC,aAAA,CAAAkC,OAAA,EAAAE,QAAA,CAAA7G,cAAA,CAAA8G,QAAA,aAAuK;IAKvK/K,EAAA,CAAAgD,SAAA,GAAsK;IAAtKhD,EAAA,CAAAE,UAAA,SAAAwK,OAAA,CAAAC,iBAAA,CAAAjC,aAAA,CAAAkC,OAAA,EAAAE,QAAA,CAAA3H,MAAA,CAAA4H,QAAA,gBAAAL,OAAA,CAAAC,iBAAA,CAAAjC,aAAA,CAAAkC,OAAA,EAAAE,QAAA,CAAA7G,cAAA,CAAA8G,QAAA,aAAsK;IAKtK/K,EAAA,CAAAgD,SAAA,GAA0E;IAA1EhD,EAAA,CAAAE,UAAA,SAAAwK,OAAA,CAAAC,iBAAA,CAAAjC,aAAA,CAAAkC,OAAA,EAAAE,QAAA,CAAA3H,MAAA,CAAA6H,MAAA,kBAAAN,OAAA,CAAAC,iBAAA,CAAAjC,aAAA,CAAAkC,OAAA,EAAAE,QAAA,CAAA3H,MAAA,CAAA6H,MAAA,CAAAC,OAAA,CAA0E;IAK1EjL,EAAA,CAAAgD,SAAA,GAAkF;IAAlFhD,EAAA,CAAAE,UAAA,SAAAwK,OAAA,CAAAC,iBAAA,CAAAjC,aAAA,CAAAkC,OAAA,EAAAE,QAAA,CAAA7G,cAAA,CAAA+G,MAAA,kBAAAN,OAAA,CAAAC,iBAAA,CAAAjC,aAAA,CAAAkC,OAAA,EAAAE,QAAA,CAAA7G,cAAA,CAAA+G,MAAA,CAAAC,OAAA,CAAkF;IAMnFjL,EAAA,CAAAgD,SAAA,GAA0D;IAA1DhD,EAAA,CAAAE,UAAA,aAAAwK,OAAA,CAAAtK,WAAA,CAAAC,SAAA,yBAA0D;;;;;;;;;;;;;;;;;;;IA9FhFL,EAAA,CAAAU,cAAA,sBAiB6B;IAVzBV,EAAA,CAAAa,UAAA,oBAAAqK,2EAAAnK,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAmK,IAAA;MAAA,MAAAC,OAAA,GAAApL,EAAA,CAAAmB,aAAA;MAAA,OAAUnB,EAAA,CAAAoB,WAAA,CAAAgK,OAAA,CAAAC,qBAAA,CAAAtK,MAAA,CAA6B;IAAA,EAAC;IAWxCf,EAAA,CAAAsB,UAAA,IAAAgK,8DAAA,0BAOc;IACdtL,EAAA,CAAAsB,UAAA,IAAAiK,8DAAA,4BAuEc;IAClBvL,EAAA,CAAAY,YAAA,EAAU;;;;IAjGNZ,EAAA,CAAAE,UAAA,mBAAkB,SAAAsL,MAAA,CAAAC,iBAAA,WAAAD,MAAA,CAAAE,iBAAA,+CAAA1L,EAAA,CAAAQ,eAAA,KAAAmL,GAAA,gCAAAH,MAAA,CAAApL,WAAA,CAAAC,SAAA,8DAAAL,EAAA,CAAAQ,eAAA,KAAAoL,GAAA,mDAAAJ,MAAA,CAAAK,gBAAA,kBAAAL,MAAA,CAAAK,gBAAA,CAAAC,MAAA,kDAAAN,MAAA,CAAAO,UAAA,gBAAA/L,EAAA,CAAAQ,eAAA,KAAAwL,GAAA;;;;;;IAoGlBhM,EAAA,CAAAU,cAAA,oBAA+K;IAA5BV,EAAA,CAAAa,UAAA,mBAAAoL,4EAAA;MAAAjM,EAAA,CAAAgB,aAAA,CAAAkL,IAAA;MAAA,MAAAC,OAAA,GAAAnM,EAAA,CAAAmB,aAAA;MAAA,OAASnB,EAAA,CAAAoB,WAAA,CAAA+K,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAACpM,EAAA,CAAAY,YAAA,EAAW;;;;IAAhLZ,EAAA,CAAAE,UAAA,cAAAmM,MAAA,CAAAC,oBAAA,GAAoC,UAAAD,MAAA,CAAAjM,WAAA,CAAAC,SAAA;;;;;IAoBVL,EAAA,CAAAU,cAAA,gBAAsD;IAAAV,EAAA,CAAAW,MAAA,GAAsB;IAAAX,EAAA,CAAAY,YAAA,EAAQ;;;;IAA9BZ,EAAA,CAAAgD,SAAA,GAAsB;IAAtBhD,EAAA,CAAAiD,iBAAA,CAAAsJ,MAAA,CAAAjE,kBAAA,CAAsB;;;;;IAoB5GtI,EAAA,CAAAU,cAAA,SAAI;IAC4CV,EAAA,CAAAW,MAAA,GAAgD;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACjGZ,EAAA,CAAAU,cAAA,cAA4C;IAAAV,EAAA,CAAAW,MAAA,GAAiD;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAClGZ,EAAA,CAAAU,cAAA,cAA+C;IAAAV,EAAA,CAAAW,MAAA,GAAkD;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACtGZ,EAAA,CAAAU,cAAA,cAA+D;IAAAV,EAAA,CAAAW,MAAA,GAA+C;IAAAX,EAAA,CAAAY,YAAA,EAAK;;;;IAHvEZ,EAAA,CAAAgD,SAAA,GAAgD;IAAhDhD,EAAA,CAAAiD,iBAAA,CAAAuJ,OAAA,CAAApM,WAAA,CAAAC,SAAA,wBAAgD;IAChDL,EAAA,CAAAgD,SAAA,GAAiD;IAAjDhD,EAAA,CAAAiD,iBAAA,CAAAuJ,OAAA,CAAApM,WAAA,CAAAC,SAAA,yBAAiD;IAC9CL,EAAA,CAAAgD,SAAA,GAAkD;IAAlDhD,EAAA,CAAAiD,iBAAA,CAAAuJ,OAAA,CAAApM,WAAA,CAAAC,SAAA,0BAAkD;IAClCL,EAAA,CAAAgD,SAAA,GAA+C;IAA/ChD,EAAA,CAAAiD,iBAAA,CAAAuJ,OAAA,CAAApM,WAAA,CAAAC,SAAA,uBAA+C;;;;;;IAQ9FL,EAAA,CAAAU,cAAA,iBAQE;IALEV,EAAA,CAAAa,UAAA,2BAAA4L,4GAAA1L,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAA0L,IAAA;MAAA,MAAAC,aAAA,GAAA3M,EAAA,CAAAmB,aAAA,GAAAwH,SAAA;MAAA,OAAa3I,EAAA,CAAAoB,WAAA,CAAAuL,aAAA,CAAAxJ,MAAA,GAAApC,MAAA,CAChD;IAAA,EADiE,2BAAA0L,4GAAA;MAAAzM,EAAA,CAAAgB,aAAA,CAAA0L,IAAA;MAAA,MAAAC,aAAA,GAAA3M,EAAA,CAAAmB,aAAA,GAAAwH,SAAA;MAAA,MAAAiE,OAAA,GAAA5M,EAAA,CAAAmB,aAAA;MAAA,OAIbnB,EAAA,CAAAoB,WAAA,CAAAwL,OAAA,CAAA/D,yBAAA,CAAA8D,aAAA,CAAoC;IAAA,EAJvB;IAHlC3M,EAAA,CAAAY,YAAA,EAQE;;;;IALEZ,EAAA,CAAAE,UAAA,YAAAyM,aAAA,CAAAxJ,MAAA,CAA8B;;;;;IAQlCnD,EAAA,CAAAW,MAAA,GACJ;;;;IADIX,EAAA,CAAAkI,kBAAA,MAAAyE,aAAA,CAAAxJ,MAAA,MACJ;;;;;;IAMInD,EAAA,CAAAU,cAAA,iBAQE;IALEV,EAAA,CAAAa,UAAA,2BAAAgM,4GAAA9L,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAA8L,IAAA;MAAA,MAAAH,aAAA,GAAA3M,EAAA,CAAAmB,aAAA,GAAAwH,SAAA;MAAA,OAAa3I,EAAA,CAAAoB,WAAA,CAAAuL,aAAA,CAAA1I,cAAA,GAAAlD,MAAA,CAChD;IAAA,EADyE,2BAAA8L,4GAAA;MAAA7M,EAAA,CAAAgB,aAAA,CAAA8L,IAAA;MAAA,MAAAH,aAAA,GAAA3M,EAAA,CAAAmB,aAAA,GAAAwH,SAAA;MAAA,MAAAoE,OAAA,GAAA/M,EAAA,CAAAmB,aAAA;MAAA,OAIrBnB,EAAA,CAAAoB,WAAA,CAAA2L,OAAA,CAAAlE,yBAAA,CAAA8D,aAAA,CAAoC;IAAA,EAJf;IAH1C3M,EAAA,CAAAY,YAAA,EAQE;;;;IALEZ,EAAA,CAAAE,UAAA,YAAAyM,aAAA,CAAA1I,cAAA,CAAsC;;;;;IAQ1CjE,EAAA,CAAAW,MAAA,GACJ;;;;IADIX,EAAA,CAAAkI,kBAAA,MAAAyE,aAAA,CAAA1I,cAAA,MACJ;;;;;IAIJjE,EAAA,CAAAU,cAAA,WACC;IAAAV,EAAA,CAAAW,MAAA,GAAkD;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;;IAAzDZ,EAAA,CAAAgD,SAAA,GAAkD;IAAlDhD,EAAA,CAAAiD,iBAAA,CAAA+J,OAAA,CAAA5M,WAAA,CAAAC,SAAA,CAAAsM,aAAA,CAAA7F,WAAA,EAAkD;;;;;IAEnD9G,EAAA,CAAAU,cAAA,WACC;IACGV,EAAA,CAAAW,MAAA,GACJ;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IADHZ,EAAA,CAAAgD,SAAA,GACJ;IADIhD,EAAA,CAAAkI,kBAAA,MAAA+E,OAAA,CAAA7M,WAAA,CAAAC,SAAA,iCAAAL,EAAA,CAAAmJ,eAAA,IAAAC,GAAA,EAAA6D,OAAA,CAAA7M,WAAA,CAAAC,SAAA,+BACJ;;;;;IAEAL,EAAA,CAAAU,cAAA,WACC;IACGV,EAAA,CAAAW,MAAA,GACJ;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IADHZ,EAAA,CAAAgD,SAAA,GACJ;IADIhD,EAAA,CAAAkI,kBAAA,MAAAgF,OAAA,CAAA9M,WAAA,CAAAC,SAAA,iCAAAL,EAAA,CAAAmJ,eAAA,IAAAC,GAAA,EAAA8D,OAAA,CAAA9M,WAAA,CAAAC,SAAA,gCACJ;;;;;IAEAL,EAAA,CAAAU,cAAA,WACC;IACGV,EAAA,CAAAW,MAAA,GACJ;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IADHZ,EAAA,CAAAgD,SAAA,GACJ;IADIhD,EAAA,CAAAkI,kBAAA,MAAAiF,OAAA,CAAA/M,WAAA,CAAAC,SAAA,iCACJ;;;;;IAEAL,EAAA,CAAAU,cAAA,WACC;IACGV,EAAA,CAAAW,MAAA,GACJ;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IADHZ,EAAA,CAAAgD,SAAA,GACJ;IADIhD,EAAA,CAAAkI,kBAAA,MAAAkF,OAAA,CAAAhN,WAAA,CAAAC,SAAA,2CACJ;;;;;IAEAL,EAAA,CAAAU,cAAA,WACC;IACGV,EAAA,CAAAW,MAAA,GACJ;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;IADHZ,EAAA,CAAAgD,SAAA,GACJ;IADIhD,EAAA,CAAAkI,kBAAA,MAAAmF,OAAA,CAAAjN,WAAA,CAAAC,SAAA,wCACJ;;;;;;IAhERL,EAAA,CAAAU,cAAA,cAAuD;IAG3CV,EAAA,CAAAsB,UAAA,IAAAgM,4EAAA,2BAUc;IACdtN,EAAA,CAAAsB,UAAA,IAAAiM,4EAAA,2BAEc;IAClBvN,EAAA,CAAAY,YAAA,EAAe;IAEnBZ,EAAA,CAAAU,cAAA,cAA+H;IAEvHV,EAAA,CAAAsB,UAAA,IAAAkM,4EAAA,2BAUc;IACdxN,EAAA,CAAAsB,UAAA,IAAAmM,4EAAA,2BAEc;IAClBzN,EAAA,CAAAY,YAAA,EAAe;IAEnBZ,EAAA,CAAAU,cAAA,cAA4C;IACxCV,EAAA,CAAAsB,UAAA,KAAAoM,sEAAA,oBAC0D;IAE1D1N,EAAA,CAAAsB,UAAA,KAAAqM,sEAAA,oBAGO;IAEP3N,EAAA,CAAAsB,UAAA,KAAAsM,sEAAA,oBAGO;IAEP5N,EAAA,CAAAsB,UAAA,KAAAuM,sEAAA,oBAGO;IAEP7N,EAAA,CAAAsB,UAAA,KAAAwM,sEAAA,oBAGO;IAEP9N,EAAA,CAAAsB,UAAA,KAAAyM,sEAAA,oBAGO;IACX/N,EAAA,CAAAY,YAAA,EAAK;IACLZ,EAAA,CAAAU,cAAA,eAAgE;IACyBV,EAAA,CAAAa,UAAA,mBAAAmN,sFAAA;MAAA,MAAA5D,WAAA,GAAApK,EAAA,CAAAgB,aAAA,CAAAiN,IAAA;MAAA,MAAAtB,aAAA,GAAAvC,WAAA,CAAAzB,SAAA;MAAA,MAAAuF,KAAA,GAAA9D,WAAA,CAAAG,QAAA;MAAA,MAAA4D,OAAA,GAAAnO,EAAA,CAAAmB,aAAA;MAAA,OAASnB,EAAA,CAAAoB,WAAA,CAAA+M,OAAA,CAAA1D,mBAAA,CAAAkC,aAAA,EAAAuB,KAAA,CAAgC;IAAA,EAAC;IAAClO,EAAA,CAAAY,YAAA,EAAO;;;;;IAnE3IZ,EAAA,CAAAE,UAAA,cAAAkO,OAAA,CAAAzD,iBAAA,CAAAgC,aAAA,CAAA/B,OAAA,EAAkD;IACN5K,EAAA,CAAAgD,SAAA,GAAoC;IAApChD,EAAA,CAAAE,UAAA,oBAAAyM,aAAA,CAAAxJ,MAAA,CAAoC;IAkBpCnD,EAAA,CAAAgD,SAAA,GAA4C;IAA5ChD,EAAA,CAAAE,UAAA,oBAAAyM,aAAA,CAAA1I,cAAA,CAA4C;IAmB7EjE,EAAA,CAAAgD,SAAA,GAAmD;IAAnDhD,EAAA,CAAAE,UAAA,UAAAkO,OAAA,CAAAzD,iBAAA,CAAAgC,aAAA,CAAA/B,OAAA,EAAAC,OAAA,CAAmD;IAGnD7K,EAAA,CAAAgD,SAAA,GAAuK;IAAvKhD,EAAA,CAAAE,UAAA,SAAAkO,OAAA,CAAAzD,iBAAA,CAAAgC,aAAA,CAAA/B,OAAA,EAAAE,QAAA,CAAA3H,MAAA,CAAA4H,QAAA,iBAAAqD,OAAA,CAAAzD,iBAAA,CAAAgC,aAAA,CAAA/B,OAAA,EAAAE,QAAA,CAAA7G,cAAA,CAAA8G,QAAA,aAAuK;IAKvK/K,EAAA,CAAAgD,SAAA,GAAuK;IAAvKhD,EAAA,CAAAE,UAAA,UAAAkO,OAAA,CAAAzD,iBAAA,CAAAgC,aAAA,CAAA/B,OAAA,EAAAE,QAAA,CAAA3H,MAAA,CAAA4H,QAAA,gBAAAqD,OAAA,CAAAzD,iBAAA,CAAAgC,aAAA,CAAA/B,OAAA,EAAAE,QAAA,CAAA7G,cAAA,CAAA8G,QAAA,aAAuK;IAKvK/K,EAAA,CAAAgD,SAAA,GAAsK;IAAtKhD,EAAA,CAAAE,UAAA,SAAAkO,OAAA,CAAAzD,iBAAA,CAAAgC,aAAA,CAAA/B,OAAA,EAAAE,QAAA,CAAA3H,MAAA,CAAA4H,QAAA,gBAAAqD,OAAA,CAAAzD,iBAAA,CAAAgC,aAAA,CAAA/B,OAAA,EAAAE,QAAA,CAAA7G,cAAA,CAAA8G,QAAA,aAAsK;IAKtK/K,EAAA,CAAAgD,SAAA,GAA0E;IAA1EhD,EAAA,CAAAE,UAAA,SAAAkO,OAAA,CAAAzD,iBAAA,CAAAgC,aAAA,CAAA/B,OAAA,EAAAE,QAAA,CAAA3H,MAAA,CAAA6H,MAAA,kBAAAoD,OAAA,CAAAzD,iBAAA,CAAAgC,aAAA,CAAA/B,OAAA,EAAAE,QAAA,CAAA3H,MAAA,CAAA6H,MAAA,CAAAC,OAAA,CAA0E;IAK1EjL,EAAA,CAAAgD,SAAA,GAAkF;IAAlFhD,EAAA,CAAAE,UAAA,SAAAkO,OAAA,CAAAzD,iBAAA,CAAAgC,aAAA,CAAA/B,OAAA,EAAAE,QAAA,CAAA7G,cAAA,CAAA+G,MAAA,kBAAAoD,OAAA,CAAAzD,iBAAA,CAAAgC,aAAA,CAAA/B,OAAA,EAAAE,QAAA,CAAA7G,cAAA,CAAA+G,MAAA,CAAAC,OAAA,CAAkF;IAMnFjL,EAAA,CAAAgD,SAAA,GAA0D;IAA1DhD,EAAA,CAAAE,UAAA,aAAAkO,OAAA,CAAAhO,WAAA,CAAAC,SAAA,yBAA0D;;;;;;IA9FpFL,EAAA,CAAAU,cAAA,sBAiB6B;IAVzBV,EAAA,CAAAa,UAAA,oBAAAwN,2EAAAtN,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAsN,IAAA;MAAA,MAAAC,OAAA,GAAAvO,EAAA,CAAAmB,aAAA;MAAA,OAAUnB,EAAA,CAAAoB,WAAA,CAAAmN,OAAA,CAAAlD,qBAAA,CAAAtK,MAAA,CAA6B;IAAA,EAAC;IAWxCf,EAAA,CAAAsB,UAAA,IAAAkN,8DAAA,0BAOc;IACdxO,EAAA,CAAAsB,UAAA,IAAAmN,8DAAA,4BAuEc;IAClBzO,EAAA,CAAAY,YAAA,EAAU;;;;IAjGNZ,EAAA,CAAAE,UAAA,mBAAkB,SAAAwO,MAAA,CAAAjD,iBAAA,WAAAiD,MAAA,CAAAhD,iBAAA,+CAAA1L,EAAA,CAAAQ,eAAA,KAAAmL,GAAA,gCAAA+C,MAAA,CAAAtO,WAAA,CAAAC,SAAA,8DAAAL,EAAA,CAAAQ,eAAA,KAAAoL,GAAA,mDAAA8C,MAAA,CAAA7C,gBAAA,kBAAA6C,MAAA,CAAA7C,gBAAA,CAAAC,MAAA,kDAAA4C,MAAA,CAAA3C,UAAA,gBAAA/L,EAAA,CAAAQ,eAAA,KAAAwL,GAAA;;;;;;IAoGlBhM,EAAA,CAAAU,cAAA,oBAA+K;IAA5BV,EAAA,CAAAa,UAAA,mBAAA8N,4EAAA;MAAA3O,EAAA,CAAAgB,aAAA,CAAA4N,KAAA;MAAA,MAAAC,QAAA,GAAA7O,EAAA,CAAAmB,aAAA;MAAA,OAASnB,EAAA,CAAAoB,WAAA,CAAAyN,QAAA,CAAAzC,eAAA,EAAiB;IAAA,EAAC;IAACpM,EAAA,CAAAY,YAAA,EAAW;;;;IAAhLZ,EAAA,CAAAE,UAAA,cAAA4O,MAAA,CAAAxC,oBAAA,GAAoC,UAAAwC,MAAA,CAAA1O,WAAA,CAAAC,SAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AD9vB1D,OAAM,MAAO0O,4BAA6B,SAAQhP,aAAa;EAyH3DiP,YAAoBC,iBAAoC,EACpCC,eAAgC,EAChCC,eAAgC,EAChCC,UAAsB,EACtBC,WAAwB,EACxBC,cAA8B,EACtCC,QAAkB;IAC1B,KAAK,CAACA,QAAQ,CAAC;IAPC,KAAAN,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IA9GlC,KAAAE,cAAc,GAAe,EAAE;IAC/B,KAAAC,oBAAoB,GAAe,EAAE;IAErC,KAAAC,WAAW,GAAe,EAAE;IAW5B,KAAAC,WAAW,GAAkC,IAAIC,IAAI,EAAE;IACvD,KAAAC,SAAS,GAAkC,IAAI;IAC/C,KAAAC,SAAS,GAAkC,IAAIF,IAAI,EAAE;IAGrD,KAAAG,uBAAuB,GAAY,KAAK;IAKxC,KAAAC,4BAA4B,GAAY,KAAK;IAG7C,KAAAC,kCAAkC,GAAY,KAAK;IAcnD,KAAAC,8BAA8B,GAAY,KAAK;IAU/C,KAAAzE,iBAAiB,GAAW,EAAE;IAC9B,KAAAC,iBAAiB,GAAW,CAAC;IAC7B,KAAAyE,iBAAiB,GAAY,KAAK;IAClC,KAAAC,gBAAgB,GAAY,KAAK;IAGjC,KAAAC,cAAc,GAAGvQ,SAAS,CAACwQ,WAAW;IACtC,KAAAjP,oBAAoB,GAAY,KAAK;IACrC,KAAA6B,SAAS,GAAQ,EAAE;IACnB,KAAAtB,eAAe,GAAQ,EAAE;IACzB,KAAAuD,cAAc,GAAQ,EAAE;IACxB,KAAAb,gBAAgB,GAAQ,EAAE;IAC1B,KAAAG,cAAc,GAAQ,EAAE;IACxB,KAAA8L,SAAS,GAAQ,EAAE;IAEnB,KAAA5K,qBAAqB,GAAY,KAAK;IA+BtC,KAAAsB,iBAAiB,GAAQ,EAAE;IAI3B,KAAAuJ,QAAQ,GAAQ,EAAE;IAClB,KAAAnJ,UAAU,GAAGvH,SAAS,CAAC2Q,iBAAiB;IACxC,KAAAC,kBAAkB,GAAqC,UAAU;IAymC9C,KAAA5Q,SAAS,GAAGA,SAAS;EA/lCxC;EAEA6Q,QAAQA,CAAA;IACJ,IAAIC,EAAE,GAAG,IAAI;IACb,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACC,QAAQ,CAACC,IAAI;IACjD,IAAI,CAACC,KAAK,GAAG,CAAC;MAACC,KAAK,EAAE,IAAI,CAAC9Q,WAAW,CAACC,SAAS,CAAC,4BAA4B;IAAC,CAAC,EAAE;MAAC6Q,KAAK,EAAE,IAAI,CAAC9Q,WAAW,CAACC,SAAS,CAAC,0BAA0B;IAAC,CAAC,CAAC;IACjJ,IAAI,CAAC8Q,IAAI,GAAG;MAACC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAC;IACjD,IAAI,CAAC/Q,aAAa,GAAG,CACjB;MACI4Q,KAAK,EAAE,IAAI,CAAC9Q,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC;MACvEiR,OAAO,EAAEA,CAAA,KAAK;QACVV,EAAE,CAACZ,4BAA4B,GAAG,IAAI;MAC1C,CAAC;MACDuB,OAAO,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC1R,SAAS,CAACwQ,WAAW,CAACmB,eAAe,CAACC,gBAAgB,CAAC;KACrF,EACD;MACIR,KAAK,EAAE,IAAI,CAAC9Q,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;MACrEiR,OAAO,EAAEA,CAAA,KAAK;QACVV,EAAE,CAACV,8BAA8B,GAAG,IAAI;QACxC;QACAU,EAAE,CAACe,iBAAiB,CAACC,IAAI,CAAC9R,SAAS,CAAC+R,UAAU,CAACC,mBAAmB,EAAE,EAAE,CAAC;QACvElB,EAAE,CAAC/E,gBAAgB,GAAGjI,SAAS;QAC/BgN,EAAE,CAACT,iBAAiB,GAAG,KAAK;MAChC,CAAC;MACDoB,OAAO,EAAE,IAAI,CAACC,WAAW,CAAC,CAAC1R,SAAS,CAACwQ,WAAW,CAACmB,eAAe,CAACM,gBAAgB,CAAC;KACrF,CACJ;IACD,IAAI,CAAC7O,SAAS,GAAG,EAAE;IACnB,IAAI,CAAC4C,cAAc,GAAG;MAClBkM,EAAE,EAAE,IAAI;MACR3M,IAAI,EAAE,IAAI;MACVD,IAAI,EAAE,IAAI;MACV9B,MAAM,EAAE,IAAI;MACZqD,YAAY,EAAE,IAAI;MAClBE,YAAY,EAAE,IAAI;MAClBG,eAAe,EAAE,IAAI;MACrBjB,gBAAgB,EAAE,IAAI;MACtBqB,WAAW,EAAE,IAAI;MACjBI,aAAa,EAAE,IAAI;MACnBC,aAAa,EAAE,IAAI;MACnBwK,MAAM,EAAE,IAAI;MACZC,IAAI,EAAE,IAAI;MACVxK,cAAc,EAAE,IAAI;MACpBC,eAAe,EAAE,IAAI;MACrBC,cAAc,EAAE,IAAI;MACpBuK,QAAQ,EAAE,IAAI;MACdtK,cAAc,EAAE,IAAI;MACpBO,aAAa,EAAE,IAAI;MACnBL,YAAY,EAAE,IAAI;MAClBC,aAAa,EAAE,IAAI;MACnBC,UAAU,EAAE,IAAI;MAChBE,aAAa,EAAE,IAAI;MACnBL,SAAS,EAAE,IAAI;MACfsK,YAAY,EAAE,IAAI;MAClBtL,WAAW,EAAE;KAChB;IACD,IAAI,CAACuL,UAAU,GAAG;IACd;IACA;IACA;IACA;IACA;MACIC,KAAK,EAAE,CAACxS,SAAS,CAACyS,UAAU,CAACC,SAAS,CAAC;MACvCpN,IAAI,EAAE,IAAI,CAAChF,WAAW,CAACC,SAAS,CAAC,sBAAsB;KAC1D,EACD;MACIiS,KAAK,EAAE,CAACxS,SAAS,CAACyS,UAAU,CAACE,SAAS,CAAC;MACvCrN,IAAI,EAAE,IAAI,CAAChF,WAAW,CAACC,SAAS,CAAC,wBAAwB;KAC5D,EACD;MACIiS,KAAK,EAAE,CAACxS,SAAS,CAACyS,UAAU,CAACG,WAAW,CAAC;MACzCtN,IAAI,EAAE,IAAI,CAAChF,WAAW,CAACC,SAAS,CAAC,wBAAwB;KAC5D,EACD;MACIiS,KAAK,EAAE,CAACxS,SAAS,CAACyS,UAAU,CAACI,MAAM,CAAC;MACpCvN,IAAI,EAAE,IAAI,CAAChF,WAAW,CAACC,SAAS,CAAC,mBAAmB;KACvD,EACD;MACIiS,KAAK,EAAE,CAAC,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,EAAE,EAAE,GAAG1S,SAAS,CAACyS,UAAU,CAACK,KAAK,CAAC;MAC7ExN,IAAI,EAAE,IAAI,CAAChF,WAAW,CAACC,SAAS,CAAC,iCAAiC;KACrE,EACD;MACIiS,KAAK,EAAE,CAAC,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,EAAE,EAAE,GAAG1S,SAAS,CAACyS,UAAU,CAACK,KAAK,CAAC;MAC7ExN,IAAI,EAAE,IAAI,CAAChF,WAAW,CAACC,SAAS,CAAC,mCAAmC;KACvE,EACD;MACIiS,KAAK,EAAE,CAAC,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,EAAE,EAAE,GAAG1S,SAAS,CAACyS,UAAU,CAACK,KAAK,CAAC;MAC7ExN,IAAI,EAAE,IAAI,CAAChF,WAAW,CAACC,SAAS,CAAC,8BAA8B;KAClE,CACJ;IAED,IAAI,CAACwS,UAAU,GAAG;MACd1P,MAAM,EAAE,IAAI;MACZG,MAAM,EAAE,IAAI;MACZG,IAAI,EAAE,IAAI;MACVqP,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,IAAI;MACdrO,YAAY,EAAE,IAAI;MAClBsO,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE;KACX;IACD,IAAI,CAACC,UAAU,GAAG,IAAI,CAAC7D,WAAW,CAAC8D,KAAK,CAAC,IAAI,CAACN,UAAU,CAAC;IACzD,IAAI,CAACnD,WAAW,GAAG,EAAE;IACrB,IAAI,CAAC0D,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB;IACA,IAAI,CAACC,IAAI,GAAG,oBAAoB;IAEhC,IAAI,CAACC,WAAW,GAAG;MACfC,gBAAgB,EAAE,IAAI;MACtBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE,KAAK;MAC1BC,MAAM,EAAE,CACJ;QACIxC,IAAI,EAAE,YAAY;QAClByC,OAAO,EAAE,IAAI,CAACzT,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;QACvEyT,IAAI,EAAE,SAAAA,CAAU9B,EAAE,EAAE+B,IAAI;UACpBnD,EAAE,CAACoD,WAAW,GAAGD,IAAI;UACrBnD,EAAE,CAACqD,0BAA0B,GAAGrD,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;UAC5FuQ,EAAE,CAACF,kBAAkB,GAAG,UAAU;UAClCE,EAAE,CAACsD,+BAA+B,CAACH,IAAI,CAACI,YAAY,CAAC;UACrD;UACAvD,EAAE,CAACb,uBAAuB,GAAG,IAAI;QACrC,CAAC;QACDqE,UAAU,EAAE,SAAAA,CAAUpC,EAAE,EAAE+B,IAAI;UAC1B,OAAOnD,EAAE,CAACnB,oBAAoB,KAAKsE,IAAI,CAACzQ,MAAM,IAAIxD,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIuB,IAAI,CAACzQ,MAAM,IAAIxD,SAAS,CAACyS,UAAU,CAACK,KAAK,CAAC,IAAKmB,IAAI,CAACjB,YAAY,IAAI,IAAK,IACtJlC,EAAE,CAACpB,cAAc,CAAC1D,MAAM,GAAG,CAAC,IAAI8E,EAAE,CAACY,WAAW,CAAC,CAAC1R,SAAS,CAACwQ,WAAW,CAACmB,eAAe,CAAC4C,aAAa,CAAC,CAAC;QAChH;OACH,EACD;QACIjD,IAAI,EAAE,YAAY;QAClByC,OAAO,EAAE,IAAI,CAACzT,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC;QACrEyT,IAAI,EAAE,SAAAA,CAAU9B,EAAE,EAAE+B,IAAI;UACpBnD,EAAE,CAACoD,WAAW,GAAGD,IAAI;UACrBnD,EAAE,CAACqD,0BAA0B,GAAGrD,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC;UAC1FuQ,EAAE,CAACF,kBAAkB,GAAG,QAAQ;UAChCE,EAAE,CAACsD,+BAA+B,CAACH,IAAI,CAACI,YAAY,CAAC;UACrDvD,EAAE,CAACnB,oBAAoB,GAAGmB,EAAE,CAACnB,oBAAoB,CAAC6E,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACvC,EAAE,IAAI+B,IAAI,CAACjB,YAAY,CAAC;UAC1FlC,EAAE,CAACb,uBAAuB,GAAG,IAAI;QACrC,CAAC;QACDqE,UAAU,EAAE,SAAAA,CAAUpC,EAAE,EAAE+B,IAAI;UAC1B,OAAOnD,EAAE,CAACnB,oBAAoB,IAAIsE,IAAI,CAACjB,YAAY,IAAI,IAAI,KAAKiB,IAAI,CAACzQ,MAAM,IAAIxD,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIuB,IAAI,CAACzQ,MAAM,IAAIxD,SAAS,CAACyS,UAAU,CAACK,KAAK,CAAC,IAAIhC,EAAE,CAACpB,cAAc,CAAC1D,MAAM,GAAG,CAAC,IAAI8E,EAAE,CAACY,WAAW,CAAC,CAAC1R,SAAS,CAACwQ,WAAW,CAACmB,eAAe,CAAC4C,aAAa,CAAC,CAAC;QACxQ;OACH,EACD;QACIjD,IAAI,EAAE,aAAa;QACnByC,OAAO,EAAE,IAAI,CAACzT,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC;QACrEyT,IAAI,EAAE,SAAAA,CAAU9B,EAAE,EAAE+B,IAAI;UACpBnD,EAAE,CAACoD,WAAW,GAAGD,IAAI;UACrBnD,EAAE,CAACF,kBAAkB,GAAG,QAAQ;UAChCE,EAAE,CAAC4D,oBAAoB,CAACC,OAAO,CAAC7D,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,EAAEuQ,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,kCAAkC,EAAE;YACrJqU,QAAQ,EAAEX,IAAI,CAAC9P,cAAc;YAC7Bd,MAAM,EAAE4Q,IAAI,CAAC5Q;WAChB,CAAC,EAAE;YACAwR,EAAE,EAAEA,CAAA,KAAK;cACL/D,EAAE,CAAC3B,iBAAiB,CAAC2F,sBAAsB,CAACb,IAAI,CAAC5Q,MAAM,EAAE4Q,IAAI,CAACjB,YAAY,EAAE,MAAK;gBAC7ElC,EAAE,CAAC4D,oBAAoB,CAACK,OAAO,CAACjE,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC,CAAC;gBAC9FuQ,EAAE,CAACkE,MAAM,CAAClE,EAAE,CAACwC,UAAU,EAAExC,EAAE,CAACyC,QAAQ,EAAEzC,EAAE,CAAC0C,IAAI,EAAE1C,EAAE,CAACiC,UAAU,CAAC;cACjE,CAAC,CAAC;YACN;WACH,CAAC;QACN,CAAC;QACDuB,UAAU,EAAE,SAAAA,CAAUpC,EAAE,EAAE+B,IAAI;UAC1B,OAAOnD,EAAE,CAACnB,oBAAoB,IAAIsE,IAAI,CAACjB,YAAY,IAAI,IAAI,KAAKiB,IAAI,CAACzQ,MAAM,IAAIxD,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIuB,IAAI,CAACzQ,MAAM,IAAIxD,SAAS,CAACyS,UAAU,CAACK,KAAK,CAAC,IAAIhC,EAAE,CAACpB,cAAc,CAAC1D,MAAM,GAAG,CAAC,IAAI8E,EAAE,CAACY,WAAW,CAAC,CAAC1R,SAAS,CAACwQ,WAAW,CAACmB,eAAe,CAAC4C,aAAa,CAAC,CAAC;QACxQ;OACH;KAER,EACG,IAAI,CAACU,OAAO,GAAG,CACX;MACI3P,IAAI,EAAE,IAAI,CAAChF,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MACvD2U,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE;QACHC,MAAM,EAAE,SAAS;QACjBC,KAAK,EAAE;OACV;MACDC,SAASA,CAACxD,EAAE,EAAE+B,IAAI;QACdnD,EAAE,CAAC6E,KAAK,GAAG1B,IAAI,EAAE5Q,MAAM,CAACuS,QAAQ,EAAE;QAClC9E,EAAE,CAAC+E,YAAY,EAAE;QACjB/E,EAAE,CAACvP,oBAAoB,GAAG,IAAI;MAClC;KACH,EACD;MACI+D,IAAI,EAAE,IAAI,CAAChF,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1D2U,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZQ,gBAAgB,EAAGtD,KAAK,IAAI;QACxB,IAAIA,KAAK,IAAI,CAAC,EAAE;UACZ,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,CAAC;SAC7E,MAAM,IAAIA,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;UAC5C;UACA,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC;SACnF,MAAM,IAAIN,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACC,SAAS,EAAE;UAChD,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC;SACnF,MAAM,IAAIF,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACE,SAAS,EAAE;UAChD,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,CAAC;SACrF,MAAM,IAAIH,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACG,WAAW,EAAE;UAClD,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,CAAC;SACrF,MAAM,IAAIJ,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACI,MAAM,EAAE;UAC7C,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,CAAC;SAC/E,MAAM,IAAIL,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;UACjG,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,CAAC;SACjF,MAAM,IAAIN,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;UACjG,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,CAAC;SACjF,MAAM,IAAIN,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;UACjG,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,CAAC;;QAEtF,OAAO,EAAE;MACb,CAAC;MACDiD,eAAe,EAAGvD,KAAK,IAAI;QACvB,IAAIA,KAAK,IAAI,CAAC,EAAE;UACZ,OAAO1B,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SAC1D,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;UAC5C;UACA,OAAOhC,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SAC1D,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACC,SAAS,EAAE;UAChD,OAAO5B,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SAC1D,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACG,WAAW,EAAE;UAClD,OAAO9B,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;SAC5D,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACI,MAAM,EAAE;UAC7C,OAAO/B,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;SACvD,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACE,SAAS,EAAE;UAChD,OAAO7B,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;SAC5D,MAAM,IAAIiS,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;UACjG,OAAO,IAAI,CAACxS,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;SACvE,MAAM,IAAIiS,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;UACjG,OAAO,IAAI,CAACxS,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC;SACzE,MAAM,IAAIiS,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;UACjG,OAAO,IAAI,CAACxS,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC;;QAErE,OAAO,EAAE;MACb,CAAC;MACDgV,KAAK,EAAE;QACHE,KAAK,EAAE;;KAEd,EACD;MACInQ,IAAI,EAAE,IAAI,CAAChF,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;MACrD2U,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE;QACHC,MAAM,EAAE,SAAS;QACjBC,KAAK,EAAE;OACV;MACDC,SAASA,CAACxD,EAAE,EAAE+B,IAAI;QACdnD,EAAE,CAACkF,MAAM,GAAG/B,IAAI,EAAEjB,YAAY;QAC9BlC,EAAE,CAACmF,aAAa,EAAE;QAClBnF,EAAE,CAACoF,qBAAqB,EAAE;QAC1BpF,EAAE,CAACjL,qBAAqB,GAAG,IAAI;MACnC,CAAC;MACDsQ,SAAS,EAAE;KACd,EACD;MACI7Q,IAAI,EAAE,IAAI,CAAChF,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACxD2U,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIhQ,IAAI,EAAE,IAAI,CAAChF,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MACvD2U,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZa,SAAS,EAAE;KACd,EACD;MACI7Q,IAAI,EAAE,IAAI,CAAChF,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MACvD2U,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIhQ,IAAI,EAAE,IAAI,CAAChF,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC5D2U,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZS,eAAeA,CAACvD,KAAK;QACjB,OAAO1B,EAAE,CAACxM,WAAW,CAAC8R,uBAAuB,CAAC5D,KAAK,CAAC;MACxD;KACH,CACJ;IACL,IAAI,CAAC6D,iBAAiB,EAAE;IACxB,IAAI,CAACrB,MAAM,CAAC,IAAI,CAAC1B,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACT,UAAU,CAAC;IAEvE,IAAI,CAACuD,0BAA0B,GAAG,CAC9B;MACIhR,IAAI,EAAE,IAAI,CAAChF,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MACvD2U,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE;QACHC,MAAM,EAAE,SAAS;QACjBC,KAAK,EAAE;OACV;MACDc,cAAcA,CAACtC,IAAI;QACf,OAAO,CAAC,gBAAgBA,IAAI,CAACvQ,IAAI,EAAE,CAAC;MACxC;KACH,EACD;MACI4B,IAAI,EAAE,IAAI,CAAChF,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1D2U,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZQ,gBAAgB,EAAGtD,KAAK,IAAI;QACxB,IAAIA,KAAK,IAAI,CAAC,EAAE;UACZ,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,CAAC;SAC7E,MAAM,IAAIA,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;UAC5C;UACA,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC;SACjE,MAAM,IAAIN,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACC,SAAS,EAAE;UAChD,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC;SACjE,MAAM,IAAIF,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACE,SAAS,EAAE;UAChD,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,CAAC;SAClE,MAAM,IAAIH,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACG,WAAW,EAAE;UAClD,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,CAAC;SAClE,MAAM,IAAIJ,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACI,MAAM,EAAE;UAC7C,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,CAAC;SAC/D,MAAM,IAAIL,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;UACjG,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,CAAC;SACjF,MAAM,IAAIN,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;UACjG,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,CAAC;SACjF,MAAM,IAAIN,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;UACjG,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,CAAC;;QAEtF,OAAO,EAAE;MACb,CAAC;MACDiD,eAAe,EAAGvD,KAAK,IAAI;QACvB,IAAIA,KAAK,IAAI,CAAC,EAAE;UACZ,OAAO1B,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SAC1D,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;UAC5C;UACA,OAAOhC,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SAC1D,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACC,SAAS,EAAE;UAChD,OAAO5B,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SAC1D,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACG,WAAW,EAAE;UAClD,OAAO9B,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;SAC5D,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACI,MAAM,EAAE;UAC7C,OAAO/B,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;SACvD,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACE,SAAS,EAAE;UAChD,OAAO7B,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;SAC5D,MAAM,IAAIiS,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;UACjG,OAAO,IAAI,CAACxS,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;SACvE,MAAM,IAAIiS,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;UACjG,OAAO,IAAI,CAACxS,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC;SACzE,MAAM,IAAIiS,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;UACjG,OAAO,IAAI,CAACxS,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC;;QAErE,OAAO,EAAE;MACb,CAAC;MACDgV,KAAK,EAAE;QACHE,KAAK,EAAE;;KAEd,EACD;MACInQ,IAAI,EAAE,IAAI,CAAChF,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MACvD2U,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZQ,gBAAgB,EAAGtD,KAAK,IAAI;QACxB,OAAO,CAAC,WAAW,EAAE,oBAAoB,CAAC;MAC9C;KACH,EACD;MACIlN,IAAI,EAAE,IAAI,CAAChF,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MACvD2U,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IAED,IAAI,CAACkB,8BAA8B,GAAG;MAClC9C,gBAAgB,EAAE,IAAI;MACtBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE,KAAK;MAC1B4C,eAAe,EAAE;KACpB;IAED,IAAI,CAACC,eAAe,GAAG;MACnBxF,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;MACrByF,gBAAgB,EAAE,IAAI,CAACrW,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;MAC7EqW,OAAO,EAAE,EAAE;MACXlS,IAAI,EAAE,IAAI;MACVmS,QAAQ,EAAE,IAAI;MACdC,kBAAkB,EAAE,IAAI;MACxBC,YAAY,EAAE,IAAI,CAACC,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;MACxCC,QAAQ,EAAE;KACb;EACL;EAEAC,qBAAqBA,CAAA;IACjB,IAAI,IAAI,CAAClH,uBAAuB,IAAI,KAAK,EAAE;MACvC,IAAI,CAACmH,YAAY,GAAG,IAAI;;IAE5B,IAAI,IAAI,CAAClH,4BAA4B,IAAI,KAAK,EAAE;MAC5C,IAAI,CAACmH,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACC,gBAAgB,GAAG,IAAI;;EAEpC;EAEAC,cAAcA,CAAA;IACV,IAAI,CAACjE,UAAU,GAAG,CAAC;IACnB,IAAI,CAAC0B,MAAM,CAAC,IAAI,CAAC1B,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACT,UAAU,CAAC;EAC3E;EAEAiC,MAAMA,CAACwC,IAAI,EAAEC,KAAK,EAAEjE,IAAI,EAAEkE,MAAM;IAC5B,IAAI5G,EAAE,GAAG,IAAI;IACb,IAAI,CAACwC,UAAU,GAAGkE,IAAI;IACtB,IAAI,CAACjE,QAAQ,GAAGkE,KAAK;IACrB,IAAI,CAACjE,IAAI,GAAGA,IAAI;IAChB,IAAImE,UAAU,GAAG;MACbH,IAAI;MACJrC,IAAI,EAAEsC,KAAK;MACXjE;KACH;IACD,IAAI,CAACoE,YAAY,CAACD,UAAU,CAAC;IAC7B,IAAI,CAACE,OAAO,GAAG;MACXC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACDjH,EAAE,CAAC4D,oBAAoB,CAACsD,MAAM,EAAE;IAChC,IAAI,CAAC1I,UAAU,CAAC0F,MAAM,CAAC2C,UAAU,EAAGjH,QAAQ,IAAI;MAC5C;MACAI,EAAE,CAAC+G,OAAO,GAAG;QACTC,OAAO,EAAEpH,QAAQ,CAACoH,OAAO;QACzBC,KAAK,EAAErH,QAAQ,CAACuH;OACnB;MACD;IACJ,CAAC,EAAE,IAAI,EAAE,MAAK;MACVnH,EAAE,CAAC4D,oBAAoB,CAACwD,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAN,YAAYA,CAACD,UAAU;IACnB,IAAI7G,EAAE,GAAG,IAAI;IACbqH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrF,UAAU,CAAC,CAACsF,OAAO,CAACnD,GAAG,IAAG;MACvC,IAAI,IAAI,CAACnC,UAAU,CAACmC,GAAG,CAAC,IAAI,IAAI,EAAE;QAC9B,IAAIA,GAAG,IAAI,UAAU,EAAE;UACnByC,UAAU,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC5E,UAAU,CAACG,QAAQ,CAACoF,OAAO,EAAE;SACtE,MAAM,IAAIpD,GAAG,IAAI,QAAQ,EAAE;UACxByC,UAAU,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC5E,UAAU,CAACI,MAAM,CAACmF,OAAO,EAAE;SAClE,MAAM,IAAIpD,GAAG,IAAI,cAAc,EAAE;UAC9ByC,UAAU,CAAC,cAAc,CAAC,GAAG7G,EAAE,CAACxM,WAAW,CAACiU,iBAAiB,CAAC,IAAI,CAACxF,UAAU,CAACnO,YAAY,CAAC;SAC9F,MAAM;UACH+S,UAAU,CAACzC,GAAG,CAAC,GAAG,IAAI,CAACnC,UAAU,CAACmC,GAAG,CAAC;;;IAGlD,CAAC,CAAC;EACN;EAGAmB,iBAAiBA,CAAA;IACb,IAAIvF,EAAE,GAAG,IAAI;IACb,IAAI,CAAC3B,iBAAiB,CAACqJ,2BAA2B,CAAE9H,QAAQ,IAAG;MAC3DI,EAAE,CAACpB,cAAc,GAAG,CAACgB,QAAQ,IAAI,EAAE,EAAE+H,GAAG,CAAChE,EAAE,IAAG;QAC1C,OAAO;UACH,GAAGA,EAAE;UACLiE,OAAO,EAAE,GAAGjE,EAAE,CAACnP,IAAI,IAAE,SAAS,MAAMmP,EAAE,CAAClP,IAAI,IAAE,SAAS;SACzD;MACL,CAAC,CAAC;MACFuL,EAAE,CAACpB,cAAc,CAAC8D,IAAI,CAAC,CAACmF,CAAC,EAACC,CAAC,KAAK,CAACD,CAAC,CAACrT,IAAI,IAAI,EAAE,EAAEuT,WAAW,EAAE,CAACC,aAAa,CAAC,CAACF,CAAC,CAACtT,IAAI,IAAI,EAAE,EAAEuT,WAAW,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAE,CAAC,CAAC,CAAC;MACrH;IACJ,CAAC,CAAC;EACN;;EACAzE,+BAA+BA,CAACC,YAAY;IACxC,IAAIvD,EAAE,GAAG,IAAI;IACb,IAAIiI,KAAK,GAAG;MACR,cAAc,EAAE1E;KACnB;IACD,IAAI,CAAClF,iBAAiB,CAACiF,+BAA+B,CAAC2E,KAAK,EAAGrI,QAAQ,IAAI;MACvEI,EAAE,CAACnB,oBAAoB,GAAG,CAACe,QAAQ,IAAI,EAAE,EAAE+H,GAAG,CAAChE,EAAE,IAAG;QAChD,OAAO;UACH,GAAGA,EAAE;UACLiE,OAAO,EAAE,GAAGjE,EAAE,CAACnP,IAAI,IAAI,SAAS,MAAMmP,EAAE,CAAClP,IAAI,IAAI,SAAS;SAC7D;MACL,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAGAyT,gBAAgBA,CAACxG,KAAK;IAClB,IAAIA,KAAK,EAAE;MACP,IAAI,CAACzC,SAAS,GAAGyC,KAAK;KACzB,MAAM;MACH,IAAI,CAACzC,SAAS,GAAG,IAAI;;EAE7B;EAEAkJ,cAAcA,CAACzG,KAAK;IAChB,IAAIA,KAAK,EAAE;MACP,IAAI,CAAC3C,WAAW,GAAG2C,KAAK;KAC3B,MAAM;MACH,IAAI,CAAC3C,WAAW,GAAG,IAAIC,IAAI,EAAE;;EAErC;EAEAoJ,iBAAiBA,CAAA;IACb,IAAI,CAAC7I,iBAAiB,GAAG,KAAK;EAClC;EAEA8I,gBAAgBA,CAAA;IACZ,IAAI,CAAChK,iBAAiB,CAACgK,gBAAgB,EAAE;EAC7C;EAEAnC,UAAUA,CAACoC,UAAe;IACtB,IAAItI,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC4D,oBAAoB,CAACsD,MAAM,EAAE;IAChC,IAAI,CAAC7I,iBAAiB,CAACkK,oBAAoB,CAACD,UAAU,EAAGE,MAAM,IAAI;MAC/DxI,EAAE,CAACyI,gCAAgC,CAACD,MAAM,CAAC;IAC/C,CAAC,CAAC;EACN;EAEAC,gCAAgCA,CAACD,MAAM;IACnC,IAAIxI,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC/E,gBAAgB,GAAGjI,SAAS;IAC/BgN,EAAE,CAACT,iBAAiB,GAAG,KAAK;IAC5B,IAAI,CAACqG,eAAe,CAACQ,QAAQ,GAAG,IAAI;IACpC,IAAIxG,QAAQ,GAAG4I,MAAM,CAACE,eAAe,IAAI,EAAE;IAC3C,IAAIF,MAAM,CAACvB,KAAK,IAAI,CAAC,EAAE;MACnBjH,EAAE,CAAC4D,oBAAoB,CAAC+E,KAAK,CAAC3I,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC+Y,MAAM,CAACI,OAAO,CAAC,CAAC;MACvE5I,EAAE,CAACV,8BAA8B,GAAG,KAAK;MACzCU,EAAE,CAACX,kCAAkC,GAAG,KAAK;MAC7CW,EAAE,CAACZ,4BAA4B,GAAG,KAAK;MACvC;;IAEJ,IAAIoJ,MAAM,CAACI,OAAO,CAACb,WAAW,EAAE,IAAI,IAAI,CAACA,WAAW,EAAE,EAAE;MACpD/H,EAAE,CAAC4D,oBAAoB,CAACiF,OAAO,CAAC7I,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,0CAA0C,EAAE;QACjGkZ,KAAK,EAAEH,MAAM,CAACvB,KAAK,GAAGuB,MAAM,CAACG,KAAK;QAClC1B,KAAK,EAAEuB,MAAM,CAACvB;OACjB,CAAC,CAAC;KACN,MAAM;MACHjH,EAAE,CAAC4D,oBAAoB,CAAC+E,KAAK,CAAC3I,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC+Y,MAAM,CAACI,OAAO,CAAC,CAAC;;IAE3E,IAAIhJ,QAAQ,CAAC1E,MAAM,IAAI,CAAC,EAAE;MACtB8E,EAAE,CAACV,8BAA8B,GAAG,KAAK;MACzCU,EAAE,CAACX,kCAAkC,GAAG,KAAK;MAC7CW,EAAE,CAACZ,4BAA4B,GAAG,KAAK;MACvC;;IAEJ;IACA,IAAI0J,KAAK,GAAG,CAAC;IACb9I,EAAE,CAACjG,iBAAiB,GAAG,EAAE;IACzBiG,EAAE,CAAC+I,gBAAgB,GAAG,EAAE;IACxB/I,EAAE,CAACgJ,YAAY,GAAG,EAAE;IACpB,IAAIC,kBAAkB,GAAG,CAAC,0BAA0B,EAAE,+BAA+B,EAAE,4BAA4B,EAAE,iCAAiC,CAAC;IACvJrJ,QAAQ,CAAC2H,OAAO,CAAC5D,EAAE,IAAG;MAClB,IAAI,CAACsF,kBAAkB,CAACC,QAAQ,CAACvF,EAAE,CAACzN,WAAW,CAAC,EAAE;QAC9C,IAAIiT,QAAQ,CAACxF,EAAE,CAACvD,IAAI,CAAC,IAAI,CAAC,EAAE;UACxB,IAAIuD,EAAE,CAACpR,MAAM,IAAI,IAAI,IAAIoR,EAAE,CAACpR,MAAM,IAAI,EAAE,IAAI,0BAA0B,CAAC6W,IAAI,CAACzF,EAAE,CAACpR,MAAM,CAAC,EAAE;YACpF,IAAI,CAACoR,EAAE,CAACzN,WAAW,IAAI,EAAE,KAAK,EAAE,EAAE;cAC9B,IAAIyN,EAAE,CAACzN,WAAW,IAAI8J,EAAE,CAACgJ,YAAY,EAAE;gBACnChJ,EAAE,CAACgJ,YAAY,CAACrF,EAAE,CAACzN,WAAW,CAAC,CAACmT,IAAI,CAAC1F,EAAE,CAACpR,MAAM,CAAC;eAClD,MAAM;gBACHyN,EAAE,CAACgJ,YAAY,CAACrF,EAAE,CAACzN,WAAW,CAAC,GAAG,CAACyN,EAAE,CAACpR,MAAM,CAAC;;;;SAI5D,MAAM,IAAI4W,QAAQ,CAACxF,EAAE,CAACvD,IAAI,CAAC,IAAI,CAAC,EAAE;UAC/B,IAAIuD,EAAE,CAACtQ,cAAc,IAAI,IAAI,IAAIsQ,EAAE,CAACtQ,cAAc,IAAI,EAAE,IAAI,mBAAmB,CAAC+V,IAAI,CAACzF,EAAE,CAACtQ,cAAc,CAAC,EAAE;YACrG,IAAI,CAACsQ,EAAE,CAACzN,WAAW,IAAI,EAAE,KAAK,EAAE,EAAE;cAC9B,IAAIyN,EAAE,CAACzN,WAAW,IAAI8J,EAAE,CAAC+I,gBAAgB,EAAE;gBACvC/I,EAAE,CAAC+I,gBAAgB,CAACpF,EAAE,CAACzN,WAAW,CAAC,CAACmT,IAAI,CAAC1F,EAAE,CAACG,QAAQ,CAAC;eACxD,MAAM;gBACH9D,EAAE,CAAC+I,gBAAgB,CAACpF,EAAE,CAACzN,WAAW,CAAC,GAAG,CAACyN,EAAE,CAACG,QAAQ,CAAC;;;;;OAKtE,MAAM;QACHH,EAAE,CAACzN,WAAW,GAAG,EAAE;;MAEvByN,EAAE,CAAC,SAAS,CAAC,GAAG,UAAUmF,KAAK,EAAE,EAAE;MACnC9I,EAAE,CAACjG,iBAAiB,CAAC4J,EAAE,CAAC,SAAS,CAAC,CAAC,GAAG3D,EAAE,CAACvB,WAAW,CAAC8D,KAAK,CAACoB,EAAE,CAAC;MAC9D3D,EAAE,CAACjG,iBAAiB,CAAC4J,EAAE,CAAC,SAAS,CAAC,CAAC,CAACzJ,QAAQ,CAAC,QAAQ,CAAC,CAACoP,aAAa,CAAC,CAACra,UAAU,CAAC8W,QAAQ,EAAE9W,UAAU,CAACoL,OAAO,CAAC,2BAA2B,CAAC,CAAC,CAAC;MAC5I2F,EAAE,CAACjG,iBAAiB,CAAC4J,EAAE,CAAC,SAAS,CAAC,CAAC,CAACzJ,QAAQ,CAAC,QAAQ,CAAC,CAACqP,sBAAsB,EAAE;MAC/EvJ,EAAE,CAACjG,iBAAiB,CAAC4J,EAAE,CAAC,SAAS,CAAC,CAAC,CAACzJ,QAAQ,CAAC,gBAAgB,CAAC,CAACoP,aAAa,CAAC,CAACra,UAAU,CAAC8W,QAAQ,EAAE9W,UAAU,CAACoL,OAAO,CAAC,0DAA0D,CAAC,CAAC,CAAC;MACnL2F,EAAE,CAACjG,iBAAiB,CAAC4J,EAAE,CAAC,SAAS,CAAC,CAAC,CAACzJ,QAAQ,CAAC,gBAAgB,CAAC,CAACqP,sBAAsB,EAAE;IAC3F,CAAC,CAAC;IACFvJ,EAAE,CAAClF,iBAAiB,GAAG,CAAC;IACxBkF,EAAE,CAACnF,iBAAiB,GAAG,EAAE;IACzBmF,EAAE,CAAC/E,gBAAgB,GAAG,CAAC,GAAG2E,QAAQ,CAAC;IACnCI,EAAE,CAAC7E,UAAU,GAAG6E,EAAE,CAAC/E,gBAAgB,CAACuO,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;EACpD;EAEA3P,mBAAmBA,CAACsJ,IAAI,EAAE2F,KAAK;IAC3B;IACA,IAAI,CAAC7N,gBAAgB,CAACwO,MAAM,CAACX,KAAK,EAAE,CAAC,CAAC;IACtC,IAAI,CAAC3N,UAAU,GAAG,IAAI,CAACF,gBAAgB,CAACuO,KAAK,CAAC,IAAI,CAAC1O,iBAAiB,EAAE,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACD,iBAAiB,CAAC;IACtH,OAAO,IAAI,CAACd,iBAAiB,CAACoJ,IAAI,CAAC,SAAS,CAAC,CAAC;IAC9C,IAAI,IAAI,CAAClI,gBAAgB,CAACC,MAAM,IAAI,CAAC,EAAE;MACnC,IAAI,CAACoE,8BAA8B,GAAG,KAAK;;EAEnD;EAEA5D,oBAAoBA,CAAA;IAChB,IAAI,IAAI,CAACP,UAAU,CAACD,MAAM,IAAI,CAAC,EAAE;MAC7B,OAAO,KAAK;;IAEhB,IAAIoM,IAAI,GAAGD,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvN,iBAAiB,CAAC;IAC9C,KAAK,IAAI2P,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpC,IAAI,CAACpM,MAAM,EAAEwO,CAAC,EAAE,EAAE;MAClC,IAAItF,GAAG,GAAGkD,IAAI,CAACoC,CAAC,CAAC;MACjB,IAAI,IAAI,CAAC3P,iBAAiB,CAACqK,GAAG,CAAC,CAACnK,OAAO,EAAE;QACrC,OAAO,KAAK;;;IAGpB,KAAK,IAAIyP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvO,UAAU,CAACD,MAAM,EAAEwO,CAAC,EAAE,EAAE;MAC7C,IAAI,IAAI,CAACvO,UAAU,CAACuO,CAAC,CAAC,CAACxT,WAAW,IAAI,IAAI,IAAI,IAAI,CAACiF,UAAU,CAACuO,CAAC,CAAC,CAACxT,WAAW,IAAI,EAAE,EAAE;QAChF,OAAO,KAAK;;;IAGpB,OAAO,IAAI;EACf;EAEA+B,yBAAyBA,CAACkL,IAAI;IAC1B,IAAIA,IAAI,CAAC9P,cAAc,IAAI,IAAI,IAAI8P,IAAI,CAAC5Q,MAAM,IAAI,IAAI,EAAE;MACpD,IAAI2D,WAAW,GAAG,EAAE;MACpB,IAAIyT,QAAQ,GAAGtC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC0B,YAAY,CAAC;MAC7C,IAAIY,QAAQ,GAAGvC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACyB,gBAAgB,CAAC;MACjD,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,QAAQ,CAACzO,MAAM,EAAEwO,CAAC,EAAE,EAAE;QACtC,IAAI,IAAI,CAACV,YAAY,CAACW,QAAQ,CAACD,CAAC,CAAC,CAAC,CAACR,QAAQ,CAAC/F,IAAI,CAAC5Q,MAAM,CAAC,EAAE;UACtD2D,WAAW,GAAGyT,QAAQ,CAACD,CAAC,CAAC;UACzB;;;MAGR,IAAIxT,WAAW,IAAI,EAAE,EAAE;QACnB,KAAK,IAAIwT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,QAAQ,CAAC1O,MAAM,EAAEwO,CAAC,EAAE,EAAE;UACtC,IAAI,IAAI,CAACX,gBAAgB,CAACa,QAAQ,CAACF,CAAC,CAAC,CAAC,CAACR,QAAQ,CAAC/F,IAAI,CAAC9P,cAAc,CAAC,EAAE;YAClE6C,WAAW,GAAG0T,QAAQ,CAACF,CAAC,CAAC;YACzB;;;;MAIZ;MACA,IAAIxT,WAAW,CAAC2T,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;QACxC,IAAIC,GAAG,GAAG,IAAI,CAAC7O,gBAAgB,CAAC0M,GAAG,CAAChE,EAAE,IAAIA,EAAE,CAACpR,MAAM,CAAC,CAACmR,MAAM,CAACC,EAAE,IAAIA,EAAE,IAAIR,IAAI,CAAC5Q,MAAM,CAAC,CAAC2I,MAAM;QAC3F,IAAI4O,GAAG,IAAI,CAAC,EAAE;UACV5T,WAAW,GAAG,EAAE;UAChB,IAAI,CAAC+E,gBAAgB,CAACsM,OAAO,CAAC5D,EAAE,IAAG;YAC/B,IAAIA,EAAE,CAACzN,WAAW,CAAC2T,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAIlG,EAAE,CAACpR,MAAM,IAAI4Q,IAAI,EAAE;cAChEQ,EAAE,CAACzN,WAAW,GAAG,EAAE;;UAE3B,CAAC,CAAC;;;MAGViN,IAAI,CAACjN,WAAW,GAAGA,WAAW;;EAEtC;EAEA6T,cAAcA,CAAA;IACV,IAAI,IAAI,CAACzD,YAAY,IAAI,IAAI,IAAI,IAAI,CAACA,YAAY,IAAItT,SAAS,EAAE;IACjE,IAAIgX,WAAW,GAAG;MACd,QAAQ,EAAE,CAAC;MACX,QAAQ,EAAE,IAAI,CAAC5G,WAAW,CAAC7Q,MAAM;MACjC,cAAc,EAAE,IAAI,CAAC+T;KACxB;IACD,IAAItG,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC4D,oBAAoB,CAACsD,MAAM,EAAE;IAChC,IAAI,CAAC7I,iBAAiB,CAAC4L,kBAAkB,CAACD,WAAW,EAAGpK,QAAQ,IAAI;MAChE,IAAII,EAAE,CAACF,kBAAkB,IAAI,UAAU,EAAE;QACrCE,EAAE,CAAC4D,oBAAoB,CAACK,OAAO,CAACjE,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,qCAAqC,CAAC,CAAC;OACnG,MAAM;QACHuQ,EAAE,CAAC4D,oBAAoB,CAACK,OAAO,CAACjE,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC,CAAC;;MAElGuQ,EAAE,CAACb,uBAAuB,GAAG,KAAK;MAClCa,EAAE,CAACkE,MAAM,CAAClE,EAAE,CAACwC,UAAU,EAAExC,EAAE,CAACyC,QAAQ,EAAEzC,EAAE,CAAC0C,IAAI,EAAE1C,EAAE,CAACiC,UAAU,CAAC;IACjE,CAAC,CAAC;EAEN;EAEAiI,mBAAmBA,CAAA;IACf,IAAI,IAAI,CAAC3D,iBAAiB,IAAI,IAAI,IAAI,IAAI,CAACA,iBAAiB,IAAIvT,SAAS,IAAI,IAAI,CAACwT,gBAAgB,IAAI,IAAI,IAAI,IAAI,CAACA,gBAAgB,IAAIxT,SAAS,EAAE;IAClJ,IAAIgX,WAAW,GAAG;MACd,QAAQ,EAAE,CAAC;MACX,YAAY,EAAE,IAAI,CAACxD,gBAAgB;MACnC,cAAc,EAAE,IAAI,CAACD;KACxB;IACD,IAAIvG,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC4D,oBAAoB,CAACsD,MAAM,EAAE;IAChC,IAAI,CAAC7I,iBAAiB,CAAC8L,uBAAuB,CAACH,WAAW,EAAGpK,QAAQ,IAAI;MACrE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAI,EAAE,CAACoK,iCAAiC,CAACxK,QAAQ,CAAC;MAC9CI,EAAE,CAAC4D,oBAAoB,CAACwD,OAAO,EAAE;IAErC,CAAC,EAAGuB,KAAK,IAAI;MACT3I,EAAE,CAAC4D,oBAAoB,CAAC+E,KAAK,CAAC3I,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAACkZ,KAAK,CAACA,KAAK,CAACA,KAAK,CAACC,OAAO,CAAC,CAAC;MAClF5I,EAAE,CAACZ,4BAA4B,GAAG,KAAK;MACvCY,EAAE,CAACkE,MAAM,CAAClE,EAAE,CAACwC,UAAU,EAAExC,EAAE,CAACyC,QAAQ,EAAEzC,EAAE,CAAC0C,IAAI,EAAE1C,EAAE,CAACiC,UAAU,CAAC;IACjE,CAAC,CAAC;EACN;EAEAmI,iCAAiCA,CAAC5B,MAAM;IACpC,IAAIxI,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC/E,gBAAgB,GAAGjI,SAAS;IAC/BgN,EAAE,CAACR,gBAAgB,GAAG,KAAK;IAC3B,IAAII,QAAQ,GAAG4I,MAAM,CAACE,eAAe,IAAI,EAAE;IAC3C,IAAIF,MAAM,CAACvB,KAAK,IAAI,CAAC,EAAE;MACnBjH,EAAE,CAAC4D,oBAAoB,CAAC+E,KAAK,CAAC3I,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC+Y,MAAM,CAACI,OAAO,CAAC,CAAC;MACvE5I,EAAE,CAACX,kCAAkC,GAAG,KAAK;MAC7CW,EAAE,CAACZ,4BAA4B,GAAG,KAAK;MACvC;;IAEJ,IAAIoJ,MAAM,CAACI,OAAO,CAACb,WAAW,EAAE,IAAI,IAAI,CAACA,WAAW,EAAE,EAAE;MACpD/H,EAAE,CAAC4D,oBAAoB,CAACiF,OAAO,CAAC7I,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC,4CAA4C,EAAE;QACnGkZ,KAAK,EAAEH,MAAM,CAACvB,KAAK,GAAGuB,MAAM,CAACG,KAAK;QAClC1B,KAAK,EAAEuB,MAAM,CAACvB;OACjB,CAAC,CAAC;KACN,MAAM;MACHjH,EAAE,CAAC4D,oBAAoB,CAAC+E,KAAK,CAAC3I,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAAC+Y,MAAM,CAACI,OAAO,CAAC,CAAC;;IAE3E,IAAIhJ,QAAQ,CAAC1E,MAAM,IAAI,CAAC,EAAE;MACtB8E,EAAE,CAACX,kCAAkC,GAAG,KAAK;MAC7CW,EAAE,CAACZ,4BAA4B,GAAG,KAAK;MACvC;;IAEJY,EAAE,CAACX,kCAAkC,GAAG,IAAI;IAC5C;IACA,IAAIyJ,KAAK,GAAG,CAAC;IACb9I,EAAE,CAACjG,iBAAiB,GAAG,EAAE;IACzBiG,EAAE,CAAC+I,gBAAgB,GAAG,EAAE;IACxB/I,EAAE,CAACgJ,YAAY,GAAG,EAAE;IACpB,IAAIC,kBAAkB,GAAG,CAAC,iCAAiC,EAAE,0BAA0B,EAAE,+BAA+B,EAAE,4BAA4B,EAAE,iCAAiC,CAAC;IAC1LrJ,QAAQ,CAAC2H,OAAO,CAAC5D,EAAE,IAAG;MAClB,IAAI,CAACsF,kBAAkB,CAACC,QAAQ,CAACvF,EAAE,CAACzN,WAAW,CAAC,EAAE;QAC9C,IAAIiT,QAAQ,CAACxF,EAAE,CAACvD,IAAI,CAAC,IAAI,CAAC,EAAE;UACxB,IAAIuD,EAAE,CAACpR,MAAM,IAAI,IAAI,IAAIoR,EAAE,CAACpR,MAAM,IAAI,EAAE,IAAI,0BAA0B,CAAC6W,IAAI,CAACzF,EAAE,CAACpR,MAAM,CAAC,EAAE;YACpF,IAAI,CAACoR,EAAE,CAACzN,WAAW,IAAI,EAAE,KAAK,EAAE,EAAE;cAC9B,IAAIyN,EAAE,CAACzN,WAAW,IAAI8J,EAAE,CAACgJ,YAAY,EAAE;gBACnChJ,EAAE,CAACgJ,YAAY,CAACrF,EAAE,CAACzN,WAAW,CAAC,CAACmT,IAAI,CAAC1F,EAAE,CAACpR,MAAM,CAAC;eAClD,MAAM;gBACHyN,EAAE,CAACgJ,YAAY,CAACrF,EAAE,CAACzN,WAAW,CAAC,GAAG,CAACyN,EAAE,CAACpR,MAAM,CAAC;;;;SAI5D,MAAM,IAAI4W,QAAQ,CAACxF,EAAE,CAACvD,IAAI,CAAC,IAAI,CAAC,EAAE;UAC/B,IAAIuD,EAAE,CAACtQ,cAAc,IAAI,IAAI,IAAIsQ,EAAE,CAACtQ,cAAc,IAAI,EAAE,IAAI,mBAAmB,CAAC+V,IAAI,CAACzF,EAAE,CAACtQ,cAAc,CAAC,EAAE;YACrG,IAAI,CAACsQ,EAAE,CAACzN,WAAW,IAAI,EAAE,KAAK,EAAE,EAAE;cAC9B,IAAIyN,EAAE,CAACzN,WAAW,IAAI8J,EAAE,CAAC+I,gBAAgB,EAAE;gBACvC/I,EAAE,CAAC+I,gBAAgB,CAACpF,EAAE,CAACzN,WAAW,CAAC,CAACmT,IAAI,CAAC1F,EAAE,CAACG,QAAQ,CAAC;eACxD,MAAM;gBACH9D,EAAE,CAAC+I,gBAAgB,CAACpF,EAAE,CAACzN,WAAW,CAAC,GAAG,CAACyN,EAAE,CAACG,QAAQ,CAAC;;;;;OAKtE,MAAM;QACHH,EAAE,CAACzN,WAAW,GAAG8J,EAAE,CAACxQ,WAAW,CAACC,SAAS,CAACkU,EAAE,CAACzN,WAAW,CAAC;;MAE7DyN,EAAE,CAAC,SAAS,CAAC,GAAG,UAAUmF,KAAK,EAAE,EAAE;MACnC9I,EAAE,CAACjG,iBAAiB,CAAC4J,EAAE,CAAC,SAAS,CAAC,CAAC,GAAG3D,EAAE,CAACvB,WAAW,CAAC8D,KAAK,CAACoB,EAAE,CAAC;MAC9D3D,EAAE,CAACjG,iBAAiB,CAAC4J,EAAE,CAAC,SAAS,CAAC,CAAC,CAACzJ,QAAQ,CAAC,QAAQ,CAAC,CAACoP,aAAa,CAAC,CAACra,UAAU,CAAC8W,QAAQ,EAAE9W,UAAU,CAACoL,OAAO,CAAC,2BAA2B,CAAC,CAAC,CAAC;MAC5I2F,EAAE,CAACjG,iBAAiB,CAAC4J,EAAE,CAAC,SAAS,CAAC,CAAC,CAACzJ,QAAQ,CAAC,QAAQ,CAAC,CAACqP,sBAAsB,EAAE;MAC/EvJ,EAAE,CAACjG,iBAAiB,CAAC4J,EAAE,CAAC,SAAS,CAAC,CAAC,CAACzJ,QAAQ,CAAC,gBAAgB,CAAC,CAACoP,aAAa,CAAC,CAACra,UAAU,CAAC8W,QAAQ,EAAE9W,UAAU,CAACoL,OAAO,CAAC,0DAA0D,CAAC,CAAC,CAAC;MACnL2F,EAAE,CAACjG,iBAAiB,CAAC4J,EAAE,CAAC,SAAS,CAAC,CAAC,CAACzJ,QAAQ,CAAC,gBAAgB,CAAC,CAACqP,sBAAsB,EAAE;IAC3F,CAAC,CAAC;IACFvJ,EAAE,CAAClF,iBAAiB,GAAG,CAAC;IACxBkF,EAAE,CAACnF,iBAAiB,GAAG,EAAE;IACzBmF,EAAE,CAAC/E,gBAAgB,GAAG,CAAC,GAAG2E,QAAQ,CAAC;IACnCI,EAAE,CAAC7E,UAAU,GAAG6E,EAAE,CAAC/E,gBAAgB,CAACuO,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;EAEpD;EAEAhO,eAAeA,CAAA;IACX,IAAI,CAAC,IAAI,CAACE,oBAAoB,EAAE,EAAE;IAClC,IAAIsE,EAAE,GAAG,IAAI;IACb,IAAIqK,IAAI,GAAG;MACPC,sBAAsB,EAAE,IAAI,CAACrP,gBAAgB;MAC7CsP,QAAQ,EAAE;KACb;IACDvK,EAAE,CAAC4D,oBAAoB,CAACsD,MAAM,EAAE;IAChC,IAAI,CAAC7I,iBAAiB,CAACmM,oBAAoB,CAACH,IAAI,EAAG7B,MAAM,IAAI;MACzDxI,EAAE,CAACyI,gCAAgC,CAACD,MAAM,CAAC;IAC/C,CAAC,EAAE,IAAI,EAAE,MAAK;MACVxI,EAAE,CAAC4D,oBAAoB,CAACwD,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAqD,yBAAyBA,CAAC/D,IAAI,EAAErC,IAAI,EAAE3B,IAAI,EAAEkE,MAAM;IAC9C,IAAI,CAAC8D,0BAA0B,GAAG;MAC9B1D,OAAO,EAAE,IAAI,CAAC2D,6BAA6B,CAACnB,KAAK,CAAC9C,IAAI,GAAGrC,IAAI,EAAEqC,IAAI,GAAGrC,IAAI,GAAGA,IAAI,CAAC;MAClF4C,KAAK,EAAE,IAAI,CAAC0D,6BAA6B,CAACzP;KAC7C;EACL;EAEAT,qBAAqBA,CAACmQ,KAAK;IACvB,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;IACvB,IAAIxG,IAAI,GAAGuG,KAAK,CAACE,IAAI;IACrB,IAAI,CAAChQ,iBAAiB,GAAG+P,KAAK;IAC9B,IAAI,CAAChQ,iBAAiB,GAAGwJ,IAAI;IAC7B,IAAI,CAAClJ,UAAU,GAAG,IAAI,CAACF,gBAAgB,CAACuO,KAAK,CAACqB,KAAK,EAAEA,KAAK,GAAGxG,IAAI,CAAC;EACtE;EAEAU,YAAYA,CAAA;IACR,IAAI/E,EAAE,GAAG,IAAI;IACb,IAAI,CAAC4D,oBAAoB,CAACsD,MAAM,EAAE;IAClClH,EAAE,CAACxB,UAAU,CAACuM,OAAO,CAAC/K,EAAE,CAAC6E,KAAK,EAAGjF,QAAQ,IAAI;MACzCI,EAAE,CAAC1N,SAAS,GAAG;QACX,GAAGsN;OACN;MACDI,EAAE,CAACgL,YAAY,EAAE;MACjBhL,EAAE,CAACiL,iBAAiB,EAAE;MACtBjL,EAAE,CAACkL,mBAAmB,EAAE;MACxBlL,EAAE,CAACmL,iBAAiB,EAAE;MACtBnL,EAAE,CAACoL,YAAY,EAAE;MACjBpL,EAAE,CAACxB,UAAU,CAAC6M,mBAAmB,CAAC,CAACrL,EAAE,CAAC6E,KAAK,CAAC,EAAGyG,IAAI,IAAI;QACnDtL,EAAE,CAAC1N,SAAS,CAACS,gBAAgB,GAAGuY,IAAI,CAAC,CAAC,CAAC,EAAEC,SAAS;MACtD,CAAC,EAAE,MAAK,CACR,CAAC,CAAC;IACN,CAAC,EAAE,IAAI,EAAE,MAAK;MACV,IAAI,CAAC3H,oBAAoB,CAACwD,OAAO,EAAE;IACvC,CAAC,CAAC;EACN;EAEA4D,YAAYA,CAAA;IACR,IAAIhL,EAAE,GAAG,IAAI;IACb,IAAI,CAACxB,UAAU,CAACgN,eAAe,CAAC,IAAI,CAAClZ,SAAS,CAACC,MAAM,EAAGqN,QAAQ,IAAI;MAChEI,EAAE,CAAChP,eAAe,GAAG;QACjBC,UAAU,EAAE2O,QAAQ,CAAC6L,UAAU,IAAI,CAAC;QACpCra,iBAAiB,EAAEwO,QAAQ,CAAC8L,QAAQ,IAAI,CAAC;QACzCna,cAAc,EAAEqO,QAAQ,CAAC+L,QAAQ,IAAI,CAAC;QACtCja,eAAe,EAAEkO,QAAQ,CAACgM,SAAS,IAAI,CAAC;QACxC/Z,gBAAgB,EAAE+N,QAAQ,CAACiM,SAAS,IAAI,CAAC;QACzC7Z,aAAa,EAAE4N,QAAQ,CAACkM,SAAS,IAAI;OACxC;IACL,CAAC,EAAE,MAAK,CACR,CAAC,CAAC;EACN;EAEAb,iBAAiBA,CAAA;IACb,IAAI,CAAC1W,cAAc,GAAG;MAClBC,IAAI,EAAE,IAAI,CAAClC,SAAS,CAACyZ,YAAY;MACjCtX,IAAI,EAAE,IAAI,CAACnC,SAAS,CAACiR;KACxB;EACL;EAEA2H,mBAAmBA,CAAA;IACf,IAAI,CAAC1M,UAAU,CAACwN,gBAAgB,CAAC,IAAI,CAAC1Z,SAAS,CAACC,MAAM,EAAGqN,QAAQ,IAAI;MACjE,IAAI,CAAClM,gBAAgB,GAAG;QACpB,GAAGkM;OACN;IACL,CAAC,EAAE,MAAK,CACR,CAAC,CAAC;EAEN;EAEAuL,iBAAiBA,CAAA;IACb,IAAI,CAAC3M,UAAU,CAAC2M,iBAAiB,CAAC,IAAI,CAAC3X,WAAW,CAACiU,iBAAiB,CAAC,IAAI,CAACnV,SAAS,CAACwB,YAAY,CAAC,EAAG8L,QAAQ,IAAI;MAC5G,IAAI,CAAC/L,cAAc,GAAG+L,QAAQ;IAClC,CAAC,EAAE,MAAK,CACR,CAAC,CAAC;EACN;EAEAwL,YAAYA,CAAA;IACR,IAAI,CAACzL,SAAS,GAAG;MACblL,IAAI,EAAE,IAAI,CAACnC,SAAS,CAAC2Z,OAAO;MAC5B7L,IAAI,EAAE,iBAAiB;MACvB9J,EAAE,EAAE,CAAC;MACL4V,OAAO,EAAE,IAAI,CAAC5Z,SAAS,CAACgE;KAC3B;EACL;EAEA3D,aAAaA,CAAC+O,KAAK;IACf,IAAIA,KAAK,IAAI,CAAC,EAAE;MACZ,OAAO,IAAI,CAAClS,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;KAC5D,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;MAC5C;MACA,OAAO,IAAI,CAACxS,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;KAC5D,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACC,SAAS,EAAE;MAChD,OAAO,IAAI,CAACpS,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;KAC5D,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACG,WAAW,EAAE;MAClD,OAAO,IAAI,CAACtS,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;KAC9D,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACI,MAAM,EAAE;MAC7C,OAAO,IAAI,CAACvS,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;KACzD,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACE,SAAS,EAAE;MAChD,OAAO,IAAI,CAACrS,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;KAC9D,MAAM,IAAIiS,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;MACjG,OAAO,IAAI,CAACxS,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;KACvE,MAAM,IAAIiS,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;MACjG,OAAO,IAAI,CAACxS,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC;KACzE,MAAM,IAAIiS,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;MACjG,OAAO,IAAI,CAACxS,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC;;IAErE,OAAO,EAAE;EACb;EAEAgD,cAAcA,CAACiP,KAAK;IAChB,IAAIA,KAAK,IAAI,CAAC,EAAE;MACZ,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,CAAC;KAC7E,MAAM,IAAIA,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;MAC5C;MACA,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC;KACnF,MAAM,IAAIN,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACC,SAAS,EAAE;MAChD,OAAO,CAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC;KACnF,MAAM,IAAIF,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACE,SAAS,EAAE;MAChD,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,CAAC;KACrF,MAAM,IAAIH,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACG,WAAW,EAAE;MAClD,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,CAAC;KACrF,MAAM,IAAIJ,KAAK,IAAIxS,SAAS,CAACyS,UAAU,CAACI,MAAM,EAAE;MAC7C,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,CAAC;KAC/E,MAAM,IAAIL,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;MACjG,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,CAAC;KACjF,MAAM,IAAIN,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;MACjG,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,CAAC;KACjF,MAAM,IAAIN,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACC,SAAS,IAAIF,KAAK,IAAI,EAAE,GAAGxS,SAAS,CAACyS,UAAU,CAACK,KAAK,EAAE;MACjG,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,CAAC;;IAEtF,OAAO,EAAE;EACb;EAEA7O,cAAcA,CAACuO,KAAK;IAChB,IAAIA,KAAK,IAAIxS,SAAS,CAACid,YAAY,CAACC,OAAO,EAAE,OAAO,IAAI,CAAC5c,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC,MACpG,IAAIiS,KAAK,IAAIxS,SAAS,CAACid,YAAY,CAACE,QAAQ,EAAE,OAAO,IAAI,CAAC7c,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC,MAC3G,OAAO,EAAE;EAClB;EAEA0V,aAAaA,CAAA;IACT,IAAInF,EAAE,GAAG,IAAI;IACb,IAAI,CAAC4D,oBAAoB,CAACsD,MAAM,EAAE;IAClClH,EAAE,CAAC3B,iBAAiB,CAAC0M,OAAO,CAACuB,MAAM,CAACtM,EAAE,CAACkF,MAAM,CAAC,EAAGtF,QAAQ,IAAI;MACzDI,EAAE,CAACJ,QAAQ,GAAGA,QAAQ;MAEtBI,EAAE,CAAC9K,cAAc,CAACkM,EAAE,GAAGxB,QAAQ,CAACwB,EAAE;MAClCpB,EAAE,CAAC9K,cAAc,CAACT,IAAI,GAAGmL,QAAQ,CAACnL,IAAI;MACtCuL,EAAE,CAAC9K,cAAc,CAACV,IAAI,GAAGoL,QAAQ,CAACpL,IAAI;MACtCwL,EAAE,CAAC9K,cAAc,CAACxC,MAAM,GAAGkN,QAAQ,CAAClN,MAAM;MAC1CsN,EAAE,CAAC9K,cAAc,CAACa,YAAY,GAAG6J,QAAQ,CAAC7J,YAAY;MACtDiK,EAAE,CAAC9K,cAAc,CAACe,YAAY,GAAG2J,QAAQ,CAAC3J,YAAY;MACtD+J,EAAE,CAAC9K,cAAc,CAACkB,eAAe,GAAGwJ,QAAQ,CAACxJ,eAAe;MAC5D4J,EAAE,CAAC9K,cAAc,CAACC,gBAAgB,GAAGyK,QAAQ,CAAC2M,QAAQ;MACtDvM,EAAE,CAAC9K,cAAc,CAACsB,WAAW,GAAGoJ,QAAQ,CAACpJ,WAAW;MACpDwJ,EAAE,CAAC9K,cAAc,CAAC0B,aAAa,GAAGgJ,QAAQ,CAAChJ,aAAa;MACxDoJ,EAAE,CAAC9K,cAAc,CAAC2B,aAAa,GAAG+I,QAAQ,CAAC/I,aAAa;MACxDmJ,EAAE,CAAC9K,cAAc,CAACmM,MAAM,GAAGzB,QAAQ,CAACyB,MAAM;MAC1CrB,EAAE,CAAC9K,cAAc,CAACoM,IAAI,GAAG1B,QAAQ,CAAC0B,IAAI;MACtCtB,EAAE,CAAC9K,cAAc,CAAC4B,cAAc,GAAG8I,QAAQ,CAAC9I,cAAc;MAC1DkJ,EAAE,CAAC9K,cAAc,CAAC6B,eAAe,GAAG6I,QAAQ,CAAC7I,eAAe;MAC5DiJ,EAAE,CAAC9K,cAAc,CAAC8B,cAAc,GAAG4I,QAAQ,CAAC5I,cAAc;MAC1DgJ,EAAE,CAAC9K,cAAc,CAACqM,QAAQ,GAAG3B,QAAQ,CAAC2B,QAAQ;MAC9CvB,EAAE,CAAC9K,cAAc,CAAC+B,cAAc,GAAG2I,QAAQ,CAAC3I,cAAc;MAC1D+I,EAAE,CAAC9K,cAAc,CAACsC,aAAa,GAAGoI,QAAQ,CAACpI,aAAa;MACxDwI,EAAE,CAAC9K,cAAc,CAACiC,YAAY,GAAGyI,QAAQ,CAACzI,YAAY;MACtD6I,EAAE,CAAC9K,cAAc,CAACkC,aAAa,GAAGwI,QAAQ,CAACxI,aAAa;MACxD4I,EAAE,CAAC9K,cAAc,CAACmC,UAAU,GAAGuI,QAAQ,CAACvI,UAAU;MAClD2I,EAAE,CAAC9K,cAAc,CAACqC,aAAa,GAAGqI,QAAQ,CAACrI,aAAa;MACxDyI,EAAE,CAAC9K,cAAc,CAACgC,SAAS,GAAG0I,QAAQ,CAAC1I,SAAS;MAChD8I,EAAE,CAAC9K,cAAc,CAACsM,YAAY,GAAG5B,QAAQ,CAAC4B,YAAY;MACtDxB,EAAE,CAAC9K,cAAc,CAACgB,WAAW,GAAG0J,QAAQ,CAAC1J,WAAW;MAEpD8J,EAAE,CAACwM,SAAS,CAACxM,EAAE,CAAC9K,cAAc,CAACmM,MAAM,CAAC;MACtCrB,EAAE,CAACyM,WAAW,CAACzM,EAAE,CAAC9K,cAAc,CAACqM,QAAQ,CAAC;MAC1CvB,EAAE,CAACrL,UAAU,GAAG,EAAE;MAElBqL,EAAE,CAACtB,cAAc,CAACgO,eAAe,CAAErC,IAAI,IAAI;QACvCrK,EAAE,CAAC2M,SAAS,GAAGtC,IAAI,CAAC1C,GAAG,CAAChE,EAAE,IAAG;UACzB,OAAO;YACHlP,IAAI,EAAEkP,EAAE,CAAClP,IAAI;YACbD,IAAI,EAAE,GAAGmP,EAAE,CAACnP,IAAI;WACnB;QACL,CAAC,CAAC;QACFwL,EAAE,CAAC2M,SAAS,CAACpF,OAAO,CAAC5D,EAAE,IAAG;UACtB,IAAI3D,EAAE,CAAC9K,cAAc,CAACsM,YAAY,CAAC0H,QAAQ,CAACvF,EAAE,CAAClP,IAAI,CAAC,EAAE;YAClDuL,EAAE,CAACrL,UAAU,IAAI,GAAGgP,EAAE,CAACnP,IAAI,IAAI;;QAGvC,CAAC,CAAC;QACF,IAAIwL,EAAE,CAACrL,UAAU,CAACuG,MAAM,GAAG,CAAC,EAAE;UAC1B8E,EAAE,CAACrL,UAAU,GAAGqL,EAAE,CAACrL,UAAU,CAACiY,SAAS,CAAC,CAAC,EAAE5M,EAAE,CAACrL,UAAU,CAACuG,MAAM,GAAG,CAAC,CAAC;;MAE5E,CAAC,CAAC;IAEN,CAAC,EAAE,IAAI,EAAE,MAAK;MACV8E,EAAE,CAAC4D,oBAAoB,CAACwD,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAoF,SAASA,CAAC9K,KAAK;IACX,IAAIA,KAAK,IAAIxS,SAAS,CAAC2d,MAAM,CAACC,GAAG,EAAE;MAC/B,OAAO,IAAI,CAACrX,aAAa,GAAG,IAAI;KACnC,MAAM,IAAIiM,KAAK,IAAIxS,SAAS,CAAC2d,MAAM,CAACE,EAAE,EAAE;MACrC,OAAO,IAAI,CAACtX,aAAa,GAAG,KAAK;;IAErC,OAAO,EAAE;EACb;EAEAgX,WAAWA,CAAC/K,KAAK;IACb,IAAIA,KAAK,IAAIxS,SAAS,CAAC8d,QAAQ,CAACF,GAAG,EAAE;MACjC,OAAO,IAAI,CAAClX,eAAe,GAAG,IAAI;KACrC,MAAM,IAAI8L,KAAK,IAAIxS,SAAS,CAAC8d,QAAQ,CAACD,EAAE,EAAE;MACvC,OAAO,IAAI,CAACnX,eAAe,GAAG,KAAK;;IAEvC,OAAO,EAAE;EACb;EAEAI,mBAAmBA,CAAC0L,KAAK;IACrB,IAAIA,KAAK,IAAIxS,SAAS,CAAC+d,aAAa,CAACC,QAAQ,EAAE;MAC3C,OAAO,IAAI,CAAC1d,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;KACxE,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAAC+d,aAAa,CAACE,UAAU,EAAE;MACpD,OAAO,IAAI,CAAC3d,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC;KAC1E,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAAC+d,aAAa,CAACG,MAAM,EAAE;MAChD,OAAO,IAAI,CAAC5d,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC;;IAEvE,OAAO,EAAE;EACb;EAEA2V,qBAAqBA,CAAA;IACjB,IAAI,CAAC/O,iBAAiB,GAAG,CAAC;MACtB+J,IAAI,EAAE,IAAI,CAAC5Q,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;MACpE6G,EAAE,EAAEpH,SAAS,CAACme,iBAAiB,CAAChB;KACnC,EACG;MACIjM,IAAI,EAAE,IAAI,CAAC5Q,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;MACnE6G,EAAE,EAAEpH,SAAS,CAACme,iBAAiB,CAACjB;KACnC,CAAC;EACV;EAEAzV,gBAAgBA,CAAC+K,KAAK;IAClB,IAAIA,KAAK,IAAIxS,SAAS,CAACoe,gBAAgB,CAACC,GAAG,EAAE;MACzC,OAAO,IAAI,CAAC/d,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;KAC5D,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAACoe,gBAAgB,CAACE,KAAK,EAAE;MAClD,OAAO,IAAI,CAAChe,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;;IAE/D,OAAO,EAAE;EACb;EAEA8G,cAAcA,CAACmL,KAAK;IAChB,IAAIA,KAAK,IAAIxS,SAAS,CAAC2Q,iBAAiB,CAAC4N,WAAW,EAAE;MAClD,OAAO,IAAI,CAACje,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC;KACzE,MAAM,IAAIiS,KAAK,IAAIxS,SAAS,CAAC2Q,iBAAiB,CAACnJ,QAAQ,EAAE;MACtD,OAAO,IAAI,CAAClH,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;;IAExE,OAAO,EAAE;EACb;;;uBA7tCS0O,4BAA4B,EAAA/O,EAAA,CAAAse,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAxe,EAAA,CAAAse,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA1e,EAAA,CAAAse,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA5e,EAAA,CAAAse,iBAAA,CAAAO,EAAA,CAAAC,UAAA,GAAA9e,EAAA,CAAAse,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAAhf,EAAA,CAAAse,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAAlf,EAAA,CAAAse,iBAAA,CAAAte,EAAA,CAAAmf,QAAA;IAAA;EAAA;;;YAA5BpQ,4BAA4B;MAAAqQ,SAAA;MAAAC,QAAA,GAAArf,EAAA,CAAAsf,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBzC5f,EAAA,CAAAU,cAAA,aAAqG;UAEzDV,EAAA,CAAAW,MAAA,GAAqD;UAAAX,EAAA,CAAAY,YAAA,EAAM;UAC/FZ,EAAA,CAAAC,SAAA,sBAAoF;UACxFD,EAAA,CAAAY,YAAA,EAAM;UACNZ,EAAA,CAAAU,cAAA,aAA6F;UACzFV,EAAA,CAAAsB,UAAA,IAAAwe,qDAAA,2BAA8S;UAC9S9f,EAAA,CAAAsB,UAAA,IAAAye,gDAAA,sBAAuT;UAC3T/f,EAAA,CAAAY,YAAA,EAAM;UAGVZ,EAAA,CAAAU,cAAA,cAA8F;UAA/DV,EAAA,CAAAa,UAAA,sBAAAmf,+DAAA;YAAA,OAAYH,GAAA,CAAAxI,cAAA,EAAgB;UAAA,EAAC;UACxDrX,EAAA,CAAAU,cAAA,iBAAoF;UAQ7DV,EAAA,CAAAa,UAAA,2BAAAof,sEAAAlf,MAAA;YAAA,OAAA8e,GAAA,CAAAhN,UAAA,CAAA1P,MAAA,GAAApC,MAAA;UAAA,EAA+B;UAHtCf,EAAA,CAAAY,YAAA,EAKE;UACFZ,EAAA,CAAAU,cAAA,iBAAwB;UAAAV,EAAA,CAAAW,MAAA,IAAgD;UAAAX,EAAA,CAAAY,YAAA,EAAQ;UAIxFZ,EAAA,CAAAU,cAAA,eAAmB;UAKHV,EAAA,CAAAa,UAAA,2BAAAqf,sEAAAnf,MAAA;YAAA,OAAA8e,GAAA,CAAAhN,UAAA,CAAApP,IAAA,GAAA1C,MAAA;UAAA,EAA6B;UAHrCf,EAAA,CAAAY,YAAA,EAKE;UACFZ,EAAA,CAAAU,cAAA,iBAAsB;UAAAV,EAAA,CAAAW,MAAA,IAAiD;UAAAX,EAAA,CAAAY,YAAA,EAAQ;UAIvFZ,EAAA,CAAAU,cAAA,eAAmB;UAIPV,EAAA,CAAAa,UAAA,yBAAAsf,0EAAApf,MAAA;YAAA,OAAA8e,GAAA,CAAAhN,UAAA,CAAAC,YAAA,GAAA/R,MAAA;UAAA,EAAmC;UAUtCf,EAAA,CAAAY,YAAA,EAAc;UAIvBZ,EAAA,CAAAU,cAAA,eAAmB;UAICV,EAAA,CAAAa,UAAA,2BAAAuf,2EAAArf,MAAA;YAAA,OAAA8e,GAAA,CAAAhN,UAAA,CAAAvP,MAAA,GAAAvC,MAAA;UAAA,EAA+B;UAK1Cf,EAAA,CAAAY,YAAA,EAAa;UACdZ,EAAA,CAAAU,cAAA,iBAA2C;UAAAV,EAAA,CAAAW,MAAA,IAAoD;UAAAX,EAAA,CAAAY,YAAA,EAAQ;UAI/GZ,EAAA,CAAAU,cAAA,eAAmB;UAIPV,EAAA,CAAAa,UAAA,yBAAAwf,0EAAAtf,MAAA;YAAA,OAAA8e,GAAA,CAAAhN,UAAA,CAAAE,QAAA,GAAAhS,MAAA;UAAA,EAA+B;UAUlCf,EAAA,CAAAY,YAAA,EAAc;UAIvBZ,EAAA,CAAAU,cAAA,eAAmB;UAIPV,EAAA,CAAAa,UAAA,yBAAAyf,0EAAAvf,MAAA;YAAA,OAAA8e,GAAA,CAAAhN,UAAA,CAAAnO,YAAA,GAAA3D,MAAA;UAAA,EAAmC;UAUtCf,EAAA,CAAAY,YAAA,EAAc;UAGvBZ,EAAA,CAAAU,cAAA,eAAoC;UAIpBV,EAAA,CAAAa,UAAA,2BAAA0f,2EAAAxf,MAAA;YAAA,OAAA8e,GAAA,CAAAhN,UAAA,CAAAG,QAAA,GAAAjS,MAAA;UAAA,EAAiC,sBAAAyf,sEAAA;YAAA,OAMrBX,GAAA,CAAA/G,gBAAA,CAAA+G,GAAA,CAAAhN,UAAA,CAAAG,QAAA,CAAqC;UAAA,EANhB,qBAAAyN,qEAAA;YAAA,OAOtBZ,GAAA,CAAA/G,gBAAA,CAAA+G,GAAA,CAAAhN,UAAA,CAAAG,QAAA,CAAqC;UAAA,EAPf;UAQxChT,EAAA,CAAAY,YAAA,EAAa;UACdZ,EAAA,CAAAU,cAAA,iBAAiD;UAAAV,EAAA,CAAAW,MAAA,IAAuD;UAAAX,EAAA,CAAAY,YAAA,EAAQ;UAGxHZ,EAAA,CAAAU,cAAA,eAAoE;UAIpDV,EAAA,CAAAa,UAAA,2BAAA6f,2EAAA3f,MAAA;YAAA,OAAA8e,GAAA,CAAAhN,UAAA,CAAAI,MAAA,GAAAlS,MAAA;UAAA,EAA+B,sBAAA4f,sEAAA;YAAA,OAOnBd,GAAA,CAAA9G,cAAA,CAAA8G,GAAA,CAAAhN,UAAA,CAAAI,MAAA,CAAiC;UAAA,EAPd,qBAAA2N,qEAAA;YAAA,OAQpBf,GAAA,CAAA9G,cAAA,CAAA8G,GAAA,CAAAhN,UAAA,CAAAI,MAAA,CAAiC;UAAA,EARb;UAFvCjT,EAAA,CAAAY,YAAA,EAWE;UACFZ,EAAA,CAAAU,cAAA,iBAA+C;UAAAV,EAAA,CAAAW,MAAA,IAAwD;UAAAX,EAAA,CAAAY,YAAA,EAAQ;UAGvHZ,EAAA,CAAAU,cAAA,eAAoD;UAChDV,EAAA,CAAAC,SAAA,oBAGY;UAChBD,EAAA,CAAAY,YAAA,EAAM;UAKlBZ,EAAA,CAAAU,cAAA,eAAqD;UACjDV,EAAA,CAAAsB,UAAA,KAAAuf,iDAAA,0BA4IW;UACf7gB,EAAA,CAAAY,YAAA,EAAM;UAENZ,EAAA,CAAAU,cAAA,eAAqD;UACjDV,EAAA,CAAAsB,UAAA,KAAAwf,iDAAA,0BA8IW;UACf9gB,EAAA,CAAAY,YAAA,EAAM;UACNZ,EAAA,CAAAU,cAAA,sBAYC;UAVGV,EAAA,CAAAa,UAAA,+BAAAkgB,+EAAAhgB,MAAA;YAAA,OAAA8e,GAAA,CAAAnQ,WAAA,GAAA3O,MAAA;UAAA,EAA6B;UAUhCf,EAAA,CAAAY,YAAA,EAAa;UAGdZ,EAAA,CAAAU,cAAA,eAA2D;UACPV,EAAA,CAAAa,UAAA,2BAAAmgB,yEAAAjgB,MAAA;YAAA,OAAA8e,GAAA,CAAA9P,uBAAA,GAAAhP,MAAA;UAAA,EAAqC;UACjFf,EAAA,CAAAU,cAAA,eAA+B;UACyCV,EAAA,CAAAW,MAAA,IAA8C;UAAAX,EAAA,CAAAY,YAAA,EAAQ;UAC1HZ,EAAA,CAAAU,cAAA,eAAiB;UAILV,EAAA,CAAAa,UAAA,2BAAAogB,2EAAAlgB,MAAA;YAAA,OAAA8e,GAAA,CAAA3I,YAAA,GAAAnW,MAAA;UAAA,EAA0B;UAMjCf,EAAA,CAAAY,YAAA,EAAa;UAGtBZ,EAAA,CAAAU,cAAA,eAAqE;UACwDV,EAAA,CAAAa,UAAA,mBAAAqgB,iEAAA;YAAA,OAAArB,GAAA,CAAA9P,uBAAA,GAAmC,KAAK;UAAA,EAAC;UAAC/P,EAAA,CAAAY,YAAA,EAAW;UAC9KZ,EAAA,CAAAU,cAAA,oBAAqL;UAA1FV,EAAA,CAAAa,UAAA,mBAAAsgB,iEAAA;YAAA,OAAStB,GAAA,CAAAlF,cAAA,EAAgB;UAAA,EAAC;UAAgE3a,EAAA,CAAAY,YAAA,EAAW;UAM5MZ,EAAA,CAAAU,cAAA,eAA2D;UAC0BV,EAAA,CAAAa,UAAA,2BAAAugB,yEAAArgB,MAAA;YAAA,OAAA8e,GAAA,CAAA7P,4BAAA,GAAAjP,MAAA;UAAA,EAA0C;UACvHf,EAAA,CAAAU,cAAA,eAA2C;UACyBV,EAAA,CAAAW,MAAA,IAA8C;UAAAX,EAAA,CAAAY,YAAA,EAAQ;UACtHZ,EAAA,CAAAU,cAAA,eAAiE;UAGzDV,EAAA,CAAAa,UAAA,yBAAAwgB,0EAAAtgB,MAAA;YAAA,OAAA8e,GAAA,CAAAzI,gBAAA,GAAArW,MAAA;UAAA,EAA4B;UAQ/Bf,EAAA,CAAAY,YAAA,EAAc;UAGvBZ,EAAA,CAAAU,cAAA,eAA2C;UAC6BV,EAAA,CAAAW,MAAA,IAA8C;UAAAX,EAAA,CAAAY,YAAA,EAAQ;UAC1HZ,EAAA,CAAAU,cAAA,eAAiE;UAIrDV,EAAA,CAAAa,UAAA,2BAAAygB,2EAAAvgB,MAAA;YAAA,OAAA8e,GAAA,CAAA1I,iBAAA,GAAApW,MAAA;UAAA,EAA+B;UAMtCf,EAAA,CAAAY,YAAA,EAAa;UAGtBZ,EAAA,CAAAU,cAAA,eAAqE;UACwDV,EAAA,CAAAa,UAAA,mBAAA0gB,iEAAA;YAAA,OAAA1B,GAAA,CAAA7P,4BAAA,GAAwC,KAAK;UAAA,EAAC;UAAChQ,EAAA,CAAAY,YAAA,EAAW;UACnLZ,EAAA,CAAAU,cAAA,oBAAiQ;UAAtKV,EAAA,CAAAa,UAAA,mBAAA2gB,iEAAA;YAAA,OAAS3B,GAAA,CAAA/E,mBAAA,EAAqB;UAAA,EAAC;UAAuI9a,EAAA,CAAAY,YAAA,EAAW;UAkCxRZ,EAAA,CAAAU,cAAA,eAA2D;UAC0BV,EAAA,CAAAa,UAAA,2BAAA4gB,yEAAA1gB,MAAA;YAAA,OAAA8e,GAAA,CAAA5P,kCAAA,GAAAlP,MAAA;UAAA,EAAgD;UAW7Hf,EAAA,CAAAU,cAAA,eAAkB;UAAsBV,EAAA,CAAAsB,UAAA,KAAAogB,8CAAA,oBAAmF;UAAA1hB,EAAA,CAAAY,YAAA,EAAM;UACjIZ,EAAA,CAAAsB,UAAA,KAAAqgB,gDAAA,uBAkGU;UACV3hB,EAAA,CAAAU,cAAA,eAAqE;UACwDV,EAAA,CAAAa,UAAA,mBAAA+gB,iEAAA;YAAA,OAAA/B,GAAA,CAAA5P,kCAAA,GAA8C,KAAK;UAAA,EAAC;UAACjQ,EAAA,CAAAY,YAAA,EAAW;UACzLZ,EAAA,CAAAsB,UAAA,KAAAugB,iDAAA,uBAA0L;UAC9L7hB,EAAA,CAAAY,YAAA,EAAM;UAOdZ,EAAA,CAAAU,cAAA,eAA2D;UACwBV,EAAA,CAAAa,UAAA,2BAAAihB,yEAAA/gB,MAAA;YAAA,OAAA8e,GAAA,CAAA3P,8BAAA,GAAAnP,MAAA;UAAA,EAA4C;UACvHf,EAAA,CAAAU,cAAA,eAA+B;UAE4BV,EAAA,CAAAa,UAAA,8BAAAkhB,mFAAAhhB,MAAA;YAAA,OAAA8e,GAAA,CAAAmC,UAAA,GAAAjhB,MAAA;UAAA,EAA2B;UAE7Ef,EAAA,CAAAY,YAAA,EAAkB;UAEvBZ,EAAA,CAAAU,cAAA,eAAwE;UAC+EV,EAAA,CAAAa,UAAA,mBAAAohB,iEAAA;YAAA,OAASpC,GAAA,CAAA5G,gBAAA,EAAkB;UAAA,EAAC;UAACjZ,EAAA,CAAAY,YAAA,EAAW;UAGnMZ,EAAA,CAAAU,cAAA,eAAkB;UAAsBV,EAAA,CAAAsB,UAAA,KAAA4gB,8CAAA,oBAAoF;UAAAliB,EAAA,CAAAY,YAAA,EAAM;UAClIZ,EAAA,CAAAsB,UAAA,KAAA6gB,gDAAA,uBAkGU;UACVniB,EAAA,CAAAU,cAAA,eAAqE;UACwDV,EAAA,CAAAa,UAAA,mBAAAuhB,iEAAA;YAAA,OAAAvC,GAAA,CAAA3P,8BAAA,GAA0C,KAAK;UAAA,EAAC;UAAClQ,EAAA,CAAAY,YAAA,EAAW;UACrLZ,EAAA,CAAAsB,UAAA,KAAA+gB,iDAAA,uBAA0L;UAC9LriB,EAAA,CAAAY,YAAA,EAAM;;;UA9wB8BZ,EAAA,CAAAgD,SAAA,GAAqD;UAArDhD,EAAA,CAAAiD,iBAAA,CAAA4c,GAAA,CAAAzf,WAAA,CAAAC,SAAA,6BAAqD;UAClDL,EAAA,CAAAgD,SAAA,GAAe;UAAfhD,EAAA,CAAAE,UAAA,UAAA2f,GAAA,CAAA5O,KAAA,CAAe,SAAA4O,GAAA,CAAA1O,IAAA;UAGtCnR,EAAA,CAAAgD,SAAA,GAAoH;UAApHhD,EAAA,CAAAE,UAAA,SAAA2f,GAAA,CAAArO,WAAA,CAAAxR,EAAA,CAAAsiB,eAAA,MAAAC,GAAA,EAAA1C,GAAA,CAAAxP,cAAA,CAAAoB,eAAA,CAAAM,gBAAA,EAAA8N,GAAA,CAAAxP,cAAA,CAAAoB,eAAA,CAAAC,gBAAA,GAAoH;UACA1R,EAAA,CAAAgD,SAAA,GAA2E;UAA3EhD,EAAA,CAAAE,UAAA,SAAA2f,GAAA,CAAArO,WAAA,CAAAxR,EAAA,CAAAmJ,eAAA,MAAAqZ,GAAA,EAAA3C,GAAA,CAAA/f,SAAA,CAAAwQ,WAAA,CAAAmB,eAAA,CAAAgR,gBAAA,GAA2E;UAIjNziB,EAAA,CAAAgD,SAAA,GAAwB;UAAxBhD,EAAA,CAAAE,UAAA,cAAA2f,GAAA,CAAA3M,UAAA,CAAwB;UACjBlT,EAAA,CAAAgD,SAAA,GAAmB;UAAnBhD,EAAA,CAAAE,UAAA,oBAAmB,WAAA2f,GAAA,CAAAzf,WAAA,CAAAC,SAAA;UAQLL,EAAA,CAAAgD,SAAA,GAA+B;UAA/BhD,EAAA,CAAAE,UAAA,YAAA2f,GAAA,CAAAhN,UAAA,CAAA1P,MAAA,CAA+B;UAGdnD,EAAA,CAAAgD,SAAA,GAAgD;UAAhDhD,EAAA,CAAAiD,iBAAA,CAAA4c,GAAA,CAAAzf,WAAA,CAAAC,SAAA,wBAAgD;UAShEL,EAAA,CAAAgD,SAAA,GAA6B;UAA7BhD,EAAA,CAAAE,UAAA,YAAA2f,GAAA,CAAAhN,UAAA,CAAApP,IAAA,CAA6B;UAGfzD,EAAA,CAAAgD,SAAA,GAAiD;UAAjDhD,EAAA,CAAAiD,iBAAA,CAAA4c,GAAA,CAAAzf,WAAA,CAAAC,SAAA,yBAAiD;UAQnEL,EAAA,CAAAgD,SAAA,GAAmC;UAAnChD,EAAA,CAAAE,UAAA,UAAA2f,GAAA,CAAAhN,UAAA,CAAAC,YAAA,CAAmC,gBAAA+M,GAAA,CAAAzf,WAAA,CAAAC,SAAA,uCAAAL,EAAA,CAAAQ,eAAA,MAAAkiB,GAAA;UAgBP1iB,EAAA,CAAAgD,SAAA,GAAkB;UAAlBhD,EAAA,CAAAE,UAAA,mBAAkB,uCAAA2f,GAAA,CAAAhN,UAAA,CAAAvP,MAAA,aAAAuc,GAAA,CAAAxN,UAAA;UAQPrS,EAAA,CAAAgD,SAAA,GAAoD;UAApDhD,EAAA,CAAAiD,iBAAA,CAAA4c,GAAA,CAAAzf,WAAA,CAAAC,SAAA,4BAAoD;UAQ3FL,EAAA,CAAAgD,SAAA,GAA+B;UAA/BhD,EAAA,CAAAE,UAAA,UAAA2f,GAAA,CAAAhN,UAAA,CAAAE,QAAA,CAA+B,gBAAA8M,GAAA,CAAAzf,WAAA,CAAAC,SAAA,yCAAAL,EAAA,CAAAQ,eAAA,MAAAmiB,IAAA;UAkB/B3iB,EAAA,CAAAgD,SAAA,GAAmC;UAAnChD,EAAA,CAAAE,UAAA,UAAA2f,GAAA,CAAAhN,UAAA,CAAAnO,YAAA,CAAmC,gBAAAmb,GAAA,CAAAzf,WAAA,CAAAC,SAAA,yCAAAL,EAAA,CAAAQ,eAAA,MAAAoiB,IAAA;UAiB/B5iB,EAAA,CAAAgD,SAAA,GAAiC;UAAjChD,EAAA,CAAAE,UAAA,YAAA2f,GAAA,CAAAhN,UAAA,CAAAG,QAAA,CAAiC,iDAAA6M,GAAA,CAAAlQ,WAAA;UASQ3P,EAAA,CAAAgD,SAAA,GAAuD;UAAvDhD,EAAA,CAAAiD,iBAAA,CAAA4c,GAAA,CAAAzf,WAAA,CAAAC,SAAA,+BAAuD;UAOhGL,EAAA,CAAAgD,SAAA,GAA+B;UAA/BhD,EAAA,CAAAE,UAAA,YAAA2f,GAAA,CAAAhN,UAAA,CAAAI,MAAA,CAA+B,iDAAA4M,GAAA,CAAAhQ,SAAA,aAAAgQ,GAAA,CAAA/P,SAAA;UAUQ9P,EAAA,CAAAgD,SAAA,GAAwD;UAAxDhD,EAAA,CAAAiD,iBAAA,CAAA4c,GAAA,CAAAzf,WAAA,CAAAC,SAAA,gCAAwD;UAckEL,EAAA,CAAAgD,SAAA,GAA0B;UAA1BhD,EAAA,CAAAE,UAAA,SAAA2f,GAAA,CAAAxe,oBAAA,CAA0B;UAgJrBrB,EAAA,CAAAgD,SAAA,GAA2B;UAA3BhD,EAAA,CAAAE,UAAA,SAAA2f,GAAA,CAAAla,qBAAA,CAA2B;UAiJzN3F,EAAA,CAAAgD,SAAA,GAAgB;UAAhBhD,EAAA,CAAAE,UAAA,iBAAgB,gBAAA2f,GAAA,CAAAnQ,WAAA,aAAAmQ,GAAA,CAAA9K,OAAA,aAAA8K,GAAA,CAAAlI,OAAA,aAAAkI,GAAA,CAAAtM,WAAA,cAAAsM,GAAA,CAAA/K,MAAA,CAAAiC,IAAA,CAAA8I,GAAA,iBAAAA,GAAA,CAAAzM,UAAA,cAAAyM,GAAA,CAAAxM,QAAA,UAAAwM,GAAA,CAAAvM,IAAA,YAAAuM,GAAA,CAAAhN,UAAA,gBAAAgN,GAAA,CAAAzf,WAAA,CAAAC,SAAA;UAeqFL,EAAA,CAAAgD,SAAA,GAA4B;UAA5BhD,EAAA,CAAA6C,UAAA,CAAA7C,EAAA,CAAAQ,eAAA,MAAAqiB,IAAA,EAA4B;UAAvH7iB,EAAA,CAAAE,UAAA,WAAA2f,GAAA,CAAA5L,0BAAA,CAAqC,YAAA4L,GAAA,CAAA9P,uBAAA;UAE6B/P,EAAA,CAAAgD,SAAA,GAA8C;UAA9ChD,EAAA,CAAAiD,iBAAA,CAAA4c,GAAA,CAAAzf,WAAA,CAAAC,SAAA,sBAA8C;UAGtGL,EAAA,CAAAgD,SAAA,GAAkB;UAAlBhD,EAAA,CAAAE,UAAA,mBAAkB,uCAAA2f,GAAA,CAAA3I,YAAA,aAAA2I,GAAA,CAAApQ,oBAAA,iCAAAoQ,GAAA,CAAAzf,WAAA,CAAAC,SAAA;UAYmCL,EAAA,CAAAgD,SAAA,GAAuD;UAAvDhD,EAAA,CAAAE,UAAA,UAAA2f,GAAA,CAAAzf,WAAA,CAAAC,SAAA,yBAAuD;UACnFL,EAAA,CAAAgD,SAAA,GAAqD;UAArDhD,EAAA,CAAAE,UAAA,UAAA2f,GAAA,CAAAzf,WAAA,CAAAC,SAAA,uBAAqD,aAAAwf,GAAA,CAAA3I,YAAA,YAAA2I,GAAA,CAAA3I,YAAA,IAAAtT,SAAA;UAOyC5D,EAAA,CAAAgD,SAAA,GAA4B;UAA5BhD,EAAA,CAAA6C,UAAA,CAAA7C,EAAA,CAAAQ,eAAA,MAAAqiB,IAAA,EAA4B;UAA7J7iB,EAAA,CAAAE,UAAA,WAAA2f,GAAA,CAAAzf,WAAA,CAAAC,SAAA,uCAAsE,YAAAwf,GAAA,CAAA7P,4BAAA;UAERhQ,EAAA,CAAAgD,SAAA,GAA8C;UAA9ChD,EAAA,CAAAiD,iBAAA,CAAA4c,GAAA,CAAAzf,WAAA,CAAAC,SAAA,sBAA8C;UAItGL,EAAA,CAAAgD,SAAA,GAA4B;UAA5BhD,EAAA,CAAAE,UAAA,UAAA2f,GAAA,CAAAzI,gBAAA,CAA4B,gBAAAyI,GAAA,CAAAzf,WAAA,CAAAC,SAAA;UAYgCL,EAAA,CAAAgD,SAAA,GAA8C;UAA9ChD,EAAA,CAAAiD,iBAAA,CAAA4c,GAAA,CAAAzf,WAAA,CAAAC,SAAA,sBAA8C;UAGtGL,EAAA,CAAAgD,SAAA,GAAkB;UAAlBhD,EAAA,CAAAE,UAAA,mBAAkB,uCAAA2f,GAAA,CAAA1I,iBAAA,aAAA0I,GAAA,CAAArQ,cAAA,iCAAAqQ,GAAA,CAAAzf,WAAA,CAAAC,SAAA;UAYmCL,EAAA,CAAAgD,SAAA,GAAuD;UAAvDhD,EAAA,CAAAE,UAAA,UAAA2f,GAAA,CAAAzf,WAAA,CAAAC,SAAA,yBAAuD;UACnFL,EAAA,CAAAgD,SAAA,GAAqD;UAArDhD,EAAA,CAAAE,UAAA,UAAA2f,GAAA,CAAAzf,WAAA,CAAAC,SAAA,uBAAqD,aAAAwf,GAAA,CAAA1I,iBAAA,YAAA0I,GAAA,CAAA1I,iBAAA,IAAAvT,SAAA,IAAAic,GAAA,CAAAzI,gBAAA,YAAAyI,GAAA,CAAAzI,gBAAA,IAAAxT,SAAA;UAmC+C5D,EAAA,CAAAgD,SAAA,GAA6B;UAA7BhD,EAAA,CAAA6C,UAAA,CAAA7C,EAAA,CAAAQ,eAAA,MAAAsiB,IAAA,EAA6B;UAApK9iB,EAAA,CAAAE,UAAA,WAAA2f,GAAA,CAAAzf,WAAA,CAAAC,SAAA,uCAAsE,YAAAwf,GAAA,CAAA5P,kCAAA;UAWPjQ,EAAA,CAAAgD,SAAA,GAAsB;UAAtBhD,EAAA,CAAAE,UAAA,SAAA2f,GAAA,CAAAzP,gBAAA,CAAsB;UAkBtFpQ,EAAA,CAAAgD,SAAA,GAAsB;UAAtBhD,EAAA,CAAAE,UAAA,SAAA2f,GAAA,CAAAhU,gBAAA,CAAsB;UAmF0C7L,EAAA,CAAAgD,SAAA,GAAuD;UAAvDhD,EAAA,CAAAE,UAAA,UAAA2f,GAAA,CAAAzf,WAAA,CAAAC,SAAA,yBAAuD;UACxEL,EAAA,CAAAgD,SAAA,GAAgB;UAAhBhD,EAAA,CAAAE,UAAA,SAAA2f,GAAA,CAAA9T,UAAA,CAAgB;UASmE/L,EAAA,CAAAgD,SAAA,GAA6B;UAA7BhD,EAAA,CAAA6C,UAAA,CAAA7C,EAAA,CAAAQ,eAAA,MAAAsiB,IAAA,EAA6B;UAA9J9iB,EAAA,CAAAE,UAAA,WAAA2f,GAAA,CAAAzf,WAAA,CAAAC,SAAA,qCAAoE,YAAAwf,GAAA,CAAA3P,8BAAA;UAGflQ,EAAA,CAAAgD,SAAA,GAA2B;UAA3BhD,EAAA,CAAAE,UAAA,eAAA2f,GAAA,CAAAmC,UAAA,CAA2B,sBAAAnC,GAAA,CAAA7G,iBAAA,CAAAjC,IAAA,CAAA8I,GAAA,cAAAA,GAAA,CAAArJ,eAAA;UAK9CxW,EAAA,CAAAgD,SAAA,GAAgE;UAAhEhD,EAAA,CAAAE,UAAA,aAAA2f,GAAA,CAAAzf,WAAA,CAAAC,SAAA,+BAAgE;UAGnCL,EAAA,CAAAgD,SAAA,GAAuB;UAAvBhD,EAAA,CAAAE,UAAA,SAAA2f,GAAA,CAAA1P,iBAAA,CAAuB;UAkBvFnQ,EAAA,CAAAgD,SAAA,GAAsB;UAAtBhD,EAAA,CAAAE,UAAA,SAAA2f,GAAA,CAAAhU,gBAAA,CAAsB;UAmF0C7L,EAAA,CAAAgD,SAAA,GAAuD;UAAvDhD,EAAA,CAAAE,UAAA,UAAA2f,GAAA,CAAAzf,WAAA,CAAAC,SAAA,yBAAuD;UACxEL,EAAA,CAAAgD,SAAA,GAAgB;UAAhBhD,EAAA,CAAAE,UAAA,SAAA2f,GAAA,CAAA9T,UAAA,CAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}