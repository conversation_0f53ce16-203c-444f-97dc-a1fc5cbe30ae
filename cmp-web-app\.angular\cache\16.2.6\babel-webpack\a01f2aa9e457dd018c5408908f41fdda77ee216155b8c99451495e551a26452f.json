{"ast": null, "code": "export default {\n  ACCOUNT: {\n    VIEW_LIST: \"searchUser\",\n    VIEW_DETAIL: \"getUser\",\n    CREATE: \"createUser\",\n    UPDATE: \"updateUser\",\n    DELETE: \"deleteUser\",\n    CHANGE_STATUS: \"updateUser\",\n    CHANGE_MANAGER_DATA: \"changeManageData\",\n    SEARCH_LOG_API: \"searchLogApi\"\n  },\n  PROFILE: {\n    VIEW: \"getProfileUser\",\n    UPDATE: \"updateProfile\"\n  },\n  ROLE: {\n    VIEW_LIST: \"searchRole\",\n    VIEW_DETAIL: \"getRole\",\n    CREATE: \"createRole\",\n    UPDATE: \"updateRole\",\n    DELETE: \"\",\n    CHANGE_STATUS: \"\"\n  },\n  PERMISSION: {\n    VIEW_LIST: \"searchPermission\",\n    VIEW_DETAIL: \"getPermission\"\n  },\n  SIM: {\n    VIEW_LIST: \"searchSim\",\n    VIEW_DETAIL: \"getSim\",\n    UPDATE: \"updateSim\",\n    CHANGE_STATUS: \"updateSim\",\n    CREATE: \"createSim\",\n    DELETE: \"deleteSim\"\n  },\n  GROUP_SIM: {\n    CREATE: \"createSimGroup\",\n    UPDATE: \"updateSimGroup\",\n    VIEW_LIST: \"searchSimGroup\",\n    VIEW_DETAIL: \"getSimGroup\",\n    DELETE: \"deleteSimGroup\"\n  },\n  CONTRACT: {\n    VIEW_LIST: \"searchContract\",\n    VIEW_DETAIL: \"getContract\"\n  },\n  RATING_PLAN: {\n    ACTIVE: \"changeStatusRatingPlan\",\n    SUPPEND: \"changeStatusRatingPlan\",\n    APPROVE: \"approveRatingPlan\",\n    CREATE: \"createRatingPlan\",\n    DELETE: \"deleteRatingPlan\",\n    ISSUE: \"issueRatingPlan\",\n    VIEW_LIST: \"searchRatingPlan\",\n    VIEW_DETAIL: \"getRatingPlan\",\n    UPDATE: \"updateRatingPlan\"\n  },\n  RATING_PLAN_SIM: {\n    REGISTER_PLAN: \"setRateSim\",\n    CANCEL_PLAN: \"cancelRateSim\",\n    REGISTER_BY_FILE: \"registerByFile\",\n    REGISTER_BY_LIST: \"registerByList\",\n    REGISTER_HISTORY: \"ratingPlanRegistrationHistory\"\n  },\n  REPORT_DYNAMIC: {\n    VIEW_LIST: \"searchRptCfg\",\n    VIEW_DETAIL: \"getRptCfg\",\n    CREATE: \"createRptCfg\",\n    UPDATE: \"updateRptCfg\",\n    DELETE: \"deleteRptCfg\"\n  },\n  REPORT_SEND: {\n    UPDATE: \"updateRptSend\"\n  },\n  GROUP_REPORT_DYNAMIC: {\n    VIEW_LIST: \"searchRptRecvGrp\",\n    VIEW_DETAIL: \"getRptRecvGrp\",\n    CREATE: \"createRptRecvGrp\",\n    UPDATE: \"updateRptRecvGrp\",\n    DELETE: \"deleteRptRecvGrp\"\n  },\n  CUSTOMER: {\n    VIEW_LIST: \"searchCustomer\",\n    VIEW_DETAIL: \"getCustomer\",\n    UPDATE: \"updateCustomer\",\n    CREATE: \"createCustomer\",\n    DELETE: \"deleteCustomer\"\n  },\n  ALERT: {\n    VIEW_LIST: \"searchAlert\",\n    VIEW_DETAIL: \"getAlert\",\n    CREATE: \"createAlert\",\n    UPDATE: \"updateAlert\",\n    DELETE: \"deleteAlert\",\n    CREATE_WALLET_THRESHOLD: \"createAlertWalletThreshold\",\n    CREATE_WALLET_EXPIRY: \"createAlertWalletExpiry\",\n    UPDATE_WALLET_THRESHOLD: \"updateAlertWalletThreshold\",\n    UPDATE_WALLET_EXPIRY: \"updateAlertWalletExpiry\",\n    CHANGE_STATUS: \"changeStatusAlert\"\n  },\n  APN_SIM: {\n    VIEW_LIST: \"searchApnSim\",\n    VIEW_DETAIL: \"getApnSim\"\n  },\n  DEVICE: {\n    VIEW_LIST: \"searchDevice\",\n    VIEW_DETAIL: \"getDevice\",\n    CREATE: \"createDevice\",\n    UPDATE: \"updateDevice\",\n    DELETE: \"deleteDevice\"\n  },\n  ALERT_RECEIVING_GROUP: {\n    VIEW_LIST: \"searchAlertRecvGrp\",\n    VIEW_DETAIL: \"getAlertRecvGrp\",\n    CREATE: \"createAlertRecvGrp\",\n    UPDATE: \"updateAlertRecvGrp\",\n    DELETE: \"deleteAlertRecvGrp\"\n  },\n  ALERT_HISTORY: {\n    VIEW_LIST: \"searchAlertLog\"\n  },\n  CONFIG_DYNAMIC_CHART: {\n    VIEW_LIST: \"searchCnfDynamicChart\",\n    VIEW_DETAIL: \"getCnfDynamicChart\",\n    CREATE: \"createCnfDynamicChart\",\n    UPDATE: \"updateCnfDynamicChart\",\n    DELETE: \"deleteCnfDynamicChart\"\n  },\n  LOG: {\n    VIEW_LIST: \"searchLog\"\n  },\n  TICKET: {\n    VIEW_LIST: \"getTicket\",\n    CREATE: \"createTicket\",\n    UPDATE: \"updateTicket\"\n  },\n  POLICY: {\n    PERSONAL: \"getPersonalDataPolicy\"\n  },\n  DYNAMICCHART: {\n    VIEW_LIST: \"getDashBoardContent\"\n  },\n  DATAPOOL: {\n    VIEW_WALLET: \"searchWallet\",\n    CREATE_WALLET: \"accuracyWallet\",\n    SHARE_WALLET: \"shareWallet\",\n    CREATE_SHARE: \"createShareInfo\",\n    VIEW_SHARE: \"searchShareInfo\",\n    VIEW_HISTORY_WALLET: \"walletHistory\"\n    // ALERT_WALLET_THRESHOLD: \"alertWalletThreshold\",\n    // ALERT_WALLET_EXPIRY: \"alertWalletExpiry\",\n  },\n\n  SHARE_GROUP: {\n    CREATE: \"createShareGroup\",\n    EDIT: \"editShareGroup\",\n    VIEW_LIST: \"searchShareGroup\",\n    VIEW_DETAIL: \"detailShareGroup\",\n    DELETE: \"deleteShareGroup\"\n  },\n  DIAGNOSE: {\n    VIEW_LIST: \"searchDiagnose\"\n  },\n  AUTO_SHARE_GROUP: {\n    CREATE: \"createShareGroup\",\n    EDIT: \"editShareGroup\",\n    VIEW_LIST: \"searchShareGroup\",\n    VIEW_DETAIL: \"detailShareGroup\",\n    DELETE: \"deleteShareGroup\"\n  },\n  THIRD_PARTY_API: {\n    GRANT_PERMISSION_3RD_API: \"grantPermission3rdApi\"\n    // GET_SIM_INFO_TO_ASSIGN: \"getSimInfoToAssign\",\n    // GRANT_PERMISSION_3RD_API: \"getSimInfo\",\n    // GRANT_PERMISSION_3RD_API: \"getListSimByAccount\",\n    // GRANT_PERMISSION_3RD_API: \"getSimInfoToImport\",\n    // GRANT_PERMISSION_3RD_API: \"count-by-provinces\",\n  },\n\n  GUIDE: {\n    INTEGRATION: \"guideIntegration\"\n  }\n};", "map": {"version": 3, "names": ["ACCOUNT", "VIEW_LIST", "VIEW_DETAIL", "CREATE", "UPDATE", "DELETE", "CHANGE_STATUS", "CHANGE_MANAGER_DATA", "SEARCH_LOG_API", "PROFILE", "VIEW", "ROLE", "PERMISSION", "SIM", "GROUP_SIM", "CONTRACT", "RATING_PLAN", "ACTIVE", "SUPPEND", "APPROVE", "ISSUE", "RATING_PLAN_SIM", "REGISTER_PLAN", "CANCEL_PLAN", "REGISTER_BY_FILE", "REGISTER_BY_LIST", "REGISTER_HISTORY", "REPORT_DYNAMIC", "REPORT_SEND", "GROUP_REPORT_DYNAMIC", "CUSTOMER", "ALERT", "CREATE_WALLET_THRESHOLD", "CREATE_WALLET_EXPIRY", "UPDATE_WALLET_THRESHOLD", "UPDATE_WALLET_EXPIRY", "APN_SIM", "DEVICE", "ALERT_RECEIVING_GROUP", "ALERT_HISTORY", "CONFIG_DYNAMIC_CHART", "LOG", "TICKET", "POLICY", "PERSONAL", "DYNAMICCHART", "DATAPOOL", "VIEW_WALLET", "CREATE_WALLET", "SHARE_WALLET", "CREATE_SHARE", "VIEW_SHARE", "VIEW_HISTORY_WALLET", "SHARE_GROUP", "EDIT", "DIAGNOSE", "AUTO_SHARE_GROUP", "THIRD_PARTY_API", "GRANT_PERMISSION_3RD_API", "GUIDE", "INTEGRATION"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\comon\\permissions.ts"], "sourcesContent": ["export default {\r\n    ACCOUNT: {\r\n        VIEW_LIST: \"searchUser\",\r\n        VIEW_DETAIL: \"getUser\",\r\n        CREATE: \"createUser\",\r\n        UPDATE: \"updateUser\",\r\n        DELETE: \"deleteUser\",\r\n        CHANGE_STATUS: \"updateUser\",\r\n        CHANGE_MANAGER_DATA: \"changeManageData\",\r\n        SEARCH_LOG_API:\"searchLogApi\"\r\n    },\r\n    PROFILE: {\r\n        VIEW: \"getProfileUser\",\r\n        UPDATE: \"updateProfile\",\r\n    },\r\n    ROLE: {\r\n        VIEW_LIST: \"searchRole\",\r\n        VIEW_DETAIL: \"getRole\",\r\n        CREATE: \"createRole\",\r\n        UPDATE: \"updateRole\",\r\n        DELETE: \"\",\r\n        CHANGE_STATUS: \"\"\r\n    },\r\n    PERMISSION: {\r\n        VIEW_LIST: \"searchPermission\",\r\n        VIEW_DETAIL: \"getPermission\",\r\n    },\r\n    SIM: {\r\n        VIEW_LIST: \"searchSim\",\r\n        VIEW_DETAIL: \"getSim\",\r\n        UPDATE: \"updateSim\",\r\n        CHANGE_STATUS: \"updateSim\",\r\n        CREATE: \"createSim\",\r\n        DELETE : \"deleteSim\",\r\n    },\r\n    GROUP_SIM: {\r\n        CREATE: \"createSimGroup\",\r\n        UPDATE: \"updateSimGroup\",\r\n        VIEW_LIST:\"searchSimGroup\",\r\n        VIEW_DETAIL:\"getSimGroup\",\r\n        DELETE:\"deleteSimGroup\"\r\n    },\r\n    CONTRACT: {\r\n        VIEW_LIST: \"searchContract\",\r\n        VIEW_DETAIL: \"getContract\"\r\n    },\r\n    RATING_PLAN: {\r\n        ACTIVE: \"changeStatusRatingPlan\",\r\n        SUPPEND: \"changeStatusRatingPlan\",\r\n        APPROVE: \"approveRatingPlan\",\r\n        CREATE: \"createRatingPlan\",\r\n        DELETE: \"deleteRatingPlan\",\r\n        ISSUE: \"issueRatingPlan\",\r\n        VIEW_LIST: \"searchRatingPlan\",\r\n        VIEW_DETAIL: \"getRatingPlan\",\r\n        UPDATE: \"updateRatingPlan\"\r\n    },\r\n    RATING_PLAN_SIM: {\r\n        REGISTER_PLAN: \"setRateSim\",\r\n        CANCEL_PLAN: \"cancelRateSim\",\r\n        REGISTER_BY_FILE: \"registerByFile\",\r\n        REGISTER_BY_LIST: \"registerByList\",\r\n        REGISTER_HISTORY: \"ratingPlanRegistrationHistory\"\r\n    },\r\n    REPORT_DYNAMIC: {\r\n        VIEW_LIST: \"searchRptCfg\",\r\n        VIEW_DETAIL: \"getRptCfg\",\r\n        CREATE: \"createRptCfg\",\r\n        UPDATE:\"updateRptCfg\",\r\n        DELETE: \"deleteRptCfg\",\r\n    },\r\n    REPORT_SEND: {\r\n        UPDATE: \"updateRptSend\"\r\n    },\r\n    GROUP_REPORT_DYNAMIC: {\r\n        VIEW_LIST: \"searchRptRecvGrp\",\r\n        VIEW_DETAIL: \"getRptRecvGrp\",\r\n        CREATE: \"createRptRecvGrp\",\r\n        UPDATE:\"updateRptRecvGrp\",\r\n        DELETE: \"deleteRptRecvGrp\",\r\n    },\r\n    CUSTOMER: {\r\n        VIEW_LIST:\"searchCustomer\",\r\n        VIEW_DETAIL:\"getCustomer\",\r\n        UPDATE:\"updateCustomer\",\r\n        CREATE:\"createCustomer\",\r\n        DELETE:\"deleteCustomer\"\r\n    },\r\n    ALERT: {\r\n        VIEW_LIST: \"searchAlert\",\r\n        VIEW_DETAIL: \"getAlert\",\r\n        CREATE: \"createAlert\",\r\n        UPDATE: \"updateAlert\",\r\n        DELETE: \"deleteAlert\",\r\n        CREATE_WALLET_THRESHOLD: \"createAlertWalletThreshold\",\r\n        CREATE_WALLET_EXPIRY: \"createAlertWalletExpiry\",\r\n        UPDATE_WALLET_THRESHOLD: \"updateAlertWalletThreshold\",\r\n        UPDATE_WALLET_EXPIRY: \"updateAlertWalletExpiry\",\r\n        CHANGE_STATUS: \"changeStatusAlert\",\r\n    },\r\n    APN_SIM: {\r\n        VIEW_LIST: \"searchApnSim\",\r\n        VIEW_DETAIL: \"getApnSim\",\r\n    },\r\n    DEVICE: {\r\n        VIEW_LIST: \"searchDevice\",\r\n        VIEW_DETAIL: \"getDevice\",\r\n        CREATE: \"createDevice\",\r\n        UPDATE:\"updateDevice\",\r\n        DELETE: \"deleteDevice\",\r\n    },\r\n    ALERT_RECEIVING_GROUP: {\r\n        VIEW_LIST: \"searchAlertRecvGrp\",\r\n        VIEW_DETAIL: \"getAlertRecvGrp\",\r\n        CREATE: \"createAlertRecvGrp\",\r\n        UPDATE: \"updateAlertRecvGrp\",\r\n        DELETE: \"deleteAlertRecvGrp\",\r\n    },\r\n    ALERT_HISTORY: {\r\n        VIEW_LIST: \"searchAlertLog\",\r\n    },\r\n    CONFIG_DYNAMIC_CHART: {\r\n        VIEW_LIST: \"searchCnfDynamicChart\",\r\n        VIEW_DETAIL: \"getCnfDynamicChart\",\r\n        CREATE: \"createCnfDynamicChart\",\r\n        UPDATE: \"updateCnfDynamicChart\",\r\n        DELETE: \"deleteCnfDynamicChart\"\r\n    },\r\n    LOG : {\r\n        VIEW_LIST : \"searchLog\"\r\n    },\r\n    TICKET: {\r\n        VIEW_LIST: \"getTicket\",\r\n        CREATE: \"createTicket\",\r\n        UPDATE: \"updateTicket\"\r\n    },\r\n    POLICY:{\r\n        PERSONAL: \"getPersonalDataPolicy\",\r\n    },\r\n    DYNAMICCHART: {\r\n        VIEW_LIST: \"getDashBoardContent\",\r\n    },\r\n    DATAPOOL:{\r\n        VIEW_WALLET: \"searchWallet\",\r\n        CREATE_WALLET: \"accuracyWallet\",\r\n        SHARE_WALLET: \"shareWallet\",\r\n        CREATE_SHARE: \"createShareInfo\",\r\n        VIEW_SHARE: \"searchShareInfo\",\r\n        VIEW_HISTORY_WALLET: \"walletHistory\",\r\n        // ALERT_WALLET_THRESHOLD: \"alertWalletThreshold\",\r\n        // ALERT_WALLET_EXPIRY: \"alertWalletExpiry\",\r\n    },\r\n    SHARE_GROUP: {\r\n        CREATE: \"createShareGroup\",\r\n        EDIT: \"editShareGroup\",\r\n        VIEW_LIST:\"searchShareGroup\",\r\n        VIEW_DETAIL:\"detailShareGroup\",\r\n        DELETE:\"deleteShareGroup\"\r\n    },\r\n    DIAGNOSE: {\r\n        VIEW_LIST: \"searchDiagnose\",\r\n    },\r\n    AUTO_SHARE_GROUP: {\r\n        CREATE: \"createShareGroup\",\r\n        EDIT: \"editShareGroup\",\r\n        VIEW_LIST:\"searchShareGroup\",\r\n        VIEW_DETAIL:\"detailShareGroup\",\r\n        DELETE:\"deleteShareGroup\"\r\n    },\r\n    THIRD_PARTY_API: {\r\n        GRANT_PERMISSION_3RD_API: \"grantPermission3rdApi\",\r\n        // GET_SIM_INFO_TO_ASSIGN: \"getSimInfoToAssign\",\r\n        // GRANT_PERMISSION_3RD_API: \"getSimInfo\",\r\n        // GRANT_PERMISSION_3RD_API: \"getListSimByAccount\",\r\n        // GRANT_PERMISSION_3RD_API: \"getSimInfoToImport\",\r\n        // GRANT_PERMISSION_3RD_API: \"count-by-provinces\",\r\n\r\n    },\r\n    GUIDE: {\r\n        INTEGRATION: \"guideIntegration\",\r\n    }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,OAAO,EAAE;IACLC,SAAS,EAAE,YAAY;IACvBC,WAAW,EAAE,SAAS;IACtBC,MAAM,EAAE,YAAY;IACpBC,MAAM,EAAE,YAAY;IACpBC,MAAM,EAAE,YAAY;IACpBC,aAAa,EAAE,YAAY;IAC3BC,mBAAmB,EAAE,kBAAkB;IACvCC,cAAc,EAAC;GAClB;EACDC,OAAO,EAAE;IACLC,IAAI,EAAE,gBAAgB;IACtBN,MAAM,EAAE;GACX;EACDO,IAAI,EAAE;IACFV,SAAS,EAAE,YAAY;IACvBC,WAAW,EAAE,SAAS;IACtBC,MAAM,EAAE,YAAY;IACpBC,MAAM,EAAE,YAAY;IACpBC,MAAM,EAAE,EAAE;IACVC,aAAa,EAAE;GAClB;EACDM,UAAU,EAAE;IACRX,SAAS,EAAE,kBAAkB;IAC7BC,WAAW,EAAE;GAChB;EACDW,GAAG,EAAE;IACDZ,SAAS,EAAE,WAAW;IACtBC,WAAW,EAAE,QAAQ;IACrBE,MAAM,EAAE,WAAW;IACnBE,aAAa,EAAE,WAAW;IAC1BH,MAAM,EAAE,WAAW;IACnBE,MAAM,EAAG;GACZ;EACDS,SAAS,EAAE;IACPX,MAAM,EAAE,gBAAgB;IACxBC,MAAM,EAAE,gBAAgB;IACxBH,SAAS,EAAC,gBAAgB;IAC1BC,WAAW,EAAC,aAAa;IACzBG,MAAM,EAAC;GACV;EACDU,QAAQ,EAAE;IACNd,SAAS,EAAE,gBAAgB;IAC3BC,WAAW,EAAE;GAChB;EACDc,WAAW,EAAE;IACTC,MAAM,EAAE,wBAAwB;IAChCC,OAAO,EAAE,wBAAwB;IACjCC,OAAO,EAAE,mBAAmB;IAC5BhB,MAAM,EAAE,kBAAkB;IAC1BE,MAAM,EAAE,kBAAkB;IAC1Be,KAAK,EAAE,iBAAiB;IACxBnB,SAAS,EAAE,kBAAkB;IAC7BC,WAAW,EAAE,eAAe;IAC5BE,MAAM,EAAE;GACX;EACDiB,eAAe,EAAE;IACbC,aAAa,EAAE,YAAY;IAC3BC,WAAW,EAAE,eAAe;IAC5BC,gBAAgB,EAAE,gBAAgB;IAClCC,gBAAgB,EAAE,gBAAgB;IAClCC,gBAAgB,EAAE;GACrB;EACDC,cAAc,EAAE;IACZ1B,SAAS,EAAE,cAAc;IACzBC,WAAW,EAAE,WAAW;IACxBC,MAAM,EAAE,cAAc;IACtBC,MAAM,EAAC,cAAc;IACrBC,MAAM,EAAE;GACX;EACDuB,WAAW,EAAE;IACTxB,MAAM,EAAE;GACX;EACDyB,oBAAoB,EAAE;IAClB5B,SAAS,EAAE,kBAAkB;IAC7BC,WAAW,EAAE,eAAe;IAC5BC,MAAM,EAAE,kBAAkB;IAC1BC,MAAM,EAAC,kBAAkB;IACzBC,MAAM,EAAE;GACX;EACDyB,QAAQ,EAAE;IACN7B,SAAS,EAAC,gBAAgB;IAC1BC,WAAW,EAAC,aAAa;IACzBE,MAAM,EAAC,gBAAgB;IACvBD,MAAM,EAAC,gBAAgB;IACvBE,MAAM,EAAC;GACV;EACD0B,KAAK,EAAE;IACH9B,SAAS,EAAE,aAAa;IACxBC,WAAW,EAAE,UAAU;IACvBC,MAAM,EAAE,aAAa;IACrBC,MAAM,EAAE,aAAa;IACrBC,MAAM,EAAE,aAAa;IACrB2B,uBAAuB,EAAE,4BAA4B;IACrDC,oBAAoB,EAAE,yBAAyB;IAC/CC,uBAAuB,EAAE,4BAA4B;IACrDC,oBAAoB,EAAE,yBAAyB;IAC/C7B,aAAa,EAAE;GAClB;EACD8B,OAAO,EAAE;IACLnC,SAAS,EAAE,cAAc;IACzBC,WAAW,EAAE;GAChB;EACDmC,MAAM,EAAE;IACJpC,SAAS,EAAE,cAAc;IACzBC,WAAW,EAAE,WAAW;IACxBC,MAAM,EAAE,cAAc;IACtBC,MAAM,EAAC,cAAc;IACrBC,MAAM,EAAE;GACX;EACDiC,qBAAqB,EAAE;IACnBrC,SAAS,EAAE,oBAAoB;IAC/BC,WAAW,EAAE,iBAAiB;IAC9BC,MAAM,EAAE,oBAAoB;IAC5BC,MAAM,EAAE,oBAAoB;IAC5BC,MAAM,EAAE;GACX;EACDkC,aAAa,EAAE;IACXtC,SAAS,EAAE;GACd;EACDuC,oBAAoB,EAAE;IAClBvC,SAAS,EAAE,uBAAuB;IAClCC,WAAW,EAAE,oBAAoB;IACjCC,MAAM,EAAE,uBAAuB;IAC/BC,MAAM,EAAE,uBAAuB;IAC/BC,MAAM,EAAE;GACX;EACDoC,GAAG,EAAG;IACFxC,SAAS,EAAG;GACf;EACDyC,MAAM,EAAE;IACJzC,SAAS,EAAE,WAAW;IACtBE,MAAM,EAAE,cAAc;IACtBC,MAAM,EAAE;GACX;EACDuC,MAAM,EAAC;IACHC,QAAQ,EAAE;GACb;EACDC,YAAY,EAAE;IACV5C,SAAS,EAAE;GACd;EACD6C,QAAQ,EAAC;IACLC,WAAW,EAAE,cAAc;IAC3BC,aAAa,EAAE,gBAAgB;IAC/BC,YAAY,EAAE,aAAa;IAC3BC,YAAY,EAAE,iBAAiB;IAC/BC,UAAU,EAAE,iBAAiB;IAC7BC,mBAAmB,EAAE;IACrB;IACA;GACH;;EACDC,WAAW,EAAE;IACTlD,MAAM,EAAE,kBAAkB;IAC1BmD,IAAI,EAAE,gBAAgB;IACtBrD,SAAS,EAAC,kBAAkB;IAC5BC,WAAW,EAAC,kBAAkB;IAC9BG,MAAM,EAAC;GACV;EACDkD,QAAQ,EAAE;IACNtD,SAAS,EAAE;GACd;EACDuD,gBAAgB,EAAE;IACdrD,MAAM,EAAE,kBAAkB;IAC1BmD,IAAI,EAAE,gBAAgB;IACtBrD,SAAS,EAAC,kBAAkB;IAC5BC,WAAW,EAAC,kBAAkB;IAC9BG,MAAM,EAAC;GACV;EACDoD,eAAe,EAAE;IACbC,wBAAwB,EAAE;IAC1B;IACA;IACA;IACA;IACA;GAEH;;EACDC,KAAK,EAAE;IACHC,WAAW,EAAE;;CAEpB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}