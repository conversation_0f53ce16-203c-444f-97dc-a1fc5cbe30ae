{"ast": null, "code": "import { TicketService } from \"src/app/service/ticket/TicketService\";\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\nimport { ComponentBase } from \"src/app/component.base\";\nimport { AccountService } from \"../../../service/account/AccountService\";\nimport { Validators } from \"@angular/forms\";\nimport { LogHandleTicketService } from \"../../../service/ticket/LogHandleTicketService\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/breadcrumb\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/inputtext\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"../../common-module/table/table.component\";\nimport * as i8 from \"../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/dialog\";\nimport * as i11 from \"primeng/inputtextarea\";\nimport * as i12 from \"primeng/panel\";\nimport * as i13 from \"primeng/table\";\nimport * as i14 from \"src/app/service/ticket/TicketService\";\nimport * as i15 from \"../../../service/account/AccountService\";\nimport * as i16 from \"../../../service/ticket/LogHandleTicketService\";\nfunction ListTestSimTicketComponent_p_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 52);\n    i0.ɵɵlistener(\"click\", function ListTestSimTicketComponent_p_button_6_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.showModalCreate());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"global.button.create\"));\n  }\n}\nfunction ListTestSimTicketComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"span\", 11)(2, \"p-dropdown\", 54);\n    i0.ɵɵlistener(\"ngModelChange\", function ListTestSimTicketComponent_div_10_Template_p_dropdown_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.searchInfo.provinceCode = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 55);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"showClear\", true)(\"filter\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx_r1.searchInfo.provinceCode)(\"required\", false)(\"options\", ctx_r1.listProvince);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.tranService.translate(\"account.label.province\"));\n  }\n}\nfunction ListTestSimTicketComponent_table_vnpt_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"table-vnpt\", 56);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"tableId\", \"tableTicketConfigList\")(\"fieldId\", \"provinceCode\")(\"columns\", ctx_r2.columns)(\"dataSet\", ctx_r2.dataSet)(\"options\", ctx_r2.optionTable)(\"pageNumber\", ctx_r2.pageNumber)(\"loadData\", ctx_r2.search.bind(ctx_r2))(\"pageSize\", ctx_r2.pageSize)(\"sort\", ctx_r2.sort)(\"params\", ctx_r2.searchInfo)(\"labelTable\", ctx_r2.tranService.translate(\"ticket.menu.requestList\"));\n  }\n}\nfunction ListTestSimTicketComponent_table_vnpt_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"table-vnpt\", 56);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"tableId\", \"tableTicketConfigList\")(\"fieldId\", \"provinceCode\")(\"columns\", ctx_r3.columns)(\"dataSet\", ctx_r3.dataSet)(\"options\", ctx_r3.optionTable)(\"pageNumber\", ctx_r3.pageNumber)(\"loadData\", ctx_r3.search.bind(ctx_r3))(\"pageSize\", ctx_r3.pageSize)(\"sort\", ctx_r3.sort)(\"params\", ctx_r3.searchInfo)(\"labelTable\", ctx_r3.tranService.translate(\"ticket.menu.requestList\"));\n  }\n}\nfunction ListTestSimTicketComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"label\", 57);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 28);\n    i0.ɵɵtext(4, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 34)(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.tranService.translate(\"account.label.province\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.getProvinceName(ctx_r4.ticket.provinceCode));\n  }\n}\nfunction ListTestSimTicketComponent_input_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 58);\n    i0.ɵɵlistener(\"ngModelChange\", function ListTestSimTicketComponent_input_41_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.ticket.contactName = $event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.ticket.contactName)(\"required\", true)(\"maxLength\", ctx_r5.maxlengthContactName)(\"placeholder\", ctx_r5.tranService.translate(\"account.text.inputFullname\"));\n  }\n}\nfunction ListTestSimTicketComponent_span_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r6.ticket.contactName);\n  }\n}\nfunction ListTestSimTicketComponent_small_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.tranService.translate(\"global.message.required\"));\n  }\n}\nconst _c0 = function () {\n  return {\n    len: 255\n  };\n};\nfunction ListTestSimTicketComponent_small_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r8.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListTestSimTicketComponent_small_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r9.tranService.translate(\"global.message.formatContainVN\"));\n  }\n}\nfunction ListTestSimTicketComponent_input_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 59);\n    i0.ɵɵlistener(\"ngModelChange\", function ListTestSimTicketComponent_input_55_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.ticket.contactEmail = $event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngModel\", ctx_r10.ticket.contactEmail)(\"required\", true)(\"maxLength\", 50)(\"placeholder\", ctx_r10.tranService.translate(\"account.text.inputEmail\"));\n  }\n}\nfunction ListTestSimTicketComponent_span_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r11.ticket.contactEmail);\n  }\n}\nfunction ListTestSimTicketComponent_small_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r12.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListTestSimTicketComponent_small_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r13.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListTestSimTicketComponent_small_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r14.tranService.translate(\"global.message.invalidEmail\"));\n  }\n}\nfunction ListTestSimTicketComponent_input_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 60);\n    i0.ɵɵlistener(\"ngModelChange\", function ListTestSimTicketComponent_input_69_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.ticket.contactPhone = $event);\n    })(\"keydown\", function ListTestSimTicketComponent_input_69_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r43 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r43.preventCharacter($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngModel\", ctx_r15.ticket.contactPhone)(\"required\", true)(\"maxLength\", 11)(\"placeholder\", ctx_r15.tranService.translate(\"account.text.inputPhone\"));\n  }\n}\nfunction ListTestSimTicketComponent_span_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r16.ticket.contactPhone);\n  }\n}\nfunction ListTestSimTicketComponent_small_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r17.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListTestSimTicketComponent_small_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r18.tranService.translate(\"ticket.message.invalidPhone\"));\n  }\n}\nfunction ListTestSimTicketComponent_textarea_80_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"textarea\", 61);\n    i0.ɵɵlistener(\"ngModelChange\", function ListTestSimTicketComponent_textarea_80_Template_textarea_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r44 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r44.ticket.content = $event);\n    })(\"keydown\", function ListTestSimTicketComponent_textarea_80_Template_textarea_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.onKeyDownContent($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"autoResize\", false)(\"ngModel\", ctx_r19.ticket.content)(\"maxlength\", 255)(\"placeholder\", ctx_r19.tranService.translate(\"ticket.label.content\"));\n  }\n}\nfunction ListTestSimTicketComponent_span_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r20.ticket.content);\n  }\n}\nfunction ListTestSimTicketComponent_small_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r21.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListTestSimTicketComponent_textarea_90_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"textarea\", 63);\n    i0.ɵɵlistener(\"ngModelChange\", function ListTestSimTicketComponent_textarea_90_Template_textarea_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.ticket.note = $event);\n    })(\"keydown\", function ListTestSimTicketComponent_textarea_90_Template_textarea_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r49 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r49.onKeyDownNote($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"autoResize\", false)(\"ngModel\", ctx_r22.ticket.note)(\"maxlength\", 255)(\"placeholder\", ctx_r22.tranService.translate(\"ticket.label.note\"));\n  }\n}\nfunction ListTestSimTicketComponent_span_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r23.ticket.note);\n  }\n}\nfunction ListTestSimTicketComponent_small_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r24.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nconst _c1 = function () {\n  return {\n    type: 3\n  };\n};\nconst _c2 = function () {\n  return {\n    bottom: \"40px\",\n    left: \"12px\",\n    \"min-width\": \"calc(100% - 20px)\"\n  };\n};\nfunction ListTestSimTicketComponent_div_96_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"label\", 65);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 66)(4, \"vnpt-select\", 67);\n    i0.ɵɵlistener(\"valueChange\", function ListTestSimTicketComponent_div_96_Template_vnpt_select_valueChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r51);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r50.ticket.assigneeId = $event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r25.tranService.translate(\"ticket.label.transferProcessing\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r25.ticket.assigneeId)(\"placeholder\", ctx_r25.tranService.translate(\"ticket.label.transferProcessing\"))(\"disabled\", ctx_r25.ticket.assigneeId != null && ctx_r25.typeRequest == \"detail\")(\"isMultiChoice\", false)(\"paramDefault\", i0.ɵɵpureFunction0(7, _c1))(\"stylePositionBoxSelect\", i0.ɵɵpureFunction0(8, _c2));\n  }\n}\nfunction ListTestSimTicketComponent_div_97_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r52 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r52.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListTestSimTicketComponent_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"label\", 68);\n    i0.ɵɵelementStart(2, \"div\", 34);\n    i0.ɵɵtemplate(3, ListTestSimTicketComponent_div_97_small_3_Template, 2, 1, \"small\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r26.formTicketSim.controls.assigneeId.dirty && (ctx_r26.formTicketSim.controls.assigneeId.errors == null ? null : ctx_r26.formTicketSim.controls.assigneeId.errors.required));\n  }\n}\nfunction ListTestSimTicketComponent_div_98_p_dropdown_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r60 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dropdown\", 72);\n    i0.ɵɵlistener(\"ngModelChange\", function ListTestSimTicketComponent_div_98_p_dropdown_4_Template_p_dropdown_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r60);\n      const ctx_r59 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r59.ticket.status = $event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", true)(\"ngModel\", ctx_r53.ticket.status)(\"options\", ctx_r53.mapTicketStatus[ctx_r53.ticket.statusOld])(\"placeholder\", ctx_r53.tranService.translate(\"ticket.label.status\"))(\"emptyMessage\", ctx_r53.tranService.translate(\"global.text.nodata\"));\n  }\n}\nconst _c3 = function () {\n  return [\"p-2\", \"text-white\", \"bg-cyan-300\", \"border-round\", \"inline-block\"];\n};\nfunction ListTestSimTicketComponent_div_98_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r54 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c3));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r54.getValueStatus(ctx_r54.ticket.statusOld));\n  }\n}\nconst _c4 = function () {\n  return [\"p-2\", \"text-white\", \"bg-bluegray-500\", \"border-round\", \"inline-block\"];\n};\nfunction ListTestSimTicketComponent_div_98_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r55 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c4));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r55.getValueStatus(ctx_r55.ticket.statusOld));\n  }\n}\nconst _c5 = function () {\n  return [\"p-2\", \"text-white\", \"bg-orange-400\", \"border-round\", \"inline-block\"];\n};\nfunction ListTestSimTicketComponent_div_98_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r56 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c5));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r56.getValueStatus(ctx_r56.ticket.statusOld));\n  }\n}\nconst _c6 = function () {\n  return [\"p-2\", \"text-white\", \"bg-red-500\", \"border-round\", \"inline-block\"];\n};\nfunction ListTestSimTicketComponent_div_98_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r57 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c6));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r57.getValueStatus(ctx_r57.ticket.statusOld));\n  }\n}\nconst _c7 = function () {\n  return [\"p-2\", \"text-white\", \"bg-green-500\", \"border-round\", \"inline-block\"];\n};\nfunction ListTestSimTicketComponent_div_98_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r58 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c7));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r58.getValueStatus(ctx_r58.ticket.statusOld));\n  }\n}\nfunction ListTestSimTicketComponent_div_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"label\", 69);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 34);\n    i0.ɵɵtemplate(4, ListTestSimTicketComponent_div_98_p_dropdown_4_Template, 1, 6, \"p-dropdown\", 70);\n    i0.ɵɵtemplate(5, ListTestSimTicketComponent_div_98_span_5_Template, 2, 4, \"span\", 71);\n    i0.ɵɵtemplate(6, ListTestSimTicketComponent_div_98_span_6_Template, 2, 4, \"span\", 71);\n    i0.ɵɵtemplate(7, ListTestSimTicketComponent_div_98_span_7_Template, 2, 4, \"span\", 71);\n    i0.ɵɵtemplate(8, ListTestSimTicketComponent_div_98_span_8_Template, 2, 4, \"span\", 71);\n    i0.ɵɵtemplate(9, ListTestSimTicketComponent_div_98_span_9_Template, 2, 4, \"span\", 71);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r27.userInfo.type == ctx_r27.userType.PROVINCE && ctx_r27.typeRequest != \"detail\" ? ctx_r27.ticket.assigneeId == null || ctx_r27.ticket.assigneeId != null && !ctx_r27.listActivatedAccount.includes(ctx_r27.ticket.assigneeId) ? \"\" : \"hidden\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r27.tranService.translate(\"ticket.label.status\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r27.typeRequest == \"update\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r27.typeRequest == \"detail\" && ctx_r27.ticket.statusOld == 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r27.typeRequest == \"detail\" && ctx_r27.ticket.statusOld == 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r27.typeRequest == \"detail\" && ctx_r27.ticket.statusOld == 2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r27.typeRequest == \"detail\" && ctx_r27.ticket.statusOld == 3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r27.typeRequest == \"detail\" && ctx_r27.ticket.statusOld == 4);\n  }\n}\nfunction ListTestSimTicketComponent_div_99_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r61 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r61.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListTestSimTicketComponent_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"label\", 68);\n    i0.ɵɵelementStart(2, \"div\", 34);\n    i0.ɵɵtemplate(3, ListTestSimTicketComponent_div_99_small_3_Template, 2, 1, \"small\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.formTicketSim.controls.status.dirty && (ctx_r28.formTicketSim.controls.status.errors == null ? null : ctx_r28.formTicketSim.controls.status.errors.required));\n  }\n}\nfunction ListTestSimTicketComponent_div_100_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 28);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ListTestSimTicketComponent_div_100_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"label\", 57);\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, ListTestSimTicketComponent_div_100_span_3_Template, 2, 0, \"span\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 34)(5, \"input\", 73);\n    i0.ɵɵlistener(\"ngModelChange\", function ListTestSimTicketComponent_div_100_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r63 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r63.ticket.cause = $event);\n    })(\"keydown\", function ListTestSimTicketComponent_div_100_Template_input_keydown_5_listener($event) {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r65 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r65.onKeyDownCause($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r29.userInfo.type == ctx_r29.userType.PROVINCE ? ctx_r29.ticket.assigneeId == null || ctx_r29.ticket.assigneeId != null && !ctx_r29.listActivatedAccount.includes(ctx_r29.ticket.assigneeId) ? \"\" : \"hidden\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r29.tranService.translate(\"ticket.label.processingNotes\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.ticket.status);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r29.ticket.cause)(\"required\", ctx_r29.ticket.status != null)(\"maxLength\", 255)(\"placeholder\", ctx_r29.tranService.translate(\"ticket.label.processingNotes\"));\n  }\n}\nfunction ListTestSimTicketComponent_div_101_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r66 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r66.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListTestSimTicketComponent_div_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"label\", 33);\n    i0.ɵɵelementStart(2, \"div\", 34);\n    i0.ɵɵtemplate(3, ListTestSimTicketComponent_div_101_small_3_Template, 2, 1, \"small\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.formTicketSim.controls.cause.dirty && (ctx_r30.formTicketSim.controls.cause.errors == null ? null : ctx_r30.formTicketSim.controls.cause.errors.required) || ctx_r30.ticket.status != null && ctx_r30.ticket.cause != null && ctx_r30.ticket.cause.trim() == \"\");\n  }\n}\nfunction ListTestSimTicketComponent_div_102_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 78);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 79);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r67 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r67.tranService.translate(\"global.text.stt\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r67.tranService.translate(\"account.text.account\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r67.tranService.translate(\"global.button.changeStatus\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r67.tranService.translate(\"account.label.time\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r67.tranService.translate(\"ticket.label.content\"));\n  }\n}\nfunction ListTestSimTicketComponent_div_102_ng_template_5_input_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r77 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 82);\n    i0.ɵɵlistener(\"ngModelChange\", function ListTestSimTicketComponent_div_102_ng_template_5_input_11_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r77);\n      const note_r69 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(note_r69.content = $event);\n    })(\"keydown\", function ListTestSimTicketComponent_div_102_ng_template_5_input_11_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r77);\n      const note_r69 = i0.ɵɵnextContext().$implicit;\n      const ctx_r78 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r78.onKeyDownNoteContent($event, note_r69));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const note_r69 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", note_r69.content)(\"required\", true)(\"maxLength\", 255);\n  }\n}\nfunction ListTestSimTicketComponent_div_102_ng_template_5_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const note_r69 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(note_r69.content);\n  }\n}\nfunction ListTestSimTicketComponent_div_102_ng_template_5_small_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r73 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r73.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListTestSimTicketComponent_div_102_ng_template_5_small_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r74 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r74.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListTestSimTicketComponent_div_102_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 80)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtemplate(11, ListTestSimTicketComponent_div_102_ng_template_5_input_11_Template, 1, 3, \"input\", 81);\n    i0.ɵɵtemplate(12, ListTestSimTicketComponent_div_102_ng_template_5_span_12_Template, 2, 1, \"span\", 31);\n    i0.ɵɵtemplate(13, ListTestSimTicketComponent_div_102_ng_template_5_small_13_Template, 2, 1, \"small\", 35);\n    i0.ɵɵtemplate(14, ListTestSimTicketComponent_div_102_ng_template_5_small_14_Template, 2, 2, \"small\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const note_r69 = ctx.$implicit;\n    const i_r70 = ctx.rowIndex;\n    const ctx_r68 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r68.mapForm[note_r69.id]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r70 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(note_r69.userName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r68.getValueStatus(note_r69.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 9, note_r69.createdDate, \"HH:mm:ss dd/MM/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r68.typeRequest == \"update\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r68.typeRequest == \"detail\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r68.mapForm[note_r69.id].controls.content.dirty && (ctx_r68.mapForm[note_r69.id].controls.content.errors == null ? null : ctx_r68.mapForm[note_r69.id].controls.content.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r68.mapForm[note_r69.id].controls.content.errors == null ? null : ctx_r68.mapForm[note_r69.id].controls.content.errors.maxLength);\n  }\n}\nconst _c8 = function () {\n  return {\n    \"min-width\": \"50rem\"\n  };\n};\nfunction ListTestSimTicketComponent_div_102_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"label\", 74);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-table\", 75);\n    i0.ɵɵtemplate(4, ListTestSimTicketComponent_div_102_ng_template_4_Template, 11, 5, \"ng-template\", 76);\n    i0.ɵɵtemplate(5, ListTestSimTicketComponent_div_102_ng_template_5_Template, 15, 12, \"ng-template\", 77);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r31.tranService.translate(\"ticket.label.listNote\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r31.listNotes)(\"tableStyle\", i0.ɵɵpureFunction0(3, _c8));\n  }\n}\nfunction ListTestSimTicketComponent_div_103_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r83 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"p-button\", 84);\n    i0.ɵɵlistener(\"click\", function ListTestSimTicketComponent_div_103_Template_p_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r83);\n      const ctx_r82 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r82.isShowCreateRequest = false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(2, \"p-button\", 85);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", ctx_r32.tranService.translate(\"global.button.cancel\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r32.formTicketSim.invalid || ctx_r32.ticket.status != null && ctx_r32.ticket.cause.trim() == \"\" || ctx_r32.listNotes.length > 0 && !ctx_r32.isFormValid())(\"label\", ctx_r32.tranService.translate(\"global.button.save\"));\n  }\n}\nconst _c9 = function (a0) {\n  return [a0];\n};\nconst _c10 = function () {\n  return {\n    width: \"800px\",\n    overflowY: \"scroll\",\n    maxHeight: \"80%\"\n  };\n};\nconst _c11 = function () {\n  return {\n    \"1199px\": \"75vw\",\n    \"575px\": \"90vw\"\n  };\n};\nexport class ListTestSimTicketComponent extends ComponentBase {\n  constructor(ticketService, accountService, logHandleTicketService, formBuilder, injector) {\n    super(injector);\n    this.ticketService = ticketService;\n    this.accountService = accountService;\n    this.logHandleTicketService = logHandleTicketService;\n    this.formBuilder = formBuilder;\n    this.injector = injector;\n    this.maxlengthContactName = 50;\n    this.oldTicket = {};\n    this.isShowTableNote = false;\n    this.mapForm = {};\n    this.titlePopup = '';\n    this.changeTable = false;\n    this.CONSTANTS = CONSTANTS;\n  }\n  ngOnInit() {\n    let me = this;\n    me.changeTable = false;\n    this.userInfo = this.sessionService.userInfo;\n    this.isShowCreateRequest = false;\n    this.typeRequest = 'create';\n    this.userType = CONSTANTS.USER_TYPE;\n    this.listNotes = [];\n    this.ticket = {\n      id: null,\n      contactName: null,\n      contactEmail: null,\n      contactPhone: null,\n      content: null,\n      note: null,\n      cause: null,\n      type: CONSTANTS.REQUEST_TYPE.TEST_SIM,\n      changeSim: null,\n      status: null,\n      statusOld: null,\n      assigneeId: null,\n      provinceCode: null\n    };\n    this.listTicketType = [{\n      label: this.tranService.translate('ticket.type.testSim'),\n      value: 1\n    }];\n    this.mapTicketStatus = {\n      0: [{\n        label: me.tranService.translate('ticket.status.received'),\n        value: 1\n      }, {\n        label: me.tranService.translate('ticket.status.reject'),\n        value: 3\n      }],\n      1: [{\n        label: me.tranService.translate('ticket.status.inProgress'),\n        value: 2\n      }, {\n        label: me.tranService.translate('ticket.status.reject'),\n        value: 3\n      }],\n      2: [{\n        label: me.tranService.translate('ticket.status.done'),\n        value: 4\n      }, {\n        label: me.tranService.translate('ticket.status.reject'),\n        value: 3\n      }]\n    };\n    this.listTicketStatus = [{\n      label: me.tranService.translate('ticket.status.new'),\n      value: 0\n    }, {\n      label: me.tranService.translate('ticket.status.received'),\n      value: 1\n    }, {\n      label: me.tranService.translate('ticket.status.inProgress'),\n      value: 2\n    }, {\n      label: me.tranService.translate('ticket.status.reject'),\n      value: 3\n    }, {\n      label: me.tranService.translate('ticket.status.done'),\n      value: 4\n    }];\n    this.searchInfo = {\n      provinceCode: null,\n      email: null,\n      contactPhone: null,\n      contactEmail: null,\n      type: CONSTANTS.REQUEST_TYPE.TEST_SIM,\n      status: null\n    };\n    this.columns = [{\n      name: this.tranService.translate(\"ticket.label.province\"),\n      key: \"provinceName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: this.userInfo.type == CONSTANTS.USER_TYPE.ADMIN,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"ticket.label.customerName\"),\n      key: \"contactName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      isShowTooltip: true,\n      style: {\n        display: 'inline-block',\n        maxWidth: '350px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.email\"),\n      key: \"contactEmail\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      isShowTooltip: true,\n      style: {\n        display: 'inline-block',\n        maxWidth: '350px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.phone\"),\n      key: \"contactPhone\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"ticket.label.content\"),\n      key: \"content\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      isShowTooltip: true,\n      style: {\n        display: 'inline-block',\n        maxWidth: '350px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.createdDate\"),\n      key: \"createdDate\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        return me.utilService.convertDateToString(new Date(value));\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.updatedDate\"),\n      key: \"updatedDate\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        return value ? me.utilService.convertDateToString(new Date(value)) : \"\";\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.updateBy\"),\n      key: \"updatedByName\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"ticket.label.status\"),\n      key: \"status\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcGetClassname: value => {\n        if (value == CONSTANTS.REQUEST_STATUS.NEW) {\n          return ['p-2', 'text-white', \"bg-cyan-300\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\n          return ['p-2', 'text-white', \"bg-bluegray-500\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\n          return ['p-2', 'text-white', \"bg-orange-400\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\n          return ['p-2', 'text-white', \"bg-red-500\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\n          return ['p-2', 'text-white', \"bg-green-500\", \"border-round\", \"inline-block\"];\n        }\n        return '';\n      },\n      funcConvertText: function (value) {\n        if (value == CONSTANTS.REQUEST_STATUS.NEW) {\n          return me.tranService.translate(\"ticket.status.new\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\n          return me.tranService.translate(\"ticket.status.received\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\n          return me.tranService.translate(\"ticket.status.inProgress\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\n          return me.tranService.translate(\"ticket.status.reject\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\n          return me.tranService.translate(\"ticket.status.done\");\n        }\n        return \"\";\n      }\n    }];\n    this.optionTable = {\n      hasClearSelected: false,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false,\n      action: [{\n        icon: \"pi pi-info-circle\",\n        tooltip: this.tranService.translate(\"global.button.view\"),\n        func: function (id, item) {\n          me.handleDetailRequest(id, item);\n        }\n      }, {\n        icon: \"pi pi-window-maximize\",\n        tooltip: this.tranService.translate(\"global.button.edit\"),\n        func: function (id, item) {\n          me.handleEditRequest(id, item);\n        },\n        funcAppear: function (id, item) {\n          if (me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER) return false;\n          if (!item.updatedBy && !item.assigneeId && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;\n          if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy !== me.userInfo.id || me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.assigneeId != null) {\n            return false;\n          }\n          if (me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;else return false;\n        }\n      }]\n    };\n    this.pageNumber = 0;\n    this.pageSize = 10;\n    this.sort = \"createdDate,desc\";\n    this.dataSet = {\n      content: [],\n      total: 0\n    };\n    this.formSearchTicket = this.formBuilder.group(this.searchInfo);\n    this.formTicketSim = this.formBuilder.group(this.ticket);\n    this.getListProvince();\n    this.listActivatedAccount = [];\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n  }\n  getValueStatus(value) {\n    let me = this;\n    {\n      if (value == CONSTANTS.REQUEST_STATUS.NEW) {\n        return me.tranService.translate(\"ticket.status.new\");\n      } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\n        return me.tranService.translate(\"ticket.status.received\");\n      } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\n        return me.tranService.translate(\"ticket.status.inProgress\");\n      } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\n        return me.tranService.translate(\"ticket.status.reject\");\n      } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\n        return me.tranService.translate(\"ticket.status.done\");\n      }\n      return \"\";\n    }\n  }\n  getValueDate(value) {\n    let me = this;\n    // console.log(value)\n    return me.utilService.convertDateToString(new Date(value));\n  }\n  search(page, limit, sort, params) {\n    let me = this;\n    me.changeTable = false;\n    this.pageNumber = page;\n    this.pageSize = limit;\n    this.sort = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    Object.keys(this.searchInfo).forEach(key => {\n      if (this.searchInfo[key] != null) {\n        dataParams[key] = this.searchInfo[key];\n      }\n    });\n    this.dataSet = {\n      content: [],\n      total: 0\n    };\n    me.messageCommonService.onload();\n    this.ticketService.searchTicket(dataParams, response => {\n      me.dataSet = {\n        content: response.content,\n        total: response.totalElements\n      };\n      if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE || me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) {\n        let listAssigneeId = Array.from(new Set(me.dataSet.content.filter(item => item.assigneeId !== null).map(item => item.assigneeId)));\n        me.dataSet.content.forEach(item => {\n          if (item.updateBy !== null) {\n            listAssigneeId.push(item.updateBy);\n          }\n        });\n        const statusCheckListId = Array.from(new Set(listAssigneeId));\n        me.accountService.getListActivatedAccount(statusCheckListId, response => {\n          me.listActivatedAccount = response;\n          this.optionTable = {\n            hasClearSelected: false,\n            hasShowChoose: false,\n            hasShowIndex: true,\n            hasShowToggleColumn: false,\n            action: [{\n              icon: \"pi pi-info-circle\",\n              tooltip: this.tranService.translate(\"global.button.view\"),\n              func: function (id, item) {\n                me.handleDetailRequest(id, item);\n              }\n            }, {\n              icon: \"pi pi-window-maximize\",\n              tooltip: this.tranService.translate(\"global.button.edit\"),\n              func: function (id, item) {\n                me.handleEditRequest(id, item);\n              },\n              funcAppear: function (id, item) {\n                if (me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER) return false;\n                if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE]) && (me.listActivatedAccount === undefined || me.listActivatedAccount == null)) return true;\n                if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE]) && item.assigneeId != null && me.listActivatedAccount.includes(item.assigneeId)) return false;\n                if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE]) && item.assigneeId == null && item.updatedBy != null && me.listActivatedAccount.includes(item.updatedBy) && item.updatedBy != me.userInfo.id) return false;\n                if (!item.updatedBy && !item.assigneeId && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;\n                if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE]) && (item.assigneeId != null && !me.listActivatedAccount.includes(item.assigneeId) || item.updatedBy != null && !me.listActivatedAccount.includes(item.updatedBy))) return true;\n                if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy !== me.userInfo.id || me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.assigneeId != null) {\n                  return false;\n                }\n                if (me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;else return false;\n              }\n            }]\n          };\n          me.changeTable = true;\n        });\n      }\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  resetTicket() {\n    this.ticket = {\n      id: null,\n      contactName: null,\n      contactEmail: null,\n      contactPhone: null,\n      content: null,\n      note: null,\n      cause: null,\n      type: CONSTANTS.REQUEST_TYPE.TEST_SIM,\n      changeSim: null,\n      status: null,\n      statusOld: null,\n      assigneeId: null,\n      provinceCode: null\n    };\n  }\n  onSubmitSearch() {\n    this.pageNumber = 0;\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n  }\n  getListProvince() {\n    this.accountService.getListProvince(response => {\n      this.listProvince = response.map(el => {\n        return {\n          ...el,\n          display: `${el.code} - ${el.name}`\n        };\n      });\n    });\n  }\n  getProvinceName(provinceCode) {\n    const province = this.listProvince.find(el => el.code === provinceCode);\n    return province ? province.code + ' - ' + province.name : \"\";\n  }\n  // tạo sửa yêu cầu\n  createOrUpdateRequest() {\n    if (this.messageCommonService.isloading == true || this.isShowCreateRequest == false) return;\n    let me = this;\n    this.messageCommonService.onload();\n    if (this.typeRequest == 'create') {\n      let bodySend = {\n        contactName: this.ticket.contactName,\n        contactEmail: this.ticket.contactEmail,\n        contactPhone: this.ticket.contactPhone,\n        content: this.ticket.content,\n        note: this.ticket.note,\n        type: this.ticket.type,\n        changeSim: this.ticket.type == 0 ? this.ticket.changeSim : null\n      };\n      if (bodySend.contactPhone != null) {\n        if (bodySend.contactPhone.startsWith('0')) {\n          bodySend.contactPhone = \"84\" + bodySend.contactPhone.substring(1, bodySend.contactPhone.length);\n        } else if (bodySend.contactPhone.length == 9 || bodySend.contactPhone.length == 10) {\n          bodySend.contactPhone = \"84\" + bodySend.contactPhone;\n        }\n      }\n      this.ticketService.createTicket(bodySend, resp => {\n        me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n        me.isShowCreateRequest = false;\n        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n        // nếu KH đc gán cho GDV thì gửi mail cho GDV và danh sách admin đc cấu hình không thì chỉ gửi mail cho danh sách admin đc cấu hình\n        // get mail admin tinh dc cau hinh\n        me.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, resp1 => {\n          let array = [];\n          for (let info of resp1.emailInfos) {\n            array.push({\n              userId: info.userId,\n              ticketId: resp.id\n            });\n          }\n          if (resp?.assigneeId) {\n            array.push({\n              userId: resp.assigneeId,\n              ticketId: resp.id\n            });\n          }\n          me.ticketService.sendMailNotify(array);\n        });\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n    } else if (this.typeRequest == 'update') {\n      let bodySend = {\n        contactName: this.ticket.contactName,\n        contactEmail: this.ticket.contactEmail,\n        contactPhone: this.ticket.contactPhone,\n        content: this.ticket.content,\n        note: this.ticket.note,\n        type: this.ticket.type,\n        changeSim: this.ticket.type == 0 ? this.ticket.changeSim : null,\n        status: this.ticket.status,\n        cause: this.ticket.cause,\n        assigneeId: this.ticket.assigneeId,\n        listLog: this.listNotes\n      };\n      if (bodySend.contactPhone != null) {\n        if (bodySend.contactPhone.startsWith('0')) {\n          bodySend.contactPhone = \"84\" + bodySend.contactPhone.substring(1, bodySend.contactPhone.length);\n        } else if (bodySend.contactPhone.length == 9 || bodySend.contactPhone.length == 10) {\n          bodySend.contactPhone = \"84\" + bodySend.contactPhone;\n        }\n      }\n      // update ticket\n      this.ticketService.updateTicket(this.ticket.id, bodySend, resp => {\n        me.isShowCreateRequest = false;\n        me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n        if (resp.assigneeId != null && resp.assigneeId != undefined) {\n          me.ticketService.sendMailNotify([{\n            userId: resp.assigneeId,\n            ticketId: resp.id\n          }]);\n        }\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n    }\n  }\n  showModalCreate() {\n    this.isShowCreateRequest = true;\n    this.typeRequest = 'create';\n    this.titlePopup = this.tranService.translate('ticket.label.createRequest');\n    this.resetTicket();\n    // auto fill thong tin khi tao\n    if (this.userInfo.type === CONSTANTS.USER_TYPE.CUSTOMER) {\n      this.ticket.contactName = this.userInfo.fullName.substring(0, this.maxlengthContactName);\n      this.ticket.contactPhone = this.userInfo.phone;\n      this.ticket.contactEmail = this.userInfo.email;\n    }\n    this.formTicketSim = this.formBuilder.group(this.ticket);\n  }\n  handleEditRequest(id, item) {\n    let me = this;\n    this.formTicketSim.reset();\n    this.typeRequest = 'update';\n    this.titlePopup = this.tranService.translate('ticket.label.updateRequest');\n    this.isShowCreateRequest = true;\n    this.ticketService.getDetailTicket(item.id, resp => {\n      me.ticket = {\n        id: resp.id,\n        contactName: resp.contactName,\n        contactEmail: resp.contactEmail,\n        contactPhone: resp.contactPhone,\n        content: resp.content,\n        note: resp.note,\n        cause: resp.cause,\n        type: resp.type,\n        changeSim: resp.changeSim,\n        status: null,\n        statusOld: resp.status,\n        assigneeId: resp.assigneeId,\n        provinceCode: resp.provinceCode\n      };\n      me.oldTicket = {\n        ...me.ticket\n      };\n      me.formTicketSim = me.formBuilder.group(me.ticket);\n      // lấy list note\n      this.logHandleTicketService.search({\n        ticketId: item.id\n      }, res => {\n        console.log(res.content);\n        this.listNotes = res.content;\n        // for(let note of this.listNotes) {\n        //   this.mapForm[note.id] = this.formBuilder.group(note);\n        // }\n        this.listNotes.forEach(note => {\n          this.mapForm[note.id] = this.formBuilder.group({\n            content: ['', [Validators.required, Validators.maxLength(255), this.noWhitespaceValidator()]]\n          });\n        });\n        me.isShowTableNote = true;\n      });\n    });\n    this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, resp => {\n      me.listEmail = resp.emailInfos;\n    });\n  }\n  handleDetailRequest(id, item) {\n    let me = this;\n    this.formTicketSim.reset();\n    this.titlePopup = this.tranService.translate('ticket.label.viewDetailTestSim');\n    this.typeRequest = 'detail';\n    this.isShowCreateRequest = true;\n    this.ticketService.getDetailTicket(item.id, resp => {\n      me.ticket = {\n        id: resp.id,\n        contactName: resp.contactName,\n        contactEmail: resp.contactEmail,\n        contactPhone: resp.contactPhone,\n        content: resp.content,\n        note: resp.note,\n        cause: resp.cause,\n        type: resp.type,\n        changeSim: resp.changeSim,\n        status: null,\n        statusOld: resp.status,\n        assigneeId: resp.assigneeId,\n        provinceCode: resp.provinceCode\n      };\n      me.oldTicket = {\n        ...me.ticket\n      };\n      me.formTicketSim = me.formBuilder.group(me.ticket);\n      // lấy list note\n      this.logHandleTicketService.search({\n        ticketId: item.id\n      }, res => {\n        console.log(res.content);\n        this.listNotes = res.content;\n        for (let note of this.listNotes) {\n          this.mapForm[note.id] = this.formBuilder.group(note);\n        }\n        me.isShowTableNote = true;\n      });\n    });\n    this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, resp => {\n      me.listEmail = resp.emailInfos;\n    });\n  }\n  preventCharacter(event) {\n    if (event.ctrlKey) {\n      return;\n    }\n    if (event.keyCode == 8 || event.keyCode == 13 || event.keyCode == 37 || event.keyCode == 39) {\n      return;\n    }\n    if (event.keyCode < 48 || event.keyCode > 57) {\n      event.preventDefault();\n    }\n    // Chặn ký tự 'e', 'E' và dấu '+'\n    if (event.keyCode == 69 || event.keyCode == 101 || event.keyCode == 107 || event.keyCode == 187) {\n      event.preventDefault();\n    }\n  }\n  isFormValid() {\n    return Object.values(this.mapForm).every(formGroup => formGroup.valid);\n  }\n  noWhitespaceValidator() {\n    return control => {\n      const isWhitespace = (control.value || '').trim().length === 0;\n      const isValid = !isWhitespace;\n      return isValid ? null : {\n        whitespace: true\n      };\n    };\n  }\n  onKeyDownNote(event) {\n    if (event.key === ' ' && (this.ticket.note == null || this.ticket.note != null && this.ticket.note.trim() === '')) {\n      event.preventDefault();\n    }\n    if (this.ticket.note != null && this.ticket.note.trim() != '') {\n      this.ticket.note = this.ticket.note.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  onKeyDownContent(event) {\n    if (event.key === ' ' && (this.ticket.content == null || this.ticket.content != null && this.ticket.content.trim() === '')) {\n      event.preventDefault();\n    }\n    if (this.ticket.content != null && this.ticket.content.trim() != '') {\n      this.ticket.content = this.ticket.content.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  onKeyDownNoteContent(event, note) {\n    if (event.key === ' ' && (!note.content || note.content.trim() === '')) {\n      event.preventDefault();\n    }\n    if (note.content && note.content.trim() !== '') {\n      note.content = note.content.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  onKeyDownCause(event) {\n    if (event.key === ' ' && (this.ticket.cause == null || this.ticket.cause != null && this.ticket.cause.trim() === '')) {\n      event.preventDefault();\n    }\n    if (this.ticket.cause != null && this.ticket.cause.trim() != '') {\n      this.ticket.cause = this.ticket.cause.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  static {\n    this.ɵfac = function ListTestSimTicketComponent_Factory(t) {\n      return new (t || ListTestSimTicketComponent)(i0.ɵɵdirectiveInject(TicketService), i0.ɵɵdirectiveInject(AccountService), i0.ɵɵdirectiveInject(LogHandleTicketService), i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ListTestSimTicketComponent,\n      selectors: [[\"ticket-config-list\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 104,\n      vars: 69,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [\"styleClass\", \"p-button-info\", \"icon\", \"\", 3, \"label\", \"click\", 4, \"ngIf\"], [1, \"pt-3\", \"pb-2\", \"vnpt-field-set\", 3, \"formGroup\", \"ngSubmit\"], [3, \"toggleable\", \"header\"], [1, \"grid\", \"search-grid-4\"], [\"class\", \"col-2 col-4\", 4, \"ngIf\"], [1, \"col-2\"], [1, \"p-float-label\"], [\"styleClass\", \"w-full\", \"filterBy\", \"display\", \"id\", \"provinceCode\", \"formControlName\", \"status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"filterBy\", \"label\", 3, \"showClear\", \"filter\", \"autoDisplayFirst\", \"ngModel\", \"required\", \"options\", \"ngModelChange\"], [\"htmlFor\", \"status\"], [\"pInputText\", \"\", \"id\", \"contactEmail\", \"formControlName\", \"contactEmail\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"contactEmail\"], [\"pInputText\", \"\", \"id\", \"contactPhone\", \"formControlName\", \"contactPhone\", \"type\", \"number\", \"min\", \"0\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\", \"keydown\"], [\"htmlFor\", \"contactPhone\"], [1, \"col-2\", \"pb-0\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"submit\"], [3, \"tableId\", \"fieldId\", \"columns\", \"dataSet\", \"options\", \"pageNumber\", \"loadData\", \"pageSize\", \"sort\", \"params\", \"labelTable\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"dialog-vnpt\"], [3, \"breakpoints\", \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"mt-3\", 3, \"formGroup\", \"ngSubmit\"], [1, \"flex\", \"flex-row\", \"flex-wrap\", \"justify-content-between\", \"w-full\"], [\"class\", \"w-full field grid chart-grid\", 4, \"ngIf\"], [1, \"w-full\", \"field\", \"grid\", \"chart-grid\"], [\"htmlFor\", \"contactName\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [1, \"text-red-500\"], [1, \"col\", 2, \"width\", \"calc(100% - 180px)\", \"overflow-wrap\", \"break-word\"], [\"class\", \"w-full\", \"pInputText\", \"\", \"id\", \"contactName\", \"formControlName\", \"contactName\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"w-full\", \"field\", \"grid\", \"text-error-field\"], [\"htmlFor\", \"fullName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"col\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [\"htmlFor\", \"email\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"class\", \"w-full\", \"pInputText\", \"\", \"id\", \"contactEmail\", \"formControlName\", \"contactEmail\", \"pattern\", \"^[a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+)+(\\\\.[a-z]{2,})$\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\", 4, \"ngIf\"], [\"htmlFor\", \"email\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"htmlFor\", \"phone\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"class\", \"w-full\", \"pInputText\", \"\", \"id\", \"contactPhone\", \"formControlName\", \"contactPhone\", \"pattern\", \"^((\\\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\", \"keydown\", 4, \"ngIf\"], [\"htmlFor\", \"content\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"class\", \"w-full\", \"style\", \"resize: none;\", \"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"content\", \"formControlName\", \"content\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"placeholder\", \"ngModelChange\", \"keydown\", 4, \"ngIf\"], [\"style\", \"word-break: break-all\", 4, \"ngIf\"], [\"htmlFor\", \"content\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"htmlFor\", \"note\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"class\", \"w-full\", \"style\", \"resize: none;\", \"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"note\", \"formControlName\", \"note\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"placeholder\", \"ngModelChange\", \"keydown\", 4, \"ngIf\"], [\"htmlFor\", \"note\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"class\", \"w-full field grid\", 4, \"ngIf\"], [\"class\", \"w-full field grid text-error-field\", 4, \"ngIf\"], [\"class\", \"w-full field grid chart-grid\", 3, \"class\", 4, \"ngIf\"], [\"class\", \"flex flex-row justify-content-center align-items-center mt-3\", 4, \"ngIf\"], [\"styleClass\", \"p-button-info\", \"icon\", \"\", 3, \"label\", \"click\"], [1, \"col-2\", \"col-4\"], [\"styleClass\", \"w-full\", \"filterBy\", \"display\", \"id\", \"provinceCode\", \"formControlName\", \"provinceCode\", \"optionLabel\", \"display\", \"optionValue\", \"code\", 3, \"showClear\", \"filter\", \"autoDisplayFirst\", \"ngModel\", \"required\", \"options\", \"ngModelChange\"], [\"htmlFor\", \"provinceCode\"], [3, \"tableId\", \"fieldId\", \"columns\", \"dataSet\", \"options\", \"pageNumber\", \"loadData\", \"pageSize\", \"sort\", \"params\", \"labelTable\"], [\"htmlFor\", \"contactName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"contactName\", \"formControlName\", \"contactName\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [\"pInputText\", \"\", \"id\", \"contactEmail\", \"formControlName\", \"contactEmail\", \"pattern\", \"^[a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+)+(\\\\.[a-z]{2,})$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [\"pInputText\", \"\", \"id\", \"contactPhone\", \"formControlName\", \"contactPhone\", \"pattern\", \"^((\\\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\", \"keydown\"], [\"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"content\", \"formControlName\", \"content\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"placeholder\", \"ngModelChange\", \"keydown\"], [2, \"word-break\", \"break-all\"], [\"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"note\", \"formControlName\", \"note\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"placeholder\", \"ngModelChange\", \"keydown\"], [1, \"w-full\", \"field\", \"grid\"], [\"for\", \"type\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"col\", 2, \"max-width\", \"calc(100% - 180px)\", \"position\", \"relative\"], [\"objectKey\", \"account\", \"paramKey\", \"email\", \"keyReturn\", \"id\", \"displayPattern\", \"${email}\", 1, \"w-full\", 3, \"value\", \"placeholder\", \"disabled\", \"isMultiChoice\", \"paramDefault\", \"stylePositionBoxSelect\", \"valueChange\"], [\"htmlFor\", \"userType\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"for\", \"status\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"styleClass\", \"w-full\", \"id\", \"type\", \"formControlName\", \"status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"placeholder\", \"emptyMessage\", \"ngModelChange\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"styleClass\", \"w-full\", \"id\", \"type\", \"formControlName\", \"status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"placeholder\", \"emptyMessage\", \"ngModelChange\"], [\"pInputText\", \"\", \"id\", \"cause\", \"formControlName\", \"cause\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\", \"keydown\"], [\"htmlFor\", \"content\", 1, \"col-fixed\", \"font-medium\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [2, \"width\", \"100%\", 3, \"value\", \"tableStyle\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [2, \"min-width\", \"146px\"], [2, \"min-width\", \"155px\"], [3, \"formGroup\"], [\"class\", \"w-full\", \"pInputText\", \"\", \"id\", \"content\", \"formControlName\", \"content\", 3, \"ngModel\", \"required\", \"maxLength\", \"ngModelChange\", \"keydown\", 4, \"ngIf\"], [\"pInputText\", \"\", \"id\", \"content\", \"formControlName\", \"content\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"ngModelChange\", \"keydown\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"align-items-center\", \"mt-3\"], [\"styleClass\", \"mr-2 p-button-secondary\", 3, \"label\", \"click\"], [\"type\", \"submit\", \"styleClass\", \"p-button-info\", 3, \"disabled\", \"label\"]],\n      template: function ListTestSimTicketComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵtemplate(6, ListTestSimTicketComponent_p_button_6_Template, 1, 1, \"p-button\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"form\", 6);\n          i0.ɵɵlistener(\"ngSubmit\", function ListTestSimTicketComponent_Template_form_ngSubmit_7_listener() {\n            return ctx.onSubmitSearch();\n          });\n          i0.ɵɵelementStart(8, \"p-panel\", 7)(9, \"div\", 8);\n          i0.ɵɵtemplate(10, ListTestSimTicketComponent_div_10_Template, 5, 7, \"div\", 9);\n          i0.ɵɵelementStart(11, \"div\", 10)(12, \"span\", 11)(13, \"p-dropdown\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function ListTestSimTicketComponent_Template_p_dropdown_ngModelChange_13_listener($event) {\n            return ctx.searchInfo.status = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"label\", 13);\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 10)(17, \"span\", 11)(18, \"input\", 14);\n          i0.ɵɵlistener(\"ngModelChange\", function ListTestSimTicketComponent_Template_input_ngModelChange_18_listener($event) {\n            return ctx.searchInfo.contactEmail = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"label\", 15);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 10)(22, \"span\", 11)(23, \"input\", 16);\n          i0.ɵɵlistener(\"ngModelChange\", function ListTestSimTicketComponent_Template_input_ngModelChange_23_listener($event) {\n            return ctx.searchInfo.contactPhone = $event;\n          })(\"keydown\", function ListTestSimTicketComponent_Template_input_keydown_23_listener($event) {\n            return ctx.preventCharacter($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"label\", 17);\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"div\", 18);\n          i0.ɵɵelement(27, \"p-button\", 19);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(28, ListTestSimTicketComponent_table_vnpt_28_Template, 1, 11, \"table-vnpt\", 20);\n          i0.ɵɵtemplate(29, ListTestSimTicketComponent_table_vnpt_29_Template, 1, 11, \"table-vnpt\", 20);\n          i0.ɵɵelementStart(30, \"div\", 21)(31, \"p-dialog\", 22);\n          i0.ɵɵlistener(\"visibleChange\", function ListTestSimTicketComponent_Template_p_dialog_visibleChange_31_listener($event) {\n            return ctx.isShowCreateRequest = $event;\n          });\n          i0.ɵɵelementStart(32, \"form\", 23);\n          i0.ɵɵlistener(\"ngSubmit\", function ListTestSimTicketComponent_Template_form_ngSubmit_32_listener() {\n            return ctx.createOrUpdateRequest();\n          });\n          i0.ɵɵelementStart(33, \"div\", 24);\n          i0.ɵɵtemplate(34, ListTestSimTicketComponent_div_34_Template, 8, 2, \"div\", 25);\n          i0.ɵɵelementStart(35, \"div\", 26)(36, \"label\", 27);\n          i0.ɵɵtext(37);\n          i0.ɵɵelementStart(38, \"span\", 28);\n          i0.ɵɵtext(39, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 29);\n          i0.ɵɵtemplate(41, ListTestSimTicketComponent_input_41_Template, 1, 4, \"input\", 30);\n          i0.ɵɵtemplate(42, ListTestSimTicketComponent_span_42_Template, 2, 1, \"span\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"div\", 32);\n          i0.ɵɵelement(44, \"label\", 33);\n          i0.ɵɵelementStart(45, \"div\", 34);\n          i0.ɵɵtemplate(46, ListTestSimTicketComponent_small_46_Template, 2, 1, \"small\", 35);\n          i0.ɵɵtemplate(47, ListTestSimTicketComponent_small_47_Template, 2, 2, \"small\", 35);\n          i0.ɵɵtemplate(48, ListTestSimTicketComponent_small_48_Template, 2, 1, \"small\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"div\", 26)(50, \"label\", 36);\n          i0.ɵɵtext(51);\n          i0.ɵɵelementStart(52, \"span\", 28);\n          i0.ɵɵtext(53, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"div\", 29);\n          i0.ɵɵtemplate(55, ListTestSimTicketComponent_input_55_Template, 1, 4, \"input\", 37);\n          i0.ɵɵtemplate(56, ListTestSimTicketComponent_span_56_Template, 2, 1, \"span\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 32);\n          i0.ɵɵelement(58, \"label\", 38);\n          i0.ɵɵelementStart(59, \"div\", 34);\n          i0.ɵɵtemplate(60, ListTestSimTicketComponent_small_60_Template, 2, 1, \"small\", 35);\n          i0.ɵɵtemplate(61, ListTestSimTicketComponent_small_61_Template, 2, 2, \"small\", 35);\n          i0.ɵɵtemplate(62, ListTestSimTicketComponent_small_62_Template, 2, 1, \"small\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(63, \"div\", 26)(64, \"label\", 39);\n          i0.ɵɵtext(65);\n          i0.ɵɵelementStart(66, \"span\", 28);\n          i0.ɵɵtext(67, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"div\", 34);\n          i0.ɵɵtemplate(69, ListTestSimTicketComponent_input_69_Template, 1, 4, \"input\", 40);\n          i0.ɵɵtemplate(70, ListTestSimTicketComponent_span_70_Template, 2, 1, \"span\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(71, \"div\", 32);\n          i0.ɵɵelement(72, \"label\", 39);\n          i0.ɵɵelementStart(73, \"div\", 34);\n          i0.ɵɵtemplate(74, ListTestSimTicketComponent_small_74_Template, 2, 1, \"small\", 35);\n          i0.ɵɵtemplate(75, ListTestSimTicketComponent_small_75_Template, 2, 1, \"small\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(76, \"div\", 26)(77, \"label\", 41);\n          i0.ɵɵtext(78);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"div\", 34);\n          i0.ɵɵtemplate(80, ListTestSimTicketComponent_textarea_80_Template, 1, 4, \"textarea\", 42);\n          i0.ɵɵtemplate(81, ListTestSimTicketComponent_span_81_Template, 2, 1, \"span\", 43);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"div\", 32);\n          i0.ɵɵelement(83, \"label\", 44);\n          i0.ɵɵelementStart(84, \"div\", 34);\n          i0.ɵɵtemplate(85, ListTestSimTicketComponent_small_85_Template, 2, 2, \"small\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(86, \"div\", 26)(87, \"label\", 45);\n          i0.ɵɵtext(88);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"div\", 34);\n          i0.ɵɵtemplate(90, ListTestSimTicketComponent_textarea_90_Template, 1, 4, \"textarea\", 46);\n          i0.ɵɵtemplate(91, ListTestSimTicketComponent_span_91_Template, 2, 1, \"span\", 43);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(92, \"div\", 32);\n          i0.ɵɵelement(93, \"label\", 47);\n          i0.ɵɵelementStart(94, \"div\", 34);\n          i0.ɵɵtemplate(95, ListTestSimTicketComponent_small_95_Template, 2, 2, \"small\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(96, ListTestSimTicketComponent_div_96_Template, 5, 9, \"div\", 48);\n          i0.ɵɵtemplate(97, ListTestSimTicketComponent_div_97_Template, 4, 1, \"div\", 49);\n          i0.ɵɵtemplate(98, ListTestSimTicketComponent_div_98_Template, 10, 9, \"div\", 50);\n          i0.ɵɵtemplate(99, ListTestSimTicketComponent_div_99_Template, 4, 1, \"div\", 49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(100, ListTestSimTicketComponent_div_100_Template, 6, 8, \"div\", 50);\n          i0.ɵɵtemplate(101, ListTestSimTicketComponent_div_101_Template, 4, 1, \"div\", 49);\n          i0.ɵɵtemplate(102, ListTestSimTicketComponent_div_102_Template, 6, 4, \"div\", 25);\n          i0.ɵɵtemplate(103, ListTestSimTicketComponent_div_103_Template, 3, 3, \"div\", 51);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.menu.testSim\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo.type == ctx.userType.CUSTOMER && ctx.checkAuthen(i0.ɵɵpureFunction1(65, _c9, ctx.CONSTANTS.PERMISSIONS.TICKET.CREATE)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formSearchTicket);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"toggleable\", true)(\"header\", ctx.tranService.translate(\"global.text.filter\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo.type == ctx.userType.ADMIN);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"showClear\", true)(\"filter\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.searchInfo.status)(\"required\", false)(\"options\", ctx.listTicketStatus)(\"filter\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.status\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.contactEmail);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.email\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.contactPhone);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.phone\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.changeTable);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.changeTable);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(67, _c10));\n          i0.ɵɵproperty(\"breakpoints\", i0.ɵɵpureFunction0(68, _c11))(\"header\", ctx.titlePopup)(\"visible\", ctx.isShowCreateRequest)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formTicketSim);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo.type == ctx.userType.ADMIN && ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.customerName\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"create\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactName.dirty && (ctx.formTicketSim.controls.contactName.errors == null ? null : ctx.formTicketSim.controls.contactName.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactName.errors == null ? null : ctx.formTicketSim.controls.contactName.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactName.errors == null ? null : ctx.formTicketSim.controls.contactName.errors.pattern);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.email\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"create\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactEmail.dirty && (ctx.formTicketSim.controls.contactEmail.errors == null ? null : ctx.formTicketSim.controls.contactEmail.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactEmail.errors == null ? null : ctx.formTicketSim.controls.contactEmail.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactEmail.errors == null ? null : ctx.formTicketSim.controls.contactEmail.errors.pattern);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.phone\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"create\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactPhone.dirty && (ctx.formTicketSim.controls.contactPhone.errors == null ? null : ctx.formTicketSim.controls.contactPhone.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactPhone.errors == null ? null : ctx.formTicketSim.controls.contactPhone.errors.pattern);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.content\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"create\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.content.errors == null ? null : ctx.formTicketSim.controls.content.errors.maxLength);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.note\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"create\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.note.errors == null ? null : ctx.formTicketSim.controls.note.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" && ctx.userInfo.type == ctx.userType.PROVINCE && ctx.ticket.status == null && ctx.ticket.statusOld == 0 || ctx.userInfo.type == ctx.userType.PROVINCE && ctx.typeRequest == \"detail\" && ctx.ticket.assigneeId != null);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" && ctx.userInfo.type == ctx.userType.PROVINCE && ctx.ticket.status == null && ctx.ticket.statusOld == 0 || ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"detail\" && ctx.ticket.assigneeId == null);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"detail\" && ctx.ticket.assigneeId == null);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (ctx.typeRequest == \"update\" || ctx.typeRequest == \"detail\") && ctx.listNotes && ctx.listNotes.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest != \"detail\");\n        }\n      },\n      dependencies: [i2.NgIf, i3.Breadcrumb, i4.PrimeTemplate, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MaxLengthValidator, i1.PatternValidator, i1.MinValidator, i1.FormGroupDirective, i1.FormControlName, i5.InputText, i6.Button, i7.TableVnptComponent, i8.VnptCombobox, i9.Dropdown, i10.Dialog, i11.InputTextarea, i12.Panel, i13.Table, i2.DatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["TicketService", "CONSTANTS", "ComponentBase", "AccountService", "Validators", "LogHandleTicketService", "i0", "ɵɵelementStart", "ɵɵlistener", "ListTestSimTicketComponent_p_button_6_Template_p_button_click_0_listener", "ɵɵrestoreView", "_r34", "ctx_r33", "ɵɵnextContext", "ɵɵresetView", "showModalCreate", "ɵɵelementEnd", "ɵɵproperty", "ctx_r0", "tranService", "translate", "ListTestSimTicketComponent_div_10_Template_p_dropdown_ngModelChange_2_listener", "$event", "_r36", "ctx_r35", "searchInfo", "provinceCode", "ɵɵtext", "ɵɵadvance", "ctx_r1", "listProvince", "ɵɵtextInterpolate", "ɵɵelement", "ctx_r2", "columns", "dataSet", "optionTable", "pageNumber", "search", "bind", "pageSize", "sort", "ctx_r3", "ctx_r4", "getProvinceName", "ticket", "ListTestSimTicketComponent_input_41_Template_input_ngModelChange_0_listener", "_r38", "ctx_r37", "contactName", "ctx_r5", "maxlengthContactName", "ctx_r6", "ctx_r7", "ctx_r8", "ɵɵpureFunction0", "_c0", "ctx_r9", "ListTestSimTicketComponent_input_55_Template_input_ngModelChange_0_listener", "_r40", "ctx_r39", "contactEmail", "ctx_r10", "ctx_r11", "ctx_r12", "ctx_r13", "ctx_r14", "ListTestSimTicketComponent_input_69_Template_input_ngModelChange_0_listener", "_r42", "ctx_r41", "contactPhone", "ListTestSimTicketComponent_input_69_Template_input_keydown_0_listener", "ctx_r43", "preventCharacter", "ctx_r15", "ctx_r16", "ctx_r17", "ctx_r18", "ListTestSimTicketComponent_textarea_80_Template_textarea_ngModelChange_0_listener", "_r45", "ctx_r44", "content", "ListTestSimTicketComponent_textarea_80_Template_textarea_keydown_0_listener", "ctx_r46", "onKeyDownContent", "ctx_r19", "ctx_r20", "ctx_r21", "ListTestSimTicketComponent_textarea_90_Template_textarea_ngModelChange_0_listener", "_r48", "ctx_r47", "note", "ListTestSimTicketComponent_textarea_90_Template_textarea_keydown_0_listener", "ctx_r49", "onKeyDownNote", "ctx_r22", "ctx_r23", "ctx_r24", "ListTestSimTicketComponent_div_96_Template_vnpt_select_valueChange_4_listener", "_r51", "ctx_r50", "assigneeId", "ctx_r25", "typeRequest", "_c1", "_c2", "ctx_r52", "ɵɵtemplate", "ListTestSimTicketComponent_div_97_small_3_Template", "ctx_r26", "formTicketSim", "controls", "dirty", "errors", "required", "ListTestSimTicketComponent_div_98_p_dropdown_4_Template_p_dropdown_ngModelChange_0_listener", "_r60", "ctx_r59", "status", "ctx_r53", "mapTicketStatus", "statusOld", "ɵɵclassMap", "_c3", "ctx_r54", "getValueStatus", "_c4", "ctx_r55", "_c5", "ctx_r56", "_c6", "ctx_r57", "_c7", "ctx_r58", "ListTestSimTicketComponent_div_98_p_dropdown_4_Template", "ListTestSimTicketComponent_div_98_span_5_Template", "ListTestSimTicketComponent_div_98_span_6_Template", "ListTestSimTicketComponent_div_98_span_7_Template", "ListTestSimTicketComponent_div_98_span_8_Template", "ListTestSimTicketComponent_div_98_span_9_Template", "ctx_r27", "userInfo", "type", "userType", "PROVINCE", "listActivatedAccount", "includes", "ctx_r61", "ListTestSimTicketComponent_div_99_small_3_Template", "ctx_r28", "ListTestSimTicketComponent_div_100_span_3_Template", "ListTestSimTicketComponent_div_100_Template_input_ngModelChange_5_listener", "_r64", "ctx_r63", "cause", "ListTestSimTicketComponent_div_100_Template_input_keydown_5_listener", "ctx_r65", "onKeyDownCause", "ctx_r29", "ctx_r66", "ListTestSimTicketComponent_div_101_small_3_Template", "ctx_r30", "trim", "ctx_r67", "ListTestSimTicketComponent_div_102_ng_template_5_input_11_Template_input_ngModelChange_0_listener", "_r77", "note_r69", "$implicit", "ListTestSimTicketComponent_div_102_ng_template_5_input_11_Template_input_keydown_0_listener", "ctx_r78", "onKeyDownNoteContent", "ctx_r73", "ctx_r74", "ListTestSimTicketComponent_div_102_ng_template_5_input_11_Template", "ListTestSimTicketComponent_div_102_ng_template_5_span_12_Template", "ListTestSimTicketComponent_div_102_ng_template_5_small_13_Template", "ListTestSimTicketComponent_div_102_ng_template_5_small_14_Template", "ctx_r68", "mapForm", "id", "i_r70", "userName", "ɵɵpipeBind2", "createdDate", "max<PERSON><PERSON><PERSON>", "ListTestSimTicketComponent_div_102_ng_template_4_Template", "ListTestSimTicketComponent_div_102_ng_template_5_Template", "ctx_r31", "listNotes", "_c8", "ListTestSimTicketComponent_div_103_Template_p_button_click_1_listener", "_r83", "ctx_r82", "isShowCreateRequest", "ctx_r32", "invalid", "length", "isFormValid", "ListTestSimTicketComponent", "constructor", "ticketService", "accountService", "logHandleTicketService", "formBuilder", "injector", "oldTicket", "isShowTableNote", "titlePopup", "changeTable", "ngOnInit", "me", "sessionService", "USER_TYPE", "REQUEST_TYPE", "TEST_SIM", "changeSim", "listTicketType", "label", "value", "listTicketStatus", "email", "name", "key", "size", "align", "isShow", "ADMIN", "isSort", "isShowTooltip", "style", "display", "max<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "funcConvertText", "utilService", "convertDateToString", "Date", "funcGetClassname", "REQUEST_STATUS", "NEW", "RECEIVED", "IN_PROGRESS", "REJECT", "DONE", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "action", "icon", "tooltip", "func", "item", "handleDetailRequest", "handleEditRequest", "funcAppear", "CUSTOMER", "updatedBy", "<PERSON><PERSON><PERSON><PERSON>", "PERMISSIONS", "TICKET", "UPDATE", "total", "formSearchTicket", "group", "getListProvince", "getValueDate", "page", "limit", "params", "dataParams", "Object", "keys", "for<PERSON>ach", "messageCommonService", "onload", "searchTicket", "response", "totalElements", "DISTRICT", "listAssigneeId", "Array", "from", "Set", "filter", "map", "updateBy", "push", "statusCheckListId", "getListActivatedAccount", "undefined", "offload", "resetTicket", "onSubmitSearch", "el", "code", "province", "find", "createOrUpdateRequest", "isloading", "bodySend", "startsWith", "substring", "createTicket", "resp", "success", "getDetailTicketConfig", "resp1", "array", "info", "emailInfos", "userId", "ticketId", "sendMailNotify", "listLog", "updateTicket", "fullName", "phone", "reset", "getDetailTicket", "res", "console", "log", "noWhitespaceValidator", "listEmail", "event", "ctrl<PERSON>ey", "keyCode", "preventDefault", "values", "every", "formGroup", "valid", "control", "isWhitespace", "<PERSON><PERSON><PERSON><PERSON>", "whitespace", "trimStart", "replace", "ɵɵdirectiveInject", "i1", "FormBuilder", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "ListTestSimTicketComponent_Template", "rf", "ctx", "ListTestSimTicketComponent_p_button_6_Template", "ListTestSimTicketComponent_Template_form_ngSubmit_7_listener", "ListTestSimTicketComponent_div_10_Template", "ListTestSimTicketComponent_Template_p_dropdown_ngModelChange_13_listener", "ListTestSimTicketComponent_Template_input_ngModelChange_18_listener", "ListTestSimTicketComponent_Template_input_ngModelChange_23_listener", "ListTestSimTicketComponent_Template_input_keydown_23_listener", "ListTestSimTicketComponent_table_vnpt_28_Template", "ListTestSimTicketComponent_table_vnpt_29_Template", "ListTestSimTicketComponent_Template_p_dialog_visibleChange_31_listener", "ListTestSimTicketComponent_Template_form_ngSubmit_32_listener", "ListTestSimTicketComponent_div_34_Template", "ListTestSimTicketComponent_input_41_Template", "ListTestSimTicketComponent_span_42_Template", "ListTestSimTicketComponent_small_46_Template", "ListTestSimTicketComponent_small_47_Template", "ListTestSimTicketComponent_small_48_Template", "ListTestSimTicketComponent_input_55_Template", "ListTestSimTicketComponent_span_56_Template", "ListTestSimTicketComponent_small_60_Template", "ListTestSimTicketComponent_small_61_Template", "ListTestSimTicketComponent_small_62_Template", "ListTestSimTicketComponent_input_69_Template", "ListTestSimTicketComponent_span_70_Template", "ListTestSimTicketComponent_small_74_Template", "ListTestSimTicketComponent_small_75_Template", "ListTestSimTicketComponent_textarea_80_Template", "ListTestSimTicketComponent_span_81_Template", "ListTestSimTicketComponent_small_85_Template", "ListTestSimTicketComponent_textarea_90_Template", "ListTestSimTicketComponent_span_91_Template", "ListTestSimTicketComponent_small_95_Template", "ListTestSimTicketComponent_div_96_Template", "ListTestSimTicketComponent_div_97_Template", "ListTestSimTicketComponent_div_98_Template", "ListTestSimTicketComponent_div_99_Template", "ListTestSimTicketComponent_div_100_Template", "ListTestSimTicketComponent_div_101_Template", "ListTestSimTicketComponent_div_102_Template", "ListTestSimTicketComponent_div_103_Template", "items", "home", "ɵɵpureFunction1", "_c9", "CREATE", "ɵɵstyleMap", "_c10", "_c11", "pattern"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\ticket\\list\\app.list.test-sim.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\ticket\\list\\app.list.test-sim.component.html"], "sourcesContent": ["import {Component, Inject, Injector, OnInit} from \"@angular/core\";\r\nimport {MenuItem} from \"primeng/api\";\r\nimport {TicketService} from \"src/app/service/ticket/TicketService\";\r\nimport {ColumnInfo, OptionTable} from \"../../common-module/table/table.component\";\r\nimport {CONSTANTS} from \"src/app/service/comon/constants\";\r\nimport {ComponentBase} from \"src/app/component.base\";\r\nimport {AccountService} from \"../../../service/account/AccountService\";\r\nimport {AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators} from \"@angular/forms\";\r\nimport {LogHandleTicketService} from \"../../../service/ticket/LogHandleTicketService\";\r\n\r\n@Component({\r\n  selector: \"ticket-config-list\",\r\n  templateUrl: './app.list.test-sim.component.html'\r\n})\r\nexport class ListTestSimTicketComponent extends ComponentBase implements OnInit {\r\n  items: MenuItem[];\r\n  home: MenuItem\r\n  searchInfo: {\r\n    provinceCode: string | null,\r\n    email: string | null,\r\n    contactPhone: string | null,\r\n    contactEmail: string | null,\r\n    type: number | null,\r\n    status: number | null,\r\n  };\r\n  columns: Array<ColumnInfo>;\r\n  dataSet: {\r\n    content: Array<any>,\r\n    total: number\r\n  };\r\n  selectItems: Array<any>;\r\n  optionTable: OptionTable;\r\n  pageNumber: number;\r\n  pageSize: number;\r\n  sort: string;\r\n  formSearchTicket: any;\r\n  listProvince: Array<any>;\r\n  listTicketType: Array<any>;\r\n  listTicketStatus: Array<any>;\r\n  mapTicketStatus: any;\r\n  listEmail: Array<any>;\r\n  isShowCreateRequest: boolean;\r\n  formTicketSim: any;\r\n  ticket: {\r\n    id: number\r\n    contactName: string | null,\r\n    contactEmail: string | null,\r\n    contactPhone: string | null,\r\n    content: string | null,\r\n    note: string | null,\r\n    cause : string| null,\r\n    type: number | null, // 0: thay thế sim, 1: test sim\r\n    changeSim: string | null\r\n    status: number | null,\r\n    statusOld?: number | null,\r\n    assigneeId: number | null,\r\n      provinceCode: string | null,\r\n  };\r\n  maxlengthContactName: number = 50;\r\n  typeRequest: string\r\n  userInfo: any\r\n  userType: any\r\n  oldTicket : any = {}\r\n  listNotes : Array<any>\r\n  isShowTableNote : boolean = false;\r\n  mapForm : any = {}\r\n  titlePopup : string = ''\r\n  listActivatedAccount: number[];\r\n  changeTable: boolean = false;\r\n  constructor(\r\n      @Inject(TicketService) private ticketService: TicketService,\r\n      @Inject(AccountService) private accountService: AccountService,\r\n      @Inject(LogHandleTicketService) private logHandleTicketService: LogHandleTicketService,\r\n      private formBuilder: FormBuilder,\r\n      private injector: Injector) {\r\n    super(injector);\r\n  }\r\n\r\n  ngOnInit() {\r\n    let me = this;\r\n    me.changeTable = false;\r\n    this.userInfo = this.sessionService.userInfo;\r\n    this.isShowCreateRequest = false;\r\n    this.typeRequest = 'create'\r\n    this.userType = CONSTANTS.USER_TYPE;\r\n    this.listNotes = []\r\n    this.ticket = {\r\n      id: null,\r\n      contactName: null,\r\n      contactEmail: null,\r\n      contactPhone: null,\r\n      content: null,\r\n      note: null,\r\n      cause : null,\r\n      type: CONSTANTS.REQUEST_TYPE.TEST_SIM, // 0: thay thế sim, 1: test sim\r\n      changeSim: null,\r\n      status: null,\r\n      statusOld: null,\r\n      assigneeId: null,\r\n        provinceCode: null,\r\n    };\r\n    this.listTicketType = [\r\n      {\r\n        label: this.tranService.translate('ticket.type.testSim'),\r\n        value: 1\r\n      }\r\n    ]\r\n    this.mapTicketStatus = {\r\n      0: [{\r\n        label: me.tranService.translate('ticket.status.received'),\r\n        value: 1\r\n      },\r\n      {\r\n          label: me.tranService.translate('ticket.status.reject'),\r\n          value: 3\r\n      },\r\n      ],\r\n      1: [\r\n        {\r\n          label: me.tranService.translate('ticket.status.inProgress'),\r\n          value: 2\r\n        },\r\n        {\r\n          label: me.tranService.translate('ticket.status.reject'),\r\n          value: 3\r\n        }\r\n      ],\r\n      2: [\r\n        {\r\n          label: me.tranService.translate('ticket.status.done'),\r\n          value: 4\r\n        },\r\n        {\r\n          label: me.tranService.translate('ticket.status.reject'),\r\n          value: 3\r\n        }\r\n      ]\r\n    }\r\n    this.listTicketStatus = [\r\n      {\r\n        label: me.tranService.translate('ticket.status.new'),\r\n        value: 0\r\n      },\r\n      {\r\n        label: me.tranService.translate('ticket.status.received'),\r\n        value: 1\r\n      },\r\n      {\r\n        label: me.tranService.translate('ticket.status.inProgress'),\r\n        value: 2\r\n      },\r\n      {\r\n        label: me.tranService.translate('ticket.status.reject'),\r\n        value: 3\r\n      },\r\n      {\r\n        label: me.tranService.translate('ticket.status.done'),\r\n        value: 4\r\n      }\r\n    ]\r\n    this.searchInfo = {\r\n      provinceCode: null,\r\n      email: null,\r\n      contactPhone: null,\r\n      contactEmail: null,\r\n      type: CONSTANTS.REQUEST_TYPE.TEST_SIM,\r\n      status: null\r\n    }\r\n    this.columns = [\r\n      {\r\n        name: this.tranService.translate(\"ticket.label.province\"),\r\n        key: \"provinceName\",\r\n        size: \"150px\",\r\n        align: \"left\",\r\n        isShow: this.userInfo.type == CONSTANTS.USER_TYPE.ADMIN,\r\n        isSort: true\r\n      },\r\n      {\r\n        name: this.tranService.translate(\"ticket.label.customerName\"),\r\n        key: \"contactName\",\r\n        size: \"150px\",\r\n        align: \"left\",\r\n        isShow: true,\r\n        isSort: true,\r\n          isShowTooltip: true,\r\n          style: {\r\n              display: 'inline-block',\r\n              maxWidth: '350px',\r\n              overflow: 'hidden',\r\n              textOverflow: 'ellipsis'\r\n          }\r\n      }, {\r\n        name: this.tranService.translate(\"ticket.label.email\"),\r\n        key: \"contactEmail\",\r\n        size: \"150px\",\r\n        align: \"left\",\r\n        isShow: true,\r\n        isSort: true,\r\n            isShowTooltip: true,\r\n            style: {\r\n                display: 'inline-block',\r\n                maxWidth: '350px',\r\n                overflow: 'hidden',\r\n                textOverflow: 'ellipsis'\r\n            }\r\n      }, {\r\n        name: this.tranService.translate(\"ticket.label.phone\"),\r\n        key: \"contactPhone\",\r\n        size: \"fit-content\",\r\n        align: \"left\",\r\n        isShow: true,\r\n        isSort: true\r\n      }, {\r\n        name: this.tranService.translate(\"ticket.label.content\"),\r\n        key: \"content\",\r\n        size: \"fit-content\",\r\n        align: \"left\",\r\n        isShow: true,\r\n        isSort: true,\r\n        isShowTooltip: true,\r\n        style: {\r\n          display: 'inline-block',\r\n          maxWidth: '350px',\r\n          overflow: 'hidden',\r\n          textOverflow: 'ellipsis'\r\n        }\r\n      },\r\n      {\r\n        name: this.tranService.translate(\"ticket.label.createdDate\"),\r\n        key: \"createdDate\",\r\n        size: \"fit-content\",\r\n        align: \"left\",\r\n        isShow: true,\r\n        isSort: true,\r\n        funcConvertText(value) {\r\n          return me.utilService.convertDateToString(new Date(value))\r\n        },\r\n      },\r\n      {\r\n        name: this.tranService.translate(\"ticket.label.updatedDate\"),\r\n        key: \"updatedDate\",\r\n        size: \"fit-content\",\r\n        align: \"left\",\r\n        isShow: true,\r\n        isSort: true,\r\n        funcConvertText(value) {\r\n          return value ? me.utilService.convertDateToString(new Date(value)) : \"\"\r\n        },\r\n      },\r\n      {\r\n        name: this.tranService.translate(\"ticket.label.updateBy\"),\r\n        key: \"updatedByName\",\r\n        size: \"fit-content\",\r\n        align: \"left\",\r\n        isShow: true,\r\n        isSort: true\r\n      },\r\n      {\r\n        name: this.tranService.translate(\"ticket.label.status\"),\r\n        key: \"status\",\r\n        size: \"fit-content\",\r\n        align: \"left\",\r\n        isShow: true,\r\n        isSort: true,\r\n        funcGetClassname: (value) => {\r\n          if (value == CONSTANTS.REQUEST_STATUS.NEW) {\r\n            return ['p-2', 'text-white', \"bg-cyan-300\", \"border-round\", \"inline-block\"];\r\n          } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\r\n            return ['p-2', 'text-white', \"bg-bluegray-500\", \"border-round\", \"inline-block\"];\r\n          } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\r\n            return ['p-2', 'text-white', \"bg-orange-400\", \"border-round\", \"inline-block\"];\r\n          } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\r\n            return ['p-2', 'text-white', \"bg-red-500\", \"border-round\", \"inline-block\"];\r\n          } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\r\n            return ['p-2', 'text-white', \"bg-green-500\", \"border-round\", \"inline-block\"];\r\n          }\r\n          return '';\r\n        },\r\n        funcConvertText: function (value) {\r\n          if (value == CONSTANTS.REQUEST_STATUS.NEW) {\r\n            return me.tranService.translate(\"ticket.status.new\");\r\n          } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\r\n            return me.tranService.translate(\"ticket.status.received\");\r\n          } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\r\n            return me.tranService.translate(\"ticket.status.inProgress\");\r\n          } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\r\n            return me.tranService.translate(\"ticket.status.reject\");\r\n          } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\r\n            return me.tranService.translate(\"ticket.status.done\");\r\n          }\r\n          return \"\";\r\n        }\r\n      }\r\n    ];\r\n\r\n    this.optionTable = {\r\n      hasClearSelected: false,\r\n      hasShowChoose: false,\r\n      hasShowIndex: true,\r\n      hasShowToggleColumn: false,\r\n      action: [\r\n        {\r\n          icon: \"pi pi-info-circle\",\r\n          tooltip: this.tranService.translate(\"global.button.view\"),\r\n          func: function (id, item) {\r\n            me.handleDetailRequest(id, item)\r\n          }\r\n        },\r\n        {\r\n          icon: \"pi pi-window-maximize\",\r\n          tooltip: this.tranService.translate(\"global.button.edit\"),\r\n          func: function (id, item) {\r\n            me.handleEditRequest(id, item)\r\n          },\r\n          funcAppear : function (id, item) {\r\n            if(me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER) return false;\r\n            if(!item.updatedBy && !item.assigneeId && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;\r\n            if((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy !== me.userInfo.id) ||\r\n                (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.assigneeId != null) ) {\r\n              return false;\r\n            }\r\n              if (me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true\r\n              else return false;\r\n          }\r\n        }\r\n      ]\r\n    }\r\n    this.pageNumber = 0;\r\n    this.pageSize = 10;\r\n    this.sort = \"createdDate,desc\";\r\n    this.dataSet = {\r\n      content: [],\r\n      total: 0\r\n    }\r\n    this.formSearchTicket = this.formBuilder.group(this.searchInfo);\r\n    this.formTicketSim = this.formBuilder.group(this.ticket);\r\n    this.getListProvince();\r\n    this.listActivatedAccount = [];\r\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n  }\r\n\r\n  getValueStatus(value) {\r\n    let me = this;\r\n    {\r\n      if (value == CONSTANTS.REQUEST_STATUS.NEW) {\r\n        return me.tranService.translate(\"ticket.status.new\");\r\n      } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\r\n        return me.tranService.translate(\"ticket.status.received\");\r\n      } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\r\n        return me.tranService.translate(\"ticket.status.inProgress\");\r\n      } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\r\n        return me.tranService.translate(\"ticket.status.reject\");\r\n      } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\r\n        return me.tranService.translate(\"ticket.status.done\");\r\n      }\r\n      return \"\";\r\n    }\r\n  }\r\n\r\n  getValueDate(value) {\r\n    let me = this;\r\n    // console.log(value)\r\n    return me.utilService.convertDateToString(new Date(value))\r\n  }\r\n\r\n  search(page, limit, sort, params) {\r\n    let me = this;\r\n    me.changeTable = false;\r\n    this.pageNumber = page;\r\n    this.pageSize = limit;\r\n    this.sort = sort;\r\n    let dataParams = {\r\n      page,\r\n      size: limit,\r\n      sort\r\n    }\r\n    Object.keys(this.searchInfo).forEach(key => {\r\n      if (this.searchInfo[key] != null) {\r\n        dataParams[key] = this.searchInfo[key];\r\n      }\r\n    })\r\n    this.dataSet = {\r\n      content: [],\r\n      total: 0\r\n    }\r\n    me.messageCommonService.onload();\r\n    this.ticketService.searchTicket(dataParams, (response) => {\r\n      me.dataSet = {\r\n        content: response.content,\r\n        total: response.totalElements\r\n      }\r\n      if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE ||\r\n                me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) {\r\n                let listAssigneeId = Array.from(new Set(me.dataSet.content.filter(item => item.assigneeId !== null)\r\n                    .map(item => item.assigneeId as number)));\r\n\r\n                me.dataSet.content.forEach(item => {\r\n                    if (item.updateBy !== null) {\r\n                        listAssigneeId.push(item.updateBy as number);\r\n                    }\r\n                });\r\n\r\n                const statusCheckListId = Array.from(new Set(listAssigneeId));\r\n\r\n                me.accountService.getListActivatedAccount(statusCheckListId, (response) => {\r\n                    me.listActivatedAccount = response;\r\n                    this.optionTable = {\r\n      hasClearSelected: false,\r\n      hasShowChoose: false,\r\n      hasShowIndex: true,\r\n      hasShowToggleColumn: false,\r\n      action: [\r\n        {\r\n          icon: \"pi pi-info-circle\",\r\n          tooltip: this.tranService.translate(\"global.button.view\"),\r\n          func: function (id, item) {\r\n            me.handleDetailRequest(id, item)\r\n          }\r\n        },\r\n        {\r\n          icon: \"pi pi-window-maximize\",\r\n          tooltip: this.tranService.translate(\"global.button.edit\"),\r\n          func: function (id, item) {\r\n            me.handleEditRequest(id, item)\r\n          },\r\n          funcAppear : function (id, item) {\r\n            if(me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER) return false;\r\n              if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE]) && ( me.listActivatedAccount === undefined || me.listActivatedAccount == null )) return true;\r\n            if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) && ((item.assigneeId != null && me.listActivatedAccount.includes(item.assigneeId)))) return false;\r\n            if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) && ((item.assigneeId == null && item.updatedBy != null && me.listActivatedAccount.includes(item.updatedBy) && item.updatedBy != me.userInfo.id))) return false;\r\n            if(!item.updatedBy && !item.assigneeId && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;\r\n            if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) && ((item.assigneeId != null && !me.listActivatedAccount.includes(item.assigneeId)) || (item.updatedBy != null && !me.listActivatedAccount.includes(item.updatedBy)))) return true;\r\n            if((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy !== me.userInfo.id) ||\r\n                (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.assigneeId != null) ) {\r\n              return false;\r\n            }\r\n              if (me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true\r\n              else return false;\r\n          }\r\n        }\r\n      ]\r\n    }\r\n    me.changeTable = true\r\n                })\r\n            }\r\n    }, null, () => {\r\n      me.messageCommonService.offload();\r\n    })\r\n  }\r\n\r\n  resetTicket() {\r\n    this.ticket = {\r\n      id: null,\r\n      contactName: null,\r\n      contactEmail: null,\r\n      contactPhone: null,\r\n      content: null,\r\n      note: null,\r\n      cause : null,\r\n      type: CONSTANTS.REQUEST_TYPE.TEST_SIM, // 0: thay thế sim, 1: test sim\r\n      changeSim: null,\r\n      status: null,\r\n      statusOld: null,\r\n      assigneeId: null,\r\n        provinceCode: null,\r\n    };\r\n  }\r\n\r\n  onSubmitSearch() {\r\n    this.pageNumber = 0;\r\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n  }\r\n\r\n  getListProvince() {\r\n    this.accountService.getListProvince((response) => {\r\n      this.listProvince = response.map(el => {\r\n        return {\r\n          ...el,\r\n          display: `${el.code} - ${el.name}`\r\n        }\r\n      })\r\n    })\r\n  }\r\n    getProvinceName(provinceCode) {\r\n        const province = this.listProvince.find(el => el.code === provinceCode);\r\n        return province ? province.code + ' - ' + province.name : \"\";\r\n    }\r\n\r\n  // tạo sửa yêu cầu\r\n  createOrUpdateRequest() {\r\n    if(this.messageCommonService.isloading == true || this.isShowCreateRequest == false) return;\r\n    let me = this;\r\n    this.messageCommonService.onload()\r\n    if (this.typeRequest == 'create') {\r\n      let bodySend = {\r\n        contactName: this.ticket.contactName,\r\n        contactEmail: this.ticket.contactEmail,\r\n        contactPhone: this.ticket.contactPhone,\r\n        content: this.ticket.content,\r\n        note: this.ticket.note,\r\n        type: this.ticket.type,\r\n        changeSim: this.ticket.type == 0 ? this.ticket.changeSim : null\r\n      }\r\n      if (bodySend.contactPhone != null){\r\n        if(bodySend.contactPhone.startsWith('0')){\r\n          bodySend.contactPhone = \"84\"+bodySend.contactPhone.substring(1, bodySend.contactPhone.length);\r\n        }else if(bodySend.contactPhone.length == 9 || bodySend.contactPhone.length == 10){\r\n          bodySend.contactPhone = \"84\"+bodySend.contactPhone;\r\n        }\r\n      }\r\n      this.ticketService.createTicket(bodySend, (resp) => {\r\n        me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n        me.isShowCreateRequest = false\r\n        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)\r\n        // nếu KH đc gán cho GDV thì gửi mail cho GDV và danh sách admin đc cấu hình không thì chỉ gửi mail cho danh sách admin đc cấu hình\r\n        // get mail admin tinh dc cau hinh\r\n        me.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp1) => {\r\n          let array = []\r\n          for (let info of resp1.emailInfos) {\r\n            array.push({\r\n              userId: info.userId,\r\n              ticketId: resp.id\r\n            })\r\n          }\r\n          if(resp?.assigneeId) {\r\n            array.push({\r\n              userId: resp.assigneeId,\r\n              ticketId: resp.id\r\n            })\r\n          }\r\n          me.ticketService.sendMailNotify(array);\r\n        })\r\n      }, null, () => {\r\n        me.messageCommonService.offload()\r\n      })\r\n    } else if (this.typeRequest == 'update') {\r\n      let bodySend = {\r\n        contactName: this.ticket.contactName,\r\n        contactEmail: this.ticket.contactEmail,\r\n        contactPhone: this.ticket.contactPhone,\r\n        content: this.ticket.content,\r\n        note: this.ticket.note,\r\n        type: this.ticket.type,\r\n        changeSim: this.ticket.type == 0 ? this.ticket.changeSim : null,\r\n        status: this.ticket.status,\r\n        cause : this.ticket.cause,\r\n        assigneeId: this.ticket.assigneeId,\r\n        listLog : this.listNotes\r\n      }\r\n      if (bodySend.contactPhone != null){\r\n        if(bodySend.contactPhone.startsWith('0')){\r\n          bodySend.contactPhone = \"84\"+bodySend.contactPhone.substring(1, bodySend.contactPhone.length);\r\n        }else if(bodySend.contactPhone.length == 9 || bodySend.contactPhone.length == 10){\r\n          bodySend.contactPhone = \"84\"+bodySend.contactPhone;\r\n        }\r\n      }\r\n      // update ticket\r\n      this.ticketService.updateTicket(this.ticket.id, bodySend, (resp) => {\r\n        me.isShowCreateRequest = false\r\n        me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)\r\n        if(resp.assigneeId != null && resp.assigneeId != undefined) {\r\n          me.ticketService.sendMailNotify([{\r\n            userId: resp.assigneeId,\r\n            ticketId: resp.id\r\n          }])\r\n        }\r\n      }, null, () => {\r\n        me.messageCommonService.offload()\r\n      })\r\n    }\r\n  }\r\n\r\n  showModalCreate() {\r\n    this.isShowCreateRequest = true\r\n    this.typeRequest = 'create'\r\n    this.titlePopup = this.tranService.translate('ticket.label.createRequest')\r\n    this.resetTicket()\r\n    // auto fill thong tin khi tao\r\n    if (this.userInfo.type === CONSTANTS.USER_TYPE.CUSTOMER) {\r\n      this.ticket.contactName = this.userInfo.fullName.substring(0, this.maxlengthContactName);\r\n      this.ticket.contactPhone = this.userInfo.phone;\r\n      this.ticket.contactEmail = this.userInfo.email;\r\n    }\r\n    this.formTicketSim = this.formBuilder.group(this.ticket)\r\n  }\r\n\r\n  handleEditRequest(id, item) {\r\n    let me = this\r\n    this.formTicketSim.reset()\r\n    this.typeRequest = 'update'\r\n    this.titlePopup = this.tranService.translate('ticket.label.updateRequest')\r\n    this.isShowCreateRequest = true;\r\n    this.ticketService.getDetailTicket(item.id, (resp) => {\r\n      me.ticket = {\r\n        id: resp.id,\r\n        contactName: resp.contactName,\r\n        contactEmail: resp.contactEmail,\r\n        contactPhone: resp.contactPhone,\r\n        content: resp.content,\r\n        note: resp.note,\r\n        cause : resp.cause,\r\n        type: resp.type, // 0: thay thế sim, 1: test sim\r\n        changeSim: resp.changeSim,\r\n        status: null,\r\n        statusOld: resp.status,\r\n        assigneeId: resp.assigneeId,\r\n          provinceCode: resp.provinceCode\r\n      }\r\n      me.oldTicket = {...me.ticket}\r\n      me.formTicketSim = me.formBuilder.group(me.ticket)\r\n      // lấy list note\r\n      this.logHandleTicketService.search({ticketId: item.id}, (res)=> {\r\n        console.log(res.content)\r\n        this.listNotes = res.content;\r\n        // for(let note of this.listNotes) {\r\n        //   this.mapForm[note.id] = this.formBuilder.group(note);\r\n        // }\r\n          this.listNotes.forEach(note => {\r\n              this.mapForm[note.id] = this.formBuilder.group({\r\n                  content: ['', [Validators.required, Validators.maxLength(255), this.noWhitespaceValidator()]]\r\n              });\r\n          });\r\n        me.isShowTableNote = true;\r\n      })\r\n    })\r\n    this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp) => {\r\n      me.listEmail = resp.emailInfos;\r\n    })\r\n  }\r\n\r\n  handleDetailRequest(id, item) {\r\n    let me = this\r\n    this.formTicketSim.reset()\r\n    this.titlePopup = this.tranService.translate('ticket.label.viewDetailTestSim')\r\n    this.typeRequest = 'detail'\r\n    this.isShowCreateRequest = true;\r\n    this.ticketService.getDetailTicket(item.id, (resp) => {\r\n      me.ticket = {\r\n        id: resp.id,\r\n        contactName: resp.contactName,\r\n        contactEmail: resp.contactEmail,\r\n        contactPhone: resp.contactPhone,\r\n        content: resp.content,\r\n        note: resp.note,\r\n        cause: resp.cause,\r\n        type: resp.type, // 0: thay thế sim, 1: test sim\r\n        changeSim: resp.changeSim,\r\n        status: null,\r\n        statusOld: resp.status,\r\n        assigneeId: resp.assigneeId,\r\n          provinceCode: resp.provinceCode,\r\n      }\r\n      me.oldTicket = {...me.ticket}\r\n      me.formTicketSim = me.formBuilder.group(me.ticket)\r\n      // lấy list note\r\n      this.logHandleTicketService.search({ticketId: item.id}, (res)=> {\r\n        console.log(res.content)\r\n        this.listNotes = res.content;\r\n        for(let note of this.listNotes) {\r\n          this.mapForm[note.id] = this.formBuilder.group(note);\r\n        }\r\n        me.isShowTableNote = true;\r\n      })\r\n    })\r\n    this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp) => {\r\n      me.listEmail = resp.emailInfos;\r\n    })\r\n  }\r\n\r\n  preventCharacter(event){\r\n    if(event.ctrlKey){\r\n      return;\r\n    }\r\n    if(event.keyCode == 8 || event.keyCode == 13 || event.keyCode == 37 || event.keyCode == 39){\r\n      return;\r\n    }\r\n    if(event.keyCode < 48 || event.keyCode > 57){\r\n      event.preventDefault();\r\n    }\r\n      // Chặn ký tự 'e', 'E' và dấu '+'\r\n      if (event.keyCode == 69 || event.keyCode == 101 || event.keyCode == 107 || event.keyCode == 187) {\r\n          event.preventDefault();\r\n      }\r\n  }\r\n    isFormValid() {\r\n        return Object.values(this.mapForm).every((formGroup: FormGroup) => formGroup.valid);\r\n    }\r\n    noWhitespaceValidator(): ValidatorFn {\r\n        return (control: AbstractControl): ValidationErrors | null => {\r\n            const isWhitespace = (control.value || '').trim().length === 0;\r\n            const isValid = !isWhitespace;\r\n            return isValid ? null : {whitespace: true};\r\n        }\r\n    };\r\n    onKeyDownNote(event): void {\r\n        if (event.key === ' ' && (this.ticket.note == null || this.ticket.note != null && this.ticket.note.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (this.ticket.note != null && this.ticket.note.trim() != '') {\r\n            this.ticket.note = this.ticket.note.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n    onKeyDownContent(event) {\r\n        if (event.key === ' ' && (this.ticket.content == null || this.ticket.content != null && this.ticket.content.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (this.ticket.content != null && this.ticket.content.trim() != '') {\r\n            this.ticket.content = this.ticket.content.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n    onKeyDownNoteContent(event: KeyboardEvent, note: any): void {\r\n        if (event.key === ' ' && (!note.content || note.content.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (note.content && note.content.trim() !== '') {\r\n            note.content = note.content.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n    onKeyDownCause(event) {\r\n        if (event.key === ' ' && (this.ticket.cause == null || this.ticket.cause != null && this.ticket.cause.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (this.ticket.cause != null && this.ticket.cause.trim() != '') {\r\n            this.ticket.cause = this.ticket.cause.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n\r\n    protected readonly CONSTANTS = CONSTANTS;\r\n}\r\n", "<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{tranService.translate(\"ticket.menu.testSim\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"col-5 flex flex-row justify-content-end align-items-center\">\r\n        <p-button styleClass=\"p-button-info\"\r\n                  *ngIf=\"userInfo.type == userType.CUSTOMER && checkAuthen([CONSTANTS.PERMISSIONS.TICKET.CREATE])\"\r\n                  [label]=\"tranService.translate('global.button.create')\"\r\n                  (click)=\"showModalCreate()\" icon=\"\">\r\n        </p-button>\r\n    </div>\r\n</div>\r\n\r\n<form [formGroup]=\"formSearchTicket\" (ngSubmit)=\"onSubmitSearch()\" class=\"pt-3 pb-2 vnpt-field-set\">\r\n    <p-panel [toggleable]=\"true\" [header]=\"tranService.translate('global.text.filter')\">\r\n        <div class=\"grid search-grid-4\">\r\n            <!-- ma tinh -->\r\n            <div *ngIf=\"this.userInfo.type == this.userType.ADMIN\" class=\"col-2 col-4\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\"\r\n                                [showClear]=\"true\" [filter]=\"true\" filterBy=\"display\"\r\n                                id=\"provinceCode\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"searchInfo.provinceCode\"\r\n                                [required]=\"false\"\r\n                                formControlName=\"provinceCode\"\r\n                                [options]=\"listProvince\"\r\n                                optionLabel=\"display\"\r\n                                optionValue=\"code\"\r\n                    ></p-dropdown>\r\n                    <label htmlFor=\"provinceCode\">{{tranService.translate(\"account.label.province\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- trạng thái ticket -->\r\n            <div class=\"col-2\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\"\r\n                                [showClear]=\"true\" [filter]=\"true\" filterBy=\"display\"\r\n                                id=\"provinceCode\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"searchInfo.status\"\r\n                                [required]=\"false\"\r\n                                formControlName=\"status\"\r\n                                [options]=\"listTicketStatus\"\r\n                                optionLabel=\"label\"\r\n                                optionValue=\"value\"\r\n                                [filter] = true\r\n                                filterBy = \"label\"\r\n                    ></p-dropdown>\r\n                    <label htmlFor=\"status\">{{tranService.translate(\"ticket.label.status\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- email -->\r\n            <div class=\"col-2\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                           pInputText id=\"contactEmail\"\r\n                           [(ngModel)]=\"searchInfo.contactEmail\"\r\n                           formControlName=\"contactEmail\"\r\n                    />\r\n                    <label htmlFor=\"contactEmail\">{{tranService.translate(\"ticket.label.email\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- phone -->\r\n            <div class=\"col-2\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                           pInputText id=\"contactPhone\"\r\n                           [(ngModel)]=\"searchInfo.contactPhone\"\r\n                           formControlName=\"contactPhone\"\r\n                           type=\"number\"\r\n                           (keydown)=\"preventCharacter($event)\"\r\n                           min=\"0\"\r\n                    />\r\n                    <label htmlFor=\"contactPhone\">{{tranService.translate(\"ticket.label.phone\")}}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-2 pb-0\">\r\n                <p-button icon=\"pi pi-search\"\r\n                          styleClass=\"p-button-rounded p-button-secondary p-button-text button-search\"\r\n                          type=\"submit\"\r\n                ></p-button>\r\n            </div>\r\n        </div>\r\n    </p-panel>\r\n</form>\r\n\r\n<table-vnpt *ngIf=\"!changeTable\"\r\n    [tableId]=\"'tableTicketConfigList'\"\r\n    [fieldId]=\"'provinceCode'\"\r\n    [columns]=\"columns\"\r\n    [dataSet]=\"dataSet\"\r\n    [options]=\"optionTable\"\r\n    [pageNumber]=\"pageNumber\"\r\n    [loadData]=\"search.bind(this)\"\r\n    [pageSize]=\"pageSize\"\r\n    [sort]=\"sort\"\r\n    [params]=\"searchInfo\"\r\n    [labelTable]=\"tranService.translate('ticket.menu.requestList')\"\r\n></table-vnpt>\r\n<table-vnpt *ngIf=\"changeTable\"\r\n    [tableId]=\"'tableTicketConfigList'\"\r\n    [fieldId]=\"'provinceCode'\"\r\n    [columns]=\"columns\"\r\n    [dataSet]=\"dataSet\"\r\n    [options]=\"optionTable\"\r\n    [pageNumber]=\"pageNumber\"\r\n    [loadData]=\"search.bind(this)\"\r\n    [pageSize]=\"pageSize\"\r\n    [sort]=\"sort\"\r\n    [params]=\"searchInfo\"\r\n    [labelTable]=\"tranService.translate('ticket.menu.requestList')\"\r\n></table-vnpt>\r\n<!--    dialog tạo sửa yêu cầu-->\r\n<div class=\"flex justify-content-center dialog-vnpt\">\r\n    <p-dialog [breakpoints]=\"{ '1199px': '75vw', '575px': '90vw' }\" [header]=\"titlePopup\"\r\n              [(visible)]=\"isShowCreateRequest\" [modal]=\"true\" [style]=\"{ width: '800px', overflowY :'scroll', maxHeight : '80%' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n        <form class=\"mt-3\" [formGroup]=\"formTicketSim\" (ngSubmit)=\"createOrUpdateRequest()\">\r\n            <div class=\"flex flex-row flex-wrap justify-content-between w-full\">\r\n                <div class=\"w-full field grid chart-grid\" *ngIf=\"this.userInfo.type == this.userType.ADMIN && typeRequest == 'detail'\">\r\n                    <label htmlFor=\"contactName\" class=\"col-fixed\"\r\n                           style=\"width:180px\">{{ tranService.translate(\"account.label.province\") }}<span\r\n                        class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\">\r\n                        <span>{{getProvinceName(ticket.provinceCode)}}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- contactName -->\r\n                <div class=\"w-full field grid chart-grid\">\r\n                    <label htmlFor=\"contactName\" class=\"col-fixed\" style=\"width:180px;height: fit-content\">{{tranService.translate(\"ticket.label.customerName\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\" style=\"width: calc(100% - 180px);overflow-wrap: break-word\">\r\n                        <input *ngIf=\"typeRequest == 'update'|| typeRequest == 'create'\" class=\"w-full\"\r\n                               pInputText id=\"contactName\"\r\n                               [(ngModel)]=\"ticket.contactName\"\r\n                               formControlName=\"contactName\"\r\n                               [required]=\"true\"\r\n                               [maxLength]=\"maxlengthContactName\"\r\n                               pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$\"\r\n                               [placeholder]=\"tranService.translate('account.text.inputFullname')\"\r\n                        />\r\n                        <span *ngIf=\"typeRequest == 'detail'\">{{ticket.contactName}}</span>\r\n\r\n                    </div>\r\n                </div>\r\n                <!-- error fullname -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"fullName\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.contactName.dirty && formTicketSim.controls.contactName.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.contactName.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.contactName.errors?.pattern\">{{tranService.translate(\"global.message.formatContainVN\")}}</small>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- email -->\r\n                <div class=\"w-full field grid chart-grid\">\r\n                    <label htmlFor=\"email\" class=\"col-fixed\" style=\"width:180px;height: fit-content\">{{tranService.translate(\"ticket.label.email\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\" style=\"width: calc(100% - 180px);overflow-wrap: break-word\">\r\n                        <input *ngIf=\"typeRequest == 'update'|| typeRequest == 'create'\" class=\"w-full\"\r\n                               pInputText id=\"contactEmail\"\r\n                               [(ngModel)]=\"ticket.contactEmail\"\r\n                               formControlName=\"contactEmail\"\r\n                               [required]=\"true\"\r\n                               [maxLength]=\"50\"\r\n                               pattern=\"^[a-z0-9]+[a-z0-9\\-\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\-\\._]*[a-z0-9]+)+(\\.[a-z]{2,})$\"\r\n                               [placeholder]=\"tranService.translate('account.text.inputEmail')\"\r\n                        />\r\n                        <span *ngIf=\"typeRequest == 'detail'\">{{ticket.contactEmail}}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- error email -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"email\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.contactEmail.dirty && formTicketSim.controls.contactEmail.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.contactEmail.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.contactEmail.errors?.pattern\">{{tranService.translate(\"global.message.invalidEmail\")}}</small>\r\n                        <!--                            <small class=\"text-red-500\" *ngIf=\"isEmailExisted\">{{tranService.translate(\"global.message.exists\",{type: tranService.translate(\"account.label.email\").toLowerCase()})}}</small> -->\r\n                    </div>\r\n                </div>\r\n                <!-- phone -->\r\n                <div class=\"w-full field grid chart-grid\">\r\n                    <label htmlFor=\"phone\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"ticket.label.phone\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\">\r\n                        <input *ngIf=\"typeRequest == 'update'|| typeRequest == 'create'\" class=\"w-full\"\r\n                               pInputText id=\"contactPhone\"\r\n                               [(ngModel)]=\"ticket.contactPhone\"\r\n                               formControlName=\"contactPhone\"\r\n                               [required]=\"true\"\r\n                               pattern=\"^((\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\"\r\n                               (keydown)=\"preventCharacter($event)\"\r\n                               [maxLength]=\"11\"\r\n                               [placeholder]=\"tranService.translate('account.text.inputPhone')\"\r\n                        />\r\n                        <span *ngIf=\"typeRequest == 'detail'\">{{ticket.contactPhone}}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- error phone -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"phone\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.contactPhone.dirty && formTicketSim.controls.contactPhone.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.contactPhone.errors?.pattern\">{{tranService.translate(\"ticket.message.invalidPhone\")}}</small>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- content-->\r\n                <div class=\"w-full field grid chart-grid\">\r\n                    <label htmlFor=\"content\" class=\"col-fixed\" style=\"width:180px;height: fit-content;\">{{tranService.translate(\"ticket.label.content\")}}</label>\r\n                    <div class=\"col\">\r\n                            <textarea *ngIf=\"typeRequest=='create'\" class=\"w-full\" style=\"resize: none;\"\r\n                                       rows=\"5\"\r\n                                       [autoResize]=\"false\"\r\n                                       pInputTextarea id=\"content\"\r\n                                       [(ngModel)]=\"ticket.content\"\r\n                                       formControlName=\"content\"\r\n                                       [maxlength]=\"255\"\r\n                                       [placeholder]=\"tranService.translate('ticket.label.content')\"\r\n                                      (keydown)=\"onKeyDownContent($event)\"\r\n                            ></textarea>\r\n                        <span style=\"word-break: break-all\" *ngIf=\"typeRequest=='update' || typeRequest == 'detail'\">{{ticket.content}}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- error content -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"content\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.content.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                    </div>\r\n                </div>\r\n                <!-- note-->\r\n                <div class=\"w-full field grid chart-grid\">\r\n                    <label htmlFor=\"note\" class=\"col-fixed\" style=\"width:180px;height: fit-content;\">{{tranService.translate(\"ticket.label.note\")}}</label>\r\n                    <div class=\"col\">\r\n                            <textarea *ngIf=\"typeRequest=='create'\"  class=\"w-full\" style=\"resize: none;\"\r\n                                       rows=\"5\"\r\n                                       [autoResize]=\"false\"\r\n                                       pInputTextarea id=\"note\"\r\n                                       [(ngModel)]=\"ticket.note\"\r\n                                       formControlName=\"note\"\r\n                                       [maxlength]=\"255\"\r\n                                       [placeholder]=\"tranService.translate('ticket.label.note')\"\r\n                                      (keydown)=\"onKeyDownNote($event)\"\r\n                            ></textarea>\r\n                        <span style=\"word-break: break-all\" *ngIf=\"typeRequest=='update' || typeRequest == 'detail'\">{{ticket.note}}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- error note -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"note\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.note.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- chuyen xu ly-->\r\n                <div *ngIf=\"(typeRequest=='update' && userInfo.type == userType.PROVINCE && ticket.status == null && ticket.statusOld == 0) || (userInfo.type == userType.PROVINCE && typeRequest == 'detail' && ticket.assigneeId != null)\" class=\"w-full field grid\">\r\n                    <label for=\"type\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"ticket.label.transferProcessing\")}}</label>\r\n                    <div class=\"col\" style=\"max-width: calc(100% - 180px);position: relative\">\r\n                        <vnpt-select\r\n                            class=\"w-full\"\r\n                            [(value)]=\"ticket.assigneeId\"\r\n                            [placeholder]=\"tranService.translate('ticket.label.transferProcessing')\"\r\n                            [disabled]=\"ticket.assigneeId != null && typeRequest == 'detail'\"\r\n                            objectKey=\"account\"\r\n                            paramKey=\"email\"\r\n                            keyReturn=\"id\"\r\n                            displayPattern=\"${email}\"\r\n                            [isMultiChoice]=\"false\"\r\n                            [paramDefault]=\"{type : 3}\"\r\n                            [stylePositionBoxSelect] = \"{bottom: '40px', left: '12px', 'min-width':'calc(100% - 20px)'}\"\r\n                        ></vnpt-select>\r\n                    </div>\r\n                </div>\r\n                <!-- error chuyen xu ly-->\r\n                <div *ngIf=\"(typeRequest=='update' && userInfo.type == userType.PROVINCE && ticket.status == null && ticket.statusOld == 0) || typeRequest == 'detail'\" class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"userType\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.assigneeId.dirty && formTicketSim.controls.assigneeId.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                    </div>\r\n                </div>\r\n                <!-- trang thai-->\r\n                <div *ngIf=\"typeRequest=='update' || typeRequest == 'detail'\" [class]=\"(userInfo.type == userType.PROVINCE && typeRequest != 'detail') ? (ticket.assigneeId == null || (ticket.assigneeId != null && !listActivatedAccount.includes(ticket.assigneeId)) ? '': 'hidden'): ''\" class=\"w-full field grid chart-grid\">\r\n                    <label for=\"status\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"ticket.label.status\")}}</label>\r\n                    <div class=\"col\">\r\n                        <p-dropdown *ngIf=\"typeRequest=='update'\" styleClass=\"w-full\"\r\n                                    [showClear]=\"true\"\r\n                                    id=\"type\" [autoDisplayFirst]=\"true\"\r\n                                    [(ngModel)]=\"ticket.status\"\r\n                                    formControlName=\"status\"\r\n                                    [options]=\"mapTicketStatus[ticket.statusOld]\"\r\n                                    optionLabel=\"label\"\r\n                                    optionValue=\"value\"\r\n                                    [placeholder]=\"tranService.translate('ticket.label.status')\"\r\n                                    [emptyMessage]=\"tranService.translate('global.text.nodata')\"\r\n                        ></p-dropdown>\r\n                        <span *ngIf=\"typeRequest=='detail' && ticket.statusOld == 0\" [class]=\"['p-2', 'text-white', 'bg-cyan-300', 'border-round', 'inline-block']\">{{getValueStatus(ticket.statusOld)}}</span>\r\n                        <span *ngIf=\"typeRequest=='detail' && ticket.statusOld == 1\"  [class]=\"['p-2', 'text-white', 'bg-bluegray-500', 'border-round', 'inline-block']\">{{getValueStatus(ticket.statusOld)}}</span>\r\n                        <span *ngIf=\"typeRequest=='detail' && ticket.statusOld == 2\"  [class]=\"['p-2', 'text-white', 'bg-orange-400', 'border-round', 'inline-block']\">{{getValueStatus(ticket.statusOld)}}</span>\r\n                        <span *ngIf=\"typeRequest=='detail' && ticket.statusOld == 3\"  [class]=\"['p-2', 'text-white', 'bg-red-500', 'border-round', 'inline-block']\">{{getValueStatus(ticket.statusOld)}}</span>\r\n                        <span *ngIf=\"typeRequest=='detail' && ticket.statusOld == 4\"  [class]=\"['p-2', 'text-white', 'bg-green-500', 'border-round', 'inline-block']\">{{getValueStatus(ticket.statusOld)}}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- error trang thai -->\r\n                <div *ngIf=\"typeRequest=='update' || typeRequest == 'detail' && ticket.assigneeId == null\" class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"userType\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.status.dirty && formTicketSim.controls.status.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <!-- ghi chu xu ly-->\r\n            <div *ngIf=\"typeRequest=='update'\" [class]=\"userInfo.type == userType.PROVINCE ? (ticket.assigneeId == null || (ticket.assigneeId != null && !listActivatedAccount.includes(ticket.assigneeId)) ? '': 'hidden'): ''\" class=\"w-full field grid chart-grid\">\r\n                <label htmlFor=\"contactName\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"ticket.label.processingNotes\")}}<span *ngIf=\"ticket.status\" class=\"text-red-500\">*</span></label>\r\n                <div class=\"col\">\r\n                    <input class=\"w-full\"\r\n                           pInputText id=\"cause\"\r\n                           [(ngModel)]=\"ticket.cause\"\r\n                           formControlName=\"cause\"\r\n                           [required]=\"ticket.status != null\"\r\n                           [maxLength]=\"255\"\r\n                           [placeholder]=\"tranService.translate('ticket.label.processingNotes')\"\r\n                           (keydown)=\"onKeyDownCause($event)\"\r\n                    />\r\n                </div>\r\n            </div>\r\n            <!-- error ghi chu xu ly-->\r\n            <div *ngIf=\"typeRequest=='update' || typeRequest == 'detail' && ticket.assigneeId == null\" class=\"w-full field grid text-error-field\">\r\n                <label htmlFor=\"fullName\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                <div class=\"col\">\r\n                    <small class=\"text-red-500\" *ngIf=\"(formTicketSim.controls.cause.dirty && formTicketSim.controls.cause.errors?.required) || (this.ticket.status != null && this.ticket.cause != null && this.ticket.cause.trim() == '')\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                </div>\r\n            </div>\r\n\r\n            <!--          danh sach ghi chu xu ly-->\r\n            <div *ngIf=\"(typeRequest=='update' || typeRequest == 'detail') && (listNotes && listNotes.length > 0)\" class=\"w-full field grid chart-grid\">\r\n                <label htmlFor=\"content\" class=\"col-fixed font-medium\"\r\n                       style=\"width:180px;height: fit-content;\">{{ tranService.translate(\"ticket.label.listNote\") }}</label>\r\n                <p-table style=\"width: 100%\" [value]=\"listNotes\" [tableStyle]=\"{ 'min-width': '50rem' }\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <tr>\r\n                            <th>{{tranService.translate('global.text.stt')}}</th>\r\n                            <th>{{tranService.translate('account.text.account')}}</th>\r\n                            <th style=\"min-width: 146px\">{{tranService.translate('global.button.changeStatus')}}</th>\r\n                            <th style=\"min-width: 155px\">{{tranService.translate('account.label.time')}}</th>\r\n                            <th>{{tranService.translate('ticket.label.content')}}</th>\r\n                        </tr>\r\n                    </ng-template>\r\n                    <ng-template pTemplate=\"body\" let-note let-i=\"rowIndex\">\r\n                        <tr [formGroup]=\"mapForm[note.id]\">\r\n                            <td>{{ i + 1 }}</td>\r\n                            <td>{{ note.userName }}</td>\r\n                            <td>{{getValueStatus(note.status)}}</td>\r\n                            <td>{{note.createdDate | date:\"HH:mm:ss dd/MM/yyyy\"}}</td>\r\n                            <td>\r\n                                <input *ngIf=\"typeRequest == 'update'\"  class=\"w-full\"\r\n                                       pInputText id=\"content\"\r\n                                       [(ngModel)]=\"note.content\"\r\n                                       formControlName=\"content\"\r\n                                       [required]=\"true\"\r\n                                       [maxLength]=\"255\"\r\n                                       (keydown)=\"onKeyDownNoteContent($event, note)\"\r\n                                />\r\n                                <span *ngIf=\"typeRequest == 'detail'\">{{note.content}}</span>\r\n                                <small class=\"text-red-500\" *ngIf=\"mapForm[note.id].controls.content.dirty && mapForm[note.id].controls.content.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                                <small class=\"text-red-500\" *ngIf=\"mapForm[note.id].controls.content.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n\r\n                            </td>\r\n                        </tr>\r\n                    </ng-template>\r\n                </p-table>\r\n            </div>\r\n            <div *ngIf=\"typeRequest != 'detail'\" class=\"flex flex-row justify-content-center align-items-center mt-3\">\r\n                <p-button styleClass=\"mr-2 p-button-secondary\" [label]=\"tranService.translate('global.button.cancel')\" (click)=\"isShowCreateRequest = false\"></p-button>\r\n                <p-button type=\"submit\" styleClass=\"p-button-info\" [disabled]=\"formTicketSim.invalid || (this.ticket.status != null && this.ticket.cause.trim() == '') || (this.listNotes.length > 0 && !isFormValid())\" [label]=\"tranService.translate('global.button.save')\"></p-button>\r\n            </div>\r\n        </form>\r\n    </p-dialog>\r\n</div>\r\n"], "mappings": "AAEA,SAAQA,aAAa,QAAO,sCAAsC;AAElE,SAAQC,SAAS,QAAO,iCAAiC;AACzD,SAAQC,aAAa,QAAO,wBAAwB;AACpD,SAAQC,cAAc,QAAO,yCAAyC;AACtE,SAAgFC,UAAU,QAAO,gBAAgB;AACjH,SAAQC,sBAAsB,QAAO,gDAAgD;;;;;;;;;;;;;;;;;;;;;ICF7EC,EAAA,CAAAC,cAAA,mBAG8C;IAApCD,EAAA,CAAAE,UAAA,mBAAAC,yEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,OAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IACrCT,EAAA,CAAAU,YAAA,EAAW;;;;IAFDV,EAAA,CAAAW,UAAA,UAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,yBAAuD;;;;;;IAU7Dd,EAAA,CAAAC,cAAA,cAA2E;IAKvDD,EAAA,CAAAE,UAAA,2BAAAa,+EAAAC,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAa,IAAA;MAAA,MAAAC,OAAA,GAAAlB,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAU,OAAA,CAAAC,UAAA,CAAAC,YAAA,GAAAJ,MAAA,CACxC;IAAA,EADgE;IAMhDhB,EAAA,CAAAU,YAAA,EAAa;IACdV,EAAA,CAAAC,cAAA,gBAA8B;IAAAD,EAAA,CAAAqB,MAAA,GAAmD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAT7EV,EAAA,CAAAsB,SAAA,GAAkB;IAAlBtB,EAAA,CAAAW,UAAA,mBAAkB,uDAAAY,MAAA,CAAAJ,UAAA,CAAAC,YAAA,gCAAAG,MAAA,CAAAC,YAAA;IASAxB,EAAA,CAAAsB,SAAA,GAAmD;IAAnDtB,EAAA,CAAAyB,iBAAA,CAAAF,MAAA,CAAAV,WAAA,CAAAC,SAAA,2BAAmD;;;;;IAwDrGd,EAAA,CAAA0B,SAAA,qBAYc;;;;IAXV1B,EAAA,CAAAW,UAAA,oCAAmC,uCAAAgB,MAAA,CAAAC,OAAA,aAAAD,MAAA,CAAAE,OAAA,aAAAF,MAAA,CAAAG,WAAA,gBAAAH,MAAA,CAAAI,UAAA,cAAAJ,MAAA,CAAAK,MAAA,CAAAC,IAAA,CAAAN,MAAA,eAAAA,MAAA,CAAAO,QAAA,UAAAP,MAAA,CAAAQ,IAAA,YAAAR,MAAA,CAAAR,UAAA,gBAAAQ,MAAA,CAAAd,WAAA,CAAAC,SAAA;;;;;IAYvCd,EAAA,CAAA0B,SAAA,qBAYc;;;;IAXV1B,EAAA,CAAAW,UAAA,oCAAmC,uCAAAyB,MAAA,CAAAR,OAAA,aAAAQ,MAAA,CAAAP,OAAA,aAAAO,MAAA,CAAAN,WAAA,gBAAAM,MAAA,CAAAL,UAAA,cAAAK,MAAA,CAAAJ,MAAA,CAAAC,IAAA,CAAAG,MAAA,eAAAA,MAAA,CAAAF,QAAA,UAAAE,MAAA,CAAAD,IAAA,YAAAC,MAAA,CAAAjB,UAAA,gBAAAiB,MAAA,CAAAvB,WAAA,CAAAC,SAAA;;;;;IAkBvBd,EAAA,CAAAC,cAAA,cAAuH;IAExFD,EAAA,CAAAqB,MAAA,GAAqD;IAAArB,EAAA,CAAAC,cAAA,eACvD;IAAAD,EAAA,CAAAqB,MAAA,QAAC;IAAArB,EAAA,CAAAU,YAAA,EAAO;IACjCV,EAAA,CAAAC,cAAA,cAAiB;IACPD,EAAA,CAAAqB,MAAA,GAAwC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAH9BV,EAAA,CAAAsB,SAAA,GAAqD;IAArDtB,EAAA,CAAAyB,iBAAA,CAAAY,MAAA,CAAAxB,WAAA,CAAAC,SAAA,2BAAqD;IAGtEd,EAAA,CAAAsB,SAAA,GAAwC;IAAxCtB,EAAA,CAAAyB,iBAAA,CAAAY,MAAA,CAAAC,eAAA,CAAAD,MAAA,CAAAE,MAAA,CAAAnB,YAAA,EAAwC;;;;;;IAO9CpB,EAAA,CAAAC,cAAA,gBAQE;IANKD,EAAA,CAAAE,UAAA,2BAAAsC,4EAAAxB,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAqC,IAAA;MAAA,MAAAC,OAAA,GAAA1C,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAkC,OAAA,CAAAH,MAAA,CAAAI,WAAA,GAAA3B,MAAA,CACvC;IAAA,EAD0D;IAFvChB,EAAA,CAAAU,YAAA,EAQE;;;;IANKV,EAAA,CAAAW,UAAA,YAAAiC,MAAA,CAAAL,MAAA,CAAAI,WAAA,CAAgC,gCAAAC,MAAA,CAAAC,oBAAA,iBAAAD,MAAA,CAAA/B,WAAA,CAAAC,SAAA;;;;;IAOvCd,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAqB,MAAA,GAAsB;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA7BV,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAyB,iBAAA,CAAAqB,MAAA,CAAAP,MAAA,CAAAI,WAAA,CAAsB;;;;;IAQ5D3C,EAAA,CAAAC,cAAA,gBAAoI;IAAAD,EAAA,CAAAqB,MAAA,GAAoD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA5DV,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAyB,iBAAA,CAAAsB,MAAA,CAAAlC,WAAA,CAAAC,SAAA,4BAAoD;;;;;;;;;;IACxLd,EAAA,CAAAC,cAAA,gBAAyF;IAAAD,EAAA,CAAAqB,MAAA,GAA+D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAvEV,EAAA,CAAAsB,SAAA,GAA+D;IAA/DtB,EAAA,CAAAyB,iBAAA,CAAAuB,MAAA,CAAAnC,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAiD,eAAA,IAAAC,GAAA,GAA+D;;;;;IACxJlD,EAAA,CAAAC,cAAA,gBAAuF;IAAAD,EAAA,CAAAqB,MAAA,GAA2D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAnEV,EAAA,CAAAsB,SAAA,GAA2D;IAA3DtB,EAAA,CAAAyB,iBAAA,CAAA0B,MAAA,CAAAtC,WAAA,CAAAC,SAAA,mCAA2D;;;;;;IAQlJd,EAAA,CAAAC,cAAA,gBAQE;IANKD,EAAA,CAAAE,UAAA,2BAAAkD,4EAAApC,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAiD,IAAA;MAAA,MAAAC,OAAA,GAAAtD,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAA8C,OAAA,CAAAf,MAAA,CAAAgB,YAAA,GAAAvC,MAAA,CACvC;IAAA,EAD2D;IAFxChB,EAAA,CAAAU,YAAA,EAQE;;;;IANKV,EAAA,CAAAW,UAAA,YAAA6C,OAAA,CAAAjB,MAAA,CAAAgB,YAAA,CAAiC,mDAAAC,OAAA,CAAA3C,WAAA,CAAAC,SAAA;;;;;IAOxCd,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAqB,MAAA,GAAuB;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA9BV,EAAA,CAAAsB,SAAA,GAAuB;IAAvBtB,EAAA,CAAAyB,iBAAA,CAAAgC,OAAA,CAAAlB,MAAA,CAAAgB,YAAA,CAAuB;;;;;IAO7DvD,EAAA,CAAAC,cAAA,gBAAsI;IAAAD,EAAA,CAAAqB,MAAA,GAAoD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA5DV,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAyB,iBAAA,CAAAiC,OAAA,CAAA7C,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAC1Ld,EAAA,CAAAC,cAAA,gBAA0F;IAAAD,EAAA,CAAAqB,MAAA,GAA+D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAvEV,EAAA,CAAAsB,SAAA,GAA+D;IAA/DtB,EAAA,CAAAyB,iBAAA,CAAAkC,OAAA,CAAA9C,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAiD,eAAA,IAAAC,GAAA,GAA+D;;;;;IACzJlD,EAAA,CAAAC,cAAA,gBAAwF;IAAAD,EAAA,CAAAqB,MAAA,GAAwD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAhEV,EAAA,CAAAsB,SAAA,GAAwD;IAAxDtB,EAAA,CAAAyB,iBAAA,CAAAmC,OAAA,CAAA/C,WAAA,CAAAC,SAAA,gCAAwD;;;;;;IAQhJd,EAAA,CAAAC,cAAA,gBASE;IAPKD,EAAA,CAAAE,UAAA,2BAAA2D,4EAAA7C,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA0D,IAAA;MAAA,MAAAC,OAAA,GAAA/D,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAuD,OAAA,CAAAxB,MAAA,CAAAyB,YAAA,GAAAhD,MAAA,CACvC;IAAA,EAD2D,qBAAAiD,sEAAAjD,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA0D,IAAA;MAAA,MAAAI,OAAA,GAAAlE,EAAA,CAAAO,aAAA;MAAA,OAItBP,EAAA,CAAAQ,WAAA,CAAA0D,OAAA,CAAAC,gBAAA,CAAAnD,MAAA,CAAwB;IAAA,EAJF;IAFxChB,EAAA,CAAAU,YAAA,EASE;;;;IAPKV,EAAA,CAAAW,UAAA,YAAAyD,OAAA,CAAA7B,MAAA,CAAAyB,YAAA,CAAiC,mDAAAI,OAAA,CAAAvD,WAAA,CAAAC,SAAA;;;;;IAQxCd,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAqB,MAAA,GAAuB;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA9BV,EAAA,CAAAsB,SAAA,GAAuB;IAAvBtB,EAAA,CAAAyB,iBAAA,CAAA4C,OAAA,CAAA9B,MAAA,CAAAyB,YAAA,CAAuB;;;;;IAO7DhE,EAAA,CAAAC,cAAA,gBAAsI;IAAAD,EAAA,CAAAqB,MAAA,GAAoD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA5DV,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAyB,iBAAA,CAAA6C,OAAA,CAAAzD,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAC1Ld,EAAA,CAAAC,cAAA,gBAAwF;IAAAD,EAAA,CAAAqB,MAAA,GAAwD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAhEV,EAAA,CAAAsB,SAAA,GAAwD;IAAxDtB,EAAA,CAAAyB,iBAAA,CAAA8C,OAAA,CAAA1D,WAAA,CAAAC,SAAA,gCAAwD;;;;;;IAQ5Id,EAAA,CAAAC,cAAA,mBASC;IALUD,EAAA,CAAAE,UAAA,2BAAAsE,kFAAAxD,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAqE,IAAA;MAAA,MAAAC,OAAA,GAAA1E,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAkE,OAAA,CAAAnC,MAAA,CAAAoC,OAAA,GAAA3D,MAAA,CAC/C;IAAA,EAD8D,qBAAA4D,4EAAA5D,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAqE,IAAA;MAAA,MAAAI,OAAA,GAAA7E,EAAA,CAAAO,aAAA;MAAA,OAIlBP,EAAA,CAAAQ,WAAA,CAAAqE,OAAA,CAAAC,gBAAA,CAAA9D,MAAA,CAAwB;IAAA,EAJN;IAKtChB,EAAA,CAAAU,YAAA,EAAW;;;;IAPDV,EAAA,CAAAW,UAAA,qBAAoB,YAAAoE,OAAA,CAAAxC,MAAA,CAAAoC,OAAA,mCAAAI,OAAA,CAAAlE,WAAA,CAAAC,SAAA;;;;;IAQnCd,EAAA,CAAAC,cAAA,eAA6F;IAAAD,EAAA,CAAAqB,MAAA,GAAkB;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAAzBV,EAAA,CAAAsB,SAAA,GAAkB;IAAlBtB,EAAA,CAAAyB,iBAAA,CAAAuD,OAAA,CAAAzC,MAAA,CAAAoC,OAAA,CAAkB;;;;;IAO/G3E,EAAA,CAAAC,cAAA,gBAAqF;IAAAD,EAAA,CAAAqB,MAAA,GAA+D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAvEV,EAAA,CAAAsB,SAAA,GAA+D;IAA/DtB,EAAA,CAAAyB,iBAAA,CAAAwD,OAAA,CAAApE,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAiD,eAAA,IAAAC,GAAA,GAA+D;;;;;;IAOhJlD,EAAA,CAAAC,cAAA,mBASC;IALUD,EAAA,CAAAE,UAAA,2BAAAgF,kFAAAlE,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA+E,IAAA;MAAA,MAAAC,OAAA,GAAApF,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAA4E,OAAA,CAAA7C,MAAA,CAAA8C,IAAA,GAAArE,MAAA,CAC/C;IAAA,EAD2D,qBAAAsE,4EAAAtE,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA+E,IAAA;MAAA,MAAAI,OAAA,GAAAvF,EAAA,CAAAO,aAAA;MAAA,OAIfP,EAAA,CAAAQ,WAAA,CAAA+E,OAAA,CAAAC,aAAA,CAAAxE,MAAA,CAAqB;IAAA,EAJN;IAKnChB,EAAA,CAAAU,YAAA,EAAW;;;;IAPDV,EAAA,CAAAW,UAAA,qBAAoB,YAAA8E,OAAA,CAAAlD,MAAA,CAAA8C,IAAA,mCAAAI,OAAA,CAAA5E,WAAA,CAAAC,SAAA;;;;;IAQnCd,EAAA,CAAAC,cAAA,eAA6F;IAAAD,EAAA,CAAAqB,MAAA,GAAe;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAAtBV,EAAA,CAAAsB,SAAA,GAAe;IAAftB,EAAA,CAAAyB,iBAAA,CAAAiE,OAAA,CAAAnD,MAAA,CAAA8C,IAAA,CAAe;;;;;IAO5GrF,EAAA,CAAAC,cAAA,gBAAkF;IAAAD,EAAA,CAAAqB,MAAA,GAA+D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAvEV,EAAA,CAAAsB,SAAA,GAA+D;IAA/DtB,EAAA,CAAAyB,iBAAA,CAAAkE,OAAA,CAAA9E,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAiD,eAAA,IAAAC,GAAA,GAA+D;;;;;;;;;;;;;;;;;;IAKzJlD,EAAA,CAAAC,cAAA,cAAuP;IAC3LD,EAAA,CAAAqB,MAAA,GAA4D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;IAC5HV,EAAA,CAAAC,cAAA,cAA0E;IAGlED,EAAA,CAAAE,UAAA,yBAAA0F,8EAAA5E,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAyF,IAAA;MAAA,MAAAC,OAAA,GAAA9F,EAAA,CAAAO,aAAA;MAAA,OAAWP,EAAA,CAAAQ,WAAA,CAAAsF,OAAA,CAAAvD,MAAA,CAAAwD,UAAA,GAAA/E,MAAA,CAClC;IAAA,EADoD;IAUhChB,EAAA,CAAAU,YAAA,EAAc;;;;IAdqCV,EAAA,CAAAsB,SAAA,GAA4D;IAA5DtB,EAAA,CAAAyB,iBAAA,CAAAuE,OAAA,CAAAnF,WAAA,CAAAC,SAAA,oCAA4D;IAI5Gd,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAW,UAAA,UAAAqF,OAAA,CAAAzD,MAAA,CAAAwD,UAAA,CAA6B,gBAAAC,OAAA,CAAAnF,WAAA,CAAAC,SAAA,iDAAAkF,OAAA,CAAAzD,MAAA,CAAAwD,UAAA,YAAAC,OAAA,CAAAC,WAAA,sDAAAjG,EAAA,CAAAiD,eAAA,IAAAiD,GAAA,6BAAAlG,EAAA,CAAAiD,eAAA,IAAAkD,GAAA;;;;;IAiBjCnG,EAAA,CAAAC,cAAA,gBAAkI;IAAAD,EAAA,CAAAqB,MAAA,GAAoD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA5DV,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAyB,iBAAA,CAAA2E,OAAA,CAAAvF,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAH9Ld,EAAA,CAAAC,cAAA,cAAmM;IAC/LD,EAAA,CAAA0B,SAAA,gBAAwE;IACxE1B,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAqG,UAAA,IAAAC,kDAAA,oBAA8L;IAClMtG,EAAA,CAAAU,YAAA,EAAM;;;;IAD2BV,EAAA,CAAAsB,SAAA,GAAmG;IAAnGtB,EAAA,CAAAW,UAAA,SAAA4F,OAAA,CAAAC,aAAA,CAAAC,QAAA,CAAAV,UAAA,CAAAW,KAAA,KAAAH,OAAA,CAAAC,aAAA,CAAAC,QAAA,CAAAV,UAAA,CAAAY,MAAA,kBAAAJ,OAAA,CAAAC,aAAA,CAAAC,QAAA,CAAAV,UAAA,CAAAY,MAAA,CAAAC,QAAA,EAAmG;;;;;;IAOhI5G,EAAA,CAAAC,cAAA,qBAUC;IAPWD,EAAA,CAAAE,UAAA,2BAAA2G,4FAAA7F,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA0G,IAAA;MAAA,MAAAC,OAAA,GAAA/G,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAuG,OAAA,CAAAxE,MAAA,CAAAyE,MAAA,GAAAhG,MAAA,CAC5C;IAAA,EAD0D;IAOtChB,EAAA,CAAAU,YAAA,EAAa;;;;IATFV,EAAA,CAAAW,UAAA,mBAAkB,sCAAAsG,OAAA,CAAA1E,MAAA,CAAAyE,MAAA,aAAAC,OAAA,CAAAC,eAAA,CAAAD,OAAA,CAAA1E,MAAA,CAAA4E,SAAA,kBAAAF,OAAA,CAAApG,WAAA,CAAAC,SAAA,yCAAAmG,OAAA,CAAApG,WAAA,CAAAC,SAAA;;;;;;;;IAU9Bd,EAAA,CAAAC,cAAA,WAA4I;IAAAD,EAAA,CAAAqB,MAAA,GAAoC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA1HV,EAAA,CAAAoH,UAAA,CAAApH,EAAA,CAAAiD,eAAA,IAAAoE,GAAA,EAA8E;IAACrH,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAyB,iBAAA,CAAA6F,OAAA,CAAAC,cAAA,CAAAD,OAAA,CAAA/E,MAAA,CAAA4E,SAAA,EAAoC;;;;;;;;IAChLnH,EAAA,CAAAC,cAAA,WAAiJ;IAAAD,EAAA,CAAAqB,MAAA,GAAoC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA9HV,EAAA,CAAAoH,UAAA,CAAApH,EAAA,CAAAiD,eAAA,IAAAuE,GAAA,EAAkF;IAACxH,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAyB,iBAAA,CAAAgG,OAAA,CAAAF,cAAA,CAAAE,OAAA,CAAAlF,MAAA,CAAA4E,SAAA,EAAoC;;;;;;;;IACrLnH,EAAA,CAAAC,cAAA,WAA+I;IAAAD,EAAA,CAAAqB,MAAA,GAAoC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA5HV,EAAA,CAAAoH,UAAA,CAAApH,EAAA,CAAAiD,eAAA,IAAAyE,GAAA,EAAgF;IAAC1H,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAyB,iBAAA,CAAAkG,OAAA,CAAAJ,cAAA,CAAAI,OAAA,CAAApF,MAAA,CAAA4E,SAAA,EAAoC;;;;;;;;IACnLnH,EAAA,CAAAC,cAAA,WAA4I;IAAAD,EAAA,CAAAqB,MAAA,GAAoC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAAzHV,EAAA,CAAAoH,UAAA,CAAApH,EAAA,CAAAiD,eAAA,IAAA2E,GAAA,EAA6E;IAAC5H,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAyB,iBAAA,CAAAoG,OAAA,CAAAN,cAAA,CAAAM,OAAA,CAAAtF,MAAA,CAAA4E,SAAA,EAAoC;;;;;;;;IAChLnH,EAAA,CAAAC,cAAA,WAA8I;IAAAD,EAAA,CAAAqB,MAAA,GAAoC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA3HV,EAAA,CAAAoH,UAAA,CAAApH,EAAA,CAAAiD,eAAA,IAAA6E,GAAA,EAA+E;IAAC9H,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAyB,iBAAA,CAAAsG,OAAA,CAAAR,cAAA,CAAAQ,OAAA,CAAAxF,MAAA,CAAA4E,SAAA,EAAoC;;;;;IAlB1LnH,EAAA,CAAAC,cAAA,cAAkT;IACpPD,EAAA,CAAAqB,MAAA,GAAgD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;IAClHV,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAqG,UAAA,IAAA2B,uDAAA,yBAUc;IACdhI,EAAA,CAAAqG,UAAA,IAAA4B,iDAAA,mBAAuL;IACvLjI,EAAA,CAAAqG,UAAA,IAAA6B,iDAAA,mBAA4L;IAC5LlI,EAAA,CAAAqG,UAAA,IAAA8B,iDAAA,mBAA0L;IAC1LnI,EAAA,CAAAqG,UAAA,IAAA+B,iDAAA,mBAAuL;IACvLpI,EAAA,CAAAqG,UAAA,IAAAgC,iDAAA,mBAAyL;IAC7LrI,EAAA,CAAAU,YAAA,EAAM;;;;IAnBoDV,EAAA,CAAAoH,UAAA,CAAAkB,OAAA,CAAAC,QAAA,CAAAC,IAAA,IAAAF,OAAA,CAAAG,QAAA,CAAAC,QAAA,IAAAJ,OAAA,CAAArC,WAAA,eAAAqC,OAAA,CAAA/F,MAAA,CAAAwD,UAAA,YAAAuC,OAAA,CAAA/F,MAAA,CAAAwD,UAAA,aAAAuC,OAAA,CAAAK,oBAAA,CAAAC,QAAA,CAAAN,OAAA,CAAA/F,MAAA,CAAAwD,UAAA,uBAA8M;IAC9M/F,EAAA,CAAAsB,SAAA,GAAgD;IAAhDtB,EAAA,CAAAyB,iBAAA,CAAA6G,OAAA,CAAAzH,WAAA,CAAAC,SAAA,wBAAgD;IAEzFd,EAAA,CAAAsB,SAAA,GAA2B;IAA3BtB,EAAA,CAAAW,UAAA,SAAA2H,OAAA,CAAArC,WAAA,aAA2B;IAWjCjG,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAW,UAAA,SAAA2H,OAAA,CAAArC,WAAA,gBAAAqC,OAAA,CAAA/F,MAAA,CAAA4E,SAAA,MAAoD;IACpDnH,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAW,UAAA,SAAA2H,OAAA,CAAArC,WAAA,gBAAAqC,OAAA,CAAA/F,MAAA,CAAA4E,SAAA,MAAoD;IACpDnH,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAW,UAAA,SAAA2H,OAAA,CAAArC,WAAA,gBAAAqC,OAAA,CAAA/F,MAAA,CAAA4E,SAAA,MAAoD;IACpDnH,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAW,UAAA,SAAA2H,OAAA,CAAArC,WAAA,gBAAAqC,OAAA,CAAA/F,MAAA,CAAA4E,SAAA,MAAoD;IACpDnH,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAW,UAAA,SAAA2H,OAAA,CAAArC,WAAA,gBAAAqC,OAAA,CAAA/F,MAAA,CAAA4E,SAAA,MAAoD;;;;;IAO3DnH,EAAA,CAAAC,cAAA,gBAA0H;IAAAD,EAAA,CAAAqB,MAAA,GAAoD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA5DV,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAyB,iBAAA,CAAAoH,OAAA,CAAAhI,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAHtLd,EAAA,CAAAC,cAAA,cAAsI;IAClID,EAAA,CAAA0B,SAAA,gBAAwE;IACxE1B,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAqG,UAAA,IAAAyC,kDAAA,oBAAsL;IAC1L9I,EAAA,CAAAU,YAAA,EAAM;;;;IAD2BV,EAAA,CAAAsB,SAAA,GAA2F;IAA3FtB,EAAA,CAAAW,UAAA,SAAAoI,OAAA,CAAAvC,aAAA,CAAAC,QAAA,CAAAO,MAAA,CAAAN,KAAA,KAAAqC,OAAA,CAAAvC,aAAA,CAAAC,QAAA,CAAAO,MAAA,CAAAL,MAAA,kBAAAoC,OAAA,CAAAvC,aAAA,CAAAC,QAAA,CAAAO,MAAA,CAAAL,MAAA,CAAAC,QAAA,EAA2F;;;;;IAMJ5G,EAAA,CAAAC,cAAA,eAAiD;IAAAD,EAAA,CAAAqB,MAAA,QAAC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;;;IADzLV,EAAA,CAAAC,cAAA,cAA0P;IACnLD,EAAA,CAAAqB,MAAA,GAAyD;IAAArB,EAAA,CAAAqG,UAAA,IAAA2C,kDAAA,mBAAyD;IAAAhJ,EAAA,CAAAU,YAAA,EAAQ;IAC7LV,EAAA,CAAAC,cAAA,cAAiB;IAGND,EAAA,CAAAE,UAAA,2BAAA+I,2EAAAjI,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA8I,IAAA;MAAA,MAAAC,OAAA,GAAAnJ,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAA2I,OAAA,CAAA5G,MAAA,CAAA6G,KAAA,GAAApI,MAAA,CACnC;IAAA,EADgD,qBAAAqI,qEAAArI,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA8I,IAAA;MAAA,MAAAI,OAAA,GAAAtJ,EAAA,CAAAO,aAAA;MAAA,OAKfP,EAAA,CAAAQ,WAAA,CAAA8I,OAAA,CAAAC,cAAA,CAAAvI,MAAA,CAAsB;IAAA,EALP;IAFjChB,EAAA,CAAAU,YAAA,EAQE;;;;IAXyBV,EAAA,CAAAoH,UAAA,CAAAoC,OAAA,CAAAjB,QAAA,CAAAC,IAAA,IAAAgB,OAAA,CAAAf,QAAA,CAAAC,QAAA,GAAAc,OAAA,CAAAjH,MAAA,CAAAwD,UAAA,YAAAyD,OAAA,CAAAjH,MAAA,CAAAwD,UAAA,aAAAyD,OAAA,CAAAb,oBAAA,CAAAC,QAAA,CAAAY,OAAA,CAAAjH,MAAA,CAAAwD,UAAA,uBAAiL;IAC7I/F,EAAA,CAAAsB,SAAA,GAAyD;IAAzDtB,EAAA,CAAAyB,iBAAA,CAAA+H,OAAA,CAAA3I,WAAA,CAAAC,SAAA,iCAAyD;IAAOd,EAAA,CAAAsB,SAAA,GAAmB;IAAnBtB,EAAA,CAAAW,UAAA,SAAA6I,OAAA,CAAAjH,MAAA,CAAAyE,MAAA,CAAmB;IAI3IhH,EAAA,CAAAsB,SAAA,GAA0B;IAA1BtB,EAAA,CAAAW,UAAA,YAAA6I,OAAA,CAAAjH,MAAA,CAAA6G,KAAA,CAA0B,aAAAI,OAAA,CAAAjH,MAAA,CAAAyE,MAAA,2CAAAwC,OAAA,CAAA3I,WAAA,CAAAC,SAAA;;;;;IAajCd,EAAA,CAAAC,cAAA,gBAAyN;IAAAD,EAAA,CAAAqB,MAAA,GAAoD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA5DV,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAyB,iBAAA,CAAAgI,OAAA,CAAA5I,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAHrRd,EAAA,CAAAC,cAAA,cAAsI;IAClID,EAAA,CAAA0B,SAAA,gBAAwE;IACxE1B,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAqG,UAAA,IAAAqD,mDAAA,oBAAqR;IACzR1J,EAAA,CAAAU,YAAA,EAAM;;;;IAD2BV,EAAA,CAAAsB,SAAA,GAA0L;IAA1LtB,EAAA,CAAAW,UAAA,SAAAgJ,OAAA,CAAAnD,aAAA,CAAAC,QAAA,CAAA2C,KAAA,CAAA1C,KAAA,KAAAiD,OAAA,CAAAnD,aAAA,CAAAC,QAAA,CAAA2C,KAAA,CAAAzC,MAAA,kBAAAgD,OAAA,CAAAnD,aAAA,CAAAC,QAAA,CAAA2C,KAAA,CAAAzC,MAAA,CAAAC,QAAA,KAAA+C,OAAA,CAAApH,MAAA,CAAAyE,MAAA,YAAA2C,OAAA,CAAApH,MAAA,CAAA6G,KAAA,YAAAO,OAAA,CAAApH,MAAA,CAAA6G,KAAA,CAAAQ,IAAA,SAA0L;;;;;IAUnN5J,EAAA,CAAAC,cAAA,SAAI;IACID,EAAA,CAAAqB,MAAA,GAA4C;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACrDV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,GAAiD;IAAArB,EAAA,CAAAU,YAAA,EAAK;IAC1DV,EAAA,CAAAC,cAAA,aAA6B;IAAAD,EAAA,CAAAqB,MAAA,GAAuD;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACzFV,EAAA,CAAAC,cAAA,aAA6B;IAAAD,EAAA,CAAAqB,MAAA,GAA+C;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACjFV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,IAAiD;IAAArB,EAAA,CAAAU,YAAA,EAAK;;;;IAJtDV,EAAA,CAAAsB,SAAA,GAA4C;IAA5CtB,EAAA,CAAAyB,iBAAA,CAAAoI,OAAA,CAAAhJ,WAAA,CAAAC,SAAA,oBAA4C;IAC5Cd,EAAA,CAAAsB,SAAA,GAAiD;IAAjDtB,EAAA,CAAAyB,iBAAA,CAAAoI,OAAA,CAAAhJ,WAAA,CAAAC,SAAA,yBAAiD;IACxBd,EAAA,CAAAsB,SAAA,GAAuD;IAAvDtB,EAAA,CAAAyB,iBAAA,CAAAoI,OAAA,CAAAhJ,WAAA,CAAAC,SAAA,+BAAuD;IACvDd,EAAA,CAAAsB,SAAA,GAA+C;IAA/CtB,EAAA,CAAAyB,iBAAA,CAAAoI,OAAA,CAAAhJ,WAAA,CAAAC,SAAA,uBAA+C;IACxEd,EAAA,CAAAsB,SAAA,GAAiD;IAAjDtB,EAAA,CAAAyB,iBAAA,CAAAoI,OAAA,CAAAhJ,WAAA,CAAAC,SAAA,yBAAiD;;;;;;IAUjDd,EAAA,CAAAC,cAAA,gBAOE;IALKD,EAAA,CAAAE,UAAA,2BAAA4J,kGAAA9I,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA2J,IAAA;MAAA,MAAAC,QAAA,GAAAhK,EAAA,CAAAO,aAAA,GAAA0J,SAAA;MAAA,OAAajK,EAAA,CAAAQ,WAAA,CAAAwJ,QAAA,CAAArF,OAAA,GAAA3D,MAAA,CAC/C;IAAA,EAD4D,qBAAAkJ,4FAAAlJ,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA2J,IAAA;MAAA,MAAAC,QAAA,GAAAhK,EAAA,CAAAO,aAAA,GAAA0J,SAAA;MAAA,MAAAE,OAAA,GAAAnK,EAAA,CAAAO,aAAA;MAAA,OAIfP,EAAA,CAAAQ,WAAA,CAAA2J,OAAA,CAAAC,oBAAA,CAAApJ,MAAA,EAAAgJ,QAAA,CAAkC;IAAA,EAJnB;IAFjChK,EAAA,CAAAU,YAAA,EAOE;;;;IALKV,EAAA,CAAAW,UAAA,YAAAqJ,QAAA,CAAArF,OAAA,CAA0B;;;;;IAMjC3E,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAqB,MAAA,GAAgB;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAAvBV,EAAA,CAAAsB,SAAA,GAAgB;IAAhBtB,EAAA,CAAAyB,iBAAA,CAAAuI,QAAA,CAAArF,OAAA,CAAgB;;;;;IACtD3E,EAAA,CAAAC,cAAA,gBAAkI;IAAAD,EAAA,CAAAqB,MAAA,GAAoD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA5DV,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAyB,iBAAA,CAAA4I,OAAA,CAAAxJ,WAAA,CAAAC,SAAA,4BAAoD;;;;;IACtLd,EAAA,CAAAC,cAAA,gBAAwF;IAAAD,EAAA,CAAAqB,MAAA,GAA+D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAvEV,EAAA,CAAAsB,SAAA,GAA+D;IAA/DtB,EAAA,CAAAyB,iBAAA,CAAA6I,OAAA,CAAAzJ,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAiD,eAAA,IAAAC,GAAA,GAA+D;;;;;IAhB/JlD,EAAA,CAAAC,cAAA,aAAmC;IAC3BD,EAAA,CAAAqB,MAAA,GAAW;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACpBV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,GAAmB;IAAArB,EAAA,CAAAU,YAAA,EAAK;IAC5BV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,GAA+B;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACxCV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,GAAiD;;IAAArB,EAAA,CAAAU,YAAA,EAAK;IAC1DV,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAqG,UAAA,KAAAkE,kEAAA,oBAOE;IACFvK,EAAA,CAAAqG,UAAA,KAAAmE,iEAAA,mBAA6D;IAC7DxK,EAAA,CAAAqG,UAAA,KAAAoE,kEAAA,oBAA8L;IAC9LzK,EAAA,CAAAqG,UAAA,KAAAqE,kEAAA,oBAA+J;IAEnK1K,EAAA,CAAAU,YAAA,EAAK;;;;;;IAlBLV,EAAA,CAAAW,UAAA,cAAAgK,OAAA,CAAAC,OAAA,CAAAZ,QAAA,CAAAa,EAAA,EAA8B;IAC1B7K,EAAA,CAAAsB,SAAA,GAAW;IAAXtB,EAAA,CAAAyB,iBAAA,CAAAqJ,KAAA,KAAW;IACX9K,EAAA,CAAAsB,SAAA,GAAmB;IAAnBtB,EAAA,CAAAyB,iBAAA,CAAAuI,QAAA,CAAAe,QAAA,CAAmB;IACnB/K,EAAA,CAAAsB,SAAA,GAA+B;IAA/BtB,EAAA,CAAAyB,iBAAA,CAAAkJ,OAAA,CAAApD,cAAA,CAAAyC,QAAA,CAAAhD,MAAA,EAA+B;IAC/BhH,EAAA,CAAAsB,SAAA,GAAiD;IAAjDtB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAAgL,WAAA,OAAAhB,QAAA,CAAAiB,WAAA,yBAAiD;IAEzCjL,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAW,UAAA,SAAAgK,OAAA,CAAA1E,WAAA,aAA6B;IAQ9BjG,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAW,UAAA,SAAAgK,OAAA,CAAA1E,WAAA,aAA6B;IACPjG,EAAA,CAAAsB,SAAA,GAAmG;IAAnGtB,EAAA,CAAAW,UAAA,SAAAgK,OAAA,CAAAC,OAAA,CAAAZ,QAAA,CAAAa,EAAA,EAAApE,QAAA,CAAA9B,OAAA,CAAA+B,KAAA,KAAAiE,OAAA,CAAAC,OAAA,CAAAZ,QAAA,CAAAa,EAAA,EAAApE,QAAA,CAAA9B,OAAA,CAAAgC,MAAA,kBAAAgE,OAAA,CAAAC,OAAA,CAAAZ,QAAA,CAAAa,EAAA,EAAApE,QAAA,CAAA9B,OAAA,CAAAgC,MAAA,CAAAC,QAAA,EAAmG;IACnG5G,EAAA,CAAAsB,SAAA,GAAyD;IAAzDtB,EAAA,CAAAW,UAAA,SAAAgK,OAAA,CAAAC,OAAA,CAAAZ,QAAA,CAAAa,EAAA,EAAApE,QAAA,CAAA9B,OAAA,CAAAgC,MAAA,kBAAAgE,OAAA,CAAAC,OAAA,CAAAZ,QAAA,CAAAa,EAAA,EAAApE,QAAA,CAAA9B,OAAA,CAAAgC,MAAA,CAAAuE,SAAA,CAAyD;;;;;;;;;;IA9B1GlL,EAAA,CAAAC,cAAA,cAA4I;IAExFD,EAAA,CAAAqB,MAAA,GAAoD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;IAC5GV,EAAA,CAAAC,cAAA,kBAAyF;IACrFD,EAAA,CAAAqG,UAAA,IAAA8E,yDAAA,2BAQc;IACdnL,EAAA,CAAAqG,UAAA,IAAA+E,yDAAA,4BAqBc;IAClBpL,EAAA,CAAAU,YAAA,EAAU;;;;IAjCsCV,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAyB,iBAAA,CAAA4J,OAAA,CAAAxK,WAAA,CAAAC,SAAA,0BAAoD;IACvEd,EAAA,CAAAsB,SAAA,GAAmB;IAAnBtB,EAAA,CAAAW,UAAA,UAAA0K,OAAA,CAAAC,SAAA,CAAmB,eAAAtL,EAAA,CAAAiD,eAAA,IAAAsI,GAAA;;;;;;IAkCpDvL,EAAA,CAAAC,cAAA,cAA0G;IACCD,EAAA,CAAAE,UAAA,mBAAAsL,sEAAA;MAAAxL,EAAA,CAAAI,aAAA,CAAAqL,IAAA;MAAA,MAAAC,OAAA,GAAA1L,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAkL,OAAA,CAAAC,mBAAA,GAA+B,KAAK;IAAA,EAAC;IAAC3L,EAAA,CAAAU,YAAA,EAAW;IACxJV,EAAA,CAAA0B,SAAA,mBAA0Q;IAC9Q1B,EAAA,CAAAU,YAAA,EAAM;;;;IAF6CV,EAAA,CAAAsB,SAAA,GAAuD;IAAvDtB,EAAA,CAAAW,UAAA,UAAAiL,OAAA,CAAA/K,WAAA,CAAAC,SAAA,yBAAuD;IACnDd,EAAA,CAAAsB,SAAA,GAAqJ;IAArJtB,EAAA,CAAAW,UAAA,aAAAiL,OAAA,CAAApF,aAAA,CAAAqF,OAAA,IAAAD,OAAA,CAAArJ,MAAA,CAAAyE,MAAA,YAAA4E,OAAA,CAAArJ,MAAA,CAAA6G,KAAA,CAAAQ,IAAA,YAAAgC,OAAA,CAAAN,SAAA,CAAAQ,MAAA,SAAAF,OAAA,CAAAG,WAAA,GAAqJ,UAAAH,OAAA,CAAA/K,WAAA,CAAAC,SAAA;;;;;;;;;;;;;;;;;;;ADvWxN,OAAM,MAAOkL,0BAA2B,SAAQpM,aAAa;EAuD3DqM,YACmCC,aAA4B,EAC3BC,cAA8B,EACtBC,sBAA8C,EAC9EC,WAAwB,EACxBC,QAAkB;IAC5B,KAAK,CAACA,QAAQ,CAAC;IALkB,KAAAJ,aAAa,GAAbA,aAAa;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACN,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtD,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAhBpB,KAAAzJ,oBAAoB,GAAW,EAAE;IAIjC,KAAA0J,SAAS,GAAS,EAAE;IAEpB,KAAAC,eAAe,GAAa,KAAK;IACjC,KAAA5B,OAAO,GAAS,EAAE;IAClB,KAAA6B,UAAU,GAAY,EAAE;IAExB,KAAAC,WAAW,GAAY,KAAK;IA4pBP,KAAA/M,SAAS,GAAGA,SAAS;EAppB1C;EAEAgN,QAAQA,CAAA;IACN,IAAIC,EAAE,GAAG,IAAI;IACbA,EAAE,CAACF,WAAW,GAAG,KAAK;IACtB,IAAI,CAACnE,QAAQ,GAAG,IAAI,CAACsE,cAAc,CAACtE,QAAQ;IAC5C,IAAI,CAACoD,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAAC1F,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAACwC,QAAQ,GAAG9I,SAAS,CAACmN,SAAS;IACnC,IAAI,CAACxB,SAAS,GAAG,EAAE;IACnB,IAAI,CAAC/I,MAAM,GAAG;MACZsI,EAAE,EAAE,IAAI;MACRlI,WAAW,EAAE,IAAI;MACjBY,YAAY,EAAE,IAAI;MAClBS,YAAY,EAAE,IAAI;MAClBW,OAAO,EAAE,IAAI;MACbU,IAAI,EAAE,IAAI;MACV+D,KAAK,EAAG,IAAI;MACZZ,IAAI,EAAE7I,SAAS,CAACoN,YAAY,CAACC,QAAQ;MACrCC,SAAS,EAAE,IAAI;MACfjG,MAAM,EAAE,IAAI;MACZG,SAAS,EAAE,IAAI;MACfpB,UAAU,EAAE,IAAI;MACd3E,YAAY,EAAE;KACjB;IACD,IAAI,CAAC8L,cAAc,GAAG,CACpB;MACEC,KAAK,EAAE,IAAI,CAACtM,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MACxDsM,KAAK,EAAE;KACR,CACF;IACD,IAAI,CAAClG,eAAe,GAAG;MACrB,CAAC,EAAE,CAAC;QACFiG,KAAK,EAAEP,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;QACzDsM,KAAK,EAAE;OACR,EACD;QACID,KAAK,EAAEP,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QACvDsM,KAAK,EAAE;OACV,CACA;MACD,CAAC,EAAE,CACD;QACED,KAAK,EAAEP,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;QAC3DsM,KAAK,EAAE;OACR,EACD;QACED,KAAK,EAAEP,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QACvDsM,KAAK,EAAE;OACR,CACF;MACD,CAAC,EAAE,CACD;QACED,KAAK,EAAEP,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACrDsM,KAAK,EAAE;OACR,EACD;QACED,KAAK,EAAEP,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QACvDsM,KAAK,EAAE;OACR;KAEJ;IACD,IAAI,CAACC,gBAAgB,GAAG,CACtB;MACEF,KAAK,EAAEP,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;MACpDsM,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAEP,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MACzDsM,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAEP,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC3DsM,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAEP,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACvDsM,KAAK,EAAE;KACR,EACD;MACED,KAAK,EAAEP,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;MACrDsM,KAAK,EAAE;KACR,CACF;IACD,IAAI,CAACjM,UAAU,GAAG;MAChBC,YAAY,EAAE,IAAI;MAClBkM,KAAK,EAAE,IAAI;MACXtJ,YAAY,EAAE,IAAI;MAClBT,YAAY,EAAE,IAAI;MAClBiF,IAAI,EAAE7I,SAAS,CAACoN,YAAY,CAACC,QAAQ;MACrChG,MAAM,EAAE;KACT;IACD,IAAI,CAACpF,OAAO,GAAG,CACb;MACE2L,IAAI,EAAE,IAAI,CAAC1M,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;MACzD0M,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI,CAACpF,QAAQ,CAACC,IAAI,IAAI7I,SAAS,CAACmN,SAAS,CAACc,KAAK;MACvDC,MAAM,EAAE;KACT,EACD;MACEN,IAAI,EAAE,IAAI,CAAC1M,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7D0M,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACVC,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAE;QACHC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;;KAErB,EAAE;MACDZ,IAAI,EAAE,IAAI,CAAC1M,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;MACtD0M,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACRC,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAE;QACHC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;;KAEvB,EAAE;MACDZ,IAAI,EAAE,IAAI,CAAC1M,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;MACtD0M,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE;KACT,EAAE;MACDN,IAAI,EAAE,IAAI,CAAC1M,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACxD0M,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZC,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAE;QACLC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;;KAEjB,EACD;MACEZ,IAAI,EAAE,IAAI,CAAC1M,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC5D0M,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZO,eAAeA,CAAChB,KAAK;QACnB,OAAOR,EAAE,CAACyB,WAAW,CAACC,mBAAmB,CAAC,IAAIC,IAAI,CAACnB,KAAK,CAAC,CAAC;MAC5D;KACD,EACD;MACEG,IAAI,EAAE,IAAI,CAAC1M,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC5D0M,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZO,eAAeA,CAAChB,KAAK;QACnB,OAAOA,KAAK,GAAGR,EAAE,CAACyB,WAAW,CAACC,mBAAmB,CAAC,IAAIC,IAAI,CAACnB,KAAK,CAAC,CAAC,GAAG,EAAE;MACzE;KACD,EACD;MACEG,IAAI,EAAE,IAAI,CAAC1M,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;MACzD0M,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE;KACT,EACD;MACEN,IAAI,EAAE,IAAI,CAAC1M,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MACvD0M,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZW,gBAAgB,EAAGpB,KAAK,IAAI;QAC1B,IAAIA,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACC,GAAG,EAAE;UACzC,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,CAAC;SAC5E,MAAM,IAAItB,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACE,QAAQ,EAAE;UACrD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,iBAAiB,EAAE,cAAc,EAAE,cAAc,CAAC;SAChF,MAAM,IAAIvB,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACG,WAAW,EAAE;UACxD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,CAAC;SAC9E,MAAM,IAAIxB,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACI,MAAM,EAAE;UACnD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,CAAC;SAC3E,MAAM,IAAIzB,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACK,IAAI,EAAE;UACjD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC;;QAE9E,OAAO,EAAE;MACX,CAAC;MACDV,eAAe,EAAE,SAAAA,CAAUhB,KAAK;QAC9B,IAAIA,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACC,GAAG,EAAE;UACzC,OAAO9B,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;SACrD,MAAM,IAAIsM,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACE,QAAQ,EAAE;UACrD,OAAO/B,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;SAC1D,MAAM,IAAIsM,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACG,WAAW,EAAE;UACxD,OAAOhC,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;SAC5D,MAAM,IAAIsM,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACI,MAAM,EAAE;UACnD,OAAOjC,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SACxD,MAAM,IAAIsM,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACK,IAAI,EAAE;UACjD,OAAOlC,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;;QAEvD,OAAO,EAAE;MACX;KACD,CACF;IAED,IAAI,CAACgB,WAAW,GAAG;MACjBiN,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE,KAAK;MAC1BC,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,mBAAmB;QACzBC,OAAO,EAAE,IAAI,CAACxO,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACzDwO,IAAI,EAAE,SAAAA,CAAUzE,EAAE,EAAE0E,IAAI;UACtB3C,EAAE,CAAC4C,mBAAmB,CAAC3E,EAAE,EAAE0E,IAAI,CAAC;QAClC;OACD,EACD;QACEH,IAAI,EAAE,uBAAuB;QAC7BC,OAAO,EAAE,IAAI,CAACxO,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACzDwO,IAAI,EAAE,SAAAA,CAAUzE,EAAE,EAAE0E,IAAI;UACtB3C,EAAE,CAAC6C,iBAAiB,CAAC5E,EAAE,EAAE0E,IAAI,CAAC;QAChC,CAAC;QACDG,UAAU,EAAG,SAAAA,CAAU7E,EAAE,EAAE0E,IAAI;UAC7B,IAAG3C,EAAE,CAACrE,QAAQ,CAACC,IAAI,IAAI7I,SAAS,CAACmN,SAAS,CAACc,KAAK,IAAIhB,EAAE,CAACrE,QAAQ,CAACC,IAAI,IAAI7I,SAAS,CAACmN,SAAS,CAAC6C,QAAQ,EAAE,OAAO,KAAK;UAClH,IAAG,CAACJ,IAAI,CAACK,SAAS,IAAI,CAACL,IAAI,CAACxJ,UAAU,IAAI6G,EAAE,CAACiD,WAAW,CAAC,CAAClQ,SAAS,CAACmQ,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI;UAC5G,IAAIpD,EAAE,CAACrE,QAAQ,CAACC,IAAI,IAAI7I,SAAS,CAACmN,SAAS,CAACpE,QAAQ,IAAI6G,IAAI,CAACK,SAAS,KAAKhD,EAAE,CAACrE,QAAQ,CAACsC,EAAE,IACpF+B,EAAE,CAACrE,QAAQ,CAACC,IAAI,IAAI7I,SAAS,CAACmN,SAAS,CAACpE,QAAQ,IAAI6G,IAAI,CAACxJ,UAAU,IAAI,IAAK,EAAG;YAClF,OAAO,KAAK;;UAEZ,IAAI6G,EAAE,CAACiD,WAAW,CAAC,CAAClQ,SAAS,CAACmQ,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,MACjE,OAAO,KAAK;QACrB;OACD;KAEJ;IACD,IAAI,CAACjO,UAAU,GAAG,CAAC;IACnB,IAAI,CAACG,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,IAAI,GAAG,kBAAkB;IAC9B,IAAI,CAACN,OAAO,GAAG;MACb8C,OAAO,EAAE,EAAE;MACXsL,KAAK,EAAE;KACR;IACD,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAAC7D,WAAW,CAAC8D,KAAK,CAAC,IAAI,CAAChP,UAAU,CAAC;IAC/D,IAAI,CAACqF,aAAa,GAAG,IAAI,CAAC6F,WAAW,CAAC8D,KAAK,CAAC,IAAI,CAAC5N,MAAM,CAAC;IACxD,IAAI,CAAC6N,eAAe,EAAE;IACtB,IAAI,CAACzH,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAAC3G,MAAM,CAAC,IAAI,CAACD,UAAU,EAAE,IAAI,CAACG,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAAChB,UAAU,CAAC;EACzE;EAEAoG,cAAcA,CAAC6F,KAAK;IAClB,IAAIR,EAAE,GAAG,IAAI;IACb;MACE,IAAIQ,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACC,GAAG,EAAE;QACzC,OAAO9B,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;OACrD,MAAM,IAAIsM,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACE,QAAQ,EAAE;QACrD,OAAO/B,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;OAC1D,MAAM,IAAIsM,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACG,WAAW,EAAE;QACxD,OAAOhC,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;OAC5D,MAAM,IAAIsM,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACI,MAAM,EAAE;QACnD,OAAOjC,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;OACxD,MAAM,IAAIsM,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACK,IAAI,EAAE;QACjD,OAAOlC,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;;MAEvD,OAAO,EAAE;;EAEb;EAEAuP,YAAYA,CAACjD,KAAK;IAChB,IAAIR,EAAE,GAAG,IAAI;IACb;IACA,OAAOA,EAAE,CAACyB,WAAW,CAACC,mBAAmB,CAAC,IAAIC,IAAI,CAACnB,KAAK,CAAC,CAAC;EAC5D;EAEApL,MAAMA,CAACsO,IAAI,EAAEC,KAAK,EAAEpO,IAAI,EAAEqO,MAAM;IAC9B,IAAI5D,EAAE,GAAG,IAAI;IACbA,EAAE,CAACF,WAAW,GAAG,KAAK;IACtB,IAAI,CAAC3K,UAAU,GAAGuO,IAAI;IACtB,IAAI,CAACpO,QAAQ,GAAGqO,KAAK;IACrB,IAAI,CAACpO,IAAI,GAAGA,IAAI;IAChB,IAAIsO,UAAU,GAAG;MACfH,IAAI;MACJ7C,IAAI,EAAE8C,KAAK;MACXpO;KACD;IACDuO,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxP,UAAU,CAAC,CAACyP,OAAO,CAACpD,GAAG,IAAG;MACzC,IAAI,IAAI,CAACrM,UAAU,CAACqM,GAAG,CAAC,IAAI,IAAI,EAAE;QAChCiD,UAAU,CAACjD,GAAG,CAAC,GAAG,IAAI,CAACrM,UAAU,CAACqM,GAAG,CAAC;;IAE1C,CAAC,CAAC;IACF,IAAI,CAAC3L,OAAO,GAAG;MACb8C,OAAO,EAAE,EAAE;MACXsL,KAAK,EAAE;KACR;IACDrD,EAAE,CAACiE,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI,CAAC5E,aAAa,CAAC6E,YAAY,CAACN,UAAU,EAAGO,QAAQ,IAAI;MACvDpE,EAAE,CAAC/K,OAAO,GAAG;QACX8C,OAAO,EAAEqM,QAAQ,CAACrM,OAAO;QACzBsL,KAAK,EAAEe,QAAQ,CAACC;OACjB;MACD,IAAIrE,EAAE,CAACrE,QAAQ,CAACC,IAAI,IAAI7I,SAAS,CAACmN,SAAS,CAACpE,QAAQ,IAC1CkE,EAAE,CAACrE,QAAQ,CAACC,IAAI,IAAI7I,SAAS,CAACmN,SAAS,CAACoE,QAAQ,EAAE;QAClD,IAAIC,cAAc,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC1E,EAAE,CAAC/K,OAAO,CAAC8C,OAAO,CAAC4M,MAAM,CAAChC,IAAI,IAAIA,IAAI,CAACxJ,UAAU,KAAK,IAAI,CAAC,CAC9FyL,GAAG,CAACjC,IAAI,IAAIA,IAAI,CAACxJ,UAAoB,CAAC,CAAC,CAAC;QAE7C6G,EAAE,CAAC/K,OAAO,CAAC8C,OAAO,CAACiM,OAAO,CAACrB,IAAI,IAAG;UAC9B,IAAIA,IAAI,CAACkC,QAAQ,KAAK,IAAI,EAAE;YACxBN,cAAc,CAACO,IAAI,CAACnC,IAAI,CAACkC,QAAkB,CAAC;;QAEpD,CAAC,CAAC;QAEF,MAAME,iBAAiB,GAAGP,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACH,cAAc,CAAC,CAAC;QAE7DvE,EAAE,CAACT,cAAc,CAACyF,uBAAuB,CAACD,iBAAiB,EAAGX,QAAQ,IAAI;UACtEpE,EAAE,CAACjE,oBAAoB,GAAGqI,QAAQ;UAClC,IAAI,CAAClP,WAAW,GAAG;YACjCiN,gBAAgB,EAAE,KAAK;YACvBC,aAAa,EAAE,KAAK;YACpBC,YAAY,EAAE,IAAI;YAClBC,mBAAmB,EAAE,KAAK;YAC1BC,MAAM,EAAE,CACN;cACEC,IAAI,EAAE,mBAAmB;cACzBC,OAAO,EAAE,IAAI,CAACxO,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;cACzDwO,IAAI,EAAE,SAAAA,CAAUzE,EAAE,EAAE0E,IAAI;gBACtB3C,EAAE,CAAC4C,mBAAmB,CAAC3E,EAAE,EAAE0E,IAAI,CAAC;cAClC;aACD,EACD;cACEH,IAAI,EAAE,uBAAuB;cAC7BC,OAAO,EAAE,IAAI,CAACxO,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;cACzDwO,IAAI,EAAE,SAAAA,CAAUzE,EAAE,EAAE0E,IAAI;gBACtB3C,EAAE,CAAC6C,iBAAiB,CAAC5E,EAAE,EAAE0E,IAAI,CAAC;cAChC,CAAC;cACDG,UAAU,EAAG,SAAAA,CAAU7E,EAAE,EAAE0E,IAAI;gBAC7B,IAAG3C,EAAE,CAACrE,QAAQ,CAACC,IAAI,IAAI7I,SAAS,CAACmN,SAAS,CAACc,KAAK,IAAIhB,EAAE,CAACrE,QAAQ,CAACC,IAAI,IAAI7I,SAAS,CAACmN,SAAS,CAAC6C,QAAQ,EAAE,OAAO,KAAK;gBAChH,IAAI/C,EAAE,CAACrE,QAAQ,CAACC,IAAI,IAAI7I,SAAS,CAACmN,SAAS,CAACpE,QAAQ,IAAIkE,EAAE,CAACiD,WAAW,CAAC,CAAClQ,SAAS,CAACmQ,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,KAAMpD,EAAE,CAACjE,oBAAoB,KAAKkJ,SAAS,IAAIjF,EAAE,CAACjE,oBAAoB,IAAI,IAAI,CAAE,EAAE,OAAO,IAAI;gBAC5M,IAAKiE,EAAE,CAACrE,QAAQ,CAACC,IAAI,IAAI7I,SAAS,CAACmN,SAAS,CAACpE,QAAQ,IAAIkE,EAAE,CAACiD,WAAW,CAAC,CAAClQ,SAAS,CAACmQ,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,IAAOT,IAAI,CAACxJ,UAAU,IAAI,IAAI,IAAI6G,EAAE,CAACjE,oBAAoB,CAACC,QAAQ,CAAC2G,IAAI,CAACxJ,UAAU,CAAG,EAAE,OAAO,KAAK;gBACjN,IAAK6G,EAAE,CAACrE,QAAQ,CAACC,IAAI,IAAI7I,SAAS,CAACmN,SAAS,CAACpE,QAAQ,IAAIkE,EAAE,CAACiD,WAAW,CAAC,CAAClQ,SAAS,CAACmQ,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,IAAOT,IAAI,CAACxJ,UAAU,IAAI,IAAI,IAAIwJ,IAAI,CAACK,SAAS,IAAI,IAAI,IAAIhD,EAAE,CAACjE,oBAAoB,CAACC,QAAQ,CAAC2G,IAAI,CAACK,SAAS,CAAC,IAAIL,IAAI,CAACK,SAAS,IAAIhD,EAAE,CAACrE,QAAQ,CAACsC,EAAI,EAAE,OAAO,KAAK;gBAC9Q,IAAG,CAAC0E,IAAI,CAACK,SAAS,IAAI,CAACL,IAAI,CAACxJ,UAAU,IAAI6G,EAAE,CAACiD,WAAW,CAAC,CAAClQ,SAAS,CAACmQ,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI;gBAC5G,IAAKpD,EAAE,CAACrE,QAAQ,CAACC,IAAI,IAAI7I,SAAS,CAACmN,SAAS,CAACpE,QAAQ,IAAIkE,EAAE,CAACiD,WAAW,CAAC,CAAClQ,SAAS,CAACmQ,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,KAAOT,IAAI,CAACxJ,UAAU,IAAI,IAAI,IAAI,CAAC6G,EAAE,CAACjE,oBAAoB,CAACC,QAAQ,CAAC2G,IAAI,CAACxJ,UAAU,CAAC,IAAMwJ,IAAI,CAACK,SAAS,IAAI,IAAI,IAAI,CAAChD,EAAE,CAACjE,oBAAoB,CAACC,QAAQ,CAAC2G,IAAI,CAACK,SAAS,CAAE,CAAC,EAAE,OAAO,IAAI;gBAClS,IAAIhD,EAAE,CAACrE,QAAQ,CAACC,IAAI,IAAI7I,SAAS,CAACmN,SAAS,CAACpE,QAAQ,IAAI6G,IAAI,CAACK,SAAS,KAAKhD,EAAE,CAACrE,QAAQ,CAACsC,EAAE,IACpF+B,EAAE,CAACrE,QAAQ,CAACC,IAAI,IAAI7I,SAAS,CAACmN,SAAS,CAACpE,QAAQ,IAAI6G,IAAI,CAACxJ,UAAU,IAAI,IAAK,EAAG;kBAClF,OAAO,KAAK;;gBAEZ,IAAI6G,EAAE,CAACiD,WAAW,CAAC,CAAClQ,SAAS,CAACmQ,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,MACjE,OAAO,KAAK;cACrB;aACD;WAEJ;UACDpD,EAAE,CAACF,WAAW,GAAG,IAAI;QACT,CAAC,CAAC;;IAEd,CAAC,EAAE,IAAI,EAAE,MAAK;MACZE,EAAE,CAACiE,oBAAoB,CAACiB,OAAO,EAAE;IACnC,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACxP,MAAM,GAAG;MACZsI,EAAE,EAAE,IAAI;MACRlI,WAAW,EAAE,IAAI;MACjBY,YAAY,EAAE,IAAI;MAClBS,YAAY,EAAE,IAAI;MAClBW,OAAO,EAAE,IAAI;MACbU,IAAI,EAAE,IAAI;MACV+D,KAAK,EAAG,IAAI;MACZZ,IAAI,EAAE7I,SAAS,CAACoN,YAAY,CAACC,QAAQ;MACrCC,SAAS,EAAE,IAAI;MACfjG,MAAM,EAAE,IAAI;MACZG,SAAS,EAAE,IAAI;MACfpB,UAAU,EAAE,IAAI;MACd3E,YAAY,EAAE;KACjB;EACH;EAEA4Q,cAAcA,CAAA;IACZ,IAAI,CAACjQ,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,MAAM,CAAC,IAAI,CAACD,UAAU,EAAE,IAAI,CAACG,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAAChB,UAAU,CAAC;EACzE;EAEAiP,eAAeA,CAAA;IACb,IAAI,CAACjE,cAAc,CAACiE,eAAe,CAAEY,QAAQ,IAAI;MAC/C,IAAI,CAACxP,YAAY,GAAGwP,QAAQ,CAACQ,GAAG,CAACS,EAAE,IAAG;QACpC,OAAO;UACL,GAAGA,EAAE;UACLjE,OAAO,EAAE,GAAGiE,EAAE,CAACC,IAAI,MAAMD,EAAE,CAAC1E,IAAI;SACjC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EACEjL,eAAeA,CAAClB,YAAY;IACxB,MAAM+Q,QAAQ,GAAG,IAAI,CAAC3Q,YAAY,CAAC4Q,IAAI,CAACH,EAAE,IAAIA,EAAE,CAACC,IAAI,KAAK9Q,YAAY,CAAC;IACvE,OAAO+Q,QAAQ,GAAGA,QAAQ,CAACD,IAAI,GAAG,KAAK,GAAGC,QAAQ,CAAC5E,IAAI,GAAG,EAAE;EAChE;EAEF;EACA8E,qBAAqBA,CAAA;IACnB,IAAG,IAAI,CAACxB,oBAAoB,CAACyB,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC3G,mBAAmB,IAAI,KAAK,EAAE;IACrF,IAAIiB,EAAE,GAAG,IAAI;IACb,IAAI,CAACiE,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAI,IAAI,CAAC7K,WAAW,IAAI,QAAQ,EAAE;MAChC,IAAIsM,QAAQ,GAAG;QACb5P,WAAW,EAAE,IAAI,CAACJ,MAAM,CAACI,WAAW;QACpCY,YAAY,EAAE,IAAI,CAAChB,MAAM,CAACgB,YAAY;QACtCS,YAAY,EAAE,IAAI,CAACzB,MAAM,CAACyB,YAAY;QACtCW,OAAO,EAAE,IAAI,CAACpC,MAAM,CAACoC,OAAO;QAC5BU,IAAI,EAAE,IAAI,CAAC9C,MAAM,CAAC8C,IAAI;QACtBmD,IAAI,EAAE,IAAI,CAACjG,MAAM,CAACiG,IAAI;QACtByE,SAAS,EAAE,IAAI,CAAC1K,MAAM,CAACiG,IAAI,IAAI,CAAC,GAAG,IAAI,CAACjG,MAAM,CAAC0K,SAAS,GAAG;OAC5D;MACD,IAAIsF,QAAQ,CAACvO,YAAY,IAAI,IAAI,EAAC;QAChC,IAAGuO,QAAQ,CAACvO,YAAY,CAACwO,UAAU,CAAC,GAAG,CAAC,EAAC;UACvCD,QAAQ,CAACvO,YAAY,GAAG,IAAI,GAACuO,QAAQ,CAACvO,YAAY,CAACyO,SAAS,CAAC,CAAC,EAAEF,QAAQ,CAACvO,YAAY,CAAC8H,MAAM,CAAC;SAC9F,MAAK,IAAGyG,QAAQ,CAACvO,YAAY,CAAC8H,MAAM,IAAI,CAAC,IAAIyG,QAAQ,CAACvO,YAAY,CAAC8H,MAAM,IAAI,EAAE,EAAC;UAC/EyG,QAAQ,CAACvO,YAAY,GAAG,IAAI,GAACuO,QAAQ,CAACvO,YAAY;;;MAGtD,IAAI,CAACkI,aAAa,CAACwG,YAAY,CAACH,QAAQ,EAAGI,IAAI,IAAI;QACjD/F,EAAE,CAACiE,oBAAoB,CAAC+B,OAAO,CAAChG,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QACvF8L,EAAE,CAACjB,mBAAmB,GAAG,KAAK;QAC9BiB,EAAE,CAAC5K,MAAM,CAAC4K,EAAE,CAAC7K,UAAU,EAAE6K,EAAE,CAAC1K,QAAQ,EAAE0K,EAAE,CAACzK,IAAI,EAAEyK,EAAE,CAACzL,UAAU,CAAC;QAC7D;QACA;QACAyL,EAAE,CAACV,aAAa,CAAC2G,qBAAqB,CAACjG,EAAE,CAACrE,QAAQ,CAACnH,YAAY,EAAG0R,KAAK,IAAI;UACzE,IAAIC,KAAK,GAAG,EAAE;UACd,KAAK,IAAIC,IAAI,IAAIF,KAAK,CAACG,UAAU,EAAE;YACjCF,KAAK,CAACrB,IAAI,CAAC;cACTwB,MAAM,EAAEF,IAAI,CAACE,MAAM;cACnBC,QAAQ,EAAER,IAAI,CAAC9H;aAChB,CAAC;;UAEJ,IAAG8H,IAAI,EAAE5M,UAAU,EAAE;YACnBgN,KAAK,CAACrB,IAAI,CAAC;cACTwB,MAAM,EAAEP,IAAI,CAAC5M,UAAU;cACvBoN,QAAQ,EAAER,IAAI,CAAC9H;aAChB,CAAC;;UAEJ+B,EAAE,CAACV,aAAa,CAACkH,cAAc,CAACL,KAAK,CAAC;QACxC,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,EAAE,MAAK;QACZnG,EAAE,CAACiE,oBAAoB,CAACiB,OAAO,EAAE;MACnC,CAAC,CAAC;KACH,MAAM,IAAI,IAAI,CAAC7L,WAAW,IAAI,QAAQ,EAAE;MACvC,IAAIsM,QAAQ,GAAG;QACb5P,WAAW,EAAE,IAAI,CAACJ,MAAM,CAACI,WAAW;QACpCY,YAAY,EAAE,IAAI,CAAChB,MAAM,CAACgB,YAAY;QACtCS,YAAY,EAAE,IAAI,CAACzB,MAAM,CAACyB,YAAY;QACtCW,OAAO,EAAE,IAAI,CAACpC,MAAM,CAACoC,OAAO;QAC5BU,IAAI,EAAE,IAAI,CAAC9C,MAAM,CAAC8C,IAAI;QACtBmD,IAAI,EAAE,IAAI,CAACjG,MAAM,CAACiG,IAAI;QACtByE,SAAS,EAAE,IAAI,CAAC1K,MAAM,CAACiG,IAAI,IAAI,CAAC,GAAG,IAAI,CAACjG,MAAM,CAAC0K,SAAS,GAAG,IAAI;QAC/DjG,MAAM,EAAE,IAAI,CAACzE,MAAM,CAACyE,MAAM;QAC1BoC,KAAK,EAAG,IAAI,CAAC7G,MAAM,CAAC6G,KAAK;QACzBrD,UAAU,EAAE,IAAI,CAACxD,MAAM,CAACwD,UAAU;QAClCsN,OAAO,EAAG,IAAI,CAAC/H;OAChB;MACD,IAAIiH,QAAQ,CAACvO,YAAY,IAAI,IAAI,EAAC;QAChC,IAAGuO,QAAQ,CAACvO,YAAY,CAACwO,UAAU,CAAC,GAAG,CAAC,EAAC;UACvCD,QAAQ,CAACvO,YAAY,GAAG,IAAI,GAACuO,QAAQ,CAACvO,YAAY,CAACyO,SAAS,CAAC,CAAC,EAAEF,QAAQ,CAACvO,YAAY,CAAC8H,MAAM,CAAC;SAC9F,MAAK,IAAGyG,QAAQ,CAACvO,YAAY,CAAC8H,MAAM,IAAI,CAAC,IAAIyG,QAAQ,CAACvO,YAAY,CAAC8H,MAAM,IAAI,EAAE,EAAC;UAC/EyG,QAAQ,CAACvO,YAAY,GAAG,IAAI,GAACuO,QAAQ,CAACvO,YAAY;;;MAGtD;MACA,IAAI,CAACkI,aAAa,CAACoH,YAAY,CAAC,IAAI,CAAC/Q,MAAM,CAACsI,EAAE,EAAE0H,QAAQ,EAAGI,IAAI,IAAI;QACjE/F,EAAE,CAACjB,mBAAmB,GAAG,KAAK;QAC9BiB,EAAE,CAACiE,oBAAoB,CAAC+B,OAAO,CAAChG,EAAE,CAAC/L,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QACvF8L,EAAE,CAAC5K,MAAM,CAAC4K,EAAE,CAAC7K,UAAU,EAAE6K,EAAE,CAAC1K,QAAQ,EAAE0K,EAAE,CAACzK,IAAI,EAAEyK,EAAE,CAACzL,UAAU,CAAC;QAC7D,IAAGwR,IAAI,CAAC5M,UAAU,IAAI,IAAI,IAAI4M,IAAI,CAAC5M,UAAU,IAAI8L,SAAS,EAAE;UAC1DjF,EAAE,CAACV,aAAa,CAACkH,cAAc,CAAC,CAAC;YAC/BF,MAAM,EAAEP,IAAI,CAAC5M,UAAU;YACvBoN,QAAQ,EAAER,IAAI,CAAC9H;WAChB,CAAC,CAAC;;MAEP,CAAC,EAAE,IAAI,EAAE,MAAK;QACZ+B,EAAE,CAACiE,oBAAoB,CAACiB,OAAO,EAAE;MACnC,CAAC,CAAC;;EAEN;EAEArR,eAAeA,CAAA;IACb,IAAI,CAACkL,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC1F,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAACwG,UAAU,GAAG,IAAI,CAAC5L,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;IAC1E,IAAI,CAACiR,WAAW,EAAE;IAClB;IACA,IAAI,IAAI,CAACxJ,QAAQ,CAACC,IAAI,KAAK7I,SAAS,CAACmN,SAAS,CAAC6C,QAAQ,EAAE;MACvD,IAAI,CAACpN,MAAM,CAACI,WAAW,GAAG,IAAI,CAAC4F,QAAQ,CAACgL,QAAQ,CAACd,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC5P,oBAAoB,CAAC;MACxF,IAAI,CAACN,MAAM,CAACyB,YAAY,GAAG,IAAI,CAACuE,QAAQ,CAACiL,KAAK;MAC9C,IAAI,CAACjR,MAAM,CAACgB,YAAY,GAAG,IAAI,CAACgF,QAAQ,CAAC+E,KAAK;;IAEhD,IAAI,CAAC9G,aAAa,GAAG,IAAI,CAAC6F,WAAW,CAAC8D,KAAK,CAAC,IAAI,CAAC5N,MAAM,CAAC;EAC1D;EAEAkN,iBAAiBA,CAAC5E,EAAE,EAAE0E,IAAI;IACxB,IAAI3C,EAAE,GAAG,IAAI;IACb,IAAI,CAACpG,aAAa,CAACiN,KAAK,EAAE;IAC1B,IAAI,CAACxN,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAACwG,UAAU,GAAG,IAAI,CAAC5L,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;IAC1E,IAAI,CAAC6K,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACO,aAAa,CAACwH,eAAe,CAACnE,IAAI,CAAC1E,EAAE,EAAG8H,IAAI,IAAI;MACnD/F,EAAE,CAACrK,MAAM,GAAG;QACVsI,EAAE,EAAE8H,IAAI,CAAC9H,EAAE;QACXlI,WAAW,EAAEgQ,IAAI,CAAChQ,WAAW;QAC7BY,YAAY,EAAEoP,IAAI,CAACpP,YAAY;QAC/BS,YAAY,EAAE2O,IAAI,CAAC3O,YAAY;QAC/BW,OAAO,EAAEgO,IAAI,CAAChO,OAAO;QACrBU,IAAI,EAAEsN,IAAI,CAACtN,IAAI;QACf+D,KAAK,EAAGuJ,IAAI,CAACvJ,KAAK;QAClBZ,IAAI,EAAEmK,IAAI,CAACnK,IAAI;QACfyE,SAAS,EAAE0F,IAAI,CAAC1F,SAAS;QACzBjG,MAAM,EAAE,IAAI;QACZG,SAAS,EAAEwL,IAAI,CAAC3L,MAAM;QACtBjB,UAAU,EAAE4M,IAAI,CAAC5M,UAAU;QACzB3E,YAAY,EAAEuR,IAAI,CAACvR;OACtB;MACDwL,EAAE,CAACL,SAAS,GAAG;QAAC,GAAGK,EAAE,CAACrK;MAAM,CAAC;MAC7BqK,EAAE,CAACpG,aAAa,GAAGoG,EAAE,CAACP,WAAW,CAAC8D,KAAK,CAACvD,EAAE,CAACrK,MAAM,CAAC;MAClD;MACA,IAAI,CAAC6J,sBAAsB,CAACpK,MAAM,CAAC;QAACmR,QAAQ,EAAE5D,IAAI,CAAC1E;MAAE,CAAC,EAAG8I,GAAG,IAAG;QAC7DC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAChP,OAAO,CAAC;QACxB,IAAI,CAAC2G,SAAS,GAAGqI,GAAG,CAAChP,OAAO;QAC5B;QACA;QACA;QACE,IAAI,CAAC2G,SAAS,CAACsF,OAAO,CAACvL,IAAI,IAAG;UAC1B,IAAI,CAACuF,OAAO,CAACvF,IAAI,CAACwF,EAAE,CAAC,GAAG,IAAI,CAACwB,WAAW,CAAC8D,KAAK,CAAC;YAC3CxL,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC7E,UAAU,CAAC8G,QAAQ,EAAE9G,UAAU,CAACoL,SAAS,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC4I,qBAAqB,EAAE,CAAC;WAC/F,CAAC;QACN,CAAC,CAAC;QACJlH,EAAE,CAACJ,eAAe,GAAG,IAAI;MAC3B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACN,aAAa,CAAC2G,qBAAqB,CAACjG,EAAE,CAACrE,QAAQ,CAACnH,YAAY,EAAGuR,IAAI,IAAI;MAC1E/F,EAAE,CAACmH,SAAS,GAAGpB,IAAI,CAACM,UAAU;IAChC,CAAC,CAAC;EACJ;EAEAzD,mBAAmBA,CAAC3E,EAAE,EAAE0E,IAAI;IAC1B,IAAI3C,EAAE,GAAG,IAAI;IACb,IAAI,CAACpG,aAAa,CAACiN,KAAK,EAAE;IAC1B,IAAI,CAAChH,UAAU,GAAG,IAAI,CAAC5L,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC;IAC9E,IAAI,CAACmF,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAAC0F,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACO,aAAa,CAACwH,eAAe,CAACnE,IAAI,CAAC1E,EAAE,EAAG8H,IAAI,IAAI;MACnD/F,EAAE,CAACrK,MAAM,GAAG;QACVsI,EAAE,EAAE8H,IAAI,CAAC9H,EAAE;QACXlI,WAAW,EAAEgQ,IAAI,CAAChQ,WAAW;QAC7BY,YAAY,EAAEoP,IAAI,CAACpP,YAAY;QAC/BS,YAAY,EAAE2O,IAAI,CAAC3O,YAAY;QAC/BW,OAAO,EAAEgO,IAAI,CAAChO,OAAO;QACrBU,IAAI,EAAEsN,IAAI,CAACtN,IAAI;QACf+D,KAAK,EAAEuJ,IAAI,CAACvJ,KAAK;QACjBZ,IAAI,EAAEmK,IAAI,CAACnK,IAAI;QACfyE,SAAS,EAAE0F,IAAI,CAAC1F,SAAS;QACzBjG,MAAM,EAAE,IAAI;QACZG,SAAS,EAAEwL,IAAI,CAAC3L,MAAM;QACtBjB,UAAU,EAAE4M,IAAI,CAAC5M,UAAU;QACzB3E,YAAY,EAAEuR,IAAI,CAACvR;OACtB;MACDwL,EAAE,CAACL,SAAS,GAAG;QAAC,GAAGK,EAAE,CAACrK;MAAM,CAAC;MAC7BqK,EAAE,CAACpG,aAAa,GAAGoG,EAAE,CAACP,WAAW,CAAC8D,KAAK,CAACvD,EAAE,CAACrK,MAAM,CAAC;MAClD;MACA,IAAI,CAAC6J,sBAAsB,CAACpK,MAAM,CAAC;QAACmR,QAAQ,EAAE5D,IAAI,CAAC1E;MAAE,CAAC,EAAG8I,GAAG,IAAG;QAC7DC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAChP,OAAO,CAAC;QACxB,IAAI,CAAC2G,SAAS,GAAGqI,GAAG,CAAChP,OAAO;QAC5B,KAAI,IAAIU,IAAI,IAAI,IAAI,CAACiG,SAAS,EAAE;UAC9B,IAAI,CAACV,OAAO,CAACvF,IAAI,CAACwF,EAAE,CAAC,GAAG,IAAI,CAACwB,WAAW,CAAC8D,KAAK,CAAC9K,IAAI,CAAC;;QAEtDuH,EAAE,CAACJ,eAAe,GAAG,IAAI;MAC3B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACN,aAAa,CAAC2G,qBAAqB,CAACjG,EAAE,CAACrE,QAAQ,CAACnH,YAAY,EAAGuR,IAAI,IAAI;MAC1E/F,EAAE,CAACmH,SAAS,GAAGpB,IAAI,CAACM,UAAU;IAChC,CAAC,CAAC;EACJ;EAEA9O,gBAAgBA,CAAC6P,KAAK;IACpB,IAAGA,KAAK,CAACC,OAAO,EAAC;MACf;;IAEF,IAAGD,KAAK,CAACE,OAAO,IAAI,CAAC,IAAIF,KAAK,CAACE,OAAO,IAAI,EAAE,IAAIF,KAAK,CAACE,OAAO,IAAI,EAAE,IAAIF,KAAK,CAACE,OAAO,IAAI,EAAE,EAAC;MACzF;;IAEF,IAAGF,KAAK,CAACE,OAAO,GAAG,EAAE,IAAIF,KAAK,CAACE,OAAO,GAAG,EAAE,EAAC;MAC1CF,KAAK,CAACG,cAAc,EAAE;;IAEtB;IACA,IAAIH,KAAK,CAACE,OAAO,IAAI,EAAE,IAAIF,KAAK,CAACE,OAAO,IAAI,GAAG,IAAIF,KAAK,CAACE,OAAO,IAAI,GAAG,IAAIF,KAAK,CAACE,OAAO,IAAI,GAAG,EAAE;MAC7FF,KAAK,CAACG,cAAc,EAAE;;EAE9B;EACEpI,WAAWA,CAAA;IACP,OAAO2E,MAAM,CAAC0D,MAAM,CAAC,IAAI,CAACxJ,OAAO,CAAC,CAACyJ,KAAK,CAAEC,SAAoB,IAAKA,SAAS,CAACC,KAAK,CAAC;EACvF;EACAT,qBAAqBA,CAAA;IACjB,OAAQU,OAAwB,IAA6B;MACzD,MAAMC,YAAY,GAAG,CAACD,OAAO,CAACpH,KAAK,IAAI,EAAE,EAAExD,IAAI,EAAE,CAACkC,MAAM,KAAK,CAAC;MAC9D,MAAM4I,OAAO,GAAG,CAACD,YAAY;MAC7B,OAAOC,OAAO,GAAG,IAAI,GAAG;QAACC,UAAU,EAAE;MAAI,CAAC;IAC9C,CAAC;EACL;EACAnP,aAAaA,CAACwO,KAAK;IACf,IAAIA,KAAK,CAACxG,GAAG,KAAK,GAAG,KAAK,IAAI,CAACjL,MAAM,CAAC8C,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC9C,MAAM,CAAC8C,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC9C,MAAM,CAAC8C,IAAI,CAACuE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MAC/GoK,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAI,IAAI,CAAC5R,MAAM,CAAC8C,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC9C,MAAM,CAAC8C,IAAI,CAACuE,IAAI,EAAE,IAAI,EAAE,EAAE;MAC3D,IAAI,CAACrH,MAAM,CAAC8C,IAAI,GAAG,IAAI,CAAC9C,MAAM,CAAC8C,IAAI,CAACuP,SAAS,EAAE,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MACvE;;EAER;EACA/P,gBAAgBA,CAACkP,KAAK;IAClB,IAAIA,KAAK,CAACxG,GAAG,KAAK,GAAG,KAAK,IAAI,CAACjL,MAAM,CAACoC,OAAO,IAAI,IAAI,IAAI,IAAI,CAACpC,MAAM,CAACoC,OAAO,IAAI,IAAI,IAAI,IAAI,CAACpC,MAAM,CAACoC,OAAO,CAACiF,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MACxHoK,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAI,IAAI,CAAC5R,MAAM,CAACoC,OAAO,IAAI,IAAI,IAAI,IAAI,CAACpC,MAAM,CAACoC,OAAO,CAACiF,IAAI,EAAE,IAAI,EAAE,EAAE;MACjE,IAAI,CAACrH,MAAM,CAACoC,OAAO,GAAG,IAAI,CAACpC,MAAM,CAACoC,OAAO,CAACiQ,SAAS,EAAE,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MAC7E;;EAER;EACAzK,oBAAoBA,CAAC4J,KAAoB,EAAE3O,IAAS;IAChD,IAAI2O,KAAK,CAACxG,GAAG,KAAK,GAAG,KAAK,CAACnI,IAAI,CAACV,OAAO,IAAIU,IAAI,CAACV,OAAO,CAACiF,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MACpEoK,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAI9O,IAAI,CAACV,OAAO,IAAIU,IAAI,CAACV,OAAO,CAACiF,IAAI,EAAE,KAAK,EAAE,EAAE;MAC5CvE,IAAI,CAACV,OAAO,GAAGU,IAAI,CAACV,OAAO,CAACiQ,SAAS,EAAE,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MAC/D;;EAER;EACAtL,cAAcA,CAACyK,KAAK;IAChB,IAAIA,KAAK,CAACxG,GAAG,KAAK,GAAG,KAAK,IAAI,CAACjL,MAAM,CAAC6G,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC7G,MAAM,CAAC6G,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC7G,MAAM,CAAC6G,KAAK,CAACQ,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MAClHoK,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAI,IAAI,CAAC5R,MAAM,CAAC6G,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC7G,MAAM,CAAC6G,KAAK,CAACQ,IAAI,EAAE,IAAI,EAAE,EAAE;MAC7D,IAAI,CAACrH,MAAM,CAAC6G,KAAK,GAAG,IAAI,CAAC7G,MAAM,CAAC6G,KAAK,CAACwL,SAAS,EAAE,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MACzE;;EAER;;;uBAhtBS7I,0BAA0B,EAAAhM,EAAA,CAAA8U,iBAAA,CAwDzBpV,aAAa,GAAAM,EAAA,CAAA8U,iBAAA,CACbjV,cAAc,GAAAG,EAAA,CAAA8U,iBAAA,CACd/U,sBAAsB,GAAAC,EAAA,CAAA8U,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhV,EAAA,CAAA8U,iBAAA,CAAA9U,EAAA,CAAAiV,QAAA;IAAA;EAAA;;;YA1DvBjJ,0BAA0B;MAAAkJ,SAAA;MAAAC,QAAA,GAAAnV,EAAA,CAAAoV,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdvC1V,EAAA,CAAAC,cAAA,aAAqG;UAEzDD,EAAA,CAAAqB,MAAA,GAAgD;UAAArB,EAAA,CAAAU,YAAA,EAAM;UAC1FV,EAAA,CAAA0B,SAAA,sBAAoF;UACxF1B,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,aAAwE;UACpED,EAAA,CAAAqG,UAAA,IAAAuP,8CAAA,sBAIW;UACf5V,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,cAAoG;UAA/DD,EAAA,CAAAE,UAAA,sBAAA2V,6DAAA;YAAA,OAAYF,GAAA,CAAA3D,cAAA,EAAgB;UAAA,EAAC;UAC9DhS,EAAA,CAAAC,cAAA,iBAAoF;UAG5ED,EAAA,CAAAqG,UAAA,KAAAyP,0CAAA,iBAcM;UAEN9V,EAAA,CAAAC,cAAA,eAAmB;UAKCD,EAAA,CAAAE,UAAA,2BAAA6V,yEAAA/U,MAAA;YAAA,OAAA2U,GAAA,CAAAxU,UAAA,CAAA6F,MAAA,GAAAhG,MAAA;UAAA,EAA+B;UAQ1ChB,EAAA,CAAAU,YAAA,EAAa;UACdV,EAAA,CAAAC,cAAA,iBAAwB;UAAAD,EAAA,CAAAqB,MAAA,IAAgD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAIxFV,EAAA,CAAAC,cAAA,eAAmB;UAIJD,EAAA,CAAAE,UAAA,2BAAA8V,oEAAAhV,MAAA;YAAA,OAAA2U,GAAA,CAAAxU,UAAA,CAAAoC,YAAA,GAAAvC,MAAA;UAAA,EAAqC;UAF5ChB,EAAA,CAAAU,YAAA,EAIE;UACFV,EAAA,CAAAC,cAAA,iBAA8B;UAAAD,EAAA,CAAAqB,MAAA,IAA+C;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAI7FV,EAAA,CAAAC,cAAA,eAAmB;UAIJD,EAAA,CAAAE,UAAA,2BAAA+V,oEAAAjV,MAAA;YAAA,OAAA2U,GAAA,CAAAxU,UAAA,CAAA6C,YAAA,GAAAhD,MAAA;UAAA,EAAqC,qBAAAkV,8DAAAlV,MAAA;YAAA,OAG1B2U,GAAA,CAAAxR,gBAAA,CAAAnD,MAAA,CAAwB;UAAA,EAHE;UAF5ChB,EAAA,CAAAU,YAAA,EAOE;UACFV,EAAA,CAAAC,cAAA,iBAA8B;UAAAD,EAAA,CAAAqB,MAAA,IAA+C;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAG7FV,EAAA,CAAAC,cAAA,eAAwB;UACpBD,EAAA,CAAA0B,SAAA,oBAGY;UAChB1B,EAAA,CAAAU,YAAA,EAAM;UAKlBV,EAAA,CAAAqG,UAAA,KAAA8P,iDAAA,0BAYc;UACdnW,EAAA,CAAAqG,UAAA,KAAA+P,iDAAA,0BAYc;UAEdpW,EAAA,CAAAC,cAAA,eAAqD;UAEvCD,EAAA,CAAAE,UAAA,2BAAAmW,uEAAArV,MAAA;YAAA,OAAA2U,GAAA,CAAAhK,mBAAA,GAAA3K,MAAA;UAAA,EAAiC;UACvChB,EAAA,CAAAC,cAAA,gBAAoF;UAArCD,EAAA,CAAAE,UAAA,sBAAAoW,8DAAA;YAAA,OAAYX,GAAA,CAAAtD,qBAAA,EAAuB;UAAA,EAAC;UAC/ErS,EAAA,CAAAC,cAAA,eAAoE;UAChED,EAAA,CAAAqG,UAAA,KAAAkQ,0CAAA,kBAOM;UAENvW,EAAA,CAAAC,cAAA,eAA0C;UACiDD,EAAA,CAAAqB,MAAA,IAAsD;UAAArB,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAqB,MAAA,SAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UAChLV,EAAA,CAAAC,cAAA,eAA6E;UACzED,EAAA,CAAAqG,UAAA,KAAAmQ,4CAAA,oBAQE;UACFxW,EAAA,CAAAqG,UAAA,KAAAoQ,2CAAA,mBAAmE;UAEvEzW,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA0B,SAAA,iBAAwE;UACxE1B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAqG,UAAA,KAAAqQ,4CAAA,oBAAgM;UAChM1W,EAAA,CAAAqG,UAAA,KAAAsQ,4CAAA,oBAAgK;UAChK3W,EAAA,CAAAqG,UAAA,KAAAuQ,4CAAA,oBAA0J;UAC9J5W,EAAA,CAAAU,YAAA,EAAM;UAIVV,EAAA,CAAAC,cAAA,eAA0C;UAC2CD,EAAA,CAAAqB,MAAA,IAA+C;UAAArB,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAqB,MAAA,SAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UACnKV,EAAA,CAAAC,cAAA,eAA6E;UACzED,EAAA,CAAAqG,UAAA,KAAAwQ,4CAAA,oBAQE;UACF7W,EAAA,CAAAqG,UAAA,KAAAyQ,2CAAA,mBAAoE;UACxE9W,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA0B,SAAA,iBAAqE;UACrE1B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAqG,UAAA,KAAA0Q,4CAAA,oBAAkM;UAClM/W,EAAA,CAAAqG,UAAA,KAAA2Q,4CAAA,oBAAiK;UACjKhX,EAAA,CAAAqG,UAAA,KAAA4Q,4CAAA,oBAAwJ;UAE5JjX,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAA0C;UACuBD,EAAA,CAAAqB,MAAA,IAA+C;UAAArB,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAqB,MAAA,SAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UAC/IV,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAqG,UAAA,KAAA6Q,4CAAA,oBASE;UACFlX,EAAA,CAAAqG,UAAA,KAAA8Q,2CAAA,mBAAoE;UACxEnX,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA0B,SAAA,iBAAqE;UACrE1B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAqG,UAAA,KAAA+Q,4CAAA,oBAAkM;UAClMpX,EAAA,CAAAqG,UAAA,KAAAgR,4CAAA,oBAAwJ;UAC5JrX,EAAA,CAAAU,YAAA,EAAM;UAIVV,EAAA,CAAAC,cAAA,eAA0C;UAC8CD,EAAA,CAAAqB,MAAA,IAAiD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAC7IV,EAAA,CAAAC,cAAA,eAAiB;UACTD,EAAA,CAAAqG,UAAA,KAAAiR,+CAAA,uBASY;UAChBtX,EAAA,CAAAqG,UAAA,KAAAkR,2CAAA,mBAAsH;UAC1HvX,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA0B,SAAA,iBAAuE;UACvE1B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAqG,UAAA,KAAAmR,4CAAA,oBAA4J;UAChKxX,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAA0C;UAC2CD,EAAA,CAAAqB,MAAA,IAA8C;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UACvIV,EAAA,CAAAC,cAAA,eAAiB;UACTD,EAAA,CAAAqG,UAAA,KAAAoR,+CAAA,uBASY;UAChBzX,EAAA,CAAAqG,UAAA,KAAAqR,2CAAA,mBAAmH;UACvH1X,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA0B,SAAA,iBAAoE;UACpE1B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAqG,UAAA,KAAAsR,4CAAA,oBAAyJ;UAC7J3X,EAAA,CAAAU,YAAA,EAAM;UAIVV,EAAA,CAAAqG,UAAA,KAAAuR,0CAAA,kBAiBM;UAEN5X,EAAA,CAAAqG,UAAA,KAAAwR,0CAAA,kBAKM;UAEN7X,EAAA,CAAAqG,UAAA,KAAAyR,0CAAA,mBAoBM;UAEN9X,EAAA,CAAAqG,UAAA,KAAA0R,0CAAA,kBAKM;UACV/X,EAAA,CAAAU,YAAA,EAAM;UAENV,EAAA,CAAAqG,UAAA,MAAA2R,2CAAA,kBAaM;UAENhY,EAAA,CAAAqG,UAAA,MAAA4R,2CAAA,kBAKM;UAGNjY,EAAA,CAAAqG,UAAA,MAAA6R,2CAAA,kBAoCM;UACNlY,EAAA,CAAAqG,UAAA,MAAA8R,2CAAA,kBAGM;UACVnY,EAAA,CAAAU,YAAA,EAAO;;;UArX6BV,EAAA,CAAAsB,SAAA,GAAgD;UAAhDtB,EAAA,CAAAyB,iBAAA,CAAAkU,GAAA,CAAA9U,WAAA,CAAAC,SAAA,wBAAgD;UAC7Cd,EAAA,CAAAsB,SAAA,GAAe;UAAftB,EAAA,CAAAW,UAAA,UAAAgV,GAAA,CAAAyC,KAAA,CAAe,SAAAzC,GAAA,CAAA0C,IAAA;UAI3CrY,EAAA,CAAAsB,SAAA,GAA8F;UAA9FtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAApN,QAAA,CAAAC,IAAA,IAAAmN,GAAA,CAAAlN,QAAA,CAAAkH,QAAA,IAAAgG,GAAA,CAAA9F,WAAA,CAAA7P,EAAA,CAAAsY,eAAA,KAAAC,GAAA,EAAA5C,GAAA,CAAAhW,SAAA,CAAAmQ,WAAA,CAAAC,MAAA,CAAAyI,MAAA,GAA8F;UAO3GxY,EAAA,CAAAsB,SAAA,GAA8B;UAA9BtB,EAAA,CAAAW,UAAA,cAAAgV,GAAA,CAAAzF,gBAAA,CAA8B;UACvBlQ,EAAA,CAAAsB,SAAA,GAAmB;UAAnBtB,EAAA,CAAAW,UAAA,oBAAmB,WAAAgV,GAAA,CAAA9U,WAAA,CAAAC,SAAA;UAGdd,EAAA,CAAAsB,SAAA,GAA+C;UAA/CtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAApN,QAAA,CAAAC,IAAA,IAAAmN,GAAA,CAAAlN,QAAA,CAAAmF,KAAA,CAA+C;UAmBjC5N,EAAA,CAAAsB,SAAA,GAAkB;UAAlBtB,EAAA,CAAAW,UAAA,mBAAkB,uDAAAgV,GAAA,CAAAxU,UAAA,CAAA6F,MAAA,gCAAA2O,GAAA,CAAAtI,gBAAA;UAWNrN,EAAA,CAAAsB,SAAA,GAAgD;UAAhDtB,EAAA,CAAAyB,iBAAA,CAAAkU,GAAA,CAAA9U,WAAA,CAAAC,SAAA,wBAAgD;UAQjEd,EAAA,CAAAsB,SAAA,GAAqC;UAArCtB,EAAA,CAAAW,UAAA,YAAAgV,GAAA,CAAAxU,UAAA,CAAAoC,YAAA,CAAqC;UAGdvD,EAAA,CAAAsB,SAAA,GAA+C;UAA/CtB,EAAA,CAAAyB,iBAAA,CAAAkU,GAAA,CAAA9U,WAAA,CAAAC,SAAA,uBAA+C;UAQtEd,EAAA,CAAAsB,SAAA,GAAqC;UAArCtB,EAAA,CAAAW,UAAA,YAAAgV,GAAA,CAAAxU,UAAA,CAAA6C,YAAA,CAAqC;UAMdhE,EAAA,CAAAsB,SAAA,GAA+C;UAA/CtB,EAAA,CAAAyB,iBAAA,CAAAkU,GAAA,CAAA9U,WAAA,CAAAC,SAAA,uBAA+C;UAapFd,EAAA,CAAAsB,SAAA,GAAkB;UAAlBtB,EAAA,CAAAW,UAAA,UAAAgV,GAAA,CAAAjJ,WAAA,CAAkB;UAalB1M,EAAA,CAAAsB,SAAA,GAAiB;UAAjBtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAAjJ,WAAA,CAAiB;UAgBiC1M,EAAA,CAAAsB,SAAA,GAAoE;UAApEtB,EAAA,CAAAyY,UAAA,CAAAzY,EAAA,CAAAiD,eAAA,KAAAyV,IAAA,EAAoE;UADrH1Y,EAAA,CAAAW,UAAA,gBAAAX,EAAA,CAAAiD,eAAA,KAAA0V,IAAA,EAAqD,WAAAhD,GAAA,CAAAlJ,UAAA,aAAAkJ,GAAA,CAAAhK,mBAAA;UAExC3L,EAAA,CAAAsB,SAAA,GAA2B;UAA3BtB,EAAA,CAAAW,UAAA,cAAAgV,GAAA,CAAAnP,aAAA,CAA2B;UAEKxG,EAAA,CAAAsB,SAAA,GAA0E;UAA1EtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAApN,QAAA,CAAAC,IAAA,IAAAmN,GAAA,CAAAlN,QAAA,CAAAmF,KAAA,IAAA+H,GAAA,CAAA1P,WAAA,aAA0E;UAU1BjG,EAAA,CAAAsB,SAAA,GAAsD;UAAtDtB,EAAA,CAAAyB,iBAAA,CAAAkU,GAAA,CAAA9U,WAAA,CAAAC,SAAA,8BAAsD;UAEjId,EAAA,CAAAsB,SAAA,GAAuD;UAAvDtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAA1P,WAAA,gBAAA0P,GAAA,CAAA1P,WAAA,aAAuD;UASxDjG,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAA1P,WAAA,aAA6B;UAQPjG,EAAA,CAAAsB,SAAA,GAAqG;UAArGtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAA9D,WAAA,CAAA+D,KAAA,KAAAiP,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAA9D,WAAA,CAAAgE,MAAA,kBAAAgP,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAA9D,WAAA,CAAAgE,MAAA,CAAAC,QAAA,EAAqG;UACrG5G,EAAA,CAAAsB,SAAA,GAA0D;UAA1DtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAA9D,WAAA,CAAAgE,MAAA,kBAAAgP,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAA9D,WAAA,CAAAgE,MAAA,CAAAuE,SAAA,CAA0D;UAC1DlL,EAAA,CAAAsB,SAAA,GAAwD;UAAxDtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAA9D,WAAA,CAAAgE,MAAA,kBAAAgP,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAA9D,WAAA,CAAAgE,MAAA,CAAAiS,OAAA,CAAwD;UAMR5Y,EAAA,CAAAsB,SAAA,GAA+C;UAA/CtB,EAAA,CAAAyB,iBAAA,CAAAkU,GAAA,CAAA9U,WAAA,CAAAC,SAAA,uBAA+C;UAEpHd,EAAA,CAAAsB,SAAA,GAAuD;UAAvDtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAA1P,WAAA,gBAAA0P,GAAA,CAAA1P,WAAA,aAAuD;UASxDjG,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAA1P,WAAA,aAA6B;UAOPjG,EAAA,CAAAsB,SAAA,GAAuG;UAAvGtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAAlD,YAAA,CAAAmD,KAAA,KAAAiP,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAAlD,YAAA,CAAAoD,MAAA,kBAAAgP,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAAlD,YAAA,CAAAoD,MAAA,CAAAC,QAAA,EAAuG;UACvG5G,EAAA,CAAAsB,SAAA,GAA2D;UAA3DtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAAlD,YAAA,CAAAoD,MAAA,kBAAAgP,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAAlD,YAAA,CAAAoD,MAAA,CAAAuE,SAAA,CAA2D;UAC3DlL,EAAA,CAAAsB,SAAA,GAAyD;UAAzDtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAAlD,YAAA,CAAAoD,MAAA,kBAAAgP,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAAlD,YAAA,CAAAoD,MAAA,CAAAiS,OAAA,CAAyD;UAM7B5Y,EAAA,CAAAsB,SAAA,GAA+C;UAA/CtB,EAAA,CAAAyB,iBAAA,CAAAkU,GAAA,CAAA9U,WAAA,CAAAC,SAAA,uBAA+C;UAEhGd,EAAA,CAAAsB,SAAA,GAAuD;UAAvDtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAA1P,WAAA,gBAAA0P,GAAA,CAAA1P,WAAA,aAAuD;UAUxDjG,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAA1P,WAAA,aAA6B;UAOPjG,EAAA,CAAAsB,SAAA,GAAuG;UAAvGtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAAzC,YAAA,CAAA0C,KAAA,KAAAiP,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAAzC,YAAA,CAAA2C,MAAA,kBAAAgP,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAAzC,YAAA,CAAA2C,MAAA,CAAAC,QAAA,EAAuG;UACvG5G,EAAA,CAAAsB,SAAA,GAAyD;UAAzDtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAAzC,YAAA,CAAA2C,MAAA,kBAAAgP,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAAzC,YAAA,CAAA2C,MAAA,CAAAiS,OAAA,CAAyD;UAMN5Y,EAAA,CAAAsB,SAAA,GAAiD;UAAjDtB,EAAA,CAAAyB,iBAAA,CAAAkU,GAAA,CAAA9U,WAAA,CAAAC,SAAA,yBAAiD;UAElHd,EAAA,CAAAsB,SAAA,GAA2B;UAA3BtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAA1P,WAAA,aAA2B;UAULjG,EAAA,CAAAsB,SAAA,GAAsD;UAAtDtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAA1P,WAAA,gBAAA0P,GAAA,CAAA1P,WAAA,aAAsD;UAO9DjG,EAAA,CAAAsB,SAAA,GAAsD;UAAtDtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAA9B,OAAA,CAAAgC,MAAA,kBAAAgP,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAA9B,OAAA,CAAAgC,MAAA,CAAAuE,SAAA,CAAsD;UAKNlL,EAAA,CAAAsB,SAAA,GAA8C;UAA9CtB,EAAA,CAAAyB,iBAAA,CAAAkU,GAAA,CAAA9U,WAAA,CAAAC,SAAA,sBAA8C;UAE5Gd,EAAA,CAAAsB,SAAA,GAA2B;UAA3BtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAA1P,WAAA,aAA2B;UAULjG,EAAA,CAAAsB,SAAA,GAAsD;UAAtDtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAA1P,WAAA,gBAAA0P,GAAA,CAAA1P,WAAA,aAAsD;UAO9DjG,EAAA,CAAAsB,SAAA,GAAmD;UAAnDtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAApB,IAAA,CAAAsB,MAAA,kBAAAgP,GAAA,CAAAnP,aAAA,CAAAC,QAAA,CAAApB,IAAA,CAAAsB,MAAA,CAAAuE,SAAA,CAAmD;UAKlFlL,EAAA,CAAAsB,SAAA,GAAqN;UAArNtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAA1P,WAAA,gBAAA0P,GAAA,CAAApN,QAAA,CAAAC,IAAA,IAAAmN,GAAA,CAAAlN,QAAA,CAAAC,QAAA,IAAAiN,GAAA,CAAApT,MAAA,CAAAyE,MAAA,YAAA2O,GAAA,CAAApT,MAAA,CAAA4E,SAAA,SAAAwO,GAAA,CAAApN,QAAA,CAAAC,IAAA,IAAAmN,GAAA,CAAAlN,QAAA,CAAAC,QAAA,IAAAiN,GAAA,CAAA1P,WAAA,gBAAA0P,GAAA,CAAApT,MAAA,CAAAwD,UAAA,SAAqN;UAmBrN/F,EAAA,CAAAsB,SAAA,GAAgJ;UAAhJtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAA1P,WAAA,gBAAA0P,GAAA,CAAApN,QAAA,CAAAC,IAAA,IAAAmN,GAAA,CAAAlN,QAAA,CAAAC,QAAA,IAAAiN,GAAA,CAAApT,MAAA,CAAAyE,MAAA,YAAA2O,GAAA,CAAApT,MAAA,CAAA4E,SAAA,SAAAwO,GAAA,CAAA1P,WAAA,aAAgJ;UAOhJjG,EAAA,CAAAsB,SAAA,GAAsD;UAAtDtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAA1P,WAAA,gBAAA0P,GAAA,CAAA1P,WAAA,aAAsD;UAsBtDjG,EAAA,CAAAsB,SAAA,GAAmF;UAAnFtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAA1P,WAAA,gBAAA0P,GAAA,CAAA1P,WAAA,gBAAA0P,GAAA,CAAApT,MAAA,CAAAwD,UAAA,SAAmF;UAQvF/F,EAAA,CAAAsB,SAAA,GAA2B;UAA3BtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAA1P,WAAA,aAA2B;UAe3BjG,EAAA,CAAAsB,SAAA,GAAmF;UAAnFtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAA1P,WAAA,gBAAA0P,GAAA,CAAA1P,WAAA,gBAAA0P,GAAA,CAAApT,MAAA,CAAAwD,UAAA,SAAmF;UAQnF/F,EAAA,CAAAsB,SAAA,GAA+F;UAA/FtB,EAAA,CAAAW,UAAA,UAAAgV,GAAA,CAAA1P,WAAA,gBAAA0P,GAAA,CAAA1P,WAAA,iBAAA0P,GAAA,CAAArK,SAAA,IAAAqK,GAAA,CAAArK,SAAA,CAAAQ,MAAA,KAA+F;UAqC/F9L,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAW,UAAA,SAAAgV,GAAA,CAAA1P,WAAA,aAA6B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}