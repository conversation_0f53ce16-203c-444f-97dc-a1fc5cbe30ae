{"ast": null, "code": "import { TicketService } from \"src/app/service/ticket/TicketService\";\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\nimport { ComponentBase } from \"src/app/component.base\";\nimport { AccountService } from \"../../../service/account/AccountService\";\nimport { Validators } from \"@angular/forms\";\nimport { LogHandleTicketService } from \"../../../service/ticket/LogHandleTicketService\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/breadcrumb\";\nimport * as i4 from \"primeng/tooltip\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/inputtext\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"../../common-module/table/table.component\";\nimport * as i9 from \"../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i10 from \"primeng/dropdown\";\nimport * as i11 from \"primeng/dialog\";\nimport * as i12 from \"primeng/inputtextarea\";\nimport * as i13 from \"primeng/panel\";\nimport * as i14 from \"primeng/chips\";\nimport * as i15 from \"primeng/table\";\nimport * as i16 from \"src/app/service/ticket/TicketService\";\nimport * as i17 from \"../../../service/account/AccountService\";\nimport * as i18 from \"../../../service/ticket/LogHandleTicketService\";\nfunction ListReplaceSimTicketComponent_p_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 51);\n    i0.ɵɵlistener(\"click\", function ListReplaceSimTicketComponent_p_button_6_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.showModalCreate());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"global.button.create\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"span\", 11)(2, \"p-dropdown\", 52);\n    i0.ɵɵlistener(\"ngModelChange\", function ListReplaceSimTicketComponent_div_10_Template_p_dropdown_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.searchInfo.provinceCode = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 53);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"showClear\", true)(\"filter\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx_r1.searchInfo.provinceCode)(\"required\", false)(\"options\", ctx_r1.listProvince);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.tranService.translate(\"account.label.province\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_table_vnpt_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"table-vnpt\", 54);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"tableId\", \"tableTicketConfigList\")(\"fieldId\", \"provinceCode\")(\"columns\", ctx_r2.columns)(\"dataSet\", ctx_r2.dataSet)(\"options\", ctx_r2.optionTable)(\"pageNumber\", ctx_r2.pageNumber)(\"loadData\", ctx_r2.search.bind(ctx_r2))(\"pageSize\", ctx_r2.pageSize)(\"sort\", ctx_r2.sort)(\"params\", ctx_r2.searchInfo)(\"labelTable\", ctx_r2.tranService.translate(\"ticket.menu.requestList\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_table_vnpt_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"table-vnpt\", 54);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"tableId\", \"tableTicketConfigList\")(\"fieldId\", \"provinceCode\")(\"columns\", ctx_r3.columns)(\"dataSet\", ctx_r3.dataSet)(\"options\", ctx_r3.optionTable)(\"pageNumber\", ctx_r3.pageNumber)(\"loadData\", ctx_r3.search.bind(ctx_r3))(\"pageSize\", ctx_r3.pageSize)(\"sort\", ctx_r3.sort)(\"params\", ctx_r3.searchInfo)(\"labelTable\", ctx_r3.tranService.translate(\"ticket.menu.requestList\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"label\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 28);\n    i0.ɵɵtext(4, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 34)(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.tranService.translate(\"account.label.province\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.getProvinceName(ctx_r4.ticket.provinceCode));\n  }\n}\nfunction ListReplaceSimTicketComponent_input_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 56);\n    i0.ɵɵlistener(\"ngModelChange\", function ListReplaceSimTicketComponent_input_41_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.ticket.contactName = $event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.ticket.contactName)(\"required\", true)(\"maxLength\", ctx_r5.maxlengthContactName)(\"placeholder\", ctx_r5.tranService.translate(\"account.text.inputFullname\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_span_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r6.ticket.contactName);\n  }\n}\nfunction ListReplaceSimTicketComponent_small_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.tranService.translate(\"global.message.required\"));\n  }\n}\nconst _c0 = function () {\n  return {\n    len: 255\n  };\n};\nfunction ListReplaceSimTicketComponent_small_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r8.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListReplaceSimTicketComponent_small_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r9.tranService.translate(\"global.message.formatContainVN\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_input_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 57);\n    i0.ɵɵlistener(\"ngModelChange\", function ListReplaceSimTicketComponent_input_55_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.ticket.contactEmail = $event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngModel\", ctx_r10.ticket.contactEmail)(\"required\", true)(\"maxLength\", 50)(\"placeholder\", ctx_r10.tranService.translate(\"account.text.inputEmail\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_span_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r11.ticket.contactEmail);\n  }\n}\nfunction ListReplaceSimTicketComponent_small_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r12.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_small_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r13.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListReplaceSimTicketComponent_small_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r14.tranService.translate(\"global.message.invalidEmail\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_input_69_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 58);\n    i0.ɵɵlistener(\"ngModelChange\", function ListReplaceSimTicketComponent_input_69_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r43 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r43.ticket.contactPhone = $event);\n    })(\"keydown\", function ListReplaceSimTicketComponent_input_69_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r45 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r45.preventCharacter($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngModel\", ctx_r15.ticket.contactPhone)(\"required\", true)(\"maxLength\", 11)(\"placeholder\", ctx_r15.tranService.translate(\"account.text.inputPhone\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_span_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r16.ticket.contactPhone);\n  }\n}\nfunction ListReplaceSimTicketComponent_small_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r17.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_small_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r18.tranService.translate(\"ticket.message.invalidPhone\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_div_76_p_chips_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-chips\", 62);\n    i0.ɵɵlistener(\"ngModelChange\", function ListReplaceSimTicketComponent_div_76_p_chips_6_Template_p_chips_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r49 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r49.ticket.sim = $event);\n    })(\"keydown\", function ListReplaceSimTicketComponent_div_76_p_chips_6_Template_p_chips_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r51.preventCharacter($event));\n    })(\"ngModelChange\", function ListReplaceSimTicketComponent_div_76_p_chips_6_Template_p_chips_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r52 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r52.onSimChangeAdd($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r46 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"pTooltip\", ctx_r46.tranService.translate(\"ticket.message.noteChangeSim\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r46.ticket.sim);\n  }\n}\nfunction ListReplaceSimTicketComponent_div_76_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r47 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r47.ticket.sim);\n  }\n}\nfunction ListReplaceSimTicketComponent_div_76_small_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r48 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r48.tranService.translate(\"ticket.message.hintValidPhone\"), \" \");\n  }\n}\nfunction ListReplaceSimTicketComponent_div_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"label\", 59);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 28);\n    i0.ɵɵtext(4, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 34);\n    i0.ɵɵtemplate(6, ListReplaceSimTicketComponent_div_76_p_chips_6_Template, 1, 2, \"p-chips\", 60);\n    i0.ɵɵtemplate(7, ListReplaceSimTicketComponent_div_76_span_7_Template, 2, 1, \"span\", 44);\n    i0.ɵɵtemplate(8, ListReplaceSimTicketComponent_div_76_small_8_Template, 2, 1, \"small\", 61);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.tranService.translate(\"ticket.label.changeSim\"), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.typeRequest == \"create\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.typeRequest == \"update\" || ctx_r19.typeRequest == \"detail\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.isValidChangeSim && (ctx_r19.formTicketSim.controls.sim.value == null ? null : ctx_r19.formTicketSim.controls.sim.value.length) > 0);\n  }\n}\nfunction ListReplaceSimTicketComponent_div_77_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r53.tranService.translate(\"global.message.required\"), \" \");\n  }\n}\nfunction ListReplaceSimTicketComponent_div_77_small_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r54 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r54.tranService.translate(\"ticket.message.invalidPhone\"), \" \");\n  }\n}\nfunction ListReplaceSimTicketComponent_div_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"label\", 65);\n    i0.ɵɵelementStart(2, \"div\", 34);\n    i0.ɵɵtemplate(3, ListReplaceSimTicketComponent_div_77_small_3_Template, 2, 1, \"small\", 66);\n    i0.ɵɵtemplate(4, ListReplaceSimTicketComponent_div_77_small_4_Template, 2, 1, \"small\", 66);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r20.formTicketSim.controls.sim.dirty && (ctx_r20.formTicketSim.controls.sim.errors == null ? null : ctx_r20.formTicketSim.controls.sim.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r20.isValidChangeSim && (ctx_r20.ticket.sim == null ? null : ctx_r20.ticket.sim.length) > 0);\n  }\n}\nfunction ListReplaceSimTicketComponent_textarea_82_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"textarea\", 68);\n    i0.ɵɵlistener(\"ngModelChange\", function ListReplaceSimTicketComponent_textarea_82_Template_textarea_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r55 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r55.ticket.content = $event);\n    })(\"keydown\", function ListReplaceSimTicketComponent_textarea_82_Template_textarea_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r57 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r57.onKeyDownContent($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"autoResize\", false)(\"ngModel\", ctx_r21.ticket.content)(\"maxlength\", 255)(\"placeholder\", ctx_r21.tranService.translate(\"ticket.label.content\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_span_83_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r22.ticket.content);\n  }\n}\nfunction ListReplaceSimTicketComponent_small_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r23.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListReplaceSimTicketComponent_textarea_92_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r59 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"textarea\", 69);\n    i0.ɵɵlistener(\"ngModelChange\", function ListReplaceSimTicketComponent_textarea_92_Template_textarea_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.ticket.note = $event);\n    })(\"keydown\", function ListReplaceSimTicketComponent_textarea_92_Template_textarea_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.onKeyDownNote($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"autoResize\", false)(\"ngModel\", ctx_r24.ticket.note)(\"maxlength\", 255)(\"placeholder\", ctx_r24.tranService.translate(\"ticket.label.note\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_span_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r25.ticket.note);\n  }\n}\nfunction ListReplaceSimTicketComponent_small_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r26.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nconst _c1 = function () {\n  return {\n    type: 3\n  };\n};\nconst _c2 = function () {\n  return {\n    bottom: \"40px\",\n    left: \"12px\",\n    \"min-width\": \"calc(100% - 20px)\"\n  };\n};\nfunction ListReplaceSimTicketComponent_div_98_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r62 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"label\", 70);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 71)(4, \"vnpt-select\", 72);\n    i0.ɵɵlistener(\"valueChange\", function ListReplaceSimTicketComponent_div_98_Template_vnpt_select_valueChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r61 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r61.ticket.assigneeId = $event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r27.tranService.translate(\"ticket.label.transferProcessing\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r27.ticket.assigneeId)(\"placeholder\", ctx_r27.tranService.translate(\"ticket.label.transferProcessing\"))(\"disabled\", ctx_r27.ticket.assigneeId != null && ctx_r27.typeRequest == \"detail\")(\"isMultiChoice\", false)(\"paramDefault\", i0.ɵɵpureFunction0(7, _c1))(\"stylePositionBoxSelect\", i0.ɵɵpureFunction0(8, _c2));\n  }\n}\nfunction ListReplaceSimTicketComponent_div_99_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r63 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r63.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"label\", 73);\n    i0.ɵɵelementStart(2, \"div\", 34);\n    i0.ɵɵtemplate(3, ListReplaceSimTicketComponent_div_99_small_3_Template, 2, 1, \"small\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.formTicketSim.controls.assigneeId.dirty && (ctx_r28.formTicketSim.controls.assigneeId.errors == null ? null : ctx_r28.formTicketSim.controls.assigneeId.errors.required));\n  }\n}\nfunction ListReplaceSimTicketComponent_div_100_p_dropdown_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dropdown\", 77);\n    i0.ɵɵlistener(\"ngModelChange\", function ListReplaceSimTicketComponent_div_100_p_dropdown_4_Template_p_dropdown_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r70 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r70.ticket.status = $event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r64 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", true)(\"ngModel\", ctx_r64.ticket.status)(\"options\", ctx_r64.ticket.statusOld !== null ? ctx_r64.mapTicketStatus[ctx_r64.ticket.statusOld] : ctx_r64.listTicketStatus)(\"placeholder\", ctx_r64.tranService.translate(\"ticket.label.status\"))(\"emptyMessage\", ctx_r64.tranService.translate(\"global.text.nodata\"));\n  }\n}\nconst _c3 = function () {\n  return [\"p-2\", \"text-white\", \"bg-cyan-300\", \"border-round\", \"inline-block\"];\n};\nfunction ListReplaceSimTicketComponent_div_100_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r65 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c3));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r65.getValueStatus(ctx_r65.ticket.statusOld));\n  }\n}\nconst _c4 = function () {\n  return [\"p-2\", \"text-white\", \"bg-bluegray-500\", \"border-round\", \"inline-block\"];\n};\nfunction ListReplaceSimTicketComponent_div_100_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r66 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c4));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r66.getValueStatus(ctx_r66.ticket.statusOld));\n  }\n}\nconst _c5 = function () {\n  return [\"p-2\", \"text-white\", \"bg-orange-400\", \"border-round\", \"inline-block\"];\n};\nfunction ListReplaceSimTicketComponent_div_100_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r67 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c5));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r67.getValueStatus(ctx_r67.ticket.statusOld));\n  }\n}\nconst _c6 = function () {\n  return [\"p-2\", \"text-white\", \"bg-red-500\", \"border-round\", \"inline-block\"];\n};\nfunction ListReplaceSimTicketComponent_div_100_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r68 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c6));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r68.getValueStatus(ctx_r68.ticket.statusOld));\n  }\n}\nconst _c7 = function () {\n  return [\"p-2\", \"text-white\", \"bg-green-500\", \"border-round\", \"inline-block\"];\n};\nfunction ListReplaceSimTicketComponent_div_100_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r69 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c7));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r69.getValueStatus(ctx_r69.ticket.statusOld));\n  }\n}\nfunction ListReplaceSimTicketComponent_div_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"label\", 74);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 34);\n    i0.ɵɵtemplate(4, ListReplaceSimTicketComponent_div_100_p_dropdown_4_Template, 1, 6, \"p-dropdown\", 75);\n    i0.ɵɵtemplate(5, ListReplaceSimTicketComponent_div_100_span_5_Template, 2, 4, \"span\", 76);\n    i0.ɵɵtemplate(6, ListReplaceSimTicketComponent_div_100_span_6_Template, 2, 4, \"span\", 76);\n    i0.ɵɵtemplate(7, ListReplaceSimTicketComponent_div_100_span_7_Template, 2, 4, \"span\", 76);\n    i0.ɵɵtemplate(8, ListReplaceSimTicketComponent_div_100_span_8_Template, 2, 4, \"span\", 76);\n    i0.ɵɵtemplate(9, ListReplaceSimTicketComponent_div_100_span_9_Template, 2, 4, \"span\", 76);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r29.userInfo.type == ctx_r29.userType.PROVINCE && ctx_r29.typeRequest != \"detail\" ? ctx_r29.ticket.assigneeId == null || ctx_r29.ticket.assigneeId != null && !ctx_r29.listActivatedAccount.includes(ctx_r29.ticket.assigneeId) ? \"\" : \"hidden\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r29.tranService.translate(\"ticket.label.status\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.typeRequest == \"update\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.typeRequest == \"detail\" && ctx_r29.ticket.statusOld == 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.typeRequest == \"detail\" && ctx_r29.ticket.statusOld == 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.typeRequest == \"detail\" && ctx_r29.ticket.statusOld == 2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.typeRequest == \"detail\" && ctx_r29.ticket.statusOld == 3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r29.typeRequest == \"detail\" && ctx_r29.ticket.statusOld == 4);\n  }\n}\nfunction ListReplaceSimTicketComponent_div_101_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r72 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r72.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_div_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"label\", 73);\n    i0.ɵɵelementStart(2, \"div\", 34);\n    i0.ɵɵtemplate(3, ListReplaceSimTicketComponent_div_101_small_3_Template, 2, 1, \"small\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.formTicketSim.controls.status.dirty && (ctx_r30.formTicketSim.controls.status.errors == null ? null : ctx_r30.formTicketSim.controls.status.errors.required));\n  }\n}\nfunction ListReplaceSimTicketComponent_div_102_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 28);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ListReplaceSimTicketComponent_div_102_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r75 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"label\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, ListReplaceSimTicketComponent_div_102_span_3_Template, 2, 0, \"span\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 34)(5, \"input\", 78);\n    i0.ɵɵlistener(\"ngModelChange\", function ListReplaceSimTicketComponent_div_102_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r75);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.ticket.cause = $event);\n    })(\"keydown\", function ListReplaceSimTicketComponent_div_102_Template_input_keydown_5_listener($event) {\n      i0.ɵɵrestoreView(_r75);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.onKeyDownCause($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r31.userInfo.type == ctx_r31.userType.PROVINCE ? ctx_r31.ticket.assigneeId == null || ctx_r31.ticket.assigneeId != null && !ctx_r31.listActivatedAccount.includes(ctx_r31.ticket.assigneeId) ? \"\" : \"hidden\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r31.tranService.translate(\"ticket.label.processingNotes\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r31.ticket.status);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r31.ticket.cause)(\"required\", ctx_r31.ticket.status != null)(\"maxLength\", 255)(\"placeholder\", ctx_r31.tranService.translate(\"ticket.label.processingNotes\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_div_103_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r77 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r77.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_div_103_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"label\", 33);\n    i0.ɵɵelementStart(2, \"div\", 34);\n    i0.ɵɵtemplate(3, ListReplaceSimTicketComponent_div_103_small_3_Template, 2, 1, \"small\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r32.formTicketSim.controls.cause.dirty && (ctx_r32.formTicketSim.controls.cause.errors == null ? null : ctx_r32.formTicketSim.controls.cause.errors.required) || ctx_r32.ticket.status != null && (ctx_r32.ticket.cause == null || ctx_r32.ticket.cause.trim() == \"\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_div_104_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 83);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 84);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r78 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r78.tranService.translate(\"global.text.stt\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r78.tranService.translate(\"account.text.account\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r78.tranService.translate(\"global.button.changeStatus\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r78.tranService.translate(\"account.label.time\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r78.tranService.translate(\"ticket.label.content\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_div_104_ng_template_5_input_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r88 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 87);\n    i0.ɵɵlistener(\"ngModelChange\", function ListReplaceSimTicketComponent_div_104_ng_template_5_input_11_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r88);\n      const note_r80 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(note_r80.content = $event);\n    })(\"keydown\", function ListReplaceSimTicketComponent_div_104_ng_template_5_input_11_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r88);\n      const note_r80 = i0.ɵɵnextContext().$implicit;\n      const ctx_r89 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r89.onKeyDownNoteContent($event, note_r80));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const note_r80 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", note_r80.content)(\"required\", true)(\"maxLength\", 255);\n  }\n}\nfunction ListReplaceSimTicketComponent_div_104_ng_template_5_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const note_r80 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(note_r80.content);\n  }\n}\nfunction ListReplaceSimTicketComponent_div_104_ng_template_5_small_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r84 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r84.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListReplaceSimTicketComponent_div_104_ng_template_5_small_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r85 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r85.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListReplaceSimTicketComponent_div_104_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 85)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtemplate(11, ListReplaceSimTicketComponent_div_104_ng_template_5_input_11_Template, 1, 3, \"input\", 86);\n    i0.ɵɵtemplate(12, ListReplaceSimTicketComponent_div_104_ng_template_5_span_12_Template, 2, 1, \"span\", 31);\n    i0.ɵɵtemplate(13, ListReplaceSimTicketComponent_div_104_ng_template_5_small_13_Template, 2, 1, \"small\", 35);\n    i0.ɵɵtemplate(14, ListReplaceSimTicketComponent_div_104_ng_template_5_small_14_Template, 2, 2, \"small\", 35);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const note_r80 = ctx.$implicit;\n    const i_r81 = ctx.rowIndex;\n    const ctx_r79 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r79.mapForm[note_r80.id]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r81 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(note_r80.userName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r79.getValueStatus(note_r80.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 9, note_r80.createdDate, \"HH:mm:ss dd/MM/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r79.typeRequest == \"update\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r79.typeRequest == \"detail\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r79.mapForm[note_r80.id].controls.content.dirty && (ctx_r79.mapForm[note_r80.id].controls.content.errors == null ? null : ctx_r79.mapForm[note_r80.id].controls.content.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r79.mapForm[note_r80.id].controls.content.errors == null ? null : ctx_r79.mapForm[note_r80.id].controls.content.errors.maxLength);\n  }\n}\nconst _c8 = function () {\n  return {\n    \"min-width\": \"50rem\"\n  };\n};\nfunction ListReplaceSimTicketComponent_div_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"label\", 79);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-table\", 80);\n    i0.ɵɵtemplate(4, ListReplaceSimTicketComponent_div_104_ng_template_4_Template, 11, 5, \"ng-template\", 81);\n    i0.ɵɵtemplate(5, ListReplaceSimTicketComponent_div_104_ng_template_5_Template, 15, 12, \"ng-template\", 82);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r33.tranService.translate(\"ticket.label.listNote\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r33.listNotes)(\"tableStyle\", i0.ɵɵpureFunction0(3, _c8));\n  }\n}\nfunction ListReplaceSimTicketComponent_div_105_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r94 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"p-button\", 89);\n    i0.ɵɵlistener(\"click\", function ListReplaceSimTicketComponent_div_105_Template_p_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r94);\n      const ctx_r93 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r93.isShowCreateRequest = false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(2, \"p-button\", 90);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", ctx_r34.tranService.translate(\"global.button.cancel\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r34.formTicketSim.invalid || ctx_r34.typeRequest == \"create\" && !ctx_r34.isValidChangeSim || ctx_r34.ticket.status != null && ctx_r34.ticket.cause != null && ctx_r34.ticket.cause.trim() == \"\" || ctx_r34.listNotes.length > 0 && !ctx_r34.isFormValid())(\"label\", ctx_r34.tranService.translate(\"global.button.save\"));\n  }\n}\nconst _c9 = function (a0) {\n  return [a0];\n};\nconst _c10 = function () {\n  return {\n    width: \"800px\",\n    overflowY: \"scroll\",\n    maxHeight: \"80%\"\n  };\n};\nconst _c11 = function () {\n  return {\n    \"1199px\": \"75vw\",\n    \"575px\": \"90vw\"\n  };\n};\nexport class ListReplaceSimTicketComponent extends ComponentBase {\n  constructor(ticketService, accountService, logHandleTicketService, formBuilder, injector) {\n    super(injector);\n    this.ticketService = ticketService;\n    this.accountService = accountService;\n    this.logHandleTicketService = logHandleTicketService;\n    this.formBuilder = formBuilder;\n    this.injector = injector;\n    this.maxlengthContactName = 50;\n    this.isValidChangeSim = true;\n    this.oldTicket = {};\n    this.isShowTableNote = false;\n    this.mapForm = {};\n    this.titlePopup = '';\n    this.changeTable = false;\n    this.CONSTANTS = CONSTANTS;\n  }\n  ngOnInit() {\n    let me = this;\n    me.changeTable = false;\n    this.userInfo = this.sessionService.userInfo;\n    this.isShowCreateRequest = false;\n    this.typeRequest = 'create';\n    this.userType = CONSTANTS.USER_TYPE;\n    this.listNotes = [];\n    this.ticket = {\n      id: null,\n      contactName: null,\n      contactEmail: null,\n      contactPhone: null,\n      content: null,\n      note: null,\n      cause: null,\n      type: CONSTANTS.REQUEST_TYPE.REPLACE_SIM,\n      sim: null,\n      status: null,\n      statusOld: null,\n      assigneeId: null,\n      provinceCode: null\n    };\n    this.listTicketType = [{\n      label: this.tranService.translate('ticket.type.replaceSim'),\n      value: 0\n    }];\n    this.mapTicketStatus = {\n      0: [{\n        label: me.tranService.translate('ticket.status.received'),\n        value: 1\n      }, {\n        label: me.tranService.translate('ticket.status.reject'),\n        value: 3\n      }],\n      1: [{\n        label: me.tranService.translate('ticket.status.inProgress'),\n        value: 2\n      }, {\n        label: me.tranService.translate('ticket.status.reject'),\n        value: 3\n      }],\n      2: [{\n        label: me.tranService.translate('ticket.status.done'),\n        value: 4\n      }, {\n        label: me.tranService.translate('ticket.status.reject'),\n        value: 3\n      }]\n    };\n    this.listTicketStatus = [{\n      label: me.tranService.translate('ticket.status.new'),\n      value: 0\n    }, {\n      label: me.tranService.translate('ticket.status.received'),\n      value: 1\n    }, {\n      label: me.tranService.translate('ticket.status.inProgress'),\n      value: 2\n    }, {\n      label: me.tranService.translate('ticket.status.reject'),\n      value: 3\n    }, {\n      label: me.tranService.translate('ticket.status.done'),\n      value: 4\n    }];\n    this.searchInfo = {\n      provinceCode: null,\n      email: null,\n      contactPhone: null,\n      contactEmail: null,\n      type: CONSTANTS.REQUEST_TYPE.REPLACE_SIM,\n      status: null\n    };\n    this.columns = [{\n      name: this.tranService.translate(\"ticket.label.province\"),\n      key: \"provinceName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: this.userInfo.type == CONSTANTS.USER_TYPE.ADMIN,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"ticket.label.customerName\"),\n      key: \"contactName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      isShowTooltip: true,\n      style: {\n        display: 'inline-block',\n        maxWidth: '350px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.email\"),\n      key: \"contactEmail\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      isShowTooltip: true,\n      style: {\n        display: 'inline-block',\n        maxWidth: '350px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.phone\"),\n      key: \"contactPhone\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"ticket.label.content\"),\n      key: \"content\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      isShowTooltip: true,\n      style: {\n        display: 'inline-block',\n        maxWidth: '350px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.createdDate\"),\n      key: \"createdDate\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        return me.utilService.convertDateToString(new Date(value));\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.updatedDate\"),\n      key: \"updatedDate\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        return value ? me.utilService.convertDateToString(new Date(value)) : \"\";\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.updateBy\"),\n      key: \"updatedByName\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShowTooltip: true,\n      isShow: true,\n      isSort: true,\n      style: {\n        width: '150px',\n        cursor: 'pointer'\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.status\"),\n      key: \"status\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcGetClassname: value => {\n        if (value == CONSTANTS.REQUEST_STATUS.NEW) {\n          return ['p-2', 'text-white', \"bg-cyan-300\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\n          return ['p-2', 'text-white', \"bg-bluegray-500\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\n          return ['p-2', 'text-white', \"bg-orange-400\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\n          return ['p-2', 'text-white', \"bg-red-500\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\n          return ['p-2', 'text-white', \"bg-green-500\", \"border-round\", \"inline-block\"];\n        }\n        return '';\n      },\n      funcConvertText: function (value) {\n        if (value == CONSTANTS.REQUEST_STATUS.NEW) {\n          return me.tranService.translate(\"ticket.status.new\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\n          return me.tranService.translate(\"ticket.status.received\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\n          return me.tranService.translate(\"ticket.status.inProgress\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\n          return me.tranService.translate(\"ticket.status.reject\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\n          return me.tranService.translate(\"ticket.status.done\");\n        }\n        return \"\";\n      }\n    }];\n    this.optionTable = {\n      hasClearSelected: false,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false,\n      action: [{\n        icon: \"pi pi-info-circle\",\n        tooltip: this.tranService.translate(\"global.button.view\"),\n        func: function (id, item) {\n          me.handleDetailRequest(id, item);\n        }\n      }, {\n        icon: \"pi pi-window-maximize\",\n        tooltip: this.tranService.translate(\"global.button.edit\"),\n        func: function (id, item) {\n          me.handleEditRequest(id, item);\n        },\n        funcAppear: function (id, item) {\n          if (me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER) return false;\n          if (!item.updatedBy && !item.assigneeId && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;\n          if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy !== me.userInfo.id || me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.assigneeId != null) {\n            return false;\n          }\n          if (me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;else return false;\n        }\n      }]\n    };\n    this.pageNumber = 0;\n    this.pageSize = 10;\n    this.sort = \"createdDate,desc\";\n    this.dataSet = {\n      content: [],\n      total: 0\n    };\n    this.formSearchTicket = this.formBuilder.group(this.searchInfo);\n    this.formTicketSim = this.formBuilder.group(this.ticket);\n    this.getListProvince();\n    this.listActivatedAccount = [];\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n  }\n  getValueStatus(value) {\n    let me = this;\n    {\n      if (value == CONSTANTS.REQUEST_STATUS.NEW) {\n        return me.tranService.translate(\"ticket.status.new\");\n      } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\n        return me.tranService.translate(\"ticket.status.received\");\n      } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\n        return me.tranService.translate(\"ticket.status.inProgress\");\n      } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\n        return me.tranService.translate(\"ticket.status.reject\");\n      } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\n        return me.tranService.translate(\"ticket.status.done\");\n      }\n      return \"\";\n    }\n  }\n  getValueDate(value) {\n    let me = this;\n    // console.log(value)\n    return me.utilService.convertDateToString(new Date(value));\n  }\n  search(page, limit, sort, params) {\n    let me = this;\n    me.changeTable = false;\n    this.pageNumber = page;\n    this.pageSize = limit;\n    this.sort = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    Object.keys(this.searchInfo).forEach(key => {\n      if (this.searchInfo[key] != null) {\n        dataParams[key] = this.searchInfo[key];\n      }\n    });\n    this.dataSet = {\n      content: [],\n      total: 0\n    };\n    me.messageCommonService.onload();\n    this.ticketService.searchTicket(dataParams, response => {\n      me.dataSet = {\n        content: response.content,\n        total: response.totalElements\n      };\n      if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE || me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) {\n        let listAssigneeId = Array.from(new Set(me.dataSet.content.filter(item => item.assigneeId !== null).map(item => item.assigneeId)));\n        me.dataSet.content.forEach(item => {\n          if (item.updateBy !== null) {\n            listAssigneeId.push(item.updateBy);\n          }\n        });\n        const statusCheckListId = Array.from(new Set(listAssigneeId));\n        me.accountService.getListActivatedAccount(statusCheckListId, response => {\n          me.listActivatedAccount = response;\n          this.optionTable = {\n            hasClearSelected: false,\n            hasShowChoose: false,\n            hasShowIndex: true,\n            hasShowToggleColumn: false,\n            action: [{\n              icon: \"pi pi-info-circle\",\n              tooltip: this.tranService.translate(\"global.button.view\"),\n              func: function (id, item) {\n                me.handleDetailRequest(id, item);\n              }\n            }, {\n              icon: \"pi pi-window-maximize\",\n              tooltip: this.tranService.translate(\"global.button.edit\"),\n              func: function (id, item) {\n                me.handleEditRequest(id, item);\n              },\n              funcAppear: function (id, item) {\n                //   admin + kh khong cap nhat\n                if (me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER) return false;\n                if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE]) && (me.listActivatedAccount === undefined || me.listActivatedAccount == null)) return true;\n                if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE]) && item.assigneeId != null && me.listActivatedAccount.includes(item.assigneeId)) return false;\n                if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE]) && item.assigneeId == null && item.updatedBy != null && me.listActivatedAccount.includes(item.updatedBy) && item.updatedBy != me.userInfo.id) return false;\n                if (!item.updatedBy && !item.assigneeId && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;\n                if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE]) && (item.assigneeId != null && !me.listActivatedAccount.includes(item.assigneeId) || item.updatedBy != null && !me.listActivatedAccount.includes(item.updatedBy))) return true;\n                if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy !== me.userInfo.id || me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.assigneeId != null) {\n                  return false;\n                }\n                if (me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;else return false;\n              }\n            }]\n          };\n          me.changeTable = true;\n        });\n      }\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  resetTicket() {\n    this.ticket = {\n      id: null,\n      contactName: null,\n      contactEmail: null,\n      contactPhone: null,\n      content: null,\n      note: null,\n      cause: null,\n      type: CONSTANTS.REQUEST_TYPE.REPLACE_SIM,\n      sim: null,\n      status: null,\n      statusOld: null,\n      assigneeId: null,\n      provinceCode: null\n    };\n  }\n  onSubmitSearch() {\n    this.pageNumber = 0;\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n  }\n  getListProvince() {\n    this.accountService.getListProvince(response => {\n      this.listProvince = response.map(el => {\n        return {\n          ...el,\n          display: `${el.code} - ${el.name}`\n        };\n      });\n    });\n  }\n  getProvinceName(provinceCode) {\n    const province = this.listProvince.find(el => el.code === provinceCode);\n    return province ? province.code + ' - ' + province.name : \"\";\n  }\n  // tạo sửa yêu cầu\n  createOrUpdateRequest() {\n    if (this.messageCommonService.isloading == true || this.isShowCreateRequest == false) return;\n    let me = this;\n    this.messageCommonService.onload();\n    if (this.typeRequest == 'create') {\n      let bodySend = {\n        contactName: this.ticket.contactName,\n        contactEmail: this.ticket.contactEmail,\n        contactPhone: this.ticket.contactPhone,\n        content: this.ticket.content,\n        note: this.ticket.note,\n        type: this.ticket.type,\n        sim: this.ticket.type == 0 ? this.ticket.sim.join(',') : null\n      };\n      if (bodySend.contactPhone != null) {\n        if (bodySend.contactPhone.startsWith('0')) {\n          bodySend.contactPhone = \"84\" + bodySend.contactPhone.substring(1, bodySend.contactPhone.length);\n        } else if (bodySend.contactPhone.length == 9 || bodySend.contactPhone.length == 10) {\n          bodySend.contactPhone = \"84\" + bodySend.contactPhone;\n        }\n      }\n      this.ticketService.createTicket(bodySend, resp => {\n        me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n        me.isShowCreateRequest = false;\n        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n        // nếu KH đc gán cho GDV thì gửi mail cho GDV và danh sách admin đc cấu hình không thì chỉ gửi mail cho danh sách admin đc cấu hình\n        // get mail admin tinh dc cau hinh\n        me.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, resp1 => {\n          let array = [];\n          for (let info of resp1.emailInfos) {\n            array.push({\n              userId: info.userId,\n              ticketId: resp.id\n            });\n          }\n          if (resp?.assigneeId) {\n            array.push({\n              userId: resp.assigneeId,\n              ticketId: resp.id\n            });\n          }\n          me.ticketService.sendMailNotify(array);\n        });\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n    } else if (this.typeRequest == 'update') {\n      let bodySend = {\n        contactName: this.ticket.contactName,\n        contactEmail: this.ticket.contactEmail,\n        contactPhone: this.ticket.contactPhone,\n        content: this.ticket.content,\n        note: this.ticket.note,\n        type: this.ticket.type,\n        sim: this.ticket.type == 0 ? this.ticket.sim : null,\n        status: this.ticket.status,\n        cause: this.ticket.cause,\n        assigneeId: this.ticket.assigneeId,\n        listLog: this.listNotes\n      };\n      if (bodySend.contactPhone != null) {\n        if (bodySend.contactPhone.startsWith('0')) {\n          bodySend.contactPhone = \"84\" + bodySend.contactPhone.substring(1, bodySend.contactPhone.length);\n        } else if (bodySend.contactPhone.length == 9 || bodySend.contactPhone.length == 10) {\n          bodySend.contactPhone = \"84\" + bodySend.contactPhone;\n        }\n      }\n      // update ticket\n      this.ticketService.updateTicket(this.ticket.id, bodySend, resp => {\n        me.isShowCreateRequest = false;\n        me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n        if (resp.assigneeId != null && resp.assigneeId != undefined) {\n          me.ticketService.sendMailNotify([{\n            userId: resp.assigneeId,\n            ticketId: resp.id\n          }]);\n        }\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n    }\n  }\n  showModalCreate() {\n    this.isShowCreateRequest = true;\n    this.typeRequest = 'create';\n    this.titlePopup = this.tranService.translate('ticket.label.createRequest');\n    this.resetTicket();\n    // auto fill thong tin khi tao\n    if (this.userInfo.type === CONSTANTS.USER_TYPE.CUSTOMER) {\n      this.ticket.contactName = this.userInfo.fullName.substring(0, this.maxlengthContactName);\n      this.ticket.contactPhone = this.userInfo.phone;\n      this.ticket.contactEmail = this.userInfo.email;\n    }\n    this.formTicketSim = this.formBuilder.group(this.ticket);\n  }\n  handleEditRequest(id, item) {\n    let me = this;\n    this.formTicketSim.reset();\n    this.titlePopup = this.tranService.translate('ticket.label.updateRequest');\n    this.typeRequest = 'update';\n    this.isShowCreateRequest = true;\n    this.ticketService.getDetailTicket(item.id, resp => {\n      me.ticket = {\n        id: resp.id,\n        contactName: resp.contactName,\n        contactEmail: resp.contactEmail,\n        contactPhone: resp.contactPhone,\n        content: resp.content,\n        note: resp.note,\n        cause: resp.cause,\n        type: resp.type,\n        sim: resp.sim,\n        status: null,\n        statusOld: resp.status,\n        assigneeId: resp.assigneeId,\n        provinceCode: resp.provinceCode\n      };\n      me.oldTicket = {\n        ...me.ticket\n      };\n      me.formTicketSim = me.formBuilder.group(me.ticket);\n      // lấy list note\n      this.logHandleTicketService.search({\n        ticketId: item.id\n      }, res => {\n        console.log(res.content);\n        this.listNotes = res.content;\n        // for(let note of this.listNotes) {\n        //   this.mapForm[note.id] = this.formBuilder.group(note);\n        // }\n        this.listNotes.forEach(note => {\n          this.mapForm[note.id] = this.formBuilder.group({\n            content: ['', [Validators.required, Validators.maxLength(255), this.noWhitespaceValidator()]]\n          });\n        });\n        me.isShowTableNote = true;\n      });\n    });\n    this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, resp => {\n      me.listEmail = resp.emailInfos;\n    });\n  }\n  handleDetailRequest(id, item) {\n    let me = this;\n    this.formTicketSim.reset();\n    this.titlePopup = this.tranService.translate('ticket.label.viewDetailReplaceSim');\n    this.typeRequest = 'detail';\n    this.isShowCreateRequest = true;\n    this.ticketService.getDetailTicket(item.id, resp => {\n      me.ticket = {\n        id: resp.id,\n        contactName: resp.contactName,\n        contactEmail: resp.contactEmail,\n        contactPhone: resp.contactPhone,\n        content: resp.content,\n        note: resp.note,\n        cause: resp.cause,\n        type: resp.type,\n        sim: resp.sim,\n        status: null,\n        statusOld: resp.status,\n        assigneeId: resp.assigneeId,\n        provinceCode: resp.provinceCode\n      };\n      me.oldTicket = {\n        ...me.ticket\n      };\n      me.formTicketSim = me.formBuilder.group(me.ticket);\n      // lấy list note\n      this.logHandleTicketService.search({\n        ticketId: item.id\n      }, res => {\n        console.log(res.content);\n        this.listNotes = res.content;\n        for (let note of this.listNotes) {\n          this.mapForm[note.id] = this.formBuilder.group(note);\n        }\n        me.isShowTableNote = true;\n      });\n    });\n    this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, resp => {\n      me.listEmail = resp.emailInfos;\n    });\n  }\n  preventCharacter(event) {\n    if (event.ctrlKey) {\n      return;\n    }\n    if (event.keyCode == 8 || event.keyCode == 13 || event.keyCode == 37 || event.keyCode == 39) {\n      return;\n    }\n    if (event.keyCode < 48 || event.keyCode > 57) {\n      event.preventDefault();\n    }\n    // Chặn ký tự 'e', 'E' và dấu '+'\n    if (event.keyCode == 69 || event.keyCode == 101 || event.keyCode == 107 || event.keyCode == 187) {\n      event.preventDefault();\n    }\n  }\n  // onSimChangeAdd(listSim){\n  //   if(listSim == null || listSim == undefined|| listSim.length == 0) {\n  //     this.isValidChangeSim = true ;\n  //     return;\n  //   }\n  //   for (let sim of listSim) {\n  //     let regex = /^((\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$/;\n  //     console.log(regex.test(sim))\n  //     if(!regex.test(sim)) {\n  //       this.isValidChangeSim = false\n  //       return\n  //     }else {\n  //       this.isValidChangeSim = true\n  //     }\n  //   }\n  //\n  // }\n  onSimChangeAdd(listSim) {\n    if (!listSim || listSim.length === 0) {\n      this.isValidChangeSim = true; // không hiển thị lỗi khi chưa nhập\n      return;\n    }\n    this.isValidChangeSim = listSim.every(sim => {\n      sim = sim.trim();\n      const regex0 = /^0\\d{9,10}$/; // Số bắt đầu bằng 0, dài 10-11\n      const regex84 = /^84\\d{9,10}$/; // Số bắt đầu bằng 84, dài 11-12\n      return regex0.test(sim) || regex84.test(sim);\n    });\n  }\n  isFormValid() {\n    return Object.values(this.mapForm).every(formGroup => formGroup.valid);\n  }\n  noWhitespaceValidator() {\n    return control => {\n      const isWhitespace = (control.value || '').trim().length === 0;\n      const isValid = !isWhitespace;\n      return isValid ? null : {\n        whitespace: true\n      };\n    };\n  }\n  onKeyDownNote(event) {\n    if (event.key === ' ' && (this.ticket.note == null || this.ticket.note != null && this.ticket.note.trim() === '')) {\n      event.preventDefault();\n    }\n    if (this.ticket.note != null && this.ticket.note.trim() != '') {\n      this.ticket.note = this.ticket.note.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  onKeyDownContent(event) {\n    if (event.key === ' ' && (this.ticket.content == null || this.ticket.content != null && this.ticket.content.trim() === '')) {\n      event.preventDefault();\n    }\n    if (this.ticket.content != null && this.ticket.content.trim() != '') {\n      this.ticket.content = this.ticket.content.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  onKeyDownNoteContent(event, note) {\n    if (event.key === ' ' && (!note.content || note.content.trim() === '')) {\n      event.preventDefault();\n    }\n    if (note.content && note.content.trim() !== '') {\n      note.content = note.content.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  onKeyDownCause(event) {\n    if (event.key === ' ' && (this.ticket.cause == null || this.ticket.cause != null && this.ticket.cause.trim() === '')) {\n      event.preventDefault();\n    }\n    if (this.ticket.cause != null && this.ticket.cause.trim() != '') {\n      this.ticket.cause = this.ticket.cause.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  static {\n    this.ɵfac = function ListReplaceSimTicketComponent_Factory(t) {\n      return new (t || ListReplaceSimTicketComponent)(i0.ɵɵdirectiveInject(TicketService), i0.ɵɵdirectiveInject(AccountService), i0.ɵɵdirectiveInject(LogHandleTicketService), i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ListReplaceSimTicketComponent,\n      selectors: [[\"ticket-config-list\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 106,\n      vars: 71,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [\"styleClass\", \"p-button-info\", \"icon\", \"\", 3, \"label\", \"click\", 4, \"ngIf\"], [1, \"pt-3\", \"pb-2\", \"vnpt-field-set\", 3, \"formGroup\", \"ngSubmit\"], [3, \"toggleable\", \"header\"], [1, \"grid\", \"search-grid-4\"], [\"class\", \"col-3\", 4, \"ngIf\"], [1, \"col-3\"], [1, \"p-float-label\"], [\"styleClass\", \"w-full\", \"filterBy\", \"display\", \"id\", \"provinceCode\", \"formControlName\", \"status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"filterBy\", \"label\", 3, \"showClear\", \"filter\", \"autoDisplayFirst\", \"ngModel\", \"required\", \"options\", \"ngModelChange\"], [\"htmlFor\", \"status\", 1, \"label-dropdown\"], [\"pInputText\", \"\", \"id\", \"contactEmail\", \"formControlName\", \"contactEmail\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"email\"], [\"pInputText\", \"\", \"id\", \"contactPhone\", \"formControlName\", \"contactPhone\", \"type\", \"number\", \"min\", \"0\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\", \"keydown\"], [\"htmlFor\", \"contactPhone\"], [1, \"col-3\", \"pb-0\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"submit\"], [3, \"tableId\", \"fieldId\", \"columns\", \"dataSet\", \"options\", \"pageNumber\", \"loadData\", \"pageSize\", \"sort\", \"params\", \"labelTable\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\", \"dialog-vnpt\"], [3, \"breakpoints\", \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"mt-3\", 3, \"formGroup\", \"ngSubmit\"], [1, \"flex\", \"flex-row\", \"flex-wrap\", \"justify-content-between\", \"w-full\"], [\"class\", \"w-full field grid chart-grid\", 4, \"ngIf\"], [1, \"w-full\", \"field\", \"grid\", \"chart-grid\"], [\"htmlFor\", \"contactName\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [1, \"text-red-500\"], [1, \"col\", 2, \"width\", \"calc(100% - 180px)\", \"overflow-wrap\", \"break-word\"], [\"class\", \"w-full\", \"pInputText\", \"\", \"id\", \"contactName\", \"formControlName\", \"contactName\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"w-full\", \"field\", \"grid\", \"text-error-field\"], [\"htmlFor\", \"fullName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"col\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [\"htmlFor\", \"email\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"class\", \"w-full\", \"pInputText\", \"\", \"id\", \"contactEmail\", \"formControlName\", \"contactEmail\", \"pattern\", \"^[a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+)+(\\\\.[a-z]{2,})$\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\", 4, \"ngIf\"], [\"htmlFor\", \"email\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"htmlFor\", \"phone\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"class\", \"w-full\", \"pInputText\", \"\", \"id\", \"contactPhone\", \"formControlName\", \"contactPhone\", \"pattern\", \"^((\\\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\", \"keydown\", 4, \"ngIf\"], [\"class\", \"w-full field grid text-error-field\", 4, \"ngIf\"], [\"htmlFor\", \"content\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"class\", \"w-full\", \"style\", \"resize: none;\", \"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"content\", \"formControlName\", \"content\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"placeholder\", \"ngModelChange\", \"keydown\", 4, \"ngIf\"], [\"style\", \"word-break: break-all\", 4, \"ngIf\"], [\"htmlFor\", \"content\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"htmlFor\", \"note\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"class\", \"w-full\", \"style\", \"resize: none;\", \"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"note\", \"formControlName\", \"note\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"placeholder\", \"ngModelChange\", \"keydown\", 4, \"ngIf\"], [\"htmlFor\", \"note\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"class\", \"w-full field grid chart-grid\", 3, \"class\", 4, \"ngIf\"], [\"class\", \"flex flex-row justify-content-center align-items-center mt-3\", 4, \"ngIf\"], [\"styleClass\", \"p-button-info\", \"icon\", \"\", 3, \"label\", \"click\"], [\"styleClass\", \"w-full\", \"filterBy\", \"display\", \"id\", \"provinceCode\", \"formControlName\", \"provinceCode\", \"optionLabel\", \"display\", \"optionValue\", \"code\", 3, \"showClear\", \"filter\", \"autoDisplayFirst\", \"ngModel\", \"required\", \"options\", \"ngModelChange\"], [\"htmlFor\", \"provinceCode\", 1, \"label-dropdown\"], [3, \"tableId\", \"fieldId\", \"columns\", \"dataSet\", \"options\", \"pageNumber\", \"loadData\", \"pageSize\", \"sort\", \"params\", \"labelTable\"], [\"htmlFor\", \"contactName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"contactName\", \"formControlName\", \"contactName\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [\"pInputText\", \"\", \"id\", \"contactEmail\", \"formControlName\", \"contactEmail\", \"pattern\", \"^[a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+)+(\\\\.[a-z]{2,})$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [\"pInputText\", \"\", \"id\", \"contactPhone\", \"formControlName\", \"contactPhone\", \"pattern\", \"^((\\\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\", \"keydown\"], [\"htmlFor\", \"changeSim\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"display\", \"inline-block\"], [\"class\", \"w-full p-fluid\", \"formControlName\", \"sim\", \"separator\", \",\", 3, \"ngModel\", \"pTooltip\", \"ngModelChange\", \"keydown\", 4, \"ngIf\"], [\"class\", \"text-green-600 block mt-1\", 4, \"ngIf\"], [\"formControlName\", \"sim\", \"separator\", \",\", 1, \"w-full\", \"p-fluid\", 3, \"ngModel\", \"pTooltip\", \"ngModelChange\", \"keydown\"], [2, \"word-break\", \"break-all\"], [1, \"text-green-600\", \"block\", \"mt-1\"], [\"htmlFor\", \"numSub\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"class\", \"text-red-500 block\", 4, \"ngIf\"], [1, \"text-red-500\", \"block\"], [\"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"content\", \"formControlName\", \"content\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"placeholder\", \"ngModelChange\", \"keydown\"], [\"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"note\", \"formControlName\", \"note\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"placeholder\", \"ngModelChange\", \"keydown\"], [\"for\", \"type\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"col\", 2, \"max-width\", \"calc(100% - 180px)\", \"position\", \"relative\"], [\"objectKey\", \"account\", \"paramKey\", \"email\", \"keyReturn\", \"id\", \"displayPattern\", \"${email}\", 1, \"w-full\", 3, \"value\", \"placeholder\", \"disabled\", \"isMultiChoice\", \"paramDefault\", \"stylePositionBoxSelect\", \"valueChange\"], [\"htmlFor\", \"userType\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"for\", \"status\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"styleClass\", \"w-full\", \"id\", \"type\", \"formControlName\", \"status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"placeholder\", \"emptyMessage\", \"ngModelChange\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"styleClass\", \"w-full\", \"id\", \"type\", \"formControlName\", \"status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"placeholder\", \"emptyMessage\", \"ngModelChange\"], [\"pInputText\", \"\", \"id\", \"cause\", \"formControlName\", \"cause\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\", \"keydown\"], [\"htmlFor\", \"content\", 1, \"col-fixed\", \"font-medium\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [2, \"width\", \"100%\", 3, \"value\", \"tableStyle\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [2, \"min-width\", \"146px\"], [2, \"min-width\", \"155px\"], [3, \"formGroup\"], [\"class\", \"w-full\", \"pInputText\", \"\", \"id\", \"content\", \"formControlName\", \"content\", 3, \"ngModel\", \"required\", \"maxLength\", \"ngModelChange\", \"keydown\", 4, \"ngIf\"], [\"pInputText\", \"\", \"id\", \"content\", \"formControlName\", \"content\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"ngModelChange\", \"keydown\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"align-items-center\", \"mt-3\"], [\"styleClass\", \"mr-2 p-button-secondary\", 3, \"label\", \"click\"], [\"type\", \"submit\", \"styleClass\", \"p-button-info\", 3, \"disabled\", \"label\"]],\n      template: function ListReplaceSimTicketComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵtemplate(6, ListReplaceSimTicketComponent_p_button_6_Template, 1, 1, \"p-button\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"form\", 6);\n          i0.ɵɵlistener(\"ngSubmit\", function ListReplaceSimTicketComponent_Template_form_ngSubmit_7_listener() {\n            return ctx.onSubmitSearch();\n          });\n          i0.ɵɵelementStart(8, \"p-panel\", 7)(9, \"div\", 8);\n          i0.ɵɵtemplate(10, ListReplaceSimTicketComponent_div_10_Template, 5, 7, \"div\", 9);\n          i0.ɵɵelementStart(11, \"div\", 10)(12, \"span\", 11)(13, \"p-dropdown\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function ListReplaceSimTicketComponent_Template_p_dropdown_ngModelChange_13_listener($event) {\n            return ctx.searchInfo.status = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"label\", 13);\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 10)(17, \"span\", 11)(18, \"input\", 14);\n          i0.ɵɵlistener(\"ngModelChange\", function ListReplaceSimTicketComponent_Template_input_ngModelChange_18_listener($event) {\n            return ctx.searchInfo.contactEmail = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"label\", 15);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 10)(22, \"span\", 11)(23, \"input\", 16);\n          i0.ɵɵlistener(\"ngModelChange\", function ListReplaceSimTicketComponent_Template_input_ngModelChange_23_listener($event) {\n            return ctx.searchInfo.contactPhone = $event;\n          })(\"keydown\", function ListReplaceSimTicketComponent_Template_input_keydown_23_listener($event) {\n            return ctx.preventCharacter($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"label\", 17);\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"div\", 18);\n          i0.ɵɵelement(27, \"p-button\", 19);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(28, ListReplaceSimTicketComponent_table_vnpt_28_Template, 1, 11, \"table-vnpt\", 20);\n          i0.ɵɵtemplate(29, ListReplaceSimTicketComponent_table_vnpt_29_Template, 1, 11, \"table-vnpt\", 20);\n          i0.ɵɵelementStart(30, \"div\", 21)(31, \"p-dialog\", 22);\n          i0.ɵɵlistener(\"visibleChange\", function ListReplaceSimTicketComponent_Template_p_dialog_visibleChange_31_listener($event) {\n            return ctx.isShowCreateRequest = $event;\n          });\n          i0.ɵɵelementStart(32, \"form\", 23);\n          i0.ɵɵlistener(\"ngSubmit\", function ListReplaceSimTicketComponent_Template_form_ngSubmit_32_listener() {\n            return ctx.createOrUpdateRequest();\n          });\n          i0.ɵɵelementStart(33, \"div\", 24);\n          i0.ɵɵtemplate(34, ListReplaceSimTicketComponent_div_34_Template, 8, 2, \"div\", 25);\n          i0.ɵɵelementStart(35, \"div\", 26)(36, \"label\", 27);\n          i0.ɵɵtext(37);\n          i0.ɵɵelementStart(38, \"span\", 28);\n          i0.ɵɵtext(39, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 29);\n          i0.ɵɵtemplate(41, ListReplaceSimTicketComponent_input_41_Template, 1, 4, \"input\", 30);\n          i0.ɵɵtemplate(42, ListReplaceSimTicketComponent_span_42_Template, 2, 1, \"span\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"div\", 32);\n          i0.ɵɵelement(44, \"label\", 33);\n          i0.ɵɵelementStart(45, \"div\", 34);\n          i0.ɵɵtemplate(46, ListReplaceSimTicketComponent_small_46_Template, 2, 1, \"small\", 35);\n          i0.ɵɵtemplate(47, ListReplaceSimTicketComponent_small_47_Template, 2, 2, \"small\", 35);\n          i0.ɵɵtemplate(48, ListReplaceSimTicketComponent_small_48_Template, 2, 1, \"small\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"div\", 26)(50, \"label\", 36);\n          i0.ɵɵtext(51);\n          i0.ɵɵelementStart(52, \"span\", 28);\n          i0.ɵɵtext(53, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"div\", 29);\n          i0.ɵɵtemplate(55, ListReplaceSimTicketComponent_input_55_Template, 1, 4, \"input\", 37);\n          i0.ɵɵtemplate(56, ListReplaceSimTicketComponent_span_56_Template, 2, 1, \"span\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 32);\n          i0.ɵɵelement(58, \"label\", 38);\n          i0.ɵɵelementStart(59, \"div\", 34);\n          i0.ɵɵtemplate(60, ListReplaceSimTicketComponent_small_60_Template, 2, 1, \"small\", 35);\n          i0.ɵɵtemplate(61, ListReplaceSimTicketComponent_small_61_Template, 2, 2, \"small\", 35);\n          i0.ɵɵtemplate(62, ListReplaceSimTicketComponent_small_62_Template, 2, 1, \"small\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(63, \"div\", 26)(64, \"label\", 39);\n          i0.ɵɵtext(65);\n          i0.ɵɵelementStart(66, \"span\", 28);\n          i0.ɵɵtext(67, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"div\", 34);\n          i0.ɵɵtemplate(69, ListReplaceSimTicketComponent_input_69_Template, 1, 4, \"input\", 40);\n          i0.ɵɵtemplate(70, ListReplaceSimTicketComponent_span_70_Template, 2, 1, \"span\", 31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(71, \"div\", 32);\n          i0.ɵɵelement(72, \"label\", 39);\n          i0.ɵɵelementStart(73, \"div\", 34);\n          i0.ɵɵtemplate(74, ListReplaceSimTicketComponent_small_74_Template, 2, 1, \"small\", 35);\n          i0.ɵɵtemplate(75, ListReplaceSimTicketComponent_small_75_Template, 2, 1, \"small\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(76, ListReplaceSimTicketComponent_div_76_Template, 9, 4, \"div\", 25);\n          i0.ɵɵtemplate(77, ListReplaceSimTicketComponent_div_77_Template, 5, 2, \"div\", 41);\n          i0.ɵɵelementStart(78, \"div\", 26)(79, \"label\", 42);\n          i0.ɵɵtext(80);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"div\", 34);\n          i0.ɵɵtemplate(82, ListReplaceSimTicketComponent_textarea_82_Template, 1, 4, \"textarea\", 43);\n          i0.ɵɵtemplate(83, ListReplaceSimTicketComponent_span_83_Template, 2, 1, \"span\", 44);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(84, \"div\", 32);\n          i0.ɵɵelement(85, \"label\", 45);\n          i0.ɵɵelementStart(86, \"div\", 34);\n          i0.ɵɵtemplate(87, ListReplaceSimTicketComponent_small_87_Template, 2, 2, \"small\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(88, \"div\", 26)(89, \"label\", 46);\n          i0.ɵɵtext(90);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"div\", 34);\n          i0.ɵɵtemplate(92, ListReplaceSimTicketComponent_textarea_92_Template, 1, 4, \"textarea\", 47);\n          i0.ɵɵtemplate(93, ListReplaceSimTicketComponent_span_93_Template, 2, 1, \"span\", 44);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(94, \"div\", 32);\n          i0.ɵɵelement(95, \"label\", 48);\n          i0.ɵɵelementStart(96, \"div\", 34);\n          i0.ɵɵtemplate(97, ListReplaceSimTicketComponent_small_97_Template, 2, 2, \"small\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(98, ListReplaceSimTicketComponent_div_98_Template, 5, 9, \"div\", 25);\n          i0.ɵɵtemplate(99, ListReplaceSimTicketComponent_div_99_Template, 4, 1, \"div\", 41);\n          i0.ɵɵtemplate(100, ListReplaceSimTicketComponent_div_100_Template, 10, 9, \"div\", 49);\n          i0.ɵɵtemplate(101, ListReplaceSimTicketComponent_div_101_Template, 4, 1, \"div\", 41);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(102, ListReplaceSimTicketComponent_div_102_Template, 6, 8, \"div\", 49);\n          i0.ɵɵtemplate(103, ListReplaceSimTicketComponent_div_103_Template, 4, 1, \"div\", 41);\n          i0.ɵɵtemplate(104, ListReplaceSimTicketComponent_div_104_Template, 6, 4, \"div\", 25);\n          i0.ɵɵtemplate(105, ListReplaceSimTicketComponent_div_105_Template, 3, 3, \"div\", 50);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.menu.replaceSim\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo.type == ctx.userType.CUSTOMER && ctx.checkAuthen(i0.ɵɵpureFunction1(67, _c9, ctx.CONSTANTS.PERMISSIONS.TICKET.CREATE)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formSearchTicket);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"toggleable\", true)(\"header\", ctx.tranService.translate(\"global.text.filter\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo.type == ctx.userType.ADMIN);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"showClear\", true)(\"filter\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.searchInfo.status)(\"required\", false)(\"options\", ctx.listTicketStatus)(\"filter\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.status\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.contactEmail);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.email\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.contactPhone);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.phone\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.changeTable);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.changeTable);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(69, _c10));\n          i0.ɵɵproperty(\"breakpoints\", i0.ɵɵpureFunction0(70, _c11))(\"header\", ctx.titlePopup)(\"visible\", ctx.isShowCreateRequest)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formTicketSim);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo.type == ctx.userType.ADMIN && ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.customerName\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"create\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactName.dirty && (ctx.formTicketSim.controls.contactName.errors == null ? null : ctx.formTicketSim.controls.contactName.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactName.errors == null ? null : ctx.formTicketSim.controls.contactName.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactName.errors == null ? null : ctx.formTicketSim.controls.contactName.errors.pattern);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.email\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"create\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactEmail.dirty && (ctx.formTicketSim.controls.contactEmail.errors == null ? null : ctx.formTicketSim.controls.contactEmail.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactEmail.errors == null ? null : ctx.formTicketSim.controls.contactEmail.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactEmail.errors == null ? null : ctx.formTicketSim.controls.contactEmail.errors.pattern);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.phone\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"create\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactPhone.dirty && (ctx.formTicketSim.controls.contactPhone.errors == null ? null : ctx.formTicketSim.controls.contactPhone.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactPhone.errors == null ? null : ctx.formTicketSim.controls.contactPhone.errors.pattern);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.ticket.type == 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.ticket.type == 0);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.content\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"create\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.content.errors == null ? null : ctx.formTicketSim.controls.content.errors.maxLength);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.note\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"create\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.note.errors == null ? null : ctx.formTicketSim.controls.note.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" && ctx.userInfo.type == ctx.userType.PROVINCE && ctx.ticket.status == null && ctx.ticket.statusOld == 0 || ctx.userInfo.type == ctx.userType.PROVINCE && ctx.typeRequest == \"detail\" && ctx.ticket.assigneeId != null);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" && ctx.userInfo.type == ctx.userType.PROVINCE && ctx.ticket.status == null && ctx.ticket.statusOld == 0 || ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"detail\" && ctx.ticket.assigneeId == null);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"detail\" && (ctx.ticket.assigneeId == null || ctx.ticket.assigneeId != null && !ctx.listActivatedAccount.includes(ctx.ticket.assigneeId)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (ctx.typeRequest == \"update\" || ctx.typeRequest == \"detail\") && ctx.listNotes && ctx.listNotes.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest != \"detail\");\n        }\n      },\n      dependencies: [i2.NgIf, i3.Breadcrumb, i4.Tooltip, i5.PrimeTemplate, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MaxLengthValidator, i1.PatternValidator, i1.MinValidator, i1.FormGroupDirective, i1.FormControlName, i6.InputText, i7.Button, i8.TableVnptComponent, i9.VnptCombobox, i10.Dropdown, i11.Dialog, i12.InputTextarea, i13.Panel, i14.Chips, i15.Table, i2.DatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["TicketService", "CONSTANTS", "ComponentBase", "AccountService", "Validators", "LogHandleTicketService", "i0", "ɵɵelementStart", "ɵɵlistener", "ListReplaceSimTicketComponent_p_button_6_Template_p_button_click_0_listener", "ɵɵrestoreView", "_r36", "ctx_r35", "ɵɵnextContext", "ɵɵresetView", "showModalCreate", "ɵɵelementEnd", "ɵɵproperty", "ctx_r0", "tranService", "translate", "ListReplaceSimTicketComponent_div_10_Template_p_dropdown_ngModelChange_2_listener", "$event", "_r38", "ctx_r37", "searchInfo", "provinceCode", "ɵɵtext", "ɵɵadvance", "ctx_r1", "listProvince", "ɵɵtextInterpolate", "ɵɵelement", "ctx_r2", "columns", "dataSet", "optionTable", "pageNumber", "search", "bind", "pageSize", "sort", "ctx_r3", "ctx_r4", "getProvinceName", "ticket", "ListReplaceSimTicketComponent_input_41_Template_input_ngModelChange_0_listener", "_r40", "ctx_r39", "contactName", "ctx_r5", "maxlengthContactName", "ctx_r6", "ctx_r7", "ctx_r8", "ɵɵpureFunction0", "_c0", "ctx_r9", "ListReplaceSimTicketComponent_input_55_Template_input_ngModelChange_0_listener", "_r42", "ctx_r41", "contactEmail", "ctx_r10", "ctx_r11", "ctx_r12", "ctx_r13", "ctx_r14", "ListReplaceSimTicketComponent_input_69_Template_input_ngModelChange_0_listener", "_r44", "ctx_r43", "contactPhone", "ListReplaceSimTicketComponent_input_69_Template_input_keydown_0_listener", "ctx_r45", "preventCharacter", "ctx_r15", "ctx_r16", "ctx_r17", "ctx_r18", "ListReplaceSimTicketComponent_div_76_p_chips_6_Template_p_chips_ngModelChange_0_listener", "_r50", "ctx_r49", "sim", "ListReplaceSimTicketComponent_div_76_p_chips_6_Template_p_chips_keydown_0_listener", "ctx_r51", "ctx_r52", "onSimChangeAdd", "ɵɵpropertyInterpolate", "ctx_r46", "ctx_r47", "ɵɵtextInterpolate1", "ctx_r48", "ɵɵtemplate", "ListReplaceSimTicketComponent_div_76_p_chips_6_Template", "ListReplaceSimTicketComponent_div_76_span_7_Template", "ListReplaceSimTicketComponent_div_76_small_8_Template", "ctx_r19", "typeRequest", "isValidChangeSim", "formTicketSim", "controls", "value", "length", "ctx_r53", "ctx_r54", "ListReplaceSimTicketComponent_div_77_small_3_Template", "ListReplaceSimTicketComponent_div_77_small_4_Template", "ctx_r20", "dirty", "errors", "required", "ListReplaceSimTicketComponent_textarea_82_Template_textarea_ngModelChange_0_listener", "_r56", "ctx_r55", "content", "ListReplaceSimTicketComponent_textarea_82_Template_textarea_keydown_0_listener", "ctx_r57", "onKeyDownContent", "ctx_r21", "ctx_r22", "ctx_r23", "ListReplaceSimTicketComponent_textarea_92_Template_textarea_ngModelChange_0_listener", "_r59", "ctx_r58", "note", "ListReplaceSimTicketComponent_textarea_92_Template_textarea_keydown_0_listener", "ctx_r60", "onKeyDownNote", "ctx_r24", "ctx_r25", "ctx_r26", "ListReplaceSimTicketComponent_div_98_Template_vnpt_select_valueChange_4_listener", "_r62", "ctx_r61", "assigneeId", "ctx_r27", "_c1", "_c2", "ctx_r63", "ListReplaceSimTicketComponent_div_99_small_3_Template", "ctx_r28", "ListReplaceSimTicketComponent_div_100_p_dropdown_4_Template_p_dropdown_ngModelChange_0_listener", "_r71", "ctx_r70", "status", "ctx_r64", "statusOld", "mapTicketStatus", "listTicketStatus", "ɵɵclassMap", "_c3", "ctx_r65", "getValueStatus", "_c4", "ctx_r66", "_c5", "ctx_r67", "_c6", "ctx_r68", "_c7", "ctx_r69", "ListReplaceSimTicketComponent_div_100_p_dropdown_4_Template", "ListReplaceSimTicketComponent_div_100_span_5_Template", "ListReplaceSimTicketComponent_div_100_span_6_Template", "ListReplaceSimTicketComponent_div_100_span_7_Template", "ListReplaceSimTicketComponent_div_100_span_8_Template", "ListReplaceSimTicketComponent_div_100_span_9_Template", "ctx_r29", "userInfo", "type", "userType", "PROVINCE", "listActivatedAccount", "includes", "ctx_r72", "ListReplaceSimTicketComponent_div_101_small_3_Template", "ctx_r30", "ListReplaceSimTicketComponent_div_102_span_3_Template", "ListReplaceSimTicketComponent_div_102_Template_input_ngModelChange_5_listener", "_r75", "ctx_r74", "cause", "ListReplaceSimTicketComponent_div_102_Template_input_keydown_5_listener", "ctx_r76", "onKeyDownCause", "ctx_r31", "ctx_r77", "ListReplaceSimTicketComponent_div_103_small_3_Template", "ctx_r32", "trim", "ctx_r78", "ListReplaceSimTicketComponent_div_104_ng_template_5_input_11_Template_input_ngModelChange_0_listener", "_r88", "note_r80", "$implicit", "ListReplaceSimTicketComponent_div_104_ng_template_5_input_11_Template_input_keydown_0_listener", "ctx_r89", "onKeyDownNoteContent", "ctx_r84", "ctx_r85", "ListReplaceSimTicketComponent_div_104_ng_template_5_input_11_Template", "ListReplaceSimTicketComponent_div_104_ng_template_5_span_12_Template", "ListReplaceSimTicketComponent_div_104_ng_template_5_small_13_Template", "ListReplaceSimTicketComponent_div_104_ng_template_5_small_14_Template", "ctx_r79", "mapForm", "id", "i_r81", "userName", "ɵɵpipeBind2", "createdDate", "max<PERSON><PERSON><PERSON>", "ListReplaceSimTicketComponent_div_104_ng_template_4_Template", "ListReplaceSimTicketComponent_div_104_ng_template_5_Template", "ctx_r33", "listNotes", "_c8", "ListReplaceSimTicketComponent_div_105_Template_p_button_click_1_listener", "_r94", "ctx_r93", "isShowCreateRequest", "ctx_r34", "invalid", "isFormValid", "ListReplaceSimTicketComponent", "constructor", "ticketService", "accountService", "logHandleTicketService", "formBuilder", "injector", "oldTicket", "isShowTableNote", "titlePopup", "changeTable", "ngOnInit", "me", "sessionService", "USER_TYPE", "REQUEST_TYPE", "REPLACE_SIM", "listTicketType", "label", "email", "name", "key", "size", "align", "isShow", "ADMIN", "isSort", "isShowTooltip", "style", "display", "max<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "funcConvertText", "utilService", "convertDateToString", "Date", "width", "cursor", "funcGetClassname", "REQUEST_STATUS", "NEW", "RECEIVED", "IN_PROGRESS", "REJECT", "DONE", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "action", "icon", "tooltip", "func", "item", "handleDetailRequest", "handleEditRequest", "funcAppear", "CUSTOMER", "updatedBy", "<PERSON><PERSON><PERSON><PERSON>", "PERMISSIONS", "TICKET", "UPDATE", "total", "formSearchTicket", "group", "getListProvince", "getValueDate", "page", "limit", "params", "dataParams", "Object", "keys", "for<PERSON>ach", "messageCommonService", "onload", "searchTicket", "response", "totalElements", "DISTRICT", "listAssigneeId", "Array", "from", "Set", "filter", "map", "updateBy", "push", "statusCheckListId", "getListActivatedAccount", "undefined", "offload", "resetTicket", "onSubmitSearch", "el", "code", "province", "find", "createOrUpdateRequest", "isloading", "bodySend", "join", "startsWith", "substring", "createTicket", "resp", "success", "getDetailTicketConfig", "resp1", "array", "info", "emailInfos", "userId", "ticketId", "sendMailNotify", "listLog", "updateTicket", "fullName", "phone", "reset", "getDetailTicket", "res", "console", "log", "noWhitespaceValidator", "listEmail", "event", "ctrl<PERSON>ey", "keyCode", "preventDefault", "listSim", "every", "regex0", "regex84", "test", "values", "formGroup", "valid", "control", "isWhitespace", "<PERSON><PERSON><PERSON><PERSON>", "whitespace", "trimStart", "replace", "ɵɵdirectiveInject", "i1", "FormBuilder", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "ListReplaceSimTicketComponent_Template", "rf", "ctx", "ListReplaceSimTicketComponent_p_button_6_Template", "ListReplaceSimTicketComponent_Template_form_ngSubmit_7_listener", "ListReplaceSimTicketComponent_div_10_Template", "ListReplaceSimTicketComponent_Template_p_dropdown_ngModelChange_13_listener", "ListReplaceSimTicketComponent_Template_input_ngModelChange_18_listener", "ListReplaceSimTicketComponent_Template_input_ngModelChange_23_listener", "ListReplaceSimTicketComponent_Template_input_keydown_23_listener", "ListReplaceSimTicketComponent_table_vnpt_28_Template", "ListReplaceSimTicketComponent_table_vnpt_29_Template", "ListReplaceSimTicketComponent_Template_p_dialog_visibleChange_31_listener", "ListReplaceSimTicketComponent_Template_form_ngSubmit_32_listener", "ListReplaceSimTicketComponent_div_34_Template", "ListReplaceSimTicketComponent_input_41_Template", "ListReplaceSimTicketComponent_span_42_Template", "ListReplaceSimTicketComponent_small_46_Template", "ListReplaceSimTicketComponent_small_47_Template", "ListReplaceSimTicketComponent_small_48_Template", "ListReplaceSimTicketComponent_input_55_Template", "ListReplaceSimTicketComponent_span_56_Template", "ListReplaceSimTicketComponent_small_60_Template", "ListReplaceSimTicketComponent_small_61_Template", "ListReplaceSimTicketComponent_small_62_Template", "ListReplaceSimTicketComponent_input_69_Template", "ListReplaceSimTicketComponent_span_70_Template", "ListReplaceSimTicketComponent_small_74_Template", "ListReplaceSimTicketComponent_small_75_Template", "ListReplaceSimTicketComponent_div_76_Template", "ListReplaceSimTicketComponent_div_77_Template", "ListReplaceSimTicketComponent_textarea_82_Template", "ListReplaceSimTicketComponent_span_83_Template", "ListReplaceSimTicketComponent_small_87_Template", "ListReplaceSimTicketComponent_textarea_92_Template", "ListReplaceSimTicketComponent_span_93_Template", "ListReplaceSimTicketComponent_small_97_Template", "ListReplaceSimTicketComponent_div_98_Template", "ListReplaceSimTicketComponent_div_99_Template", "ListReplaceSimTicketComponent_div_100_Template", "ListReplaceSimTicketComponent_div_101_Template", "ListReplaceSimTicketComponent_div_102_Template", "ListReplaceSimTicketComponent_div_103_Template", "ListReplaceSimTicketComponent_div_104_Template", "ListReplaceSimTicketComponent_div_105_Template", "items", "home", "ɵɵpureFunction1", "_c9", "CREATE", "ɵɵstyleMap", "_c10", "_c11", "pattern"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\ticket\\list\\app.list.replace-sim.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\ticket\\list\\app.list.replace-sim.component.html"], "sourcesContent": ["import {Component, Inject, Injector, OnInit} from \"@angular/core\";\r\nimport {MenuItem} from \"primeng/api\";\r\nimport {TicketService} from \"src/app/service/ticket/TicketService\";\r\nimport {ColumnInfo, OptionTable} from \"../../common-module/table/table.component\";\r\nimport {CONSTANTS} from \"src/app/service/comon/constants\";\r\nimport {ComponentBase} from \"src/app/component.base\";\r\nimport {AccountService} from \"../../../service/account/AccountService\";\r\nimport {AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators} from \"@angular/forms\";\r\nimport {LogHandleTicketService} from \"../../../service/ticket/LogHandleTicketService\";\r\n\r\n@Component({\r\n  selector: \"ticket-config-list\",\r\n  templateUrl: './app.list.replace-sim.component.html'\r\n})\r\nexport class ListReplaceSimTicketComponent extends ComponentBase implements OnInit {\r\n  items: MenuItem[];\r\n  home: MenuItem\r\n  searchInfo: {\r\n    provinceCode: string | null,\r\n    email: string | null,\r\n    contactPhone: string | null,\r\n    contactEmail: string | null,\r\n    type: number | null,\r\n    status: number | null,\r\n  };\r\n  columns: Array<ColumnInfo>;\r\n  dataSet: {\r\n    content: Array<any>,\r\n    total: number\r\n  };\r\n  selectItems: Array<any>;\r\n  optionTable: OptionTable;\r\n  pageNumber: number;\r\n  pageSize: number;\r\n  sort: string;\r\n  formSearchTicket: any;\r\n  listProvince: Array<any>;\r\n  listTicketType: Array<any>;\r\n  listTicketStatus: Array<any>;\r\n  mapTicketStatus: any;\r\n  listEmail: Array<any>;\r\n  isShowCreateRequest: boolean;\r\n  formTicketSim: any;\r\n  listActivatedAccount: number[];\r\n  ticket: {\r\n    id: number\r\n    contactName: string | null,\r\n    contactEmail: string | null,\r\n    contactPhone: string | null,\r\n    content: string | null,\r\n    note: string | null,\r\n    cause: string | null,\r\n    type: number | null, // 0: thay thế sim, 1: test sim\r\n    sim: any\r\n    status: number | null,\r\n    statusOld?: number | null,\r\n    assigneeId: number | null,\r\n    provinceCode: string | null,\r\n  };\r\n  maxlengthContactName: number = 50;\r\n  typeRequest: string\r\n  userInfo: any\r\n  userType: any\r\n  isValidChangeSim : boolean = true\r\n  oldTicket : any = {}\r\n  listNotes : Array<any>\r\n  isShowTableNote : boolean = false;\r\n  mapForm : any = {}\r\n  titlePopup : string = ''\r\n  changeTable: boolean = false;\r\n  constructor(\r\n      @Inject(TicketService) private ticketService: TicketService,\r\n      @Inject(AccountService) private accountService: AccountService,\r\n      @Inject(LogHandleTicketService) private logHandleTicketService: LogHandleTicketService,\r\n      private formBuilder: FormBuilder,\r\n      private injector: Injector) {\r\n    super(injector);\r\n  }\r\n\r\n  ngOnInit() {\r\n    let me = this;\r\n    me.changeTable = false;\r\n    this.userInfo = this.sessionService.userInfo;\r\n    this.isShowCreateRequest = false;\r\n    this.typeRequest = 'create'\r\n    this.userType = CONSTANTS.USER_TYPE;\r\n    this.listNotes = [];\r\n    this.ticket = {\r\n      id: null,\r\n      contactName: null,\r\n      contactEmail: null,\r\n      contactPhone: null,\r\n      content: null,\r\n      note: null,\r\n      cause: null,\r\n      type: CONSTANTS.REQUEST_TYPE.REPLACE_SIM, // 0: thay thế sim, 1: test sim\r\n      sim: null,\r\n      status: null,\r\n      statusOld: null,\r\n      assigneeId: null,\r\n      provinceCode: null,\r\n    };\r\n    this.listTicketType = [\r\n      {\r\n        label: this.tranService.translate('ticket.type.replaceSim'),\r\n        value: 0\r\n      }\r\n    ]\r\n    this.mapTicketStatus = {\r\n      0: [{\r\n        label: me.tranService.translate('ticket.status.received'),\r\n        value: 1\r\n      },\r\n      {\r\n          label: me.tranService.translate('ticket.status.reject'),\r\n          value: 3\r\n      },\r\n      ],\r\n      1: [\r\n        {\r\n          label: me.tranService.translate('ticket.status.inProgress'),\r\n          value: 2\r\n        },\r\n        {\r\n          label: me.tranService.translate('ticket.status.reject'),\r\n          value: 3\r\n        }\r\n      ],\r\n      2: [\r\n        {\r\n          label: me.tranService.translate('ticket.status.done'),\r\n          value: 4\r\n        },\r\n        {\r\n          label: me.tranService.translate('ticket.status.reject'),\r\n          value: 3\r\n        }\r\n      ]\r\n    }\r\n    this.listTicketStatus = [\r\n      {\r\n        label: me.tranService.translate('ticket.status.new'),\r\n        value: 0\r\n      },\r\n      {\r\n        label: me.tranService.translate('ticket.status.received'),\r\n        value: 1\r\n      },\r\n      {\r\n        label: me.tranService.translate('ticket.status.inProgress'),\r\n        value: 2\r\n      },\r\n      {\r\n        label: me.tranService.translate('ticket.status.reject'),\r\n        value: 3\r\n      },\r\n      {\r\n        label: me.tranService.translate('ticket.status.done'),\r\n        value: 4\r\n      }\r\n    ]\r\n    this.searchInfo = {\r\n      provinceCode: null,\r\n      email: null,\r\n      contactPhone: null,\r\n      contactEmail: null,\r\n      type: CONSTANTS.REQUEST_TYPE.REPLACE_SIM,\r\n      status: null\r\n    }\r\n    this.columns = [\r\n      {\r\n        name: this.tranService.translate(\"ticket.label.province\"),\r\n        key: \"provinceName\",\r\n        size: \"150px\",\r\n        align: \"left\",\r\n        isShow: this.userInfo.type == CONSTANTS.USER_TYPE.ADMIN,\r\n        isSort: true\r\n      },\r\n      {\r\n        name: this.tranService.translate(\"ticket.label.customerName\"),\r\n        key: \"contactName\",\r\n        size: \"150px\",\r\n        align: \"left\",\r\n        isShow: true,\r\n        isSort: true,\r\n          isShowTooltip: true,\r\n          style: {\r\n              display: 'inline-block',\r\n              maxWidth: '350px',\r\n              overflow: 'hidden',\r\n              textOverflow: 'ellipsis'\r\n          }\r\n      }, {\r\n        name: this.tranService.translate(\"ticket.label.email\"),\r\n        key: \"contactEmail\",\r\n        size: \"150px\",\r\n        align: \"left\",\r\n        isShow: true,\r\n        isSort: true,\r\n            isShowTooltip: true,\r\n            style: {\r\n                display: 'inline-block',\r\n                maxWidth: '350px',\r\n                overflow: 'hidden',\r\n                textOverflow: 'ellipsis'\r\n            }\r\n      }, {\r\n        name: this.tranService.translate(\"ticket.label.phone\"),\r\n        key: \"contactPhone\",\r\n        size: \"fit-content\",\r\n        align: \"left\",\r\n        isShow: true,\r\n        isSort: true\r\n      }, {\r\n        name: this.tranService.translate(\"ticket.label.content\"),\r\n        key: \"content\",\r\n        size: \"fit-content\",\r\n        align: \"left\",\r\n        isShow: true,\r\n        isSort: true,\r\n        isShowTooltip: true,\r\n        style: {\r\n          display: 'inline-block',\r\n          maxWidth: '350px',\r\n          overflow: 'hidden',\r\n          textOverflow: 'ellipsis'\r\n        }\r\n      },\r\n      {\r\n        name: this.tranService.translate(\"ticket.label.createdDate\"),\r\n        key: \"createdDate\",\r\n        size: \"fit-content\",\r\n        align: \"left\",\r\n        isShow: true,\r\n        isSort: true,\r\n        funcConvertText(value) {\r\n          return me.utilService.convertDateToString(new Date(value))\r\n        },\r\n      },\r\n      {\r\n        name: this.tranService.translate(\"ticket.label.updatedDate\"),\r\n        key: \"updatedDate\",\r\n        size: \"fit-content\",\r\n        align: \"left\",\r\n        isShow: true,\r\n        isSort: true,\r\n        funcConvertText(value) {\r\n          return value ? me.utilService.convertDateToString(new Date(value)) : \"\"\r\n        }\r\n      },\r\n      {\r\n        name: this.tranService.translate(\"ticket.label.updateBy\"),\r\n        key: \"updatedByName\",\r\n        size: \"fit-content\",\r\n        align: \"left\",\r\n        isShowTooltip: true,\r\n        isShow: true,\r\n        isSort: true,\r\n          style:{\r\n            width: '150px',\r\n              cursor: 'pointer',\r\n          }\r\n      },\r\n      {\r\n        name: this.tranService.translate(\"ticket.label.status\"),\r\n        key: \"status\",\r\n        size: \"fit-content\",\r\n        align: \"left\",\r\n        isShow: true,\r\n        isSort: true,\r\n        funcGetClassname: (value) => {\r\n          if (value == CONSTANTS.REQUEST_STATUS.NEW) {\r\n            return ['p-2', 'text-white', \"bg-cyan-300\", \"border-round\", \"inline-block\"];\r\n          } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\r\n            return ['p-2', 'text-white', \"bg-bluegray-500\", \"border-round\", \"inline-block\"];\r\n          } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\r\n            return ['p-2', 'text-white', \"bg-orange-400\", \"border-round\", \"inline-block\"];\r\n          } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\r\n            return ['p-2', 'text-white', \"bg-red-500\", \"border-round\", \"inline-block\"];\r\n          } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\r\n            return ['p-2', 'text-white', \"bg-green-500\", \"border-round\", \"inline-block\"];\r\n          }\r\n          return '';\r\n        },\r\n        funcConvertText: function (value) {\r\n          if (value == CONSTANTS.REQUEST_STATUS.NEW) {\r\n            return me.tranService.translate(\"ticket.status.new\");\r\n          } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\r\n            return me.tranService.translate(\"ticket.status.received\");\r\n          } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\r\n            return me.tranService.translate(\"ticket.status.inProgress\");\r\n          } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\r\n            return me.tranService.translate(\"ticket.status.reject\");\r\n          } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\r\n            return me.tranService.translate(\"ticket.status.done\");\r\n          }\r\n          return \"\";\r\n        }\r\n      }\r\n    ];\r\n\r\n    this.optionTable = {\r\n      hasClearSelected: false,\r\n      hasShowChoose: false,\r\n      hasShowIndex: true,\r\n      hasShowToggleColumn: false,\r\n      action: [\r\n        {\r\n          icon: \"pi pi-info-circle\",\r\n          tooltip: this.tranService.translate(\"global.button.view\"),\r\n          func: function (id, item) {\r\n            me.handleDetailRequest(id, item)\r\n          }\r\n        },\r\n        {\r\n          icon: \"pi pi-window-maximize\",\r\n          tooltip: this.tranService.translate(\"global.button.edit\"),\r\n          func: function (id, item) {\r\n            me.handleEditRequest(id, item)\r\n          },\r\n          funcAppear : function (id, item) {\r\n            if(me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER) return false;\r\n            if(!item.updatedBy && !item.assigneeId && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;\r\n            if((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy !== me.userInfo.id) ||\r\n                (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.assigneeId != null) ) {\r\n              return false;\r\n            }\r\n            if (me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true\r\n              else return false;\r\n          }\r\n        }\r\n      ]\r\n    }\r\n    this.pageNumber = 0;\r\n    this.pageSize = 10;\r\n    this.sort = \"createdDate,desc\";\r\n    this.dataSet = {\r\n      content: [],\r\n      total: 0\r\n    }\r\n    this.formSearchTicket = this.formBuilder.group(this.searchInfo);\r\n    this.formTicketSim = this.formBuilder.group(this.ticket);\r\n    this.getListProvince();\r\n    this.listActivatedAccount = [];\r\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n  }\r\n\r\n  getValueStatus(value) {\r\n    let me = this;\r\n    {\r\n      if (value == CONSTANTS.REQUEST_STATUS.NEW) {\r\n        return me.tranService.translate(\"ticket.status.new\");\r\n      } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\r\n        return me.tranService.translate(\"ticket.status.received\");\r\n      } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\r\n        return me.tranService.translate(\"ticket.status.inProgress\");\r\n      } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\r\n        return me.tranService.translate(\"ticket.status.reject\");\r\n      } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\r\n        return me.tranService.translate(\"ticket.status.done\");\r\n      }\r\n      return \"\";\r\n    }\r\n  }\r\n\r\n  getValueDate(value) {\r\n    let me = this;\r\n    // console.log(value)\r\n    return me.utilService.convertDateToString(new Date(value))\r\n  }\r\n\r\n  search(page, limit, sort, params) {\r\n    let me = this;\r\n    me.changeTable = false;\r\n    this.pageNumber = page;\r\n    this.pageSize = limit;\r\n    this.sort = sort;\r\n    let dataParams = {\r\n      page,\r\n      size: limit,\r\n      sort\r\n    }\r\n    Object.keys(this.searchInfo).forEach(key => {\r\n      if (this.searchInfo[key] != null) {\r\n        dataParams[key] = this.searchInfo[key];\r\n      }\r\n    })\r\n    this.dataSet = {\r\n      content: [],\r\n      total: 0\r\n    }\r\n    me.messageCommonService.onload();\r\n    this.ticketService.searchTicket(dataParams, (response) => {\r\n      me.dataSet = {\r\n        content: response.content,\r\n        total: response.totalElements\r\n      }\r\n      if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE ||\r\n                me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) {\r\n                let listAssigneeId = Array.from(new Set(me.dataSet.content.filter(item => item.assigneeId !== null)\r\n                    .map(item => item.assigneeId as number)));\r\n\r\n                me.dataSet.content.forEach(item => {\r\n                    if (item.updateBy !== null) {\r\n                        listAssigneeId.push(item.updateBy as number);\r\n                    }\r\n                });\r\n\r\n                const statusCheckListId = Array.from(new Set(listAssigneeId));\r\n\r\n                me.accountService.getListActivatedAccount(statusCheckListId, (response) => {\r\n                    me.listActivatedAccount = response;\r\n                    this.optionTable = {\r\n      hasClearSelected: false,\r\n      hasShowChoose: false,\r\n      hasShowIndex: true,\r\n      hasShowToggleColumn: false,\r\n      action: [\r\n        {\r\n          icon: \"pi pi-info-circle\",\r\n          tooltip: this.tranService.translate(\"global.button.view\"),\r\n          func: function (id, item) {\r\n            me.handleDetailRequest(id, item)\r\n          }\r\n        },\r\n        {\r\n          icon: \"pi pi-window-maximize\",\r\n          tooltip: this.tranService.translate(\"global.button.edit\"),\r\n          func: function (id, item) {\r\n            me.handleEditRequest(id, item)\r\n          },\r\n          funcAppear : function (id, item) {\r\n            //   admin + kh khong cap nhat\r\n            if(me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER) return false;\r\n            if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE]) && ( me.listActivatedAccount === undefined || me.listActivatedAccount == null )) return true;\r\n            if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) && ((item.assigneeId != null && me.listActivatedAccount.includes(item.assigneeId)))) return false;\r\n            if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) && ((item.assigneeId == null && item.updatedBy != null && me.listActivatedAccount.includes(item.updatedBy) && item.updatedBy != me.userInfo.id))) return false;\r\n            if(!item.updatedBy && !item.assigneeId && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;\r\n            if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) && ((item.assigneeId != null && !me.listActivatedAccount.includes(item.assigneeId)) || (item.updatedBy != null && !me.listActivatedAccount.includes(item.updatedBy)))) return true;\r\n            if((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy !== me.userInfo.id) ||\r\n                (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.assigneeId != null) ) {\r\n              return false;\r\n            }\r\n            if (me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true\r\n              else return false;\r\n          }\r\n        }\r\n      ]\r\n    }\r\n    me.changeTable = true\r\n                })\r\n            }\r\n    }, null, () => {\r\n      me.messageCommonService.offload();\r\n    })\r\n  }\r\n\r\n  resetTicket() {\r\n    this.ticket = {\r\n      id: null,\r\n      contactName: null,\r\n      contactEmail: null,\r\n      contactPhone: null,\r\n      content: null,\r\n      note: null,\r\n      cause: null,\r\n      type: CONSTANTS.REQUEST_TYPE.REPLACE_SIM, // 0: thay thế sim, 1: test sim\r\n      sim: null,\r\n      status: null,\r\n      statusOld: null,\r\n      assigneeId: null,\r\n      provinceCode: null,\r\n    };\r\n  }\r\n\r\n  onSubmitSearch() {\r\n    this.pageNumber = 0;\r\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n  }\r\n\r\n  getListProvince() {\r\n    this.accountService.getListProvince((response) => {\r\n      this.listProvince = response.map(el => {\r\n        return {\r\n          ...el,\r\n          display: `${el.code} - ${el.name}`\r\n        }\r\n      })\r\n    })\r\n  }\r\n    getProvinceName(provinceCode) {\r\n        const province = this.listProvince.find(el => el.code === provinceCode);\r\n        return province ? province.code + ' - ' + province.name : \"\";\r\n    }\r\n\r\n  // tạo sửa yêu cầu\r\n  createOrUpdateRequest() {\r\n    if(this.messageCommonService.isloading == true || this.isShowCreateRequest == false) return;\r\n    let me = this;\r\n    this.messageCommonService.onload()\r\n    if (this.typeRequest == 'create') {\r\n      let bodySend = {\r\n        contactName: this.ticket.contactName,\r\n        contactEmail: this.ticket.contactEmail,\r\n        contactPhone: this.ticket.contactPhone,\r\n        content: this.ticket.content,\r\n        note: this.ticket.note,\r\n        type: this.ticket.type,\r\n        sim: this.ticket.type == 0 ? this.ticket.sim.join(',') : null\r\n      }\r\n      if (bodySend.contactPhone != null){\r\n        if(bodySend.contactPhone.startsWith('0')){\r\n          bodySend.contactPhone = \"84\"+bodySend.contactPhone.substring(1, bodySend.contactPhone.length);\r\n        }else if(bodySend.contactPhone.length == 9 || bodySend.contactPhone.length == 10){\r\n          bodySend.contactPhone = \"84\"+bodySend.contactPhone;\r\n        }\r\n      }\r\n      this.ticketService.createTicket(bodySend, (resp) => {\r\n        me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n        me.isShowCreateRequest = false\r\n        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)\r\n        // nếu KH đc gán cho GDV thì gửi mail cho GDV và danh sách admin đc cấu hình không thì chỉ gửi mail cho danh sách admin đc cấu hình\r\n        // get mail admin tinh dc cau hinh\r\n        me.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp1) => {\r\n          let array = []\r\n          for (let info of resp1.emailInfos) {\r\n            array.push({\r\n              userId: info.userId,\r\n              ticketId: resp.id\r\n            })\r\n          }\r\n          if(resp?.assigneeId) {\r\n            array.push({\r\n              userId: resp.assigneeId,\r\n              ticketId: resp.id\r\n            })\r\n          }\r\n          me.ticketService.sendMailNotify(array);\r\n        })\r\n      }, null, () => {\r\n        me.messageCommonService.offload()\r\n      })\r\n    } else if (this.typeRequest == 'update') {\r\n      let bodySend = {\r\n        contactName: this.ticket.contactName,\r\n        contactEmail: this.ticket.contactEmail,\r\n        contactPhone: this.ticket.contactPhone,\r\n        content: this.ticket.content,\r\n        note: this.ticket.note,\r\n        type: this.ticket.type,\r\n        sim: this.ticket.type == 0 ? this.ticket.sim : null,\r\n        status: this.ticket.status,\r\n        cause: this.ticket.cause,\r\n        assigneeId: this.ticket.assigneeId,\r\n        listLog : this.listNotes\r\n      }\r\n      if (bodySend.contactPhone != null){\r\n        if(bodySend.contactPhone.startsWith('0')){\r\n          bodySend.contactPhone = \"84\"+bodySend.contactPhone.substring(1, bodySend.contactPhone.length);\r\n        }else if(bodySend.contactPhone.length == 9 || bodySend.contactPhone.length == 10){\r\n          bodySend.contactPhone = \"84\"+bodySend.contactPhone;\r\n        }\r\n      }\r\n      // update ticket\r\n      this.ticketService.updateTicket(this.ticket.id, bodySend, (resp) => {\r\n        me.isShowCreateRequest = false\r\n        me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)\r\n        if(resp.assigneeId != null && resp.assigneeId != undefined) {\r\n          me.ticketService.sendMailNotify([{\r\n            userId: resp.assigneeId,\r\n            ticketId: resp.id\r\n          }])\r\n        }\r\n\r\n      }, null, () => {\r\n        me.messageCommonService.offload()\r\n      })\r\n    }\r\n  }\r\n\r\n  showModalCreate() {\r\n    this.isShowCreateRequest = true\r\n    this.typeRequest = 'create'\r\n    this.titlePopup = this.tranService.translate('ticket.label.createRequest')\r\n    this.resetTicket()\r\n    // auto fill thong tin khi tao\r\n    if (this.userInfo.type === CONSTANTS.USER_TYPE.CUSTOMER) {\r\n      this.ticket.contactName = this.userInfo.fullName.substring(0, this.maxlengthContactName);\r\n      this.ticket.contactPhone = this.userInfo.phone;\r\n      this.ticket.contactEmail = this.userInfo.email;\r\n    }\r\n    this.formTicketSim = this.formBuilder.group(this.ticket)\r\n  }\r\n\r\n  handleEditRequest(id, item) {\r\n    let me = this\r\n    this.formTicketSim.reset()\r\n    this.titlePopup = this.tranService.translate('ticket.label.updateRequest')\r\n    this.typeRequest = 'update'\r\n    this.isShowCreateRequest = true;\r\n    this.ticketService.getDetailTicket(item.id, (resp) => {\r\n      me.ticket = {\r\n        id: resp.id,\r\n        contactName: resp.contactName,\r\n        contactEmail: resp.contactEmail,\r\n        contactPhone: resp.contactPhone,\r\n        content: resp.content,\r\n        note: resp.note,\r\n        cause: resp.cause,\r\n        type: resp.type, // 0: thay thế sim, 1: test sim\r\n        sim: resp.sim,\r\n        status: null,\r\n        statusOld: resp.status,\r\n        assigneeId: resp.assigneeId,\r\n        provinceCode: resp.provinceCode,\r\n      }\r\n      me.oldTicket = {...me.ticket}\r\n      me.formTicketSim = me.formBuilder.group(me.ticket)\r\n      // lấy list note\r\n      this.logHandleTicketService.search({ticketId: item.id}, (res)=> {\r\n        console.log(res.content)\r\n        this.listNotes = res.content;\r\n        // for(let note of this.listNotes) {\r\n        //   this.mapForm[note.id] = this.formBuilder.group(note);\r\n        // }\r\n          this.listNotes.forEach(note => {\r\n              this.mapForm[note.id] = this.formBuilder.group({\r\n                  content: ['', [Validators.required, Validators.maxLength(255), this.noWhitespaceValidator()]]\r\n              });\r\n          });\r\n        me.isShowTableNote = true;\r\n      })\r\n    })\r\n    this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp) => {\r\n      me.listEmail = resp.emailInfos;\r\n    })\r\n  }\r\n\r\n  handleDetailRequest(id, item) {\r\n    let me = this\r\n    this.formTicketSim.reset()\r\n    this.titlePopup = this.tranService.translate('ticket.label.viewDetailReplaceSim')\r\n    this.typeRequest = 'detail'\r\n    this.isShowCreateRequest = true;\r\n    this.ticketService.getDetailTicket(item.id, (resp) => {\r\n      me.ticket = {\r\n        id: resp.id,\r\n        contactName: resp.contactName,\r\n        contactEmail: resp.contactEmail,\r\n        contactPhone: resp.contactPhone,\r\n        content: resp.content,\r\n        note: resp.note,\r\n        cause: resp.cause,\r\n        type: resp.type, // 0: thay thế sim, 1: test sim\r\n        sim: resp.sim,\r\n        status: null,\r\n        statusOld: resp.status,\r\n        assigneeId: resp.assigneeId,\r\n          provinceCode: resp.provinceCode,\r\n\r\n      }\r\n      me.oldTicket = {...me.ticket}\r\n      me.formTicketSim = me.formBuilder.group(me.ticket)\r\n      // lấy list note\r\n      this.logHandleTicketService.search({ticketId: item.id}, (res)=> {\r\n        console.log(res.content)\r\n        this.listNotes = res.content;\r\n        for(let note of this.listNotes) {\r\n          this.mapForm[note.id] = this.formBuilder.group(note);\r\n        }\r\n        me.isShowTableNote = true;\r\n      })\r\n    })\r\n    this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp) => {\r\n      me.listEmail = resp.emailInfos;\r\n    })\r\n  }\r\n\r\n  preventCharacter(event) {\r\n    if (event.ctrlKey) {\r\n      return;\r\n    }\r\n    if (event.keyCode == 8 || event.keyCode == 13  || event.keyCode == 37 || event.keyCode == 39) {\r\n      return;\r\n    }\r\n    if (event.keyCode < 48 || event.keyCode > 57) {\r\n      event.preventDefault();\r\n    }\r\n      // Chặn ký tự 'e', 'E' và dấu '+'\r\n      if (event.keyCode == 69 || event.keyCode == 101 || event.keyCode == 107 || event.keyCode == 187) {\r\n          event.preventDefault();\r\n      }\r\n  }\r\n  // onSimChangeAdd(listSim){\r\n  //   if(listSim == null || listSim == undefined|| listSim.length == 0) {\r\n  //     this.isValidChangeSim = true ;\r\n  //     return;\r\n  //   }\r\n  //   for (let sim of listSim) {\r\n  //     let regex = /^((\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$/;\r\n  //     console.log(regex.test(sim))\r\n  //     if(!regex.test(sim)) {\r\n  //       this.isValidChangeSim = false\r\n  //       return\r\n  //     }else {\r\n  //       this.isValidChangeSim = true\r\n  //     }\r\n  //   }\r\n  //\r\n  // }\r\n\r\n    onSimChangeAdd(listSim: string[]) {\r\n        if (!listSim || listSim.length === 0) {\r\n            this.isValidChangeSim = true; // không hiển thị lỗi khi chưa nhập\r\n            return;\r\n        }\r\n\r\n        this.isValidChangeSim = listSim.every(sim => {\r\n            sim = sim.trim();\r\n            const regex0 = /^0\\d{9,10}$/;     // Số bắt đầu bằng 0, dài 10-11\r\n            const regex84 = /^84\\d{9,10}$/;   // Số bắt đầu bằng 84, dài 11-12\r\n            return regex0.test(sim) || regex84.test(sim);\r\n        });\r\n    }\r\n    isFormValid() {\r\n        return Object.values(this.mapForm).every((formGroup: FormGroup) => formGroup.valid);\r\n    }\r\n    noWhitespaceValidator(): ValidatorFn {\r\n        return (control: AbstractControl): ValidationErrors | null => {\r\n            const isWhitespace = (control.value || '').trim().length === 0;\r\n            const isValid = !isWhitespace;\r\n            return isValid ? null : {whitespace: true};\r\n        }\r\n    };\r\n    onKeyDownNote(event): void {\r\n        if (event.key === ' ' && (this.ticket.note == null || this.ticket.note != null && this.ticket.note.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (this.ticket.note != null && this.ticket.note.trim() != '') {\r\n            this.ticket.note = this.ticket.note.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n    onKeyDownContent(event) {\r\n        if (event.key === ' ' && (this.ticket.content == null || this.ticket.content != null && this.ticket.content.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (this.ticket.content != null && this.ticket.content.trim() != '') {\r\n            this.ticket.content = this.ticket.content.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n    onKeyDownNoteContent(event: KeyboardEvent, note: any): void {\r\n        if (event.key === ' ' && (!note.content || note.content.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (note.content && note.content.trim() !== '') {\r\n            note.content = note.content.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n    onKeyDownCause(event) {\r\n        if (event.key === ' ' && (this.ticket.cause == null || this.ticket.cause != null && this.ticket.cause.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (this.ticket.cause != null && this.ticket.cause.trim() != '') {\r\n            this.ticket.cause = this.ticket.cause.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n\r\n    protected readonly CONSTANTS = CONSTANTS;\r\n}\r\n", "<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{tranService.translate(\"ticket.menu.replaceSim\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"col-5 flex flex-row justify-content-end align-items-center\">\r\n        <p-button styleClass=\"p-button-info\"\r\n                  *ngIf=\"userInfo.type == userType.CUSTOMER && checkAuthen([CONSTANTS.PERMISSIONS.TICKET.CREATE])\"\r\n                  [label]=\"tranService.translate('global.button.create')\"\r\n                  (click)=\"showModalCreate()\" icon=\"\">\r\n        </p-button>\r\n    </div>\r\n</div>\r\n\r\n<form [formGroup]=\"formSearchTicket\" (ngSubmit)=\"onSubmitSearch()\" class=\"pt-3 pb-2 vnpt-field-set\">\r\n    <p-panel [toggleable]=\"true\" [header]=\"tranService.translate('global.text.filter')\">\r\n        <div class=\"grid search-grid-4\">\r\n            <!-- ma tinh -->\r\n            <div *ngIf=\"this.userInfo.type == this.userType.ADMIN\" class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\"\r\n                                [showClear]=\"true\" [filter]=\"true\" filterBy=\"display\"\r\n                                id=\"provinceCode\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"searchInfo.provinceCode\"\r\n                                [required]=\"false\"\r\n                                formControlName=\"provinceCode\"\r\n                                [options]=\"listProvince\"\r\n                                optionLabel=\"display\"\r\n                                optionValue=\"code\"\r\n                    ></p-dropdown>\r\n                    <label class=\"label-dropdown\" htmlFor=\"provinceCode\">{{tranService.translate(\"account.label.province\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- trạng thái ticket -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\"\r\n                                [showClear]=\"true\" [filter]=\"true\" filterBy=\"display\"\r\n                                id=\"provinceCode\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"searchInfo.status\"\r\n                                [required]=\"false\"\r\n                                formControlName=\"status\"\r\n                                [options]=\"listTicketStatus\"\r\n                                optionLabel=\"label\"\r\n                                optionValue=\"value\"\r\n                                [filter] = true\r\n                                filterBy = \"label\"\r\n                    ></p-dropdown>\r\n                    <label class=\"label-dropdown\" htmlFor=\"status\">{{tranService.translate(\"ticket.label.status\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- email -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                           pInputText id=\"contactEmail\"\r\n                           [(ngModel)]=\"searchInfo.contactEmail\"\r\n                           formControlName=\"contactEmail\"\r\n                    />\r\n                    <label htmlFor=\"email\">{{tranService.translate(\"ticket.label.email\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- phone -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                           pInputText id=\"contactPhone\"\r\n                           [(ngModel)]=\"searchInfo.contactPhone\"\r\n                           formControlName=\"contactPhone\"\r\n                           type=\"number\"\r\n                           (keydown)=\"preventCharacter($event)\"\r\n                           min=\"0\"\r\n                    />\r\n                    <label htmlFor=\"contactPhone\">{{tranService.translate(\"ticket.label.phone\")}}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-3 pb-0\">\r\n                <p-button icon=\"pi pi-search\"\r\n                          styleClass=\"p-button-rounded p-button-secondary p-button-text button-search\"\r\n                          type=\"submit\"\r\n                ></p-button>\r\n            </div>\r\n        </div>\r\n    </p-panel>\r\n</form>\r\n\r\n<table-vnpt *ngIf=\"!changeTable\"\r\n    [tableId]=\"'tableTicketConfigList'\"\r\n    [fieldId]=\"'provinceCode'\"\r\n    [columns]=\"columns\"\r\n    [dataSet]=\"dataSet\"\r\n    [options]=\"optionTable\"\r\n    [pageNumber]=\"pageNumber\"\r\n    [loadData]=\"search.bind(this)\"\r\n    [pageSize]=\"pageSize\"\r\n    [sort]=\"sort\"\r\n    [params]=\"searchInfo\"\r\n    [labelTable]=\"tranService.translate('ticket.menu.requestList')\"\r\n></table-vnpt>\r\n<table-vnpt *ngIf=\"changeTable\"\r\n    [tableId]=\"'tableTicketConfigList'\"\r\n    [fieldId]=\"'provinceCode'\"\r\n    [columns]=\"columns\"\r\n    [dataSet]=\"dataSet\"\r\n    [options]=\"optionTable\"\r\n    [pageNumber]=\"pageNumber\"\r\n    [loadData]=\"search.bind(this)\"\r\n    [pageSize]=\"pageSize\"\r\n    [sort]=\"sort\"\r\n    [params]=\"searchInfo\"\r\n    [labelTable]=\"tranService.translate('ticket.menu.requestList')\"\r\n></table-vnpt>\r\n<!--    dialog tạo sửa yêu cầu-->\r\n<div class=\"flex justify-content-center dialog-vnpt\">\r\n    <p-dialog [breakpoints]=\"{ '1199px': '75vw', '575px': '90vw' }\" [header]=\"titlePopup\"\r\n              [(visible)]=\"isShowCreateRequest\" [modal]=\"true\" [style]=\"{ width: '800px', overflowY :'scroll', maxHeight : '80%' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n        <form class=\"mt-3\" [formGroup]=\"formTicketSim\" (ngSubmit)=\"createOrUpdateRequest()\">\r\n            <div class=\"flex flex-row flex-wrap justify-content-between w-full\">\r\n                <div class=\"w-full field grid chart-grid\" *ngIf=\"this.userInfo.type == this.userType.ADMIN && typeRequest == 'detail'\">\r\n                    <label htmlFor=\"contactName\" class=\"col-fixed\"\r\n                           style=\"width:180px\">{{ tranService.translate(\"account.label.province\") }}<span\r\n                        class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\">\r\n                        <span>{{getProvinceName(ticket.provinceCode)}}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- contactName -->\r\n                <div class=\"w-full field grid chart-grid\">\r\n                    <label htmlFor=\"contactName\" class=\"col-fixed\" style=\"width:180px;height: fit-content\">{{tranService.translate(\"ticket.label.customerName\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\" style=\"width: calc(100% - 180px);overflow-wrap: break-word\">\r\n                        <input *ngIf=\"typeRequest == 'update' || typeRequest == 'create'\" class=\"w-full\"\r\n                               pInputText id=\"contactName\"\r\n                               [(ngModel)]=\"ticket.contactName\"\r\n                               formControlName=\"contactName\"\r\n                               [required]=\"true\"\r\n                               [maxLength]=\"maxlengthContactName\"\r\n                               pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$\"\r\n                               [placeholder]=\"tranService.translate('account.text.inputFullname')\"\r\n                        />\r\n                      <span *ngIf=\"typeRequest == 'detail'\">{{ticket.contactName}}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- error fullname -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"fullName\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.contactName.dirty && formTicketSim.controls.contactName.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.contactName.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.contactName.errors?.pattern\">{{tranService.translate(\"global.message.formatContainVN\")}}</small>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- email -->\r\n                <div class=\"w-full field grid chart-grid\">\r\n                    <label htmlFor=\"email\" class=\"col-fixed\" style=\"width:180px;height: fit-content\">{{tranService.translate(\"ticket.label.email\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\" style=\"width: calc(100% - 180px);overflow-wrap: break-word\">\r\n                        <input *ngIf=\"typeRequest == 'update'|| typeRequest == 'create'\" class=\"w-full\"\r\n                               pInputText id=\"contactEmail\"\r\n                               [(ngModel)]=\"ticket.contactEmail\"\r\n                               formControlName=\"contactEmail\"\r\n                               [required]=\"true\"\r\n                               [maxLength]=\"50\"\r\n                               pattern=\"^[a-z0-9]+[a-z0-9\\-\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\-\\._]*[a-z0-9]+)+(\\.[a-z]{2,})$\"\r\n                               [placeholder]=\"tranService.translate('account.text.inputEmail')\"\r\n                        />\r\n                      <span *ngIf=\"typeRequest == 'detail'\">{{ticket.contactEmail}}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- error email -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"email\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.contactEmail.dirty && formTicketSim.controls.contactEmail.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.contactEmail.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.contactEmail.errors?.pattern\">{{tranService.translate(\"global.message.invalidEmail\")}}</small>\r\n                        <!--                            <small class=\"text-red-500\" *ngIf=\"isEmailExisted\">{{tranService.translate(\"global.message.exists\",{type: tranService.translate(\"account.label.email\").toLowerCase()})}}</small> -->\r\n                    </div>\r\n                </div>\r\n                <!-- phone -->\r\n                <div class=\"w-full field grid chart-grid\">\r\n                    <label htmlFor=\"phone\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"ticket.label.phone\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\">\r\n                        <input *ngIf=\"typeRequest == 'update'|| typeRequest == 'create'\" class=\"w-full\"\r\n                               pInputText id=\"contactPhone\"\r\n                               [(ngModel)]=\"ticket.contactPhone\"\r\n                               formControlName=\"contactPhone\"\r\n                               [required]=\"true\"\r\n                               [maxLength]=\"11\"\r\n                               pattern=\"^((\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\"\r\n                               (keydown)=\"preventCharacter($event)\"\r\n                               [placeholder]=\"tranService.translate('account.text.inputPhone')\"\r\n                        />\r\n                      <span *ngIf=\"typeRequest == 'detail'\">{{ticket.contactPhone}}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- error phone -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"phone\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.contactPhone.dirty && formTicketSim.controls.contactPhone.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.contactPhone.errors?.pattern\">{{tranService.translate(\"ticket.message.invalidPhone\")}}</small>\r\n                    </div>\r\n                </div>\r\n                <!-- change sim -->\r\n<!--                <div *ngIf=\"ticket.type == 0\" class=\"w-full field grid chart-grid\">-->\r\n<!--                    <label htmlFor=\"changeSim\" class=\"col-fixed\" style=\"width:180px;display:inline-block\">{{tranService.translate(\"ticket.label.changeSim\")}}<span class=\"text-red-500\">*</span></label>-->\r\n<!--                    <div class=\"col\">-->\r\n<!--                        <p-chips *ngIf=\"typeRequest=='create'\" class=\"w-full p-fluid\"-->\r\n<!--                                 formControlName=\"sim\"-->\r\n<!--                                 [(ngModel)]=\"ticket.sim\"-->\r\n<!--                                 (keydown)=\"preventCharacter($event)\" separator=\",\"-->\r\n<!--                                 (ngModelChange)=\"onSimChangeAdd($event)\"  [required]=\"true\"-->\r\n<!--                                 pTooltip=\"{{tranService.translate('ticket.message.noteChangeSim')}}\"-->\r\n<!--                        >-->\r\n<!--                        </p-chips>-->\r\n<!--                        <span style=\"word-break: break-all\" *ngIf=\"typeRequest=='update' || typeRequest == 'detail'\">{{ticket.sim}}</span>-->\r\n<!--                    </div>-->\r\n<!--                </div>-->\r\n<!--                &lt;!&ndash; error change sim  &ndash;&gt;-->\r\n<!--                <div *ngIf=\"ticket.type == 0\" class=\"w-full field grid text-error-field\">-->\r\n<!--                    <label htmlFor=\"numSub\" class=\"col-fixed\" style=\"width:180px\"></label>-->\r\n<!--                    <div class=\"col\">-->\r\n<!--                        <small class=\"text-red-500 block\" *ngIf=\"formTicketSim.controls.sim.dirty && formTicketSim.controls.sim.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>-->\r\n<!--                        <small class=\"text-red-500 block\" *ngIf=\"!isValidChangeSim\">{{tranService.translate(\"ticket.message.invalidPhone\")}}</small>-->\r\n<!--                    </div>-->\r\n<!--                </div>-->\r\n                <div *ngIf=\"ticket.type == 0\" class=\"w-full field grid chart-grid\">\r\n                    <label htmlFor=\"changeSim\" class=\"col-fixed\" style=\"width:180px; display:inline-block\">\r\n                        {{tranService.translate(\"ticket.label.changeSim\")}}<span class=\"text-red-500\">*</span>\r\n                    </label>\r\n                    <div class=\"col\">\r\n                        <p-chips *ngIf=\"typeRequest == 'create'\"\r\n                                 class=\"w-full p-fluid\"\r\n                                 formControlName=\"sim\"\r\n                                 [(ngModel)]=\"ticket.sim\"\r\n                                 (keydown)=\"preventCharacter($event)\"  separator=\",\"\r\n                                 (ngModelChange)=\"onSimChangeAdd($event)\"\r\n                                 pTooltip=\"{{tranService.translate('ticket.message.noteChangeSim')}}\">\r\n                        </p-chips>\r\n\r\n                        <span style=\"word-break: break-all\" *ngIf=\"typeRequest == 'update' || typeRequest == 'detail'\">{{ticket.sim}}</span>\r\n\r\n                        <!-- Thông báo hướng dẫn khi nhập đúng -->\r\n                        <small class=\"text-green-600 block mt-1\" *ngIf=\"isValidChangeSim && formTicketSim.controls.sim.value?.length > 0\">\r\n                            {{tranService.translate(\"ticket.message.hintValidPhone\")}}\r\n                        </small>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- error change sim -->\r\n                <div *ngIf=\"ticket.type == 0\" class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"numSub\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500 block\" *ngIf=\"formTicketSim.controls.sim.dirty && formTicketSim.controls.sim.errors?.required\">\r\n                            {{tranService.translate(\"global.message.required\")}}\r\n                        </small>\r\n                        <small class=\"text-red-500 block\" *ngIf=\"!isValidChangeSim && ticket.sim?.length > 0\">\r\n                            {{tranService.translate(\"ticket.message.invalidPhone\")}}\r\n                        </small>\r\n                    </div>\r\n                </div>\r\n\r\n\r\n                <!-- content-->\r\n                <div class=\"w-full field grid chart-grid\">\r\n                    <label htmlFor=\"content\" class=\"col-fixed\" style=\"width:180px;height: fit-content;\">{{tranService.translate(\"ticket.label.content\")}}</label>\r\n                    <div class=\"col\">\r\n                            <textarea *ngIf=\"typeRequest=='create'\" class=\"w-full\" style=\"resize: none;\"\r\n                                       rows=\"5\"\r\n                                       [autoResize]=\"false\"\r\n                                       pInputTextarea id=\"content\"\r\n                                       [(ngModel)]=\"ticket.content\"\r\n                                       formControlName=\"content\"\r\n                                       [maxlength]=\"255\"\r\n                                       [placeholder]=\"tranService.translate('ticket.label.content')\"\r\n                                      (keydown)=\"onKeyDownContent($event)\"\r\n                            ></textarea>\r\n                            <span style=\"word-break: break-all\" *ngIf=\"typeRequest=='update' || typeRequest == 'detail'\">{{ticket.content}}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- error content -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"content\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.content.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                    </div>\r\n                </div>\r\n                <!-- note-->\r\n                <div class=\"w-full field grid chart-grid\">\r\n                    <label htmlFor=\"note\" class=\"col-fixed\" style=\"width:180px;height: fit-content;\">{{tranService.translate(\"ticket.label.note\")}}</label>\r\n                    <div class=\"col\">\r\n                            <textarea *ngIf=\"typeRequest=='create'\"  class=\"w-full\" style=\"resize: none;\"\r\n                                       rows=\"5\"\r\n                                       [autoResize]=\"false\"\r\n                                       pInputTextarea id=\"note\"\r\n                                       [(ngModel)]=\"ticket.note\"\r\n                                       formControlName=\"note\"\r\n                                       [maxlength]=\"255\"\r\n                                       [placeholder]=\"tranService.translate('ticket.label.note')\"\r\n                                      (keydown)=\"onKeyDownNote($event)\"\r\n                            ></textarea>\r\n                            <span style=\"word-break: break-all\" *ngIf=\"typeRequest=='update' || typeRequest == 'detail'\">{{ticket.note}}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- error note -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"note\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.note.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                    </div>\r\n                </div>\r\n                <!-- chuyen xu ly-->\r\n              <div *ngIf=\"(typeRequest=='update' && userInfo.type == userType.PROVINCE && ticket.status == null && ticket.statusOld == 0) || (userInfo.type == userType.PROVINCE && typeRequest == 'detail' && ticket.assigneeId != null)\" class=\"w-full field grid chart-grid\">\r\n                    <label for=\"type\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"ticket.label.transferProcessing\")}}</label>\r\n                    <div class=\"col\" style=\"max-width: calc(100% - 180px); position: relative\">\r\n                      <vnpt-select\r\n                          class=\"w-full\"\r\n                          [(value)]=\"ticket.assigneeId\"\r\n                          [placeholder]=\"tranService.translate('ticket.label.transferProcessing')\"\r\n                          [disabled]=\"ticket.assigneeId != null && typeRequest == 'detail'\"\r\n                          objectKey=\"account\"\r\n                          paramKey=\"email\"\r\n                          keyReturn=\"id\"\r\n                          displayPattern=\"${email}\"\r\n                          [isMultiChoice]=\"false\"\r\n                          [paramDefault]=\"{type : 3}\"\r\n                          [stylePositionBoxSelect] = \"{bottom: '40px', left: '12px', 'min-width':'calc(100% - 20px)'}\"\r\n                      ></vnpt-select>\r\n                    </div>\r\n                </div>\r\n                <!-- error chuyen xu ly-->\r\n                <div *ngIf=\"(typeRequest=='update' && userInfo.type == userType.PROVINCE && ticket.status == null && ticket.statusOld == 0) || typeRequest == 'detail'\" class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"userType\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.assigneeId.dirty && formTicketSim.controls.assigneeId.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                    </div>\r\n                </div>\r\n                <!-- trang thai-->\r\n              <div *ngIf=\"typeRequest=='update' || typeRequest == 'detail'\" [class]=\"(userInfo.type == userType.PROVINCE && typeRequest != 'detail') ? (ticket.assigneeId == null || (ticket.assigneeId != null && !listActivatedAccount.includes(ticket.assigneeId))? '': 'hidden'): ''\" class=\"w-full field grid chart-grid\">\r\n                    <label for=\"status\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"ticket.label.status\")}}</label>\r\n                    <div class=\"col\">\r\n                        <p-dropdown *ngIf=\"typeRequest=='update'\" styleClass=\"w-full\"\r\n                                    [showClear]=\"true\"\r\n                                    id=\"type\" [autoDisplayFirst]=\"true\"\r\n                                    [(ngModel)]=\"ticket.status\"\r\n                                    formControlName=\"status\"\r\n                                    [options]=\"ticket.statusOld !== null ? mapTicketStatus[ticket.statusOld] : listTicketStatus\"\r\n                                    optionLabel=\"label\"\r\n                                    optionValue=\"value\"\r\n                                    [placeholder]=\"tranService.translate('ticket.label.status')\"\r\n                                    [emptyMessage]=\"tranService.translate('global.text.nodata')\"\r\n                        ></p-dropdown>\r\n                      <span *ngIf=\"typeRequest=='detail' && ticket.statusOld == 0\" [class]=\"['p-2', 'text-white', 'bg-cyan-300', 'border-round', 'inline-block']\">{{getValueStatus(ticket.statusOld)}}</span>\r\n                      <span *ngIf=\"typeRequest=='detail' && ticket.statusOld == 1\"  [class]=\"['p-2', 'text-white', 'bg-bluegray-500', 'border-round', 'inline-block']\">{{getValueStatus(ticket.statusOld)}}</span>\r\n                      <span *ngIf=\"typeRequest=='detail' && ticket.statusOld == 2\"  [class]=\"['p-2', 'text-white', 'bg-orange-400', 'border-round', 'inline-block']\">{{getValueStatus(ticket.statusOld)}}</span>\r\n                      <span *ngIf=\"typeRequest=='detail' && ticket.statusOld == 3\"  [class]=\"['p-2', 'text-white', 'bg-red-500', 'border-round', 'inline-block']\">{{getValueStatus(ticket.statusOld)}}</span>\r\n                      <span *ngIf=\"typeRequest=='detail' && ticket.statusOld == 4\"  [class]=\"['p-2', 'text-white', 'bg-green-500', 'border-round', 'inline-block']\">{{getValueStatus(ticket.statusOld)}}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- error trang thai -->\r\n              <div *ngIf=\"typeRequest=='update' || typeRequest == 'detail' && ticket.assigneeId == null\" class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"userType\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\" *ngIf=\"formTicketSim.controls.status.dirty && formTicketSim.controls.status.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n          <!-- ghi chu xu ly-->\r\n          <div *ngIf=\"typeRequest=='update'\" [class]=\"userInfo.type == userType.PROVINCE ? (ticket.assigneeId == null || (ticket.assigneeId != null && !listActivatedAccount.includes(ticket.assigneeId))? '': 'hidden'): ''\" class=\"w-full field grid chart-grid\">\r\n            <label htmlFor=\"contactName\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"ticket.label.processingNotes\")}}<span *ngIf=\"ticket.status\" class=\"text-red-500\">*</span></label>\r\n            <div class=\"col\">\r\n              <input class=\"w-full\"\r\n                     pInputText id=\"cause\"\r\n                     [(ngModel)]=\"ticket.cause\"\r\n                     formControlName=\"cause\"\r\n                     [required]=\"ticket.status != null\"\r\n                     [maxLength]=\"255\"\r\n                     [placeholder]=\"tranService.translate('ticket.label.processingNotes')\"\r\n                     (keydown)=\"onKeyDownCause($event)\"\r\n              />\r\n            </div>\r\n          </div>\r\n          <!-- error ghi chu xu ly-->\r\n          <div *ngIf=\"typeRequest=='update' || typeRequest == 'detail' && (ticket.assigneeId == null || (ticket.assigneeId != null && !listActivatedAccount.includes(ticket.assigneeId)))\" class=\"w-full field grid text-error-field\">\r\n            <label htmlFor=\"fullName\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n            <div class=\"col\">\r\n              <small class=\"text-red-500\" *ngIf=\"(formTicketSim.controls.cause.dirty && formTicketSim.controls.cause.errors?.required) || (this.ticket.status != null && (this.ticket.cause == null || this.ticket.cause.trim() == ''))\">{{tranService.translate(\"global.message.required\")}}</small>\r\n            </div>\r\n          </div>\r\n\r\n<!--          danh sach ghi chu xu ly-->\r\n          <div *ngIf=\"(typeRequest=='update' || typeRequest == 'detail') && (listNotes && listNotes.length > 0)\" class=\"w-full field grid chart-grid\">\r\n            <label htmlFor=\"content\" class=\"col-fixed font-medium\"\r\n                   style=\"width:180px;height: fit-content;\">{{ tranService.translate(\"ticket.label.listNote\") }}</label>\r\n            <p-table style=\"width: 100%\" [value]=\"listNotes\" [tableStyle]=\"{ 'min-width': '50rem' }\">\r\n              <ng-template pTemplate=\"header\">\r\n                <tr>\r\n                  <th>{{tranService.translate('global.text.stt')}}</th>\r\n                  <th>{{tranService.translate('account.text.account')}}</th>\r\n                  <th style=\"min-width: 146px\">{{tranService.translate('global.button.changeStatus')}}</th>\r\n                  <th style=\"min-width: 155px\">{{tranService.translate('account.label.time')}}</th>\r\n                  <th>{{tranService.translate('ticket.label.content')}}</th>\r\n                </tr>\r\n              </ng-template>\r\n              <ng-template pTemplate=\"body\" let-note let-i=\"rowIndex\">\r\n                <tr [formGroup]=\"mapForm[note.id]\">\r\n                  <td>{{ i + 1 }}</td>\r\n                  <td>{{ note.userName }}</td>\r\n                  <td>{{getValueStatus(note.status)}}</td>\r\n                  <td>{{note.createdDate | date:\"HH:mm:ss dd/MM/yyyy\"}}</td>\r\n                  <td>\r\n                    <input *ngIf=\"typeRequest == 'update'\"  class=\"w-full\"\r\n                           pInputText id=\"content\"\r\n                           [(ngModel)]=\"note.content\"\r\n                           formControlName=\"content\"\r\n                           [required]=\"true\"\r\n                           [maxLength]=\"255\"\r\n                           (keydown)=\"onKeyDownNoteContent($event, note)\"\r\n                    />\r\n                    <span *ngIf=\"typeRequest == 'detail'\">{{note.content}}</span>\r\n                    <small class=\"text-red-500\" *ngIf=\"mapForm[note.id].controls.content.dirty && mapForm[note.id].controls.content.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                    <small class=\"text-red-500\" *ngIf=\"mapForm[note.id].controls.content.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n\r\n                  </td>\r\n                </tr>\r\n              </ng-template>\r\n            </p-table>\r\n          </div>\r\n\r\n          <div *ngIf=\"typeRequest != 'detail'\" class=\"flex flex-row justify-content-center align-items-center mt-3\">\r\n                <p-button styleClass=\"mr-2 p-button-secondary\" [label]=\"tranService.translate('global.button.cancel')\" (click)=\"isShowCreateRequest = false\"></p-button>\r\n                <p-button type=\"submit\" styleClass=\"p-button-info\" [disabled]=\"formTicketSim.invalid || (typeRequest=='create' && !isValidChangeSim) || (this.ticket.status != null && this.ticket.cause != null && this.ticket.cause.trim() == '') || (this.listNotes.length > 0 && !isFormValid())\" [label]=\"tranService.translate('global.button.save')\"></p-button>\r\n            </div>\r\n        </form>\r\n    </p-dialog>\r\n</div>\r\n"], "mappings": "AAEA,SAAQA,aAAa,QAAO,sCAAsC;AAElE,SAAQC,SAAS,QAAO,iCAAiC;AACzD,SAAQC,aAAa,QAAO,wBAAwB;AACpD,SAAQC,cAAc,QAAO,yCAAyC;AACtE,SAAgFC,UAAU,QAAO,gBAAgB;AACjH,SAAQC,sBAAsB,QAAO,gDAAgD;;;;;;;;;;;;;;;;;;;;;;;ICF7EC,EAAA,CAAAC,cAAA,mBAG8C;IAApCD,EAAA,CAAAE,UAAA,mBAAAC,4EAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,OAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IACrCT,EAAA,CAAAU,YAAA,EAAW;;;;IAFDV,EAAA,CAAAW,UAAA,UAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,yBAAuD;;;;;;IAU7Dd,EAAA,CAAAC,cAAA,cAAqE;IAKjDD,EAAA,CAAAE,UAAA,2BAAAa,kFAAAC,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAa,IAAA;MAAA,MAAAC,OAAA,GAAAlB,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAU,OAAA,CAAAC,UAAA,CAAAC,YAAA,GAAAJ,MAAA,CACxC;IAAA,EADgE;IAMhDhB,EAAA,CAAAU,YAAA,EAAa;IACdV,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAqB,MAAA,GAAmD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IATpGV,EAAA,CAAAsB,SAAA,GAAkB;IAAlBtB,EAAA,CAAAW,UAAA,mBAAkB,uDAAAY,MAAA,CAAAJ,UAAA,CAAAC,YAAA,gCAAAG,MAAA,CAAAC,YAAA;IASuBxB,EAAA,CAAAsB,SAAA,GAAmD;IAAnDtB,EAAA,CAAAyB,iBAAA,CAAAF,MAAA,CAAAV,WAAA,CAAAC,SAAA,2BAAmD;;;;;IAwD5Hd,EAAA,CAAA0B,SAAA,qBAYc;;;;IAXV1B,EAAA,CAAAW,UAAA,oCAAmC,uCAAAgB,MAAA,CAAAC,OAAA,aAAAD,MAAA,CAAAE,OAAA,aAAAF,MAAA,CAAAG,WAAA,gBAAAH,MAAA,CAAAI,UAAA,cAAAJ,MAAA,CAAAK,MAAA,CAAAC,IAAA,CAAAN,MAAA,eAAAA,MAAA,CAAAO,QAAA,UAAAP,MAAA,CAAAQ,IAAA,YAAAR,MAAA,CAAAR,UAAA,gBAAAQ,MAAA,CAAAd,WAAA,CAAAC,SAAA;;;;;IAYvCd,EAAA,CAAA0B,SAAA,qBAYc;;;;IAXV1B,EAAA,CAAAW,UAAA,oCAAmC,uCAAAyB,MAAA,CAAAR,OAAA,aAAAQ,MAAA,CAAAP,OAAA,aAAAO,MAAA,CAAAN,WAAA,gBAAAM,MAAA,CAAAL,UAAA,cAAAK,MAAA,CAAAJ,MAAA,CAAAC,IAAA,CAAAG,MAAA,eAAAA,MAAA,CAAAF,QAAA,UAAAE,MAAA,CAAAD,IAAA,YAAAC,MAAA,CAAAjB,UAAA,gBAAAiB,MAAA,CAAAvB,WAAA,CAAAC,SAAA;;;;;IAkBvBd,EAAA,CAAAC,cAAA,cAAuH;IAExFD,EAAA,CAAAqB,MAAA,GAAqD;IAAArB,EAAA,CAAAC,cAAA,eACvD;IAAAD,EAAA,CAAAqB,MAAA,QAAC;IAAArB,EAAA,CAAAU,YAAA,EAAO;IACjCV,EAAA,CAAAC,cAAA,cAAiB;IACPD,EAAA,CAAAqB,MAAA,GAAwC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAH9BV,EAAA,CAAAsB,SAAA,GAAqD;IAArDtB,EAAA,CAAAyB,iBAAA,CAAAY,MAAA,CAAAxB,WAAA,CAAAC,SAAA,2BAAqD;IAGtEd,EAAA,CAAAsB,SAAA,GAAwC;IAAxCtB,EAAA,CAAAyB,iBAAA,CAAAY,MAAA,CAAAC,eAAA,CAAAD,MAAA,CAAAE,MAAA,CAAAnB,YAAA,EAAwC;;;;;;IAO9CpB,EAAA,CAAAC,cAAA,gBAQE;IANKD,EAAA,CAAAE,UAAA,2BAAAsC,+EAAAxB,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAqC,IAAA;MAAA,MAAAC,OAAA,GAAA1C,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAkC,OAAA,CAAAH,MAAA,CAAAI,WAAA,GAAA3B,MAAA,CACvC;IAAA,EAD0D;IAFvChB,EAAA,CAAAU,YAAA,EAQE;;;;IANKV,EAAA,CAAAW,UAAA,YAAAiC,MAAA,CAAAL,MAAA,CAAAI,WAAA,CAAgC,gCAAAC,MAAA,CAAAC,oBAAA,iBAAAD,MAAA,CAAA/B,WAAA,CAAAC,SAAA;;;;;IAOzCd,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAqB,MAAA,GAAsB;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA7BV,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAAyB,iBAAA,CAAAqB,MAAA,CAAAP,MAAA,CAAAI,WAAA,CAAsB;;;;;IAO1D3C,EAAA,CAAAC,cAAA,gBAAoI;IAAAD,EAAA,CAAAqB,MAAA,GAAoD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA5DV,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAyB,iBAAA,CAAAsB,MAAA,CAAAlC,WAAA,CAAAC,SAAA,4BAAoD;;;;;;;;;;IACxLd,EAAA,CAAAC,cAAA,gBAAyF;IAAAD,EAAA,CAAAqB,MAAA,GAA+D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAvEV,EAAA,CAAAsB,SAAA,GAA+D;IAA/DtB,EAAA,CAAAyB,iBAAA,CAAAuB,MAAA,CAAAnC,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAiD,eAAA,IAAAC,GAAA,GAA+D;;;;;IACxJlD,EAAA,CAAAC,cAAA,gBAAuF;IAAAD,EAAA,CAAAqB,MAAA,GAA2D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAnEV,EAAA,CAAAsB,SAAA,GAA2D;IAA3DtB,EAAA,CAAAyB,iBAAA,CAAA0B,MAAA,CAAAtC,WAAA,CAAAC,SAAA,mCAA2D;;;;;;IAQlJd,EAAA,CAAAC,cAAA,gBAQE;IANKD,EAAA,CAAAE,UAAA,2BAAAkD,+EAAApC,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAiD,IAAA;MAAA,MAAAC,OAAA,GAAAtD,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAA8C,OAAA,CAAAf,MAAA,CAAAgB,YAAA,GAAAvC,MAAA,CACvC;IAAA,EAD2D;IAFxChB,EAAA,CAAAU,YAAA,EAQE;;;;IANKV,EAAA,CAAAW,UAAA,YAAA6C,OAAA,CAAAjB,MAAA,CAAAgB,YAAA,CAAiC,mDAAAC,OAAA,CAAA3C,WAAA,CAAAC,SAAA;;;;;IAO1Cd,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAqB,MAAA,GAAuB;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA9BV,EAAA,CAAAsB,SAAA,GAAuB;IAAvBtB,EAAA,CAAAyB,iBAAA,CAAAgC,OAAA,CAAAlB,MAAA,CAAAgB,YAAA,CAAuB;;;;;IAO3DvD,EAAA,CAAAC,cAAA,gBAAsI;IAAAD,EAAA,CAAAqB,MAAA,GAAoD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA5DV,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAyB,iBAAA,CAAAiC,OAAA,CAAA7C,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAC1Ld,EAAA,CAAAC,cAAA,gBAA0F;IAAAD,EAAA,CAAAqB,MAAA,GAA+D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAvEV,EAAA,CAAAsB,SAAA,GAA+D;IAA/DtB,EAAA,CAAAyB,iBAAA,CAAAkC,OAAA,CAAA9C,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAiD,eAAA,IAAAC,GAAA,GAA+D;;;;;IACzJlD,EAAA,CAAAC,cAAA,gBAAwF;IAAAD,EAAA,CAAAqB,MAAA,GAAwD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAhEV,EAAA,CAAAsB,SAAA,GAAwD;IAAxDtB,EAAA,CAAAyB,iBAAA,CAAAmC,OAAA,CAAA/C,WAAA,CAAAC,SAAA,gCAAwD;;;;;;IAQhJd,EAAA,CAAAC,cAAA,gBASE;IAPKD,EAAA,CAAAE,UAAA,2BAAA2D,+EAAA7C,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA0D,IAAA;MAAA,MAAAC,OAAA,GAAA/D,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAuD,OAAA,CAAAxB,MAAA,CAAAyB,YAAA,GAAAhD,MAAA,CACvC;IAAA,EAD2D,qBAAAiD,yEAAAjD,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA0D,IAAA;MAAA,MAAAI,OAAA,GAAAlE,EAAA,CAAAO,aAAA;MAAA,OAKtBP,EAAA,CAAAQ,WAAA,CAAA0D,OAAA,CAAAC,gBAAA,CAAAnD,MAAA,CAAwB;IAAA,EALF;IAFxChB,EAAA,CAAAU,YAAA,EASE;;;;IAPKV,EAAA,CAAAW,UAAA,YAAAyD,OAAA,CAAA7B,MAAA,CAAAyB,YAAA,CAAiC,mDAAAI,OAAA,CAAAvD,WAAA,CAAAC,SAAA;;;;;IAQ1Cd,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAqB,MAAA,GAAuB;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA9BV,EAAA,CAAAsB,SAAA,GAAuB;IAAvBtB,EAAA,CAAAyB,iBAAA,CAAA4C,OAAA,CAAA9B,MAAA,CAAAyB,YAAA,CAAuB;;;;;IAO3DhE,EAAA,CAAAC,cAAA,gBAAsI;IAAAD,EAAA,CAAAqB,MAAA,GAAoD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA5DV,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAyB,iBAAA,CAAA6C,OAAA,CAAAzD,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAC1Ld,EAAA,CAAAC,cAAA,gBAAwF;IAAAD,EAAA,CAAAqB,MAAA,GAAwD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAhEV,EAAA,CAAAsB,SAAA,GAAwD;IAAxDtB,EAAA,CAAAyB,iBAAA,CAAA8C,OAAA,CAAA1D,WAAA,CAAAC,SAAA,gCAAwD;;;;;;IA+BhJd,EAAA,CAAAC,cAAA,kBAM8E;IAHrED,EAAA,CAAAE,UAAA,2BAAAsE,yFAAAxD,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAqE,IAAA;MAAA,MAAAC,OAAA,GAAA1E,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAkE,OAAA,CAAAnC,MAAA,CAAAoC,GAAA,GAAA3D,MAAA,CACzC;IAAA,EADoD,qBAAA4D,mFAAA5D,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAqE,IAAA;MAAA,MAAAI,OAAA,GAAA7E,EAAA,CAAAO,aAAA;MAAA,OACbP,EAAA,CAAAQ,WAAA,CAAAqE,OAAA,CAAAV,gBAAA,CAAAnD,MAAA,CAAwB;IAAA,EADX,2BAAAwD,yFAAAxD,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAqE,IAAA;MAAA,MAAAK,OAAA,GAAA9E,EAAA,CAAAO,aAAA;MAAA,OAEPP,EAAA,CAAAQ,WAAA,CAAAsE,OAAA,CAAAC,cAAA,CAAA/D,MAAA,CAAsB;IAAA,EAFf;IAIjChB,EAAA,CAAAU,YAAA,EAAU;;;;IADDV,EAAA,CAAAgF,qBAAA,aAAAC,OAAA,CAAApE,WAAA,CAAAC,SAAA,iCAAoE;IAHpEd,EAAA,CAAAW,UAAA,YAAAsE,OAAA,CAAA1C,MAAA,CAAAoC,GAAA,CAAwB;;;;;IAMjC3E,EAAA,CAAAC,cAAA,eAA+F;IAAAD,EAAA,CAAAqB,MAAA,GAAc;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAArBV,EAAA,CAAAsB,SAAA,GAAc;IAAdtB,EAAA,CAAAyB,iBAAA,CAAAyD,OAAA,CAAA3C,MAAA,CAAAoC,GAAA,CAAc;;;;;IAG7G3E,EAAA,CAAAC,cAAA,gBAAkH;IAC9GD,EAAA,CAAAqB,MAAA,GACJ;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IADJV,EAAA,CAAAsB,SAAA,GACJ;IADItB,EAAA,CAAAmF,kBAAA,MAAAC,OAAA,CAAAvE,WAAA,CAAAC,SAAA,uCACJ;;;;;IAnBRd,EAAA,CAAAC,cAAA,cAAmE;IAE3DD,EAAA,CAAAqB,MAAA,GAAmD;IAAArB,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAqB,MAAA,QAAC;IAAArB,EAAA,CAAAU,YAAA,EAAO;IAE1FV,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAqF,UAAA,IAAAC,uDAAA,sBAOU;IAEVtF,EAAA,CAAAqF,UAAA,IAAAE,oDAAA,mBAAoH;IAGpHvF,EAAA,CAAAqF,UAAA,IAAAG,qDAAA,oBAEQ;IACZxF,EAAA,CAAAU,YAAA,EAAM;;;;IAlBFV,EAAA,CAAAsB,SAAA,GAAmD;IAAnDtB,EAAA,CAAAmF,kBAAA,MAAAM,OAAA,CAAA5E,WAAA,CAAAC,SAAA,+BAAmD;IAGzCd,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAW,UAAA,SAAA8E,OAAA,CAAAC,WAAA,aAA6B;IASF1F,EAAA,CAAAsB,SAAA,GAAwD;IAAxDtB,EAAA,CAAAW,UAAA,SAAA8E,OAAA,CAAAC,WAAA,gBAAAD,OAAA,CAAAC,WAAA,aAAwD;IAGnD1F,EAAA,CAAAsB,SAAA,GAAsE;IAAtEtB,EAAA,CAAAW,UAAA,SAAA8E,OAAA,CAAAE,gBAAA,KAAAF,OAAA,CAAAG,aAAA,CAAAC,QAAA,CAAAlB,GAAA,CAAAmB,KAAA,kBAAAL,OAAA,CAAAG,aAAA,CAAAC,QAAA,CAAAlB,GAAA,CAAAmB,KAAA,CAAAC,MAAA,MAAsE;;;;;IAUhH/F,EAAA,CAAAC,cAAA,gBAA0H;IACtHD,EAAA,CAAAqB,MAAA,GACJ;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IADJV,EAAA,CAAAsB,SAAA,GACJ;IADItB,EAAA,CAAAmF,kBAAA,MAAAa,OAAA,CAAAnF,WAAA,CAAAC,SAAA,iCACJ;;;;;IACAd,EAAA,CAAAC,cAAA,gBAAsF;IAClFD,EAAA,CAAAqB,MAAA,GACJ;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IADJV,EAAA,CAAAsB,SAAA,GACJ;IADItB,EAAA,CAAAmF,kBAAA,MAAAc,OAAA,CAAApF,WAAA,CAAAC,SAAA,qCACJ;;;;;IARRd,EAAA,CAAAC,cAAA,cAAyE;IACrED,EAAA,CAAA0B,SAAA,gBAAsE;IACtE1B,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAqF,UAAA,IAAAa,qDAAA,oBAEQ;IACRlG,EAAA,CAAAqF,UAAA,IAAAc,qDAAA,oBAEQ;IACZnG,EAAA,CAAAU,YAAA,EAAM;;;;IANiCV,EAAA,CAAAsB,SAAA,GAAqF;IAArFtB,EAAA,CAAAW,UAAA,SAAAyF,OAAA,CAAAR,aAAA,CAAAC,QAAA,CAAAlB,GAAA,CAAA0B,KAAA,KAAAD,OAAA,CAAAR,aAAA,CAAAC,QAAA,CAAAlB,GAAA,CAAA2B,MAAA,kBAAAF,OAAA,CAAAR,aAAA,CAAAC,QAAA,CAAAlB,GAAA,CAAA2B,MAAA,CAAAC,QAAA,EAAqF;IAGrFvG,EAAA,CAAAsB,SAAA,GAAiD;IAAjDtB,EAAA,CAAAW,UAAA,UAAAyF,OAAA,CAAAT,gBAAA,KAAAS,OAAA,CAAA7D,MAAA,CAAAoC,GAAA,kBAAAyB,OAAA,CAAA7D,MAAA,CAAAoC,GAAA,CAAAoB,MAAA,MAAiD;;;;;;IAWhF/F,EAAA,CAAAC,cAAA,mBASC;IALUD,EAAA,CAAAE,UAAA,2BAAAsG,qFAAAxF,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAqG,IAAA;MAAA,MAAAC,OAAA,GAAA1G,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAkG,OAAA,CAAAnE,MAAA,CAAAoE,OAAA,GAAA3F,MAAA,CAC/C;IAAA,EAD8D,qBAAA4F,+EAAA5F,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAqG,IAAA;MAAA,MAAAI,OAAA,GAAA7G,EAAA,CAAAO,aAAA;MAAA,OAIlBP,EAAA,CAAAQ,WAAA,CAAAqG,OAAA,CAAAC,gBAAA,CAAA9F,MAAA,CAAwB;IAAA,EAJN;IAKtChB,EAAA,CAAAU,YAAA,EAAW;;;;IAPDV,EAAA,CAAAW,UAAA,qBAAoB,YAAAoG,OAAA,CAAAxE,MAAA,CAAAoE,OAAA,mCAAAI,OAAA,CAAAlG,WAAA,CAAAC,SAAA;;;;;IAQ/Bd,EAAA,CAAAC,cAAA,eAA6F;IAAAD,EAAA,CAAAqB,MAAA,GAAkB;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAAzBV,EAAA,CAAAsB,SAAA,GAAkB;IAAlBtB,EAAA,CAAAyB,iBAAA,CAAAuF,OAAA,CAAAzE,MAAA,CAAAoE,OAAA,CAAkB;;;;;IAOnH3G,EAAA,CAAAC,cAAA,gBAAqF;IAAAD,EAAA,CAAAqB,MAAA,GAA+D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAvEV,EAAA,CAAAsB,SAAA,GAA+D;IAA/DtB,EAAA,CAAAyB,iBAAA,CAAAwF,OAAA,CAAApG,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAiD,eAAA,IAAAC,GAAA,GAA+D;;;;;;IAOhJlD,EAAA,CAAAC,cAAA,mBASC;IALUD,EAAA,CAAAE,UAAA,2BAAAgH,qFAAAlG,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA+G,IAAA;MAAA,MAAAC,OAAA,GAAApH,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAA4G,OAAA,CAAA7E,MAAA,CAAA8E,IAAA,GAAArG,MAAA,CAC/C;IAAA,EAD2D,qBAAAsG,+EAAAtG,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA+G,IAAA;MAAA,MAAAI,OAAA,GAAAvH,EAAA,CAAAO,aAAA;MAAA,OAIfP,EAAA,CAAAQ,WAAA,CAAA+G,OAAA,CAAAC,aAAA,CAAAxG,MAAA,CAAqB;IAAA,EAJN;IAKnChB,EAAA,CAAAU,YAAA,EAAW;;;;IAPDV,EAAA,CAAAW,UAAA,qBAAoB,YAAA8G,OAAA,CAAAlF,MAAA,CAAA8E,IAAA,mCAAAI,OAAA,CAAA5G,WAAA,CAAAC,SAAA;;;;;IAQ/Bd,EAAA,CAAAC,cAAA,eAA6F;IAAAD,EAAA,CAAAqB,MAAA,GAAe;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAAtBV,EAAA,CAAAsB,SAAA,GAAe;IAAftB,EAAA,CAAAyB,iBAAA,CAAAiG,OAAA,CAAAnF,MAAA,CAAA8E,IAAA,CAAe;;;;;IAOhHrH,EAAA,CAAAC,cAAA,gBAAkF;IAAAD,EAAA,CAAAqB,MAAA,GAA+D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAvEV,EAAA,CAAAsB,SAAA,GAA+D;IAA/DtB,EAAA,CAAAyB,iBAAA,CAAAkG,OAAA,CAAA9G,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAiD,eAAA,IAAAC,GAAA,GAA+D;;;;;;;;;;;;;;;;;;IAI3JlD,EAAA,CAAAC,cAAA,cAAkQ;IACpMD,EAAA,CAAAqB,MAAA,GAA4D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;IAC5HV,EAAA,CAAAC,cAAA,cAA2E;IAGrED,EAAA,CAAAE,UAAA,yBAAA0H,iFAAA5G,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAyH,IAAA;MAAA,MAAAC,OAAA,GAAA9H,EAAA,CAAAO,aAAA;MAAA,OAAWP,EAAA,CAAAQ,WAAA,CAAAsH,OAAA,CAAAvF,MAAA,CAAAwF,UAAA,GAAA/G,MAAA,CAChC;IAAA,EADkD;IAUhChB,EAAA,CAAAU,YAAA,EAAc;;;;IAduCV,EAAA,CAAAsB,SAAA,GAA4D;IAA5DtB,EAAA,CAAAyB,iBAAA,CAAAuG,OAAA,CAAAnH,WAAA,CAAAC,SAAA,oCAA4D;IAI9Gd,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAW,UAAA,UAAAqH,OAAA,CAAAzF,MAAA,CAAAwF,UAAA,CAA6B,gBAAAC,OAAA,CAAAnH,WAAA,CAAAC,SAAA,iDAAAkH,OAAA,CAAAzF,MAAA,CAAAwF,UAAA,YAAAC,OAAA,CAAAtC,WAAA,sDAAA1F,EAAA,CAAAiD,eAAA,IAAAgF,GAAA,6BAAAjI,EAAA,CAAAiD,eAAA,IAAAiF,GAAA;;;;;IAiB/BlI,EAAA,CAAAC,cAAA,gBAAkI;IAAAD,EAAA,CAAAqB,MAAA,GAAoD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA5DV,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAyB,iBAAA,CAAA0G,OAAA,CAAAtH,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAH9Ld,EAAA,CAAAC,cAAA,cAAmM;IAC/LD,EAAA,CAAA0B,SAAA,gBAAwE;IACxE1B,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAqF,UAAA,IAAA+C,qDAAA,oBAA8L;IAClMpI,EAAA,CAAAU,YAAA,EAAM;;;;IAD2BV,EAAA,CAAAsB,SAAA,GAAmG;IAAnGtB,EAAA,CAAAW,UAAA,SAAA0H,OAAA,CAAAzC,aAAA,CAAAC,QAAA,CAAAkC,UAAA,CAAA1B,KAAA,KAAAgC,OAAA,CAAAzC,aAAA,CAAAC,QAAA,CAAAkC,UAAA,CAAAzB,MAAA,kBAAA+B,OAAA,CAAAzC,aAAA,CAAAC,QAAA,CAAAkC,UAAA,CAAAzB,MAAA,CAAAC,QAAA,EAAmG;;;;;;IAOhIvG,EAAA,CAAAC,cAAA,qBAUC;IAPWD,EAAA,CAAAE,UAAA,2BAAAoI,gGAAAtH,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAmI,IAAA;MAAA,MAAAC,OAAA,GAAAxI,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAgI,OAAA,CAAAjG,MAAA,CAAAkG,MAAA,GAAAzH,MAAA,CAC5C;IAAA,EAD0D;IAOtChB,EAAA,CAAAU,YAAA,EAAa;;;;IATFV,EAAA,CAAAW,UAAA,mBAAkB,sCAAA+H,OAAA,CAAAnG,MAAA,CAAAkG,MAAA,aAAAC,OAAA,CAAAnG,MAAA,CAAAoG,SAAA,YAAAD,OAAA,CAAAE,eAAA,CAAAF,OAAA,CAAAnG,MAAA,CAAAoG,SAAA,IAAAD,OAAA,CAAAG,gBAAA,iBAAAH,OAAA,CAAA7H,WAAA,CAAAC,SAAA,yCAAA4H,OAAA,CAAA7H,WAAA,CAAAC,SAAA;;;;;;;;IAUhCd,EAAA,CAAAC,cAAA,WAA4I;IAAAD,EAAA,CAAAqB,MAAA,GAAoC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA1HV,EAAA,CAAA8I,UAAA,CAAA9I,EAAA,CAAAiD,eAAA,IAAA8F,GAAA,EAA8E;IAAC/I,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAyB,iBAAA,CAAAuH,OAAA,CAAAC,cAAA,CAAAD,OAAA,CAAAzG,MAAA,CAAAoG,SAAA,EAAoC;;;;;;;;IAChL3I,EAAA,CAAAC,cAAA,WAAiJ;IAAAD,EAAA,CAAAqB,MAAA,GAAoC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA9HV,EAAA,CAAA8I,UAAA,CAAA9I,EAAA,CAAAiD,eAAA,IAAAiG,GAAA,EAAkF;IAAClJ,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAyB,iBAAA,CAAA0H,OAAA,CAAAF,cAAA,CAAAE,OAAA,CAAA5G,MAAA,CAAAoG,SAAA,EAAoC;;;;;;;;IACrL3I,EAAA,CAAAC,cAAA,WAA+I;IAAAD,EAAA,CAAAqB,MAAA,GAAoC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA5HV,EAAA,CAAA8I,UAAA,CAAA9I,EAAA,CAAAiD,eAAA,IAAAmG,GAAA,EAAgF;IAACpJ,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAyB,iBAAA,CAAA4H,OAAA,CAAAJ,cAAA,CAAAI,OAAA,CAAA9G,MAAA,CAAAoG,SAAA,EAAoC;;;;;;;;IACnL3I,EAAA,CAAAC,cAAA,WAA4I;IAAAD,EAAA,CAAAqB,MAAA,GAAoC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAAzHV,EAAA,CAAA8I,UAAA,CAAA9I,EAAA,CAAAiD,eAAA,IAAAqG,GAAA,EAA6E;IAACtJ,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAyB,iBAAA,CAAA8H,OAAA,CAAAN,cAAA,CAAAM,OAAA,CAAAhH,MAAA,CAAAoG,SAAA,EAAoC;;;;;;;;IAChL3I,EAAA,CAAAC,cAAA,WAA8I;IAAAD,EAAA,CAAAqB,MAAA,GAAoC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA3HV,EAAA,CAAA8I,UAAA,CAAA9I,EAAA,CAAAiD,eAAA,IAAAuG,GAAA,EAA+E;IAACxJ,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAyB,iBAAA,CAAAgI,OAAA,CAAAR,cAAA,CAAAQ,OAAA,CAAAlH,MAAA,CAAAoG,SAAA,EAAoC;;;;;IAlB1L3I,EAAA,CAAAC,cAAA,cAAiT;IACjPD,EAAA,CAAAqB,MAAA,GAAgD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;IAClHV,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAqF,UAAA,IAAAqE,2DAAA,yBAUc;IAChB1J,EAAA,CAAAqF,UAAA,IAAAsE,qDAAA,mBAAuL;IACvL3J,EAAA,CAAAqF,UAAA,IAAAuE,qDAAA,mBAA4L;IAC5L5J,EAAA,CAAAqF,UAAA,IAAAwE,qDAAA,mBAA0L;IAC1L7J,EAAA,CAAAqF,UAAA,IAAAyE,qDAAA,mBAAuL;IACvL9J,EAAA,CAAAqF,UAAA,IAAA0E,qDAAA,mBAAyL;IAC3L/J,EAAA,CAAAU,YAAA,EAAM;;;;IAnBkDV,EAAA,CAAA8I,UAAA,CAAAkB,OAAA,CAAAC,QAAA,CAAAC,IAAA,IAAAF,OAAA,CAAAG,QAAA,CAAAC,QAAA,IAAAJ,OAAA,CAAAtE,WAAA,eAAAsE,OAAA,CAAAzH,MAAA,CAAAwF,UAAA,YAAAiC,OAAA,CAAAzH,MAAA,CAAAwF,UAAA,aAAAiC,OAAA,CAAAK,oBAAA,CAAAC,QAAA,CAAAN,OAAA,CAAAzH,MAAA,CAAAwF,UAAA,uBAA6M;IAC3M/H,EAAA,CAAAsB,SAAA,GAAgD;IAAhDtB,EAAA,CAAAyB,iBAAA,CAAAuI,OAAA,CAAAnJ,WAAA,CAAAC,SAAA,wBAAgD;IAEzFd,EAAA,CAAAsB,SAAA,GAA2B;IAA3BtB,EAAA,CAAAW,UAAA,SAAAqJ,OAAA,CAAAtE,WAAA,aAA2B;IAWnC1F,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAW,UAAA,SAAAqJ,OAAA,CAAAtE,WAAA,gBAAAsE,OAAA,CAAAzH,MAAA,CAAAoG,SAAA,MAAoD;IACpD3I,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAW,UAAA,SAAAqJ,OAAA,CAAAtE,WAAA,gBAAAsE,OAAA,CAAAzH,MAAA,CAAAoG,SAAA,MAAoD;IACpD3I,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAW,UAAA,SAAAqJ,OAAA,CAAAtE,WAAA,gBAAAsE,OAAA,CAAAzH,MAAA,CAAAoG,SAAA,MAAoD;IACpD3I,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAW,UAAA,SAAAqJ,OAAA,CAAAtE,WAAA,gBAAAsE,OAAA,CAAAzH,MAAA,CAAAoG,SAAA,MAAoD;IACpD3I,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAW,UAAA,SAAAqJ,OAAA,CAAAtE,WAAA,gBAAAsE,OAAA,CAAAzH,MAAA,CAAAoG,SAAA,MAAoD;;;;;IAOzD3I,EAAA,CAAAC,cAAA,gBAA0H;IAAAD,EAAA,CAAAqB,MAAA,GAAoD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA5DV,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAyB,iBAAA,CAAA8I,OAAA,CAAA1J,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAHxLd,EAAA,CAAAC,cAAA,cAAsI;IAChID,EAAA,CAAA0B,SAAA,gBAAwE;IACxE1B,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAqF,UAAA,IAAAmF,sDAAA,oBAAsL;IAC1LxK,EAAA,CAAAU,YAAA,EAAM;;;;IAD2BV,EAAA,CAAAsB,SAAA,GAA2F;IAA3FtB,EAAA,CAAAW,UAAA,SAAA8J,OAAA,CAAA7E,aAAA,CAAAC,QAAA,CAAA4C,MAAA,CAAApC,KAAA,KAAAoE,OAAA,CAAA7E,aAAA,CAAAC,QAAA,CAAA4C,MAAA,CAAAnC,MAAA,kBAAAmE,OAAA,CAAA7E,aAAA,CAAAC,QAAA,CAAA4C,MAAA,CAAAnC,MAAA,CAAAC,QAAA,EAA2F;;;;;IAMRvG,EAAA,CAAAC,cAAA,eAAiD;IAAAD,EAAA,CAAAqB,MAAA,QAAC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;;;IADvLV,EAAA,CAAAC,cAAA,cAAyP;IACpLD,EAAA,CAAAqB,MAAA,GAAyD;IAAArB,EAAA,CAAAqF,UAAA,IAAAqF,qDAAA,mBAAyD;IAAA1K,EAAA,CAAAU,YAAA,EAAQ;IAC7LV,EAAA,CAAAC,cAAA,cAAiB;IAGRD,EAAA,CAAAE,UAAA,2BAAAyK,8EAAA3J,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAwK,IAAA;MAAA,MAAAC,OAAA,GAAA7K,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAqK,OAAA,CAAAtI,MAAA,CAAAuI,KAAA,GAAA9J,MAAA,CAC7B;IAAA,EAD0C,qBAAA+J,wEAAA/J,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAwK,IAAA;MAAA,MAAAI,OAAA,GAAAhL,EAAA,CAAAO,aAAA;MAAA,OAKfP,EAAA,CAAAQ,WAAA,CAAAwK,OAAA,CAAAC,cAAA,CAAAjK,MAAA,CAAsB;IAAA,EALP;IAFjChB,EAAA,CAAAU,YAAA,EAQE;;;;IAX6BV,EAAA,CAAA8I,UAAA,CAAAoC,OAAA,CAAAjB,QAAA,CAAAC,IAAA,IAAAgB,OAAA,CAAAf,QAAA,CAAAC,QAAA,GAAAc,OAAA,CAAA3I,MAAA,CAAAwF,UAAA,YAAAmD,OAAA,CAAA3I,MAAA,CAAAwF,UAAA,aAAAmD,OAAA,CAAAb,oBAAA,CAAAC,QAAA,CAAAY,OAAA,CAAA3I,MAAA,CAAAwF,UAAA,uBAAgL;IAC9I/H,EAAA,CAAAsB,SAAA,GAAyD;IAAzDtB,EAAA,CAAAyB,iBAAA,CAAAyJ,OAAA,CAAArK,WAAA,CAAAC,SAAA,iCAAyD;IAAOd,EAAA,CAAAsB,SAAA,GAAmB;IAAnBtB,EAAA,CAAAW,UAAA,SAAAuK,OAAA,CAAA3I,MAAA,CAAAkG,MAAA,CAAmB;IAI7IzI,EAAA,CAAAsB,SAAA,GAA0B;IAA1BtB,EAAA,CAAAW,UAAA,YAAAuK,OAAA,CAAA3I,MAAA,CAAAuI,KAAA,CAA0B,aAAAI,OAAA,CAAA3I,MAAA,CAAAkG,MAAA,2CAAAyC,OAAA,CAAArK,WAAA,CAAAC,SAAA;;;;;IAajCd,EAAA,CAAAC,cAAA,gBAA2N;IAAAD,EAAA,CAAAqB,MAAA,GAAoD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA5DV,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAyB,iBAAA,CAAA0J,OAAA,CAAAtK,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAHnRd,EAAA,CAAAC,cAAA,cAA4N;IAC1ND,EAAA,CAAA0B,SAAA,gBAAwE;IACxE1B,EAAA,CAAAC,cAAA,cAAiB;IACfD,EAAA,CAAAqF,UAAA,IAAA+F,sDAAA,oBAAuR;IACzRpL,EAAA,CAAAU,YAAA,EAAM;;;;IADyBV,EAAA,CAAAsB,SAAA,GAA4L;IAA5LtB,EAAA,CAAAW,UAAA,SAAA0K,OAAA,CAAAzF,aAAA,CAAAC,QAAA,CAAAiF,KAAA,CAAAzE,KAAA,KAAAgF,OAAA,CAAAzF,aAAA,CAAAC,QAAA,CAAAiF,KAAA,CAAAxE,MAAA,kBAAA+E,OAAA,CAAAzF,aAAA,CAAAC,QAAA,CAAAiF,KAAA,CAAAxE,MAAA,CAAAC,QAAA,KAAA8E,OAAA,CAAA9I,MAAA,CAAAkG,MAAA,aAAA4C,OAAA,CAAA9I,MAAA,CAAAuI,KAAA,YAAAO,OAAA,CAAA9I,MAAA,CAAAuI,KAAA,CAAAQ,IAAA,UAA4L;;;;;IAUvNtL,EAAA,CAAAC,cAAA,SAAI;IACED,EAAA,CAAAqB,MAAA,GAA4C;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACrDV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,GAAiD;IAAArB,EAAA,CAAAU,YAAA,EAAK;IAC1DV,EAAA,CAAAC,cAAA,aAA6B;IAAAD,EAAA,CAAAqB,MAAA,GAAuD;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACzFV,EAAA,CAAAC,cAAA,aAA6B;IAAAD,EAAA,CAAAqB,MAAA,GAA+C;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACjFV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,IAAiD;IAAArB,EAAA,CAAAU,YAAA,EAAK;;;;IAJtDV,EAAA,CAAAsB,SAAA,GAA4C;IAA5CtB,EAAA,CAAAyB,iBAAA,CAAA8J,OAAA,CAAA1K,WAAA,CAAAC,SAAA,oBAA4C;IAC5Cd,EAAA,CAAAsB,SAAA,GAAiD;IAAjDtB,EAAA,CAAAyB,iBAAA,CAAA8J,OAAA,CAAA1K,WAAA,CAAAC,SAAA,yBAAiD;IACxBd,EAAA,CAAAsB,SAAA,GAAuD;IAAvDtB,EAAA,CAAAyB,iBAAA,CAAA8J,OAAA,CAAA1K,WAAA,CAAAC,SAAA,+BAAuD;IACvDd,EAAA,CAAAsB,SAAA,GAA+C;IAA/CtB,EAAA,CAAAyB,iBAAA,CAAA8J,OAAA,CAAA1K,WAAA,CAAAC,SAAA,uBAA+C;IACxEd,EAAA,CAAAsB,SAAA,GAAiD;IAAjDtB,EAAA,CAAAyB,iBAAA,CAAA8J,OAAA,CAAA1K,WAAA,CAAAC,SAAA,yBAAiD;;;;;;IAUnDd,EAAA,CAAAC,cAAA,gBAOE;IALKD,EAAA,CAAAE,UAAA,2BAAAsL,qGAAAxK,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAqL,IAAA;MAAA,MAAAC,QAAA,GAAA1L,EAAA,CAAAO,aAAA,GAAAoL,SAAA;MAAA,OAAa3L,EAAA,CAAAQ,WAAA,CAAAkL,QAAA,CAAA/E,OAAA,GAAA3F,MAAA,CACnC;IAAA,EADgD,qBAAA4K,+FAAA5K,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAqL,IAAA;MAAA,MAAAC,QAAA,GAAA1L,EAAA,CAAAO,aAAA,GAAAoL,SAAA;MAAA,MAAAE,OAAA,GAAA7L,EAAA,CAAAO,aAAA;MAAA,OAIfP,EAAA,CAAAQ,WAAA,CAAAqL,OAAA,CAAAC,oBAAA,CAAA9K,MAAA,EAAA0K,QAAA,CAAkC;IAAA,EAJnB;IAFjC1L,EAAA,CAAAU,YAAA,EAOE;;;;IALKV,EAAA,CAAAW,UAAA,YAAA+K,QAAA,CAAA/E,OAAA,CAA0B;;;;;IAMjC3G,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAqB,MAAA,GAAgB;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAAvBV,EAAA,CAAAsB,SAAA,GAAgB;IAAhBtB,EAAA,CAAAyB,iBAAA,CAAAiK,QAAA,CAAA/E,OAAA,CAAgB;;;;;IACtD3G,EAAA,CAAAC,cAAA,gBAAkI;IAAAD,EAAA,CAAAqB,MAAA,GAAoD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA5DV,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAyB,iBAAA,CAAAsK,OAAA,CAAAlL,WAAA,CAAAC,SAAA,4BAAoD;;;;;IACtLd,EAAA,CAAAC,cAAA,gBAAwF;IAAAD,EAAA,CAAAqB,MAAA,GAA+D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAvEV,EAAA,CAAAsB,SAAA,GAA+D;IAA/DtB,EAAA,CAAAyB,iBAAA,CAAAuK,OAAA,CAAAnL,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAiD,eAAA,IAAAC,GAAA,GAA+D;;;;;IAhB3JlD,EAAA,CAAAC,cAAA,aAAmC;IAC7BD,EAAA,CAAAqB,MAAA,GAAW;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACpBV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,GAAmB;IAAArB,EAAA,CAAAU,YAAA,EAAK;IAC5BV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,GAA+B;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACxCV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,GAAiD;;IAAArB,EAAA,CAAAU,YAAA,EAAK;IAC1DV,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAAqF,UAAA,KAAA4G,qEAAA,oBAOE;IACFjM,EAAA,CAAAqF,UAAA,KAAA6G,oEAAA,mBAA6D;IAC7DlM,EAAA,CAAAqF,UAAA,KAAA8G,qEAAA,oBAA8L;IAC9LnM,EAAA,CAAAqF,UAAA,KAAA+G,qEAAA,oBAA+J;IAEjKpM,EAAA,CAAAU,YAAA,EAAK;;;;;;IAlBHV,EAAA,CAAAW,UAAA,cAAA0L,OAAA,CAAAC,OAAA,CAAAZ,QAAA,CAAAa,EAAA,EAA8B;IAC5BvM,EAAA,CAAAsB,SAAA,GAAW;IAAXtB,EAAA,CAAAyB,iBAAA,CAAA+K,KAAA,KAAW;IACXxM,EAAA,CAAAsB,SAAA,GAAmB;IAAnBtB,EAAA,CAAAyB,iBAAA,CAAAiK,QAAA,CAAAe,QAAA,CAAmB;IACnBzM,EAAA,CAAAsB,SAAA,GAA+B;IAA/BtB,EAAA,CAAAyB,iBAAA,CAAA4K,OAAA,CAAApD,cAAA,CAAAyC,QAAA,CAAAjD,MAAA,EAA+B;IAC/BzI,EAAA,CAAAsB,SAAA,GAAiD;IAAjDtB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA0M,WAAA,OAAAhB,QAAA,CAAAiB,WAAA,yBAAiD;IAE3C3M,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAW,UAAA,SAAA0L,OAAA,CAAA3G,WAAA,aAA6B;IAQ9B1F,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAW,UAAA,SAAA0L,OAAA,CAAA3G,WAAA,aAA6B;IACP1F,EAAA,CAAAsB,SAAA,GAAmG;IAAnGtB,EAAA,CAAAW,UAAA,SAAA0L,OAAA,CAAAC,OAAA,CAAAZ,QAAA,CAAAa,EAAA,EAAA1G,QAAA,CAAAc,OAAA,CAAAN,KAAA,KAAAgG,OAAA,CAAAC,OAAA,CAAAZ,QAAA,CAAAa,EAAA,EAAA1G,QAAA,CAAAc,OAAA,CAAAL,MAAA,kBAAA+F,OAAA,CAAAC,OAAA,CAAAZ,QAAA,CAAAa,EAAA,EAAA1G,QAAA,CAAAc,OAAA,CAAAL,MAAA,CAAAC,QAAA,EAAmG;IACnGvG,EAAA,CAAAsB,SAAA,GAAyD;IAAzDtB,EAAA,CAAAW,UAAA,SAAA0L,OAAA,CAAAC,OAAA,CAAAZ,QAAA,CAAAa,EAAA,EAAA1G,QAAA,CAAAc,OAAA,CAAAL,MAAA,kBAAA+F,OAAA,CAAAC,OAAA,CAAAZ,QAAA,CAAAa,EAAA,EAAA1G,QAAA,CAAAc,OAAA,CAAAL,MAAA,CAAAsG,SAAA,CAAyD;;;;;;;;;;IA9BhG5M,EAAA,CAAAC,cAAA,cAA4I;IAE1FD,EAAA,CAAAqB,MAAA,GAAoD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;IAC5GV,EAAA,CAAAC,cAAA,kBAAyF;IACvFD,EAAA,CAAAqF,UAAA,IAAAwH,4DAAA,2BAQc;IACd7M,EAAA,CAAAqF,UAAA,IAAAyH,4DAAA,4BAqBc;IAChB9M,EAAA,CAAAU,YAAA,EAAU;;;;IAjCsCV,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAyB,iBAAA,CAAAsL,OAAA,CAAAlM,WAAA,CAAAC,SAAA,0BAAoD;IACvEd,EAAA,CAAAsB,SAAA,GAAmB;IAAnBtB,EAAA,CAAAW,UAAA,UAAAoM,OAAA,CAAAC,SAAA,CAAmB,eAAAhN,EAAA,CAAAiD,eAAA,IAAAgK,GAAA;;;;;;IAmClDjN,EAAA,CAAAC,cAAA,cAA0G;IACGD,EAAA,CAAAE,UAAA,mBAAAgN,yEAAA;MAAAlN,EAAA,CAAAI,aAAA,CAAA+M,IAAA;MAAA,MAAAC,OAAA,GAAApN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAA4M,OAAA,CAAAC,mBAAA,GAA+B,KAAK;IAAA,EAAC;IAACrN,EAAA,CAAAU,YAAA,EAAW;IACxJV,EAAA,CAAA0B,SAAA,mBAAuV;IAC3V1B,EAAA,CAAAU,YAAA,EAAM;;;;IAF6CV,EAAA,CAAAsB,SAAA,GAAuD;IAAvDtB,EAAA,CAAAW,UAAA,UAAA2M,OAAA,CAAAzM,WAAA,CAAAC,SAAA,yBAAuD;IACnDd,EAAA,CAAAsB,SAAA,GAAkO;IAAlOtB,EAAA,CAAAW,UAAA,aAAA2M,OAAA,CAAA1H,aAAA,CAAA2H,OAAA,IAAAD,OAAA,CAAA5H,WAAA,iBAAA4H,OAAA,CAAA3H,gBAAA,IAAA2H,OAAA,CAAA/K,MAAA,CAAAkG,MAAA,YAAA6E,OAAA,CAAA/K,MAAA,CAAAuI,KAAA,YAAAwC,OAAA,CAAA/K,MAAA,CAAAuI,KAAA,CAAAQ,IAAA,YAAAgC,OAAA,CAAAN,SAAA,CAAAjH,MAAA,SAAAuH,OAAA,CAAAE,WAAA,GAAkO,UAAAF,OAAA,CAAAzM,WAAA,CAAAC,SAAA;;;;;;;;;;;;;;;;;;;ADjarS,OAAM,MAAO2M,6BAA8B,SAAQ7N,aAAa;EAwD9D8N,YACmCC,aAA4B,EAC3BC,cAA8B,EACtBC,sBAA8C,EAC9EC,WAAwB,EACxBC,QAAkB;IAC5B,KAAK,CAACA,QAAQ,CAAC;IALkB,KAAAJ,aAAa,GAAbA,aAAa;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACN,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtD,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAhBpB,KAAAlL,oBAAoB,GAAW,EAAE;IAIjC,KAAA8C,gBAAgB,GAAa,IAAI;IACjC,KAAAqI,SAAS,GAAS,EAAE;IAEpB,KAAAC,eAAe,GAAa,KAAK;IACjC,KAAA3B,OAAO,GAAS,EAAE;IAClB,KAAA4B,UAAU,GAAY,EAAE;IACxB,KAAAC,WAAW,GAAY,KAAK;IAmsBP,KAAAxO,SAAS,GAAGA,SAAS;EA3rB1C;EAEAyO,QAAQA,CAAA;IACN,IAAIC,EAAE,GAAG,IAAI;IACbA,EAAE,CAACF,WAAW,GAAG,KAAK;IACtB,IAAI,CAAClE,QAAQ,GAAG,IAAI,CAACqE,cAAc,CAACrE,QAAQ;IAC5C,IAAI,CAACoD,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAAC3H,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAACyE,QAAQ,GAAGxK,SAAS,CAAC4O,SAAS;IACnC,IAAI,CAACvB,SAAS,GAAG,EAAE;IACnB,IAAI,CAACzK,MAAM,GAAG;MACZgK,EAAE,EAAE,IAAI;MACR5J,WAAW,EAAE,IAAI;MACjBY,YAAY,EAAE,IAAI;MAClBS,YAAY,EAAE,IAAI;MAClB2C,OAAO,EAAE,IAAI;MACbU,IAAI,EAAE,IAAI;MACVyD,KAAK,EAAE,IAAI;MACXZ,IAAI,EAAEvK,SAAS,CAAC6O,YAAY,CAACC,WAAW;MACxC9J,GAAG,EAAE,IAAI;MACT8D,MAAM,EAAE,IAAI;MACZE,SAAS,EAAE,IAAI;MACfZ,UAAU,EAAE,IAAI;MAChB3G,YAAY,EAAE;KACf;IACD,IAAI,CAACsN,cAAc,GAAG,CACpB;MACEC,KAAK,EAAE,IAAI,CAAC9N,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC3DgF,KAAK,EAAE;KACR,CACF;IACD,IAAI,CAAC8C,eAAe,GAAG;MACrB,CAAC,EAAE,CAAC;QACF+F,KAAK,EAAEN,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;QACzDgF,KAAK,EAAE;OACR,EACD;QACI6I,KAAK,EAAEN,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QACvDgF,KAAK,EAAE;OACV,CACA;MACD,CAAC,EAAE,CACD;QACE6I,KAAK,EAAEN,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;QAC3DgF,KAAK,EAAE;OACR,EACD;QACE6I,KAAK,EAAEN,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QACvDgF,KAAK,EAAE;OACR,CACF;MACD,CAAC,EAAE,CACD;QACE6I,KAAK,EAAEN,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACrDgF,KAAK,EAAE;OACR,EACD;QACE6I,KAAK,EAAEN,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QACvDgF,KAAK,EAAE;OACR;KAEJ;IACD,IAAI,CAAC+C,gBAAgB,GAAG,CACtB;MACE8F,KAAK,EAAEN,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;MACpDgF,KAAK,EAAE;KACR,EACD;MACE6I,KAAK,EAAEN,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MACzDgF,KAAK,EAAE;KACR,EACD;MACE6I,KAAK,EAAEN,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC3DgF,KAAK,EAAE;KACR,EACD;MACE6I,KAAK,EAAEN,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACvDgF,KAAK,EAAE;KACR,EACD;MACE6I,KAAK,EAAEN,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;MACrDgF,KAAK,EAAE;KACR,CACF;IACD,IAAI,CAAC3E,UAAU,GAAG;MAChBC,YAAY,EAAE,IAAI;MAClBwN,KAAK,EAAE,IAAI;MACX5K,YAAY,EAAE,IAAI;MAClBT,YAAY,EAAE,IAAI;MAClB2G,IAAI,EAAEvK,SAAS,CAAC6O,YAAY,CAACC,WAAW;MACxChG,MAAM,EAAE;KACT;IACD,IAAI,CAAC7G,OAAO,GAAG,CACb;MACEiN,IAAI,EAAE,IAAI,CAAChO,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;MACzDgO,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI,CAAChF,QAAQ,CAACC,IAAI,IAAIvK,SAAS,CAAC4O,SAAS,CAACW,KAAK;MACvDC,MAAM,EAAE;KACT,EACD;MACEN,IAAI,EAAE,IAAI,CAAChO,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7DgO,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACVC,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAE;QACHC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;;KAErB,EAAE;MACDZ,IAAI,EAAE,IAAI,CAAChO,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;MACtDgO,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACRC,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAE;QACHC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;;KAEvB,EAAE;MACDZ,IAAI,EAAE,IAAI,CAAChO,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;MACtDgO,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE;KACT,EAAE;MACDN,IAAI,EAAE,IAAI,CAAChO,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACxDgO,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZC,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAE;QACLC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;;KAEjB,EACD;MACEZ,IAAI,EAAE,IAAI,CAAChO,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC5DgO,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZO,eAAeA,CAAC5J,KAAK;QACnB,OAAOuI,EAAE,CAACsB,WAAW,CAACC,mBAAmB,CAAC,IAAIC,IAAI,CAAC/J,KAAK,CAAC,CAAC;MAC5D;KACD,EACD;MACE+I,IAAI,EAAE,IAAI,CAAChO,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC5DgO,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZO,eAAeA,CAAC5J,KAAK;QACnB,OAAOA,KAAK,GAAGuI,EAAE,CAACsB,WAAW,CAACC,mBAAmB,CAAC,IAAIC,IAAI,CAAC/J,KAAK,CAAC,CAAC,GAAG,EAAE;MACzE;KACD,EACD;MACE+I,IAAI,EAAE,IAAI,CAAChO,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;MACzDgO,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbI,aAAa,EAAE,IAAI;MACnBH,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACVE,KAAK,EAAC;QACJS,KAAK,EAAE,OAAO;QACZC,MAAM,EAAE;;KAEf,EACD;MACElB,IAAI,EAAE,IAAI,CAAChO,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MACvDgO,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZa,gBAAgB,EAAGlK,KAAK,IAAI;QAC1B,IAAIA,KAAK,IAAInG,SAAS,CAACsQ,cAAc,CAACC,GAAG,EAAE;UACzC,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,CAAC;SAC5E,MAAM,IAAIpK,KAAK,IAAInG,SAAS,CAACsQ,cAAc,CAACE,QAAQ,EAAE;UACrD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,iBAAiB,EAAE,cAAc,EAAE,cAAc,CAAC;SAChF,MAAM,IAAIrK,KAAK,IAAInG,SAAS,CAACsQ,cAAc,CAACG,WAAW,EAAE;UACxD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,CAAC;SAC9E,MAAM,IAAItK,KAAK,IAAInG,SAAS,CAACsQ,cAAc,CAACI,MAAM,EAAE;UACnD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,CAAC;SAC3E,MAAM,IAAIvK,KAAK,IAAInG,SAAS,CAACsQ,cAAc,CAACK,IAAI,EAAE;UACjD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC;;QAE9E,OAAO,EAAE;MACX,CAAC;MACDZ,eAAe,EAAE,SAAAA,CAAU5J,KAAK;QAC9B,IAAIA,KAAK,IAAInG,SAAS,CAACsQ,cAAc,CAACC,GAAG,EAAE;UACzC,OAAO7B,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;SACrD,MAAM,IAAIgF,KAAK,IAAInG,SAAS,CAACsQ,cAAc,CAACE,QAAQ,EAAE;UACrD,OAAO9B,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;SAC1D,MAAM,IAAIgF,KAAK,IAAInG,SAAS,CAACsQ,cAAc,CAACG,WAAW,EAAE;UACxD,OAAO/B,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;SAC5D,MAAM,IAAIgF,KAAK,IAAInG,SAAS,CAACsQ,cAAc,CAACI,MAAM,EAAE;UACnD,OAAOhC,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SACxD,MAAM,IAAIgF,KAAK,IAAInG,SAAS,CAACsQ,cAAc,CAACK,IAAI,EAAE;UACjD,OAAOjC,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;;QAEvD,OAAO,EAAE;MACX;KACD,CACF;IAED,IAAI,CAACgB,WAAW,GAAG;MACjByO,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE,KAAK;MAC1BC,MAAM,EAAE,CACN;QACEC,IAAI,EAAE,mBAAmB;QACzBC,OAAO,EAAE,IAAI,CAAChQ,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACzDgQ,IAAI,EAAE,SAAAA,CAAUvE,EAAE,EAAEwE,IAAI;UACtB1C,EAAE,CAAC2C,mBAAmB,CAACzE,EAAE,EAAEwE,IAAI,CAAC;QAClC;OACD,EACD;QACEH,IAAI,EAAE,uBAAuB;QAC7BC,OAAO,EAAE,IAAI,CAAChQ,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACzDgQ,IAAI,EAAE,SAAAA,CAAUvE,EAAE,EAAEwE,IAAI;UACtB1C,EAAE,CAAC4C,iBAAiB,CAAC1E,EAAE,EAAEwE,IAAI,CAAC;QAChC,CAAC;QACDG,UAAU,EAAG,SAAAA,CAAU3E,EAAE,EAAEwE,IAAI;UAC7B,IAAG1C,EAAE,CAACpE,QAAQ,CAACC,IAAI,IAAIvK,SAAS,CAAC4O,SAAS,CAACW,KAAK,IAAIb,EAAE,CAACpE,QAAQ,CAACC,IAAI,IAAIvK,SAAS,CAAC4O,SAAS,CAAC4C,QAAQ,EAAE,OAAO,KAAK;UAClH,IAAG,CAACJ,IAAI,CAACK,SAAS,IAAI,CAACL,IAAI,CAAChJ,UAAU,IAAIsG,EAAE,CAACgD,WAAW,CAAC,CAAC1R,SAAS,CAAC2R,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI;UAC5G,IAAInD,EAAE,CAACpE,QAAQ,CAACC,IAAI,IAAIvK,SAAS,CAAC4O,SAAS,CAACnE,QAAQ,IAAI2G,IAAI,CAACK,SAAS,KAAK/C,EAAE,CAACpE,QAAQ,CAACsC,EAAE,IACpF8B,EAAE,CAACpE,QAAQ,CAACC,IAAI,IAAIvK,SAAS,CAAC4O,SAAS,CAACnE,QAAQ,IAAI2G,IAAI,CAAChJ,UAAU,IAAI,IAAK,EAAG;YAClF,OAAO,KAAK;;UAEd,IAAIsG,EAAE,CAACgD,WAAW,CAAC,CAAC1R,SAAS,CAAC2R,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,MAC/D,OAAO,KAAK;QACrB;OACD;KAEJ;IACD,IAAI,CAACzP,UAAU,GAAG,CAAC;IACnB,IAAI,CAACG,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,IAAI,GAAG,kBAAkB;IAC9B,IAAI,CAACN,OAAO,GAAG;MACb8E,OAAO,EAAE,EAAE;MACX8K,KAAK,EAAE;KACR;IACD,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAAC5D,WAAW,CAAC6D,KAAK,CAAC,IAAI,CAACxQ,UAAU,CAAC;IAC/D,IAAI,CAACyE,aAAa,GAAG,IAAI,CAACkI,WAAW,CAAC6D,KAAK,CAAC,IAAI,CAACpP,MAAM,CAAC;IACxD,IAAI,CAACqP,eAAe,EAAE;IACtB,IAAI,CAACvH,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACrI,MAAM,CAAC,IAAI,CAACD,UAAU,EAAE,IAAI,CAACG,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAAChB,UAAU,CAAC;EACzE;EAEA8H,cAAcA,CAACnD,KAAK;IAClB,IAAIuI,EAAE,GAAG,IAAI;IACb;MACE,IAAIvI,KAAK,IAAInG,SAAS,CAACsQ,cAAc,CAACC,GAAG,EAAE;QACzC,OAAO7B,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;OACrD,MAAM,IAAIgF,KAAK,IAAInG,SAAS,CAACsQ,cAAc,CAACE,QAAQ,EAAE;QACrD,OAAO9B,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;OAC1D,MAAM,IAAIgF,KAAK,IAAInG,SAAS,CAACsQ,cAAc,CAACG,WAAW,EAAE;QACxD,OAAO/B,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;OAC5D,MAAM,IAAIgF,KAAK,IAAInG,SAAS,CAACsQ,cAAc,CAACI,MAAM,EAAE;QACnD,OAAOhC,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;OACxD,MAAM,IAAIgF,KAAK,IAAInG,SAAS,CAACsQ,cAAc,CAACK,IAAI,EAAE;QACjD,OAAOjC,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;;MAEvD,OAAO,EAAE;;EAEb;EAEA+Q,YAAYA,CAAC/L,KAAK;IAChB,IAAIuI,EAAE,GAAG,IAAI;IACb;IACA,OAAOA,EAAE,CAACsB,WAAW,CAACC,mBAAmB,CAAC,IAAIC,IAAI,CAAC/J,KAAK,CAAC,CAAC;EAC5D;EAEA9D,MAAMA,CAAC8P,IAAI,EAAEC,KAAK,EAAE5P,IAAI,EAAE6P,MAAM;IAC9B,IAAI3D,EAAE,GAAG,IAAI;IACbA,EAAE,CAACF,WAAW,GAAG,KAAK;IACtB,IAAI,CAACpM,UAAU,GAAG+P,IAAI;IACtB,IAAI,CAAC5P,QAAQ,GAAG6P,KAAK;IACrB,IAAI,CAAC5P,IAAI,GAAGA,IAAI;IAChB,IAAI8P,UAAU,GAAG;MACfH,IAAI;MACJ/C,IAAI,EAAEgD,KAAK;MACX5P;KACD;IACD+P,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChR,UAAU,CAAC,CAACiR,OAAO,CAACtD,GAAG,IAAG;MACzC,IAAI,IAAI,CAAC3N,UAAU,CAAC2N,GAAG,CAAC,IAAI,IAAI,EAAE;QAChCmD,UAAU,CAACnD,GAAG,CAAC,GAAG,IAAI,CAAC3N,UAAU,CAAC2N,GAAG,CAAC;;IAE1C,CAAC,CAAC;IACF,IAAI,CAACjN,OAAO,GAAG;MACb8E,OAAO,EAAE,EAAE;MACX8K,KAAK,EAAE;KACR;IACDpD,EAAE,CAACgE,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI,CAAC3E,aAAa,CAAC4E,YAAY,CAACN,UAAU,EAAGO,QAAQ,IAAI;MACvDnE,EAAE,CAACxM,OAAO,GAAG;QACX8E,OAAO,EAAE6L,QAAQ,CAAC7L,OAAO;QACzB8K,KAAK,EAAEe,QAAQ,CAACC;OACjB;MACD,IAAIpE,EAAE,CAACpE,QAAQ,CAACC,IAAI,IAAIvK,SAAS,CAAC4O,SAAS,CAACnE,QAAQ,IAC1CiE,EAAE,CAACpE,QAAQ,CAACC,IAAI,IAAIvK,SAAS,CAAC4O,SAAS,CAACmE,QAAQ,EAAE;QAClD,IAAIC,cAAc,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACzE,EAAE,CAACxM,OAAO,CAAC8E,OAAO,CAACoM,MAAM,CAAChC,IAAI,IAAIA,IAAI,CAAChJ,UAAU,KAAK,IAAI,CAAC,CAC9FiL,GAAG,CAACjC,IAAI,IAAIA,IAAI,CAAChJ,UAAoB,CAAC,CAAC,CAAC;QAE7CsG,EAAE,CAACxM,OAAO,CAAC8E,OAAO,CAACyL,OAAO,CAACrB,IAAI,IAAG;UAC9B,IAAIA,IAAI,CAACkC,QAAQ,KAAK,IAAI,EAAE;YACxBN,cAAc,CAACO,IAAI,CAACnC,IAAI,CAACkC,QAAkB,CAAC;;QAEpD,CAAC,CAAC;QAEF,MAAME,iBAAiB,GAAGP,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACH,cAAc,CAAC,CAAC;QAE7DtE,EAAE,CAACT,cAAc,CAACwF,uBAAuB,CAACD,iBAAiB,EAAGX,QAAQ,IAAI;UACtEnE,EAAE,CAAChE,oBAAoB,GAAGmI,QAAQ;UAClC,IAAI,CAAC1Q,WAAW,GAAG;YACjCyO,gBAAgB,EAAE,KAAK;YACvBC,aAAa,EAAE,KAAK;YACpBC,YAAY,EAAE,IAAI;YAClBC,mBAAmB,EAAE,KAAK;YAC1BC,MAAM,EAAE,CACN;cACEC,IAAI,EAAE,mBAAmB;cACzBC,OAAO,EAAE,IAAI,CAAChQ,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;cACzDgQ,IAAI,EAAE,SAAAA,CAAUvE,EAAE,EAAEwE,IAAI;gBACtB1C,EAAE,CAAC2C,mBAAmB,CAACzE,EAAE,EAAEwE,IAAI,CAAC;cAClC;aACD,EACD;cACEH,IAAI,EAAE,uBAAuB;cAC7BC,OAAO,EAAE,IAAI,CAAChQ,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;cACzDgQ,IAAI,EAAE,SAAAA,CAAUvE,EAAE,EAAEwE,IAAI;gBACtB1C,EAAE,CAAC4C,iBAAiB,CAAC1E,EAAE,EAAEwE,IAAI,CAAC;cAChC,CAAC;cACDG,UAAU,EAAG,SAAAA,CAAU3E,EAAE,EAAEwE,IAAI;gBAC7B;gBACA,IAAG1C,EAAE,CAACpE,QAAQ,CAACC,IAAI,IAAIvK,SAAS,CAAC4O,SAAS,CAACW,KAAK,IAAIb,EAAE,CAACpE,QAAQ,CAACC,IAAI,IAAIvK,SAAS,CAAC4O,SAAS,CAAC4C,QAAQ,EAAE,OAAO,KAAK;gBAClH,IAAI9C,EAAE,CAACpE,QAAQ,CAACC,IAAI,IAAIvK,SAAS,CAAC4O,SAAS,CAACnE,QAAQ,IAAIiE,EAAE,CAACgD,WAAW,CAAC,CAAC1R,SAAS,CAAC2R,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,KAAMnD,EAAE,CAAChE,oBAAoB,KAAKgJ,SAAS,IAAIhF,EAAE,CAAChE,oBAAoB,IAAI,IAAI,CAAE,EAAE,OAAO,IAAI;gBAC1M,IAAKgE,EAAE,CAACpE,QAAQ,CAACC,IAAI,IAAIvK,SAAS,CAAC4O,SAAS,CAACnE,QAAQ,IAAIiE,EAAE,CAACgD,WAAW,CAAC,CAAC1R,SAAS,CAAC2R,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,IAAOT,IAAI,CAAChJ,UAAU,IAAI,IAAI,IAAIsG,EAAE,CAAChE,oBAAoB,CAACC,QAAQ,CAACyG,IAAI,CAAChJ,UAAU,CAAG,EAAE,OAAO,KAAK;gBACjN,IAAKsG,EAAE,CAACpE,QAAQ,CAACC,IAAI,IAAIvK,SAAS,CAAC4O,SAAS,CAACnE,QAAQ,IAAIiE,EAAE,CAACgD,WAAW,CAAC,CAAC1R,SAAS,CAAC2R,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,IAAOT,IAAI,CAAChJ,UAAU,IAAI,IAAI,IAAIgJ,IAAI,CAACK,SAAS,IAAI,IAAI,IAAI/C,EAAE,CAAChE,oBAAoB,CAACC,QAAQ,CAACyG,IAAI,CAACK,SAAS,CAAC,IAAIL,IAAI,CAACK,SAAS,IAAI/C,EAAE,CAACpE,QAAQ,CAACsC,EAAI,EAAE,OAAO,KAAK;gBAC9Q,IAAG,CAACwE,IAAI,CAACK,SAAS,IAAI,CAACL,IAAI,CAAChJ,UAAU,IAAIsG,EAAE,CAACgD,WAAW,CAAC,CAAC1R,SAAS,CAAC2R,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI;gBAC5G,IAAKnD,EAAE,CAACpE,QAAQ,CAACC,IAAI,IAAIvK,SAAS,CAAC4O,SAAS,CAACnE,QAAQ,IAAIiE,EAAE,CAACgD,WAAW,CAAC,CAAC1R,SAAS,CAAC2R,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,KAAOT,IAAI,CAAChJ,UAAU,IAAI,IAAI,IAAI,CAACsG,EAAE,CAAChE,oBAAoB,CAACC,QAAQ,CAACyG,IAAI,CAAChJ,UAAU,CAAC,IAAMgJ,IAAI,CAACK,SAAS,IAAI,IAAI,IAAI,CAAC/C,EAAE,CAAChE,oBAAoB,CAACC,QAAQ,CAACyG,IAAI,CAACK,SAAS,CAAE,CAAC,EAAE,OAAO,IAAI;gBAClS,IAAI/C,EAAE,CAACpE,QAAQ,CAACC,IAAI,IAAIvK,SAAS,CAAC4O,SAAS,CAACnE,QAAQ,IAAI2G,IAAI,CAACK,SAAS,KAAK/C,EAAE,CAACpE,QAAQ,CAACsC,EAAE,IACpF8B,EAAE,CAACpE,QAAQ,CAACC,IAAI,IAAIvK,SAAS,CAAC4O,SAAS,CAACnE,QAAQ,IAAI2G,IAAI,CAAChJ,UAAU,IAAI,IAAK,EAAG;kBAClF,OAAO,KAAK;;gBAEd,IAAIsG,EAAE,CAACgD,WAAW,CAAC,CAAC1R,SAAS,CAAC2R,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,MAC/D,OAAO,KAAK;cACrB;aACD;WAEJ;UACDnD,EAAE,CAACF,WAAW,GAAG,IAAI;QACT,CAAC,CAAC;;IAEd,CAAC,EAAE,IAAI,EAAE,MAAK;MACZE,EAAE,CAACgE,oBAAoB,CAACiB,OAAO,EAAE;IACnC,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAChR,MAAM,GAAG;MACZgK,EAAE,EAAE,IAAI;MACR5J,WAAW,EAAE,IAAI;MACjBY,YAAY,EAAE,IAAI;MAClBS,YAAY,EAAE,IAAI;MAClB2C,OAAO,EAAE,IAAI;MACbU,IAAI,EAAE,IAAI;MACVyD,KAAK,EAAE,IAAI;MACXZ,IAAI,EAAEvK,SAAS,CAAC6O,YAAY,CAACC,WAAW;MACxC9J,GAAG,EAAE,IAAI;MACT8D,MAAM,EAAE,IAAI;MACZE,SAAS,EAAE,IAAI;MACfZ,UAAU,EAAE,IAAI;MAChB3G,YAAY,EAAE;KACf;EACH;EAEAoS,cAAcA,CAAA;IACZ,IAAI,CAACzR,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,MAAM,CAAC,IAAI,CAACD,UAAU,EAAE,IAAI,CAACG,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAAChB,UAAU,CAAC;EACzE;EAEAyQ,eAAeA,CAAA;IACb,IAAI,CAAChE,cAAc,CAACgE,eAAe,CAAEY,QAAQ,IAAI;MAC/C,IAAI,CAAChR,YAAY,GAAGgR,QAAQ,CAACQ,GAAG,CAACS,EAAE,IAAG;QACpC,OAAO;UACL,GAAGA,EAAE;UACLnE,OAAO,EAAE,GAAGmE,EAAE,CAACC,IAAI,MAAMD,EAAE,CAAC5E,IAAI;SACjC;MACH,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EACEvM,eAAeA,CAAClB,YAAY;IACxB,MAAMuS,QAAQ,GAAG,IAAI,CAACnS,YAAY,CAACoS,IAAI,CAACH,EAAE,IAAIA,EAAE,CAACC,IAAI,KAAKtS,YAAY,CAAC;IACvE,OAAOuS,QAAQ,GAAGA,QAAQ,CAACD,IAAI,GAAG,KAAK,GAAGC,QAAQ,CAAC9E,IAAI,GAAG,EAAE;EAChE;EAEF;EACAgF,qBAAqBA,CAAA;IACnB,IAAG,IAAI,CAACxB,oBAAoB,CAACyB,SAAS,IAAI,IAAI,IAAI,IAAI,CAACzG,mBAAmB,IAAI,KAAK,EAAE;IACrF,IAAIgB,EAAE,GAAG,IAAI;IACb,IAAI,CAACgE,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAI,IAAI,CAAC5M,WAAW,IAAI,QAAQ,EAAE;MAChC,IAAIqO,QAAQ,GAAG;QACbpR,WAAW,EAAE,IAAI,CAACJ,MAAM,CAACI,WAAW;QACpCY,YAAY,EAAE,IAAI,CAAChB,MAAM,CAACgB,YAAY;QACtCS,YAAY,EAAE,IAAI,CAACzB,MAAM,CAACyB,YAAY;QACtC2C,OAAO,EAAE,IAAI,CAACpE,MAAM,CAACoE,OAAO;QAC5BU,IAAI,EAAE,IAAI,CAAC9E,MAAM,CAAC8E,IAAI;QACtB6C,IAAI,EAAE,IAAI,CAAC3H,MAAM,CAAC2H,IAAI;QACtBvF,GAAG,EAAE,IAAI,CAACpC,MAAM,CAAC2H,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC3H,MAAM,CAACoC,GAAG,CAACqP,IAAI,CAAC,GAAG,CAAC,GAAG;OAC1D;MACD,IAAID,QAAQ,CAAC/P,YAAY,IAAI,IAAI,EAAC;QAChC,IAAG+P,QAAQ,CAAC/P,YAAY,CAACiQ,UAAU,CAAC,GAAG,CAAC,EAAC;UACvCF,QAAQ,CAAC/P,YAAY,GAAG,IAAI,GAAC+P,QAAQ,CAAC/P,YAAY,CAACkQ,SAAS,CAAC,CAAC,EAAEH,QAAQ,CAAC/P,YAAY,CAAC+B,MAAM,CAAC;SAC9F,MAAK,IAAGgO,QAAQ,CAAC/P,YAAY,CAAC+B,MAAM,IAAI,CAAC,IAAIgO,QAAQ,CAAC/P,YAAY,CAAC+B,MAAM,IAAI,EAAE,EAAC;UAC/EgO,QAAQ,CAAC/P,YAAY,GAAG,IAAI,GAAC+P,QAAQ,CAAC/P,YAAY;;;MAGtD,IAAI,CAAC2J,aAAa,CAACwG,YAAY,CAACJ,QAAQ,EAAGK,IAAI,IAAI;QACjD/F,EAAE,CAACgE,oBAAoB,CAACgC,OAAO,CAAChG,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QACvFuN,EAAE,CAAChB,mBAAmB,GAAG,KAAK;QAC9BgB,EAAE,CAACrM,MAAM,CAACqM,EAAE,CAACtM,UAAU,EAAEsM,EAAE,CAACnM,QAAQ,EAAEmM,EAAE,CAAClM,IAAI,EAAEkM,EAAE,CAAClN,UAAU,CAAC;QAC7D;QACA;QACAkN,EAAE,CAACV,aAAa,CAAC2G,qBAAqB,CAACjG,EAAE,CAACpE,QAAQ,CAAC7I,YAAY,EAAGmT,KAAK,IAAI;UACzE,IAAIC,KAAK,GAAG,EAAE;UACd,KAAK,IAAIC,IAAI,IAAIF,KAAK,CAACG,UAAU,EAAE;YACjCF,KAAK,CAACtB,IAAI,CAAC;cACTyB,MAAM,EAAEF,IAAI,CAACE,MAAM;cACnBC,QAAQ,EAAER,IAAI,CAAC7H;aAChB,CAAC;;UAEJ,IAAG6H,IAAI,EAAErM,UAAU,EAAE;YACnByM,KAAK,CAACtB,IAAI,CAAC;cACTyB,MAAM,EAAEP,IAAI,CAACrM,UAAU;cACvB6M,QAAQ,EAAER,IAAI,CAAC7H;aAChB,CAAC;;UAEJ8B,EAAE,CAACV,aAAa,CAACkH,cAAc,CAACL,KAAK,CAAC;QACxC,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,EAAE,MAAK;QACZnG,EAAE,CAACgE,oBAAoB,CAACiB,OAAO,EAAE;MACnC,CAAC,CAAC;KACH,MAAM,IAAI,IAAI,CAAC5N,WAAW,IAAI,QAAQ,EAAE;MACvC,IAAIqO,QAAQ,GAAG;QACbpR,WAAW,EAAE,IAAI,CAACJ,MAAM,CAACI,WAAW;QACpCY,YAAY,EAAE,IAAI,CAAChB,MAAM,CAACgB,YAAY;QACtCS,YAAY,EAAE,IAAI,CAACzB,MAAM,CAACyB,YAAY;QACtC2C,OAAO,EAAE,IAAI,CAACpE,MAAM,CAACoE,OAAO;QAC5BU,IAAI,EAAE,IAAI,CAAC9E,MAAM,CAAC8E,IAAI;QACtB6C,IAAI,EAAE,IAAI,CAAC3H,MAAM,CAAC2H,IAAI;QACtBvF,GAAG,EAAE,IAAI,CAACpC,MAAM,CAAC2H,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC3H,MAAM,CAACoC,GAAG,GAAG,IAAI;QACnD8D,MAAM,EAAE,IAAI,CAAClG,MAAM,CAACkG,MAAM;QAC1BqC,KAAK,EAAE,IAAI,CAACvI,MAAM,CAACuI,KAAK;QACxB/C,UAAU,EAAE,IAAI,CAACxF,MAAM,CAACwF,UAAU;QAClC+M,OAAO,EAAG,IAAI,CAAC9H;OAChB;MACD,IAAI+G,QAAQ,CAAC/P,YAAY,IAAI,IAAI,EAAC;QAChC,IAAG+P,QAAQ,CAAC/P,YAAY,CAACiQ,UAAU,CAAC,GAAG,CAAC,EAAC;UACvCF,QAAQ,CAAC/P,YAAY,GAAG,IAAI,GAAC+P,QAAQ,CAAC/P,YAAY,CAACkQ,SAAS,CAAC,CAAC,EAAEH,QAAQ,CAAC/P,YAAY,CAAC+B,MAAM,CAAC;SAC9F,MAAK,IAAGgO,QAAQ,CAAC/P,YAAY,CAAC+B,MAAM,IAAI,CAAC,IAAIgO,QAAQ,CAAC/P,YAAY,CAAC+B,MAAM,IAAI,EAAE,EAAC;UAC/EgO,QAAQ,CAAC/P,YAAY,GAAG,IAAI,GAAC+P,QAAQ,CAAC/P,YAAY;;;MAGtD;MACA,IAAI,CAAC2J,aAAa,CAACoH,YAAY,CAAC,IAAI,CAACxS,MAAM,CAACgK,EAAE,EAAEwH,QAAQ,EAAGK,IAAI,IAAI;QACjE/F,EAAE,CAAChB,mBAAmB,GAAG,KAAK;QAC9BgB,EAAE,CAACgE,oBAAoB,CAACgC,OAAO,CAAChG,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QACvFuN,EAAE,CAACrM,MAAM,CAACqM,EAAE,CAACtM,UAAU,EAAEsM,EAAE,CAACnM,QAAQ,EAAEmM,EAAE,CAAClM,IAAI,EAAEkM,EAAE,CAAClN,UAAU,CAAC;QAC7D,IAAGiT,IAAI,CAACrM,UAAU,IAAI,IAAI,IAAIqM,IAAI,CAACrM,UAAU,IAAIsL,SAAS,EAAE;UAC1DhF,EAAE,CAACV,aAAa,CAACkH,cAAc,CAAC,CAAC;YAC/BF,MAAM,EAAEP,IAAI,CAACrM,UAAU;YACvB6M,QAAQ,EAAER,IAAI,CAAC7H;WAChB,CAAC,CAAC;;MAGP,CAAC,EAAE,IAAI,EAAE,MAAK;QACZ8B,EAAE,CAACgE,oBAAoB,CAACiB,OAAO,EAAE;MACnC,CAAC,CAAC;;EAEN;EAEA7S,eAAeA,CAAA;IACb,IAAI,CAAC4M,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC3H,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAACwI,UAAU,GAAG,IAAI,CAACrN,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;IAC1E,IAAI,CAACyS,WAAW,EAAE;IAClB;IACA,IAAI,IAAI,CAACtJ,QAAQ,CAACC,IAAI,KAAKvK,SAAS,CAAC4O,SAAS,CAAC4C,QAAQ,EAAE;MACvD,IAAI,CAAC5O,MAAM,CAACI,WAAW,GAAG,IAAI,CAACsH,QAAQ,CAAC+K,QAAQ,CAACd,SAAS,CAAC,CAAC,EAAE,IAAI,CAACrR,oBAAoB,CAAC;MACxF,IAAI,CAACN,MAAM,CAACyB,YAAY,GAAG,IAAI,CAACiG,QAAQ,CAACgL,KAAK;MAC9C,IAAI,CAAC1S,MAAM,CAACgB,YAAY,GAAG,IAAI,CAAC0G,QAAQ,CAAC2E,KAAK;;IAEhD,IAAI,CAAChJ,aAAa,GAAG,IAAI,CAACkI,WAAW,CAAC6D,KAAK,CAAC,IAAI,CAACpP,MAAM,CAAC;EAC1D;EAEA0O,iBAAiBA,CAAC1E,EAAE,EAAEwE,IAAI;IACxB,IAAI1C,EAAE,GAAG,IAAI;IACb,IAAI,CAACzI,aAAa,CAACsP,KAAK,EAAE;IAC1B,IAAI,CAAChH,UAAU,GAAG,IAAI,CAACrN,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;IAC1E,IAAI,CAAC4E,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAAC2H,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACM,aAAa,CAACwH,eAAe,CAACpE,IAAI,CAACxE,EAAE,EAAG6H,IAAI,IAAI;MACnD/F,EAAE,CAAC9L,MAAM,GAAG;QACVgK,EAAE,EAAE6H,IAAI,CAAC7H,EAAE;QACX5J,WAAW,EAAEyR,IAAI,CAACzR,WAAW;QAC7BY,YAAY,EAAE6Q,IAAI,CAAC7Q,YAAY;QAC/BS,YAAY,EAAEoQ,IAAI,CAACpQ,YAAY;QAC/B2C,OAAO,EAAEyN,IAAI,CAACzN,OAAO;QACrBU,IAAI,EAAE+M,IAAI,CAAC/M,IAAI;QACfyD,KAAK,EAAEsJ,IAAI,CAACtJ,KAAK;QACjBZ,IAAI,EAAEkK,IAAI,CAAClK,IAAI;QACfvF,GAAG,EAAEyP,IAAI,CAACzP,GAAG;QACb8D,MAAM,EAAE,IAAI;QACZE,SAAS,EAAEyL,IAAI,CAAC3L,MAAM;QACtBV,UAAU,EAAEqM,IAAI,CAACrM,UAAU;QAC3B3G,YAAY,EAAEgT,IAAI,CAAChT;OACpB;MACDiN,EAAE,CAACL,SAAS,GAAG;QAAC,GAAGK,EAAE,CAAC9L;MAAM,CAAC;MAC7B8L,EAAE,CAACzI,aAAa,GAAGyI,EAAE,CAACP,WAAW,CAAC6D,KAAK,CAACtD,EAAE,CAAC9L,MAAM,CAAC;MAClD;MACA,IAAI,CAACsL,sBAAsB,CAAC7L,MAAM,CAAC;QAAC4S,QAAQ,EAAE7D,IAAI,CAACxE;MAAE,CAAC,EAAG6I,GAAG,IAAG;QAC7DC,OAAO,CAACC,GAAG,CAACF,GAAG,CAACzO,OAAO,CAAC;QACxB,IAAI,CAACqG,SAAS,GAAGoI,GAAG,CAACzO,OAAO;QAC5B;QACA;QACA;QACE,IAAI,CAACqG,SAAS,CAACoF,OAAO,CAAC/K,IAAI,IAAG;UAC1B,IAAI,CAACiF,OAAO,CAACjF,IAAI,CAACkF,EAAE,CAAC,GAAG,IAAI,CAACuB,WAAW,CAAC6D,KAAK,CAAC;YAC3ChL,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC7G,UAAU,CAACyG,QAAQ,EAAEzG,UAAU,CAAC8M,SAAS,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC2I,qBAAqB,EAAE,CAAC;WAC/F,CAAC;QACN,CAAC,CAAC;QACJlH,EAAE,CAACJ,eAAe,GAAG,IAAI;MAC3B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACN,aAAa,CAAC2G,qBAAqB,CAACjG,EAAE,CAACpE,QAAQ,CAAC7I,YAAY,EAAGgT,IAAI,IAAI;MAC1E/F,EAAE,CAACmH,SAAS,GAAGpB,IAAI,CAACM,UAAU;IAChC,CAAC,CAAC;EACJ;EAEA1D,mBAAmBA,CAACzE,EAAE,EAAEwE,IAAI;IAC1B,IAAI1C,EAAE,GAAG,IAAI;IACb,IAAI,CAACzI,aAAa,CAACsP,KAAK,EAAE;IAC1B,IAAI,CAAChH,UAAU,GAAG,IAAI,CAACrN,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC;IACjF,IAAI,CAAC4E,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAAC2H,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACM,aAAa,CAACwH,eAAe,CAACpE,IAAI,CAACxE,EAAE,EAAG6H,IAAI,IAAI;MACnD/F,EAAE,CAAC9L,MAAM,GAAG;QACVgK,EAAE,EAAE6H,IAAI,CAAC7H,EAAE;QACX5J,WAAW,EAAEyR,IAAI,CAACzR,WAAW;QAC7BY,YAAY,EAAE6Q,IAAI,CAAC7Q,YAAY;QAC/BS,YAAY,EAAEoQ,IAAI,CAACpQ,YAAY;QAC/B2C,OAAO,EAAEyN,IAAI,CAACzN,OAAO;QACrBU,IAAI,EAAE+M,IAAI,CAAC/M,IAAI;QACfyD,KAAK,EAAEsJ,IAAI,CAACtJ,KAAK;QACjBZ,IAAI,EAAEkK,IAAI,CAAClK,IAAI;QACfvF,GAAG,EAAEyP,IAAI,CAACzP,GAAG;QACb8D,MAAM,EAAE,IAAI;QACZE,SAAS,EAAEyL,IAAI,CAAC3L,MAAM;QACtBV,UAAU,EAAEqM,IAAI,CAACrM,UAAU;QACzB3G,YAAY,EAAEgT,IAAI,CAAChT;OAEtB;MACDiN,EAAE,CAACL,SAAS,GAAG;QAAC,GAAGK,EAAE,CAAC9L;MAAM,CAAC;MAC7B8L,EAAE,CAACzI,aAAa,GAAGyI,EAAE,CAACP,WAAW,CAAC6D,KAAK,CAACtD,EAAE,CAAC9L,MAAM,CAAC;MAClD;MACA,IAAI,CAACsL,sBAAsB,CAAC7L,MAAM,CAAC;QAAC4S,QAAQ,EAAE7D,IAAI,CAACxE;MAAE,CAAC,EAAG6I,GAAG,IAAG;QAC7DC,OAAO,CAACC,GAAG,CAACF,GAAG,CAACzO,OAAO,CAAC;QACxB,IAAI,CAACqG,SAAS,GAAGoI,GAAG,CAACzO,OAAO;QAC5B,KAAI,IAAIU,IAAI,IAAI,IAAI,CAAC2F,SAAS,EAAE;UAC9B,IAAI,CAACV,OAAO,CAACjF,IAAI,CAACkF,EAAE,CAAC,GAAG,IAAI,CAACuB,WAAW,CAAC6D,KAAK,CAACtK,IAAI,CAAC;;QAEtDgH,EAAE,CAACJ,eAAe,GAAG,IAAI;MAC3B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACN,aAAa,CAAC2G,qBAAqB,CAACjG,EAAE,CAACpE,QAAQ,CAAC7I,YAAY,EAAGgT,IAAI,IAAI;MAC1E/F,EAAE,CAACmH,SAAS,GAAGpB,IAAI,CAACM,UAAU;IAChC,CAAC,CAAC;EACJ;EAEAvQ,gBAAgBA,CAACsR,KAAK;IACpB,IAAIA,KAAK,CAACC,OAAO,EAAE;MACjB;;IAEF,IAAID,KAAK,CAACE,OAAO,IAAI,CAAC,IAAIF,KAAK,CAACE,OAAO,IAAI,EAAE,IAAKF,KAAK,CAACE,OAAO,IAAI,EAAE,IAAIF,KAAK,CAACE,OAAO,IAAI,EAAE,EAAE;MAC5F;;IAEF,IAAIF,KAAK,CAACE,OAAO,GAAG,EAAE,IAAIF,KAAK,CAACE,OAAO,GAAG,EAAE,EAAE;MAC5CF,KAAK,CAACG,cAAc,EAAE;;IAEtB;IACA,IAAIH,KAAK,CAACE,OAAO,IAAI,EAAE,IAAIF,KAAK,CAACE,OAAO,IAAI,GAAG,IAAIF,KAAK,CAACE,OAAO,IAAI,GAAG,IAAIF,KAAK,CAACE,OAAO,IAAI,GAAG,EAAE;MAC7FF,KAAK,CAACG,cAAc,EAAE;;EAE9B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEE7Q,cAAcA,CAAC8Q,OAAiB;IAC5B,IAAI,CAACA,OAAO,IAAIA,OAAO,CAAC9P,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAACJ,gBAAgB,GAAG,IAAI,CAAC,CAAC;MAC9B;;IAGJ,IAAI,CAACA,gBAAgB,GAAGkQ,OAAO,CAACC,KAAK,CAACnR,GAAG,IAAG;MACxCA,GAAG,GAAGA,GAAG,CAAC2G,IAAI,EAAE;MAChB,MAAMyK,MAAM,GAAG,aAAa,CAAC,CAAK;MAClC,MAAMC,OAAO,GAAG,cAAc,CAAC,CAAG;MAClC,OAAOD,MAAM,CAACE,IAAI,CAACtR,GAAG,CAAC,IAAIqR,OAAO,CAACC,IAAI,CAACtR,GAAG,CAAC;IAChD,CAAC,CAAC;EACN;EACA6I,WAAWA,CAAA;IACP,OAAO0E,MAAM,CAACgE,MAAM,CAAC,IAAI,CAAC5J,OAAO,CAAC,CAACwJ,KAAK,CAAEK,SAAoB,IAAKA,SAAS,CAACC,KAAK,CAAC;EACvF;EACAb,qBAAqBA,CAAA;IACjB,OAAQc,OAAwB,IAA6B;MACzD,MAAMC,YAAY,GAAG,CAACD,OAAO,CAACvQ,KAAK,IAAI,EAAE,EAAEwF,IAAI,EAAE,CAACvF,MAAM,KAAK,CAAC;MAC9D,MAAMwQ,OAAO,GAAG,CAACD,YAAY;MAC7B,OAAOC,OAAO,GAAG,IAAI,GAAG;QAACC,UAAU,EAAE;MAAI,CAAC;IAC9C,CAAC;EACL;EACAhP,aAAaA,CAACiO,KAAK;IACf,IAAIA,KAAK,CAAC3G,GAAG,KAAK,GAAG,KAAK,IAAI,CAACvM,MAAM,CAAC8E,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC9E,MAAM,CAAC8E,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC9E,MAAM,CAAC8E,IAAI,CAACiE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MAC/GmK,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAI,IAAI,CAACrT,MAAM,CAAC8E,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC9E,MAAM,CAAC8E,IAAI,CAACiE,IAAI,EAAE,IAAI,EAAE,EAAE;MAC3D,IAAI,CAAC/I,MAAM,CAAC8E,IAAI,GAAG,IAAI,CAAC9E,MAAM,CAAC8E,IAAI,CAACoP,SAAS,EAAE,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MACvE;;EAER;EACA5P,gBAAgBA,CAAC2O,KAAK;IAClB,IAAIA,KAAK,CAAC3G,GAAG,KAAK,GAAG,KAAK,IAAI,CAACvM,MAAM,CAACoE,OAAO,IAAI,IAAI,IAAI,IAAI,CAACpE,MAAM,CAACoE,OAAO,IAAI,IAAI,IAAI,IAAI,CAACpE,MAAM,CAACoE,OAAO,CAAC2E,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MACxHmK,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAI,IAAI,CAACrT,MAAM,CAACoE,OAAO,IAAI,IAAI,IAAI,IAAI,CAACpE,MAAM,CAACoE,OAAO,CAAC2E,IAAI,EAAE,IAAI,EAAE,EAAE;MACjE,IAAI,CAAC/I,MAAM,CAACoE,OAAO,GAAG,IAAI,CAACpE,MAAM,CAACoE,OAAO,CAAC8P,SAAS,EAAE,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MAC7E;;EAER;EACA5K,oBAAoBA,CAAC2J,KAAoB,EAAEpO,IAAS;IAChD,IAAIoO,KAAK,CAAC3G,GAAG,KAAK,GAAG,KAAK,CAACzH,IAAI,CAACV,OAAO,IAAIU,IAAI,CAACV,OAAO,CAAC2E,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MACpEmK,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAIvO,IAAI,CAACV,OAAO,IAAIU,IAAI,CAACV,OAAO,CAAC2E,IAAI,EAAE,KAAK,EAAE,EAAE;MAC5CjE,IAAI,CAACV,OAAO,GAAGU,IAAI,CAACV,OAAO,CAAC8P,SAAS,EAAE,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MAC/D;;EAER;EACAzL,cAAcA,CAACwK,KAAK;IAChB,IAAIA,KAAK,CAAC3G,GAAG,KAAK,GAAG,KAAK,IAAI,CAACvM,MAAM,CAACuI,KAAK,IAAI,IAAI,IAAI,IAAI,CAACvI,MAAM,CAACuI,KAAK,IAAI,IAAI,IAAI,IAAI,CAACvI,MAAM,CAACuI,KAAK,CAACQ,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MAClHmK,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAI,IAAI,CAACrT,MAAM,CAACuI,KAAK,IAAI,IAAI,IAAI,IAAI,CAACvI,MAAM,CAACuI,KAAK,CAACQ,IAAI,EAAE,IAAI,EAAE,EAAE;MAC7D,IAAI,CAAC/I,MAAM,CAACuI,KAAK,GAAG,IAAI,CAACvI,MAAM,CAACuI,KAAK,CAAC2L,SAAS,EAAE,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MACzE;;EAER;;;uBAxvBSjJ,6BAA6B,EAAAzN,EAAA,CAAA2W,iBAAA,CAyD5BjX,aAAa,GAAAM,EAAA,CAAA2W,iBAAA,CACb9W,cAAc,GAAAG,EAAA,CAAA2W,iBAAA,CACd5W,sBAAsB,GAAAC,EAAA,CAAA2W,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7W,EAAA,CAAA2W,iBAAA,CAAA3W,EAAA,CAAA8W,QAAA;IAAA;EAAA;;;YA3DvBrJ,6BAA6B;MAAAsJ,SAAA;MAAAC,QAAA,GAAAhX,EAAA,CAAAiX,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCd1CvX,EAAA,CAAAC,cAAA,aAAqG;UAEzDD,EAAA,CAAAqB,MAAA,GAAmD;UAAArB,EAAA,CAAAU,YAAA,EAAM;UAC7FV,EAAA,CAAA0B,SAAA,sBAAoF;UACxF1B,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,aAAwE;UACpED,EAAA,CAAAqF,UAAA,IAAAoS,iDAAA,sBAIW;UACfzX,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,cAAoG;UAA/DD,EAAA,CAAAE,UAAA,sBAAAwX,gEAAA;YAAA,OAAYF,GAAA,CAAAhE,cAAA,EAAgB;UAAA,EAAC;UAC9DxT,EAAA,CAAAC,cAAA,iBAAoF;UAG5ED,EAAA,CAAAqF,UAAA,KAAAsS,6CAAA,iBAcM;UAEN3X,EAAA,CAAAC,cAAA,eAAmB;UAKCD,EAAA,CAAAE,UAAA,2BAAA0X,4EAAA5W,MAAA;YAAA,OAAAwW,GAAA,CAAArW,UAAA,CAAAsH,MAAA,GAAAzH,MAAA;UAAA,EAA+B;UAQ1ChB,EAAA,CAAAU,YAAA,EAAa;UACdV,EAAA,CAAAC,cAAA,iBAA+C;UAAAD,EAAA,CAAAqB,MAAA,IAAgD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAI/GV,EAAA,CAAAC,cAAA,eAAmB;UAIJD,EAAA,CAAAE,UAAA,2BAAA2X,uEAAA7W,MAAA;YAAA,OAAAwW,GAAA,CAAArW,UAAA,CAAAoC,YAAA,GAAAvC,MAAA;UAAA,EAAqC;UAF5ChB,EAAA,CAAAU,YAAA,EAIE;UACFV,EAAA,CAAAC,cAAA,iBAAuB;UAAAD,EAAA,CAAAqB,MAAA,IAA+C;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAItFV,EAAA,CAAAC,cAAA,eAAmB;UAIJD,EAAA,CAAAE,UAAA,2BAAA4X,uEAAA9W,MAAA;YAAA,OAAAwW,GAAA,CAAArW,UAAA,CAAA6C,YAAA,GAAAhD,MAAA;UAAA,EAAqC,qBAAA+W,iEAAA/W,MAAA;YAAA,OAG1BwW,GAAA,CAAArT,gBAAA,CAAAnD,MAAA,CAAwB;UAAA,EAHE;UAF5ChB,EAAA,CAAAU,YAAA,EAOE;UACFV,EAAA,CAAAC,cAAA,iBAA8B;UAAAD,EAAA,CAAAqB,MAAA,IAA+C;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAG7FV,EAAA,CAAAC,cAAA,eAAwB;UACpBD,EAAA,CAAA0B,SAAA,oBAGY;UAChB1B,EAAA,CAAAU,YAAA,EAAM;UAKlBV,EAAA,CAAAqF,UAAA,KAAA2S,oDAAA,0BAYc;UACdhY,EAAA,CAAAqF,UAAA,KAAA4S,oDAAA,0BAYc;UAEdjY,EAAA,CAAAC,cAAA,eAAqD;UAEvCD,EAAA,CAAAE,UAAA,2BAAAgY,0EAAAlX,MAAA;YAAA,OAAAwW,GAAA,CAAAnK,mBAAA,GAAArM,MAAA;UAAA,EAAiC;UACvChB,EAAA,CAAAC,cAAA,gBAAoF;UAArCD,EAAA,CAAAE,UAAA,sBAAAiY,iEAAA;YAAA,OAAYX,GAAA,CAAA3D,qBAAA,EAAuB;UAAA,EAAC;UAC/E7T,EAAA,CAAAC,cAAA,eAAoE;UAChED,EAAA,CAAAqF,UAAA,KAAA+S,6CAAA,kBAOM;UAENpY,EAAA,CAAAC,cAAA,eAA0C;UACiDD,EAAA,CAAAqB,MAAA,IAAsD;UAAArB,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAqB,MAAA,SAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UAChLV,EAAA,CAAAC,cAAA,eAA6E;UACzED,EAAA,CAAAqF,UAAA,KAAAgT,+CAAA,oBAQE;UACJrY,EAAA,CAAAqF,UAAA,KAAAiT,8CAAA,mBAAmE;UACrEtY,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA0B,SAAA,iBAAwE;UACxE1B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAqF,UAAA,KAAAkT,+CAAA,oBAAgM;UAChMvY,EAAA,CAAAqF,UAAA,KAAAmT,+CAAA,oBAAgK;UAChKxY,EAAA,CAAAqF,UAAA,KAAAoT,+CAAA,oBAA0J;UAC9JzY,EAAA,CAAAU,YAAA,EAAM;UAIVV,EAAA,CAAAC,cAAA,eAA0C;UAC2CD,EAAA,CAAAqB,MAAA,IAA+C;UAAArB,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAqB,MAAA,SAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UACnKV,EAAA,CAAAC,cAAA,eAA6E;UACzED,EAAA,CAAAqF,UAAA,KAAAqT,+CAAA,oBAQE;UACJ1Y,EAAA,CAAAqF,UAAA,KAAAsT,8CAAA,mBAAoE;UACtE3Y,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA0B,SAAA,iBAAqE;UACrE1B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAqF,UAAA,KAAAuT,+CAAA,oBAAkM;UAClM5Y,EAAA,CAAAqF,UAAA,KAAAwT,+CAAA,oBAAiK;UACjK7Y,EAAA,CAAAqF,UAAA,KAAAyT,+CAAA,oBAAwJ;UAE5J9Y,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAA0C;UACuBD,EAAA,CAAAqB,MAAA,IAA+C;UAAArB,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAqB,MAAA,SAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UAC/IV,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAqF,UAAA,KAAA0T,+CAAA,oBASE;UACJ/Y,EAAA,CAAAqF,UAAA,KAAA2T,8CAAA,mBAAoE;UACtEhZ,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA0B,SAAA,iBAAqE;UACrE1B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAqF,UAAA,KAAA4T,+CAAA,oBAAkM;UAClMjZ,EAAA,CAAAqF,UAAA,KAAA6T,+CAAA,oBAAwJ;UAC5JlZ,EAAA,CAAAU,YAAA,EAAM;UAyBVV,EAAA,CAAAqF,UAAA,KAAA8T,6CAAA,kBAqBM;UAGNnZ,EAAA,CAAAqF,UAAA,KAAA+T,6CAAA,kBAUM;UAINpZ,EAAA,CAAAC,cAAA,eAA0C;UAC8CD,EAAA,CAAAqB,MAAA,IAAiD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAC7IV,EAAA,CAAAC,cAAA,eAAiB;UACTD,EAAA,CAAAqF,UAAA,KAAAgU,kDAAA,uBASY;UACZrZ,EAAA,CAAAqF,UAAA,KAAAiU,8CAAA,mBAAsH;UAC9HtZ,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA0B,SAAA,iBAAuE;UACvE1B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAqF,UAAA,KAAAkU,+CAAA,oBAA4J;UAChKvZ,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAA0C;UAC2CD,EAAA,CAAAqB,MAAA,IAA8C;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UACvIV,EAAA,CAAAC,cAAA,eAAiB;UACTD,EAAA,CAAAqF,UAAA,KAAAmU,kDAAA,uBASY;UACZxZ,EAAA,CAAAqF,UAAA,KAAAoU,8CAAA,mBAAmH;UAC3HzZ,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA0B,SAAA,iBAAoE;UACpE1B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAqF,UAAA,KAAAqU,+CAAA,oBAAyJ;UAC7J1Z,EAAA,CAAAU,YAAA,EAAM;UAGZV,EAAA,CAAAqF,UAAA,KAAAsU,6CAAA,kBAiBQ;UAEN3Z,EAAA,CAAAqF,UAAA,KAAAuU,6CAAA,kBAKM;UAER5Z,EAAA,CAAAqF,UAAA,MAAAwU,8CAAA,mBAoBQ;UAER7Z,EAAA,CAAAqF,UAAA,MAAAyU,8CAAA,kBAKQ;UACV9Z,EAAA,CAAAU,YAAA,EAAM;UAERV,EAAA,CAAAqF,UAAA,MAAA0U,8CAAA,kBAaM;UAEN/Z,EAAA,CAAAqF,UAAA,MAAA2U,8CAAA,kBAKM;UAGNha,EAAA,CAAAqF,UAAA,MAAA4U,8CAAA,kBAoCM;UAENja,EAAA,CAAAqF,UAAA,MAAA6U,8CAAA,kBAGQ;UACVla,EAAA,CAAAU,YAAA,EAAO;;;UA/a6BV,EAAA,CAAAsB,SAAA,GAAmD;UAAnDtB,EAAA,CAAAyB,iBAAA,CAAA+V,GAAA,CAAA3W,WAAA,CAAAC,SAAA,2BAAmD;UAChDd,EAAA,CAAAsB,SAAA,GAAe;UAAftB,EAAA,CAAAW,UAAA,UAAA6W,GAAA,CAAA2C,KAAA,CAAe,SAAA3C,GAAA,CAAA4C,IAAA;UAI3Cpa,EAAA,CAAAsB,SAAA,GAA8F;UAA9FtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAAvN,QAAA,CAAAC,IAAA,IAAAsN,GAAA,CAAArN,QAAA,CAAAgH,QAAA,IAAAqG,GAAA,CAAAnG,WAAA,CAAArR,EAAA,CAAAqa,eAAA,KAAAC,GAAA,EAAA9C,GAAA,CAAA7X,SAAA,CAAA2R,WAAA,CAAAC,MAAA,CAAAgJ,MAAA,GAA8F;UAO3Gva,EAAA,CAAAsB,SAAA,GAA8B;UAA9BtB,EAAA,CAAAW,UAAA,cAAA6W,GAAA,CAAA9F,gBAAA,CAA8B;UACvB1R,EAAA,CAAAsB,SAAA,GAAmB;UAAnBtB,EAAA,CAAAW,UAAA,oBAAmB,WAAA6W,GAAA,CAAA3W,WAAA,CAAAC,SAAA;UAGdd,EAAA,CAAAsB,SAAA,GAA+C;UAA/CtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAAvN,QAAA,CAAAC,IAAA,IAAAsN,GAAA,CAAArN,QAAA,CAAA+E,KAAA,CAA+C;UAmBjClP,EAAA,CAAAsB,SAAA,GAAkB;UAAlBtB,EAAA,CAAAW,UAAA,mBAAkB,uDAAA6W,GAAA,CAAArW,UAAA,CAAAsH,MAAA,gCAAA+O,GAAA,CAAA3O,gBAAA;UAWiB7I,EAAA,CAAAsB,SAAA,GAAgD;UAAhDtB,EAAA,CAAAyB,iBAAA,CAAA+V,GAAA,CAAA3W,WAAA,CAAAC,SAAA,wBAAgD;UAQxFd,EAAA,CAAAsB,SAAA,GAAqC;UAArCtB,EAAA,CAAAW,UAAA,YAAA6W,GAAA,CAAArW,UAAA,CAAAoC,YAAA,CAAqC;UAGrBvD,EAAA,CAAAsB,SAAA,GAA+C;UAA/CtB,EAAA,CAAAyB,iBAAA,CAAA+V,GAAA,CAAA3W,WAAA,CAAAC,SAAA,uBAA+C;UAQ/Dd,EAAA,CAAAsB,SAAA,GAAqC;UAArCtB,EAAA,CAAAW,UAAA,YAAA6W,GAAA,CAAArW,UAAA,CAAA6C,YAAA,CAAqC;UAMdhE,EAAA,CAAAsB,SAAA,GAA+C;UAA/CtB,EAAA,CAAAyB,iBAAA,CAAA+V,GAAA,CAAA3W,WAAA,CAAAC,SAAA,uBAA+C;UAapFd,EAAA,CAAAsB,SAAA,GAAkB;UAAlBtB,EAAA,CAAAW,UAAA,UAAA6W,GAAA,CAAArJ,WAAA,CAAkB;UAalBnO,EAAA,CAAAsB,SAAA,GAAiB;UAAjBtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAArJ,WAAA,CAAiB;UAgBiCnO,EAAA,CAAAsB,SAAA,GAAoE;UAApEtB,EAAA,CAAAwa,UAAA,CAAAxa,EAAA,CAAAiD,eAAA,KAAAwX,IAAA,EAAoE;UADrHza,EAAA,CAAAW,UAAA,gBAAAX,EAAA,CAAAiD,eAAA,KAAAyX,IAAA,EAAqD,WAAAlD,GAAA,CAAAtJ,UAAA,aAAAsJ,GAAA,CAAAnK,mBAAA;UAExCrN,EAAA,CAAAsB,SAAA,GAA2B;UAA3BtB,EAAA,CAAAW,UAAA,cAAA6W,GAAA,CAAA5R,aAAA,CAA2B;UAEK5F,EAAA,CAAAsB,SAAA,GAA0E;UAA1EtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAAvN,QAAA,CAAAC,IAAA,IAAAsN,GAAA,CAAArN,QAAA,CAAA+E,KAAA,IAAAsI,GAAA,CAAA9R,WAAA,aAA0E;UAU1B1F,EAAA,CAAAsB,SAAA,GAAsD;UAAtDtB,EAAA,CAAAyB,iBAAA,CAAA+V,GAAA,CAAA3W,WAAA,CAAAC,SAAA,8BAAsD;UAEjId,EAAA,CAAAsB,SAAA,GAAwD;UAAxDtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA9R,WAAA,gBAAA8R,GAAA,CAAA9R,WAAA,aAAwD;UAS3D1F,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA9R,WAAA,aAA6B;UAOL1F,EAAA,CAAAsB,SAAA,GAAqG;UAArGtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAAlD,WAAA,CAAA0D,KAAA,KAAAmR,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAAlD,WAAA,CAAA2D,MAAA,kBAAAkR,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAAlD,WAAA,CAAA2D,MAAA,CAAAC,QAAA,EAAqG;UACrGvG,EAAA,CAAAsB,SAAA,GAA0D;UAA1DtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAAlD,WAAA,CAAA2D,MAAA,kBAAAkR,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAAlD,WAAA,CAAA2D,MAAA,CAAAsG,SAAA,CAA0D;UAC1D5M,EAAA,CAAAsB,SAAA,GAAwD;UAAxDtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAAlD,WAAA,CAAA2D,MAAA,kBAAAkR,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAAlD,WAAA,CAAA2D,MAAA,CAAAqU,OAAA,CAAwD;UAMR3a,EAAA,CAAAsB,SAAA,GAA+C;UAA/CtB,EAAA,CAAAyB,iBAAA,CAAA+V,GAAA,CAAA3W,WAAA,CAAAC,SAAA,uBAA+C;UAEpHd,EAAA,CAAAsB,SAAA,GAAuD;UAAvDtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA9R,WAAA,gBAAA8R,GAAA,CAAA9R,WAAA,aAAuD;UAS1D1F,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA9R,WAAA,aAA6B;UAOL1F,EAAA,CAAAsB,SAAA,GAAuG;UAAvGtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAAtC,YAAA,CAAA8C,KAAA,KAAAmR,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAAtC,YAAA,CAAA+C,MAAA,kBAAAkR,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAAtC,YAAA,CAAA+C,MAAA,CAAAC,QAAA,EAAuG;UACvGvG,EAAA,CAAAsB,SAAA,GAA2D;UAA3DtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAAtC,YAAA,CAAA+C,MAAA,kBAAAkR,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAAtC,YAAA,CAAA+C,MAAA,CAAAsG,SAAA,CAA2D;UAC3D5M,EAAA,CAAAsB,SAAA,GAAyD;UAAzDtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAAtC,YAAA,CAAA+C,MAAA,kBAAAkR,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAAtC,YAAA,CAAA+C,MAAA,CAAAqU,OAAA,CAAyD;UAM7B3a,EAAA,CAAAsB,SAAA,GAA+C;UAA/CtB,EAAA,CAAAyB,iBAAA,CAAA+V,GAAA,CAAA3W,WAAA,CAAAC,SAAA,uBAA+C;UAEhGd,EAAA,CAAAsB,SAAA,GAAuD;UAAvDtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA9R,WAAA,gBAAA8R,GAAA,CAAA9R,WAAA,aAAuD;UAU1D1F,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA9R,WAAA,aAA6B;UAOL1F,EAAA,CAAAsB,SAAA,GAAuG;UAAvGtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAA7B,YAAA,CAAAqC,KAAA,KAAAmR,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAA7B,YAAA,CAAAsC,MAAA,kBAAAkR,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAA7B,YAAA,CAAAsC,MAAA,CAAAC,QAAA,EAAuG;UACvGvG,EAAA,CAAAsB,SAAA,GAAyD;UAAzDtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAA7B,YAAA,CAAAsC,MAAA,kBAAAkR,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAA7B,YAAA,CAAAsC,MAAA,CAAAqU,OAAA,CAAyD;UA0BxF3a,EAAA,CAAAsB,SAAA,GAAsB;UAAtBtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAAjV,MAAA,CAAA2H,IAAA,MAAsB;UAwBtBlK,EAAA,CAAAsB,SAAA,GAAsB;UAAtBtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAAjV,MAAA,CAAA2H,IAAA,MAAsB;UAe4DlK,EAAA,CAAAsB,SAAA,GAAiD;UAAjDtB,EAAA,CAAAyB,iBAAA,CAAA+V,GAAA,CAAA3W,WAAA,CAAAC,SAAA,yBAAiD;UAElHd,EAAA,CAAAsB,SAAA,GAA2B;UAA3BtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA9R,WAAA,aAA2B;UAUD1F,EAAA,CAAAsB,SAAA,GAAsD;UAAtDtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA9R,WAAA,gBAAA8R,GAAA,CAAA9R,WAAA,aAAsD;UAOlE1F,EAAA,CAAAsB,SAAA,GAAsD;UAAtDtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAAc,OAAA,CAAAL,MAAA,kBAAAkR,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAAc,OAAA,CAAAL,MAAA,CAAAsG,SAAA,CAAsD;UAKN5M,EAAA,CAAAsB,SAAA,GAA8C;UAA9CtB,EAAA,CAAAyB,iBAAA,CAAA+V,GAAA,CAAA3W,WAAA,CAAAC,SAAA,sBAA8C;UAE5Gd,EAAA,CAAAsB,SAAA,GAA2B;UAA3BtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA9R,WAAA,aAA2B;UAUD1F,EAAA,CAAAsB,SAAA,GAAsD;UAAtDtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA9R,WAAA,gBAAA8R,GAAA,CAAA9R,WAAA,aAAsD;UAOlE1F,EAAA,CAAAsB,SAAA,GAAmD;UAAnDtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAAwB,IAAA,CAAAf,MAAA,kBAAAkR,GAAA,CAAA5R,aAAA,CAAAC,QAAA,CAAAwB,IAAA,CAAAf,MAAA,CAAAsG,SAAA,CAAmD;UAIpF5M,EAAA,CAAAsB,SAAA,GAAqN;UAArNtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA9R,WAAA,gBAAA8R,GAAA,CAAAvN,QAAA,CAAAC,IAAA,IAAAsN,GAAA,CAAArN,QAAA,CAAAC,QAAA,IAAAoN,GAAA,CAAAjV,MAAA,CAAAkG,MAAA,YAAA+O,GAAA,CAAAjV,MAAA,CAAAoG,SAAA,SAAA6O,GAAA,CAAAvN,QAAA,CAAAC,IAAA,IAAAsN,GAAA,CAAArN,QAAA,CAAAC,QAAA,IAAAoN,GAAA,CAAA9R,WAAA,gBAAA8R,GAAA,CAAAjV,MAAA,CAAAwF,UAAA,SAAqN;UAmBnN/H,EAAA,CAAAsB,SAAA,GAAgJ;UAAhJtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA9R,WAAA,gBAAA8R,GAAA,CAAAvN,QAAA,CAAAC,IAAA,IAAAsN,GAAA,CAAArN,QAAA,CAAAC,QAAA,IAAAoN,GAAA,CAAAjV,MAAA,CAAAkG,MAAA,YAAA+O,GAAA,CAAAjV,MAAA,CAAAoG,SAAA,SAAA6O,GAAA,CAAA9R,WAAA,aAAgJ;UAOlJ1F,EAAA,CAAAsB,SAAA,GAAsD;UAAtDtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA9R,WAAA,gBAAA8R,GAAA,CAAA9R,WAAA,aAAsD;UAsBtD1F,EAAA,CAAAsB,SAAA,GAAmF;UAAnFtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA9R,WAAA,gBAAA8R,GAAA,CAAA9R,WAAA,gBAAA8R,GAAA,CAAAjV,MAAA,CAAAwF,UAAA,SAAmF;UAQvF/H,EAAA,CAAAsB,SAAA,GAA2B;UAA3BtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA9R,WAAA,aAA2B;UAe3B1F,EAAA,CAAAsB,SAAA,GAAyK;UAAzKtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA9R,WAAA,gBAAA8R,GAAA,CAAA9R,WAAA,iBAAA8R,GAAA,CAAAjV,MAAA,CAAAwF,UAAA,YAAAyP,GAAA,CAAAjV,MAAA,CAAAwF,UAAA,aAAAyP,GAAA,CAAAnN,oBAAA,CAAAC,QAAA,CAAAkN,GAAA,CAAAjV,MAAA,CAAAwF,UAAA,GAAyK;UAQzK/H,EAAA,CAAAsB,SAAA,GAA+F;UAA/FtB,EAAA,CAAAW,UAAA,UAAA6W,GAAA,CAAA9R,WAAA,gBAAA8R,GAAA,CAAA9R,WAAA,iBAAA8R,GAAA,CAAAxK,SAAA,IAAAwK,GAAA,CAAAxK,SAAA,CAAAjH,MAAA,KAA+F;UAsC/F/F,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAW,UAAA,SAAA6W,GAAA,CAAA9R,WAAA,aAA6B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}