import {AfterContentChecked, ChangeDetector<PERSON>ef, Component, Injector, OnInit} from "@angular/core";
import { FormBuilder } from "@angular/forms";
import {MenuItem, SelectItem} from "primeng/api";
import { ComponentBase } from "src/app/component.base";
import { AccountService } from "src/app/service/account/AccountService";
import { CONSTANTS } from "src/app/service/comon/constants";
import { ComboLazyControl } from "../../common-module/combobox-lazyload/combobox.lazyload";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";
import {CustomerService} from "../../../service/customer/CustomerService";
import {ContractService} from "../../../service/contract/ContractService";
import {de} from "@fullcalendar/core/internal-common";

@Component({
    selector: "app-account-create",
    templateUrl: './app.account.create.component.html'
})
export class AppAccountCreateComponent extends ComponentBase implements OnInit, AfterContentChecked{
    constructor(public accountService: AccountService,
                public customerService: CustomerService,
                public contractService: ContractService,
                private formBuilder: FormBuilder,
                private cdr: ChangeDetectorRef,
                injector: Injector) {
        super(injector);
    }
    items: Array<MenuItem>;
    home: MenuItem;
    accountInfo: {
        accountName: string| null,
        fullName: string|null,
        email: string|null,
        phone: string|null,
        userType: number| null,
        province: any,
        roles: Array<any>,
        description: string|null,
        manager: any,
        //Lưu lại list customer cuối cùng để gửi đi
        customers: Array<any>
        customerAccounts : null
        accountRootId: null
    };
    formAccount: any;
    statusAccounts: Array<any>;
    listRole: Array<any>;
    listProvince: Array<any>;
    listCustomer: Array<any>;
    userType: number;
    optionUserType: any;
    isUsernameExisted: boolean = false;
    isEmailExisted: boolean = false;
    isPhoneExisted: boolean = false;
    oldUserType: number | null = null;
    paramSearchCustomerProvince: {provinceCode: string} = {provinceCode: ""};
    paramSearchManager :{type: number, provinceCode: string} = {type: 3, provinceCode: ""};
    paramSearchCustomerAccount : {provinceCode: string, managerId: number} = {provinceCode: "", managerId: -1};
    paramSearchRole : {accountCustomerId: number, type: number} = {accountCustomerId: -1, type: -1};
    controlComboSelect: ComboLazyControl = new ComboLazyControl();
    controlComboSelectManager : ComboLazyControl = new ComboLazyControl();
    controlComboSelectCustomerAccount : ComboLazyControl = new ComboLazyControl();
    controlComboSelectCustomerRoles : ComboLazyControl = new ComboLazyControl();
    paramQuickSearchCustomer: {
        keyword: string|null,
        provinceCode: string|null,
        accountRootId: number| null,
        managerId: number | null
    }

    customSelectAllCustomer = false;
    customSelectAllContract = false;
    loadingCustomer: boolean = false;
    loadingContract: boolean = false;

    columnInfoCustomer: Array<ColumnInfo>;
    optionTableCustomer: OptionTable;
    //Lưu lại list customer đã chọn
    selectItemCustomer: Array<any>
    //sẽ lưu lại list contract sau khi đã chọn
    selectItemContract: Array<any>
    //lưu lại contract bỏ chọn
    deselectedContracts: Set<string>
    dataSetCustomer: {
        content: Array<any>,
        total: number,
    }
    paginationCustomer: {
        page: number|null,
        size: number|null,
        sortBy: string|null,
    }

    paramQuickSearchContract: {
        keyword: string|null,
        customerIds: Array<{ id: number }>|null,
    }
    columnInfoContract: Array<ColumnInfo>;
    optionTableContract: OptionTable;
    dataSetContract: {
        content: Array<any>,
        total: number,
    }
    paginationContract: {
        page: number|null,
        size: number|null,
        sortBy: string|null,
    }
    accountId: number | null
    isShowSecretKey = true
    listModule = []
    //sẽ lưu lại list api sau khi đã chọn
    selectItemGrantApi: Array<any> = []
    paginationGrantApi: {
        page: number|null,
        size: number|null,
        sortBy: string|null,
    }
    columnInfoGrantApi: Array<ColumnInfo>;

    dataSetGrantApi: {
        content: Array<any>,
        total: number,
    }
    optionTableGrantApi: OptionTable;

    paramsSearchGrantApi = {api : null, module : null}

    genGrantApi = {clientId: null, secretKey: null}

    statusGrantApi : any = 1; // 1: Hoạt động default

    userInfo = this.sessionService.userInfo;

    accountCurrentDetail : any = {}
    isSubmitting: boolean = false;
    ngOnInit(): void {
        if (!this.checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.CREATE])) {window.location.hash = "/access";}
        this.userType = this.sessionService.userInfo.type;
        this.accountId = this.sessionService.userInfo.id
        this.optionUserType = CONSTANTS.USER_TYPE;
        this.items = [
            { label: this.tranService.translate("global.menu.accountmgmt") },
            { label: this.tranService.translate("global.menu.listaccount"), routerLink:"/accounts" },
            { label: this.tranService.translate("global.button.create") }
        ];
        this.home = { icon: 'pi pi-home', routerLink: '/' };

        let fullTypeAccount = [
            {name: this.tranService.translate("account.usertype.admin"),value:CONSTANTS.USER_TYPE.ADMIN, accepts:[CONSTANTS.USER_TYPE.ADMIN]},
            // {name: this.tranService.translate("account.usertype.customer"),value:CONSTANTS.USER_TYPE.CUSTOMER,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT, CONSTANTS.USER_TYPE.AGENCY, CONSTANTS.USER_TYPE.CUSTOMER]},
            {name: this.tranService.translate("account.usertype.customer"),value:CONSTANTS.USER_TYPE.CUSTOMER,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT, CONSTANTS.USER_TYPE.CUSTOMER]},
            {name: this.tranService.translate("account.usertype.province"),value:CONSTANTS.USER_TYPE.PROVINCE,accepts:[CONSTANTS.USER_TYPE.ADMIN]},
            {name: this.tranService.translate("account.usertype.district"),value:CONSTANTS.USER_TYPE.DISTRICT,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE]},
            // {name: this.tranService.translate("account.usertype.agency"),value:CONSTANTS.USER_TYPE.AGENCY,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT]},
        ]
        this.statusAccounts = fullTypeAccount.filter(el => el.accepts.includes(this.userType));
        this.accountInfo = {
            accountName: null,
            fullName: null,
            email: null,
            phone: null,
            userType: this.statusAccounts[0].value,
            province: this.sessionService.userInfo.provinceCode,
            roles: null,
            description: null,
            manager: null,
            customers: null,
            customerAccounts : null,
            accountRootId: null
        }
        this.paramQuickSearchCustomer = {
            keyword: null,
            accountRootId: null,
            provinceCode: this.accountInfo.province,
            managerId: -1,
        }
        if (this.userType == CONSTANTS.USER_TYPE.CUSTOMER) {
            this.paramQuickSearchCustomer.accountRootId = this.accountId;
        }
        this.columnInfoCustomer = [
            {
                name: this.tranService.translate("customer.label.customerCode"),
                key: "code",
                size: "30%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("customer.label.customerName"),
                key: "name",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ]
        this.dataSetCustomer = {
            content: [],
            total: 0,
        }
        this.paginationCustomer = {
            page: 0,
            size: 10,
            sortBy: "name,asc;id,asc",
        }
        this.paginationGrantApi = {
            page: 0,
            size: 10,
            sortBy: "id,desc",
        }
        this.optionTableCustomer = {
            hasClearSelected: false,
            hasShowChoose: true,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.selectItemCustomer = []

        this.paramQuickSearchContract = {
            keyword: null,
            customerIds: [],
        }
        this.columnInfoContract = [
            {
                name: this.tranService.translate("customer.label.customerCode"),
                key: "customerCode",
                size: "30%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("customer.label.customerName"),
                key: "customerName",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("contract.label.contractCode"),
                key: "contractCode",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ]

        this.columnInfoGrantApi = [
            {
                name: "API",
                key: "name",
                size: "30%",
                align: "left",
                isShow: true,
                isSort: true,
            },
            {
                name: "Module",
                key: "module",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: true,
            }
        ]
        this.dataSetContract = {
            content: [],
            total: 0,
        }
        this.paginationContract = {
            page: 0,
            size: 10,
            sortBy: "customerName,asc;id,asc",
        }
        this.dataSetGrantApi = {
            content: [],
            total: 0,
        }
        this.optionTableContract = {
            hasClearSelected: false,
            hasShowChoose: true,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.optionTableGrantApi = {
            hasClearSelected: false,
            hasShowChoose: true,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.selectItemContract = []
        this.deselectedContracts = new Set<string>();
        this.formAccount = this.formBuilder.group(this.accountInfo);
        this.getListAppIdSelected()
        this.getListProvince();
        this.isSubmitting = false;
        let me = this;
    }

    getListAppIdSelected(){
        let me = this;
        this.accountService.viewProfile( (response)=>{
            me.accountCurrentDetail = response;
        })
    }

    ngAfterContentChecked(): void {
        if(this.accountInfo.userType != this.oldUserType){
            this.oldUserType = this.accountInfo.userType;
            // this.formAccount.get("province").reset();
            this.formAccount.get("customers").reset();
        }
    }

    checkExistAccount(type){
        let email = null;
        let username = null;
        if(type == "accountName"){
            username = this.accountInfo.accountName;
        }else if(type == "email"){
            email = this.accountInfo.email;
        }

        let me = this;

        this.debounceService.set(type, this.accountService.checkAccount.bind(this.accountService), email, username,(response)=>{
            if(response >= 1){
                if(type == "accountName"){
                    me.isUsernameExisted = true;
                }else{
                    me.isEmailExisted = true;
                }
            }else{
                if(type == "accountName"){
                    me.isUsernameExisted = false;
                }else{
                    me.isEmailExisted = false;
                }
            }
        })
    }

    onSubmitCreate(){
        let me = this;
        if (me.isSubmitting) return;
        if(me.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER && me.selectItemCustomer.length == 0) {
            me.messageCommonService.warning(me.tranService.translate('account.message.customerRequired'))
            return;
        }
        me.isSubmitting = true;
        this.messageCommonService.onload();
        setTimeout(function(){
            me.handleCreate();
        })
    }

    handleCreate(){
        if(this.accountInfo.userType != CONSTANTS.USER_TYPE.CUSTOMER) {
            this.statusGrantApi = null
            this.selectItemGrantApi = []
        }
        let dataBody = {
            username: this.accountInfo.accountName,
            fullName: this.accountInfo.fullName,
            description: this.accountInfo.description,
            email: this.accountInfo.email,
            phone: this.accountInfo.phone,
            type: this.accountInfo.userType,
            provinceCode: this.accountInfo.province,
            roleLst: (this.accountInfo.roles || []).map(el => el.id),
            customerIdLst: (this.accountInfo.customers || []).map(customer => customer.id),
            idManager: (this.accountInfo.manager || null),
            idUserManageList: (this.accountInfo.customerAccounts || []),
            contractIdLst: (this.selectItemContract || []).map(contract => contract.id),
            accountRootId: this.accountInfo.accountRootId,
            statusApi: this.statusGrantApi,
            listApiId: (this.selectItemGrantApi || []).map(el=>el.id)
        }
        if(dataBody.phone != null){
            if(dataBody.phone.startsWith('0')){
                dataBody.phone = "84"+dataBody.phone.substring(1, dataBody.phone.length);
            }else if(dataBody.phone.length == 9 || dataBody.phone.length == 10){
                dataBody.phone = "84"+dataBody.phone;
            }
        }

        let me = this;
        this.accountService.createAccount(dataBody, (response)=>{
            let body111 = {
                clientId: response.username,
                secretId : this.genGrantApi.secretKey,
                name : response.username,
                active : this.statusGrantApi,
                userId : response.id
            }
            if(this.statusGrantApi) {
                this.accountService.createClientAuthen(body111,()=>{
                    me.isSubmitting = false;
                    me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
                    me.router.navigate(['/accounts/']);
                }, null, ()=>{
                    me.isSubmitting = false;
                    me.messageCommonService.offload();
                })
            }else {
                me.isSubmitting = false;
                me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
                me.router.navigate(['/accounts/']);
            }
        }, null, ()=>{
            me.isSubmitting = false;
            me.messageCommonService.offload();
        })
    }

    closeForm(){
        this.router.navigate(['/accounts'])
    }

    getListRole(isClear){
        if(isClear){
            this.accountInfo.roles = null;
        }
        let type = -1;
        if(this.accountInfo.userType != null){
            type = this.accountInfo.userType;
        }
        this.accountService.getListRole({type: type, accountRootId: this.accountInfo.accountRootId ? this.accountInfo.accountRootId : -1}, (response)=>{
            this.listRole = response.map(el => {
                return {
                    id: el.id,
                    name: el.name
                }
            })
        })
    }

    getListCustomer(isClear, name:string=""){
        let me = this;
        if(this.accountInfo.userType == this.optionUserType.CUSTOMER) {
            me.paginationCustomer.page = 0;
            me.paginationContract.page = 0;
            if(isClear){
                this.accountInfo.customers = [];
                this.accountInfo.manager = null;
                this.accountInfo.accountRootId = null
            }
            this.selectItemCustomer = []
            this.selectItemContract = []
            if(this.accountInfo.province != null){
                this.paramSearchCustomerProvince = {provinceCode: this.accountInfo.province}
                this.paramSearchManager = {type: 3, provinceCode: this.accountInfo.province}
                me.paramSearchCustomerAccount.provinceCode = me.accountInfo.province
            }
            // nếu tài khoản thực hiện tạo là gdv thì gán gdv
            if (me.userType == CONSTANTS.USER_TYPE.DISTRICT){
                me.accountInfo.manager = me.accountId
            }
            if (me.accountInfo.manager) {
                me.paramSearchCustomerAccount.managerId = me.accountInfo.manager
            } else {
                me.paramSearchCustomerAccount.managerId = -1;
            }
            me.getListRole(true)
            // reload and clear selected when changer root account
            if(this.genGrantApi.secretKey != null) {
                me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);
                this.selectItemGrantApi = []
            }
        }
        if(this.accountInfo.userType == this.optionUserType.DISTRICT) {
            if(isClear){
                this.accountInfo.customerAccounts = null;
            }
            if(this.accountInfo.province != null){
                me.paramSearchCustomerAccount.provinceCode = me.accountInfo.province
            }
            if (me.accountInfo.manager) {
                me.paramSearchCustomerAccount.managerId = me.accountInfo.manager
            } else {
                me.paramSearchCustomerAccount.managerId = -1;
            }
        }
    }
    onChangeTeller() {
        let me = this;
        this.accountInfo.customers = [];
        this.selectItemCustomer = []
        this.selectItemContract = []
        this.accountInfo.accountRootId = null

        if (me.accountInfo.manager) {
            me.paramSearchCustomerAccount.managerId = me.accountInfo.manager
            me.paramQuickSearchCustomer.managerId = me.accountInfo.manager
        } else {
            me.paramSearchCustomerAccount.managerId = -1;
            me.paramQuickSearchCustomer.managerId = me.accountInfo.manager
        }
    }

    filterCustomerByName(event){
        this.debounceService.set("filterCustomer", this.getListCustomer.bind(this), false, event.filter);
    }

    getListProvince(){
        this.accountService.getListProvince((response)=>{
            this.listProvince = response.map(el => {
                return {
                    id: el.code,
                    name: `${el.name} (${el.code})`
                }
            })
        })
    }

    loadCustomerAccount(params, callback) {
        return this.accountService.getCustomerAccount(params, callback)
    }
    onSearchCustomer(back?) {
        let me = this;
        if(back) {
            me.paginationCustomer.page = 0;
        }
        me.paramQuickSearchCustomer.provinceCode = this.accountInfo.province,
        me.paramQuickSearchCustomer.accountRootId = me.userType == CONSTANTS.USER_TYPE.CUSTOMER ? me.accountId : me.accountInfo.accountRootId
        me.searchCustomer(me.paginationCustomer.page, me.paginationCustomer.size, me.paginationCustomer.sortBy, me.paramQuickSearchCustomer);
    }
    searchCustomer(page, limit, sort, params){
        let me = this;
        this.paginationCustomer.page = page;
        this.paginationCustomer.size = limit;
        this.paginationCustomer.sortBy = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.paramQuickSearchCustomer).forEach(key => {
            if(this.paramQuickSearchCustomer[key] != null){
                dataParams[key] = this.paramQuickSearchCustomer[key];
            }
        })
        me.messageCommonService.onload();
        this.customerService.quickSearchCustomer(dataParams, this.paramQuickSearchCustomer,(response)=>{
            me.dataSetCustomer = {
                content: response.content,
                total: response.totalElements
            }
            if(this.selectItemCustomer.length==response.totalElements && response.totalElements != 0){
                this.customSelectAllCustomer = true
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
        // console.log(this.selectItemCustomer)
    }

    onSearchContract(back?) {
        let me = this;
        if(back) {
            me.paginationContract.page = 0;
        }
        me.paramQuickSearchContract.customerIds = (me.selectItemCustomer|| []).map(customer => customer.id),
        me.searchContract(me.paginationContract.page, me.paginationContract.size, me.paginationContract.sortBy, me.paramQuickSearchContract);
    }

    onSearchGrantApi(back?) {
        let me = this;
        if(back) {
            me.paginationGrantApi.page = 0;
        }
        // me.paramQuickSearchContract.customerIds = (me.selectItemCustomer|| []).map(customer => customer.id),
            me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);
    }
    searchContract(page, limit, sort, params){
        let me = this;
        this.paginationContract.page = page;
        this.paginationContract.size = limit;
        this.paginationContract.sortBy = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        // Object.keys(this.paramQuickSearchContract).forEach(key => {
        //     if(this.paramQuickSearchContract[key] != null){
        //         dataParams[key] = this.paramQuickSearchContract[key];
        //     }
        // })
        me.messageCommonService.onload();
        this.contractService.quickSearchContract(dataParams, this.paramQuickSearchContract,(response)=>{
            me.dataSetContract = {
                content: response.content,
                total: response.totalElements
            }
            if(this.selectItemContract.length==response.totalElements && response.totalElements !=0){
                this.customSelectAllContract = true
            }
            // console.log(response)
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    searchGrantApi(page, limit, sort, params){
        let me = this;
        this.paginationGrantApi.page = page;
        this.paginationGrantApi.size = limit;
        this.paginationGrantApi.sortBy = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.paramsSearchGrantApi).forEach(key => {
            if(this.paramsSearchGrantApi[key] != null){
                dataParams[key] = this.paramsSearchGrantApi[key];
            }
        })
        me.messageCommonService.onload();
        // tài khoản tỉnh , admin, GDV khi tạo KH chưa chọn accountRoot ->lấy full
        if((this.userType == CONSTANTS.USER_TYPE.PROVINCE ||
            this.userType == CONSTANTS.USER_TYPE.ADMIN ||
            this.userType == CONSTANTS.USER_TYPE.DISTRICT) && !this.accountInfo.accountRootId) {
            this.accountService.searchGrantApi(dataParams,(response)=>{
                me.dataSetGrantApi = {
                    content: response.content,
                    total: response.totalElements
                }
            }, null, ()=>{
                me.messageCommonService.offload();
            })
            let copyParam = {...dataParams};
            copyParam.size = *********;
            this.accountService.searchGrantApi(copyParam,(response)=>{
                me.listModule = [...new Set(response.content.map(el=>el.module))]
                me.listModule = me.listModule.map(el=>({
                    name : el,
                    value : el
                }))
            }, null, ()=>{
                me.messageCommonService.offload();
            })
            // tài khoản tỉnh , admin, GDV khi tạo KH đã chọn accountRoot -> lấy theo accountRoot
            // tài khoản tạo là KH và là tài khoản con -> lấy theo cha
        }else if((this.userType == CONSTANTS.USER_TYPE.PROVINCE ||
            this.userType == CONSTANTS.USER_TYPE.ADMIN ||
            this.userType == CONSTANTS.USER_TYPE.DISTRICT || this.userType == CONSTANTS.USER_TYPE.CUSTOMER) && this.accountInfo.accountRootId) {
            dataParams['userCustomerParent'] = this.accountInfo.accountRootId
            this.accountService.getListAPI2(dataParams,(response)=>{
                me.dataSetGrantApi = {
                    content: response.content,
                    total: response.totalElements
                }
            }, null, ()=>{
                me.messageCommonService.offload();
            })
            let copyParam = {...dataParams};
            copyParam.size = *********;
            this.accountService.getListAPI2(copyParam,(response)=>{
                me.listModule = [...new Set(response.content.map(el=>el.module))]
                me.listModule = me.listModule.map(el=>({
                    name : el,
                    value : el
                }))
            }, null, ()=>{
                me.messageCommonService.offload();
            })
        // tài khoản tạo là KH và là tài khoản cha -> lấy theo cái nó được tài khoản cấp trên gán
        }else if(this.userType == CONSTANTS.USER_TYPE.CUSTOMER && !this.accountInfo.accountRootId) {
            dataParams['selectedApiIds'] = (this.accountCurrentDetail.listApiId || [-99]).join(",");
            this.accountService.searchGrantApi(dataParams,(response)=>{
                me.dataSetGrantApi = {
                    content: response.content,
                    total: response.totalElements
                }
            }, null, ()=>{
                me.messageCommonService.offload();
            })
            let copyParam = {...dataParams};
            copyParam.size = *********;
            this.accountService.searchGrantApi(copyParam,(response)=>{
                me.listModule = [...new Set(response.content.map(el=>el.module))]
                me.listModule = me.listModule.map(el=>({
                    name : el,
                    value : el
                }))
            }, null, ()=>{
                me.messageCommonService.offload();
            })
        }
    }

    protected readonly CONSTANTS = CONSTANTS;
    protected readonly onclick = onclick;
    onTabChange(event) {
        const tabName = event.originalEvent.target.innerText;
        let me = this;
        if (event && tabName.includes(this.tranService.translate('account.text.grantApi'))) {
            this.genGrantApi.clientId = this.accountInfo.accountName;
        } else if (event && tabName.includes(this.tranService.translate('account.text.addContract'))) {
            me.onSearchContract()
        } else if (event && tabName.includes(this.tranService.translate('account.text.addCustomer'))) {
            me.onSearchCustomer()
        }
    }

    generateToken(n) {
        var chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        var token = '';
        for(var i = 0; i < n; i++) {
            token += chars[Math.floor(Math.random() * chars.length)];
        }
        return token;
    }

    checkSelectItemChangeCustomer(event: any[]) {
        let me = this;

        if(this.selectItemCustomer.length==this.dataSetCustomer.total){
            this.customSelectAllCustomer = true
        }else{
            this.customSelectAllCustomer = false
        }

        const currentCustomerIds = new Set((event|| []).map(customer => customer.id));
        const previousCustomerIds = new Set((this.accountInfo.customers|| []).map(customer => customer.id));

        const addedCustomers = (event|| []).filter(customer => !previousCustomerIds.has(customer.id));

        const removedCustomers = this.accountInfo.customers.filter(customer => !currentCustomerIds.has(customer.id));

        this.fetchContractsByCustomerId((addedCustomers|| []).map(customer => customer.id))

        removedCustomers.forEach(customer => {
            this.selectItemContract = this.selectItemContract.filter(contract => contract.customerCode != customer.code);
        });

        this.accountInfo.customers = event;
    }
    fetchContractsByCustomerId(customerIds: number[]) {

        let me = this;
        this.messageCommonService.onload()
        this.paginationContract.page = 0;
        let dataParams = {
            page: '0',
            size: '10000',
            sort: this.paginationContract.sortBy
        }
        this.contractService.quickSearchContract(dataParams,
            {
                keyword: null,
                accountRootId: this.accountInfo.accountRootId,
                provinceCode: this.accountInfo.province,
                customerIds: customerIds,
            }, (res) => {
                if (res.totalElements > 0) {
                    const newContracts = res.content.filter(contract => !this.deselectedContracts.has(contract.contractCode) &&
                        !this.selectItemContract.some(existingContract => existingContract.contractCode === contract.contractCode));
                    this.selectItemContract.push(...newContracts);
                }
            }, null, () => {
                me.messageCommonService.offload();
            })
    }

    checkSelectItemChangeContract(event) {
        if(this.selectItemContract.length==this.dataSetContract.total){
            this.customSelectAllContract = true
        }else{
            this.customSelectAllContract = false
        }
    }

    onChangeSelectAllItemsCustomer(){
        // console.log(this.selectItemCustomer);
        let me = this;
        let params = {
            page: "0",
            size: "99999999",
            sort: "name,asc;id,asc",
        }
        Object.keys(this.paramQuickSearchCustomer).forEach(key => {
            if(this.paramQuickSearchCustomer[key] != null){
                params[key] = this.paramQuickSearchCustomer[key];
            }
        })
        this.loadingCustomer = true
        this.customerService.quickSearchCustomer(params,this.paramQuickSearchCustomer,(response)=>{
            if(this.selectItemCustomer.length == response.totalElements){
                this.selectItemCustomer = [];
                this.customSelectAllCustomer = false
                return;
            }
            this.selectItemCustomer = response.content
            this.customSelectAllCustomer = true;
        },null,()=>{ this.loadingCustomer = false });
    }

    onChangeSelectAllItemsContract(){
        // console.log(this.selectItemCustomer);
        let me = this;
        let params = {
            page: "0",
            size: "99999999",
            sort: "customerName,asc;id,asc",
        }
        this.loadingContract = true
        this.contractService.quickSearchContract(params, this.paramQuickSearchContract,(response)=>{
            if(this.selectItemContract.length == response.totalElements){
                this.selectItemContract = [];
                this.customSelectAllContract = false
                return;
            }
            this.selectItemContract = response.content
            this.customSelectAllContract = true
        }, null, ()=>{
            this.loadingContract = false;
        })
    }

    checkShowTabAddCustomerAndContract() {
        let me = this

        if (me.accountInfo.userType != CONSTANTS.USER_TYPE.CUSTOMER)  return false;
        if (me.formAccount.invalid || me.isEmailExisted || me.isPhoneExisted || me.isUsernameExisted ) {
            // Object.keys(this.formAccount.controls).forEach(key => {
            //     const control = this.formAccount.get(key);
            //     if (control.invalid) {
            //         console.log('Field:', key, 'is invalid. Errors:', control.errors);
            //     }
            // });
            // console.log(me.isEmailExisted)
            // console.log(me.isPhoneExisted)
            // console.log(me.isUsernameExisted)
            return false;
        }
        if ((me.userType == CONSTANTS.USER_TYPE.ADMIN && this.accountInfo.province == null) || ((me.userType == CONSTANTS.USER_TYPE.ADMIN || me.userType == CONSTANTS.USER_TYPE.PROVINCE) && me.accountInfo.manager == null)){
            return false
        }
        return  true
    }

    genToken(){
        let me = this;
        if(this.genGrantApi.secretKey) {
            this.genGrantApi.secretKey = this.generateToken(20);
        }else {
            this.genGrantApi.secretKey = this.generateToken(20);
            me.onSearchGrantApi()
        }
    }
}
