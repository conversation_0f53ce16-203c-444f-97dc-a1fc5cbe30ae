import { Component } from '@angular/core';
import { FormBuilder } from "@angular/forms";
import { LayoutService } from 'src/app/service/app.layout.service';
import { TranslateService } from "src/app/service/comon/translate.service";
import {SessionService} from "../../../service/session/SessionService";
import {Router} from "@angular/router";
import {MessageCommonService} from "../../../service/comon/message-common.service";
import {timeout} from "rxjs/operators";
import {AccountService} from "../../../service/account/AccountService";
import { CaptchaController } from '../../common-module/captcha/captcha';
import { ObservableService } from 'src/app/service/comon/observable.service';
import { CONSTANTS } from 'src/app/service/comon/constants';

@Component({
    selector: 'app-login',
    templateUrl: './login.component.html',
    styles: [`
        :host ::ng-deep .pi-eye,
        :host ::ng-deep .pi-eye-slash {
            transform:scale(1.6);
            margin-right: 1rem;
            color: var(--primary-color) !important;
        }
    `]
})
export class LoginComponent {

    valCheck: string[] = ['remember'];

    password!: string;

    isShowDialogForgotPass:boolean = false;
    isShowDialogCaptcha: boolean = false;
    isExpiredPassword: boolean = false;
    captchaControl: CaptchaController = new CaptchaController();

    loginInfo : any
    formForgot:any;
    forgotInfo: {
        email: string|null,
    };
    constructor(public layoutService: LayoutService,
        public tranService: TranslateService,
        private formBuilder: FormBuilder,
        private sessionService: SessionService,
        private router: Router,
        public messageCommonService: MessageCommonService,
        public accountService: AccountService,
        private observableService: ObservableService
    ) { }
        ngOnInit(): void {
            this.loginInfo = {
                email: "<EMAIL>",
                password: "123456aA@",
                rememberMe : true
            }

            this.loginInfo = {
                email: null,
                password: null,
                rememberMe : true
            }

            this.forgotInfo = {
                email: null,
            }
            this.formForgot = this.formBuilder.group(this.forgotInfo);
            let me = this;
        }

    resetPasswordEmail(forgotInfo : any) : void {
        // console.log("email reset: " + forgotInfo.email)
        if(this.formForgot.invalid) {
            return;
        }

        this.messageCommonService.onload();
        let me = this;
        this.accountService.forgotPasswordInit(forgotInfo.email, (response) => {
            me.messageCommonService.success(me.tranService.translate("global.message.forgotPassSendMailSuccess"));
            this.isShowDialogForgotPass = false;
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    login(loginInfo : any) : void {
        let me = this;
        this.handleLogin();
       //   this.isShowDialogCaptcha = true;
        // this.captchaControl.reload();
    }

    handleLogin(){
        let me = this;
        this.messageCommonService.onload();
        this.sessionService.login(this.loginInfo, (response) => {
            let token = response.id_token;
            if (response.error_code === 'error.expiredPassword') {
                me.observableService.next(CONSTANTS.OBSERVABLE.KEY_EXPIRED_PASSWORD, {isExpiredPassword: true});
                localStorage.setItem("tokenUpdatePass", token)
                return;
            }
            //set token to local storage
            localStorage.setItem("token", token)
            me.sessionService.updateToken(token);
            // call api get current
            me.sessionService.current((response) => {
                me.sessionService.setData("userInfo",JSON.stringify(response));
                me.sessionService.userInfo = response;
                me.getConfirmPolicyHistory();
            });
        }, null, ()=>{
            me.messageCommonService.offload();
        })
        this.isShowDialogCaptcha = false;
    }

    getConfirmPolicyHistory(){
        let me = this;
        this.accountService.getListConfirmPolicyHistory((response)=>{
            me.sessionService.confirmPolicyHistory = response || [];
            me.sessionService.setData("confirmPolicyHistory",JSON.stringify(response || []));
            let listPolicyChecked = me.sessionService.confirmPolicyHistory.filter(el => el.status == 0).map(el => el.policyId)
            this.sessionService.setData("listPolicyChecked", JSON.stringify(listPolicyChecked));
            setTimeout(function(){
                me.router.navigate(['/sims'])
            })
        })
    }

    handleCaptchaResolved(response: string) {
        if (response) {
            this.handleLogin();
        }
    }

    showDialogForgotPass() {
        this.isShowDialogForgotPass = true;
        this.formForgot.reset();
    }

    passwordFocus(){

    }
}


