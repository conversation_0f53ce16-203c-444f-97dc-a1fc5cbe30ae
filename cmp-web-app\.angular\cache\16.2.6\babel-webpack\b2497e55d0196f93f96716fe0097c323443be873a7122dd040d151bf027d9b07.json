{"ast": null, "code": "import { ComponentBase } from \"src/app/component.base\";\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\nimport { ComboLazyControl } from \"src/app/template/common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"src/app/service/report/ReportService\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/breadcrumb\";\nimport * as i5 from \"primeng/inputtext\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"../../../common-module/table/table.component\";\nimport * as i8 from \"../../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i9 from \"primeng/calendar\";\nimport * as i10 from \"primeng/tabmenu\";\nimport * as i11 from \"primeng/panel\";\nfunction ReportDynamicContentComponent_div_0_p_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 12);\n    i0.ɵɵlistener(\"onClick\", function ReportDynamicContentComponent_div_0_p_button_7_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onSubmitSearch());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.checkValidForm())(\"label\", ctx_r1.tranService.translate(\"global.button.preview\"));\n  }\n}\nfunction ReportDynamicContentComponent_div_0_form_9_div_3_span_1_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReportDynamicContentComponent_div_0_form_9_div_3_span_1_small_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.tranService.translate(\"global.message.required\"), \" \");\n  }\n}\nfunction ReportDynamicContentComponent_div_0_form_9_div_3_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 21)(1, \"input\", 22);\n    i0.ɵɵlistener(\"keydown\", function ReportDynamicContentComponent_div_0_form_9_div_3_span_1_Template_input_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r15.preventCharacter($event));\n    })(\"ngModelChange\", function ReportDynamicContentComponent_div_0_form_9_div_3_span_1_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const param_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r17.searchInfo[param_r7.prKey] = $event);\n    })(\"ngModelChange\", function ReportDynamicContentComponent_div_0_form_9_div_3_span_1_Template_input_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const param_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r19 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r19.checkIsNumberOrNull(param_r7.prKey));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, ReportDynamicContentComponent_div_0_form_9_div_3_span_1_span_4_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ReportDynamicContentComponent_div_0_form_9_div_3_span_1_small_5_Template, 2, 1, \"small\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const param_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r8.formSearch.controls[param_r7.prKey].dirty && (ctx_r8.formSearch.controls[param_r7.prKey].errors == null ? null : ctx_r8.formSearch.controls[param_r7.prKey].errors.required) ? \"report-param-required-error\" : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r8.searchInfo[param_r7.prKey])(\"formControlName\", param_r7.prKey)(\"required\", param_r7.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"htmlFor\", param_r7.prKey);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(param_r7.prDisplayName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", param_r7.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.formSearch.controls[param_r7.prKey].dirty && (ctx_r8.formSearch.controls[param_r7.prKey].errors == null ? null : ctx_r8.formSearch.controls[param_r7.prKey].errors.required));\n  }\n}\nfunction ReportDynamicContentComponent_div_0_form_9_div_3_span_2_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReportDynamicContentComponent_div_0_form_9_div_3_span_2_small_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.tranService.translate(\"global.message.required\"), \" \");\n  }\n}\nfunction ReportDynamicContentComponent_div_0_form_9_div_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 21)(1, \"input\", 26);\n    i0.ɵɵlistener(\"ngModelChange\", function ReportDynamicContentComponent_div_0_form_9_div_3_span_2_Template_input_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const param_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r24 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r24.searchInfo[param_r7.prKey] = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, ReportDynamicContentComponent_div_0_form_9_div_3_span_2_span_4_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ReportDynamicContentComponent_div_0_form_9_div_3_span_2_small_5_Template, 2, 1, \"small\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const param_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r9.formSearch.controls[param_r7.prKey].dirty && (ctx_r9.formSearch.controls[param_r7.prKey].errors == null ? null : ctx_r9.formSearch.controls[param_r7.prKey].errors.required) ? \"report-param-required-error\" : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r9.searchInfo[param_r7.prKey])(\"formControlName\", param_r7.prKey)(\"required\", param_r7.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"htmlFor\", param_r7.prKey);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(param_r7.prDisplayName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", param_r7.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.formSearch.controls[param_r7.prKey].dirty && (ctx_r9.formSearch.controls[param_r7.prKey].errors == null ? null : ctx_r9.formSearch.controls[param_r7.prKey].errors.required));\n  }\n}\nfunction ReportDynamicContentComponent_div_0_form_9_div_3_span_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReportDynamicContentComponent_div_0_form_9_div_3_span_3_small_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r29.tranService.translate(\"global.message.required\"), \" \");\n  }\n}\nfunction ReportDynamicContentComponent_div_0_form_9_div_3_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 21)(1, \"p-calendar\", 27);\n    i0.ɵɵlistener(\"ngModelChange\", function ReportDynamicContentComponent_div_0_form_9_div_3_span_3_Template_p_calendar_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const param_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r30 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r30.searchInfo[param_r7.prKey] = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, ReportDynamicContentComponent_div_0_form_9_div_3_span_3_span_4_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ReportDynamicContentComponent_div_0_form_9_div_3_span_3_small_5_Template, 2, 1, \"small\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const param_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r10.formSearch.controls[param_r7.prKey].dirty && (ctx_r10.formSearch.controls[param_r7.prKey].errors == null ? null : ctx_r10.formSearch.controls[param_r7.prKey].errors.required) ? \"report-param-required-error\" : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r10.searchInfo[param_r7.prKey])(\"showClear\", true)(\"showIcon\", true)(\"dateFormat\", param_r7.dateType == ctx_r10.dateTypes.MONTH ? \"mm/yy\" : \"dd/mm/yy\")(\"hourFormat\", param_r7.dateType == ctx_r10.dateTypes.DATETIME ? \"hh:mm:ss\" : \"\")(\"view\", param_r7.dateType == ctx_r10.dateTypes.MONTH ? \"month\" : \"date\")(\"showTime\", param_r7.dateType == ctx_r10.dateTypes.DATETIME)(\"showSeconds\", param_r7.dateType == ctx_r10.dateTypes.DATETIME)(\"showClear\", true)(\"showIcon\", true)(\"formControlName\", param_r7.prKey)(\"placeholder\", ctx_r10.tranService.translate(\"global.text.inputDate\"))(\"required\", param_r7.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"htmlFor\", param_r7.prKey);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(param_r7.prDisplayName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", param_r7.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r10.formSearch.controls[param_r7.prKey].dirty && (ctx_r10.formSearch.controls[param_r7.prKey].errors == null ? null : ctx_r10.formSearch.controls[param_r7.prKey].errors.required));\n  }\n}\nfunction ReportDynamicContentComponent_div_0_form_9_div_3_span_4_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ReportDynamicContentComponent_div_0_form_9_div_3_span_4_small_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r35 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r35.tranService.translate(\"global.message.required\"), \" \");\n  }\n}\nfunction ReportDynamicContentComponent_div_0_form_9_div_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 21)(1, \"p-calendar\", 28);\n    i0.ɵɵlistener(\"ngModelChange\", function ReportDynamicContentComponent_div_0_form_9_div_3_span_4_Template_p_calendar_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r37);\n      const param_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r36 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r36.searchInfo[param_r7.prKey] = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 23);\n    i0.ɵɵtext(3);\n    i0.ɵɵtemplate(4, ReportDynamicContentComponent_div_0_form_9_div_3_span_4_span_4_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ReportDynamicContentComponent_div_0_form_9_div_3_span_4_small_5_Template, 2, 1, \"small\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const param_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r11.formSearch.controls[param_r7.prKey].dirty && (ctx_r11.formSearch.controls[param_r7.prKey].errors == null ? null : ctx_r11.formSearch.controls[param_r7.prKey].errors.required) ? \"report-param-required-error\" : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r11.searchInfo[param_r7.prKey])(\"showClear\", true)(\"showIcon\", true)(\"showTime\", true)(\"showSeconds\", true)(\"showClear\", true)(\"showIcon\", true)(\"formControlName\", param_r7.prKey)(\"placeholder\", ctx_r11.tranService.translate(\"global.text.inputDate\"))(\"required\", param_r7.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"htmlFor\", param_r7.prKey);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(param_r7.prDisplayName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", param_r7.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.formSearch.controls[param_r7.prKey].dirty && (ctx_r11.formSearch.controls[param_r7.prKey].errors == null ? null : ctx_r11.formSearch.controls[param_r7.prKey].errors.required));\n  }\n}\nfunction ReportDynamicContentComponent_div_0_form_9_div_3_div_5_small_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r40.tranService.translate(\"global.message.required\"), \" \");\n  }\n}\nconst _c0 = function () {\n  return {};\n};\nfunction ReportDynamicContentComponent_div_0_form_9_div_3_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r42 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"vnpt-select\", 30);\n    i0.ɵɵlistener(\"valueChange\", function ReportDynamicContentComponent_div_0_form_9_div_3_div_5_Template_vnpt_select_valueChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r42);\n      const param_r7 = i0.ɵɵnextContext().$implicit;\n      const ctx_r41 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r41.searchInfo[param_r7.prKey] = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, ReportDynamicContentComponent_div_0_form_9_div_3_div_5_small_2_Template, 2, 1, \"small\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const param_r7 = i0.ɵɵnextContext().$implicit;\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"control\", param_r7.control)(\"value\", ctx_r12.searchInfo[param_r7.prKey])(\"placeholder\", param_r7.prDisplayName)(\"isAutoComplete\", param_r7.prType == ctx_r12.paramTypes.STRING)(\"isMultiChoice\", param_r7.isMultiChoice)(\"options\", param_r7.valueList)(\"objectKey\", param_r7.queryInfo.objectKey)(\"paramKey\", param_r7.isAutoComplete ? param_r7.queryInfo.input : \"display\")(\"keyReturn\", param_r7.isAutoComplete ? param_r7.queryInfo.output : \"value\")(\"displayPattern\", param_r7.isAutoComplete ? param_r7.queryInfo.displayPattern : \"${display}\")(\"lazyLoad\", param_r7.isAutoComplete)(\"paramDefault\", param_r7.isAutoComplete && param_r7.queryParam ? ctx_r12.getParamDefault(param_r7) : i0.ɵɵpureFunction0(16, _c0))(\"floatLabel\", true)(\"required\", param_r7.required)(\"showTextRequired\", param_r7.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", param_r7.control.dirty && param_r7.control.error.required);\n  }\n}\nfunction ReportDynamicContentComponent_div_0_form_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, ReportDynamicContentComponent_div_0_form_9_div_3_span_1_Template, 6, 9, \"span\", 19);\n    i0.ɵɵtemplate(2, ReportDynamicContentComponent_div_0_form_9_div_3_span_2_Template, 6, 9, \"span\", 19);\n    i0.ɵɵtemplate(3, ReportDynamicContentComponent_div_0_form_9_div_3_span_3_Template, 6, 19, \"span\", 19);\n    i0.ɵɵtemplate(4, ReportDynamicContentComponent_div_0_form_9_div_3_span_4_Template, 6, 16, \"span\", 19);\n    i0.ɵɵtemplate(5, ReportDynamicContentComponent_div_0_form_9_div_3_div_5_Template, 3, 17, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const param_r7 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", param_r7.prType == ctx_r6.paramTypes.NUMBER);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", param_r7.prType == ctx_r6.paramTypes.STRING && param_r7.isAutoComplete == false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", param_r7.prType == ctx_r6.paramTypes.DATE);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", param_r7.prType == ctx_r6.paramTypes.TIMESTAMP);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", param_r7.prType == ctx_r6.paramTypes.LIST_NUMBER || param_r7.prType == ctx_r6.paramTypes.LIST_STRING || param_r7.prType == ctx_r6.paramTypes.STRING && param_r7.isAutoComplete == true);\n  }\n}\nfunction ReportDynamicContentComponent_div_0_form_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 13);\n    i0.ɵɵlistener(\"ngSubmit\", function ReportDynamicContentComponent_div_0_form_9_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r45.onSubmitSearch());\n    });\n    i0.ɵɵelementStart(1, \"p-panel\", 14)(2, \"div\", 15);\n    i0.ɵɵtemplate(3, ReportDynamicContentComponent_div_0_form_9_div_3_Template, 6, 5, \"div\", 16);\n    i0.ɵɵelement(4, \"div\", 17);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.formSearch);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"toggleable\", true)(\"header\", ctx_r2.tranService.translate(\"global.text.filter\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.listParameters);\n  }\n}\nfunction ReportDynamicContentComponent_div_0_div_12_table_vnpt_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"table-vnpt\", 33);\n    i0.ɵɵlistener(\"selectItemsChange\", function ReportDynamicContentComponent_div_0_div_12_table_vnpt_1_Template_table_vnpt_selectItemsChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r50);\n      const table_r47 = i0.ɵɵnextContext().$implicit;\n      const ctx_r49 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r49.mapTable[table_r47.id].selectItems = $event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const table_r47 = i0.ɵɵnextContext().$implicit;\n    const ctx_r48 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"selectItems\", ctx_r48.mapTable[table_r47.id].selectItems)(\"columns\", ctx_r48.mapTable[table_r47.id].columns)(\"dataSet\", ctx_r48.mapTable[table_r47.id].dataSet)(\"options\", ctx_r48.mapTable[table_r47.id].optionTable)(\"loadData\", ctx_r48.mapTable[table_r47.id].loadData.bind(ctx_r48))(\"pageNumber\", ctx_r48.mapTable[table_r47.id].pageNumber)(\"pageSize\", ctx_r48.mapTable[table_r47.id].pageSize)(\"sort\", ctx_r48.mapTable[table_r47.id].sort)(\"params\", ctx_r48.mapTable[table_r47.id].searchInfo)(\"labelTable\", table_r47.tableName);\n  }\n}\nfunction ReportDynamicContentComponent_div_0_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, ReportDynamicContentComponent_div_0_div_12_table_vnpt_1_Template, 1, 11, \"table-vnpt\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const table_r47 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.activeTable == table_r47.id);\n  }\n}\nfunction ReportDynamicContentComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"p-breadcrumb\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 5);\n    i0.ɵɵtemplate(7, ReportDynamicContentComponent_div_0_p_button_7_Template, 1, 2, \"p-button\", 6);\n    i0.ɵɵelementStart(8, \"p-button\", 7);\n    i0.ɵɵlistener(\"onClick\", function ReportDynamicContentComponent_div_0_Template_p_button_onClick_8_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r53 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r53.export());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(9, ReportDynamicContentComponent_div_0_form_9_Template, 5, 4, \"form\", 8);\n    i0.ɵɵelementStart(10, \"div\", 9);\n    i0.ɵɵelement(11, \"p-tabMenu\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, ReportDynamicContentComponent_div_0_div_12_Template, 2, 1, \"div\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(ctx_r0.reportDetail.name));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"model\", ctx_r0.items)(\"home\", ctx_r0.home);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.reportDetail.enablePreview);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.checkValidForm())(\"label\", ctx_r0.tranService.translate(\"global.button.export\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formSearch);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"model\", ctx_r0.menuTable)(\"activeItem\", ctx_r0.defaultTableActive);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.tables);\n  }\n}\nexport class ReportDynamicContentComponent extends ComponentBase {\n  constructor(injector, formBuilder, reportService) {\n    super(injector);\n    this.formBuilder = formBuilder;\n    this.reportService = reportService;\n    this.listTable = [];\n    this.listParameters = [];\n    this.paramTypes = CONSTANTS.PARAMETER_TYPE;\n    this.dateTypes = CONSTANTS.DATE_TYPE;\n    this.menuTable = [];\n    this.defaultTableActive = null;\n    this.tables = [];\n    this.mapTable = {};\n  }\n  ngOnInit() {\n    this.idReport = parseInt(this.route.snapshot.paramMap.get(\"id\"));\n    this.getReportDetail();\n  }\n  getReportDetail() {\n    let me = this;\n    this.reportService.getDetailReportDynamic(this.idReport, response => {\n      me.reportDetail = response;\n      setTimeout(function () {\n        me.loadPage();\n      });\n    });\n  }\n  loadPage() {\n    this.items = [{\n      label: this.tranService.translate(\"global.menu.report\")\n    }, {\n      label: this.tranService.translate(\"global.menu.dynamicreport\"),\n      routerLink: '/reports/report-dynamic'\n    }, {\n      label: this.tranService.translate(\"permission.RptContent.RptContent\"),\n      routerLink: '/reports/report-dynamic/report-content'\n    }, {\n      label: this.reportDetail.name\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.loadListParams();\n    this.loadTables();\n  }\n  loadListParams() {\n    let me = this;\n    if (this.reportDetail.filterParams) {\n      this.listParameters = JSON.parse(this.reportDetail.filterParams);\n      this.listParameters.forEach(el => {\n        if (el.prType == this.paramTypes.LIST_NUMBER || el.prType == this.paramTypes.LIST_STRING || el.prType == this.paramTypes.STRING && el.isAutoComplete == true) {\n          el[\"control\"] = new ComboLazyControl();\n        }\n      });\n      this.searchInfo = {};\n      this.listParameters.forEach(el => {\n        me.searchInfo[el.prKey] = null;\n      });\n      this.formSearch = this.formBuilder.group(this.searchInfo);\n    }\n  }\n  loadTables() {\n    let me = this;\n    if (this.reportDetail.reportContents) {\n      this.reportDetail.reportContents.forEach(el => {\n        me.menuTable.push({\n          id: el.id,\n          label: el.tableName,\n          command: () => {\n            me.activeTable = el.id;\n          }\n        });\n        let columnKeys = el.columnQueryResult.split(\",\");\n        let columnDisplays = el.columnDisplay.split(\",\");\n        let columns = [];\n        columnKeys.forEach((el, index) => {\n          columns.push({\n            key: el,\n            name: columnDisplays[index]\n          });\n        });\n        me.mapTable[el.id] = {\n          selectItems: [],\n          columns: columns.map(function (el) {\n            let object = {\n              key: el.key,\n              name: el.name,\n              align: \"left\",\n              isShow: true,\n              isSort: false,\n              size: `calc((100% - 100px) / ${columns.length})`\n            };\n            return object;\n          }),\n          dataSet: {\n            content: [],\n            total: 0\n          },\n          dataOrigin: [],\n          loadData(page, size, sort, params) {\n            me.loadDataTable(el.id, page, size, sort, params);\n          },\n          optionTable: {\n            hasClearSelected: true,\n            action: null,\n            hasShowChoose: false,\n            hasShowIndex: false,\n            hasShowJumpPage: true,\n            hasShowToggleColumn: false,\n            paginator: true\n          },\n          pageNumber: 0,\n          pageSize: 10,\n          params: {},\n          sort: `${columns[0].key},asc`\n        };\n      });\n      this.defaultTableActive = this.menuTable[0];\n      this.activeTable = this.defaultTableActive.id;\n      this.tables = [...this.reportDetail.reportContents];\n    }\n  }\n  prepareData() {\n    let data = {\n      id: this.reportDetail.id,\n      customerCodes: null,\n      paramsValue: []\n    };\n    this.listParameters.forEach(el => {\n      if (el.prType == CONSTANTS.PARAMETER_TYPE.DATE) {\n        data.paramsValue.push({\n          prKey: el.prKey,\n          value: this.searchInfo[el.prKey] ? this.utilService.convertDateTimeToString(this.searchInfo[el.prKey]) : null\n        });\n      } else if (el.prType == CONSTANTS.PARAMETER_TYPE.TIMESTAMP) {\n        data.paramsValue.push({\n          prKey: el.prKey,\n          value: this.searchInfo[el.prKey] ? this.searchInfo[el.prKey].getTime() : null\n        });\n      } else if (el.prType == CONSTANTS.PARAMETER_TYPE.NUMBER) {\n        data.paramsValue.push({\n          prKey: el.prKey,\n          value: this.searchInfo[el.prKey] ? this.searchInfo[el.prKey] * 1 : null\n        });\n      } else if (el.prType == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || el.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING) {\n        if (el.isMultiChoice) {\n          if (this.searchInfo[el.prKey] == null || this.searchInfo[el.prKey].length == 0) {\n            data.paramsValue.push({\n              prKey: el.prKey,\n              value: null\n            });\n          } else {\n            data.paramsValue.push({\n              prKey: el.prKey,\n              value: this.searchInfo[el.prKey] ? this.searchInfo[el.prKey] : null\n            });\n          }\n        } else {\n          data.paramsValue.push({\n            prKey: el.prKey,\n            value: this.searchInfo[el.prKey] ? [this.searchInfo[el.prKey]] : null\n          });\n        }\n      } else {\n        data.paramsValue.push({\n          prKey: el.prKey,\n          value: this.searchInfo[el.prKey] ? this.searchInfo[el.prKey] : null\n        });\n      }\n    });\n    return data;\n  }\n  getParamDefault(param) {\n    const parsed = this.parseKeyValuePairs(param.queryParam);\n    // Khởi tạo đối tượng trống để lưu các cặp key-value\n    const result = {};\n    if (param.isAutoComplete) {\n      // Sử dụng vòng lặp để thêm nhiều cặp key-value vào đối tượng\n      for (let i = 0; i < parsed.keys.length; i++) {\n        result[parsed.keys[i]] = parsed.values[i] || null;\n      }\n    }\n    return result;\n  }\n  parseKeyValuePairs(input) {\n    // Khởi tạo đối tượng để lưu các cặp key-value\n    const keyValueMap = {};\n    // Tách chuỗi thành các cặp key-value\n    const pairs = input.split('&');\n    // Xử lý từng cặp\n    for (const pair of pairs) {\n      const [key, value] = pair.split('=');\n      // Xử lý giá trị bắt đầu bằng $\n      let actualValue;\n      if (value.startsWith('$')) {\n        actualValue = this.getValueData(value.substring(1)); // Loại bỏ dấu $ và gọi hàm\n      } else {\n        // Xử lý giá trị không bắt đầu bằng $\n        actualValue = value.replace(/^\"|\"$/g, ''); // Loại bỏ dấu ngoặc kép ở đầu và cuối nếu có\n      }\n      // Thêm giá trị vào đối tượng keyValueMap\n      if (key in keyValueMap) {\n        keyValueMap[key].push(actualValue);\n      } else {\n        keyValueMap[key] = [actualValue];\n      }\n    }\n    // Tạo mảng keys và values từ đối tượng keyValueMap\n    const keys = [];\n    const values = [];\n    for (const key in keyValueMap) {\n      keys.push(key);\n      values.push(keyValueMap[key].join(',')); // Nối các giá trị với dấu phẩy\n    }\n    // console.log(keys, values);\n    // Trả về cả mảng keys và values\n    return {\n      keys,\n      values\n    };\n  }\n  getValueData(key) {\n    let returnValue;\n    if (this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.DATE) {\n      returnValue = this.searchInfo[key] ? this.utilService.convertDateTimeToString(this.searchInfo[key]) : null;\n    } else if (this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.TIMESTAMP) {\n      returnValue = this.searchInfo[key] ? this.searchInfo[key].getTime() : null;\n    } else if (this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.NUMBER) {\n      returnValue = this.searchInfo[key] ? this.searchInfo[key] * 1 : null;\n    } else if (this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.LIST_STRING) {\n      returnValue = this.searchInfo[key] ? this.searchInfo[key] : null;\n    } else {\n      returnValue = this.searchInfo[key] ? this.searchInfo[key] : null;\n    }\n    return returnValue;\n  }\n  getTypeKey(key) {\n    const value = this.listParameters.find(data => data.prKey === key);\n    return value ? value.prType : null;\n  }\n  onSubmitSearch() {\n    if (!this.reportDetail.enablePreview) return;\n    if (!this.checkValidForm()) return;\n    let me = this;\n    let data = this.prepareData();\n    this.messageCommonService.onload();\n    this.reportService.preview(data, response => {\n      if (response.reportContents) {\n        response.reportContents.forEach(tableContent => {\n          me.mapTable[tableContent.reportContentID].dataOrigin = tableContent.data.filter(el => Object.keys(el).length > 0);\n          me.loadDataTable(tableContent.reportContentID, 0, me.mapTable[tableContent.reportContentID].pageSize, me.mapTable[tableContent.reportContentID].sort, {});\n        });\n      }\n      // console.log(me.mapTable)\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  loadDataTable(tableId, page, size, sort, param) {\n    this.mapTable[tableId].pageNumber = page;\n    this.mapTable[tableId].pageSize = size;\n    this.mapTable[tableId].sort = sort;\n    this.mapTable[tableId].params = param;\n    let dataSet = {\n      content: this.mapTable[tableId].dataOrigin.slice(page * size, page * size + size),\n      total: this.mapTable[tableId].dataOrigin.length\n    };\n    this.mapTable[tableId].dataSet = dataSet;\n  }\n  export() {\n    if (!this.checkValidForm()) return;\n    let data = this.prepareData();\n    this.messageCommonService.onload();\n    this.reportService.exportFile(data, () => {}, error => {\n      console.log(error);\n      let me = this;\n      if (error.error instanceof Blob) {\n        error.error.text().then(function (resultText) {\n          let bodyError = JSON.parse(resultText).error;\n          if (bodyError.errorCode == \"error.report.excel.limit\") {\n            me.messageCommonService.error(me.tranService.translate(\"report.text.errorExportLimit\"));\n          } else {\n            me.messageCommonService.error(me.tranService.translate(\"global.message.error\"));\n          }\n        });\n      } else {\n        console.log(error);\n      }\n    });\n  }\n  checkValidForm() {\n    if (!this.formSearch) return false;\n    if (this.formSearch.invalid) return false;\n    if ((this.listParameters || []).length > 0) {\n      for (let i = 0; i < this.listParameters.length; i++) {\n        let el = this.listParameters[i];\n        if (el[\"control\"]) {\n          if (el[\"control\"].invalid) {\n            return false;\n          }\n        }\n      }\n    }\n    return true;\n  }\n  preventCharacter(event) {\n    if (event.ctrlKey || event.altKey || event.shiftKey) {\n      return;\n    }\n    if (event.keyCode == 8 || event.keyCode == 13 || event.keyCode == 46 || event.keyCode == 37 || event.keyCode == 39) {\n      return;\n    }\n    if (event.keyCode < 48 || event.keyCode > 57) {\n      event.preventDefault();\n    }\n  }\n  checkIsNumberOrNull(key) {\n    let me = this;\n    if (this.searchInfo[key] == null) return;\n    if (isNaN(this.searchInfo[key])) {\n      setTimeout(function () {\n        me.searchInfo[key] = null;\n      });\n    }\n  }\n  static {\n    this.ɵfac = function ReportDynamicContentComponent_Factory(t) {\n      return new (t || ReportDynamicContentComponent)(i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ReportService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ReportDynamicContentComponent,\n      selectors: [[\"report-dynamic-content\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[4, \"ngIf\"], [1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-3\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\", \"report-dynamic-button-div\"], [\"styleClass\", \"p-button-secondary mr-2\", \"icon\", \"pi pi-search\", 3, \"disabled\", \"label\", \"onClick\", 4, \"ngIf\"], [\"styleClass\", \"p-button-info\", \"icon\", \"pi pi-download\", 3, \"disabled\", \"label\", \"onClick\"], [\"class\", \"pb-2 pt-3 vnpt-field-set\", 3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [1, \"w-full\", \"custom-tabMenu\", 2, \"padding\", \"2px\", \"margin-top\", \"12px\"], [3, \"model\", \"activeItem\"], [\"class\", \"w-full\", \"style\", \"padding: 2px;\", 4, \"ngFor\", \"ngForOf\"], [\"styleClass\", \"p-button-secondary mr-2\", \"icon\", \"pi pi-search\", 3, \"disabled\", \"label\", \"onClick\"], [1, \"pb-2\", \"pt-3\", \"vnpt-field-set\", 3, \"formGroup\", \"ngSubmit\"], [3, \"toggleable\", \"header\"], [1, \"grid\"], [\"class\", \"col-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-3\", \"pb-0\"], [1, \"col-3\"], [\"class\", \"p-float-label relative\", 3, \"class\", 4, \"ngIf\"], [\"class\", \"relative\", 4, \"ngIf\"], [1, \"p-float-label\", \"relative\"], [\"pInputText\", \"\", 1, \"w-full\", 3, \"ngModel\", \"formControlName\", \"required\", \"keydown\", \"ngModelChange\"], [3, \"htmlFor\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [1, \"text-red-500\"], [\"pInputText\", \"\", 1, \"w-full\", 3, \"ngModel\", \"formControlName\", \"required\", \"ngModelChange\"], [\"styleClass\", \"w-full\", \"appendTo\", \"body\", 3, \"ngModel\", \"showClear\", \"showIcon\", \"dateFormat\", \"hourFormat\", \"view\", \"showTime\", \"showSeconds\", \"formControlName\", \"placeholder\", \"required\", \"ngModelChange\"], [\"styleClass\", \"w-full\", \"dateFormat\", \"dd/mm/yy\", \"hourFormat\", \"hh:mm:ss\", \"appendTo\", \"body\", 3, \"ngModel\", \"showClear\", \"showIcon\", \"showTime\", \"showSeconds\", \"formControlName\", \"placeholder\", \"required\", \"ngModelChange\"], [1, \"relative\"], [\"typeValue\", \"primitive\", 1, \"w-full\", 3, \"control\", \"value\", \"placeholder\", \"isAutoComplete\", \"isMultiChoice\", \"options\", \"objectKey\", \"paramKey\", \"keyReturn\", \"displayPattern\", \"lazyLoad\", \"paramDefault\", \"floatLabel\", \"required\", \"showTextRequired\", \"valueChange\"], [1, \"w-full\", 2, \"padding\", \"2px\"], [3, \"fieldId\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"pageNumber\", \"pageSize\", \"sort\", \"params\", \"labelTable\", \"selectItemsChange\", 4, \"ngIf\"], [3, \"fieldId\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"pageNumber\", \"pageSize\", \"sort\", \"params\", \"labelTable\", \"selectItemsChange\"]],\n      template: function ReportDynamicContentComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ReportDynamicContentComponent_div_0_Template, 13, 10, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.reportDetail);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.Breadcrumb, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i5.InputText, i6.Button, i7.TableVnptComponent, i8.VnptCombobox, i9.Calendar, i10.TabMenu, i11.Panel],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["ComponentBase", "CONSTANTS", "ComboLazyControl", "i0", "ɵɵelementStart", "ɵɵlistener", "ReportDynamicContentComponent_div_0_p_button_7_Template_p_button_onClick_0_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "onSubmitSearch", "ɵɵelementEnd", "ɵɵproperty", "ctx_r1", "checkValidForm", "tranService", "translate", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r14", "ReportDynamicContentComponent_div_0_form_9_div_3_span_1_Template_input_keydown_1_listener", "$event", "_r16", "ctx_r15", "preventCharacter", "ReportDynamicContentComponent_div_0_form_9_div_3_span_1_Template_input_ngModelChange_1_listener", "param_r7", "$implicit", "ctx_r17", "searchInfo", "pr<PERSON><PERSON>", "ctx_r19", "checkIsNumberOrNull", "ɵɵtemplate", "ReportDynamicContentComponent_div_0_form_9_div_3_span_1_span_4_Template", "ReportDynamicContentComponent_div_0_form_9_div_3_span_1_small_5_Template", "ɵɵclassMap", "ctx_r8", "formSearch", "controls", "dirty", "errors", "required", "ɵɵtextInterpolate", "prDisplayName", "ctx_r23", "ReportDynamicContentComponent_div_0_form_9_div_3_span_2_Template_input_ngModelChange_1_listener", "_r25", "ctx_r24", "ReportDynamicContentComponent_div_0_form_9_div_3_span_2_span_4_Template", "ReportDynamicContentComponent_div_0_form_9_div_3_span_2_small_5_Template", "ctx_r9", "ctx_r29", "ReportDynamicContentComponent_div_0_form_9_div_3_span_3_Template_p_calendar_ngModelChange_1_listener", "_r31", "ctx_r30", "ReportDynamicContentComponent_div_0_form_9_div_3_span_3_span_4_Template", "ReportDynamicContentComponent_div_0_form_9_div_3_span_3_small_5_Template", "ctx_r10", "dateType", "dateTypes", "MONTH", "DATETIME", "ctx_r35", "ReportDynamicContentComponent_div_0_form_9_div_3_span_4_Template_p_calendar_ngModelChange_1_listener", "_r37", "ctx_r36", "ReportDynamicContentComponent_div_0_form_9_div_3_span_4_span_4_Template", "ReportDynamicContentComponent_div_0_form_9_div_3_span_4_small_5_Template", "ctx_r11", "ctx_r40", "ReportDynamicContentComponent_div_0_form_9_div_3_div_5_Template_vnpt_select_valueChange_1_listener", "_r42", "ctx_r41", "ReportDynamicContentComponent_div_0_form_9_div_3_div_5_small_2_Template", "control", "ctx_r12", "prType", "paramTypes", "STRING", "isMultiChoice", "valueList", "queryInfo", "object<PERSON>ey", "isAutoComplete", "input", "output", "displayPattern", "queryParam", "getParamDefault", "ɵɵpureFunction0", "_c0", "error", "ReportDynamicContentComponent_div_0_form_9_div_3_span_1_Template", "ReportDynamicContentComponent_div_0_form_9_div_3_span_2_Template", "ReportDynamicContentComponent_div_0_form_9_div_3_span_3_Template", "ReportDynamicContentComponent_div_0_form_9_div_3_span_4_Template", "ReportDynamicContentComponent_div_0_form_9_div_3_div_5_Template", "ctx_r6", "NUMBER", "DATE", "TIMESTAMP", "LIST_NUMBER", "LIST_STRING", "ReportDynamicContentComponent_div_0_form_9_Template_form_ngSubmit_0_listener", "_r46", "ctx_r45", "ReportDynamicContentComponent_div_0_form_9_div_3_Template", "ɵɵelement", "ctx_r2", "listParameters", "ReportDynamicContentComponent_div_0_div_12_table_vnpt_1_Template_table_vnpt_selectItemsChange_0_listener", "_r50", "table_r47", "ctx_r49", "mapTable", "id", "selectItems", "ctx_r48", "columns", "dataSet", "optionTable", "loadData", "bind", "pageNumber", "pageSize", "sort", "tableName", "ReportDynamicContentComponent_div_0_div_12_table_vnpt_1_Template", "ctx_r3", "activeTable", "ReportDynamicContentComponent_div_0_p_button_7_Template", "ReportDynamicContentComponent_div_0_Template_p_button_onClick_8_listener", "_r54", "ctx_r53", "export", "ReportDynamicContentComponent_div_0_form_9_Template", "ReportDynamicContentComponent_div_0_div_12_Template", "ctx_r0", "reportDetail", "name", "items", "home", "enablePreview", "menuTable", "defaultTableActive", "tables", "ReportDynamicContentComponent", "constructor", "injector", "formBuilder", "reportService", "listTable", "PARAMETER_TYPE", "DATE_TYPE", "ngOnInit", "idReport", "parseInt", "route", "snapshot", "paramMap", "get", "getReportDetail", "me", "getDetailReportDynamic", "response", "setTimeout", "loadPage", "label", "routerLink", "icon", "loadListParams", "loadTables", "filterParams", "JSON", "parse", "for<PERSON>ach", "el", "group", "reportContents", "push", "command", "columnKeys", "columnQueryResult", "split", "columnDisplays", "columnDisplay", "index", "key", "map", "object", "align", "isShow", "isSort", "size", "length", "content", "total", "dataOrigin", "page", "params", "loadDataTable", "hasClearSelected", "action", "hasShowChoose", "hasShowIndex", "hasShowJumpPage", "hasShowToggleColumn", "paginator", "prepareData", "data", "customerCodes", "paramsValue", "value", "utilService", "convertDateTimeToString", "getTime", "param", "parsed", "parseKeyValuePairs", "result", "i", "keys", "values", "keyValueMap", "pairs", "pair", "actualValue", "startsWith", "getValueData", "substring", "replace", "join", "returnValue", "getTypeKey", "find", "messageCommonService", "onload", "preview", "tableContent", "reportContentID", "filter", "Object", "offload", "tableId", "slice", "exportFile", "console", "log", "Blob", "text", "then", "resultText", "bodyError", "errorCode", "invalid", "event", "ctrl<PERSON>ey", "altKey", "shift<PERSON>ey", "keyCode", "preventDefault", "isNaN", "ɵɵdirectiveInject", "Injector", "i1", "FormBuilder", "i2", "ReportService", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "ReportDynamicContentComponent_Template", "rf", "ctx", "ReportDynamicContentComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\reporting\\report-dynamic\\content\\report.dynamic.content.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\reporting\\report-dynamic\\content\\report.dynamic.content.component.html"], "sourcesContent": ["import { Component, Injector, OnInit } from \"@angular/core\";\r\nimport { MenuItem } from \"primeng/api\";\r\nimport { ComponentBase } from \"src/app/component.base\";\r\nimport { ReportService } from \"src/app/service/report/ReportService\";\r\nimport { ParameterInfo, TableReportInfo } from \"../components/tab.report.dynamic.general\";\r\nimport { FormBuilder } from \"@angular/forms\";\r\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\r\nimport { ColumnInfo, OptionTable } from \"src/app/template/common-module/table/table.component\";\r\nimport { dA } from \"@fullcalendar/core/internal-common\";\r\nimport { ComboLazyControl } from \"src/app/template/common-module/combobox-lazyload/combobox.lazyload\";\r\nimport {TimeoutError} from \"rxjs\";\r\nimport {HttpErrorResponse} from \"@angular/common/http\";\r\n\r\n@Component({\r\n    selector: \"report-dynamic-content\",\r\n    templateUrl: \"./report.dynamic.content.component.html\"\r\n})\r\nexport class ReportDynamicContentComponent extends ComponentBase implements OnInit {\r\n    constructor(injector: Injector, private formBuilder: FormBuilder,\r\n                private reportService: ReportService) {\r\n        super(injector);\r\n    }\r\n\r\n    reportDetail: any;\r\n    idReport: number;\r\n    items: MenuItem[];\r\n    home: MenuItem;\r\n\r\n    listTable: Array<TableReportInfo> = [];\r\n    listParameters: Array<ParameterInfo> = [];\r\n\r\n    formSearch: any;\r\n    searchInfo: any;\r\n\r\n    paramTypes = CONSTANTS.PARAMETER_TYPE;\r\n    dateTypes = CONSTANTS.DATE_TYPE;\r\n\r\n    menuTable: MenuItem[] = [];\r\n    defaultTableActive: MenuItem = null;\r\n    activeTable: any;\r\n    tables: Array<any> = [];\r\n    mapTable: {\r\n        [key: string | number]: {\r\n            selectItems: Array<any>,\r\n            columns: ColumnInfo[],\r\n            dataSet: {\r\n                content: Array<any>,\r\n                total: number\r\n            },\r\n            optionTable: OptionTable,\r\n            loadData(page, size, sort, params):void,\r\n            pageNumber: number,\r\n            pageSize: number,\r\n            sort: string,\r\n            params: any,\r\n            dataOrigin: Array<any>\r\n        }\r\n    } ={};\r\n\r\n    ngOnInit(): void {\r\n        this.idReport = parseInt(this.route.snapshot.paramMap.get(\"id\"));\r\n        this.getReportDetail();\r\n    }\r\n\r\n    getReportDetail(){\r\n        let me = this;\r\n        this.reportService.getDetailReportDynamic(this.idReport, (response)=>{\r\n            me.reportDetail = response;\r\n            setTimeout(function(){\r\n                me.loadPage();\r\n            })\r\n        })\r\n    }\r\n\r\n    loadPage(){\r\n        this.items = [{ label: this.tranService.translate(\"global.menu.report\")},\r\n                        { label: this.tranService.translate(\"global.menu.dynamicreport\"),routerLink: '/reports/report-dynamic'},\r\n                        { label: this.tranService.translate(\"permission.RptContent.RptContent\"),routerLink: '/reports/report-dynamic/report-content'},\r\n                        { label: this.reportDetail.name}\r\n                    ];\r\n        this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n        this.loadListParams();\r\n        this.loadTables();\r\n    }\r\n\r\n    loadListParams(){\r\n        let me = this;\r\n        if(this.reportDetail.filterParams){\r\n            this.listParameters = JSON.parse(this.reportDetail.filterParams);\r\n            this.listParameters.forEach(el => {\r\n                if(el.prType == this.paramTypes.LIST_NUMBER || el.prType == this.paramTypes.LIST_STRING || (el.prType == this.paramTypes.STRING && el.isAutoComplete == true)){\r\n                    el[\"control\"] = new ComboLazyControl();\r\n                }\r\n            })\r\n            this.searchInfo = {};\r\n            this.listParameters.forEach(el => {\r\n                me.searchInfo[el.prKey] = null;\r\n            })\r\n            this.formSearch = this.formBuilder.group(this.searchInfo);\r\n        }\r\n    }\r\n\r\n    loadTables(){\r\n        let me = this;\r\n        if(this.reportDetail.reportContents){\r\n            this.reportDetail.reportContents.forEach(el => {\r\n                me.menuTable.push({\r\n                    id: el.id,\r\n                    label: el.tableName,\r\n                    command: ()=>{\r\n                        me.activeTable = el.id;\r\n                    }\r\n                })\r\n                let columnKeys = el.columnQueryResult.split(\",\");\r\n                let columnDisplays = el.columnDisplay.split(\",\");\r\n                let columns = [];\r\n                columnKeys.forEach((el, index)=>{\r\n                    columns.push({\r\n                        key: el,\r\n                        name: columnDisplays[index],\r\n                    })\r\n                })\r\n                me.mapTable[el.id] = {\r\n                    selectItems: [],\r\n                    columns: columns.map(function(el): ColumnInfo {\r\n                        let object: ColumnInfo = {\r\n                            key: el.key,\r\n                            name: el.name,\r\n                            align: \"left\",\r\n                            isShow: true,\r\n                            isSort: false,\r\n                            size: `calc((100% - 100px) / ${columns.length})`,\r\n                        }\r\n                        return object;\r\n                    }),\r\n                    dataSet: {\r\n                        content: [],\r\n                        total: 0\r\n                    },\r\n                    dataOrigin: [],\r\n                    loadData(page, size, sort, params) {\r\n                        me.loadDataTable(el.id, page, size, sort, params);\r\n                    },\r\n                    optionTable: {\r\n                        hasClearSelected: true,\r\n                        action: null,\r\n                        hasShowChoose: false,\r\n                        hasShowIndex: false,\r\n                        hasShowJumpPage: true,\r\n                        hasShowToggleColumn: false,\r\n                        paginator: true\r\n                    },\r\n                    pageNumber: 0,\r\n                    pageSize: 10,\r\n                    params: {},\r\n                    sort: `${columns[0].key},asc`\r\n                }\r\n            })\r\n            this.defaultTableActive = this.menuTable[0];\r\n            this.activeTable = this.defaultTableActive.id;\r\n            this.tables = [...this.reportDetail.reportContents];\r\n        }\r\n    }\r\n\r\n    prepareData(){\r\n        let data = {\r\n            id: this.reportDetail.id,\r\n            customerCodes: null,\r\n            paramsValue: []\r\n        };\r\n        this.listParameters.forEach(el => {\r\n            if(el.prType == CONSTANTS.PARAMETER_TYPE.DATE){\r\n                data.paramsValue.push({\r\n                    prKey: el.prKey,\r\n                    value: this.searchInfo[el.prKey] ? this.utilService.convertDateTimeToString(this.searchInfo[el.prKey]) : null\r\n                })\r\n            }else if(el.prType == CONSTANTS.PARAMETER_TYPE.TIMESTAMP){\r\n                data.paramsValue.push({\r\n                    prKey: el.prKey,\r\n                    value: this.searchInfo[el.prKey] ? this.searchInfo[el.prKey].getTime() : null\r\n                })\r\n            }else if(el.prType == CONSTANTS.PARAMETER_TYPE.NUMBER){\r\n                data.paramsValue.push({\r\n                    prKey: el.prKey,\r\n                    value: this.searchInfo[el.prKey] ? this.searchInfo[el.prKey] * 1 : null\r\n                })\r\n            }else if(el.prType == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || el.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING){\r\n                if(el.isMultiChoice){\r\n                    if(this.searchInfo[el.prKey]==null||this.searchInfo[el.prKey].length == 0){\r\n                        data.paramsValue.push({\r\n                            prKey: el.prKey,\r\n                            value: null\r\n                        })\r\n                    }else{\r\n                        data.paramsValue.push({\r\n                            prKey: el.prKey,\r\n                            value: this.searchInfo[el.prKey] ? this.searchInfo[el.prKey] : null\r\n                        })\r\n                    }\r\n                }else{\r\n                    data.paramsValue.push({\r\n                        prKey: el.prKey,\r\n                        value: this.searchInfo[el.prKey] ? [this.searchInfo[el.prKey]] : null\r\n                    })\r\n                }\r\n            }else{\r\n                data.paramsValue.push({\r\n                    prKey: el.prKey,\r\n                    value: this.searchInfo[el.prKey] ? this.searchInfo[el.prKey] : null\r\n                })\r\n            }\r\n        })\r\n        return data;\r\n    }\r\n\r\n    getParamDefault(param): { [key: string]: string } {\r\n        const parsed = this.parseKeyValuePairs(param.queryParam);\r\n\r\n        // Khởi tạo đối tượng trống để lưu các cặp key-value\r\n        const result: { [key: string]: string } = {};\r\n\r\n        if (param.isAutoComplete) {\r\n            // Sử dụng vòng lặp để thêm nhiều cặp key-value vào đối tượng\r\n            for (let i = 0; i < parsed.keys.length; i++) {\r\n                result[parsed.keys[i]] = parsed.values[i] || null;\r\n            }\r\n        }\r\n\r\n        return result;\r\n    }\r\n\r\n    parseKeyValuePairs(input: string): { keys: string[], values: string[] } {\r\n        // Khởi tạo đối tượng để lưu các cặp key-value\r\n        const keyValueMap: { [key: string]: string[] } = {};\r\n\r\n        // Tách chuỗi thành các cặp key-value\r\n        const pairs = input.split('&');\r\n\r\n        // Xử lý từng cặp\r\n        for (const pair of pairs) {\r\n            const [key, value] = pair.split('=');\r\n\r\n            // Xử lý giá trị bắt đầu bằng $\r\n            let actualValue: string;\r\n            if (value.startsWith('$')) {\r\n                actualValue = this.getValueData(value.substring(1)); // Loại bỏ dấu $ và gọi hàm\r\n            } else {\r\n                // Xử lý giá trị không bắt đầu bằng $\r\n                actualValue = value.replace(/^\"|\"$/g, ''); // Loại bỏ dấu ngoặc kép ở đầu và cuối nếu có\r\n            }\r\n\r\n            // Thêm giá trị vào đối tượng keyValueMap\r\n            if (key in keyValueMap) {\r\n                keyValueMap[key].push(actualValue);\r\n            } else {\r\n                keyValueMap[key] = [actualValue];\r\n            }\r\n        }\r\n\r\n        // Tạo mảng keys và values từ đối tượng keyValueMap\r\n        const keys: string[] = [];\r\n        const values: string[] = [];\r\n\r\n        for (const key in keyValueMap) {\r\n            keys.push(key);\r\n            values.push(keyValueMap[key].join(',')); // Nối các giá trị với dấu phẩy\r\n        }\r\n\r\n        // console.log(keys, values);\r\n\r\n        // Trả về cả mảng keys và values\r\n        return { keys, values };\r\n    }\r\n\r\n    getValueData(key){\r\n        let returnValue\r\n        if(this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.DATE){\r\n            returnValue = this.searchInfo[key] ? this.utilService.convertDateTimeToString(this.searchInfo[key]) : null\r\n        }else if(this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.TIMESTAMP){\r\n            returnValue = this.searchInfo[key] ? this.searchInfo[key].getTime() : null\r\n        }else if(this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.NUMBER){\r\n            returnValue = this.searchInfo[key] ? this.searchInfo[key] * 1 : null\r\n        }else if(this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || this.getTypeKey(key) == CONSTANTS.PARAMETER_TYPE.LIST_STRING){\r\n            returnValue = this.searchInfo[key] ? this.searchInfo[key] : null\r\n        }else{\r\n            returnValue = this.searchInfo[key] ? this.searchInfo[key] : null\r\n        }\r\n        return returnValue\r\n    }\r\n\r\n    getTypeKey(key){\r\n        const value = this.listParameters.find(data => data.prKey === key)\r\n        return value ? value.prType :null\r\n    }\r\n\r\n    onSubmitSearch(){\r\n        if(!this.reportDetail.enablePreview) return;\r\n        if(!this.checkValidForm()) return;\r\n        let me = this;\r\n        let data = this.prepareData();\r\n        this.messageCommonService.onload();\r\n        this.reportService.preview(data, (response)=>{\r\n            if(response.reportContents){\r\n                response.reportContents.forEach(tableContent => {\r\n                    me.mapTable[tableContent.reportContentID].dataOrigin = tableContent.data.filter(el => Object.keys(el).length > 0);\r\n                    me.loadDataTable(tableContent.reportContentID, 0, me.mapTable[tableContent.reportContentID].pageSize, me.mapTable[tableContent.reportContentID].sort, {})\r\n                })\r\n            }\r\n            // console.log(me.mapTable)\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    loadDataTable(tableId, page, size, sort, param){\r\n        this.mapTable[tableId].pageNumber = page;\r\n        this.mapTable[tableId].pageSize = size;\r\n        this.mapTable[tableId].sort = sort;\r\n        this.mapTable[tableId].params = param;\r\n        let dataSet = {\r\n            content: this.mapTable[tableId].dataOrigin.slice(page * size, page*size + size),\r\n            total: this.mapTable[tableId].dataOrigin.length\r\n        }\r\n        this.mapTable[tableId].dataSet = dataSet;\r\n    }\r\n\r\n    export(){\r\n        if(!this.checkValidForm()) return;\r\n        let data = this.prepareData();\r\n        this.messageCommonService.onload();\r\n        this.reportService.exportFile(data,()=>{}, (error)=>{\r\n            console.log(error);\r\n            let me = this;\r\n            if(error.error instanceof Blob){\r\n                error.error.text().then(function(resultText){\r\n                    let bodyError = JSON.parse(resultText).error;\r\n                    if(bodyError.errorCode == \"error.report.excel.limit\") {\r\n                        me.messageCommonService.error(me.tranService.translate(\"report.text.errorExportLimit\"))\r\n                    }else{\r\n                        me.messageCommonService.error(me.tranService.translate(\"global.message.error\"))\r\n                    }\r\n                })\r\n            }else{\r\n                console.log(error)\r\n            }\r\n        });\r\n    }\r\n\r\n    checkValidForm(){\r\n        if(!this.formSearch) return false;\r\n        if(this.formSearch.invalid) return false;\r\n        if((this.listParameters || []).length > 0){\r\n            for(let i = 0; i < this.listParameters.length;i++){\r\n                let el = this.listParameters[i];\r\n                if(el[\"control\"]){\r\n                    if(el[\"control\"].invalid){\r\n                        return false;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    preventCharacter(event){\r\n        if(event.ctrlKey || event.altKey || event.shiftKey){\r\n            return;\r\n        }\r\n        if(event.keyCode == 8 || event.keyCode == 13 || event.keyCode == 46 || event.keyCode == 37 || event.keyCode == 39){\r\n            return;\r\n        }\r\n        if(event.keyCode < 48 || event.keyCode > 57){\r\n            event.preventDefault();\r\n        }\r\n    }\r\n\r\n    checkIsNumberOrNull(key){\r\n        let me = this;\r\n        if(this.searchInfo[key] == null) return;\r\n        if(isNaN(this.searchInfo[key])){\r\n            setTimeout(function(){\r\n                me.searchInfo[key] = null;\r\n            })\r\n        }\r\n    }\r\n}\r\n", "<div *ngIf=\"reportDetail\">\r\n    <div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n        <div class=\"\">\r\n            <div class=\"text-xl font-bold mb-1\">{{tranService.translate(this.reportDetail.name)}}</div>\r\n            <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n        </div>\r\n        <div class=\"col-3 flex flex-row justify-content-end align-items-center report-dynamic-button-div\">\r\n            <p-button *ngIf=\"reportDetail.enablePreview\" [disabled]=\"!checkValidForm()\" styleClass=\"p-button-secondary mr-2\" [label]=\"tranService.translate('global.button.preview')\" icon=\"pi pi-search\" (onClick)=\"onSubmitSearch()\"></p-button>\r\n            <p-button [disabled]=\"!checkValidForm()\" styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.export')\" icon=\"pi pi-download\" (onClick)=\"export()\"></p-button>\r\n        </div>\r\n    </div>\r\n\r\n    <form *ngIf=\"formSearch\" [formGroup]=\"formSearch\" (ngSubmit)=\"onSubmitSearch()\" class=\"pb-2 pt-3 vnpt-field-set\">\r\n        <p-panel [toggleable]=\"true\" [header]=\"tranService.translate('global.text.filter')\">\r\n            <div class=\"grid\">\r\n                <div class=\"col-3\" *ngFor=\"let param of listParameters\">\r\n                    <span class=\"p-float-label relative\"\r\n                          [class]=\"formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required ? 'report-param-required-error' : ''\"\r\n                          *ngIf=\"param.prType == paramTypes.NUMBER\"\r\n                    >\r\n                        <input class=\"w-full\" pInputText (keydown)=\"preventCharacter($event)\" [(ngModel)]=\"searchInfo[param.prKey]\" [formControlName]=\"param.prKey\" [required]=\"param.required\" (ngModelChange)=\"checkIsNumberOrNull(param.prKey)\"/>\r\n                        <!-- <p-inputNumber [useGrouping]=\"false\" mode=\"decimal\" class=\"w-full\" [(ngModel)]=\"searchInfo[param.prKey]\" [required]=\"param.required\" [formControlName]=\"param.prKey\"></p-inputNumber> -->\r\n                        <label [htmlFor]=\"param.prKey\">{{param.prDisplayName}}<span *ngIf=\"param.required\" class=\"text-red-500\">*</span></label>\r\n                        <small class=\"text-red-500\" *ngIf=\"formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required\">\r\n                            {{tranService.translate(\"global.message.required\")}}\r\n                        </small>\r\n                    </span>\r\n                    <span\r\n                        class=\"p-float-label relative\"\r\n                        *ngIf=\"param.prType == paramTypes.STRING && param.isAutoComplete == false\"\r\n                        [class]=\"formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required ? 'report-param-required-error' : ''\"\r\n                    >\r\n                        <input class=\"w-full\" pInputText  [(ngModel)]=\"searchInfo[param.prKey]\" [formControlName]=\"param.prKey\" [required]=\"param.required\"/>\r\n                        <label [htmlFor]=\"param.prKey\">{{param.prDisplayName}}<span *ngIf=\"param.required\" class=\"text-red-500\">*</span></label>\r\n                        <small class=\"text-red-500\" *ngIf=\"formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required\">\r\n                            {{tranService.translate(\"global.message.required\")}}\r\n                        </small>\r\n                    </span>\r\n                    <span\r\n                        class=\"p-float-label relative\"\r\n                        *ngIf=\"param.prType == paramTypes.DATE\"\r\n                        [class]=\"formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required ? 'report-param-required-error' : ''\"\r\n                    >\r\n                        <p-calendar styleClass=\"w-full\"\r\n                                    [(ngModel)]=\"searchInfo[param.prKey]\"\r\n                                    [showClear]=\"true\"\r\n                                    [showIcon]=\"true\"\r\n                                    [dateFormat]=\"param.dateType == dateTypes.MONTH ? 'mm/yy' : 'dd/mm/yy'\"\r\n                                    [hourFormat]=\"param.dateType == dateTypes.DATETIME ? 'hh:mm:ss' : ''\"\r\n                                    [view]=\"param.dateType == dateTypes.MONTH ? 'month' : 'date'\"\r\n                                    [showTime]=\"param.dateType == dateTypes.DATETIME\" [showSeconds]=\"param.dateType == dateTypes.DATETIME\"\r\n                                    [showClear]=\"true\" [showIcon]=\"true\" appendTo=\"body\"\r\n                                    [formControlName]=\"param.prKey\"\r\n                                    [placeholder]=\"tranService.translate('global.text.inputDate')\"\r\n                                    [required]=\"param.required\"\r\n                        ></p-calendar>\r\n                        <label [htmlFor]=\"param.prKey\">{{param.prDisplayName}}<span *ngIf=\"param.required\" class=\"text-red-500\">*</span></label>\r\n                        <small class=\"text-red-500\" *ngIf=\"formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required\">\r\n                            {{tranService.translate(\"global.message.required\")}}\r\n                        </small>\r\n                    </span>\r\n                    <span\r\n                        class=\"p-float-label relative\"\r\n                        *ngIf=\"param.prType == paramTypes.TIMESTAMP\"\r\n                        [class]=\"formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required ? 'report-param-required-error' : ''\"\r\n                    >\r\n                        <p-calendar styleClass=\"w-full\"\r\n                                    [(ngModel)]=\"searchInfo[param.prKey]\"\r\n                                    [showClear]=\"true\"\r\n                                    [showIcon]=\"true\"\r\n                                    dateFormat=\"dd/mm/yy\"\r\n                                    hourFormat=\"hh:mm:ss\"\r\n                                    [showTime]=\"true\" [showSeconds]=\"true\"\r\n                                    [showClear]=\"true\" [showIcon]=\"true\" appendTo=\"body\"\r\n                                    [formControlName]=\"param.prKey\"\r\n                                    [placeholder]=\"tranService.translate('global.text.inputDate')\"\r\n                                    [required]=\"param.required\"\r\n                        ></p-calendar>\r\n                        <label [htmlFor]=\"param.prKey\">{{param.prDisplayName}}<span *ngIf=\"param.required\" class=\"text-red-500\">*</span></label>\r\n                        <small class=\"text-red-500\" *ngIf=\"formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required\">\r\n                            {{tranService.translate(\"global.message.required\")}}\r\n                        </small>\r\n                    </span>\r\n                    <div class=\"relative\" *ngIf=\"param.prType == paramTypes.LIST_NUMBER || param.prType == paramTypes.LIST_STRING || (param.prType == paramTypes.STRING && param.isAutoComplete == true)\" >\r\n                        <vnpt-select\r\n                            [control]=\"param.control\"\r\n                            class=\"w-full\"\r\n                            [(value)]=\"searchInfo[param.prKey]\"\r\n                            [placeholder]=\"param.prDisplayName\"\r\n                            [isAutoComplete]=\"param.prType == paramTypes.STRING\"\r\n                            [isMultiChoice]=\"param.isMultiChoice\"\r\n                            [options]=\"param.valueList\"\r\n                            [objectKey]=\"param.queryInfo.objectKey\"\r\n                            [paramKey]=\"(param.isAutoComplete ? param.queryInfo.input : 'display')\"\r\n                            [keyReturn]=\"(param.isAutoComplete ? param.queryInfo.output : 'value')\"\r\n                            [displayPattern]=\"(param.isAutoComplete ? param.queryInfo.displayPattern : '${display}')\"\r\n                            [lazyLoad]=\"param.isAutoComplete\"\r\n                            [paramDefault]=\"((param.isAutoComplete && param.queryParam) ? getParamDefault(param) : {})\"\r\n                            typeValue=\"primitive\"\r\n                            [floatLabel]=\"true\"\r\n                            [required]=\"param.required\"\r\n                            [showTextRequired]=\"param.required\"\r\n                        ></vnpt-select>\r\n                        <small class=\"text-red-500\" *ngIf=\"param.control.dirty && param.control.error.required\">\r\n                            {{tranService.translate(\"global.message.required\")}}\r\n                        </small>\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-3 pb-0\">\r\n                    <!-- <p-button icon=\"pi pi-search\"\r\n                                styleClass=\"p-button-rounded p-button-secondary p-button-text button-search\"\r\n                                type=\"submit\"\r\n                    ></p-button> -->\r\n                </div>\r\n            </div>\r\n        </p-panel>\r\n    </form>\r\n\r\n    <div class=\"w-full custom-tabMenu\" style=\"padding: 2px;margin-top: 12px;\">\r\n        <p-tabMenu [model]=\"menuTable\" [activeItem]=\"defaultTableActive\"></p-tabMenu>\r\n    </div>\r\n    <div class=\"w-full\" style=\"padding: 2px;\" *ngFor=\"let table of tables\">\r\n        <table-vnpt *ngIf=\"activeTable == table.id\"\r\n            [fieldId]=\"'id'\"\r\n            [(selectItems)]=\"mapTable[table.id].selectItems\"\r\n            [columns]=\"mapTable[table.id].columns\"\r\n            [dataSet]=\"mapTable[table.id].dataSet\"\r\n            [options]=\"mapTable[table.id].optionTable\"\r\n            [loadData]=\"mapTable[table.id].loadData.bind(this)\"\r\n            [pageNumber]=\"mapTable[table.id].pageNumber\"\r\n            [pageSize]=\"mapTable[table.id].pageSize\"\r\n            [sort]=\"mapTable[table.id].sort\"\r\n            [params]=\"mapTable[table.id].searchInfo\"\r\n            [labelTable]=\"table.tableName\"\r\n        ></table-vnpt>\r\n    </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,aAAa,QAAQ,wBAAwB;AAItD,SAASC,SAAS,QAAQ,iCAAiC;AAG3D,SAASC,gBAAgB,QAAQ,oEAAoE;;;;;;;;;;;;;;;;ICFzFC,EAAA,CAAAC,cAAA,mBAA2N;IAA7BD,EAAA,CAAAE,UAAA,qBAAAC,oFAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAWP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,cAAA,EAAgB;IAAA,EAAC;IAACT,EAAA,CAAAU,YAAA,EAAW;;;;IAAzLV,EAAA,CAAAW,UAAA,cAAAC,MAAA,CAAAC,cAAA,GAA8B,UAAAD,MAAA,CAAAE,WAAA,CAAAC,SAAA;;;;;IAeTf,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAgB,MAAA,QAAC;IAAAhB,EAAA,CAAAU,YAAA,EAAO;;;;;IAChHV,EAAA,CAAAC,cAAA,gBAAgI;IAC5HD,EAAA,CAAAgB,MAAA,GACJ;IAAAhB,EAAA,CAAAU,YAAA,EAAQ;;;;IADJV,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAkB,kBAAA,MAAAC,OAAA,CAAAL,WAAA,CAAAC,SAAA,iCACJ;;;;;;IATJf,EAAA,CAAAC,cAAA,eAGC;IACoCD,EAAA,CAAAE,UAAA,qBAAAkB,0FAAAC,MAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkB,IAAA;MAAA,MAAAC,OAAA,GAAAvB,EAAA,CAAAO,aAAA;MAAA,OAAWP,EAAA,CAAAQ,WAAA,CAAAe,OAAA,CAAAC,gBAAA,CAAAH,MAAA,CAAwB;IAAA,EAAC,2BAAAI,gGAAAJ,MAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkB,IAAA;MAAA,MAAAI,QAAA,GAAA1B,EAAA,CAAAO,aAAA,GAAAoB,SAAA;MAAA,MAAAC,OAAA,GAAA5B,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAoB,OAAA,CAAAC,UAAA,CAAAH,QAAA,CAAAI,KAAA,IAAAT,MAAA;IAAA,6BAAAI,gGAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAkB,IAAA;MAAA,MAAAI,QAAA,GAAA1B,EAAA,CAAAO,aAAA,GAAAoB,SAAA;MAAA,MAAAI,OAAA,GAAA/B,EAAA,CAAAO,aAAA;MAAA,OAAoHP,EAAA,CAAAQ,WAAA,CAAAuB,OAAA,CAAAC,mBAAA,CAAAN,QAAA,CAAAI,KAAA,CAAgC;IAAA,EAApJ;IAArE9B,EAAA,CAAAU,YAAA,EAA4N;IAE5NV,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAgB,MAAA,GAAuB;IAAAhB,EAAA,CAAAiC,UAAA,IAAAC,uEAAA,mBAA0D;IAAAlC,EAAA,CAAAU,YAAA,EAAQ;IACxHV,EAAA,CAAAiC,UAAA,IAAAE,wEAAA,oBAEQ;IACZnC,EAAA,CAAAU,YAAA,EAAO;;;;;IATDV,EAAA,CAAAoC,UAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAU,KAAA,KAAAH,MAAA,CAAAC,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAW,MAAA,kBAAAJ,MAAA,CAAAC,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAW,MAAA,CAAAC,QAAA,uCAA0I;IAGtE1C,EAAA,CAAAiB,SAAA,GAAqC;IAArCjB,EAAA,CAAAW,UAAA,YAAA0B,MAAA,CAAAR,UAAA,CAAAH,QAAA,CAAAI,KAAA,EAAqC,oBAAAJ,QAAA,CAAAI,KAAA,cAAAJ,QAAA,CAAAgB,QAAA;IAEpG1C,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAAW,UAAA,YAAAe,QAAA,CAAAI,KAAA,CAAuB;IAAC9B,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAA2C,iBAAA,CAAAjB,QAAA,CAAAkB,aAAA,CAAuB;IAAO5C,EAAA,CAAAiB,SAAA,GAAoB;IAApBjB,EAAA,CAAAW,UAAA,SAAAe,QAAA,CAAAgB,QAAA,CAAoB;IACpD1C,EAAA,CAAAiB,SAAA,GAAiG;IAAjGjB,EAAA,CAAAW,UAAA,SAAA0B,MAAA,CAAAC,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAU,KAAA,KAAAH,MAAA,CAAAC,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAW,MAAA,kBAAAJ,MAAA,CAAAC,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAW,MAAA,CAAAC,QAAA,EAAiG;;;;;IAUxE1C,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAgB,MAAA,QAAC;IAAAhB,EAAA,CAAAU,YAAA,EAAO;;;;;IAChHV,EAAA,CAAAC,cAAA,gBAAgI;IAC5HD,EAAA,CAAAgB,MAAA,GACJ;IAAAhB,EAAA,CAAAU,YAAA,EAAQ;;;;IADJV,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAkB,kBAAA,MAAA2B,OAAA,CAAA/B,WAAA,CAAAC,SAAA,iCACJ;;;;;;IATJf,EAAA,CAAAC,cAAA,eAIC;IACqCD,EAAA,CAAAE,UAAA,2BAAA4C,gGAAAzB,MAAA;MAAArB,EAAA,CAAAI,aAAA,CAAA2C,IAAA;MAAA,MAAArB,QAAA,GAAA1B,EAAA,CAAAO,aAAA,GAAAoB,SAAA;MAAA,MAAAqB,OAAA,GAAAhD,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAwC,OAAA,CAAAnB,UAAA,CAAAH,QAAA,CAAAI,KAAA,IAAAT,MAAA;IAAA,EAAqC;IAAvErB,EAAA,CAAAU,YAAA,EAAqI;IACrIV,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAgB,MAAA,GAAuB;IAAAhB,EAAA,CAAAiC,UAAA,IAAAgB,uEAAA,mBAA0D;IAAAjD,EAAA,CAAAU,YAAA,EAAQ;IACxHV,EAAA,CAAAiC,UAAA,IAAAiB,wEAAA,oBAEQ;IACZlD,EAAA,CAAAU,YAAA,EAAO;;;;;IAPHV,EAAA,CAAAoC,UAAA,CAAAe,MAAA,CAAAb,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAU,KAAA,KAAAW,MAAA,CAAAb,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAW,MAAA,kBAAAU,MAAA,CAAAb,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAW,MAAA,CAAAC,QAAA,uCAA0I;IAExG1C,EAAA,CAAAiB,SAAA,GAAqC;IAArCjB,EAAA,CAAAW,UAAA,YAAAwC,MAAA,CAAAtB,UAAA,CAAAH,QAAA,CAAAI,KAAA,EAAqC,oBAAAJ,QAAA,CAAAI,KAAA,cAAAJ,QAAA,CAAAgB,QAAA;IAChE1C,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAAW,UAAA,YAAAe,QAAA,CAAAI,KAAA,CAAuB;IAAC9B,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAA2C,iBAAA,CAAAjB,QAAA,CAAAkB,aAAA,CAAuB;IAAO5C,EAAA,CAAAiB,SAAA,GAAoB;IAApBjB,EAAA,CAAAW,UAAA,SAAAe,QAAA,CAAAgB,QAAA,CAAoB;IACpD1C,EAAA,CAAAiB,SAAA,GAAiG;IAAjGjB,EAAA,CAAAW,UAAA,SAAAwC,MAAA,CAAAb,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAU,KAAA,KAAAW,MAAA,CAAAb,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAW,MAAA,kBAAAU,MAAA,CAAAb,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAW,MAAA,CAAAC,QAAA,EAAiG;;;;;IAsBxE1C,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAgB,MAAA,QAAC;IAAAhB,EAAA,CAAAU,YAAA,EAAO;;;;;IAChHV,EAAA,CAAAC,cAAA,gBAAgI;IAC5HD,EAAA,CAAAgB,MAAA,GACJ;IAAAhB,EAAA,CAAAU,YAAA,EAAQ;;;;IADJV,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAkB,kBAAA,MAAAkC,OAAA,CAAAtC,WAAA,CAAAC,SAAA,iCACJ;;;;;;IArBJf,EAAA,CAAAC,cAAA,eAIC;IAEeD,EAAA,CAAAE,UAAA,2BAAAmD,qGAAAhC,MAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAkD,IAAA;MAAA,MAAA5B,QAAA,GAAA1B,EAAA,CAAAO,aAAA,GAAAoB,SAAA;MAAA,MAAA4B,OAAA,GAAAvD,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAA+C,OAAA,CAAA1B,UAAA,CAAAH,QAAA,CAAAI,KAAA,IAAAT,MAAA;IAAA,EAAqC;IAWhDrB,EAAA,CAAAU,YAAA,EAAa;IACdV,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAgB,MAAA,GAAuB;IAAAhB,EAAA,CAAAiC,UAAA,IAAAuB,uEAAA,mBAA0D;IAAAxD,EAAA,CAAAU,YAAA,EAAQ;IACxHV,EAAA,CAAAiC,UAAA,IAAAwB,wEAAA,oBAEQ;IACZzD,EAAA,CAAAU,YAAA,EAAO;;;;;IAnBHV,EAAA,CAAAoC,UAAA,CAAAsB,OAAA,CAAApB,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAU,KAAA,KAAAkB,OAAA,CAAApB,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAW,MAAA,kBAAAiB,OAAA,CAAApB,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAW,MAAA,CAAAC,QAAA,uCAA0I;IAG9H1C,EAAA,CAAAiB,SAAA,GAAqC;IAArCjB,EAAA,CAAAW,UAAA,YAAA+C,OAAA,CAAA7B,UAAA,CAAAH,QAAA,CAAAI,KAAA,EAAqC,oDAAAJ,QAAA,CAAAiC,QAAA,IAAAD,OAAA,CAAAE,SAAA,CAAAC,KAAA,uCAAAnC,QAAA,CAAAiC,QAAA,IAAAD,OAAA,CAAAE,SAAA,CAAAE,QAAA,4BAAApC,QAAA,CAAAiC,QAAA,IAAAD,OAAA,CAAAE,SAAA,CAAAC,KAAA,iCAAAnC,QAAA,CAAAiC,QAAA,IAAAD,OAAA,CAAAE,SAAA,CAAAE,QAAA,iBAAApC,QAAA,CAAAiC,QAAA,IAAAD,OAAA,CAAAE,SAAA,CAAAE,QAAA,0DAAApC,QAAA,CAAAI,KAAA,iBAAA4B,OAAA,CAAA5C,WAAA,CAAAC,SAAA,uCAAAW,QAAA,CAAAgB,QAAA;IAY1C1C,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAAW,UAAA,YAAAe,QAAA,CAAAI,KAAA,CAAuB;IAAC9B,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAA2C,iBAAA,CAAAjB,QAAA,CAAAkB,aAAA,CAAuB;IAAO5C,EAAA,CAAAiB,SAAA,GAAoB;IAApBjB,EAAA,CAAAW,UAAA,SAAAe,QAAA,CAAAgB,QAAA,CAAoB;IACpD1C,EAAA,CAAAiB,SAAA,GAAiG;IAAjGjB,EAAA,CAAAW,UAAA,SAAA+C,OAAA,CAAApB,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAU,KAAA,KAAAkB,OAAA,CAAApB,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAW,MAAA,kBAAAiB,OAAA,CAAApB,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAW,MAAA,CAAAC,QAAA,EAAiG;;;;;IAqBxE1C,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAgB,MAAA,QAAC;IAAAhB,EAAA,CAAAU,YAAA,EAAO;;;;;IAChHV,EAAA,CAAAC,cAAA,gBAAgI;IAC5HD,EAAA,CAAAgB,MAAA,GACJ;IAAAhB,EAAA,CAAAU,YAAA,EAAQ;;;;IADJV,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAkB,kBAAA,MAAA6C,OAAA,CAAAjD,WAAA,CAAAC,SAAA,iCACJ;;;;;;IApBJf,EAAA,CAAAC,cAAA,eAIC;IAEeD,EAAA,CAAAE,UAAA,2BAAA8D,qGAAA3C,MAAA;MAAArB,EAAA,CAAAI,aAAA,CAAA6D,IAAA;MAAA,MAAAvC,QAAA,GAAA1B,EAAA,CAAAO,aAAA,GAAAoB,SAAA;MAAA,MAAAuC,OAAA,GAAAlE,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAA0D,OAAA,CAAArC,UAAA,CAAAH,QAAA,CAAAI,KAAA,IAAAT,MAAA;IAAA,EAAqC;IAUhDrB,EAAA,CAAAU,YAAA,EAAa;IACdV,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAgB,MAAA,GAAuB;IAAAhB,EAAA,CAAAiC,UAAA,IAAAkC,uEAAA,mBAA0D;IAAAnE,EAAA,CAAAU,YAAA,EAAQ;IACxHV,EAAA,CAAAiC,UAAA,IAAAmC,wEAAA,oBAEQ;IACZpE,EAAA,CAAAU,YAAA,EAAO;;;;;IAlBHV,EAAA,CAAAoC,UAAA,CAAAiC,OAAA,CAAA/B,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAU,KAAA,KAAA6B,OAAA,CAAA/B,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAW,MAAA,kBAAA4B,OAAA,CAAA/B,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAW,MAAA,CAAAC,QAAA,uCAA0I;IAG9H1C,EAAA,CAAAiB,SAAA,GAAqC;IAArCjB,EAAA,CAAAW,UAAA,YAAA0D,OAAA,CAAAxC,UAAA,CAAAH,QAAA,CAAAI,KAAA,EAAqC,qIAAAJ,QAAA,CAAAI,KAAA,iBAAAuC,OAAA,CAAAvD,WAAA,CAAAC,SAAA,uCAAAW,QAAA,CAAAgB,QAAA;IAW1C1C,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAAW,UAAA,YAAAe,QAAA,CAAAI,KAAA,CAAuB;IAAC9B,EAAA,CAAAiB,SAAA,GAAuB;IAAvBjB,EAAA,CAAA2C,iBAAA,CAAAjB,QAAA,CAAAkB,aAAA,CAAuB;IAAO5C,EAAA,CAAAiB,SAAA,GAAoB;IAApBjB,EAAA,CAAAW,UAAA,SAAAe,QAAA,CAAAgB,QAAA,CAAoB;IACpD1C,EAAA,CAAAiB,SAAA,GAAiG;IAAjGjB,EAAA,CAAAW,UAAA,SAAA0D,OAAA,CAAA/B,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAU,KAAA,KAAA6B,OAAA,CAAA/B,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAW,MAAA,kBAAA4B,OAAA,CAAA/B,UAAA,CAAAC,QAAA,CAAAb,QAAA,CAAAI,KAAA,EAAAW,MAAA,CAAAC,QAAA,EAAiG;;;;;IAwB9H1C,EAAA,CAAAC,cAAA,gBAAwF;IACpFD,EAAA,CAAAgB,MAAA,GACJ;IAAAhB,EAAA,CAAAU,YAAA,EAAQ;;;;IADJV,EAAA,CAAAiB,SAAA,GACJ;IADIjB,EAAA,CAAAkB,kBAAA,MAAAoD,OAAA,CAAAxD,WAAA,CAAAC,SAAA,iCACJ;;;;;;;;;IAtBJf,EAAA,CAAAC,cAAA,cAAuL;IAI/KD,EAAA,CAAAE,UAAA,yBAAAqE,mGAAAlD,MAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAoE,IAAA;MAAA,MAAA9C,QAAA,GAAA1B,EAAA,CAAAO,aAAA,GAAAoB,SAAA;MAAA,MAAA8C,OAAA,GAAAzE,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAiE,OAAA,CAAA5C,UAAA,CAAAH,QAAA,CAAAI,KAAA,IAAAT,MAAA;IAAA,EAAmC;IAetCrB,EAAA,CAAAU,YAAA,EAAc;IACfV,EAAA,CAAAiC,UAAA,IAAAyC,uEAAA,oBAEQ;IACZ1E,EAAA,CAAAU,YAAA,EAAM;;;;;IArBEV,EAAA,CAAAiB,SAAA,GAAyB;IAAzBjB,EAAA,CAAAW,UAAA,YAAAe,QAAA,CAAAiD,OAAA,CAAyB,UAAAC,OAAA,CAAA/C,UAAA,CAAAH,QAAA,CAAAI,KAAA,kBAAAJ,QAAA,CAAAkB,aAAA,oBAAAlB,QAAA,CAAAmD,MAAA,IAAAD,OAAA,CAAAE,UAAA,CAAAC,MAAA,mBAAArD,QAAA,CAAAsD,aAAA,aAAAtD,QAAA,CAAAuD,SAAA,eAAAvD,QAAA,CAAAwD,SAAA,CAAAC,SAAA,cAAAzD,QAAA,CAAA0D,cAAA,GAAA1D,QAAA,CAAAwD,SAAA,CAAAG,KAAA,2BAAA3D,QAAA,CAAA0D,cAAA,GAAA1D,QAAA,CAAAwD,SAAA,CAAAI,MAAA,8BAAA5D,QAAA,CAAA0D,cAAA,GAAA1D,QAAA,CAAAwD,SAAA,CAAAK,cAAA,6BAAA7D,QAAA,CAAA0D,cAAA,kBAAA1D,QAAA,CAAA0D,cAAA,IAAA1D,QAAA,CAAA8D,UAAA,GAAAZ,OAAA,CAAAa,eAAA,CAAA/D,QAAA,IAAA1B,EAAA,CAAA0F,eAAA,KAAAC,GAAA,mCAAAjE,QAAA,CAAAgB,QAAA,sBAAAhB,QAAA,CAAAgB,QAAA;IAkBA1C,EAAA,CAAAiB,SAAA,GAAyD;IAAzDjB,EAAA,CAAAW,UAAA,SAAAe,QAAA,CAAAiD,OAAA,CAAAnC,KAAA,IAAAd,QAAA,CAAAiD,OAAA,CAAAiB,KAAA,CAAAlD,QAAA,CAAyD;;;;;IAxF9F1C,EAAA,CAAAC,cAAA,cAAwD;IACpDD,EAAA,CAAAiC,UAAA,IAAA4D,gEAAA,mBAUO;IACP7F,EAAA,CAAAiC,UAAA,IAAA6D,gEAAA,mBAUO;IACP9F,EAAA,CAAAiC,UAAA,IAAA8D,gEAAA,oBAsBO;IACP/F,EAAA,CAAAiC,UAAA,IAAA+D,gEAAA,oBAqBO;IACPhG,EAAA,CAAAiC,UAAA,IAAAgE,+DAAA,mBAuBM;IACVjG,EAAA,CAAAU,YAAA,EAAM;;;;;IAzFKV,EAAA,CAAAiB,SAAA,GAAuC;IAAvCjB,EAAA,CAAAW,UAAA,SAAAe,QAAA,CAAAmD,MAAA,IAAAqB,MAAA,CAAApB,UAAA,CAAAqB,MAAA,CAAuC;IAWzCnG,EAAA,CAAAiB,SAAA,GAAwE;IAAxEjB,EAAA,CAAAW,UAAA,SAAAe,QAAA,CAAAmD,MAAA,IAAAqB,MAAA,CAAApB,UAAA,CAAAC,MAAA,IAAArD,QAAA,CAAA0D,cAAA,UAAwE;IAWxEpF,EAAA,CAAAiB,SAAA,GAAqC;IAArCjB,EAAA,CAAAW,UAAA,SAAAe,QAAA,CAAAmD,MAAA,IAAAqB,MAAA,CAAApB,UAAA,CAAAsB,IAAA,CAAqC;IAuBrCpG,EAAA,CAAAiB,SAAA,GAA0C;IAA1CjB,EAAA,CAAAW,UAAA,SAAAe,QAAA,CAAAmD,MAAA,IAAAqB,MAAA,CAAApB,UAAA,CAAAuB,SAAA,CAA0C;IAoBxBrG,EAAA,CAAAiB,SAAA,GAA6J;IAA7JjB,EAAA,CAAAW,UAAA,SAAAe,QAAA,CAAAmD,MAAA,IAAAqB,MAAA,CAAApB,UAAA,CAAAwB,WAAA,IAAA5E,QAAA,CAAAmD,MAAA,IAAAqB,MAAA,CAAApB,UAAA,CAAAyB,WAAA,IAAA7E,QAAA,CAAAmD,MAAA,IAAAqB,MAAA,CAAApB,UAAA,CAAAC,MAAA,IAAArD,QAAA,CAAA0D,cAAA,SAA6J;;;;;;IAvEpMpF,EAAA,CAAAC,cAAA,eAAiH;IAA/DD,EAAA,CAAAE,UAAA,sBAAAsG,6EAAA;MAAAxG,EAAA,CAAAI,aAAA,CAAAqG,IAAA;MAAA,MAAAC,OAAA,GAAA1G,EAAA,CAAAO,aAAA;MAAA,OAAYP,EAAA,CAAAQ,WAAA,CAAAkG,OAAA,CAAAjG,cAAA,EAAgB;IAAA,EAAC;IAC3ET,EAAA,CAAAC,cAAA,kBAAoF;IAE5ED,EAAA,CAAAiC,UAAA,IAAA0E,yDAAA,kBA4FM;IACN3G,EAAA,CAAA4G,SAAA,cAKM;IACV5G,EAAA,CAAAU,YAAA,EAAM;;;;IAtGWV,EAAA,CAAAW,UAAA,cAAAkG,MAAA,CAAAvE,UAAA,CAAwB;IACpCtC,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAW,UAAA,oBAAmB,WAAAkG,MAAA,CAAA/F,WAAA,CAAAC,SAAA;IAEiBf,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAW,UAAA,YAAAkG,MAAA,CAAAC,cAAA,CAAiB;;;;;;IA2G9D9G,EAAA,CAAAC,cAAA,qBAYC;IAVGD,EAAA,CAAAE,UAAA,+BAAA6G,yGAAA1F,MAAA;MAAArB,EAAA,CAAAI,aAAA,CAAA4G,IAAA;MAAA,MAAAC,SAAA,GAAAjH,EAAA,CAAAO,aAAA,GAAAoB,SAAA;MAAA,MAAAuF,OAAA,GAAAlH,EAAA,CAAAO,aAAA;MAAA,OAAiBP,EAAA,CAAAQ,WAAA,CAAA0G,OAAA,CAAAC,QAAA,CAAAF,SAAA,CAAAG,EAAA,EAAAC,WAAA,GAAAhG,MAAA,CACxB;IAAA,EADuD;IAUnDrB,EAAA,CAAAU,YAAA,EAAa;;;;;IAXVV,EAAA,CAAAW,UAAA,iBAAgB,gBAAA2G,OAAA,CAAAH,QAAA,CAAAF,SAAA,CAAAG,EAAA,EAAAC,WAAA,aAAAC,OAAA,CAAAH,QAAA,CAAAF,SAAA,CAAAG,EAAA,EAAAG,OAAA,aAAAD,OAAA,CAAAH,QAAA,CAAAF,SAAA,CAAAG,EAAA,EAAAI,OAAA,aAAAF,OAAA,CAAAH,QAAA,CAAAF,SAAA,CAAAG,EAAA,EAAAK,WAAA,cAAAH,OAAA,CAAAH,QAAA,CAAAF,SAAA,CAAAG,EAAA,EAAAM,QAAA,CAAAC,IAAA,CAAAL,OAAA,iBAAAA,OAAA,CAAAH,QAAA,CAAAF,SAAA,CAAAG,EAAA,EAAAQ,UAAA,cAAAN,OAAA,CAAAH,QAAA,CAAAF,SAAA,CAAAG,EAAA,EAAAS,QAAA,UAAAP,OAAA,CAAAH,QAAA,CAAAF,SAAA,CAAAG,EAAA,EAAAU,IAAA,YAAAR,OAAA,CAAAH,QAAA,CAAAF,SAAA,CAAAG,EAAA,EAAAvF,UAAA,gBAAAoF,SAAA,CAAAc,SAAA;;;;;IAFxB/H,EAAA,CAAAC,cAAA,cAAuE;IACnED,EAAA,CAAAiC,UAAA,IAAA+F,gEAAA,0BAYc;IAClBhI,EAAA,CAAAU,YAAA,EAAM;;;;;IAbWV,EAAA,CAAAiB,SAAA,GAA6B;IAA7BjB,EAAA,CAAAW,UAAA,SAAAsH,MAAA,CAAAC,WAAA,IAAAjB,SAAA,CAAAG,EAAA,CAA6B;;;;;;IA1HlDpH,EAAA,CAAAC,cAAA,UAA0B;IAGsBD,EAAA,CAAAgB,MAAA,GAAiD;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IAC3FV,EAAA,CAAA4G,SAAA,sBAAoF;IACxF5G,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,aAAkG;IAC9FD,EAAA,CAAAiC,UAAA,IAAAkG,uDAAA,sBAAsO;IACtOnI,EAAA,CAAAC,cAAA,kBAAuK;IAArBD,EAAA,CAAAE,UAAA,qBAAAkI,yEAAA;MAAApI,EAAA,CAAAI,aAAA,CAAAiI,IAAA;MAAA,MAAAC,OAAA,GAAAtI,EAAA,CAAAO,aAAA;MAAA,OAAWP,EAAA,CAAAQ,WAAA,CAAA8H,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAACvI,EAAA,CAAAU,YAAA,EAAW;IAI1LV,EAAA,CAAAiC,UAAA,IAAAuG,mDAAA,kBAwGO;IAEPxI,EAAA,CAAAC,cAAA,cAA0E;IACtED,EAAA,CAAA4G,SAAA,qBAA6E;IACjF5G,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAiC,UAAA,KAAAwG,mDAAA,kBAcM;IACVzI,EAAA,CAAAU,YAAA,EAAM;;;;IArI0CV,EAAA,CAAAiB,SAAA,GAAiD;IAAjDjB,EAAA,CAAA2C,iBAAA,CAAA+F,MAAA,CAAA5H,WAAA,CAAAC,SAAA,CAAA2H,MAAA,CAAAC,YAAA,CAAAC,IAAA,EAAiD;IAC9C5I,EAAA,CAAAiB,SAAA,GAAe;IAAfjB,EAAA,CAAAW,UAAA,UAAA+H,MAAA,CAAAG,KAAA,CAAe,SAAAH,MAAA,CAAAI,IAAA;IAG3C9I,EAAA,CAAAiB,SAAA,GAAgC;IAAhCjB,EAAA,CAAAW,UAAA,SAAA+H,MAAA,CAAAC,YAAA,CAAAI,aAAA,CAAgC;IACjC/I,EAAA,CAAAiB,SAAA,GAA8B;IAA9BjB,EAAA,CAAAW,UAAA,cAAA+H,MAAA,CAAA7H,cAAA,GAA8B,UAAA6H,MAAA,CAAA5H,WAAA,CAAAC,SAAA;IAIzCf,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAW,UAAA,SAAA+H,MAAA,CAAApG,UAAA,CAAgB;IA2GRtC,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAW,UAAA,UAAA+H,MAAA,CAAAM,SAAA,CAAmB,eAAAN,MAAA,CAAAO,kBAAA;IAE0BjJ,EAAA,CAAAiB,SAAA,GAAS;IAATjB,EAAA,CAAAW,UAAA,YAAA+H,MAAA,CAAAQ,MAAA,CAAS;;;ADxGzE,OAAM,MAAOC,6BAA8B,SAAQtJ,aAAa;EAC5DuJ,YAAYC,QAAkB,EAAUC,WAAwB,EAC5CC,aAA4B;IAC5C,KAAK,CAACF,QAAQ,CAAC;IAFqB,KAAAC,WAAW,GAAXA,WAAW;IAC/B,KAAAC,aAAa,GAAbA,aAAa;IASjC,KAAAC,SAAS,GAA2B,EAAE;IACtC,KAAA1C,cAAc,GAAyB,EAAE;IAKzC,KAAAhC,UAAU,GAAGhF,SAAS,CAAC2J,cAAc;IACrC,KAAA7F,SAAS,GAAG9D,SAAS,CAAC4J,SAAS;IAE/B,KAAAV,SAAS,GAAe,EAAE;IAC1B,KAAAC,kBAAkB,GAAa,IAAI;IAEnC,KAAAC,MAAM,GAAe,EAAE;IACvB,KAAA/B,QAAQ,GAgBL,EAAE;EApCL;EAsCAwC,QAAQA,CAAA;IACJ,IAAI,CAACC,QAAQ,GAAGC,QAAQ,CAAC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,CAAC;IAChE,IAAI,CAACC,eAAe,EAAE;EAC1B;EAEAA,eAAeA,CAAA;IACX,IAAIC,EAAE,GAAG,IAAI;IACb,IAAI,CAACZ,aAAa,CAACa,sBAAsB,CAAC,IAAI,CAACR,QAAQ,EAAGS,QAAQ,IAAG;MACjEF,EAAE,CAACxB,YAAY,GAAG0B,QAAQ;MAC1BC,UAAU,CAAC;QACPH,EAAE,CAACI,QAAQ,EAAE;MACjB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEAA,QAAQA,CAAA;IACJ,IAAI,CAAC1B,KAAK,GAAG,CAAC;MAAE2B,KAAK,EAAE,IAAI,CAAC1J,WAAW,CAACC,SAAS,CAAC,oBAAoB;IAAC,CAAC,EACxD;MAAEyJ,KAAK,EAAE,IAAI,CAAC1J,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAAC0J,UAAU,EAAE;IAAyB,CAAC,EACvG;MAAED,KAAK,EAAE,IAAI,CAAC1J,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;MAAC0J,UAAU,EAAE;IAAwC,CAAC,EAC7H;MAAED,KAAK,EAAE,IAAI,CAAC7B,YAAY,CAACC;IAAI,CAAC,CACnC;IACb,IAAI,CAACE,IAAI,GAAG;MAAE4B,IAAI,EAAE,YAAY;MAAED,UAAU,EAAE;IAAG,CAAE;IACnD,IAAI,CAACE,cAAc,EAAE;IACrB,IAAI,CAACC,UAAU,EAAE;EACrB;EAEAD,cAAcA,CAAA;IACV,IAAIR,EAAE,GAAG,IAAI;IACb,IAAG,IAAI,CAACxB,YAAY,CAACkC,YAAY,EAAC;MAC9B,IAAI,CAAC/D,cAAc,GAAGgE,IAAI,CAACC,KAAK,CAAC,IAAI,CAACpC,YAAY,CAACkC,YAAY,CAAC;MAChE,IAAI,CAAC/D,cAAc,CAACkE,OAAO,CAACC,EAAE,IAAG;QAC7B,IAAGA,EAAE,CAACpG,MAAM,IAAI,IAAI,CAACC,UAAU,CAACwB,WAAW,IAAI2E,EAAE,CAACpG,MAAM,IAAI,IAAI,CAACC,UAAU,CAACyB,WAAW,IAAK0E,EAAE,CAACpG,MAAM,IAAI,IAAI,CAACC,UAAU,CAACC,MAAM,IAAIkG,EAAE,CAAC7F,cAAc,IAAI,IAAK,EAAC;UAC1J6F,EAAE,CAAC,SAAS,CAAC,GAAG,IAAIlL,gBAAgB,EAAE;;MAE9C,CAAC,CAAC;MACF,IAAI,CAAC8B,UAAU,GAAG,EAAE;MACpB,IAAI,CAACiF,cAAc,CAACkE,OAAO,CAACC,EAAE,IAAG;QAC7Bd,EAAE,CAACtI,UAAU,CAACoJ,EAAE,CAACnJ,KAAK,CAAC,GAAG,IAAI;MAClC,CAAC,CAAC;MACF,IAAI,CAACQ,UAAU,GAAG,IAAI,CAACgH,WAAW,CAAC4B,KAAK,CAAC,IAAI,CAACrJ,UAAU,CAAC;;EAEjE;EAEA+I,UAAUA,CAAA;IACN,IAAIT,EAAE,GAAG,IAAI;IACb,IAAG,IAAI,CAACxB,YAAY,CAACwC,cAAc,EAAC;MAChC,IAAI,CAACxC,YAAY,CAACwC,cAAc,CAACH,OAAO,CAACC,EAAE,IAAG;QAC1Cd,EAAE,CAACnB,SAAS,CAACoC,IAAI,CAAC;UACdhE,EAAE,EAAE6D,EAAE,CAAC7D,EAAE;UACToD,KAAK,EAAES,EAAE,CAAClD,SAAS;UACnBsD,OAAO,EAAEA,CAAA,KAAI;YACTlB,EAAE,CAACjC,WAAW,GAAG+C,EAAE,CAAC7D,EAAE;UAC1B;SACH,CAAC;QACF,IAAIkE,UAAU,GAAGL,EAAE,CAACM,iBAAiB,CAACC,KAAK,CAAC,GAAG,CAAC;QAChD,IAAIC,cAAc,GAAGR,EAAE,CAACS,aAAa,CAACF,KAAK,CAAC,GAAG,CAAC;QAChD,IAAIjE,OAAO,GAAG,EAAE;QAChB+D,UAAU,CAACN,OAAO,CAAC,CAACC,EAAE,EAAEU,KAAK,KAAG;UAC5BpE,OAAO,CAAC6D,IAAI,CAAC;YACTQ,GAAG,EAAEX,EAAE;YACPrC,IAAI,EAAE6C,cAAc,CAACE,KAAK;WAC7B,CAAC;QACN,CAAC,CAAC;QACFxB,EAAE,CAAChD,QAAQ,CAAC8D,EAAE,CAAC7D,EAAE,CAAC,GAAG;UACjBC,WAAW,EAAE,EAAE;UACfE,OAAO,EAAEA,OAAO,CAACsE,GAAG,CAAC,UAASZ,EAAE;YAC5B,IAAIa,MAAM,GAAe;cACrBF,GAAG,EAAEX,EAAE,CAACW,GAAG;cACXhD,IAAI,EAAEqC,EAAE,CAACrC,IAAI;cACbmD,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,IAAI;cACZC,MAAM,EAAE,KAAK;cACbC,IAAI,EAAE,yBAAyB3E,OAAO,CAAC4E,MAAM;aAChD;YACD,OAAOL,MAAM;UACjB,CAAC,CAAC;UACFtE,OAAO,EAAE;YACL4E,OAAO,EAAE,EAAE;YACXC,KAAK,EAAE;WACV;UACDC,UAAU,EAAE,EAAE;UACd5E,QAAQA,CAAC6E,IAAI,EAAEL,IAAI,EAAEpE,IAAI,EAAE0E,MAAM;YAC7BrC,EAAE,CAACsC,aAAa,CAACxB,EAAE,CAAC7D,EAAE,EAAEmF,IAAI,EAAEL,IAAI,EAAEpE,IAAI,EAAE0E,MAAM,CAAC;UACrD,CAAC;UACD/E,WAAW,EAAE;YACTiF,gBAAgB,EAAE,IAAI;YACtBC,MAAM,EAAE,IAAI;YACZC,aAAa,EAAE,KAAK;YACpBC,YAAY,EAAE,KAAK;YACnBC,eAAe,EAAE,IAAI;YACrBC,mBAAmB,EAAE,KAAK;YAC1BC,SAAS,EAAE;WACd;UACDpF,UAAU,EAAE,CAAC;UACbC,QAAQ,EAAE,EAAE;UACZ2E,MAAM,EAAE,EAAE;UACV1E,IAAI,EAAE,GAAGP,OAAO,CAAC,CAAC,CAAC,CAACqE,GAAG;SAC1B;MACL,CAAC,CAAC;MACF,IAAI,CAAC3C,kBAAkB,GAAG,IAAI,CAACD,SAAS,CAAC,CAAC,CAAC;MAC3C,IAAI,CAACd,WAAW,GAAG,IAAI,CAACe,kBAAkB,CAAC7B,EAAE;MAC7C,IAAI,CAAC8B,MAAM,GAAG,CAAC,GAAG,IAAI,CAACP,YAAY,CAACwC,cAAc,CAAC;;EAE3D;EAEA8B,WAAWA,CAAA;IACP,IAAIC,IAAI,GAAG;MACP9F,EAAE,EAAE,IAAI,CAACuB,YAAY,CAACvB,EAAE;MACxB+F,aAAa,EAAE,IAAI;MACnBC,WAAW,EAAE;KAChB;IACD,IAAI,CAACtG,cAAc,CAACkE,OAAO,CAACC,EAAE,IAAG;MAC7B,IAAGA,EAAE,CAACpG,MAAM,IAAI/E,SAAS,CAAC2J,cAAc,CAACrD,IAAI,EAAC;QAC1C8G,IAAI,CAACE,WAAW,CAAChC,IAAI,CAAC;UAClBtJ,KAAK,EAAEmJ,EAAE,CAACnJ,KAAK;UACfuL,KAAK,EAAE,IAAI,CAACxL,UAAU,CAACoJ,EAAE,CAACnJ,KAAK,CAAC,GAAG,IAAI,CAACwL,WAAW,CAACC,uBAAuB,CAAC,IAAI,CAAC1L,UAAU,CAACoJ,EAAE,CAACnJ,KAAK,CAAC,CAAC,GAAG;SAC5G,CAAC;OACL,MAAK,IAAGmJ,EAAE,CAACpG,MAAM,IAAI/E,SAAS,CAAC2J,cAAc,CAACpD,SAAS,EAAC;QACrD6G,IAAI,CAACE,WAAW,CAAChC,IAAI,CAAC;UAClBtJ,KAAK,EAAEmJ,EAAE,CAACnJ,KAAK;UACfuL,KAAK,EAAE,IAAI,CAACxL,UAAU,CAACoJ,EAAE,CAACnJ,KAAK,CAAC,GAAG,IAAI,CAACD,UAAU,CAACoJ,EAAE,CAACnJ,KAAK,CAAC,CAAC0L,OAAO,EAAE,GAAG;SAC5E,CAAC;OACL,MAAK,IAAGvC,EAAE,CAACpG,MAAM,IAAI/E,SAAS,CAAC2J,cAAc,CAACtD,MAAM,EAAC;QAClD+G,IAAI,CAACE,WAAW,CAAChC,IAAI,CAAC;UAClBtJ,KAAK,EAAEmJ,EAAE,CAACnJ,KAAK;UACfuL,KAAK,EAAE,IAAI,CAACxL,UAAU,CAACoJ,EAAE,CAACnJ,KAAK,CAAC,GAAG,IAAI,CAACD,UAAU,CAACoJ,EAAE,CAACnJ,KAAK,CAAC,GAAG,CAAC,GAAG;SACtE,CAAC;OACL,MAAK,IAAGmJ,EAAE,CAACpG,MAAM,IAAI/E,SAAS,CAAC2J,cAAc,CAACnD,WAAW,IAAI2E,EAAE,CAACpG,MAAM,IAAI/E,SAAS,CAAC2J,cAAc,CAAClD,WAAW,EAAC;QAC5G,IAAG0E,EAAE,CAACjG,aAAa,EAAC;UAChB,IAAG,IAAI,CAACnD,UAAU,CAACoJ,EAAE,CAACnJ,KAAK,CAAC,IAAE,IAAI,IAAE,IAAI,CAACD,UAAU,CAACoJ,EAAE,CAACnJ,KAAK,CAAC,CAACqK,MAAM,IAAI,CAAC,EAAC;YACtEe,IAAI,CAACE,WAAW,CAAChC,IAAI,CAAC;cAClBtJ,KAAK,EAAEmJ,EAAE,CAACnJ,KAAK;cACfuL,KAAK,EAAE;aACV,CAAC;WACL,MAAI;YACDH,IAAI,CAACE,WAAW,CAAChC,IAAI,CAAC;cAClBtJ,KAAK,EAAEmJ,EAAE,CAACnJ,KAAK;cACfuL,KAAK,EAAE,IAAI,CAACxL,UAAU,CAACoJ,EAAE,CAACnJ,KAAK,CAAC,GAAG,IAAI,CAACD,UAAU,CAACoJ,EAAE,CAACnJ,KAAK,CAAC,GAAG;aAClE,CAAC;;SAET,MAAI;UACDoL,IAAI,CAACE,WAAW,CAAChC,IAAI,CAAC;YAClBtJ,KAAK,EAAEmJ,EAAE,CAACnJ,KAAK;YACfuL,KAAK,EAAE,IAAI,CAACxL,UAAU,CAACoJ,EAAE,CAACnJ,KAAK,CAAC,GAAG,CAAC,IAAI,CAACD,UAAU,CAACoJ,EAAE,CAACnJ,KAAK,CAAC,CAAC,GAAG;WACpE,CAAC;;OAET,MAAI;QACDoL,IAAI,CAACE,WAAW,CAAChC,IAAI,CAAC;UAClBtJ,KAAK,EAAEmJ,EAAE,CAACnJ,KAAK;UACfuL,KAAK,EAAE,IAAI,CAACxL,UAAU,CAACoJ,EAAE,CAACnJ,KAAK,CAAC,GAAG,IAAI,CAACD,UAAU,CAACoJ,EAAE,CAACnJ,KAAK,CAAC,GAAG;SAClE,CAAC;;IAEV,CAAC,CAAC;IACF,OAAOoL,IAAI;EACf;EAEAzH,eAAeA,CAACgI,KAAK;IACjB,MAAMC,MAAM,GAAG,IAAI,CAACC,kBAAkB,CAACF,KAAK,CAACjI,UAAU,CAAC;IAExD;IACA,MAAMoI,MAAM,GAA8B,EAAE;IAE5C,IAAIH,KAAK,CAACrI,cAAc,EAAE;MACtB;MACA,KAAK,IAAIyI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,IAAI,CAAC3B,MAAM,EAAE0B,CAAC,EAAE,EAAE;QACzCD,MAAM,CAACF,MAAM,CAACI,IAAI,CAACD,CAAC,CAAC,CAAC,GAAGH,MAAM,CAACK,MAAM,CAACF,CAAC,CAAC,IAAI,IAAI;;;IAIzD,OAAOD,MAAM;EACjB;EAEAD,kBAAkBA,CAACtI,KAAa;IAC5B;IACA,MAAM2I,WAAW,GAAgC,EAAE;IAEnD;IACA,MAAMC,KAAK,GAAG5I,KAAK,CAACmG,KAAK,CAAC,GAAG,CAAC;IAE9B;IACA,KAAK,MAAM0C,IAAI,IAAID,KAAK,EAAE;MACtB,MAAM,CAACrC,GAAG,EAAEyB,KAAK,CAAC,GAAGa,IAAI,CAAC1C,KAAK,CAAC,GAAG,CAAC;MAEpC;MACA,IAAI2C,WAAmB;MACvB,IAAId,KAAK,CAACe,UAAU,CAAC,GAAG,CAAC,EAAE;QACvBD,WAAW,GAAG,IAAI,CAACE,YAAY,CAAChB,KAAK,CAACiB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;OACxD,MAAM;QACH;QACAH,WAAW,GAAGd,KAAK,CAACkB,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC;;MAG/C;MACA,IAAI3C,GAAG,IAAIoC,WAAW,EAAE;QACpBA,WAAW,CAACpC,GAAG,CAAC,CAACR,IAAI,CAAC+C,WAAW,CAAC;OACrC,MAAM;QACHH,WAAW,CAACpC,GAAG,CAAC,GAAG,CAACuC,WAAW,CAAC;;;IAIxC;IACA,MAAML,IAAI,GAAa,EAAE;IACzB,MAAMC,MAAM,GAAa,EAAE;IAE3B,KAAK,MAAMnC,GAAG,IAAIoC,WAAW,EAAE;MAC3BF,IAAI,CAAC1C,IAAI,CAACQ,GAAG,CAAC;MACdmC,MAAM,CAAC3C,IAAI,CAAC4C,WAAW,CAACpC,GAAG,CAAC,CAAC4C,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAG7C;IAEA;IACA,OAAO;MAAEV,IAAI;MAAEC;IAAM,CAAE;EAC3B;EAEAM,YAAYA,CAACzC,GAAG;IACZ,IAAI6C,WAAW;IACf,IAAG,IAAI,CAACC,UAAU,CAAC9C,GAAG,CAAC,IAAI9L,SAAS,CAAC2J,cAAc,CAACrD,IAAI,EAAC;MACrDqI,WAAW,GAAG,IAAI,CAAC5M,UAAU,CAAC+J,GAAG,CAAC,GAAG,IAAI,CAAC0B,WAAW,CAACC,uBAAuB,CAAC,IAAI,CAAC1L,UAAU,CAAC+J,GAAG,CAAC,CAAC,GAAG,IAAI;KAC7G,MAAK,IAAG,IAAI,CAAC8C,UAAU,CAAC9C,GAAG,CAAC,IAAI9L,SAAS,CAAC2J,cAAc,CAACpD,SAAS,EAAC;MAChEoI,WAAW,GAAG,IAAI,CAAC5M,UAAU,CAAC+J,GAAG,CAAC,GAAG,IAAI,CAAC/J,UAAU,CAAC+J,GAAG,CAAC,CAAC4B,OAAO,EAAE,GAAG,IAAI;KAC7E,MAAK,IAAG,IAAI,CAACkB,UAAU,CAAC9C,GAAG,CAAC,IAAI9L,SAAS,CAAC2J,cAAc,CAACtD,MAAM,EAAC;MAC7DsI,WAAW,GAAG,IAAI,CAAC5M,UAAU,CAAC+J,GAAG,CAAC,GAAG,IAAI,CAAC/J,UAAU,CAAC+J,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI;KACvE,MAAK,IAAG,IAAI,CAAC8C,UAAU,CAAC9C,GAAG,CAAC,IAAI9L,SAAS,CAAC2J,cAAc,CAACnD,WAAW,IAAI,IAAI,CAACoI,UAAU,CAAC9C,GAAG,CAAC,IAAI9L,SAAS,CAAC2J,cAAc,CAAClD,WAAW,EAAC;MAClIkI,WAAW,GAAG,IAAI,CAAC5M,UAAU,CAAC+J,GAAG,CAAC,GAAG,IAAI,CAAC/J,UAAU,CAAC+J,GAAG,CAAC,GAAG,IAAI;KACnE,MAAI;MACD6C,WAAW,GAAG,IAAI,CAAC5M,UAAU,CAAC+J,GAAG,CAAC,GAAG,IAAI,CAAC/J,UAAU,CAAC+J,GAAG,CAAC,GAAG,IAAI;;IAEpE,OAAO6C,WAAW;EACtB;EAEAC,UAAUA,CAAC9C,GAAG;IACV,MAAMyB,KAAK,GAAG,IAAI,CAACvG,cAAc,CAAC6H,IAAI,CAACzB,IAAI,IAAIA,IAAI,CAACpL,KAAK,KAAK8J,GAAG,CAAC;IAClE,OAAOyB,KAAK,GAAGA,KAAK,CAACxI,MAAM,GAAE,IAAI;EACrC;EAEApE,cAAcA,CAAA;IACV,IAAG,CAAC,IAAI,CAACkI,YAAY,CAACI,aAAa,EAAE;IACrC,IAAG,CAAC,IAAI,CAAClI,cAAc,EAAE,EAAE;IAC3B,IAAIsJ,EAAE,GAAG,IAAI;IACb,IAAI+C,IAAI,GAAG,IAAI,CAACD,WAAW,EAAE;IAC7B,IAAI,CAAC2B,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAI,CAACtF,aAAa,CAACuF,OAAO,CAAC5B,IAAI,EAAG7C,QAAQ,IAAG;MACzC,IAAGA,QAAQ,CAACc,cAAc,EAAC;QACvBd,QAAQ,CAACc,cAAc,CAACH,OAAO,CAAC+D,YAAY,IAAG;UAC3C5E,EAAE,CAAChD,QAAQ,CAAC4H,YAAY,CAACC,eAAe,CAAC,CAAC1C,UAAU,GAAGyC,YAAY,CAAC7B,IAAI,CAAC+B,MAAM,CAAChE,EAAE,IAAIiE,MAAM,CAACpB,IAAI,CAAC7C,EAAE,CAAC,CAACkB,MAAM,GAAG,CAAC,CAAC;UACjHhC,EAAE,CAACsC,aAAa,CAACsC,YAAY,CAACC,eAAe,EAAE,CAAC,EAAE7E,EAAE,CAAChD,QAAQ,CAAC4H,YAAY,CAACC,eAAe,CAAC,CAACnH,QAAQ,EAAEsC,EAAE,CAAChD,QAAQ,CAAC4H,YAAY,CAACC,eAAe,CAAC,CAAClH,IAAI,EAAE,EAAE,CAAC;QAC7J,CAAC,CAAC;;MAEN;IACJ,CAAC,EAAE,IAAI,EAAE,MAAI;MACTqC,EAAE,CAACyE,oBAAoB,CAACO,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEA1C,aAAaA,CAAC2C,OAAO,EAAE7C,IAAI,EAAEL,IAAI,EAAEpE,IAAI,EAAE2F,KAAK;IAC1C,IAAI,CAACtG,QAAQ,CAACiI,OAAO,CAAC,CAACxH,UAAU,GAAG2E,IAAI;IACxC,IAAI,CAACpF,QAAQ,CAACiI,OAAO,CAAC,CAACvH,QAAQ,GAAGqE,IAAI;IACtC,IAAI,CAAC/E,QAAQ,CAACiI,OAAO,CAAC,CAACtH,IAAI,GAAGA,IAAI;IAClC,IAAI,CAACX,QAAQ,CAACiI,OAAO,CAAC,CAAC5C,MAAM,GAAGiB,KAAK;IACrC,IAAIjG,OAAO,GAAG;MACV4E,OAAO,EAAE,IAAI,CAACjF,QAAQ,CAACiI,OAAO,CAAC,CAAC9C,UAAU,CAAC+C,KAAK,CAAC9C,IAAI,GAAGL,IAAI,EAAEK,IAAI,GAACL,IAAI,GAAGA,IAAI,CAAC;MAC/EG,KAAK,EAAE,IAAI,CAAClF,QAAQ,CAACiI,OAAO,CAAC,CAAC9C,UAAU,CAACH;KAC5C;IACD,IAAI,CAAChF,QAAQ,CAACiI,OAAO,CAAC,CAAC5H,OAAO,GAAGA,OAAO;EAC5C;EAEAe,MAAMA,CAAA;IACF,IAAG,CAAC,IAAI,CAAC1H,cAAc,EAAE,EAAE;IAC3B,IAAIqM,IAAI,GAAG,IAAI,CAACD,WAAW,EAAE;IAC7B,IAAI,CAAC2B,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAI,CAACtF,aAAa,CAAC+F,UAAU,CAACpC,IAAI,EAAC,MAAI,CAAC,CAAC,EAAGtH,KAAK,IAAG;MAChD2J,OAAO,CAACC,GAAG,CAAC5J,KAAK,CAAC;MAClB,IAAIuE,EAAE,GAAG,IAAI;MACb,IAAGvE,KAAK,CAACA,KAAK,YAAY6J,IAAI,EAAC;QAC3B7J,KAAK,CAACA,KAAK,CAAC8J,IAAI,EAAE,CAACC,IAAI,CAAC,UAASC,UAAU;UACvC,IAAIC,SAAS,GAAG/E,IAAI,CAACC,KAAK,CAAC6E,UAAU,CAAC,CAAChK,KAAK;UAC5C,IAAGiK,SAAS,CAACC,SAAS,IAAI,0BAA0B,EAAE;YAClD3F,EAAE,CAACyE,oBAAoB,CAAChJ,KAAK,CAACuE,EAAE,CAACrJ,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC,CAAC;WAC1F,MAAI;YACDoJ,EAAE,CAACyE,oBAAoB,CAAChJ,KAAK,CAACuE,EAAE,CAACrJ,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC,CAAC;;QAEvF,CAAC,CAAC;OACL,MAAI;QACDwO,OAAO,CAACC,GAAG,CAAC5J,KAAK,CAAC;;IAE1B,CAAC,CAAC;EACN;EAEA/E,cAAcA,CAAA;IACV,IAAG,CAAC,IAAI,CAACyB,UAAU,EAAE,OAAO,KAAK;IACjC,IAAG,IAAI,CAACA,UAAU,CAACyN,OAAO,EAAE,OAAO,KAAK;IACxC,IAAG,CAAC,IAAI,CAACjJ,cAAc,IAAI,EAAE,EAAEqF,MAAM,GAAG,CAAC,EAAC;MACtC,KAAI,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC/G,cAAc,CAACqF,MAAM,EAAC0B,CAAC,EAAE,EAAC;QAC9C,IAAI5C,EAAE,GAAG,IAAI,CAACnE,cAAc,CAAC+G,CAAC,CAAC;QAC/B,IAAG5C,EAAE,CAAC,SAAS,CAAC,EAAC;UACb,IAAGA,EAAE,CAAC,SAAS,CAAC,CAAC8E,OAAO,EAAC;YACrB,OAAO,KAAK;;;;;IAK5B,OAAO,IAAI;EACf;EAEAvO,gBAAgBA,CAACwO,KAAK;IAClB,IAAGA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,MAAM,IAAIF,KAAK,CAACG,QAAQ,EAAC;MAC/C;;IAEJ,IAAGH,KAAK,CAACI,OAAO,IAAI,CAAC,IAAIJ,KAAK,CAACI,OAAO,IAAI,EAAE,IAAIJ,KAAK,CAACI,OAAO,IAAI,EAAE,IAAIJ,KAAK,CAACI,OAAO,IAAI,EAAE,IAAIJ,KAAK,CAACI,OAAO,IAAI,EAAE,EAAC;MAC9G;;IAEJ,IAAGJ,KAAK,CAACI,OAAO,GAAG,EAAE,IAAIJ,KAAK,CAACI,OAAO,GAAG,EAAE,EAAC;MACxCJ,KAAK,CAACK,cAAc,EAAE;;EAE9B;EAEArO,mBAAmBA,CAAC4J,GAAG;IACnB,IAAIzB,EAAE,GAAG,IAAI;IACb,IAAG,IAAI,CAACtI,UAAU,CAAC+J,GAAG,CAAC,IAAI,IAAI,EAAE;IACjC,IAAG0E,KAAK,CAAC,IAAI,CAACzO,UAAU,CAAC+J,GAAG,CAAC,CAAC,EAAC;MAC3BtB,UAAU,CAAC;QACPH,EAAE,CAACtI,UAAU,CAAC+J,GAAG,CAAC,GAAG,IAAI;MAC7B,CAAC,CAAC;;EAEV;;;uBA/WSzC,6BAA6B,EAAAnJ,EAAA,CAAAuQ,iBAAA,CAAAvQ,EAAA,CAAAwQ,QAAA,GAAAxQ,EAAA,CAAAuQ,iBAAA,CAAAE,EAAA,CAAAC,WAAA,GAAA1Q,EAAA,CAAAuQ,iBAAA,CAAAI,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAA7BzH,6BAA6B;MAAA0H,SAAA;MAAAC,QAAA,GAAA9Q,EAAA,CAAA+Q,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjB1CrR,EAAA,CAAAiC,UAAA,IAAAsP,4CAAA,mBAwIM;;;UAxIAvR,EAAA,CAAAW,UAAA,SAAA2Q,GAAA,CAAA3I,YAAA,CAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}