<style>
    /* .col-3{
        padding: 10px;
    } */
</style>

<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{this.tranService.translate("global.menu.listaccount")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button styleClass="p-button-info"
                    [label]="tranService.translate('global.button.create')"
                    icon="" [routerLink]="['/accounts/create']"
                    routerLinkActive="router-link-active"
                    *ngIf="checkPermission([allPermissions.ACCOUNT.CREATE])"></p-button>
    </div>
</div>

<form [formGroup]="formSearchAccount" (ngSubmit)="onSubmitSearch()" class="pt-3 pb-2 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid search-grid-4">
            <!-- Ten dang nhap -->
            <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                            class="w-full"
                            pInputText id="username"
                            [(ngModel)]="searchInfo.username"
                            formControlName="username"
                    />
                    <label htmlFor="username">{{tranService.translate("account.label.username")}}</label>
                </span>
            </div>
            <!-- Ho ten -->
            <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                            class="w-full"
                            pInputText id="fullName"
                            [(ngModel)]="searchInfo.fullName"
                            formControlName="fullName"
                    />
                    <label htmlFor="fullName">{{tranService.translate("account.label.fullname")}}</label>
                </span>
            </div>
            <!-- loai tai khoan -->
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                            id="type" [autoDisplayFirst]="false"
                            [(ngModel)]="searchInfo.type"
                            formControlName="type"
                            [options]="statusAccounts"
                            optionLabel="name"
                            optionValue="value"
                    ></p-dropdown>
                    <label class="label-dropdown" for="type">{{tranService.translate("account.label.userType")}}</label>
                </span>
            </div>
             <!-- email -->
             <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                            pInputText id="email"
                            [(ngModel)]="searchInfo.email"
                            formControlName="email"
                    />
                    <label htmlFor="email">{{tranService.translate("account.label.email")}}</label>
                </span>
            </div>
            <!-- ma tinh -->
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full"
                            [showClear]="true" [filter]="true" filterBy="display"
                            id="provinceCode" [autoDisplayFirst]="false"
                            [(ngModel)]="searchInfo.provinceCode"
                            formControlName="provinceCode"
                            [options]="listProvince"
                            optionLabel="display"
                            optionValue="code"
                            [emptyFilterMessage]="tranService.translate('global.text.nodata')"
                    ></p-dropdown>
                    <label class="label-dropdown" htmlFor="provinceCode">{{tranService.translate("account.label.province")}}</label>
                </span>
            </div>
            <!-- trang thai  -->
            <div class="col-3">
                <span class="p-float-label">
                     <p-dropdown styleClass="w-full"
                                 [showClear]="true" [filter]="true" filterBy="display"
                                 id="status" [autoDisplayFirst]="false"
                                 [(ngModel)]="searchInfo.status"
                                 formControlName="status"
                                 [options]="listStatus"
                                 optionLabel="name"
                                 optionValue="value"
                                 [emptyFilterMessage]="tranService.translate('account.label.status')"
                     ></p-dropdown>
                    <label class="label-dropdown" htmlFor="status">{{tranService.translate("account.label.status")}}</label>
                </span>
            </div>


            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                            styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                            type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>

<div class="flex justify-content-center dialog-vnpt ">
    <p-dialog [header]="tranService.translate('global.button.view')" [(visible)]="isShowModalDetail" [modal]="true" [style]="{ width: '980px' }" [draggable]="false" [resizable]="false" *ngIf="isShowModalDetail">
        <p-tabView (onChange)="onTabChange($event)">
            <p-tabPanel header="{{tranService.translate('account.label.generalInfo')}}">
        <div class="flex flex-row justify-content-between account-create" *ngIf="accountResponse">
            <div style="width: 49%;">
                <!-- username -->
                <div class="w-full field grid dialog account-info-grid">
                    <label htmlFor="accountName" class="col-fixed" style="width:180px">{{tranService.translate("account.label.username")}}</label>
                    <div class="col">
                        {{accountResponse.username}}
                    </div>
                </div>
                <!-- status -->
                <div class="w-full field grid account-info-grid">
                    <label htmlFor="fullName" class="col-fixed" style="width:180px">{{tranService.translate("account.label.status")}}</label>
                    <div class="col">
                        {{getStringUserStatus(accountResponse.status)}}
                    </div>
                </div>
                <!-- fullname -->
                <div class="w-full field grid account-info-grid">
                    <label htmlFor="fullName" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate("account.label.fullname")}}</label>
                    <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                        {{accountResponse.fullName}}
                    </div>
                </div>
                <!-- phone -->
                <div class="w-full field grid account-info-grid">
                    <label htmlFor="phone" class="col-fixed" style="width:180px">{{tranService.translate("account.label.phone")}}</label>
                    <div class="col">
                        {{accountResponse.phone}}
                    </div>
                </div>
                <!-- email -->
                <div class="w-full field grid account-info-grid">
                    <label htmlFor="email" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate("account.label.email")}}</label>
                    <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                        {{accountResponse.email}}
                    </div>
                </div>
                <!-- description -->
                <div class="w-full field grid account-info-grid">
                    <label htmlFor="description" class="col-fixed" style="width:180px">{{tranService.translate("account.label.description")}}</label>
                    <div class="col">
                        {{accountResponse.description}}
                    </div>
                </div>
            </div>
            <div style="width: 49%;">
                <!-- loai tai khoan -->
                <div class="w-full field grid account-info-grid">
                    <label for="userType" class="col-fixed" style="width:180px">{{tranService.translate("account.label.userType")}}</label>
                    <div class="col">
                        <span>{{getStringUserType(accountResponse.type)}}</span>
                    </div>
                </div>
                <!-- Tinh thanh pho -->
                <div class="w-full field grid account-info-grid" *ngIf="accountInfo.userType != optionUserType.ADMIN && accountInfo.userType != optionUserType.AGENCY">
                    <label htmlFor="province" class="col-fixed" style="width:180px">{{tranService.translate("account.label.province")}}</label>
                    <div class="col">
                        <span>{{accountResponse.provinceName}} ({{accountResponse.provinceCode}})</span>
                    </div>
                </div>
<!--                &lt;!&ndash; ten khach hang &ndash;&gt;-->
<!--                <div class="w-full field grid" *ngIf="accountInfo.userType == optionUserType.CUSTOMER">-->
<!--                    <label htmlFor="roles" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate("account.label.customerName")}}</label>-->
<!--                    <div class="col" style="max-width: calc(100% - 180px) !important;">-->
<!--                        <div *ngFor="let item of accountResponse.customers">-->
<!--                            {{ item.customerName + ' - ' + item.customerCode}}-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
                <!-- GDV quan ly-->
                <div class="w-full field grid account-info-grid" [class]="accountInfo.userType == optionUserType.CUSTOMER && (userType == optionUserType.ADMIN || userType == optionUserType.PROVINCE) ? '' : 'hidden'">
                    <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate("account.label.managerName")}}</label>
                    <div class="col" style="max-width: calc(100% - 180px) !important;">
                        {{ accountResponse?.manager?.username }}
                    </div>
                </div>
                <!-- Danh sach tai khoan khach hang -->
                <div class="w-full field grid align-items-start account-info-grid" *ngIf="accountInfo.userType == optionUserType.DISTRICT">
                    <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate("account.label.customerAccount")}}</label>
                    <div class="col" style="max-width: calc(100% - 180px) !important;">
                        <div *ngFor="let item of accountResponse?.userManages">
                            <div *ngIf="item?.isRootCustomer">{{ item.username}}</div>
                        </div>
                    </div>
                </div>
                <!-- Tài khoản khách hàng root khi-->
                <div class="w-full field grid align-items-start account-info-grid" *ngIf="accountInfo.userType == optionUserType.CUSTOMER && !accountResponse?.isRootCustomer">
                    <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate("account.label.customerAccount")}}</label>
                    <div class="col" style="max-width: calc(100% - 180px) !important;">
                        <div *ngIf="accountResponse?.rootAccount?.username">
                            {{ accountResponse.rootAccount.username}}
                        </div>
                    </div>
                </div>
                <!-- nhom quyen -->
                <div class="w-full field grid account-info-grid">
                    <label htmlFor="roles" class="col-fixed" style="width:180px; height: fit-content;">{{tranService.translate("account.label.role")}}</label>
                    <div class="col" style="max-width: calc(100% - 180px) !important;">
                        <!-- <div>{{getStringRoles()}}</div> -->
                        <div *ngFor="let item of accountResponse.roles">
                            {{ item.roleName}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
            </p-tabPanel>
            <p-tabPanel header="{{tranService.translate('global.menu.listcustomer')}}" *ngIf="accountInfo.userType ==  CONSTANTS.USER_TYPE.CUSTOMER">
                <div class="flex flex-row justify-content-center gap-3 mt-4">
                    <input style="min-width: 35vw"  type="text" pInputText [placeholder]="tranService.translate('sim.label.quickSearch')" (keydown.enter)="onSearchCustomer(true)" [(ngModel)]="paramQuickSearchCustomer.keyword" [ngModelOptions]="{standalone: true}">
                    <p-button icon="pi pi-search"
                              styleClass="ml-3 p-button-rounded p-button-secondary p-button-text button-search"
                              type="button"
                              (click)="onSearchCustomer(true)"
                    ></p-button>
                </div>
                <table-vnpt
                    [fieldId]="'id'"
                    [pageNumber]="paginationCustomer.page"
                    [pageSize]="paginationCustomer.size"
                    [columns]="columnInfoCustomer"
                    [dataSet]="dataSetCustomer"
                    [options]="optionTableCustomer"
                    [loadData]="searchCustomer.bind(this)"
                    [rowsPerPageOptions]="[5,10,20,25,50]"
                    [scrollHeight]="'400px'"
                    [sort]="paginationCustomer.sortBy"
                    [params]="paramQuickSearchCustomer"
                ></table-vnpt>
            </p-tabPanel>
            <p-tabPanel header="{{tranService.translate('global.menu.listbill')}}" *ngIf="accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER">
                <div class="flex flex-row justify-content-center gap-3 mt-4">
                    <input style="min-width: 35vw"  type="text" pInputText [placeholder]="tranService.translate('sim.label.quickSearch')" (keydown.enter)="onSearchContract(true)" [(ngModel)]="paramQuickSearchContract.keyword" [ngModelOptions]="{standalone: true}">
                    <p-button icon="pi pi-search"
                              styleClass="ml-3 p-button-rounded p-button-secondary p-button-text button-search"
                              type="button"
                              (click)="onSearchContract(true)"
                    ></p-button>
                </div>
                <table-vnpt
                    [fieldId]="'id'"
                    [pageNumber]="paginationContract.page"
                    [pageSize]="paginationContract.size"
                    [columns]="columnInfoContract"
                    [dataSet]="dataSetContract"
                    [options]="optionTableContract"
                    [loadData]="searchContract.bind(this)"
                    [rowsPerPageOptions]="[5,10,20,25,50]"
                    [scrollHeight]="'400px'"
                    [sort]="paginationContract.sortBy"
                    [params]="paramQuickSearchContract"
                ></table-vnpt>
            </p-tabPanel>
            <p-tabPanel header="{{tranService.translate('account.text.grantApi')}}" *ngIf="genGrantApi.secretKey != null" [pt]="'ProfileTab'">
                <div class="mb-3">
                    <p-panel [showHeader]="false">
                        <div class="flex gap-2">
                            <p-radioButton
                                    [label]="tranService.translate('account.text.working')"
                                    value="1"
                                    class="p-3"
                                    [(ngModel)]="statusGrantApi"
                                    [disabled]="true"
                                    [ngModelOptions]="{standalone: true}"
                            >
                            </p-radioButton>
                            <p-radioButton
                                    [label]="tranService.translate('account.text.notWorking')"
                                    value="0"
                                    class="p-3"
                                    [(ngModel)]="statusGrantApi"
                                    [disabled]="true"
                                    [ngModelOptions]="{standalone: true}"
                            >
                            </p-radioButton>
                        </div>
                        <div class="flex gap-3 align-items-center api-input-section">
                            <div class="col-6">
                                <div class="flex align-items-center">
                                    <label style="min-width: 100px" class="mr-3">Client ID</label>
                                    <input [(ngModel)]="genGrantApi.clientId"  [disabled]="true" [ngModelOptions]="{standalone: true}" class="w-full" type="text" pInputText>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="flex align-items-center">
                                    <label style="min-width: 100px" class="mr-3">Secret Key</label>
                                    <div class="w-full flex align-items-center">
                                        <input class="w-full mr-2" style="padding-right: 30px;"
                                               [(ngModel)]="genGrantApi.secretKey"
                                               [ngModelOptions]="{standalone: true}"
                                               [type]="isShowSecretKey ? 'text': 'password'"
                                               pInputText
                                               [disabled]="true"
                                        />
                                        <label style="margin-left: -30px;z-index: 1;" *ngIf="isShowSecretKey == false" class="pi pi-eye toggle-password" (click)="isShowSecretKey = true"></label>
                                        <label style="margin-left: -30px;z-index: 1;" *ngIf="isShowSecretKey == true" class="pi pi-eye-slash toggle-password" (click)="isShowSecretKey = false"></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </p-panel>
                </div>
                <div>
                    <p-panel [showHeader]="false">
                        <div class="flex gap-3 align-items-center module-search">
                            <div class="col-3 dropdown-fit">
                                <p-dropdown class="w-full"
                                            [showClear]="true"
                                            [(ngModel)]="paramsSearchGrantApi.module"
                                            [ngModelOptions]="{standalone: true}"
                                            [options]="listModule"
                                            optionLabel="name"
                                            optionValue="value"
                                            [emptyFilterMessage]="tranService.translate('global.text.nodata')"
                                            filter="true"
                                            [placeholder]="tranService.translate('account.text.module')"
                                ></p-dropdown>
                            </div>
                            <div class="col-3 dropdown-fit">
                                <input [(ngModel)]="paramsSearchGrantApi.api" [ngModelOptions]="{standalone: true}" class="w-full mr-2" type="text" pInputText placeholder="API"/>
                            </div>
                            <p-button icon="pi pi-search"
                                      styleClass="ml-3 p-button-rounded p-button-secondary p-button-text button-search"
                                      type="button"
                                      (click)="onSearchGrantApi(true)"
                            ></p-button>
                        </div>

                        <table-vnpt
                                [fieldId]="'id'"
                                [pageNumber]="paginationGrantApi.page"
                                [pageSize]="paginationGrantApi.size"
                                [columns]="columnInfoGrantApi"
                                [dataSet]="dataSetGrantApi"
                                [options]="optionTableGrantApi"
                                [loadData]="searchGrantApi.bind(this)"
                                [rowsPerPageOptions]="[5,10,20,25,50]"
                                [scrollHeight]="'400px'"
                                [sort]="paginationGrantApi.sortBy"
                                [params]="paramsSearchGrantApi"
                        ></table-vnpt>
                    </p-panel>
                </div>
            </p-tabPanel>
        </p-tabView>
    </p-dialog>
</div>
<table-vnpt
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.listaccount')"
></table-vnpt>

<div class="flex justify-content-center dialog-push-group">
    <p-dialog [header]="tranService.translate('account.text.titleChangeManageLevel')" [(visible)]="isShowDialogChangeManageLevel" [modal]="true" [style]="{ width: '500px' }" [draggable]="false" [resizable]="false">
        <div class="w-full field grid">
            <label htmlFor="account" class="col-fixed" style="width:100px">{{tranService.translate("account.text.account")}}</label>
            <div class="col">
                <p-autoComplete styleClass="w-full"
                    id="account"
                    field="name"
                    [(ngModel)]="accountSelected"
                    [suggestions]="listAccounts"
                    (completeMethod)="filterListAccount($event)"
                    [dropdown] = "true"
                    [placeholder]="tranService.translate('account.text.selectAccount')"
                ></p-autoComplete>
            </div>
        </div>
        <div class="flex flex-row justify-content-center align-items-center">
            <p-button styleClass="mr-2 p-button-secondary" [label]="tranService.translate('global.button.cancel')" (click)="isShowDialogChangeManageLevel = false"></p-button>
            <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.save')" (click)="changeManageLevel()" [disabled]="accountSelected == null || accountSelected == undefined"></p-button>
        </div>
    </p-dialog>
</div>

<div class="flex justify-content-center dialog-vnpt" *ngIf="formChangeManageData">
    <p-dialog [header]="tranService.translate('global.text.changeManageData')" [(visible)]="isShowDialogChangeManageData" [modal]="true" [style]="{ width: '500px' }" [draggable]="false" [resizable]="false">
        <form [formGroup]="formChangeManageData" (ngSubmit)="changeManageData()">
            <!-- giao dich vien -->
            <div class="w-full field grid">
                <label htmlFor="customer" class="col-fixed" style="width:140px">{{tranService.translate("account.usertype.district")}}<span class="text-red-500">*</span></label>
                <div class="col dropdown-fit">
                    <vnpt-select
                        [control]="searchUserTellerController"
                        [(value)]="changeManageDataInfo.userId"
                        [required]="true"
                        [isAutoComplete]="false"
                        [isMultiChoice]="false"
                        objectKey="account"
                        paramKey="fullName"
                        [paramDefault]="paramSearchTeller"
                        keyReturn="id"
                        displayPattern="${fullName} - ${email}"
                        [lazyLoad]="true"
                        [listExclude]="listTellerExcludes"
                    ></vnpt-select>
                </div>
            </div>
            <!-- error giao dich vien -->
            <div class="w-full field grid text-error-field">
                <label htmlFor="province" class="col-fixed" style="width:140px"></label>
                <div class="col dropdown-fit" style="width: calc(100% - 140px)">
                    <small class="text-red-500" *ngIf="searchUserTellerController.dirty && searchUserTellerController.error.required">{{tranService.translate("global.message.required")}}</small>
                </div>
            </div>
            <div class="w-full field grid">
                <div class="col">
                    <p-radioButton
                        name="typeSelect"
                        [value]="0"
                        [(ngModel)]="changeManageDataInfo.typeSelect"
                        formControlName="typeSelect"
                        inputId="typeSelectAll"/>
                    <label for="typeSelectAll" class="ml-2">
                        {{tranService.translate("account.text.typeSelectAll")}}
                    </label>
                </div>
                <div class="col">
                    <p-radioButton
                        name="typeSelect"
                        [value]="1"
                        [(ngModel)]="changeManageDataInfo.typeSelect"
                        formControlName="typeSelect"
                        inputId="typeSelectList"/>
                    <label for="typeSelectList" class="ml-2">
                        {{tranService.translate("account.text.typeSelectList")}}
                    </label>
                </div>
            </div>
            <!-- khach hang -->
            <div class="w-full field grid" [class]="changeManageDataInfo.typeSelect == 1 ? '' : 'hidden'">
                <label htmlFor="customer" class="col-fixed" style="width:140px">{{tranService.translate("account.usertype.customer")}}<span class="text-red-500">*</span></label>
                <div class="col dropdown-fit" style="width: calc(100% - 140px)">
                    <vnpt-select
                        [control]="searchUserCustomerController"
                        [(value)]="changeManageDataInfo.accountCustomerIds"
                        [required]="true"
                        [isAutoComplete]="false"
                        [isMultiChoice]="true"
                        [loadData]="loadListUserCustomerOfTeller.bind(this)"
                        paramKey="fullName"
                        keyReturn="id"
                        displayPattern="${fullName} - ${email}"
                        [lazyLoad]="true"
                    ></vnpt-select>
                </div>
            </div>
            <!-- error khach hang -->
            <div class="w-full field grid text-error-field" *ngIf="changeManageDataInfo.typeSelect == 1">
                <label htmlFor="province" class="col-fixed" style="width:140px"></label>
                <div class="col" style="width: calc(100% - 140px)">
                    <small class="text-red-500" *ngIf="searchUserCustomerController.dirty && searchUserCustomerController.error.required">{{tranService.translate("global.message.required")}}</small>
                </div>
            </div>
            <div class="flex flex-row justify-content-center align-items-center">
                <p-button styleClass="mr-2 p-button-secondary p-button-outlined" [label]="tranService.translate('global.button.cancel')" (click)="isShowDialogChangeManageData = false"></p-button>
                <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.save')" type="submit" [disabled]="formChangeManageData.invalid || (searchUserTellerController.invalid === true) || (changeManageDataInfo.typeSelect == 1 && (searchUserCustomerController.invalid === true))"></p-button>
            </div>
        </form>
    </p-dialog>
</div>
