{"ast": null, "code": "import { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { Observable, debounceTime, map, switchMap, take } from 'rxjs';\nimport { ComponentBase } from 'src/app/component.base';\nimport { RatingPlanService } from 'src/app/service/rating-plan/RatingPlanService';\nimport { AccountService } from \"../../../../service/account/AccountService\";\nimport { CONSTANTS } from 'src/app/service/comon/constants';\nimport { ComboLazyControl } from \"../../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/breadcrumb\";\nimport * as i5 from \"primeng/inputtext\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"../../../common-module/table/table.component\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/card\";\nimport * as i10 from \"primeng/dialog\";\nimport * as i11 from \"primeng/multiselect\";\nimport * as i12 from \"primeng/inputswitch\";\nimport * as i13 from \"primeng/radiobutton\";\nimport * as i14 from \"src/app/service/rating-plan/RatingPlanService\";\nimport * as i15 from \"../../../../service/account/AccountService\";\nfunction CreatePlanComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.tranService.translate(\"ratingPlan.error.lengthError_64\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.tranService.translate(\"ratingPlan.error.characterError_code\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.tranService.translate(\"ratingPlan.error.existedCodeError\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.tranService.translate(\"ratingPlan.error.lengthError_255\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.tranService.translate(\"global.message.wrongFormatName\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.tranService.translate(\"ratingPlan.error.existedNameError\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.tranService.translate(\"ratingPlan.error.lengthError_64\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.tranService.translate(\"ratingPlan.error.characterError_code\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.tranService.translate(\"ratingPlan.error.lengthError_255\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nconst _c0 = function () {\n  return {\n    value: 0\n  };\n};\nfunction CreatePlanComponent_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction CreatePlanComponent_div_90_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r58 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 104)(1, \"p-radioButton\", 105);\n    i0.ɵɵlistener(\"ngModelChange\", function CreatePlanComponent_div_90_Template_p_radioButton_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r58);\n      const ctx_r57 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r57.selectedPaidCategory = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 106);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const paidType_r56 = ctx.$implicit;\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"inputId\", paidType_r56.key)(\"value\", paidType_r56.key)(\"ngModel\", ctx_r16.selectedPaidCategory);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", paidType_r56.key);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(paidType_r56.name);\n  }\n}\nfunction CreatePlanComponent_div_107_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r17.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_115_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r18.tranService.translate(\"ratingPlan.cycle.day\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r19.tranService.translate(\"ratingPlan.cycle.month\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r20.tranService.translate(\"ratingPlan.text.dayMonth\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r21.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r22.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_123_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction CreatePlanComponent_div_135_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r24.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_136_p_multiSelect_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r62 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-multiSelect\", 111);\n    i0.ɵɵlistener(\"onChange\", function CreatePlanComponent_div_136_p_multiSelect_6_Template_p_multiSelect_onChange_0_listener() {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r61 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r61.changeProvince());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r59 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"options\", ctx_r59.provinces)(\"showToggleAll\", false)(\"placeholder\", ctx_r59.placeHolder.provinceCode);\n  }\n}\nfunction CreatePlanComponent_div_136_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r60 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r60.provinceInfo);\n  }\n}\nfunction CreatePlanComponent_div_136_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"label\", 107);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 10);\n    i0.ɵɵtext(4, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 108);\n    i0.ɵɵtemplate(6, CreatePlanComponent_div_136_p_multiSelect_6_Template, 1, 3, \"p-multiSelect\", 109);\n    i0.ɵɵtemplate(7, CreatePlanComponent_div_136_span_7_Template, 2, 1, \"span\", 100);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"div\", 110);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r25.tranService.translate(\"ratingPlan.label.province\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.userType == ctx_r25.allUserType.ADMIN);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.userType != ctx_r25.allUserType.ADMIN);\n  }\n}\nfunction CreatePlanComponent_div_140_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r26.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_141_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 112)(1, \"label\", 113);\n    i0.ɵɵlistener(\"click\", function CreatePlanComponent_div_141_Template_label_click_1_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r63 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r63.openDialogAddCustomerAccount());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r27.tranService.translate(\"account.label.addCustomerAccount\"));\n  }\n}\nfunction CreatePlanComponent_div_158_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r28.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_159_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r29.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_160_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r30.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction CreatePlanComponent_div_173_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r31.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_174_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r32.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_175_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r33.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction CreatePlanComponent_div_185_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r34.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_186_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r35 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r35.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction CreatePlanComponent_div_195_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r36 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r36.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_196_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r37 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r37.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction CreatePlanComponent_div_218_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r38 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r38.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_219_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r39.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_220_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r40.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction CreatePlanComponent_div_222_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r41.tranService.translate(\"ratingPlan.error.requiredError\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_223_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r42.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_224_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r43 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r43.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction CreatePlanComponent_div_239_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r44 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r44.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_240_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r45 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r45.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction CreatePlanComponent_div_242_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r46 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r46.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_243_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r47 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r47.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction CreatePlanComponent_div_255_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r48 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r48.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_256_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r49 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r49.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction CreatePlanComponent_div_268_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r50 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r50.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_269_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r51 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r51.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction CreatePlanComponent_div_281_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r52 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r52.tranService.translate(\"ratingPlan.error.lengthError_number\"), \" \");\n  }\n}\nfunction CreatePlanComponent_div_282_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r53.tranService.translate(\"global.message.min\", i0.ɵɵpureFunction0(1, _c0)), \" \");\n  }\n}\nfunction CreatePlanComponent_span_302_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r66 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 94)(1, \"p-multiSelect\", 114);\n    i0.ɵɵlistener(\"ngModelChange\", function CreatePlanComponent_span_302_Template_p_multiSelect_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r66);\n      const ctx_r65 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r65.searchInfoUser.provinceCode = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 115);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r54 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx_r54.searchInfoUser.provinceCode)(\"options\", ctx_r54.listProvince);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r54.tranService.translate(\"ratingPlan.label.province\"));\n  }\n}\nfunction CreatePlanComponent_span_303_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r55 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r55.tranService.translate(\"account.label.province\"), \": \", ctx_r55.provinceInfo, \"\");\n  }\n}\nconst _c1 = function () {\n  return {\n    width: \"850px\"\n  };\n};\nconst _c2 = function () {\n  return [5, 10, 20, 25, 50];\n};\nexport class CreatePlanComponent extends ComponentBase {\n  constructor(ratingPlanService, accountService, formBuilder, injector) {\n    super(injector);\n    this.ratingPlanService = ratingPlanService;\n    this.accountService = accountService;\n    this.formBuilder = formBuilder;\n    this.isPlanCodeExisted = true;\n    this.isPlanNameExisted = true;\n    this.isPlanCodeValid = false;\n    this.isPlaneNameValid = false;\n    this.isDispatchCodeValid = false;\n    this.isCustomerTypeValid = false;\n    this.isSubscriptionFeeValid = false;\n    this.isSubscriptionTypeValid = false; // Đã set default\n    this.isPlanScopeValid = false;\n    this.isProvinceCodeValid = false;\n    this.isPlanCycleValid = false;\n    this.isDurationValid = false;\n    this.isDescriptionValid = false;\n    this.isFreeDataValid = false;\n    this.isLimitInsideSMSFreeValid = false;\n    this.isLimitOutsideSMSFreeValid = false;\n    this.isFeePerUnitNumberatorValid = false;\n    this.isFeePerUnitDenominatorValid = false;\n    this.isSqueezeSpeedNumberatorValid = false;\n    this.isSqueezeSpeedDenominatorValid = false;\n    this.isFeePerInsideSMSValid = false;\n    this.isFeePerOutsideSMSValid = false;\n    this.isMaxFeeValid = false;\n    this.isDataMaxValid = false;\n    this.isShowDialogAddCustomerAccount = false;\n    this.selectItemsUser = [];\n    this.selectItemsUserOld = [{\n      id: -1,\n      provinceCode: \"\"\n    }];\n    this.controlComboSelect = new ComboLazyControl();\n    this.isProvince = false;\n    this.isCustomer = false;\n    this.isFlexible = false;\n    this.paidCategories = [{\n      name: 'Trả trước',\n      key: '1'\n    }, {\n      name: 'Trả sau',\n      key: '0'\n    }];\n    this.selectedPaidCategory = null;\n    this.placeHolderDescription = this.tranService.translate(\"groupSim.placeHolder.description\");\n    this.provinceInfo = \"\";\n    this.userType = this.sessionService.userInfo.type;\n    this.allUserType = CONSTANTS.USER_TYPE;\n    this.placeHolder = {\n      planCode: this.tranService.translate(\"ratingPlan.placeHolder.planCode\"),\n      planName: this.tranService.translate(\"ratingPlan.placeHolder.planeName\"),\n      dispatchCode: this.tranService.translate(\"ratingPlan.placeHolder.dispatchCode\"),\n      customerType: this.tranService.translate(\"ratingPlan.placeHolder.customerType\"),\n      description: this.tranService.translate(\"ratingPlan.placeHolder.description\"),\n      subscriptionFee: this.tranService.translate(\"ratingPlan.placeHolder.subscriptionFee\"),\n      subscriptionType: this.tranService.translate(\"ratingPlan.placeHolder.subscriptionType\"),\n      planScope: this.tranService.translate(\"ratingPlan.placeHolder.planScope\"),\n      provinceCode: this.tranService.translate(\"ratingPlan.placeHolder.provinceCode\"),\n      planCycle: this.tranService.translate(\"ratingPlan.placeHolder.planCycle\"),\n      duration: this.tranService.translate(\"ratingPlan.placeHolder.duration\"),\n      freeData: this.tranService.translate(\"ratingPlan.placeHolder.freeData\"),\n      insideSMSFree: this.tranService.translate(\"ratingPlan.placeHolder.insideSMSFree\"),\n      outsideSMSFree: this.tranService.translate(\"ratingPlan.placeHolder.outsideSMSFree\"),\n      feePerUnit: this.tranService.translate(\"ratingPlan.placeHolder.feePerUnit\"),\n      squeezedSpeed: this.tranService.translate(\"ratingPlan.placeHolder.squeezeSpeed\"),\n      feePerInsideSMS: this.tranService.translate(\"ratingPlan.placeHolder.feePerInsideSMS\"),\n      feePerOutsideSMS: this.tranService.translate(\"ratingPlan.placeHolder.feePerOutsideSMS\"),\n      maxFee: this.tranService.translate(\"ratingPlan.placeHolder.maxFee\"),\n      dataMax: this.tranService.translate(\"ratingPlan.placeHolder.dataMax\")\n    };\n    this.createPlanForm = new FormGroup({\n      status: new FormControl(2),\n      code: new FormControl(\"\", [Validators.required, Validators.maxLength(64), this.customCodeCharacterValidator()], [this.planCodeValidator()]),\n      name: new FormControl(\"\", [Validators.required, Validators.maxLength(255), this.customNameCharacterValidator()], [this.planNameValidator()]),\n      dispatchCode: new FormControl(\"\", [Validators.required, Validators.maxLength(64), this.customCodeCharacterValidator()]),\n      customerType: new FormControl(\"\", [Validators.required]),\n      subscriptionFee: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\n      paidType: new FormControl(\"\", [Validators.required]),\n      ratingScope: new FormControl(\"0\", [Validators.required]),\n      provinceCode: new FormControl({\n        value: this.userType == CONSTANTS.USER_TYPE.ADMIN ? \"\" : [this.sessionService.userInfo.provinceCode],\n        disabled: !this.isProvince\n      }, [Validators.required]),\n      userIds: new FormControl(),\n      cycleTimeUnit: new FormControl(\"\", [Validators.required]),\n      cycleInterval: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\n      reload: new FormControl(0),\n      description: new FormControl(\"\", [Validators.maxLength(255)]),\n      limitDataUsage: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\n      limitSmsInside: new FormControl(0, [Validators.max(9999999999), Validators.min(0)]),\n      limitSmsOutside: new FormControl(0, [Validators.max(9999999999), Validators.min(0)]),\n      dataMax: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\n      flexible: new FormControl(0),\n      feePerDataUnit: new FormControl({\n        value: 0,\n        disabled: !this.isFlexible\n      }, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\n      dataRoundUnit: new FormControl({\n        value: 0,\n        disabled: !this.isFlexible\n      }, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\n      downSpeed: new FormControl({\n        value: 0,\n        disabled: !this.isFlexible\n      }, [Validators.max(9999999999), Validators.min(0)]),\n      squeezedSpeed: new FormControl({\n        value: 0,\n        disabled: !this.isFlexible\n      }, [Validators.max(9999999999), Validators.min(0)]),\n      feeSmsInside: new FormControl({\n        value: 0,\n        disabled: !this.isFlexible\n      }, [Validators.max(9999999999), Validators.min(0)]),\n      feeSmsOutside: new FormControl({\n        value: 0,\n        disabled: !this.isFlexible\n      }, [Validators.max(9999999999), Validators.min(0)]),\n      maximumFee: new FormControl({\n        value: 0,\n        disabled: !this.isFlexible\n      }, [Validators.max(9999999999), Validators.min(0)]),\n      uploadSpeed: new FormControl({\n        value: 0,\n        disabled: !this.isFlexible\n      })\n    });\n  }\n  customCodeCharacterValidator() {\n    return control => {\n      const value = control.value;\n      const isValid = /^[a-zA-Z0-9\\-_]*$/.test(value);\n      return isValid ? null : {\n        'invalidCharacters': {\n          value\n        }\n      };\n    };\n  }\n  customNameCharacterValidator() {\n    return control => {\n      const value = control.value;\n      if (value == '') {\n        return null;\n      }\n      const isValid = /^[a-zA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơỲỴÝỳỵỷỹƯứừữựụợ́̉̃À-ỹ0-9 _-]+$/.test(value);\n      return isValid ? null : {\n        'invalidCharacters': {\n          value\n        }\n      };\n    };\n  }\n  checkCodeExisted(query) {\n    return new Observable(observer => {\n      this.ratingPlanService.checkingPlanCodeExisted(query, response => {\n        observer.next(response);\n        observer.complete();\n      });\n    });\n  }\n  checkNameExisted(query) {\n    return new Observable(observer => {\n      this.ratingPlanService.checkingPlanNameExisted(query, response => {\n        observer.next(response);\n        observer.complete();\n      });\n    });\n  }\n  planCodeValidator() {\n    return control => {\n      return control.valueChanges.pipe(debounceTime(500), switchMap(value => this.checkCodeExisted({\n        code: value\n      })), take(1), map(result => {\n        // console.log('Map result:', result);\n        if (result === 0) {\n          this.isPlanCodeExisted = false;\n          return null;\n        } else {\n          this.isPlanCodeExisted = true;\n          return {\n            'exited': true\n          };\n        }\n      }));\n    };\n  }\n  planNameValidator() {\n    return control => {\n      return control.valueChanges.pipe(debounceTime(500), switchMap(value => this.checkNameExisted({\n        name: value\n      })), take(1), map(result => {\n        // console.log('Map result:', result);\n        if (result === 0) {\n          this.isPlanNameExisted = false;\n          return null;\n        } else {\n          this.isPlanNameExisted = true;\n          return {\n            'exited': true\n          };\n        }\n      }));\n    };\n  }\n  blockMinus(event) {\n    const invalidChars = ['-', '+', ',', '.', 'e', 'E', 'r', 'R']; // Danh sách các ký tự không cho phép\n    if (invalidChars.includes(event.key)) {\n      event.preventDefault();\n    }\n    // if (event.key === '-' || event.key ==='+' || event.key === ',' || event.key === '.') {\n    //   event.preventDefault();\n    // }\n  }\n\n  checkInputValue(event) {\n    const input = event.target;\n    input.value = input.value.replace(/[^0-9]/g, ''); // Chỉ cho phép nhập số\n  }\n\n  onDropdownChange(event) {\n    if (event.value == 0) {\n      this.isProvince = false;\n      this.isCustomer = false;\n      this.createPlanForm.get('provinceCode').disable({\n        emitEvent: false\n      });\n      // this.createPlanForm.get('userIds').disable({ emitEvent: false });\n    } else if (event.value == 1) {\n      // gói cước loại khách hàng\n      this.isProvince = true;\n      this.isCustomer = false;\n      this.createPlanForm.get('provinceCode').enable({\n        emitEvent: false\n      });\n      // this.createPlanForm.get('userIds').enable({ emitEvent: false });\n      this.changeProvince();\n    } else if (event.value == 2) {\n      // gói cước loại tỉnh/thành phố\n      this.isProvince = true;\n      this.isCustomer = false;\n      this.createPlanForm.get('provinceCode').enable({\n        emitEvent: false\n      });\n      // this.createPlanForm.get('userIds').disable({ emitEvent: false });\n      this.changeProvince();\n    }\n  }\n  onSwitchChange() {\n    // console.log(this.createPlanForm);\n    if (this.isFlexible) {\n      this.createPlanForm.get('feePerDataUnit').enable({\n        emitEvent: false\n      });\n      this.createPlanForm.get('dataRoundUnit').enable({\n        emitEvent: false\n      });\n      this.createPlanForm.get('downSpeed').enable({\n        emitEvent: false\n      });\n      this.createPlanForm.get('squeezedSpeed').enable({\n        emitEvent: false\n      });\n      this.createPlanForm.get('feeSmsInside').enable({\n        emitEvent: false\n      });\n      this.createPlanForm.get('feeSmsOutside').enable({\n        emitEvent: false\n      });\n      this.createPlanForm.get('maximumFee').enable({\n        emitEvent: false\n      });\n    } else {\n      this.createPlanForm.get('feePerDataUnit').disable({\n        emitEvent: false\n      });\n      this.createPlanForm.get('dataRoundUnit').disable({\n        emitEvent: false\n      });\n      this.createPlanForm.get('downSpeed').disable({\n        emitEvent: false\n      });\n      this.createPlanForm.get('squeezedSpeed').disable({\n        emitEvent: false\n      });\n      this.createPlanForm.get('feeSmsInside').disable({\n        emitEvent: false\n      });\n      this.createPlanForm.get('feeSmsOutside').disable({\n        emitEvent: false\n      });\n      this.createPlanForm.get('maximumFee').disable({\n        emitEvent: false\n      });\n    }\n  }\n  submitForm() {\n    this.messageCommonService.onload();\n    let me = this;\n    if (this.createPlanForm.valid) {\n      let data = {\n        ...this.createPlanForm.value\n      };\n      if (data.reload) {\n        data.reload = 1;\n      } else {\n        data.reload = 0;\n      }\n      if (data.flexible) {\n        data.flexible = 1;\n        data.uploadSpeed = data.downSpeed - data.squeezedSpeed;\n      } else {\n        data.flexible = 0;\n      }\n      if (this.selectItemsUser.length > 0) {\n        let provinceSelected = me.createPlanForm.get(\"provinceCode\").value;\n        let currentSelected = me.selectItemsUser.filter(el => provinceSelected.includes(el.provinceCode)).map(e => e.id);\n        data.userIds = currentSelected;\n      }\n      this.ratingPlanService.createRatingPlan(data, response => {\n        // console.log(response)\n        this.messageCommonService.success(this.tranService.translate('global.message.saveSuccess'));\n        this.router.navigate(['/plans']);\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n    }\n  }\n  onSubmitSearchUser() {\n    this.pageNumberAssign = 0;\n    this.searchUser(this.pageNumberAssign, this.pageSizeAssign, this.sortAssign, this.searchInfoUser);\n  }\n  ngOnInit() {\n    this.optionTableAddCustomerAccount = {\n      hasClearSelected: false,\n      hasShowIndex: true,\n      hasShowChoose: true,\n      hasShowToggleColumn: false\n    };\n    this.dataSetAssignPlan = {\n      content: [],\n      total: 0\n    };\n    this.searchInfoUser = {\n      username: null,\n      fullName: null,\n      email: null,\n      provinceCode: null\n    };\n    this.formSearchUser = this.formBuilder.group(this.searchInfoUser);\n    this.items = [{\n      label: this.tranService.translate(\"global.menu.ratingplanmgmt\"),\n      routerLink: '../'\n    }, {\n      label: this.tranService.translate(\"global.menu.listplan\"),\n      routerLink: '../'\n    }, {\n      label: this.tranService.translate(\"global.button.create\")\n    }];\n    this.columnsInfoUser = [{\n      name: this.tranService.translate(\"ratingPlan.label.username\"),\n      key: \"username\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.fullName\"),\n      key: \"fullName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.email\"),\n      key: \"email\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.province\"),\n      key: \"provinceName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }];\n    this.customerTypes = [{\n      id: \"1\",\n      name: this.tranService.translate(\"ratingPlan.customerType.personal\")\n    }, {\n      id: \"2\",\n      name: this.tranService.translate(\"ratingPlan.customerType.enterprise\")\n    }, {\n      id: \"0\",\n      name: this.tranService.translate(\"ratingPlan.customerType.agency\")\n    }\n    // ,\n    // {\n    //   id: \"0\",\n    //   name: this.tranService.translate(\"ratingPlan.customerType.agency\"),\n    // },\n    ];\n\n    this.ratingScopes = [{\n      id: \"0\",\n      name: this.tranService.translate(\"ratingPlan.ratingScope.nativeWide\")\n    }, {\n      id: \"2\",\n      name: this.tranService.translate(\"ratingPlan.ratingScope.province\")\n    }, {\n      id: \"1\",\n      name: this.tranService.translate(\"ratingPlan.ratingScope.customer\")\n    }];\n    this.cycles = [{\n      id: \"1\",\n      name: this.tranService.translate(\"ratingPlan.cycle.day\")\n    }, {\n      id: \"3\",\n      name: this.tranService.translate(\"ratingPlan.cycle.month\")\n    }];\n    this.accountService.getListProvince(data => {\n      let me = this;\n      this.provinces = data.map(el => {\n        if (el.code == me.sessionService.userInfo.provinceCode) {\n          me.provinceInfo = `${el.name} - ${el.code}`;\n        }\n        return {\n          code: el.code,\n          name: `${el.name} - ${el.code}`\n        };\n      });\n    });\n    this.selectedPaidCategory = this.paidCategories[0].key;\n    this.subPlanCode = this.createPlanForm.get('code').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('code').errors;\n      if (errors) {\n        this.isPlanCodeValid = true;\n      } else {\n        this.isPlanCodeValid = false;\n      }\n    });\n    this.subPlaneName = this.createPlanForm.get('name').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('name').errors;\n      if (errors) {\n        this.isPlaneNameValid = true;\n      } else {\n        this.isPlaneNameValid = false;\n      }\n    });\n    this.subDispatchCode = this.createPlanForm.get('dispatchCode').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('dispatchCode').errors;\n      if (errors) {\n        this.isDispatchCodeValid = true;\n      } else {\n        this.isDispatchCodeValid = false;\n      }\n    });\n    this.subCustomerType = this.createPlanForm.get('customerType').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('customerType').errors;\n      if (errors) {\n        this.isCustomerTypeValid = true;\n      } else {\n        this.isCustomerTypeValid = false;\n      }\n    });\n    this.subSubscriptionFee = this.createPlanForm.get('subscriptionFee').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('subscriptionFee').errors;\n      if (errors) {\n        this.isSubscriptionFeeValid = true;\n      } else {\n        this.isSubscriptionFeeValid = false;\n      }\n    });\n    this.subSubscriptionType = this.createPlanForm.get('paidType').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('paidType').errors;\n      if (errors) {\n        this.isSubscriptionTypeValid = true;\n      } else {\n        this.isSubscriptionTypeValid = false;\n      }\n    });\n    this.subPlanScope = this.createPlanForm.get('ratingScope').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('ratingScope').errors;\n      if (errors) {\n        this.isPlanScopeValid = true;\n      } else {\n        this.isPlanScopeValid = false;\n      }\n    });\n    this.subProvinceCode = this.createPlanForm.get('provinceCode').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('provinceCode').errors;\n      if (errors) {\n        this.isProvinceCodeValid = true;\n      } else {\n        this.isProvinceCodeValid = false;\n      }\n    });\n    this.subPlanCycle = this.createPlanForm.get('cycleTimeUnit').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('cycleTimeUnit').errors;\n      if (errors) {\n        this.isPlanCycleValid = true;\n      } else {\n        this.isPlanCycleValid = false;\n      }\n    });\n    this.subDuration = this.createPlanForm.get('cycleInterval').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('cycleInterval').errors;\n      if (errors) {\n        this.isDurationValid = true;\n      } else {\n        this.isDurationValid = false;\n      }\n    });\n    this.subDescription = this.createPlanForm.get('description').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('description').errors;\n      if (errors) {\n        this.isDescriptionValid = true;\n      } else {\n        this.isDescriptionValid = false;\n      }\n    });\n    this.subFreeData = this.createPlanForm.get('limitDataUsage').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('limitDataUsage').errors;\n      if (errors) {\n        this.isFreeDataValid = true;\n      } else {\n        this.isFreeDataValid = false;\n      }\n    });\n    this.subLimitInsideSMSFree = this.createPlanForm.get('limitSmsInside').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('limitSmsInside').errors;\n      if (errors) {\n        this.isLimitInsideSMSFreeValid = true;\n      } else {\n        this.isLimitInsideSMSFreeValid = false;\n      }\n    });\n    this.subLimitOutsideSMSFree = this.createPlanForm.get('limitSmsOutside').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('limitSmsOutside').errors;\n      if (errors) {\n        this.isLimitOutsideSMSFreeValid = true;\n      } else {\n        this.isLimitOutsideSMSFreeValid = false;\n      }\n    });\n    this.subDataMax = this.createPlanForm.get('dataMax').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('dataMax').errors;\n      if (errors) {\n        this.isDataMaxValid = true;\n      } else {\n        this.isDataMaxValid = false;\n      }\n    });\n    this.subFeePerUnitNumberator = this.createPlanForm.get('feePerDataUnit').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('feePerDataUnit').errors;\n      if (errors) {\n        this.isFeePerUnitNumberatorValid = true;\n      } else {\n        this.isFeePerUnitNumberatorValid = false;\n      }\n    });\n    this.subFeePerUnitDenominator = this.createPlanForm.get('dataRoundUnit').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('dataRoundUnit').errors;\n      if (errors) {\n        this.isFeePerUnitDenominatorValid = true;\n      } else {\n        this.isFeePerUnitDenominatorValid = false;\n      }\n    });\n    this.subSqueezeSpeedNumberator = this.createPlanForm.get('downSpeed').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('downSpeed').errors;\n      if (errors) {\n        this.isSqueezeSpeedNumberatorValid = true;\n      } else {\n        this.isSqueezeSpeedNumberatorValid = false;\n      }\n    });\n    this.subSqueezeSpeedDenominator = this.createPlanForm.get('squeezedSpeed').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('squeezedSpeed').errors;\n      if (errors) {\n        this.isSqueezeSpeedDenominatorValid = true;\n      } else {\n        this.isSqueezeSpeedDenominatorValid = false;\n      }\n    });\n    this.subFeePerInsideSMS = this.createPlanForm.get('feeSmsInside').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('feeSmsInside').errors;\n      if (errors) {\n        this.isFeePerInsideSMSValid = true;\n      } else {\n        this.isFeePerInsideSMSValid = false;\n      }\n    });\n    this.subFeePerOutsideSMS = this.createPlanForm.get('feeSmsOutside').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('feeSmsOutside').errors;\n      if (errors) {\n        this.isFeePerOutsideSMSValid = true;\n      } else {\n        this.isFeePerOutsideSMSValid = false;\n      }\n    });\n    this.subMaxFee = this.createPlanForm.get('maximumFee').statusChanges.subscribe(() => {\n      const errors = this.createPlanForm.get('maximumFee').errors;\n      if (errors) {\n        this.isMaxFeeValid = true;\n      } else {\n        this.isMaxFeeValid = false;\n      }\n    });\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n  }\n  ngOnDestroy() {\n    this.subPlanCode.unsubscribe();\n    this.subPlaneName.unsubscribe();\n    this.subDispatchCode.unsubscribe();\n    this.subCustomerType.unsubscribe();\n    this.subSubscriptionFee.unsubscribe();\n    this.subSubscriptionType.unsubscribe();\n    this.subPlanScope.unsubscribe();\n    this.subProvinceCode.unsubscribe();\n    this.subPlanCycle.unsubscribe();\n    this.subDuration.unsubscribe();\n    this.subDescription.unsubscribe();\n    this.subFreeData.unsubscribe();\n    this.subLimitInsideSMSFree.unsubscribe();\n    this.subLimitOutsideSMSFree.unsubscribe();\n    this.subFeePerUnitNumberator.unsubscribe();\n    this.subFeePerUnitDenominator.unsubscribe();\n    this.subSqueezeSpeedNumberator.unsubscribe();\n    this.subSqueezeSpeedDenominator.unsubscribe();\n    this.subFeePerInsideSMS.unsubscribe();\n    this.subFeePerOutsideSMS.unsubscribe();\n    this.subMaxFee.unsubscribe();\n  }\n  openDialogAddCustomerAccount() {\n    let provincesSelected = this.createPlanForm.get('provinceCode').value;\n    this.listProvince = this.provinces.filter(prov => provincesSelected.includes(prov.code));\n    // this.selectItemsUser = [];\n    if (this.pageNumberAssign == null) {\n      this.pageNumberAssign = 0;\n    }\n    if (this.pageSizeAssign == null) {\n      this.pageSizeAssign = 10;\n    }\n    if (this.sortAssign == null) {\n      this.sortAssign = \"id,desc\";\n    }\n    this.searchInfoUser.provinceCode = this.listProvince.map(e => e.code);\n    this.searchUser(this.pageNumberAssign, this.pageSizeAssign, this.sortAssign, this.searchInfoUser);\n    this.isShowDialogAddCustomerAccount = true;\n  }\n  searchUser(page, limit, sort, params) {\n    let me = this;\n    this.pageNumberAssign = page;\n    this.pageSizeAssign = limit;\n    if (sort == null || sort == undefined) {\n      this.sortAssign = \"id,desc\";\n      sort = \"id,desc\";\n    } else {\n      this.sortAssign = sort;\n    }\n    let dataParams = {\n      page,\n      size: limit,\n      sort,\n      provinceCode: this.listProvince.map(e => e.code)\n    };\n    Object.keys(this.searchInfoUser).forEach(key => {\n      if (this.searchInfoUser[key] != null) {\n        dataParams[key] = this.searchInfoUser[key];\n      }\n    });\n    if (this.searchInfoUser.provinceCode == null) {\n      dataParams.provinceCode = this.listProvince.map(e => e.code);\n    }\n    this.selectItemsUserOld = [...this.selectItemsUser];\n    this.ratingPlanService.getUserToAddAccount(dataParams, response => {\n      me.dataSetAssignPlan = {\n        content: response.content,\n        total: response.totalElements\n      };\n      this.selectItemsUser = [...this.selectItemsUserOld];\n    });\n  }\n  changeProvince() {\n    let provinceSelected = this.createPlanForm.get(\"provinceCode\").value;\n    let ratingScope = this.createPlanForm.get(\"ratingScope\").value;\n    if (ratingScope == '1') {\n      if (provinceSelected.length > 0) {\n        this.isCustomer = true;\n      } else {\n        this.isCustomer = false;\n      }\n    }\n  }\n  static {\n    this.ɵfac = function CreatePlanComponent_Factory(t) {\n      return new (t || CreatePlanComponent)(i0.ɵɵdirectiveInject(RatingPlanService), i0.ɵɵdirectiveInject(AccountService), i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CreatePlanComponent,\n      selectors: [[\"app-create-plan\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 307,\n      vars: 175,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [\"styleClass\", \"responsive-form-plans\", 1, \"p-4\"], [\"action\", \"\", 3, \"formGroup\", \"submit\"], [1, \"pt-0\", \"shadow-2\", \"border-round-md\", \"m-1\", \"flex\", \"p-fluid\", \"p-formgrid\", \"grid\"], [1, \"col-6\", \"grid-col\"], [1, \"grid\", \"px-4\", \"pt-4\", \"flex\", \"flex-row\", \"flex-nowrap\", \"responsive-size-input\"], [\"htmlFor\", \"code\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"140px\"], [1, \"text-red-500\"], [1, \"col-12\", \"md:col-10\", \"flex-1\"], [\"formControlName\", \"code\", \"pInputText\", \"\", \"id\", \"code\", \"type\", \"text\", 3, \"placeholder\"], [1, \"grid\", \"px-4\", \"flex\", \"flex-row\", \"flex-nowrap\", \"mb-3\", \"responsive-size-input\"], [2, \"min-width\", \"140px\"], [1, \"col-11\", \"md:col-11\", \"py-0\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [1, \"field\", \"grid\", \"px-4\", \"flex\", \"flex-row\", \"flex-nowrap\", \"responsive-size-input\"], [\"htmlFor\", \"name\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"140px\"], [\"formControlName\", \"name\", \"pInputText\", \"\", \"id\", \"name\", \"type\", \"text\", 3, \"placeholder\"], [\"htmlFor\", \"dispatchCode\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"140px\"], [\"formControlName\", \"dispatchCode\", \"pInputText\", \"\", \"id\", \"dispatchCode\", \"type\", \"text\", 3, \"placeholder\"], [\"htmlFor\", \"customerType\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"140px\"], [\"formControlName\", \"customerType\", \"optionLabel\", \"name\", \"optionValue\", \"id\", \"autoDisplayFirst\", \"false\", 3, \"options\", \"placeholder\"], [1, \"grid\", \"px-4\", \"flex\", \"flex-row\", \"flex-nowrap\", \"responsive-size-input\"], [\"htmlFor\", \"description\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"140px\"], [\"formControlName\", \"description\", \"id\", \"description\", \"rows\", \"4\", \"cols\", \"30\", \"pInputText\", \"\", 3, \"placeholder\"], [1, \"field\", \"grid\", \"px-4\", \"pt-4\", \"flex\", \"flex-row\", \"flex-nowrap\", \"responsive-size-input\"], [\"htmlFor\", \"subscriptionFee\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"140px\"], [\"formControlName\", \"subscriptionFee\", \"pInputText\", \"\", \"id\", \"subscriptionFee\", \"type\", \"number\", 3, \"placeholder\", \"keydown\", \"input\"], [1, \"my-auto\", \"pr-1\", 2, \"min-width\", \"90px\"], [1, \"field\", \"grid\", \"px-4\", \"pb-2\", \"custom-responsive-field\"], [1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", \"flex\", \"flex-row\", \"align-items-center\", \"radio-group\", \"wrapper\"], [\"class\", \"field-checkbox my-auto\", \"style\", \"min-width: 140px; min-height:35px;\", 4, \"ngFor\", \"ngForOf\"], [1, \"switch-group-wrapper\", \"flex\", \"flex-row\", \"align-items-center\", \"mt-2\"], [\"htmlFor\", \"reload\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", \"responsive-switch-label\", 2, \"min-width\", \"130px\"], [\"formControlName\", \"reload\", 1, \"flex\", \"align-items-center\"], [\"htmlFor\", \"cycle\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"140px\"], [\"formControlName\", \"cycleTimeUnit\", \"optionLabel\", \"name\", \"optionValue\", \"id\", \"autoDisplayFirst\", \"true\", 3, \"options\", \"placeholder\"], [\"htmlFor\", \"cycleInterval\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"140px\"], [\"formControlName\", \"cycleInterval\", \"pInputText\", \"\", \"id\", \"cycleInterval\", \"type\", \"number\", 3, \"placeholder\", \"keydown\", \"input\"], [\"class\", \"my-auto pr-1\", \"style\", \"min-width: 90px;\", 4, \"ngIf\"], [\"htmlFor\", \"ratingScope\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"140px\"], [\"formControlName\", \"ratingScope\", \"optionLabel\", \"name\", \"optionValue\", \"id\", \"autoDisplayFirst\", \"false\", 3, \"options\", \"placeholder\", \"onChange\"], [\"class\", \"field grid px-4 flex flex-row flex-nowrap responsive-size-input\", 4, \"ngIf\"], [1, \"grid\", \"px-4\", \"flex\", \"flex-row\", \"flex-nowrap\", \"mb-3\"], [\"class\", \"field grid px-4 flex flex-row flex-nowrap3 responsive-size-input\", 4, \"ngIf\"], [1, \"ml-2\"], [1, \"flex-1\"], [\"htmlFor\", \"limitDataUsage\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"170px\"], [\"formControlName\", \"limitDataUsage\", \"pInputText\", \"\", \"id\", \"limitDataUsage\", \"type\", \"number\", 3, \"placeholder\", \"keydown\", \"input\"], [1, \"my-auto\", \"pr-1\", 2, \"min-width\", \"40px\"], [2, \"min-width\", \"170px\"], [\"formControlName\", \"dataMax\", \"pInputText\", \"\", \"id\", \"dataMax\", \"type\", \"number\", 3, \"placeholder\", \"keydown\", \"input\"], [\"htmlFor\", \"insideSMSFree\", 1, \"col-12\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"170px\"], [\"formControlName\", \"limitSmsInside\", \"pInputText\", \"\", \"id\", \"insideSMSFree\", \"type\", \"number\", 3, \"placeholder\", \"keydown\", \"input\"], [\"htmlFor\", \"outsideSMSFree\", 1, \"col-12\", \"mb-2\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"170px\"], [\"formControlName\", \"limitSmsOutside\", \"pInputText\", \"\", \"id\", \"outsideSMSFree\", \"type\", \"number\", 3, \"placeholder\", \"keydown\", \"input\"], [1, \"flex\", \"flex-row\", \"gap-3\", \"ml-2\", \"mt-4\", \"mb-3\"], [1, \"m-0\"], [\"formControlName\", \"flexible\", 1, \"\", 3, \"ngModel\", \"ngModelChange\", \"onChange\"], [1, \"pt-0\", \"shadow-2\", \"border-round-md\", \"mb-4\", \"m-1\", \"flex\", \"p-fluid\", \"p-formgrid\", \"flex-column\", \"grid\"], [\"htmlFor\", \"feePerDataUnit\", 1, \"col-12\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"190px\"], [\"formControlName\", \"feePerDataUnit\", \"pInputText\", \"\", \"id\", \"feePerDataUnit\", \"type\", \"number\", 1, \"feePerDataUnit\", 3, \"disabled\", \"placeholder\", \"keydown\", \"input\"], [1, \"my-auto\", \"mx-auto\", 2, \"min-width\", \"10px\"], [\"formControlName\", \"dataRoundUnit\", \"pInputText\", \"\", \"id\", \"dataRoundUnit\", \"type\", \"number\", 3, \"disabled\", \"placeholder\", \"keydown\", \"input\"], [1, \"my-auto\", 2, \"min-width\", \"40px\"], [1, \"col-12\", \"md:col-2\", \"md:mb-0\", \"p-0\", \"responsive-div-error-2\", 2, \"min-width\", \"190px\"], [1, \"col-11\", \"md:col-5\", \"py-0\"], [1, \"col-11\", \"md:col-5\", \"py-0\", \"responsive-error-2\"], [\"htmlFor\", \"downSpeed\", 1, \"col-12\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"190px\"], [\"formControlName\", \"downSpeed\", \"pInputText\", \"\", \"id\", \"downSpeed\", \"type\", \"number\", 3, \"disabled\", \"placeholder\", \"keydown\", \"input\"], [\"formControlName\", \"squeezedSpeed\", \"pInputText\", \"\", \"id\", \"squeezedSpeed\", \"type\", \"number\", 3, \"disabled\", \"placeholder\", \"keydown\", \"input\"], [1, \"field\", \"grid\", \"px-4\", \"flex\", \"flex-row\", \"flex-nowrap\", \"responsive-div\"], [\"htmlFor\", \"feeSmsInside\", 1, \"col-12\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"190px\"], [\"formControlName\", \"feeSmsInside\", \"pInputText\", \"\", \"id\", \"feeSmsInside\", \"type\", \"number\", 3, \"disabled\", \"placeholder\", \"keydown\", \"input\"], [1, \"col-12\", \"md:col-2\", \"md:mb-0\", \"p-0\", \"responsive-div-error\", 2, \"min-width\", \"190px\"], [1, \"col-11\", \"md:col-11\", \"py-0\", \"responsive-error-1\"], [\"htmlFor\", \"feeSmsOutside\", 1, \"col-12\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"190px\"], [1, \"col-12\", \"md:col-10\", \"flex-1\", \"feeSmsOutside\"], [\"formControlName\", \"feeSmsOutside\", \"pInputText\", \"\", \"id\", \"feeSmsOutside\", \"type\", \"number\", 3, \"disabled\", \"placeholder\", \"keydown\", \"input\"], [1, \"field\", \"grid\", \"px-4\", \"pb-3\", \"flex\", \"flex-row\", \"flex-nowrap\", \"responsive-div\"], [\"htmlFor\", \"maximumFee\", 1, \"col-12\", \"md:col-2\", \"md:mb-0\", 2, \"min-width\", \"190px\"], [1, \"col-12\", \"md:col-10\", \"flex-1\", \"maximumFee\"], [\"formControlName\", \"maximumFee\", \"pInputText\", \"\", \"id\", \"maximumFee\", \"type\", \"number\", 3, \"disabled\", \"placeholder\", \"keydown\", \"input\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"gap-3\", \"p-2\"], [\"routerLink\", \"/plans\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-secondary\", \"p-button-outlined\", 3, \"label\"], [\"pButton\", \"\", \"type\", \"submit\", 1, \"p-button-info\", 3, \"label\", \"disabled\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"flex\", \"justify-content-center\", \"dialog-push-group\"], [3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"grid\"], [1, \"col-3\"], [1, \"p-float-label\"], [\"pInputText\", \"\", \"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"username\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"username\"], [\"pInputText\", \"\", \"pInputText\", \"\", \"id\", \"fullName\", \"formControlName\", \"fullName\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"fullName\"], [\"class\", \"p-float-label\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"col-3\", \"pb-0\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"submit\"], [3, \"fieldId\", \"pageNumber\", \"pageSize\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"rowsPerPageOptions\", \"scrollHeight\", \"sort\", \"params\", \"selectItemsChange\"], [1, \"field-checkbox\", \"my-auto\", 2, \"min-width\", \"140px\", \"min-height\", \"35px\"], [\"formControlName\", \"paidType\", \"name\", \"paidType\", 3, \"inputId\", \"value\", \"ngModel\", \"ngModelChange\"], [1, \"ml-2\", 3, \"for\"], [\"htmlFor\", \"provinceCode\", 1, \"col-fixed\", 2, \"min-width\", \"140px\"], [1, \"col\", 2, \"max-width\", \"calc(100% - 230px)\"], [\"formControlName\", \"provinceCode\", \"display\", \"chip\", \"optionLabel\", \"name\", \"optionValue\", \"code\", \"autoDisplayFirst\", \"false\", 3, \"options\", \"showToggleAll\", \"placeholder\", \"onChange\", 4, \"ngIf\"], [1, \"col-fixed\", 2, \"min-width\", \"90px\"], [\"formControlName\", \"provinceCode\", \"display\", \"chip\", \"optionLabel\", \"name\", \"optionValue\", \"code\", \"autoDisplayFirst\", \"false\", 3, \"options\", \"showToggleAll\", \"placeholder\", \"onChange\"], [1, \"field\", \"grid\", \"px-4\", \"flex\", \"flex-row\", \"flex-nowrap3\", \"responsive-size-input\"], [\"htmlFor\", \"roles\", 1, \"col-fixed\", 2, \"min-width\", \"140px\", \"cursor\", \"pointer\", \"text-decoration\", \"underline\", \"color\", \"blue\", \"transition\", \"color 0.3s\", 3, \"click\"], [\"styleClass\", \"w-full\", \"id\", \"provinceCode\", \"optionLabel\", \"name\", \"optionValue\", \"code\", \"formControlName\", \"provinceCode\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"ngModelChange\"], [\"for\", \"provinceCode\"]],\n      template: function CreatePlanComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"p-card\", 4)(6, \"form\", 5);\n          i0.ɵɵlistener(\"submit\", function CreatePlanComponent_Template_form_submit_6_listener() {\n            return ctx.submitForm();\n          });\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8)(10, \"label\", 9);\n          i0.ɵɵtext(11);\n          i0.ɵɵelementStart(12, \"span\", 10);\n          i0.ɵɵtext(13, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 11);\n          i0.ɵɵelement(15, \"input\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 13);\n          i0.ɵɵelement(17, \"div\", 14);\n          i0.ɵɵelementStart(18, \"div\", 15);\n          i0.ɵɵtemplate(19, CreatePlanComponent_div_19_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(20, CreatePlanComponent_div_20_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(21, CreatePlanComponent_div_21_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(22, CreatePlanComponent_div_22_Template, 2, 1, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 17)(24, \"label\", 18);\n          i0.ɵɵtext(25);\n          i0.ɵɵelementStart(26, \"span\", 10);\n          i0.ɵɵtext(27, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 11);\n          i0.ɵɵelement(29, \"input\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 13);\n          i0.ɵɵelement(31, \"div\", 14);\n          i0.ɵɵelementStart(32, \"div\", 15);\n          i0.ɵɵtemplate(33, CreatePlanComponent_div_33_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(34, CreatePlanComponent_div_34_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(35, CreatePlanComponent_div_35_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(36, CreatePlanComponent_div_36_Template, 2, 1, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"div\", 17)(38, \"label\", 20);\n          i0.ɵɵtext(39);\n          i0.ɵɵelementStart(40, \"span\", 10);\n          i0.ɵɵtext(41, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 11);\n          i0.ɵɵelement(43, \"input\", 21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"div\", 13);\n          i0.ɵɵelement(45, \"div\", 14);\n          i0.ɵɵelementStart(46, \"div\", 15);\n          i0.ɵɵtemplate(47, CreatePlanComponent_div_47_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(48, CreatePlanComponent_div_48_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(49, CreatePlanComponent_div_49_Template, 2, 1, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 17)(51, \"label\", 22);\n          i0.ɵɵtext(52);\n          i0.ɵɵelementStart(53, \"span\", 10);\n          i0.ɵɵtext(54, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"div\", 11);\n          i0.ɵɵelement(56, \"p-dropdown\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 13);\n          i0.ɵɵelement(58, \"div\", 14);\n          i0.ɵɵelementStart(59, \"div\", 15);\n          i0.ɵɵtemplate(60, CreatePlanComponent_div_60_Template, 2, 1, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"div\", 24)(62, \"label\", 25);\n          i0.ɵɵtext(63);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"div\", 11);\n          i0.ɵɵelement(65, \"textarea\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"div\", 13);\n          i0.ɵɵelement(67, \"div\", 14);\n          i0.ɵɵelementStart(68, \"div\", 15);\n          i0.ɵɵtemplate(69, CreatePlanComponent_div_69_Template, 2, 1, \"div\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(70, \"div\", 7)(71, \"div\", 27)(72, \"label\", 28);\n          i0.ɵɵtext(73);\n          i0.ɵɵelementStart(74, \"span\", 10);\n          i0.ɵɵtext(75, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(76, \"div\", 11)(77, \"input\", 29);\n          i0.ɵɵlistener(\"keydown\", function CreatePlanComponent_Template_input_keydown_77_listener($event) {\n            return ctx.blockMinus($event);\n          })(\"input\", function CreatePlanComponent_Template_input_input_77_listener($event) {\n            return ctx.checkInputValue($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(78, \"div\", 30);\n          i0.ɵɵtext(79);\n          i0.ɵɵelement(80, \"br\");\n          i0.ɵɵtext(81);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"div\", 13);\n          i0.ɵɵelement(83, \"div\", 14);\n          i0.ɵɵelementStart(84, \"div\", 15);\n          i0.ɵɵtemplate(85, CreatePlanComponent_div_85_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(86, CreatePlanComponent_div_86_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(87, CreatePlanComponent_div_87_Template, 2, 2, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(88, \"div\", 31)(89, \"div\", 32);\n          i0.ɵɵtemplate(90, CreatePlanComponent_div_90_Template, 4, 5, \"div\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"div\", 34)(92, \"label\", 35);\n          i0.ɵɵtext(93);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"div\", 11);\n          i0.ɵɵelement(95, \"p-inputSwitch\", 36);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(96, \"div\", 17)(97, \"label\", 37);\n          i0.ɵɵtext(98);\n          i0.ɵɵelementStart(99, \"span\", 10);\n          i0.ɵɵtext(100, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(101, \"div\", 11);\n          i0.ɵɵelement(102, \"p-dropdown\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(103, \"div\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"div\", 13);\n          i0.ɵɵelement(105, \"div\", 14);\n          i0.ɵɵelementStart(106, \"div\", 15);\n          i0.ɵɵtemplate(107, CreatePlanComponent_div_107_Template, 2, 1, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(108, \"div\", 17)(109, \"label\", 39);\n          i0.ɵɵtext(110);\n          i0.ɵɵelementStart(111, \"span\", 10);\n          i0.ɵɵtext(112, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(113, \"div\", 11)(114, \"input\", 40);\n          i0.ɵɵlistener(\"keydown\", function CreatePlanComponent_Template_input_keydown_114_listener($event) {\n            return ctx.blockMinus($event);\n          })(\"input\", function CreatePlanComponent_Template_input_input_114_listener($event) {\n            return ctx.checkInputValue($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(115, CreatePlanComponent_div_115_Template, 2, 1, \"div\", 41);\n          i0.ɵɵtemplate(116, CreatePlanComponent_div_116_Template, 2, 1, \"div\", 41);\n          i0.ɵɵtemplate(117, CreatePlanComponent_div_117_Template, 2, 1, \"div\", 41);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(118, \"div\", 13);\n          i0.ɵɵelement(119, \"div\", 14);\n          i0.ɵɵelementStart(120, \"div\", 15);\n          i0.ɵɵtemplate(121, CreatePlanComponent_div_121_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(122, CreatePlanComponent_div_122_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(123, CreatePlanComponent_div_123_Template, 2, 2, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(124, \"div\", 17)(125, \"label\", 42);\n          i0.ɵɵtext(126);\n          i0.ɵɵelementStart(127, \"span\", 10);\n          i0.ɵɵtext(128, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(129, \"div\", 11)(130, \"p-dropdown\", 43);\n          i0.ɵɵlistener(\"onChange\", function CreatePlanComponent_Template_p_dropdown_onChange_130_listener($event) {\n            return ctx.onDropdownChange($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(131, \"div\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(132, \"div\", 13);\n          i0.ɵɵelement(133, \"div\", 14);\n          i0.ɵɵelementStart(134, \"div\", 15);\n          i0.ɵɵtemplate(135, CreatePlanComponent_div_135_Template, 2, 1, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(136, CreatePlanComponent_div_136_Template, 9, 3, \"div\", 44);\n          i0.ɵɵelementStart(137, \"div\", 45);\n          i0.ɵɵelement(138, \"div\", 14);\n          i0.ɵɵelementStart(139, \"div\", 15);\n          i0.ɵɵtemplate(140, CreatePlanComponent_div_140_Template, 2, 1, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(141, CreatePlanComponent_div_141_Template, 3, 1, \"div\", 46);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(142, \"h4\", 47);\n          i0.ɵɵtext(143);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(144, \"div\", 6)(145, \"div\", 48)(146, \"div\", 27)(147, \"label\", 49);\n          i0.ɵɵtext(148);\n          i0.ɵɵelementStart(149, \"span\", 10);\n          i0.ɵɵtext(150, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(151, \"div\", 11)(152, \"input\", 50);\n          i0.ɵɵlistener(\"keydown\", function CreatePlanComponent_Template_input_keydown_152_listener($event) {\n            return ctx.blockMinus($event);\n          })(\"input\", function CreatePlanComponent_Template_input_input_152_listener($event) {\n            return ctx.checkInputValue($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(153, \"div\", 51);\n          i0.ɵɵtext(154, \" (MB) \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(155, \"div\", 13);\n          i0.ɵɵelement(156, \"div\", 52);\n          i0.ɵɵelementStart(157, \"div\", 15);\n          i0.ɵɵtemplate(158, CreatePlanComponent_div_158_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(159, CreatePlanComponent_div_159_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(160, CreatePlanComponent_div_160_Template, 2, 2, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(161, \"div\", 17)(162, \"label\", 49);\n          i0.ɵɵtext(163);\n          i0.ɵɵelementStart(164, \"span\", 10);\n          i0.ɵɵtext(165, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(166, \"div\", 11)(167, \"input\", 53);\n          i0.ɵɵlistener(\"keydown\", function CreatePlanComponent_Template_input_keydown_167_listener($event) {\n            return ctx.blockMinus($event);\n          })(\"input\", function CreatePlanComponent_Template_input_input_167_listener($event) {\n            return ctx.checkInputValue($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(168, \"div\", 51);\n          i0.ɵɵtext(169, \" (MB) \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(170, \"div\", 13);\n          i0.ɵɵelement(171, \"div\", 52);\n          i0.ɵɵelementStart(172, \"div\", 15);\n          i0.ɵɵtemplate(173, CreatePlanComponent_div_173_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(174, CreatePlanComponent_div_174_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(175, CreatePlanComponent_div_175_Template, 2, 2, \"div\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(176, \"div\", 48)(177, \"div\", 27)(178, \"label\", 54);\n          i0.ɵɵtext(179);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(180, \"div\", 11)(181, \"input\", 55);\n          i0.ɵɵlistener(\"keydown\", function CreatePlanComponent_Template_input_keydown_181_listener($event) {\n            return ctx.blockMinus($event);\n          })(\"input\", function CreatePlanComponent_Template_input_input_181_listener($event) {\n            return ctx.checkInputValue($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(182, \"div\", 13);\n          i0.ɵɵelement(183, \"div\", 52);\n          i0.ɵɵelementStart(184, \"div\", 15);\n          i0.ɵɵtemplate(185, CreatePlanComponent_div_185_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(186, CreatePlanComponent_div_186_Template, 2, 2, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(187, \"div\", 17)(188, \"label\", 56);\n          i0.ɵɵtext(189);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(190, \"div\", 11)(191, \"input\", 57);\n          i0.ɵɵlistener(\"keydown\", function CreatePlanComponent_Template_input_keydown_191_listener($event) {\n            return ctx.blockMinus($event);\n          })(\"input\", function CreatePlanComponent_Template_input_input_191_listener($event) {\n            return ctx.checkInputValue($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(192, \"div\", 13);\n          i0.ɵɵelement(193, \"div\", 52);\n          i0.ɵɵelementStart(194, \"div\", 15);\n          i0.ɵɵtemplate(195, CreatePlanComponent_div_195_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(196, CreatePlanComponent_div_196_Template, 2, 2, \"div\", 16);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(197, \"div\", 58)(198, \"h4\", 59);\n          i0.ɵɵtext(199);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(200, \"p-inputSwitch\", 60);\n          i0.ɵɵlistener(\"ngModelChange\", function CreatePlanComponent_Template_p_inputSwitch_ngModelChange_200_listener($event) {\n            return ctx.isFlexible = $event;\n          })(\"onChange\", function CreatePlanComponent_Template_p_inputSwitch_onChange_200_listener() {\n            return ctx.onSwitchChange();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(201, \"div\", 61)(202, \"div\", 27)(203, \"label\", 62);\n          i0.ɵɵtext(204);\n          i0.ɵɵelementStart(205, \"span\", 10);\n          i0.ɵɵtext(206, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(207, \"div\", 11)(208, \"input\", 63);\n          i0.ɵɵlistener(\"keydown\", function CreatePlanComponent_Template_input_keydown_208_listener($event) {\n            return ctx.blockMinus($event);\n          })(\"input\", function CreatePlanComponent_Template_input_input_208_listener($event) {\n            return ctx.checkInputValue($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(209, \"div\", 64);\n          i0.ɵɵtext(210, \"/\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(211, \"div\", 11)(212, \"input\", 65);\n          i0.ɵɵlistener(\"keydown\", function CreatePlanComponent_Template_input_keydown_212_listener($event) {\n            return ctx.blockMinus($event);\n          })(\"input\", function CreatePlanComponent_Template_input_input_212_listener($event) {\n            return ctx.checkInputValue($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(213, \"div\", 66);\n          i0.ɵɵtext(214, \"(KB)\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(215, \"div\", 45);\n          i0.ɵɵelement(216, \"div\", 67);\n          i0.ɵɵelementStart(217, \"div\", 68);\n          i0.ɵɵtemplate(218, CreatePlanComponent_div_218_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(219, CreatePlanComponent_div_219_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(220, CreatePlanComponent_div_220_Template, 2, 2, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(221, \"div\", 69);\n          i0.ɵɵtemplate(222, CreatePlanComponent_div_222_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(223, CreatePlanComponent_div_223_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(224, CreatePlanComponent_div_224_Template, 2, 2, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(225, \"div\", 17)(226, \"label\", 70);\n          i0.ɵɵtext(227);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(228, \"div\", 11)(229, \"input\", 71);\n          i0.ɵɵlistener(\"keydown\", function CreatePlanComponent_Template_input_keydown_229_listener($event) {\n            return ctx.blockMinus($event);\n          })(\"input\", function CreatePlanComponent_Template_input_input_229_listener($event) {\n            return ctx.checkInputValue($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(230, \"div\", 64);\n          i0.ɵɵtext(231, \"/\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(232, \"div\", 11)(233, \"input\", 72);\n          i0.ɵɵlistener(\"keydown\", function CreatePlanComponent_Template_input_keydown_233_listener($event) {\n            return ctx.blockMinus($event);\n          })(\"input\", function CreatePlanComponent_Template_input_input_233_listener($event) {\n            return ctx.checkInputValue($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(234, \"div\", 66);\n          i0.ɵɵtext(235, \"(Kbps)\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(236, \"div\", 45);\n          i0.ɵɵelement(237, \"div\", 67);\n          i0.ɵɵelementStart(238, \"div\", 68);\n          i0.ɵɵtemplate(239, CreatePlanComponent_div_239_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(240, CreatePlanComponent_div_240_Template, 2, 2, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(241, \"div\", 69);\n          i0.ɵɵtemplate(242, CreatePlanComponent_div_242_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(243, CreatePlanComponent_div_243_Template, 2, 2, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(244, \"div\", 73)(245, \"label\", 74);\n          i0.ɵɵtext(246);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(247, \"div\", 11)(248, \"input\", 75);\n          i0.ɵɵlistener(\"keydown\", function CreatePlanComponent_Template_input_keydown_248_listener($event) {\n            return ctx.blockMinus($event);\n          })(\"input\", function CreatePlanComponent_Template_input_input_248_listener($event) {\n            return ctx.checkInputValue($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(249, \"div\", 64)(250, \"div\", 11)(251, \"div\", 66);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(252, \"div\", 45);\n          i0.ɵɵelement(253, \"div\", 76);\n          i0.ɵɵelementStart(254, \"div\", 77);\n          i0.ɵɵtemplate(255, CreatePlanComponent_div_255_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(256, CreatePlanComponent_div_256_Template, 2, 2, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(257, \"div\", 73)(258, \"label\", 78);\n          i0.ɵɵtext(259);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(260, \"div\", 79)(261, \"input\", 80);\n          i0.ɵɵlistener(\"keydown\", function CreatePlanComponent_Template_input_keydown_261_listener($event) {\n            return ctx.blockMinus($event);\n          })(\"input\", function CreatePlanComponent_Template_input_input_261_listener($event) {\n            return ctx.checkInputValue($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(262, \"div\", 64)(263, \"div\", 11)(264, \"div\", 66);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(265, \"div\", 45);\n          i0.ɵɵelement(266, \"div\", 76);\n          i0.ɵɵelementStart(267, \"div\", 77);\n          i0.ɵɵtemplate(268, CreatePlanComponent_div_268_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(269, CreatePlanComponent_div_269_Template, 2, 2, \"div\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(270, \"div\", 81)(271, \"label\", 82);\n          i0.ɵɵtext(272);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(273, \"div\", 83)(274, \"input\", 84);\n          i0.ɵɵlistener(\"keydown\", function CreatePlanComponent_Template_input_keydown_274_listener($event) {\n            return ctx.blockMinus($event);\n          })(\"input\", function CreatePlanComponent_Template_input_input_274_listener($event) {\n            return ctx.checkInputValue($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(275, \"div\", 64)(276, \"div\", 11)(277, \"div\", 66);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(278, \"div\", 45);\n          i0.ɵɵelement(279, \"div\", 76);\n          i0.ɵɵelementStart(280, \"div\", 77);\n          i0.ɵɵtemplate(281, CreatePlanComponent_div_281_Template, 2, 1, \"div\", 16);\n          i0.ɵɵtemplate(282, CreatePlanComponent_div_282_Template, 2, 2, \"div\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(283, \"div\", 85)(284, \"a\", 86);\n          i0.ɵɵelement(285, \"button\", 87);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(286, \"button\", 88);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(287, \"form\", 89);\n          i0.ɵɵlistener(\"ngSubmit\", function CreatePlanComponent_Template_form_ngSubmit_287_listener() {\n            return ctx.onSubmitSearchUser();\n          });\n          i0.ɵɵelementStart(288, \"div\", 90)(289, \"p-dialog\", 91);\n          i0.ɵɵlistener(\"visibleChange\", function CreatePlanComponent_Template_p_dialog_visibleChange_289_listener($event) {\n            return ctx.isShowDialogAddCustomerAccount = $event;\n          });\n          i0.ɵɵelementStart(290, \"div\", 92)(291, \"div\", 93)(292, \"span\", 94)(293, \"input\", 95);\n          i0.ɵɵlistener(\"ngModelChange\", function CreatePlanComponent_Template_input_ngModelChange_293_listener($event) {\n            return ctx.searchInfoUser.username = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(294, \"label\", 96);\n          i0.ɵɵtext(295);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(296, \"div\", 93)(297, \"span\", 94)(298, \"input\", 97);\n          i0.ɵɵlistener(\"ngModelChange\", function CreatePlanComponent_Template_input_ngModelChange_298_listener($event) {\n            return ctx.searchInfoUser.fullName = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(299, \"label\", 98);\n          i0.ɵɵtext(300);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(301, \"div\", 93);\n          i0.ɵɵtemplate(302, CreatePlanComponent_span_302_Template, 4, 5, \"span\", 99);\n          i0.ɵɵtemplate(303, CreatePlanComponent_span_303_Template, 2, 2, \"span\", 100);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(304, \"div\", 101);\n          i0.ɵɵelement(305, \"p-button\", 102);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(306, \"table-vnpt\", 103);\n          i0.ɵɵlistener(\"selectItemsChange\", function CreatePlanComponent_Template_table_vnpt_selectItemsChange_306_listener($event) {\n            return ctx.selectItemsUser = $event;\n          });\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.menu.listplan\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.createPlanForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.planCode\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"placeholder\", ctx.placeHolder.planCode);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isPlanCodeValid && ctx.createPlanForm.get(\"code\").hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isPlanCodeValid && ctx.createPlanForm.get(\"code\").hasError(\"maxlength\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isPlanCodeValid && ctx.createPlanForm.get(\"code\").hasError(\"invalidCharacters\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isPlanCodeValid && ctx.createPlanForm.get(\"code\").hasError(\"exited\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.planName\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"placeholder\", ctx.placeHolder.planName);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isPlaneNameValid && ctx.createPlanForm.get(\"name\").hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isPlaneNameValid && ctx.createPlanForm.get(\"name\").hasError(\"maxlength\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isPlaneNameValid && ctx.createPlanForm.get(\"name\").hasError(\"invalidCharacters\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isPlaneNameValid && ctx.createPlanForm.get(\"name\").hasError(\"exited\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.dispatchCode\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"placeholder\", ctx.placeHolder.dispatchCode);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDispatchCodeValid && ctx.createPlanForm.get(\"dispatchCode\").hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDispatchCodeValid && ctx.createPlanForm.get(\"dispatchCode\").hasError(\"maxlength\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDispatchCodeValid && ctx.createPlanForm.get(\"dispatchCode\").hasError(\"invalidCharacters\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.customerType\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"options\", ctx.customerTypes)(\"placeholder\", ctx.placeHolder.customerType);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCustomerTypeValid && ctx.createPlanForm.get(\"customerType\").hasError(\"required\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.description\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"placeholder\", ctx.placeHolder.description);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDescriptionValid && ctx.createPlanForm.get(\"description\").hasError(\"maxlength\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.subscriptionFee\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"placeholder\", ctx.placeHolder.subscriptionFee);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.tranService.translate(\"ratingPlan.text.textDong\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.tranService.translate(\"ratingPlan.text.vat\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSubscriptionFeeValid && ctx.createPlanForm.get(\"subscriptionFee\").hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSubscriptionFeeValid && ctx.createPlanForm.get(\"subscriptionFee\").hasError(\"max\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSubscriptionFeeValid && ctx.createPlanForm.get(\"subscriptionFee\").hasError(\"min\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.paidCategories);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.autoReload\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.cycle\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"options\", ctx.cycles)(\"placeholder\", ctx.placeHolder.planCycle);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.isPlanCycleValid && ctx.createPlanForm.get(\"cycleTimeUnit\").hasError(\"required\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.duration\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"placeholder\", ctx.placeHolder.duration);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.createPlanForm.get(\"cycleTimeUnit\").value == \"1\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.createPlanForm.get(\"cycleTimeUnit\").value == \"3\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.createPlanForm.get(\"cycleTimeUnit\").value != \"1\" && ctx.createPlanForm.get(\"cycleTimeUnit\").value != \"3\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDurationValid && ctx.createPlanForm.get(\"cycleInterval\").hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDurationValid && ctx.createPlanForm.get(\"cycleInterval\").hasError(\"max\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDurationValid && ctx.createPlanForm.get(\"cycleInterval\").hasError(\"min\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.ratingScope\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"options\", ctx.ratingScopes)(\"placeholder\", ctx.placeHolder.planScope);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.isPlanScopeValid && ctx.createPlanForm.get(\"ratingScope\").hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isProvince);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isProvinceCodeValid && ctx.createPlanForm.get(\"provinceCode\").hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCustomer);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.flat\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.freeData\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"placeholder\", ctx.placeHolder.freeData);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isFreeDataValid && ctx.createPlanForm.get(\"limitDataUsage\").hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isFreeDataValid && ctx.createPlanForm.get(\"limitDataUsage\").hasError(\"max\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isFreeDataValid && ctx.createPlanForm.get(\"limitDataUsage\").hasError(\"min\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.placeHolder.dataMax\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"placeholder\", ctx.placeHolder.dataMax);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDataMaxValid && ctx.createPlanForm.get(\"dataMax\").hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDataMaxValid && ctx.createPlanForm.get(\"dataMax\").hasError(\"max\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDataMaxValid && ctx.createPlanForm.get(\"dataMax\").hasError(\"min\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.insideSMSFree\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"placeholder\", ctx.placeHolder.insideSMSFree);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLimitInsideSMSFreeValid && ctx.createPlanForm.get(\"limitSmsInside\").hasError(\"max\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLimitInsideSMSFreeValid && ctx.createPlanForm.get(\"limitSmsInside\").hasError(\"min\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.outsideSMSFree\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"placeholder\", ctx.placeHolder.outsideSMSFree);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLimitOutsideSMSFreeValid && ctx.createPlanForm.get(\"limitSmsOutside\").hasError(\"max\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLimitOutsideSMSFreeValid && ctx.createPlanForm.get(\"limitSmsOutside\").hasError(\"min\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.flexible\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngModel\", ctx.isFlexible);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleProp(\"color\", !ctx.isFlexible ? \"gray\" : \"black\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.feePerUnit\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", !ctx.isFlexible)(\"placeholder\", ctx.placeHolder.feePerUnit);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"color\", !ctx.isFlexible ? \"gray\" : \"black\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", !ctx.isFlexible)(\"placeholder\", ctx.placeHolder.feePerUnit);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"color\", !ctx.isFlexible ? \"gray\" : \"black\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.isFeePerUnitNumberatorValid && ctx.createPlanForm.get(\"feePerDataUnit\").hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isFeePerUnitNumberatorValid && ctx.createPlanForm.get(\"feePerDataUnit\").hasError(\"max\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isFeePerUnitNumberatorValid && ctx.createPlanForm.get(\"feePerDataUnit\").hasError(\"min\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isFeePerUnitDenominatorValid && ctx.createPlanForm.get(\"dataRoundUnit\").hasError(\"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isFeePerUnitDenominatorValid && ctx.createPlanForm.get(\"dataRoundUnit\").hasError(\"max\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isFeePerUnitDenominatorValid && ctx.createPlanForm.get(\"dataRoundUnit\").hasError(\"min\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"color\", !ctx.isFlexible ? \"gray\" : \"black\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.squeezeSpeed\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", !ctx.isFlexible)(\"placeholder\", ctx.placeHolder.squeezedSpeed);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"color\", !ctx.isFlexible ? \"gray\" : \"black\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", !ctx.isFlexible)(\"placeholder\", ctx.placeHolder.squeezedSpeed);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"color\", !ctx.isFlexible ? \"gray\" : \"black\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSqueezeSpeedNumberatorValid && ctx.createPlanForm.get(\"downSpeed\").hasError(\"max\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSqueezeSpeedNumberatorValid && ctx.createPlanForm.get(\"downSpeed\").hasError(\"min\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSqueezeSpeedDenominatorValid && ctx.createPlanForm.get(\"squeezedSpeed\").hasError(\"max\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isSqueezeSpeedDenominatorValid && ctx.createPlanForm.get(\"squeezedSpeed\").hasError(\"min\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"color\", !ctx.isFlexible ? \"gray\" : \"black\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.feePerInsideSMS\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", !ctx.isFlexible)(\"placeholder\", ctx.placeHolder.feePerInsideSMS);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"color\", !ctx.isFlexible ? \"gray\" : \"black\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"color\", !ctx.isFlexible ? \"gray\" : \"black\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isFeePerInsideSMSValid && ctx.createPlanForm.get(\"feeSmsInside\").hasError(\"max\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isFeePerInsideSMSValid && ctx.createPlanForm.get(\"feeSmsInside\").hasError(\"min\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"color\", !ctx.isFlexible ? \"gray\" : \"black\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.feePerOutsideSMS\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", !ctx.isFlexible)(\"placeholder\", ctx.placeHolder.feePerOutsideSMS);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"color\", !ctx.isFlexible ? \"gray\" : \"black\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"color\", !ctx.isFlexible ? \"gray\" : \"black\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isFeePerOutsideSMSValid && ctx.createPlanForm.get(\"feeSmsOutside\").hasError(\"max\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isFeePerOutsideSMSValid && ctx.createPlanForm.get(\"feeSmsOutside\").hasError(\"min\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"color\", !ctx.isFlexible ? \"gray\" : \"black\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.maxFee\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", !ctx.isFlexible)(\"placeholder\", ctx.placeHolder.maxFee);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"color\", !ctx.isFlexible ? \"gray\" : \"black\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"color\", !ctx.isFlexible ? \"gray\" : \"black\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isMaxFeeValid && ctx.createPlanForm.get(\"maximumFee\").hasError(\"max\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isMaxFeeValid && ctx.createPlanForm.get(\"maximumFee\").hasError(\"min\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.cancel\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.save\"))(\"disabled\", ctx.isPlanCodeExisted || ctx.isPlanNameExisted || ctx.createPlanForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formSearchUser);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(173, _c1));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"account.label.addCustomerAccount\"))(\"visible\", ctx.isShowDialogAddCustomerAccount)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfoUser.username);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.username\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfoUser.fullName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.fullName\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.userType == ctx.allUserType.ADMIN ? \"\" : \"flex flex-row justify-content-start align-items-center\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.userType == ctx.allUserType.ADMIN);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.userType != ctx.allUserType.ADMIN);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"fieldId\", \"id\")(\"pageNumber\", ctx.pageNumberAssign)(\"pageSize\", ctx.pageSizeAssign)(\"selectItems\", ctx.selectItemsUser)(\"columns\", ctx.columnsInfoUser)(\"dataSet\", ctx.dataSetAssignPlan)(\"options\", ctx.optionTableAddCustomerAccount)(\"loadData\", ctx.searchUser.bind(ctx))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(174, _c2))(\"scrollHeight\", \"400px\")(\"sort\", ctx.sort)(\"params\", ctx.searchInfoUser);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.RouterLink, i4.Breadcrumb, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.InputText, i6.ButtonDirective, i6.Button, i7.TableVnptComponent, i8.Dropdown, i9.Card, i10.Dialog, i11.MultiSelect, i12.InputSwitch, i13.RadioButton],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["FormControl", "FormGroup", "Validators", "Observable", "debounceTime", "map", "switchMap", "take", "ComponentBase", "RatingPlanService", "AccountService", "CONSTANTS", "ComboLazyControl", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "tranService", "translate", "ctx_r1", "ctx_r2", "ctx_r3", "ctx_r4", "ctx_r5", "ctx_r6", "ctx_r7", "ctx_r8", "ctx_r9", "ctx_r10", "ctx_r11", "ctx_r12", "ctx_r13", "ctx_r14", "ctx_r15", "ɵɵpureFunction0", "_c0", "ɵɵlistener", "CreatePlanComponent_div_90_Template_p_radioButton_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r58", "ctx_r57", "ɵɵnextContext", "ɵɵresetView", "selectedPaid<PERSON>ate<PERSON><PERSON>", "ɵɵproperty", "paidType_r56", "key", "ctx_r16", "ɵɵtextInterpolate", "name", "ctx_r17", "ctx_r18", "ctx_r19", "ctx_r20", "ctx_r21", "ctx_r22", "ctx_r23", "ctx_r24", "CreatePlanComponent_div_136_p_multiSelect_6_Template_p_multiSelect_onChange_0_listener", "_r62", "ctx_r61", "changeProvince", "ctx_r59", "provinces", "placeHolder", "provinceCode", "ctx_r60", "provinceInfo", "ɵɵtemplate", "CreatePlanComponent_div_136_p_multiSelect_6_Template", "CreatePlanComponent_div_136_span_7_Template", "ɵɵelement", "ctx_r25", "userType", "allUserType", "ADMIN", "ctx_r26", "CreatePlanComponent_div_141_Template_label_click_1_listener", "_r64", "ctx_r63", "openDialogAddCustomerAccount", "ctx_r27", "ctx_r28", "ctx_r29", "ctx_r30", "ctx_r31", "ctx_r32", "ctx_r33", "ctx_r34", "ctx_r35", "ctx_r36", "ctx_r37", "ctx_r38", "ctx_r39", "ctx_r40", "ctx_r41", "ctx_r42", "ctx_r43", "ctx_r44", "ctx_r45", "ctx_r46", "ctx_r47", "ctx_r48", "ctx_r49", "ctx_r50", "ctx_r51", "ctx_r52", "ctx_r53", "CreatePlanComponent_span_302_Template_p_multiSelect_ngModelChange_1_listener", "_r66", "ctx_r65", "searchInfoUser", "ctx_r54", "listProvince", "ɵɵtextInterpolate2", "ctx_r55", "CreatePlanComponent", "constructor", "ratingPlanService", "accountService", "formBuilder", "injector", "isPlanCodeExisted", "isPlanNameExisted", "isPlanCodeValid", "isPlaneNameValid", "isDispatchCodeValid", "isCustomerTypeValid", "isSubscriptionFeeValid", "isSubscriptionTypeValid", "isPlanScopeValid", "isProvinceCodeValid", "isPlanCycleValid", "isDurationValid", "isDescriptionValid", "isFreeDataValid", "isLimitInsideSMSFreeValid", "isLimitOutsideSMSFreeValid", "isFeePerUnitNumberatorValid", "isFeePerUnitDenominatorValid", "isSqueezeSpeedNumberatorValid", "isSqueezeSpeedDenominatorValid", "isFeePerInsideSMSValid", "isFeePerOutsideSMSValid", "isMaxFeeValid", "isDataMaxValid", "isShowDialogAddCustomerAccount", "selectItemsUser", "selectItemsUserOld", "id", "controlComboSelect", "isProvince", "isCustomer", "isFlexible", "paidCategories", "placeHolderDescription", "sessionService", "userInfo", "type", "USER_TYPE", "planCode", "planName", "dispatchCode", "customerType", "description", "subscriptionFee", "subscriptionType", "planScope", "planCycle", "duration", "freeData", "insideSMSFree", "outsideSMSFree", "feePerUnit", "squeezedSpeed", "feePerInsideSMS", "feePerOutsideSMS", "maxFee", "dataMax", "createPlanForm", "status", "code", "required", "max<PERSON><PERSON><PERSON>", "customCodeCharacterValidator", "planCodeValidator", "customNameCharacterValidator", "planNameValidator", "max", "min", "paidType", "ratingScope", "value", "disabled", "userIds", "cycleTimeUnit", "cycleInterval", "reload", "limitDataUsage", "limitSmsInside", "limitSmsOutside", "flexible", "feePerDataUnit", "dataRoundUnit", "downSpeed", "feeSmsInside", "feeSmsOutside", "maximumFee", "uploadSpeed", "control", "<PERSON><PERSON><PERSON><PERSON>", "test", "checkCodeExisted", "query", "observer", "checkingPlanCodeExisted", "response", "next", "complete", "checkNameExisted", "checkingPlanNameExisted", "valueChanges", "pipe", "result", "blockMinus", "event", "invalid<PERSON>hars", "includes", "preventDefault", "checkInputValue", "input", "target", "replace", "onDropdownChange", "get", "disable", "emitEvent", "enable", "onSwitchChange", "submitForm", "messageCommonService", "onload", "me", "valid", "data", "length", "provinceSelected", "currentSelected", "filter", "el", "e", "createRatingPlan", "success", "router", "navigate", "offload", "onSubmitSearchUser", "pageNumberAssign", "searchUser", "pageSizeAssign", "sortAssign", "ngOnInit", "optionTableAddCustomerAccount", "hasClearSelected", "hasShowIndex", "hasShowChoose", "hasShowToggleColumn", "dataSetAssignPlan", "content", "total", "username", "fullName", "email", "formSearchUser", "group", "items", "label", "routerLink", "columnsInfoUser", "size", "align", "isShow", "isSort", "customerTypes", "ratingScopes", "cycles", "getListProvince", "subPlanCode", "statusChanges", "subscribe", "errors", "subPlaneName", "subDispatchCode", "subCustomerType", "subSubscriptionFee", "subSubscriptionType", "subPlanScope", "subProvinceCode", "subPlanCycle", "subDuration", "subDescription", "subFreeData", "subLimitInsideSMSFree", "subLimitOutsideSMSFree", "subDataMax", "subFeePerUnitNumberator", "subFeePerUnitDenominator", "subSqueezeSpeedNumberator", "subSqueezeSpeedDenominator", "subFeePerInsideSMS", "subFeePerOutsideSMS", "subMaxFee", "home", "icon", "ngOnDestroy", "unsubscribe", "provincesSelected", "prov", "page", "limit", "sort", "params", "undefined", "dataParams", "Object", "keys", "for<PERSON>ach", "getUserToAddAccount", "totalElements", "ɵɵdirectiveInject", "i1", "FormBuilder", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "CreatePlanComponent_Template", "rf", "ctx", "CreatePlanComponent_Template_form_submit_6_listener", "CreatePlanComponent_div_19_Template", "CreatePlanComponent_div_20_Template", "CreatePlanComponent_div_21_Template", "CreatePlanComponent_div_22_Template", "CreatePlanComponent_div_33_Template", "CreatePlanComponent_div_34_Template", "CreatePlanComponent_div_35_Template", "CreatePlanComponent_div_36_Template", "CreatePlanComponent_div_47_Template", "CreatePlanComponent_div_48_Template", "CreatePlanComponent_div_49_Template", "CreatePlanComponent_div_60_Template", "CreatePlanComponent_div_69_Template", "CreatePlanComponent_Template_input_keydown_77_listener", "CreatePlanComponent_Template_input_input_77_listener", "CreatePlanComponent_div_85_Template", "CreatePlanComponent_div_86_Template", "CreatePlanComponent_div_87_Template", "CreatePlanComponent_div_90_Template", "CreatePlanComponent_div_107_Template", "CreatePlanComponent_Template_input_keydown_114_listener", "CreatePlanComponent_Template_input_input_114_listener", "CreatePlanComponent_div_115_Template", "CreatePlanComponent_div_116_Template", "CreatePlanComponent_div_117_Template", "CreatePlanComponent_div_121_Template", "CreatePlanComponent_div_122_Template", "CreatePlanComponent_div_123_Template", "CreatePlanComponent_Template_p_dropdown_onChange_130_listener", "CreatePlanComponent_div_135_Template", "CreatePlanComponent_div_136_Template", "CreatePlanComponent_div_140_Template", "CreatePlanComponent_div_141_Template", "CreatePlanComponent_Template_input_keydown_152_listener", "CreatePlanComponent_Template_input_input_152_listener", "CreatePlanComponent_div_158_Template", "CreatePlanComponent_div_159_Template", "CreatePlanComponent_div_160_Template", "CreatePlanComponent_Template_input_keydown_167_listener", "CreatePlanComponent_Template_input_input_167_listener", "CreatePlanComponent_div_173_Template", "CreatePlanComponent_div_174_Template", "CreatePlanComponent_div_175_Template", "CreatePlanComponent_Template_input_keydown_181_listener", "CreatePlanComponent_Template_input_input_181_listener", "CreatePlanComponent_div_185_Template", "CreatePlanComponent_div_186_Template", "CreatePlanComponent_Template_input_keydown_191_listener", "CreatePlanComponent_Template_input_input_191_listener", "CreatePlanComponent_div_195_Template", "CreatePlanComponent_div_196_Template", "CreatePlanComponent_Template_p_inputSwitch_ngModelChange_200_listener", "CreatePlanComponent_Template_p_inputSwitch_onChange_200_listener", "CreatePlanComponent_Template_input_keydown_208_listener", "CreatePlanComponent_Template_input_input_208_listener", "CreatePlanComponent_Template_input_keydown_212_listener", "CreatePlanComponent_Template_input_input_212_listener", "CreatePlanComponent_div_218_Template", "CreatePlanComponent_div_219_Template", "CreatePlanComponent_div_220_Template", "CreatePlanComponent_div_222_Template", "CreatePlanComponent_div_223_Template", "CreatePlanComponent_div_224_Template", "CreatePlanComponent_Template_input_keydown_229_listener", "CreatePlanComponent_Template_input_input_229_listener", "CreatePlanComponent_Template_input_keydown_233_listener", "CreatePlanComponent_Template_input_input_233_listener", "CreatePlanComponent_div_239_Template", "CreatePlanComponent_div_240_Template", "CreatePlanComponent_div_242_Template", "CreatePlanComponent_div_243_Template", "CreatePlanComponent_Template_input_keydown_248_listener", "CreatePlanComponent_Template_input_input_248_listener", "CreatePlanComponent_div_255_Template", "CreatePlanComponent_div_256_Template", "CreatePlanComponent_Template_input_keydown_261_listener", "CreatePlanComponent_Template_input_input_261_listener", "CreatePlanComponent_div_268_Template", "CreatePlanComponent_div_269_Template", "CreatePlanComponent_Template_input_keydown_274_listener", "CreatePlanComponent_Template_input_input_274_listener", "CreatePlanComponent_div_281_Template", "CreatePlanComponent_div_282_Template", "CreatePlanComponent_Template_form_ngSubmit_287_listener", "CreatePlanComponent_Template_p_dialog_visibleChange_289_listener", "CreatePlanComponent_Template_input_ngModelChange_293_listener", "CreatePlanComponent_Template_input_ngModelChange_298_listener", "CreatePlanComponent_span_302_Template", "CreatePlanComponent_span_303_Template", "CreatePlanComponent_Template_table_vnpt_selectItemsChange_306_listener", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵstyleProp", "invalid", "ɵɵstyleMap", "_c1", "ɵɵclassMap", "bind", "_c2"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\rating-plan-management\\list-plan\\create-plan\\create-plan.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\rating-plan-management\\list-plan\\create-plan\\create-plan.component.html"], "sourcesContent": ["import {On<PERSON><PERSON>roy, Injector} from '@angular/core';\r\nimport {Component, Inject} from '@angular/core';\r\nimport {\r\n    AbstractControl,\r\n    AsyncValidatorFn, FormBuilder,\r\n    FormControl,\r\n    FormGroup,\r\n    ValidationErrors,\r\n    ValidatorFn,\r\n    Validators\r\n} from '@angular/forms';\r\nimport {MenuItem} from 'primeng/api';\r\nimport {Observable, Subscription, debounceTime, map, switchMap, take} from 'rxjs';\r\nimport {ComponentBase} from 'src/app/component.base';\r\nimport {RatingPlanService} from 'src/app/service/rating-plan/RatingPlanService';\r\nimport {AccountService} from \"../../../../service/account/AccountService\";\r\nimport {CONSTANTS} from 'src/app/service/comon/constants';\r\nimport {ComboLazyControl} from \"../../../common-module/combobox-lazyload/combobox.lazyload\";\r\nimport {ColumnInfo, OptionTable} from \"../../../common-module/table/table.component\";\r\nimport {da, el} from \"@fullcalendar/core/internal-common\";\r\n\r\ninterface DropDownValue {\r\n    id: string;\r\n    name: string;\r\n}\r\n\r\n@Component({\r\n    selector: 'app-create-plan',\r\n    templateUrl: './create-plan.component.html',\r\n})\r\nexport class CreatePlanComponent extends ComponentBase implements OnDestroy {\r\n    home: MenuItem;\r\n    items: MenuItem[];\r\n    isPlanCodeExisted: boolean = true;\r\n    isPlanNameExisted: boolean = true;\r\n\r\n    customerTypes: DropDownValue[] | undefined;\r\n    ratingScopes: DropDownValue[] | undefined;\r\n    provinces: any[] | undefined;\r\n    isPlanCodeValid: boolean = false;\r\n    isPlaneNameValid: boolean = false;\r\n    isDispatchCodeValid: boolean = false;\r\n    isCustomerTypeValid: boolean = false;\r\n    isSubscriptionFeeValid: boolean = false;\r\n    isSubscriptionTypeValid: boolean = false; // Đã set default\r\n    isPlanScopeValid: boolean = false;\r\n    isProvinceCodeValid: boolean = false;\r\n    isPlanCycleValid: boolean = false;\r\n    isDurationValid: boolean = false;\r\n    isDescriptionValid: boolean = false;\r\n    isFreeDataValid: boolean = false;\r\n    isLimitInsideSMSFreeValid: boolean = false;\r\n    isLimitOutsideSMSFreeValid: boolean = false;\r\n    isFeePerUnitNumberatorValid: boolean = false;\r\n    isFeePerUnitDenominatorValid: boolean = false;\r\n    isSqueezeSpeedNumberatorValid: boolean = false;\r\n    isSqueezeSpeedDenominatorValid: boolean = false;\r\n    isFeePerInsideSMSValid: boolean = false;\r\n    isFeePerOutsideSMSValid: boolean = false;\r\n    isMaxFeeValid: boolean = false;\r\n    isDataMaxValid: boolean = false;\r\n    isShowDialogAddCustomerAccount: boolean = false;\r\n    searchInfoUser: {\r\n        username: string | null,\r\n        fullName: string | null,\r\n        email: string | null,\r\n        provinceCode: any | null,\r\n    }\r\n    listProvince: any[] | undefined;\r\n    selectItemsUser: Array<{id: number, provinceCode: string, [key:string]:any}> = [];\r\n    selectItemsUserOld: Array<{id: number, provinceCode: string, [key:string]:any}> = [{id: -1, provinceCode: \"\"}];\r\n    pageNumberAssign: number;\r\n    pageSizeAssign: number;\r\n    sortAssign: string;\r\n    formSearchUser: any;\r\n    subPlanCode: Subscription;\r\n    subPlaneName: Subscription;\r\n    subDispatchCode: Subscription;\r\n    subCustomerType: Subscription;\r\n    subSubscriptionFee: Subscription;\r\n    subSubscriptionType: Subscription;\r\n    subPlanScope: Subscription;\r\n    subProvinceCode: Subscription;\r\n    subPlanCycle: Subscription;\r\n    subDuration: Subscription;\r\n    subDescription: Subscription;\r\n    subFreeData: Subscription;\r\n    subLimitInsideSMSFree: Subscription;\r\n    subLimitOutsideSMSFree: Subscription;\r\n    subFeePerUnitNumberator: Subscription;\r\n    subFeePerUnitDenominator: Subscription;\r\n    subSqueezeSpeedNumberator: Subscription;\r\n    subSqueezeSpeedDenominator: Subscription;\r\n    subFeePerInsideSMS: Subscription;\r\n    subFeePerOutsideSMS: Subscription;\r\n    subMaxFee: Subscription;\r\n    subDataMax: Subscription;\r\n    columnsInfoUser: Array<ColumnInfo>;\r\n    dataSetAssignPlan: {\r\n        content: Array<any>,\r\n        total: number\r\n    };\r\n    optionTableAddCustomerAccount: OptionTable;\r\n    sort: string;\r\n\r\n    controlComboSelect: ComboLazyControl = new ComboLazyControl();\r\n    customerCode: [];\r\n\r\n    isProvince = false\r\n    isCustomer = false\r\n\r\n    cycles: DropDownValue[] | undefined;\r\n    isFlexible: boolean = false;\r\n\r\n    paidCategories: any[] = [\r\n        {name: 'Trả trước', key: '1'},\r\n        {name: 'Trả sau', key: '0'},\r\n    ];\r\n\r\n    selectedPaidCategory: any = null;\r\n\r\n    placeHolderDescription: string = this.tranService.translate(\"groupSim.placeHolder.description\");\r\n\r\n    provinceInfo: string = \"\";\r\n    userType = this.sessionService.userInfo.type;\r\n    allUserType = CONSTANTS.USER_TYPE;\r\n\r\n    constructor(@Inject(RatingPlanService) public ratingPlanService: RatingPlanService,\r\n                @Inject(AccountService) private accountService: AccountService,\r\n                private formBuilder: FormBuilder,\r\n                injector: Injector) {\r\n        super(injector);\r\n    }\r\n\r\n    placeHolder = {\r\n        planCode: this.tranService.translate(\"ratingPlan.placeHolder.planCode\"),\r\n        planName: this.tranService.translate(\"ratingPlan.placeHolder.planeName\"),\r\n        dispatchCode: this.tranService.translate(\"ratingPlan.placeHolder.dispatchCode\"),\r\n        customerType: this.tranService.translate(\"ratingPlan.placeHolder.customerType\"),\r\n        description: this.tranService.translate(\"ratingPlan.placeHolder.description\"),\r\n        subscriptionFee: this.tranService.translate(\"ratingPlan.placeHolder.subscriptionFee\"),\r\n        subscriptionType: this.tranService.translate(\"ratingPlan.placeHolder.subscriptionType\"),\r\n        planScope: this.tranService.translate(\"ratingPlan.placeHolder.planScope\"),\r\n        provinceCode: this.tranService.translate(\"ratingPlan.placeHolder.provinceCode\"),\r\n        planCycle: this.tranService.translate(\"ratingPlan.placeHolder.planCycle\"),\r\n        duration: this.tranService.translate(\"ratingPlan.placeHolder.duration\"),\r\n        freeData: this.tranService.translate(\"ratingPlan.placeHolder.freeData\"),\r\n        insideSMSFree: this.tranService.translate(\"ratingPlan.placeHolder.insideSMSFree\"),\r\n        outsideSMSFree: this.tranService.translate(\"ratingPlan.placeHolder.outsideSMSFree\"),\r\n        feePerUnit: this.tranService.translate(\"ratingPlan.placeHolder.feePerUnit\"),\r\n        squeezedSpeed: this.tranService.translate(\"ratingPlan.placeHolder.squeezeSpeed\"),\r\n        feePerInsideSMS: this.tranService.translate(\"ratingPlan.placeHolder.feePerInsideSMS\"),\r\n        feePerOutsideSMS: this.tranService.translate(\"ratingPlan.placeHolder.feePerOutsideSMS\"),\r\n        maxFee: this.tranService.translate(\"ratingPlan.placeHolder.maxFee\"),\r\n        dataMax: this.tranService.translate(\"ratingPlan.placeHolder.dataMax\"),\r\n    }\r\n\r\n    customCodeCharacterValidator(): ValidatorFn {\r\n        return (control: AbstractControl): ValidationErrors | null => {\r\n            const value = control.value;\r\n            const isValid = /^[a-zA-Z0-9\\-_]*$/.test(value);\r\n            return isValid ? null : {'invalidCharacters': {value}};\r\n        };\r\n    }\r\n\r\n    customNameCharacterValidator(): ValidatorFn {\r\n        return (control: AbstractControl): ValidationErrors | null => {\r\n            const value = control.value;\r\n            if (value == '') {\r\n                return null;\r\n            }\r\n            const isValid = /^[a-zA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơỲỴÝỳỵỷỹƯứừữựụợ́̉̃À-ỹ0-9 _-]+$/.test(value);\r\n            return isValid ? null : {'invalidCharacters': {value}};\r\n        };\r\n    }\r\n\r\n    checkCodeExisted(query: {}): Observable<number> {\r\n        return new Observable(observer => {\r\n            this.ratingPlanService.checkingPlanCodeExisted(query, (response) => {\r\n                observer.next(response);\r\n                observer.complete();\r\n            });\r\n        });\r\n    }\r\n\r\n    checkNameExisted(query: {}): Observable<number> {\r\n        return new Observable(observer => {\r\n            this.ratingPlanService.checkingPlanNameExisted(query, (response) => {\r\n                observer.next(response);\r\n                observer.complete();\r\n            });\r\n        });\r\n    }\r\n\r\n    planCodeValidator(): AsyncValidatorFn {\r\n        return (control: AbstractControl): Observable<ValidationErrors | null> => {\r\n            return control.valueChanges.pipe(\r\n                debounceTime(500),\r\n                switchMap(value => this.checkCodeExisted({code: value})),\r\n                take(1),\r\n                map(result => {\r\n                    // console.log('Map result:', result);\r\n                    if (result === 0) {\r\n                        this.isPlanCodeExisted = false;\r\n                        return null;\r\n                    } else {\r\n                        this.isPlanCodeExisted = true;\r\n                        return {'exited': true};\r\n                    }\r\n                }),\r\n            );\r\n        };\r\n    }\r\n\r\n    planNameValidator(): AsyncValidatorFn {\r\n        return (control: AbstractControl): Observable<ValidationErrors | null> => {\r\n            return control.valueChanges.pipe(\r\n                debounceTime(500),\r\n                switchMap(value => this.checkNameExisted({name: value})),\r\n                take(1),\r\n                map(result => {\r\n                    // console.log('Map result:', result);\r\n                    if (result === 0) {\r\n                        this.isPlanNameExisted = false\r\n                        return null;\r\n                    } else {\r\n                        this.isPlanNameExisted = true\r\n                        return {'exited': true};\r\n                    }\r\n                }),\r\n            );\r\n        };\r\n    }\r\n\r\n    blockMinus(event: KeyboardEvent) {\r\n        const invalidChars = ['-', '+', ',', '.', 'e', 'E', 'r', 'R']; // Danh sách các ký tự không cho phép\r\n        if (invalidChars.includes(event.key)) {\r\n            event.preventDefault();\r\n        }\r\n        // if (event.key === '-' || event.key ==='+' || event.key === ',' || event.key === '.') {\r\n        //   event.preventDefault();\r\n        // }\r\n    }\r\n\r\n    checkInputValue(event: InputEvent) {\r\n        const input = event.target as HTMLInputElement;\r\n        input.value = input.value.replace(/[^0-9]/g, ''); // Chỉ cho phép nhập số\r\n    }\r\n\r\n\r\n  createPlanForm = new FormGroup({\r\n    status : new FormControl(2),\r\n    code: new FormControl(\"\", [Validators.required,Validators.maxLength(64), this.customCodeCharacterValidator()],[this.planCodeValidator()]),\r\n    name: new FormControl(\"\", [Validators.required, Validators.maxLength(255), this.customNameCharacterValidator()], [this.planNameValidator()]),\r\n    dispatchCode: new FormControl(\"\", [Validators.required, Validators.maxLength(64), this.customCodeCharacterValidator()]),\r\n    customerType: new FormControl(\"\", [Validators.required]),\r\n    subscriptionFee: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0) ]),\r\n    paidType: new FormControl(\"\", [Validators.required]),\r\n    ratingScope: new FormControl(\"0\", [Validators.required]),\r\n    provinceCode: new FormControl({value: this.userType == CONSTANTS.USER_TYPE.ADMIN ? \"\": [this.sessionService.userInfo.provinceCode], disabled:!this.isProvince}, [Validators.required]),\r\n    userIds: new FormControl(),\r\n    cycleTimeUnit: new FormControl(\"\", [Validators.required]),\r\n    cycleInterval: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\r\n    reload: new FormControl(0),\r\n    description: new FormControl(\"\", [Validators.maxLength(255)]),//MS\r\n\r\n    limitDataUsage: new FormControl(0, [Validators.required,Validators.max(9999999999), Validators.min(0)]),\r\n    limitSmsInside: new FormControl(0, [Validators.max(9999999999), Validators.min(0)]),//insideSMSFree\r\n    limitSmsOutside: new FormControl(0, [Validators.max(9999999999), Validators.min(0)]),//outsideSMSFree\r\n    dataMax: new FormControl(0, [Validators.required,Validators.max(9999999999), Validators.min(0)]),\r\n\r\n    flexible: new FormControl(0),\r\n    feePerDataUnit: new FormControl({value: 0, disabled: !this.isFlexible}, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\r\n    dataRoundUnit: new FormControl({value: 0, disabled: !this.isFlexible}, [Validators.required, Validators.max(9999999999), Validators.min(0)]),\r\n    downSpeed: new FormControl({value: 0, disabled: !this.isFlexible}, [Validators.max(9999999999), Validators.min(0)]),\r\n    squeezedSpeed: new FormControl({value: 0, disabled: !this.isFlexible}, [ Validators.max(9999999999), Validators.min(0) ]),\r\n    feeSmsInside: new FormControl({value: 0, disabled: !this.isFlexible}, [Validators.max(9999999999), Validators.min(0)]),\r\n    feeSmsOutside: new FormControl({value: 0, disabled: !this.isFlexible}, [Validators.max(9999999999), Validators.min(0)]),\r\n    maximumFee: new FormControl({value: 0, disabled: !this.isFlexible}, [Validators.max(9999999999), Validators.min(0)]),\r\n      uploadSpeed : new FormControl({value:0, disabled: !this.isFlexible}),\r\n  });\r\n\r\n  onDropdownChange(event) {\r\n    if(event.value==0){\r\n      this.isProvince = false\r\n      this.isCustomer = false\r\n      this.createPlanForm.get('provinceCode').disable({ emitEvent: false });\r\n      // this.createPlanForm.get('userIds').disable({ emitEvent: false });\r\n    }else if (event.value == 1){\r\n        // gói cước loại khách hàng\r\n        this.isProvince = true\r\n        this.isCustomer = false\r\n        this.createPlanForm.get('provinceCode').enable({ emitEvent: false });\r\n        // this.createPlanForm.get('userIds').enable({ emitEvent: false });\r\n        this.changeProvince();\r\n    }else if (event.value == 2){\r\n        // gói cước loại tỉnh/thành phố\r\n      this.isProvince = true\r\n      this.isCustomer = false\r\n      this.createPlanForm.get('provinceCode').enable({ emitEvent: false });\r\n      // this.createPlanForm.get('userIds').disable({ emitEvent: false });\r\n        this.changeProvince();\r\n    }\r\n  }\r\n\r\n  onSwitchChange() {\r\n      // console.log(this.createPlanForm);\r\n    if (this.isFlexible) {\r\n      this.createPlanForm.get('feePerDataUnit').enable({ emitEvent: false });\r\n      this.createPlanForm.get('dataRoundUnit').enable({ emitEvent: false });\r\n      this.createPlanForm.get('downSpeed').enable({ emitEvent: false });\r\n      this.createPlanForm.get('squeezedSpeed').enable({ emitEvent: false });\r\n      this.createPlanForm.get('feeSmsInside').enable({ emitEvent: false });\r\n      this.createPlanForm.get('feeSmsOutside').enable({ emitEvent: false });\r\n      this.createPlanForm.get('maximumFee').enable({ emitEvent: false });\r\n    } else {\r\n      this.createPlanForm.get('feePerDataUnit').disable({ emitEvent: false });\r\n      this.createPlanForm.get('dataRoundUnit').disable({ emitEvent: false });\r\n      this.createPlanForm.get('downSpeed').disable({ emitEvent: false });\r\n      this.createPlanForm.get('squeezedSpeed').disable({ emitEvent: false });\r\n      this.createPlanForm.get('feeSmsInside').disable({ emitEvent: false });\r\n      this.createPlanForm.get('feeSmsOutside').disable({ emitEvent: false });\r\n      this.createPlanForm.get('maximumFee').disable({ emitEvent: false });\r\n    }\r\n  }\r\n\r\n  submitForm(){\r\n    this.messageCommonService.onload();\r\n    let me = this\r\n    if (this.createPlanForm.valid) {\r\n      let data = {...this.createPlanForm.value};\r\n      if(data.reload){\r\n        data.reload = 1\r\n      }else{\r\n        data.reload = 0;\r\n      }\r\n      if(data.flexible){\r\n        data.flexible = 1\r\n          data.uploadSpeed = data.downSpeed - data.squeezedSpeed\r\n      }else{\r\n        data.flexible = 0;\r\n      }\r\n      if (this.selectItemsUser.length > 0){\r\n          let provinceSelected = me.createPlanForm.get(\"provinceCode\").value;\r\n          let currentSelected = me.selectItemsUser.filter((el) => provinceSelected.includes(el.provinceCode)).map(e => e.id);\r\n          data.userIds = currentSelected;\r\n      }\r\n      this.ratingPlanService.createRatingPlan(data,(response)=>{\r\n        // console.log(response)\r\n\r\n        this.messageCommonService.success(this.tranService.translate('global.message.saveSuccess'))\r\n        this.router.navigate(['/plans'])\r\n      }, null, ()=>{\r\n          me.messageCommonService.offload();\r\n      })\r\n    }\r\n  }\r\n\r\n    onSubmitSearchUser(){\r\n        this.pageNumberAssign = 0;\r\n        this.searchUser(this.pageNumberAssign, this.pageSizeAssign, this.sortAssign, this.searchInfoUser);\r\n    }\r\n\r\n  ngOnInit(){\r\n      this.optionTableAddCustomerAccount = {\r\n          hasClearSelected: false,\r\n          hasShowIndex: true,\r\n          hasShowChoose: true,\r\n          hasShowToggleColumn: false,\r\n      };\r\n      this.dataSetAssignPlan = {\r\n          content: [],\r\n          total: 0,\r\n      }\r\n      this.searchInfoUser = {\r\n          username: null,\r\n          fullName: null,\r\n          email: null,\r\n          provinceCode: null\r\n      }\r\n      this.formSearchUser = this.formBuilder.group(this.searchInfoUser);\r\n    this.items = [\r\n      { label: this.tranService.translate(\"global.menu.ratingplanmgmt\"), routerLink: '../'},\r\n      { label: this.tranService.translate(\"global.menu.listplan\"), routerLink: '../'},\r\n      { label: this.tranService.translate(\"global.button.create\") },\r\n    ];\r\n      this.columnsInfoUser = [\r\n          {\r\n              name: this.tranService.translate(\"ratingPlan.label.username\"),\r\n              key: \"username\",\r\n              size: \"150px\",\r\n              align: \"left\",\r\n              isShow: true,\r\n              isSort: false,\r\n          },\r\n          {\r\n              name: this.tranService.translate(\"ratingPlan.label.fullName\"),\r\n              key: \"fullName\",\r\n              size: \"150px\",\r\n              align: \"left\",\r\n              isShow: true,\r\n              isSort: false,\r\n          },\r\n          {\r\n              name: this.tranService.translate(\"ratingPlan.label.email\"),\r\n              key: \"email\",\r\n              size: \"150px\",\r\n              align: \"left\",\r\n              isShow: true,\r\n              isSort: false,\r\n          },\r\n          {\r\n              name: this.tranService.translate(\"ratingPlan.label.province\"),\r\n              key: \"provinceName\",\r\n              size: \"150px\",\r\n              align: \"left\",\r\n              isShow: true,\r\n              isSort: false,\r\n          },\r\n      ]\r\n        this.customerTypes = [\r\n            {\r\n                id: \"1\",\r\n                name: this.tranService.translate(\"ratingPlan.customerType.personal\"),\r\n            },\r\n            {\r\n                id: \"2\",\r\n                name: this.tranService.translate(\"ratingPlan.customerType.enterprise\"),\r\n            },\r\n            {\r\n                id: \"0\",\r\n                name: this.tranService.translate(\"ratingPlan.customerType.agency\"),\r\n            },\r\n            // ,\r\n            // {\r\n            //   id: \"0\",\r\n            //   name: this.tranService.translate(\"ratingPlan.customerType.agency\"),\r\n            // },\r\n        ];\r\n        this.ratingScopes = [\r\n            {\r\n                id: \"0\",\r\n                name: this.tranService.translate(\"ratingPlan.ratingScope.nativeWide\"),\r\n            },\r\n            {\r\n                id: \"2\",\r\n                name: this.tranService.translate(\"ratingPlan.ratingScope.province\"),\r\n            },\r\n            {\r\n                id: \"1\",\r\n                name: this.tranService.translate(\"ratingPlan.ratingScope.customer\"),\r\n            },\r\n        ];\r\n\r\n        this.cycles = [\r\n            {\r\n                id: \"1\",\r\n                name: this.tranService.translate(\"ratingPlan.cycle.day\")\r\n            },\r\n            {\r\n                id: \"3\",\r\n                name: this.tranService.translate(\"ratingPlan.cycle.month\")\r\n            },\r\n        ]\r\n        this.accountService.getListProvince((data) => {\r\n            let me = this;\r\n            this.provinces = data.map(el => {\r\n                if (el.code == me.sessionService.userInfo.provinceCode) {\r\n                    me.provinceInfo = `${el.name} - ${el.code}`;\r\n                }\r\n                return {\r\n                    code: el.code,\r\n                    name: `${el.name} - ${el.code}`\r\n                }\r\n            })\r\n        })\r\n\r\n        this.selectedPaidCategory = this.paidCategories[0].key\r\n\r\n        this.subPlanCode = this.createPlanForm.get('code').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('code').errors;\r\n            if (errors) {\r\n                this.isPlanCodeValid = true;\r\n            } else {\r\n                this.isPlanCodeValid = false;\r\n            }\r\n        });\r\n\r\n        this.subPlaneName = this.createPlanForm.get('name').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('name').errors;\r\n            if (errors) {\r\n                this.isPlaneNameValid = true;\r\n            } else {\r\n                this.isPlaneNameValid = false;\r\n            }\r\n        });\r\n\r\n        this.subDispatchCode = this.createPlanForm.get('dispatchCode').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('dispatchCode').errors;\r\n            if (errors) {\r\n                this.isDispatchCodeValid = true;\r\n            } else {\r\n                this.isDispatchCodeValid = false;\r\n            }\r\n        });\r\n\r\n        this.subCustomerType = this.createPlanForm.get('customerType').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('customerType').errors;\r\n            if (errors) {\r\n                this.isCustomerTypeValid = true;\r\n            } else {\r\n                this.isCustomerTypeValid = false;\r\n            }\r\n        });\r\n\r\n        this.subSubscriptionFee = this.createPlanForm.get('subscriptionFee').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('subscriptionFee').errors;\r\n            if (errors) {\r\n                this.isSubscriptionFeeValid = true;\r\n            } else {\r\n                this.isSubscriptionFeeValid = false;\r\n            }\r\n        });\r\n\r\n        this.subSubscriptionType = this.createPlanForm.get('paidType').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('paidType').errors;\r\n            if (errors) {\r\n                this.isSubscriptionTypeValid = true;\r\n            } else {\r\n                this.isSubscriptionTypeValid = false;\r\n            }\r\n        });\r\n\r\n        this.subPlanScope = this.createPlanForm.get('ratingScope').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('ratingScope').errors;\r\n            if (errors) {\r\n                this.isPlanScopeValid = true;\r\n            } else {\r\n                this.isPlanScopeValid = false;\r\n            }\r\n        });\r\n\r\n        this.subProvinceCode = this.createPlanForm.get('provinceCode').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('provinceCode').errors;\r\n            if (errors) {\r\n                this.isProvinceCodeValid = true;\r\n            } else {\r\n                this.isProvinceCodeValid = false;\r\n            }\r\n        });\r\n\r\n        this.subPlanCycle = this.createPlanForm.get('cycleTimeUnit').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('cycleTimeUnit').errors;\r\n            if (errors) {\r\n                this.isPlanCycleValid = true;\r\n            } else {\r\n                this.isPlanCycleValid = false;\r\n            }\r\n        });\r\n\r\n        this.subDuration = this.createPlanForm.get('cycleInterval').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('cycleInterval').errors;\r\n            if (errors) {\r\n                this.isDurationValid = true;\r\n            } else {\r\n                this.isDurationValid = false;\r\n            }\r\n        });\r\n\r\n        this.subDescription = this.createPlanForm.get('description').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('description').errors;\r\n            if (errors) {\r\n                this.isDescriptionValid = true;\r\n            } else {\r\n                this.isDescriptionValid = false;\r\n            }\r\n        });\r\n\r\n        this.subFreeData = this.createPlanForm.get('limitDataUsage').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('limitDataUsage').errors;\r\n            if (errors) {\r\n                this.isFreeDataValid = true;\r\n            } else {\r\n                this.isFreeDataValid = false;\r\n            }\r\n        });\r\n\r\n        this.subLimitInsideSMSFree = this.createPlanForm.get('limitSmsInside').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('limitSmsInside').errors;\r\n            if (errors) {\r\n                this.isLimitInsideSMSFreeValid = true;\r\n            } else {\r\n                this.isLimitInsideSMSFreeValid = false;\r\n            }\r\n        });\r\n\r\n        this.subLimitOutsideSMSFree = this.createPlanForm.get('limitSmsOutside').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('limitSmsOutside').errors;\r\n            if (errors) {\r\n                this.isLimitOutsideSMSFreeValid = true;\r\n            } else {\r\n                this.isLimitOutsideSMSFreeValid = false;\r\n            }\r\n        });\r\n\r\n        this.subDataMax = this.createPlanForm.get('dataMax').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('dataMax').errors;\r\n            if (errors) {\r\n                this.isDataMaxValid = true;\r\n            } else {\r\n                this.isDataMaxValid = false;\r\n            }\r\n        });\r\n\r\n        this.subFeePerUnitNumberator = this.createPlanForm.get('feePerDataUnit').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('feePerDataUnit').errors;\r\n            if (errors) {\r\n                this.isFeePerUnitNumberatorValid = true;\r\n            } else {\r\n                this.isFeePerUnitNumberatorValid = false;\r\n            }\r\n        });\r\n\r\n        this.subFeePerUnitDenominator = this.createPlanForm.get('dataRoundUnit').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('dataRoundUnit').errors;\r\n            if (errors) {\r\n                this.isFeePerUnitDenominatorValid = true;\r\n            } else {\r\n                this.isFeePerUnitDenominatorValid = false;\r\n            }\r\n        });\r\n\r\n        this.subSqueezeSpeedNumberator = this.createPlanForm.get('downSpeed').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('downSpeed').errors;\r\n            if (errors) {\r\n                this.isSqueezeSpeedNumberatorValid = true;\r\n            } else {\r\n                this.isSqueezeSpeedNumberatorValid = false;\r\n            }\r\n        });\r\n\r\n        this.subSqueezeSpeedDenominator = this.createPlanForm.get('squeezedSpeed').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('squeezedSpeed').errors;\r\n            if (errors) {\r\n                this.isSqueezeSpeedDenominatorValid = true;\r\n            } else {\r\n                this.isSqueezeSpeedDenominatorValid = false;\r\n            }\r\n        });\r\n\r\n        this.subFeePerInsideSMS = this.createPlanForm.get('feeSmsInside').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('feeSmsInside').errors;\r\n            if (errors) {\r\n                this.isFeePerInsideSMSValid = true;\r\n            } else {\r\n                this.isFeePerInsideSMSValid = false;\r\n            }\r\n        });\r\n\r\n        this.subFeePerOutsideSMS = this.createPlanForm.get('feeSmsOutside').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('feeSmsOutside').errors;\r\n            if (errors) {\r\n                this.isFeePerOutsideSMSValid = true;\r\n            } else {\r\n                this.isFeePerOutsideSMSValid = false;\r\n            }\r\n        });\r\n\r\n        this.subMaxFee = this.createPlanForm.get('maximumFee').statusChanges.subscribe(() => {\r\n            const errors = this.createPlanForm.get('maximumFee').errors;\r\n            if (errors) {\r\n                this.isMaxFeeValid = true;\r\n            } else {\r\n                this.isMaxFeeValid = false;\r\n            }\r\n        });\r\n\r\n        this.home = {icon: 'pi pi-home', routerLink: '/'};\r\n    }\r\n\r\n    ngOnDestroy(): void {\r\n        this.subPlanCode.unsubscribe();\r\n        this.subPlaneName.unsubscribe();\r\n        this.subDispatchCode.unsubscribe();\r\n        this.subCustomerType.unsubscribe();\r\n        this.subSubscriptionFee.unsubscribe();\r\n        this.subSubscriptionType.unsubscribe();\r\n        this.subPlanScope.unsubscribe();\r\n        this.subProvinceCode.unsubscribe();\r\n        this.subPlanCycle.unsubscribe();\r\n        this.subDuration.unsubscribe();\r\n        this.subDescription.unsubscribe();\r\n        this.subFreeData.unsubscribe();\r\n        this.subLimitInsideSMSFree.unsubscribe();\r\n        this.subLimitOutsideSMSFree.unsubscribe();\r\n        this.subFeePerUnitNumberator.unsubscribe();\r\n        this.subFeePerUnitDenominator.unsubscribe();\r\n        this.subSqueezeSpeedNumberator.unsubscribe();\r\n        this.subSqueezeSpeedDenominator.unsubscribe();\r\n        this.subFeePerInsideSMS.unsubscribe();\r\n        this.subFeePerOutsideSMS.unsubscribe();\r\n        this.subMaxFee.unsubscribe();\r\n    }\r\n\r\n    openDialogAddCustomerAccount() {\r\n        let provincesSelected = this.createPlanForm.get('provinceCode').value;\r\n        this.listProvince = this.provinces.filter((prov) =>\r\n            provincesSelected.includes(prov.code)\r\n        )\r\n        // this.selectItemsUser = [];\r\n        if (this.pageNumberAssign == null){\r\n            this.pageNumberAssign = 0;\r\n        }\r\n        if (this.pageSizeAssign == null){\r\n            this.pageSizeAssign = 10;\r\n        }\r\n        if (this.sortAssign == null){\r\n            this.sortAssign = \"id,desc\";\r\n        }\r\n        this.searchInfoUser.provinceCode = this.listProvince.map(e => e.code);\r\n        this.searchUser(this.pageNumberAssign, this.pageSizeAssign, this.sortAssign, this.searchInfoUser)\r\n        this.isShowDialogAddCustomerAccount = true\r\n    }\r\n\r\n\r\n    searchUser(page, limit, sort, params){\r\n      let  me = this;\r\n        this.pageNumberAssign = page;\r\n        this.pageSizeAssign = limit;\r\n        if (sort == null || sort == undefined){\r\n            this.sortAssign = \"id,desc\";\r\n            sort = \"id,desc\";\r\n        }else {\r\n            this.sortAssign = sort;\r\n        }\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort,\r\n            provinceCode: this.listProvince.map(e => e.code)\r\n        }\r\n        Object.keys(this.searchInfoUser).forEach(key => {\r\n            if(this.searchInfoUser[key] != null){\r\n                dataParams[key] = this.searchInfoUser[key];\r\n            }\r\n        })\r\n        if (this.searchInfoUser.provinceCode == null){\r\n            dataParams.provinceCode = this.listProvince.map(e => e.code);\r\n        }\r\n        this.selectItemsUserOld = [...this.selectItemsUser]\r\n        this.ratingPlanService.getUserToAddAccount(dataParams, (response) => {\r\n            me.dataSetAssignPlan = {\r\n                    content: response.content,\r\n                    total: response.totalElements\r\n                }\r\n            this.selectItemsUser = [...this.selectItemsUserOld]\r\n            })\r\n\r\n    }\r\n\r\n\r\n    changeProvince() {\r\n        let provinceSelected = this.createPlanForm.get(\"provinceCode\").value\r\n        let ratingScope = this.createPlanForm.get(\"ratingScope\").value\r\n        if (ratingScope == '1'){\r\n            if (provinceSelected.length > 0){\r\n                this.isCustomer = true;\r\n            }else {\r\n                this.isCustomer = false;\r\n            }\r\n        }\r\n    }\r\n}\r\n", "<!-- <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb> -->\r\n<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{tranService.translate(\"global.menu.listplan\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n</div>\r\n<p-card class=\"p-4\" styleClass=\"responsive-form-plans\">\r\n    <form action=\"\" [formGroup]=\"createPlanForm\" (submit)=\"submitForm()\">\r\n        <div class=\"pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid\">\r\n            <div class=\"col-6 grid-col\" >\r\n                <div class=\" grid px-4 pt-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                    <label htmlFor=\"code\"  style=\"min-width: 140px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.planCode\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <input formControlName=\"code\" pInputText id=\"code\" type=\"text\" [placeholder]=\"placeHolder.planCode\"/>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isPlanCodeValid && createPlanForm.get('code').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isPlanCodeValid && createPlanForm.get('code').hasError('maxlength')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_64\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isPlanCodeValid && createPlanForm.get('code').hasError('invalidCharacters')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.characterError_code\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isPlanCodeValid && createPlanForm.get('code').hasError('exited')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.existedCodeError\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                    <label htmlFor=\"name\"  style=\"min-width: 140px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.planName\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <input formControlName=\"name\" pInputText id=\"name\" type=\"text\" [placeholder]=\"placeHolder.planName\">\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isPlaneNameValid && createPlanForm.get('name').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isPlaneNameValid && createPlanForm.get('name').hasError('maxlength')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_255\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isPlaneNameValid && createPlanForm.get('name').hasError('invalidCharacters')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"global.message.wrongFormatName\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isPlaneNameValid && createPlanForm.get('name').hasError('exited')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.existedNameError\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                    <label htmlFor=\"dispatchCode\"  style=\"min-width: 140px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.dispatchCode\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <input formControlName=\"dispatchCode\" pInputText id=\"dispatchCode\" type=\"text\" [placeholder]=\"placeHolder.dispatchCode\"/>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isDispatchCodeValid && createPlanForm.get('dispatchCode').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isDispatchCodeValid && createPlanForm.get('dispatchCode').hasError('maxlength')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_64\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isDispatchCodeValid && createPlanForm.get('dispatchCode').hasError('invalidCharacters')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.characterError_code\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                    <label htmlFor=\"customerType\"  style=\"min-width: 140px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.customerType\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <p-dropdown formControlName=\"customerType\" [options]=\"customerTypes\" optionLabel=\"name\" optionValue=\"id\" autoDisplayFirst=\"false\" [placeholder]=\"placeHolder.customerType\"></p-dropdown>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isCustomerTypeValid && createPlanForm.get('customerType').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                    <label htmlFor=\"description\"  style=\"min-width: 140px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.description\")}}</label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <textarea formControlName=\"description\" id=\"description\" rows=\"4\" cols=\"30\" [placeholder]=\"placeHolder.description\" pInputText></textarea>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isDescriptionValid && createPlanForm.get('description').hasError('maxlength')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_255\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-6 grid-col\">\r\n                <div class=\"field grid px-4 pt-4 flex flex-row flex-nowrap responsive-size-input\" >\r\n                    <label htmlFor=\"subscriptionFee\"  style=\"min-width: 140px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.subscriptionFee\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <input formControlName=\"subscriptionFee\" pInputText id=\"subscriptionFee\" type=\"number\" (keydown)=\"blockMinus($event)\" (input)=\"checkInputValue($event)\"  [placeholder]=\"placeHolder.subscriptionFee\"/>\r\n                    </div>\r\n                    <div class=\"my-auto pr-1\" style=\"min-width: 90px;\">\r\n                        {{tranService.translate(\"ratingPlan.text.textDong\")}} <br/> {{tranService.translate(\"ratingPlan.text.vat\")}}\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isSubscriptionFeeValid && createPlanForm.get('subscriptionFee').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isSubscriptionFeeValid && createPlanForm.get('subscriptionFee').hasError('max')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isSubscriptionFeeValid && createPlanForm.get('subscriptionFee').hasError('min')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"field grid px-4 pb-2 custom-responsive-field\">\r\n                    <div class=\"col-12 mb-2 md:col-2 md:mb-0 flex flex-row align-items-center radio-group wrapper\">\r\n                        <div *ngFor=\"let paidType of paidCategories\" class=\"field-checkbox my-auto\"  style=\"min-width: 140px; min-height:35px;\">\r\n                            <p-radioButton formControlName=\"paidType\" [inputId]=\"paidType.key\" name=\"paidType\" [value]=\"paidType.key\" [(ngModel)]=\"selectedPaidCategory\"></p-radioButton>\r\n                            <label [for]=\"paidType.key\" class=\"ml-2\">{{ paidType.name }}</label>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"switch-group-wrapper flex flex-row align-items-center mt-2\">\r\n                        <label htmlFor=\"reload\"  style=\"min-width: 130px;\" class=\"col-12 mb-2 md:col-2 md:mb-0 responsive-switch-label\">{{tranService.translate(\"ratingPlan.label.autoReload\")}}</label>\r\n                        <div class=\"col-12 md:col-10 flex-1\">\r\n                            <p-inputSwitch formControlName=\"reload\" class=\"flex align-items-center\"></p-inputSwitch>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\" >\r\n                    <label htmlFor=\"cycle\"  style=\"min-width: 140px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.cycle\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <p-dropdown formControlName=\"cycleTimeUnit\" [options]=\"cycles\" optionLabel=\"name\" optionValue=\"id\" autoDisplayFirst=\"true\" [placeholder]=\"placeHolder.planCycle\"></p-dropdown>\r\n                    </div>\r\n                    <div class=\"my-auto pr-1\" style=\"min-width: 90px;\">\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isPlanCycleValid && createPlanForm.get('cycleTimeUnit').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                    <label htmlFor=\"cycleInterval\"  style=\"min-width: 140px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.duration\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <input formControlName=\"cycleInterval\" pInputText id=\"cycleInterval\" type=\"number\" (keydown)=\"blockMinus($event)\" (input)=\"checkInputValue($event)\" [placeholder]=\"placeHolder.duration\"/>\r\n                    </div>\r\n                    <div *ngIf=\"createPlanForm.get('cycleTimeUnit').value == '1'\" class=\"my-auto pr-1\" style=\"min-width: 90px;\">\r\n                        {{tranService.translate(\"ratingPlan.cycle.day\")}}\r\n                    </div>\r\n                    <div *ngIf=\"createPlanForm.get('cycleTimeUnit').value == '3'\" class=\"my-auto pr-1\" style=\"min-width: 90px;\">\r\n                        {{tranService.translate(\"ratingPlan.cycle.month\")}}\r\n                    </div>\r\n                    <div *ngIf=\"createPlanForm.get('cycleTimeUnit').value != '1' && createPlanForm.get('cycleTimeUnit').value != '3'\" class=\"my-auto pr-1\" style=\"min-width: 90px;\">\r\n                        {{tranService.translate(\"ratingPlan.text.dayMonth\")}}\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isDurationValid && createPlanForm.get('cycleInterval').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isDurationValid && createPlanForm.get('cycleInterval').hasError('max')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isDurationValid && createPlanForm.get('cycleInterval').hasError('min')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\" >\r\n                    <label htmlFor=\"ratingScope\"  style=\"min-width: 140px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.ratingScope\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <p-dropdown (onChange)=\"onDropdownChange($event)\" formControlName=\"ratingScope\" [options]=\"ratingScopes\" optionLabel=\"name\" optionValue=\"id\" autoDisplayFirst=\"false\" [placeholder]=\"placeHolder.planScope\"></p-dropdown>\r\n                    </div>\r\n                    <div class=\"my-auto pr-1\" style=\"min-width: 90px;\">\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isPlanScopeValid && createPlanForm.get('ratingScope').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div *ngIf=\"isProvince\" class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\" >\r\n                    <label htmlFor=\"provinceCode\" style=\"min-width: 140px;\" class=\"col-fixed\">{{tranService.translate(\"ratingPlan.label.province\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\" style=\"max-width: calc(100% - 230px);\">\r\n                        <p-multiSelect *ngIf=\"userType == allUserType.ADMIN\"\r\n                                       formControlName=\"provinceCode\"\r\n                                       [options]=\"provinces\"\r\n                                       [showToggleAll]=\"false\"\r\n                                       display=\"chip\" optionLabel=\"name\"\r\n                                       optionValue=\"code\" autoDisplayFirst=\"false\"\r\n                                       [placeholder]=\"placeHolder.provinceCode\"\r\n                                       (onChange)=\"changeProvince()\"\r\n                        ></p-multiSelect>\r\n                        <span *ngIf=\"userType != allUserType.ADMIN\">{{provinceInfo}}</span>\r\n                    </div>\r\n                    <div class=\"col-fixed\" style=\"min-width: 90px;\">\r\n                    </div>\r\n                </div>\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3\">\r\n                    <div style=\"min-width: 140px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isProvinceCodeValid && createPlanForm.get('provinceCode').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div *ngIf=\"isCustomer\" class=\"field grid px-4 flex flex-row flex-nowrap3 responsive-size-input\">\r\n                    <label htmlFor=\"roles\" class=\"col-fixed\" style=\"min-width: 140px; cursor: pointer; text-decoration: underline; color: blue; transition: color 0.3s;\" (click)=\"openDialogAddCustomerAccount()\">{{tranService.translate(\"account.label.addCustomerAccount\")}}</label>\r\n                    <!--                    <div class=\"col\" style=\"max-width: calc(100% - 230px) !important;\">-->\r\n                    <!--                        <vnpt-select-->\r\n                    <!--                            [control]=\"controlComboSelect\"-->\r\n                    <!--                            class=\"w-full\"-->\r\n                    <!--                            [(value)]=\"customerCode\"-->\r\n                    <!--                            [placeholder]=\"tranService.translate('account.text.selectCustomers')\"-->\r\n                    <!--                            objectKey=\"searchUserForRatingPlan\"-->\r\n                    <!--                            paramKey=\"keySearch\"-->\r\n                    <!--                            keyReturn=\"id\"-->\r\n                    <!--                            displayPattern=\"${provinceName}-${username}-${fullName}\"-->\r\n                    <!--                            typeValue=\"primitive\"-->\r\n                    <!--                            [required]=\"isCustomer\"-->\r\n                    <!--                            (onchange)=\"onChangeCustomers()\"-->\r\n                    <!--                        ></vnpt-select>-->\r\n                    <!--                    </div>-->\r\n                </div>\r\n\r\n\r\n            </div>\r\n        </div>\r\n        <h4 class=\"ml-2\">{{tranService.translate(\"ratingPlan.label.flat\")}}</h4>\r\n        <div class=\"pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid\">\r\n            <div class=\"flex-1\">\r\n                <div class=\"field grid px-4 pt-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                    <label htmlFor=\"limitDataUsage\"  style=\"min-width: 170px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.freeData\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <input formControlName=\"limitDataUsage\" pInputText id=\"limitDataUsage\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [placeholder]=\"placeHolder.freeData\"/>\r\n                    </div>\r\n                    <div class=\"my-auto pr-1\" style=\"min-width: 40px;\">\r\n                        (MB)\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 170px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isFreeDataValid && createPlanForm.get('limitDataUsage').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isFreeDataValid && createPlanForm.get('limitDataUsage').hasError('max')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isFreeDataValid && createPlanForm.get('limitDataUsage').hasError('min')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                    <label htmlFor=\"limitDataUsage\"  style=\"min-width: 170px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.placeHolder.dataMax\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <input formControlName=\"dataMax\" pInputText id=\"dataMax\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [placeholder]=\"placeHolder.dataMax\"/>\r\n                    </div>\r\n                    <div class=\"my-auto pr-1\" style=\"min-width: 40px;\">\r\n                        (MB)\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 170px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isDataMaxValid && createPlanForm.get('dataMax').hasError('required')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isDataMaxValid && createPlanForm.get('dataMax').hasError('max')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isDataMaxValid && createPlanForm.get('dataMax').hasError('min')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n<!--                <div class=\"field grid px-4 pb-3 flex flex-row flex-nowrap\">-->\r\n<!--                    <label htmlFor=\"outsideSMSFree\"  style=\"min-width: 170px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.outsideSMSFree\")}}</label>-->\r\n<!--                    <div class=\"col-12 md:col-10 flex-1\">-->\r\n<!--                        <input formControlName=\"limitSmsOutside\" pInputText id=\"outsideSMSFree\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [placeholder]=\"placeHolder.outsideSMSFree\"/>-->\r\n<!--                    </div>-->\r\n<!--                    <div class=\"my-auto pr-1\" style=\"min-width: 40px;\"></div>-->\r\n<!--                </div>-->\r\n\r\n<!--                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3\">-->\r\n<!--                    <div style=\"min-width: 170px;\"></div>-->\r\n<!--                    <div class=\"col-11 md:col-11 py-0\">-->\r\n<!--                        <div *ngIf=\"isLimitOutsideSMSFreeValid && createPlanForm.get('limitSmsOutside').hasError('max')\" class=\"text-red-500\">-->\r\n<!--                            {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}-->\r\n<!--                        </div>-->\r\n<!--                        <div *ngIf=\"isLimitOutsideSMSFreeValid && createPlanForm.get('limitSmsOutside').hasError('min')\" class=\"text-red-500\">-->\r\n<!--                            {{tranService.translate(\"global.message.min\",{value: 0})}}-->\r\n<!--                        </div>-->\r\n<!--                    </div>-->\r\n<!--                </div>-->\r\n            </div>\r\n            <div class=\"flex-1\">\r\n                <div class=\"field grid px-4 pt-4 flex flex-row flex-nowrap responsive-size-input\" >\r\n                    <label htmlFor=\"insideSMSFree\"  style=\"min-width: 170px;\" class=\"col-12 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.insideSMSFree\")}}</label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <input formControlName=\"limitSmsInside\" pInputText id=\"insideSMSFree\" type=\"number\" (keydown)=\"blockMinus($event)\" (input)=\"checkInputValue($event)\"  [placeholder]=\"placeHolder.insideSMSFree\"/>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 170px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isLimitInsideSMSFreeValid && createPlanForm.get('limitSmsInside').hasError('max')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isLimitInsideSMSFreeValid && createPlanForm.get('limitSmsInside').hasError('min')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                    <label htmlFor=\"outsideSMSFree\"  style=\"min-width: 170px;\" class=\"col-12 mb-2 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.outsideSMSFree\")}}</label>\r\n                    <div class=\"col-12 md:col-10 flex-1\">\r\n                        <input formControlName=\"limitSmsOutside\" pInputText id=\"outsideSMSFree\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [placeholder]=\"placeHolder.outsideSMSFree\"/>\r\n                    </div>\r\n                    <!--                    <div class=\"my-auto pr-1\" style=\"min-width: 40px;\"></div>-->\r\n                </div>\r\n\r\n                <div class=\"grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input\">\r\n                    <div style=\"min-width: 170px;\"></div>\r\n                    <div class=\"col-11 md:col-11 py-0\">\r\n                        <div *ngIf=\"isLimitOutsideSMSFreeValid && createPlanForm.get('limitSmsOutside').hasError('max')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                        </div>\r\n                        <div *ngIf=\"isLimitOutsideSMSFreeValid && createPlanForm.get('limitSmsOutside').hasError('min')\" class=\"text-red-500\">\r\n                            {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex flex-row gap-3 ml-2 mt-4 mb-3\">\r\n            <h4 class=\"m-0\">{{tranService.translate(\"ratingPlan.label.flexible\")}}</h4>\r\n            <p-inputSwitch formControlName=\"flexible\" class=\"\" [(ngModel)]=\"isFlexible\" (onChange)=\"onSwitchChange()\"></p-inputSwitch>\r\n        </div>\r\n        <div class=\"pt-0 shadow-2 border-round-md mb-4 m-1 flex p-fluid p-formgrid flex-column grid\">\r\n            <div class=\"field grid px-4 pt-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                <label htmlFor=\"feePerDataUnit\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.feePerUnit\")}}<span class=\"text-red-500\">*</span></label>\r\n                <div class=\"col-12 md:col-10 flex-1\">\r\n                    <input class=\"feePerDataUnit\" formControlName=\"feePerDataUnit\" pInputText id=\"feePerDataUnit\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [disabled]=\"!isFlexible\" [placeholder]=\"placeHolder.feePerUnit\"/>\r\n                </div>\r\n                <div class=\"my-auto mx-auto\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 10px;\">/</div>\r\n                <div class=\"col-12 md:col-10 flex-1\">\r\n                    <input formControlName=\"dataRoundUnit\" pInputText id=\"dataRoundUnit\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [disabled]=\"!isFlexible\" [placeholder]=\"placeHolder.feePerUnit\"/>\r\n                </div>\r\n                <div [style.color]=\"!isFlexible ? 'gray' : 'black'\" class=\"my-auto\" style=\"min-width: 40px;\">(KB)</div>\r\n            </div>\r\n\r\n            <div class=\"grid px-4 flex flex-row flex-nowrap mb-3\">\r\n                <div style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0 p-0 responsive-div-error-2\"></div>\r\n                <div class=\"col-11 md:col-5 py-0\">\r\n                    <div *ngIf=\"isFeePerUnitNumberatorValid && createPlanForm.get('feePerDataUnit').hasError('required')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isFeePerUnitNumberatorValid && createPlanForm.get('feePerDataUnit').hasError('max')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isFeePerUnitNumberatorValid && createPlanForm.get('feePerDataUnit').hasError('min')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                    </div>\r\n                    <!-- <div *ngIf=\"isShowValid && createPlanForm.get('dataRoundUnit').hasError('max')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                    </div> -->\r\n                </div>\r\n                <div class=\"col-11 md:col-5 py-0 responsive-error-2\">\r\n                    <div *ngIf=\"isFeePerUnitDenominatorValid && createPlanForm.get('dataRoundUnit').hasError('required')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.requiredError\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isFeePerUnitDenominatorValid && createPlanForm.get('dataRoundUnit').hasError('max')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isFeePerUnitDenominatorValid && createPlanForm.get('dataRoundUnit').hasError('min')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                    </div>\r\n                    <!-- <div *ngIf=\"isShowValid && createPlanForm.get('dataRoundUnit').hasError('max')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                    </div> -->\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-size-input\">\r\n                <label htmlFor=\"downSpeed\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.squeezeSpeed\")}}</label>\r\n                <div class=\"col-12 md:col-10 flex-1\">\r\n                    <input formControlName=\"downSpeed\" pInputText id=\"downSpeed\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [disabled]=\"!isFlexible\" [placeholder]=\"placeHolder.squeezedSpeed\"/>\r\n                </div>\r\n                <div class=\"my-auto mx-auto\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 10px;\">/</div>\r\n                <div class=\"col-12 md:col-10 flex-1\">\r\n                    <input formControlName=\"squeezedSpeed\" pInputText id=\"squeezedSpeed\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [disabled]=\"!isFlexible\" [placeholder]=\"placeHolder.squeezedSpeed\"/>\r\n                </div>\r\n                <div [style.color]=\"!isFlexible ? 'gray' : 'black'\" class=\"my-auto\" style=\"min-width: 40px;\">(Kbps)</div>\r\n            </div>\r\n\r\n            <div class=\"grid px-4 flex flex-row flex-nowrap mb-3\">\r\n                <div style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0 p-0 responsive-div-error-2\"></div>\r\n                <div class=\"col-11 md:col-5 py-0\">\r\n                    <div *ngIf=\"isSqueezeSpeedNumberatorValid && createPlanForm.get('downSpeed').hasError('max')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isSqueezeSpeedNumberatorValid && createPlanForm.get('downSpeed').hasError('min')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-11 md:col-5 py-0 responsive-error-2\">\r\n                    <div *ngIf=\"isSqueezeSpeedDenominatorValid && createPlanForm.get('squeezedSpeed').hasError('max')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isSqueezeSpeedDenominatorValid && createPlanForm.get('squeezedSpeed').hasError('min')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-div\">\r\n                <label htmlFor=\"feeSmsInside\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.feePerInsideSMS\")}}</label>\r\n                <div class=\"col-12 md:col-10 flex-1 \">\r\n                    <input  formControlName=\"feeSmsInside\" pInputText id=\"feeSmsInside\" type=\"number\" (keydown)=\"blockMinus($event)\" (input)=\"checkInputValue($event)\"  [disabled]=\"!isFlexible\" [placeholder]=\"placeHolder.feePerInsideSMS\"/>\r\n                </div>\r\n                <div class=\"my-auto mx-auto\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 10px;\"></div>\r\n                <div class=\"col-12 md:col-10 flex-1\">\r\n                </div>\r\n                <div [style.color]=\"!isFlexible ? 'gray' : 'black'\" class=\"my-auto\" style=\"min-width: 40px;\"></div>\r\n            </div>\r\n\r\n            <div class=\"grid px-4 flex flex-row flex-nowrap mb-3\">\r\n                <div style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0 p-0 responsive-div-error\"></div>\r\n                <div class=\"col-11 md:col-11 py-0 responsive-error-1\">\r\n                    <div *ngIf=\"isFeePerInsideSMSValid && createPlanForm.get('feeSmsInside').hasError('max')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isFeePerInsideSMSValid && createPlanForm.get('feeSmsInside').hasError('min')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"field grid px-4 flex flex-row flex-nowrap responsive-div\">\r\n                <label htmlFor=\"feeSmsOutside\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.feePerOutsideSMS\")}}</label>\r\n                <div class=\"col-12 md:col-10 flex-1 feeSmsOutside\">\r\n                    <input formControlName=\"feeSmsOutside\" pInputText id=\"feeSmsOutside\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [disabled]=\"!isFlexible\" [placeholder]=\"placeHolder.feePerOutsideSMS\"/>\r\n                </div>\r\n                <div class=\"my-auto mx-auto\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 10px;\"></div>\r\n                <div class=\"col-12 md:col-10 flex-1\">\r\n                </div>\r\n                <div [style.color]=\"!isFlexible ? 'gray' : 'black'\" class=\"my-auto\" style=\"min-width: 40px;\"></div>\r\n            </div>\r\n\r\n            <div class=\"grid px-4 flex flex-row flex-nowrap mb-3\">\r\n                <div style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0 p-0 responsive-div-error\"></div>\r\n                <div class=\"col-11 md:col-11 py-0 responsive-error-1\">\r\n                    <div *ngIf=\"isFeePerOutsideSMSValid && createPlanForm.get('feeSmsOutside').hasError('max')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isFeePerOutsideSMSValid && createPlanForm.get('feeSmsOutside').hasError('min')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"field grid px-4 pb-3 flex flex-row flex-nowrap responsive-div\" >\r\n                <label htmlFor=\"maximumFee\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0\">{{tranService.translate(\"ratingPlan.label.maxFee\")}}</label>\r\n                <div class=\"col-12 md:col-10 flex-1 maximumFee\">\r\n                    <input formControlName=\"maximumFee\" pInputText id=\"maximumFee\" type=\"number\" (keydown)=\"blockMinus($event)\"  (input)=\"checkInputValue($event)\" [disabled]=\"!isFlexible\" [placeholder]=\"placeHolder.maxFee\"/>\r\n                </div>\r\n                <div class=\"my-auto mx-auto\" [style.color]=\"!isFlexible ? 'gray' : 'black'\" style=\"min-width: 10px;\"></div>\r\n                <div class=\"col-12 md:col-10 flex-1\">\r\n                </div>\r\n                <div [style.color]=\"!isFlexible ? 'gray' : 'black'\" class=\"my-auto\" style=\"min-width: 40px;\"></div>\r\n            </div>\r\n\r\n            <div class=\"grid px-4 flex flex-row flex-nowrap mb-3\">\r\n                <div style=\"min-width: 190px;\" class=\"col-12 md:col-2 md:mb-0 p-0 responsive-div-error\"></div>\r\n                <div class=\"col-11 md:col-11 py-0 responsive-error-1\">\r\n                    <div *ngIf=\"isMaxFeeValid && createPlanForm.get('maximumFee').hasError('max')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"ratingPlan.error.lengthError_number\")}}\r\n                    </div>\r\n                    <div *ngIf=\"isMaxFeeValid && createPlanForm.get('maximumFee').hasError('min')\" class=\"text-red-500\">\r\n                        {{tranService.translate(\"global.message.min\",{value: 0})}}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex flex-row justify-content-center gap-3 p-2\">\r\n            <a routerLink=\"/plans\"><button pButton [label]=\"tranService.translate('global.button.cancel')\" class=\"p-button-secondary p-button-outlined\" type=\"button\"></button></a>\r\n            <button pButton [label]=\"tranService.translate('global.button.save')\" class=\"p-button-info\" type=\"submit\" [disabled]=\"isPlanCodeExisted||isPlanNameExisted||createPlanForm.invalid\"></button>\r\n        </div>\r\n    </form>\r\n</p-card>\r\n\r\n<form [formGroup]=\"formSearchUser\" (ngSubmit)=\"onSubmitSearchUser()\">\r\n    <div class=\"flex justify-content-center dialog-push-group\">\r\n        <p-dialog [header]=\"tranService.translate('account.label.addCustomerAccount')\" [(visible)]=\"isShowDialogAddCustomerAccount\" [modal]=\"true\" [style]=\"{ width: '850px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n            <div class=\"grid\">\r\n                <!-- Ten dang nhap -->\r\n                <div class=\"col-3\">\r\n                    <span class=\"p-float-label\">\r\n                        <input pInputText\r\n                               class=\"w-full\"\r\n                               [(ngModel)]=\"searchInfoUser.username\"\r\n                               pInputText id=\"username\"\r\n                               formControlName=\"username\"\r\n                        />\r\n                        <label htmlFor=\"username\">{{tranService.translate(\"ratingPlan.label.username\")}}</label>\r\n                    </span>\r\n                </div>\r\n                <!-- Ho ten -->\r\n                <div class=\"col-3\">\r\n                    <span class=\"p-float-label\">\r\n                        <input pInputText\r\n                               class=\"w-full\"\r\n                               [(ngModel)]=\"searchInfoUser.fullName\"\r\n                               pInputText id=\"fullName\"\r\n                               formControlName=\"fullName\"\r\n                        />\r\n                        <label htmlFor=\"fullName\">{{tranService.translate(\"ratingPlan.label.fullName\")}}</label>\r\n                    </span>\r\n                </div>\r\n                <!-- Thanh pho -->\r\n                <div class=\"col-3\" [class]=\"userType == allUserType.ADMIN ? '' : 'flex flex-row justify-content-start align-items-center'\">\r\n                    <span class=\"p-float-label\" *ngIf=\"userType == allUserType.ADMIN\">\r\n                        <p-multiSelect styleClass=\"w-full\" [showClear]=\"true\"\r\n                                    id=\"provinceCode\" [autoDisplayFirst]=\"false\"\r\n                                       [(ngModel)]=\"searchInfoUser.provinceCode\"\r\n                                    [options]=\"listProvince\"\r\n                                    optionLabel=\"name\"\r\n                                    optionValue=\"code\"\r\n                                    formControlName=\"provinceCode\"\r\n                        ></p-multiSelect>\r\n                        <label for=\"provinceCode\">{{tranService.translate(\"ratingPlan.label.province\")}}</label>\r\n                    </span>\r\n                    <span *ngIf=\"userType != allUserType.ADMIN\">{{tranService.translate(\"account.label.province\")}}: {{provinceInfo}}</span>\r\n                </div>\r\n                <div class=\"col-3 pb-0\">\r\n                    <p-button icon=\"pi pi-search\"\r\n                              styleClass=\"p-button-rounded p-button-secondary p-button-text button-search\"\r\n                              type=\"submit\"\r\n                    ></p-button>\r\n                </div>\r\n            </div>\r\n            <table-vnpt\r\n                [fieldId]=\"'id'\"\r\n                [pageNumber]=\"pageNumberAssign\"\r\n                [pageSize]=\"pageSizeAssign\"\r\n                [(selectItems)]=\"selectItemsUser\"\r\n                [columns]=\"columnsInfoUser\"\r\n                [dataSet]=\"dataSetAssignPlan\"\r\n                [options]=\"optionTableAddCustomerAccount\"\r\n                [loadData]=\"searchUser.bind(this)\"\r\n                [rowsPerPageOptions]=\"[5,10,20, 25, 50]\"\r\n                [scrollHeight]=\"'400px'\"\r\n                [sort]=\"sort\"\r\n                [params]=\"searchInfoUser\"\r\n            ></table-vnpt>\r\n<!--            <div class=\"flex flex-row justify-content-center align-items-center\" style=\"padding-top: 30px\">-->\r\n<!--&lt;!&ndash;                <p-button styleClass=\"p-button-secondary\" [label]=\"tranService.translate('global.button.cancel')\" (click)=\"isShowDialogAddCustomerAccount = false\" [style]=\"{'margin-right': '20px'}\"></p-button>&ndash;&gt;-->\r\n<!--&lt;!&ndash;                <p-button styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.save')\" (click)=\"isShowDialogAddCustomerAccount = false\" ></p-button>&ndash;&gt;-->\r\n<!--            </div>-->\r\n        </p-dialog>\r\n    </div>\r\n</form>\r\n"], "mappings": "AAEA,SAGIA,WAAW,EACXC,SAAS,EAGTC,UAAU,QACP,gBAAgB;AAEvB,SAAQC,UAAU,EAAgBC,YAAY,EAAEC,GAAG,EAAEC,SAAS,EAAEC,IAAI,QAAO,MAAM;AACjF,SAAQC,aAAa,QAAO,wBAAwB;AACpD,SAAQC,iBAAiB,QAAO,+CAA+C;AAC/E,SAAQC,cAAc,QAAO,4CAA4C;AACzE,SAAQC,SAAS,QAAO,iCAAiC;AACzD,SAAQC,gBAAgB,QAAO,4DAA4D;;;;;;;;;;;;;;;;;;;ICInEC,EAAA,CAAAC,cAAA,cAAqG;IACjGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAsG;IAClGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAI,MAAA,CAAAF,WAAA,CAAAC,SAAA,yCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAA8G;IAC1GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAK,MAAA,CAAAH,WAAA,CAAAC,SAAA,8CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAmG;IAC/FD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAM,MAAA,CAAAJ,WAAA,CAAAC,SAAA,2CACJ;;;;;IAcAR,EAAA,CAAAC,cAAA,cAAsG;IAClGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAO,MAAA,CAAAL,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAuG;IACnGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAQ,MAAA,CAAAN,WAAA,CAAAC,SAAA,0CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAA+G;IAC3GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAS,MAAA,CAAAP,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAoG;IAChGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAU,MAAA,CAAAR,WAAA,CAAAC,SAAA,2CACJ;;;;;IAcAR,EAAA,CAAAC,cAAA,cAAiH;IAC7GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAW,MAAA,CAAAT,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAkH;IAC9GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAY,MAAA,CAAAV,WAAA,CAAAC,SAAA,yCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAA0H;IACtHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAa,OAAA,CAAAX,WAAA,CAAAC,SAAA,8CACJ;;;;;IAcAR,EAAA,CAAAC,cAAA,cAAiH;IAC7GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAc,OAAA,CAAAZ,WAAA,CAAAC,SAAA,wCACJ;;;;;IAcAR,EAAA,CAAAC,cAAA,cAAgH;IAC5GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAe,OAAA,CAAAb,WAAA,CAAAC,SAAA,0CACJ;;;;;IAkBAR,EAAA,CAAAC,cAAA,cAAuH;IACnHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAgB,OAAA,CAAAd,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAkH;IAC9GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAiB,OAAA,CAAAf,WAAA,CAAAC,SAAA,6CACJ;;;;;;;;;;IACAR,EAAA,CAAAC,cAAA,cAAkH;IAC9GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAkB,OAAA,CAAAhB,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;;IAMAzB,EAAA,CAAAC,cAAA,eAAwH;IACVD,EAAA,CAAA0B,UAAA,2BAAAC,2EAAAC,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA/B,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAiC,WAAA,CAAAF,OAAA,CAAAG,oBAAA,GAAAN,MAAA;IAAA,EAAkC;IAAC5B,EAAA,CAAAG,YAAA,EAAgB;IAC7JH,EAAA,CAAAC,cAAA,iBAAyC;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAD1BH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAmC,UAAA,YAAAC,YAAA,CAAAC,GAAA,CAAwB,UAAAD,YAAA,CAAAC,GAAA,aAAAC,OAAA,CAAAJ,oBAAA;IAC3DlC,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAmC,UAAA,QAAAC,YAAA,CAAAC,GAAA,CAAoB;IAAcrC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAuC,iBAAA,CAAAH,YAAA,CAAAI,IAAA,CAAmB;;;;;IAsBhExC,EAAA,CAAAC,cAAA,cAA+G;IAC3GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAoC,OAAA,CAAAlC,WAAA,CAAAC,SAAA,wCACJ;;;;;IASJR,EAAA,CAAAC,cAAA,cAA4G;IACxGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAqC,OAAA,CAAAnC,WAAA,CAAAC,SAAA,8BACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAA4G;IACxGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAsC,OAAA,CAAApC,WAAA,CAAAC,SAAA,gCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAgK;IAC5JD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAuC,OAAA,CAAArC,WAAA,CAAAC,SAAA,kCACJ;;;;;IAMIR,EAAA,CAAAC,cAAA,cAA8G;IAC1GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAwC,OAAA,CAAAtC,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAyG;IACrGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAyC,OAAA,CAAAvC,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAyG;IACrGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA0C,OAAA,CAAAxC,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAgBAzB,EAAA,CAAAC,cAAA,cAA6G;IACzGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA2C,OAAA,CAAAzC,WAAA,CAAAC,SAAA,wCACJ;;;;;;IAOAR,EAAA,CAAAC,cAAA,yBAQC;IADcD,EAAA,CAAA0B,UAAA,sBAAAuB,uFAAA;MAAAjD,EAAA,CAAA6B,aAAA,CAAAqB,IAAA;MAAA,MAAAC,OAAA,GAAAnD,EAAA,CAAAgC,aAAA;MAAA,OAAYhC,EAAA,CAAAiC,WAAA,CAAAkB,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAC3CpD,EAAA,CAAAG,YAAA,EAAgB;;;;IANFH,EAAA,CAAAmC,UAAA,YAAAkB,OAAA,CAAAC,SAAA,CAAqB,wCAAAD,OAAA,CAAAE,WAAA,CAAAC,YAAA;;;;;IAOpCxD,EAAA,CAAAC,cAAA,WAA4C;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAvBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAuC,iBAAA,CAAAkB,OAAA,CAAAC,YAAA,CAAgB;;;;;IAZpE1D,EAAA,CAAAC,cAAA,cAAiG;IACnBD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnKH,EAAA,CAAAC,cAAA,eAAwD;IACpDD,EAAA,CAAA2D,UAAA,IAAAC,oDAAA,6BAQiB;IACjB5D,EAAA,CAAA2D,UAAA,IAAAE,2CAAA,oBAAmE;IACvE7D,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAA8D,SAAA,eACM;IACV9D,EAAA,CAAAG,YAAA,EAAM;;;;IAfwEH,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAuC,iBAAA,CAAAwB,OAAA,CAAAxD,WAAA,CAAAC,SAAA,8BAAsD;IAE5GR,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAmC,UAAA,SAAA4B,OAAA,CAAAC,QAAA,IAAAD,OAAA,CAAAE,WAAA,CAAAC,KAAA,CAAmC;IAS5ClE,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAmC,UAAA,SAAA4B,OAAA,CAAAC,QAAA,IAAAD,OAAA,CAAAE,WAAA,CAAAC,KAAA,CAAmC;;;;;IAQ1ClE,EAAA,CAAAC,cAAA,cAAiH;IAC7GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA8D,OAAA,CAAA5D,WAAA,CAAAC,SAAA,wCACJ;;;;;;IAGRR,EAAA,CAAAC,cAAA,eAAiG;IACwDD,EAAA,CAAA0B,UAAA,mBAAA0C,4DAAA;MAAApE,EAAA,CAAA6B,aAAA,CAAAwC,IAAA;MAAA,MAAAC,OAAA,GAAAtE,EAAA,CAAAgC,aAAA;MAAA,OAAShC,EAAA,CAAAiC,WAAA,CAAAqC,OAAA,CAAAC,4BAAA,EAA8B;IAAA,EAAC;IAACvE,EAAA,CAAAE,MAAA,GAA6D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAArEH,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAuC,iBAAA,CAAAiC,OAAA,CAAAjE,WAAA,CAAAC,SAAA,qCAA6D;;;;;IAqCvPR,EAAA,CAAAC,cAAA,cAA+G;IAC3GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAoE,OAAA,CAAAlE,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAA0G;IACtGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAqE,OAAA,CAAAnE,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAA0G;IACtGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAsE,OAAA,CAAApE,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAgBAzB,EAAA,CAAAC,cAAA,cAAuG;IACnGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAuE,OAAA,CAAArE,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAkG;IAC9FD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAwE,OAAA,CAAAtE,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAkG;IAC9FD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAyE,OAAA,CAAAvE,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAmCAzB,EAAA,CAAAC,cAAA,cAAoH;IAChHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA0E,OAAA,CAAAxE,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAoH;IAChHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA2E,OAAA,CAAAzE,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAcAzB,EAAA,CAAAC,cAAA,cAAsH;IAClHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA4E,OAAA,CAAA1E,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAsH;IAClHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA6E,OAAA,CAAA3E,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAyBJzB,EAAA,CAAAC,cAAA,cAA2H;IACvHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA8E,OAAA,CAAA5E,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAsH;IAClHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA+E,OAAA,CAAA7E,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAsH;IAClHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAgF,OAAA,CAAA9E,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAMAzB,EAAA,CAAAC,cAAA,cAA2H;IACvHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAiF,OAAA,CAAA/E,WAAA,CAAAC,SAAA,wCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAsH;IAClHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAkF,OAAA,CAAAhF,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAsH;IAClHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAmF,OAAA,CAAAjF,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAsBAzB,EAAA,CAAAC,cAAA,cAAmH;IAC/GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAoF,OAAA,CAAAlF,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAmH;IAC/GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAqF,OAAA,CAAAnF,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAGAzB,EAAA,CAAAC,cAAA,cAAwH;IACpHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAsF,OAAA,CAAApF,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAwH;IACpHD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAuF,OAAA,CAAArF,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAkBAzB,EAAA,CAAAC,cAAA,cAA+G;IAC3GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAwF,OAAA,CAAAtF,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAA+G;IAC3GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAyF,OAAA,CAAAvF,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAkBAzB,EAAA,CAAAC,cAAA,cAAiH;IAC7GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA0F,OAAA,CAAAxF,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAiH;IAC7GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA2F,OAAA,CAAAzF,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;IAkBAzB,EAAA,CAAAC,cAAA,cAAoG;IAChGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA4F,OAAA,CAAA1F,WAAA,CAAAC,SAAA,6CACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAoG;IAChGD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA6F,OAAA,CAAA3F,WAAA,CAAAC,SAAA,uBAAAR,EAAA,CAAAwB,eAAA,IAAAC,GAAA,QACJ;;;;;;IAyCAzB,EAAA,CAAAC,cAAA,eAAkE;IAG/CD,EAAA,CAAA0B,UAAA,2BAAAyE,6EAAAvE,MAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAuE,IAAA;MAAA,MAAAC,OAAA,GAAArG,EAAA,CAAAgC,aAAA;MAAA,OAAahC,EAAA,CAAAiC,WAAA,CAAAoE,OAAA,CAAAC,cAAA,CAAA9C,YAAA,GAAA5B,MAAA,CAC/C;IAAA,EAD2E;IAKvD5B,EAAA,CAAAG,YAAA,EAAgB;IACjBH,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IARrDH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAmC,UAAA,mBAAkB,uCAAAoE,OAAA,CAAAD,cAAA,CAAA9C,YAAA,aAAA+C,OAAA,CAAAC,YAAA;IAQ3BxG,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAuC,iBAAA,CAAAgE,OAAA,CAAAhG,WAAA,CAAAC,SAAA,8BAAsD;;;;;IAEpFR,EAAA,CAAAC,cAAA,WAA4C;IAAAD,EAAA,CAAAE,MAAA,GAAqE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA5EH,EAAA,CAAAI,SAAA,GAAqE;IAArEJ,EAAA,CAAAyG,kBAAA,KAAAC,OAAA,CAAAnG,WAAA,CAAAC,SAAA,kCAAAkG,OAAA,CAAAhD,YAAA,KAAqE;;;;;;;;;;;ADriBrI,OAAM,MAAOiD,mBAAoB,SAAQhH,aAAa;EAiGlDiH,YAA8CC,iBAAoC,EACtCC,cAA8B,EACtDC,WAAwB,EAChCC,QAAkB;IAC1B,KAAK,CAACA,QAAQ,CAAC;IAJ2B,KAAAH,iBAAiB,GAAjBA,iBAAiB;IACnB,KAAAC,cAAc,GAAdA,cAAc;IACtC,KAAAC,WAAW,GAAXA,WAAW;IAhG/B,KAAAE,iBAAiB,GAAY,IAAI;IACjC,KAAAC,iBAAiB,GAAY,IAAI;IAKjC,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,mBAAmB,GAAY,KAAK;IACpC,KAAAC,mBAAmB,GAAY,KAAK;IACpC,KAAAC,sBAAsB,GAAY,KAAK;IACvC,KAAAC,uBAAuB,GAAY,KAAK,CAAC,CAAC;IAC1C,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,mBAAmB,GAAY,KAAK;IACpC,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAAC,kBAAkB,GAAY,KAAK;IACnC,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAAC,yBAAyB,GAAY,KAAK;IAC1C,KAAAC,0BAA0B,GAAY,KAAK;IAC3C,KAAAC,2BAA2B,GAAY,KAAK;IAC5C,KAAAC,4BAA4B,GAAY,KAAK;IAC7C,KAAAC,6BAA6B,GAAY,KAAK;IAC9C,KAAAC,8BAA8B,GAAY,KAAK;IAC/C,KAAAC,sBAAsB,GAAY,KAAK;IACvC,KAAAC,uBAAuB,GAAY,KAAK;IACxC,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,8BAA8B,GAAY,KAAK;IAQ/C,KAAAC,eAAe,GAAgE,EAAE;IACjF,KAAAC,kBAAkB,GAAgE,CAAC;MAACC,EAAE,EAAE,CAAC,CAAC;MAAEpF,YAAY,EAAE;IAAE,CAAC,CAAC;IAmC9G,KAAAqF,kBAAkB,GAAqB,IAAI9I,gBAAgB,EAAE;IAG7D,KAAA+I,UAAU,GAAG,KAAK;IAClB,KAAAC,UAAU,GAAG,KAAK;IAGlB,KAAAC,UAAU,GAAY,KAAK;IAE3B,KAAAC,cAAc,GAAU,CACpB;MAACzG,IAAI,EAAE,WAAW;MAAEH,GAAG,EAAE;IAAG,CAAC,EAC7B;MAACG,IAAI,EAAE,SAAS;MAAEH,GAAG,EAAE;IAAG,CAAC,CAC9B;IAED,KAAAH,oBAAoB,GAAQ,IAAI;IAEhC,KAAAgH,sBAAsB,GAAW,IAAI,CAAC3I,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;IAE/F,KAAAkD,YAAY,GAAW,EAAE;IACzB,KAAAM,QAAQ,GAAG,IAAI,CAACmF,cAAc,CAACC,QAAQ,CAACC,IAAI;IAC5C,KAAApF,WAAW,GAAGnE,SAAS,CAACwJ,SAAS;IASjC,KAAA/F,WAAW,GAAG;MACVgG,QAAQ,EAAE,IAAI,CAAChJ,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;MACvEgJ,QAAQ,EAAE,IAAI,CAACjJ,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;MACxEiJ,YAAY,EAAE,IAAI,CAAClJ,WAAW,CAACC,SAAS,CAAC,qCAAqC,CAAC;MAC/EkJ,YAAY,EAAE,IAAI,CAACnJ,WAAW,CAACC,SAAS,CAAC,qCAAqC,CAAC;MAC/EmJ,WAAW,EAAE,IAAI,CAACpJ,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC;MAC7EoJ,eAAe,EAAE,IAAI,CAACrJ,WAAW,CAACC,SAAS,CAAC,wCAAwC,CAAC;MACrFqJ,gBAAgB,EAAE,IAAI,CAACtJ,WAAW,CAACC,SAAS,CAAC,yCAAyC,CAAC;MACvFsJ,SAAS,EAAE,IAAI,CAACvJ,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;MACzEgD,YAAY,EAAE,IAAI,CAACjD,WAAW,CAACC,SAAS,CAAC,qCAAqC,CAAC;MAC/EuJ,SAAS,EAAE,IAAI,CAACxJ,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;MACzEwJ,QAAQ,EAAE,IAAI,CAACzJ,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;MACvEyJ,QAAQ,EAAE,IAAI,CAAC1J,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;MACvE0J,aAAa,EAAE,IAAI,CAAC3J,WAAW,CAACC,SAAS,CAAC,sCAAsC,CAAC;MACjF2J,cAAc,EAAE,IAAI,CAAC5J,WAAW,CAACC,SAAS,CAAC,uCAAuC,CAAC;MACnF4J,UAAU,EAAE,IAAI,CAAC7J,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC;MAC3E6J,aAAa,EAAE,IAAI,CAAC9J,WAAW,CAACC,SAAS,CAAC,qCAAqC,CAAC;MAChF8J,eAAe,EAAE,IAAI,CAAC/J,WAAW,CAACC,SAAS,CAAC,wCAAwC,CAAC;MACrF+J,gBAAgB,EAAE,IAAI,CAAChK,WAAW,CAACC,SAAS,CAAC,yCAAyC,CAAC;MACvFgK,MAAM,EAAE,IAAI,CAACjK,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;MACnEiK,OAAO,EAAE,IAAI,CAAClK,WAAW,CAACC,SAAS,CAAC,gCAAgC;KACvE;IA+FH,KAAAkK,cAAc,GAAG,IAAItL,SAAS,CAAC;MAC7BuL,MAAM,EAAG,IAAIxL,WAAW,CAAC,CAAC,CAAC;MAC3ByL,IAAI,EAAE,IAAIzL,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACwL,QAAQ,EAACxL,UAAU,CAACyL,SAAS,CAAC,EAAE,CAAC,EAAE,IAAI,CAACC,4BAA4B,EAAE,CAAC,EAAC,CAAC,IAAI,CAACC,iBAAiB,EAAE,CAAC,CAAC;MACzIxI,IAAI,EAAE,IAAIrD,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACwL,QAAQ,EAAExL,UAAU,CAACyL,SAAS,CAAC,GAAG,CAAC,EAAE,IAAI,CAACG,4BAA4B,EAAE,CAAC,EAAE,CAAC,IAAI,CAACC,iBAAiB,EAAE,CAAC,CAAC;MAC5IzB,YAAY,EAAE,IAAItK,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACwL,QAAQ,EAAExL,UAAU,CAACyL,SAAS,CAAC,EAAE,CAAC,EAAE,IAAI,CAACC,4BAA4B,EAAE,CAAC,CAAC;MACvHrB,YAAY,EAAE,IAAIvK,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACwL,QAAQ,CAAC,CAAC;MACxDjB,eAAe,EAAE,IAAIzK,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAACwL,QAAQ,EAAExL,UAAU,CAAC8L,GAAG,CAAC,UAAU,CAAC,EAAE9L,UAAU,CAAC+L,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC;MAC1GC,QAAQ,EAAE,IAAIlM,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACwL,QAAQ,CAAC,CAAC;MACpDS,WAAW,EAAE,IAAInM,WAAW,CAAC,GAAG,EAAE,CAACE,UAAU,CAACwL,QAAQ,CAAC,CAAC;MACxDrH,YAAY,EAAE,IAAIrE,WAAW,CAAC;QAACoM,KAAK,EAAE,IAAI,CAACvH,QAAQ,IAAIlE,SAAS,CAACwJ,SAAS,CAACpF,KAAK,GAAG,EAAE,GAAE,CAAC,IAAI,CAACiF,cAAc,CAACC,QAAQ,CAAC5F,YAAY,CAAC;QAAEgI,QAAQ,EAAC,CAAC,IAAI,CAAC1C;MAAU,CAAC,EAAE,CAACzJ,UAAU,CAACwL,QAAQ,CAAC,CAAC;MACtLY,OAAO,EAAE,IAAItM,WAAW,EAAE;MAC1BuM,aAAa,EAAE,IAAIvM,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACwL,QAAQ,CAAC,CAAC;MACzDc,aAAa,EAAE,IAAIxM,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAACwL,QAAQ,EAAExL,UAAU,CAAC8L,GAAG,CAAC,UAAU,CAAC,EAAE9L,UAAU,CAAC+L,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACvGQ,MAAM,EAAE,IAAIzM,WAAW,CAAC,CAAC,CAAC;MAC1BwK,WAAW,EAAE,IAAIxK,WAAW,CAAC,EAAE,EAAE,CAACE,UAAU,CAACyL,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAE7De,cAAc,EAAE,IAAI1M,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAACwL,QAAQ,EAACxL,UAAU,CAAC8L,GAAG,CAAC,UAAU,CAAC,EAAE9L,UAAU,CAAC+L,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACvGU,cAAc,EAAE,IAAI3M,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAAC8L,GAAG,CAAC,UAAU,CAAC,EAAE9L,UAAU,CAAC+L,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACnFW,eAAe,EAAE,IAAI5M,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAAC8L,GAAG,CAAC,UAAU,CAAC,EAAE9L,UAAU,CAAC+L,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACpFX,OAAO,EAAE,IAAItL,WAAW,CAAC,CAAC,EAAE,CAACE,UAAU,CAACwL,QAAQ,EAACxL,UAAU,CAAC8L,GAAG,CAAC,UAAU,CAAC,EAAE9L,UAAU,CAAC+L,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAEhGY,QAAQ,EAAE,IAAI7M,WAAW,CAAC,CAAC,CAAC;MAC5B8M,cAAc,EAAE,IAAI9M,WAAW,CAAC;QAACoM,KAAK,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC,IAAI,CAACxC;MAAU,CAAC,EAAE,CAAC3J,UAAU,CAACwL,QAAQ,EAAExL,UAAU,CAAC8L,GAAG,CAAC,UAAU,CAAC,EAAE9L,UAAU,CAAC+L,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7Ic,aAAa,EAAE,IAAI/M,WAAW,CAAC;QAACoM,KAAK,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC,IAAI,CAACxC;MAAU,CAAC,EAAE,CAAC3J,UAAU,CAACwL,QAAQ,EAAExL,UAAU,CAAC8L,GAAG,CAAC,UAAU,CAAC,EAAE9L,UAAU,CAAC+L,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5Ie,SAAS,EAAE,IAAIhN,WAAW,CAAC;QAACoM,KAAK,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC,IAAI,CAACxC;MAAU,CAAC,EAAE,CAAC3J,UAAU,CAAC8L,GAAG,CAAC,UAAU,CAAC,EAAE9L,UAAU,CAAC+L,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACnHf,aAAa,EAAE,IAAIlL,WAAW,CAAC;QAACoM,KAAK,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC,IAAI,CAACxC;MAAU,CAAC,EAAE,CAAE3J,UAAU,CAAC8L,GAAG,CAAC,UAAU,CAAC,EAAE9L,UAAU,CAAC+L,GAAG,CAAC,CAAC,CAAC,CAAE,CAAC;MACzHgB,YAAY,EAAE,IAAIjN,WAAW,CAAC;QAACoM,KAAK,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC,IAAI,CAACxC;MAAU,CAAC,EAAE,CAAC3J,UAAU,CAAC8L,GAAG,CAAC,UAAU,CAAC,EAAE9L,UAAU,CAAC+L,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACtHiB,aAAa,EAAE,IAAIlN,WAAW,CAAC;QAACoM,KAAK,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC,IAAI,CAACxC;MAAU,CAAC,EAAE,CAAC3J,UAAU,CAAC8L,GAAG,CAAC,UAAU,CAAC,EAAE9L,UAAU,CAAC+L,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACvHkB,UAAU,EAAE,IAAInN,WAAW,CAAC;QAACoM,KAAK,EAAE,CAAC;QAAEC,QAAQ,EAAE,CAAC,IAAI,CAACxC;MAAU,CAAC,EAAE,CAAC3J,UAAU,CAAC8L,GAAG,CAAC,UAAU,CAAC,EAAE9L,UAAU,CAAC+L,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAClHmB,WAAW,EAAG,IAAIpN,WAAW,CAAC;QAACoM,KAAK,EAAC,CAAC;QAAEC,QAAQ,EAAE,CAAC,IAAI,CAACxC;MAAU,CAAC;KACtE,CAAC;EApJA;EAyBA+B,4BAA4BA,CAAA;IACxB,OAAQyB,OAAwB,IAA6B;MACzD,MAAMjB,KAAK,GAAGiB,OAAO,CAACjB,KAAK;MAC3B,MAAMkB,OAAO,GAAG,mBAAmB,CAACC,IAAI,CAACnB,KAAK,CAAC;MAC/C,OAAOkB,OAAO,GAAG,IAAI,GAAG;QAAC,mBAAmB,EAAE;UAAClB;QAAK;MAAC,CAAC;IAC1D,CAAC;EACL;EAEAN,4BAA4BA,CAAA;IACxB,OAAQuB,OAAwB,IAA6B;MACzD,MAAMjB,KAAK,GAAGiB,OAAO,CAACjB,KAAK;MAC3B,IAAIA,KAAK,IAAI,EAAE,EAAE;QACb,OAAO,IAAI;;MAEf,MAAMkB,OAAO,GAAG,gFAAgF,CAACC,IAAI,CAACnB,KAAK,CAAC;MAC5G,OAAOkB,OAAO,GAAG,IAAI,GAAG;QAAC,mBAAmB,EAAE;UAAClB;QAAK;MAAC,CAAC;IAC1D,CAAC;EACL;EAEAoB,gBAAgBA,CAACC,KAAS;IACtB,OAAO,IAAItN,UAAU,CAACuN,QAAQ,IAAG;MAC7B,IAAI,CAAChG,iBAAiB,CAACiG,uBAAuB,CAACF,KAAK,EAAGG,QAAQ,IAAI;QAC/DF,QAAQ,CAACG,IAAI,CAACD,QAAQ,CAAC;QACvBF,QAAQ,CAACI,QAAQ,EAAE;MACvB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEAC,gBAAgBA,CAACN,KAAS;IACtB,OAAO,IAAItN,UAAU,CAACuN,QAAQ,IAAG;MAC7B,IAAI,CAAChG,iBAAiB,CAACsG,uBAAuB,CAACP,KAAK,EAAGG,QAAQ,IAAI;QAC/DF,QAAQ,CAACG,IAAI,CAACD,QAAQ,CAAC;QACvBF,QAAQ,CAACI,QAAQ,EAAE;MACvB,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEAjC,iBAAiBA,CAAA;IACb,OAAQwB,OAAwB,IAAyC;MACrE,OAAOA,OAAO,CAACY,YAAY,CAACC,IAAI,CAC5B9N,YAAY,CAAC,GAAG,CAAC,EACjBE,SAAS,CAAC8L,KAAK,IAAI,IAAI,CAACoB,gBAAgB,CAAC;QAAC/B,IAAI,EAAEW;MAAK,CAAC,CAAC,CAAC,EACxD7L,IAAI,CAAC,CAAC,CAAC,EACPF,GAAG,CAAC8N,MAAM,IAAG;QACT;QACA,IAAIA,MAAM,KAAK,CAAC,EAAE;UACd,IAAI,CAACrG,iBAAiB,GAAG,KAAK;UAC9B,OAAO,IAAI;SACd,MAAM;UACH,IAAI,CAACA,iBAAiB,GAAG,IAAI;UAC7B,OAAO;YAAC,QAAQ,EAAE;UAAI,CAAC;;MAE/B,CAAC,CAAC,CACL;IACL,CAAC;EACL;EAEAiE,iBAAiBA,CAAA;IACb,OAAQsB,OAAwB,IAAyC;MACrE,OAAOA,OAAO,CAACY,YAAY,CAACC,IAAI,CAC5B9N,YAAY,CAAC,GAAG,CAAC,EACjBE,SAAS,CAAC8L,KAAK,IAAI,IAAI,CAAC2B,gBAAgB,CAAC;QAAC1K,IAAI,EAAE+I;MAAK,CAAC,CAAC,CAAC,EACxD7L,IAAI,CAAC,CAAC,CAAC,EACPF,GAAG,CAAC8N,MAAM,IAAG;QACT;QACA,IAAIA,MAAM,KAAK,CAAC,EAAE;UACd,IAAI,CAACpG,iBAAiB,GAAG,KAAK;UAC9B,OAAO,IAAI;SACd,MAAM;UACH,IAAI,CAACA,iBAAiB,GAAG,IAAI;UAC7B,OAAO;YAAC,QAAQ,EAAE;UAAI,CAAC;;MAE/B,CAAC,CAAC,CACL;IACL,CAAC;EACL;EAEAqG,UAAUA,CAACC,KAAoB;IAC3B,MAAMC,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAC/D,IAAIA,YAAY,CAACC,QAAQ,CAACF,KAAK,CAACnL,GAAG,CAAC,EAAE;MAClCmL,KAAK,CAACG,cAAc,EAAE;;IAE1B;IACA;IACA;EACJ;;EAEAC,eAAeA,CAACJ,KAAiB;IAC7B,MAAMK,KAAK,GAAGL,KAAK,CAACM,MAA0B;IAC9CD,KAAK,CAACtC,KAAK,GAAGsC,KAAK,CAACtC,KAAK,CAACwC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC;EACtD;;EAmCFC,gBAAgBA,CAACR,KAAK;IACpB,IAAGA,KAAK,CAACjC,KAAK,IAAE,CAAC,EAAC;MAChB,IAAI,CAACzC,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB,IAAI,CAAC2B,cAAc,CAACuD,GAAG,CAAC,cAAc,CAAC,CAACC,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;MACrE;KACD,MAAK,IAAIX,KAAK,CAACjC,KAAK,IAAI,CAAC,EAAC;MACvB;MACA,IAAI,CAACzC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB,IAAI,CAAC2B,cAAc,CAACuD,GAAG,CAAC,cAAc,CAAC,CAACG,MAAM,CAAC;QAAED,SAAS,EAAE;MAAK,CAAE,CAAC;MACpE;MACA,IAAI,CAAC/K,cAAc,EAAE;KACxB,MAAK,IAAIoK,KAAK,CAACjC,KAAK,IAAI,CAAC,EAAC;MACvB;MACF,IAAI,CAACzC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB,IAAI,CAAC2B,cAAc,CAACuD,GAAG,CAAC,cAAc,CAAC,CAACG,MAAM,CAAC;QAAED,SAAS,EAAE;MAAK,CAAE,CAAC;MACpE;MACE,IAAI,CAAC/K,cAAc,EAAE;;EAE3B;EAEAiL,cAAcA,CAAA;IACV;IACF,IAAI,IAAI,CAACrF,UAAU,EAAE;MACnB,IAAI,CAAC0B,cAAc,CAACuD,GAAG,CAAC,gBAAgB,CAAC,CAACG,MAAM,CAAC;QAAED,SAAS,EAAE;MAAK,CAAE,CAAC;MACtE,IAAI,CAACzD,cAAc,CAACuD,GAAG,CAAC,eAAe,CAAC,CAACG,MAAM,CAAC;QAAED,SAAS,EAAE;MAAK,CAAE,CAAC;MACrE,IAAI,CAACzD,cAAc,CAACuD,GAAG,CAAC,WAAW,CAAC,CAACG,MAAM,CAAC;QAAED,SAAS,EAAE;MAAK,CAAE,CAAC;MACjE,IAAI,CAACzD,cAAc,CAACuD,GAAG,CAAC,eAAe,CAAC,CAACG,MAAM,CAAC;QAAED,SAAS,EAAE;MAAK,CAAE,CAAC;MACrE,IAAI,CAACzD,cAAc,CAACuD,GAAG,CAAC,cAAc,CAAC,CAACG,MAAM,CAAC;QAAED,SAAS,EAAE;MAAK,CAAE,CAAC;MACpE,IAAI,CAACzD,cAAc,CAACuD,GAAG,CAAC,eAAe,CAAC,CAACG,MAAM,CAAC;QAAED,SAAS,EAAE;MAAK,CAAE,CAAC;MACrE,IAAI,CAACzD,cAAc,CAACuD,GAAG,CAAC,YAAY,CAAC,CAACG,MAAM,CAAC;QAAED,SAAS,EAAE;MAAK,CAAE,CAAC;KACnE,MAAM;MACL,IAAI,CAACzD,cAAc,CAACuD,GAAG,CAAC,gBAAgB,CAAC,CAACC,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;MACvE,IAAI,CAACzD,cAAc,CAACuD,GAAG,CAAC,eAAe,CAAC,CAACC,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;MACtE,IAAI,CAACzD,cAAc,CAACuD,GAAG,CAAC,WAAW,CAAC,CAACC,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;MAClE,IAAI,CAACzD,cAAc,CAACuD,GAAG,CAAC,eAAe,CAAC,CAACC,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;MACtE,IAAI,CAACzD,cAAc,CAACuD,GAAG,CAAC,cAAc,CAAC,CAACC,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;MACrE,IAAI,CAACzD,cAAc,CAACuD,GAAG,CAAC,eAAe,CAAC,CAACC,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;MACtE,IAAI,CAACzD,cAAc,CAACuD,GAAG,CAAC,YAAY,CAAC,CAACC,OAAO,CAAC;QAAEC,SAAS,EAAE;MAAK,CAAE,CAAC;;EAEvE;EAEAG,UAAUA,CAAA;IACR,IAAI,CAACC,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAIC,EAAE,GAAG,IAAI;IACb,IAAI,IAAI,CAAC/D,cAAc,CAACgE,KAAK,EAAE;MAC7B,IAAIC,IAAI,GAAG;QAAC,GAAG,IAAI,CAACjE,cAAc,CAACa;MAAK,CAAC;MACzC,IAAGoD,IAAI,CAAC/C,MAAM,EAAC;QACb+C,IAAI,CAAC/C,MAAM,GAAG,CAAC;OAChB,MAAI;QACH+C,IAAI,CAAC/C,MAAM,GAAG,CAAC;;MAEjB,IAAG+C,IAAI,CAAC3C,QAAQ,EAAC;QACf2C,IAAI,CAAC3C,QAAQ,GAAG,CAAC;QACf2C,IAAI,CAACpC,WAAW,GAAGoC,IAAI,CAACxC,SAAS,GAAGwC,IAAI,CAACtE,aAAa;OACzD,MAAI;QACHsE,IAAI,CAAC3C,QAAQ,GAAG,CAAC;;MAEnB,IAAI,IAAI,CAACtD,eAAe,CAACkG,MAAM,GAAG,CAAC,EAAC;QAChC,IAAIC,gBAAgB,GAAGJ,EAAE,CAAC/D,cAAc,CAACuD,GAAG,CAAC,cAAc,CAAC,CAAC1C,KAAK;QAClE,IAAIuD,eAAe,GAAGL,EAAE,CAAC/F,eAAe,CAACqG,MAAM,CAAEC,EAAE,IAAKH,gBAAgB,CAACnB,QAAQ,CAACsB,EAAE,CAACxL,YAAY,CAAC,CAAC,CAAChE,GAAG,CAACyP,CAAC,IAAIA,CAAC,CAACrG,EAAE,CAAC;QAClH+F,IAAI,CAAClD,OAAO,GAAGqD,eAAe;;MAElC,IAAI,CAACjI,iBAAiB,CAACqI,gBAAgB,CAACP,IAAI,EAAE5B,QAAQ,IAAG;QACvD;QAEA,IAAI,CAACwB,oBAAoB,CAACY,OAAO,CAAC,IAAI,CAAC5O,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QAC3F,IAAI,CAAC4O,MAAM,CAACC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;MAClC,CAAC,EAAE,IAAI,EAAE,MAAI;QACTZ,EAAE,CAACF,oBAAoB,CAACe,OAAO,EAAE;MACrC,CAAC,CAAC;;EAEN;EAEEC,kBAAkBA,CAAA;IACd,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,UAAU,CAAC,IAAI,CAACD,gBAAgB,EAAE,IAAI,CAACE,cAAc,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACrJ,cAAc,CAAC;EACrG;EAEFsJ,QAAQA,CAAA;IACJ,IAAI,CAACC,6BAA6B,GAAG;MACjCC,gBAAgB,EAAE,KAAK;MACvBC,YAAY,EAAE,IAAI;MAClBC,aAAa,EAAE,IAAI;MACnBC,mBAAmB,EAAE;KACxB;IACD,IAAI,CAACC,iBAAiB,GAAG;MACrBC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD,IAAI,CAAC9J,cAAc,GAAG;MAClB+J,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,IAAI;MACX/M,YAAY,EAAE;KACjB;IACD,IAAI,CAACgN,cAAc,GAAG,IAAI,CAACzJ,WAAW,CAAC0J,KAAK,CAAC,IAAI,CAACnK,cAAc,CAAC;IACnE,IAAI,CAACoK,KAAK,GAAG,CACX;MAAEC,KAAK,EAAE,IAAI,CAACpQ,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;MAAEoQ,UAAU,EAAE;IAAK,CAAC,EACrF;MAAED,KAAK,EAAE,IAAI,CAACpQ,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MAAEoQ,UAAU,EAAE;IAAK,CAAC,EAC/E;MAAED,KAAK,EAAE,IAAI,CAACpQ,WAAW,CAACC,SAAS,CAAC,sBAAsB;IAAC,CAAE,CAC9D;IACC,IAAI,CAACqQ,eAAe,GAAG,CACnB;MACIrO,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7D6B,GAAG,EAAE,UAAU;MACfyO,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIzO,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7D6B,GAAG,EAAE,UAAU;MACfyO,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIzO,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1D6B,GAAG,EAAE,OAAO;MACZyO,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIzO,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7D6B,GAAG,EAAE,cAAc;MACnByO,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IACC,IAAI,CAACC,aAAa,GAAG,CACjB;MACItI,EAAE,EAAE,GAAG;MACPpG,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,kCAAkC;KACtE,EACD;MACIoI,EAAE,EAAE,GAAG;MACPpG,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,oCAAoC;KACxE,EACD;MACIoI,EAAE,EAAE,GAAG;MACPpG,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,gCAAgC;;IAErE;IACA;IACA;IACA;IACA;IAAA,CACH;;IACD,IAAI,CAAC2Q,YAAY,GAAG,CAChB;MACIvI,EAAE,EAAE,GAAG;MACPpG,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,mCAAmC;KACvE,EACD;MACIoI,EAAE,EAAE,GAAG;MACPpG,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,iCAAiC;KACrE,EACD;MACIoI,EAAE,EAAE,GAAG;MACPpG,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,iCAAiC;KACrE,CACJ;IAED,IAAI,CAAC4Q,MAAM,GAAG,CACV;MACIxI,EAAE,EAAE,GAAG;MACPpG,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,sBAAsB;KAC1D,EACD;MACIoI,EAAE,EAAE,GAAG;MACPpG,IAAI,EAAE,IAAI,CAACjC,WAAW,CAACC,SAAS,CAAC,wBAAwB;KAC5D,CACJ;IACD,IAAI,CAACsG,cAAc,CAACuK,eAAe,CAAE1C,IAAI,IAAI;MACzC,IAAIF,EAAE,GAAG,IAAI;MACb,IAAI,CAACnL,SAAS,GAAGqL,IAAI,CAACnP,GAAG,CAACwP,EAAE,IAAG;QAC3B,IAAIA,EAAE,CAACpE,IAAI,IAAI6D,EAAE,CAACtF,cAAc,CAACC,QAAQ,CAAC5F,YAAY,EAAE;UACpDiL,EAAE,CAAC/K,YAAY,GAAG,GAAGsL,EAAE,CAACxM,IAAI,MAAMwM,EAAE,CAACpE,IAAI,EAAE;;QAE/C,OAAO;UACHA,IAAI,EAAEoE,EAAE,CAACpE,IAAI;UACbpI,IAAI,EAAE,GAAGwM,EAAE,CAACxM,IAAI,MAAMwM,EAAE,CAACpE,IAAI;SAChC;MACL,CAAC,CAAC;IACN,CAAC,CAAC;IAEF,IAAI,CAAC1I,oBAAoB,GAAG,IAAI,CAAC+G,cAAc,CAAC,CAAC,CAAC,CAAC5G,GAAG;IAEtD,IAAI,CAACiP,WAAW,GAAG,IAAI,CAAC5G,cAAc,CAACuD,GAAG,CAAC,MAAM,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MAC5E,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,MAAM,CAAC,CAACwD,MAAM;MACrD,IAAIA,MAAM,EAAE;QACR,IAAI,CAACtK,eAAe,GAAG,IAAI;OAC9B,MAAM;QACH,IAAI,CAACA,eAAe,GAAG,KAAK;;IAEpC,CAAC,CAAC;IAEF,IAAI,CAACuK,YAAY,GAAG,IAAI,CAAChH,cAAc,CAACuD,GAAG,CAAC,MAAM,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MAC7E,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,MAAM,CAAC,CAACwD,MAAM;MACrD,IAAIA,MAAM,EAAE;QACR,IAAI,CAACrK,gBAAgB,GAAG,IAAI;OAC/B,MAAM;QACH,IAAI,CAACA,gBAAgB,GAAG,KAAK;;IAErC,CAAC,CAAC;IAEF,IAAI,CAACuK,eAAe,GAAG,IAAI,CAACjH,cAAc,CAACuD,GAAG,CAAC,cAAc,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MACxF,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,cAAc,CAAC,CAACwD,MAAM;MAC7D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACpK,mBAAmB,GAAG,IAAI;OAClC,MAAM;QACH,IAAI,CAACA,mBAAmB,GAAG,KAAK;;IAExC,CAAC,CAAC;IAEF,IAAI,CAACuK,eAAe,GAAG,IAAI,CAAClH,cAAc,CAACuD,GAAG,CAAC,cAAc,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MACxF,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,cAAc,CAAC,CAACwD,MAAM;MAC7D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACnK,mBAAmB,GAAG,IAAI;OAClC,MAAM;QACH,IAAI,CAACA,mBAAmB,GAAG,KAAK;;IAExC,CAAC,CAAC;IAEF,IAAI,CAACuK,kBAAkB,GAAG,IAAI,CAACnH,cAAc,CAACuD,GAAG,CAAC,iBAAiB,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MAC9F,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,iBAAiB,CAAC,CAACwD,MAAM;MAChE,IAAIA,MAAM,EAAE;QACR,IAAI,CAAClK,sBAAsB,GAAG,IAAI;OACrC,MAAM;QACH,IAAI,CAACA,sBAAsB,GAAG,KAAK;;IAE3C,CAAC,CAAC;IAEF,IAAI,CAACuK,mBAAmB,GAAG,IAAI,CAACpH,cAAc,CAACuD,GAAG,CAAC,UAAU,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MACxF,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,UAAU,CAAC,CAACwD,MAAM;MACzD,IAAIA,MAAM,EAAE;QACR,IAAI,CAACjK,uBAAuB,GAAG,IAAI;OACtC,MAAM;QACH,IAAI,CAACA,uBAAuB,GAAG,KAAK;;IAE5C,CAAC,CAAC;IAEF,IAAI,CAACuK,YAAY,GAAG,IAAI,CAACrH,cAAc,CAACuD,GAAG,CAAC,aAAa,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MACpF,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,aAAa,CAAC,CAACwD,MAAM;MAC5D,IAAIA,MAAM,EAAE;QACR,IAAI,CAAChK,gBAAgB,GAAG,IAAI;OAC/B,MAAM;QACH,IAAI,CAACA,gBAAgB,GAAG,KAAK;;IAErC,CAAC,CAAC;IAEF,IAAI,CAACuK,eAAe,GAAG,IAAI,CAACtH,cAAc,CAACuD,GAAG,CAAC,cAAc,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MACxF,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,cAAc,CAAC,CAACwD,MAAM;MAC7D,IAAIA,MAAM,EAAE;QACR,IAAI,CAAC/J,mBAAmB,GAAG,IAAI;OAClC,MAAM;QACH,IAAI,CAACA,mBAAmB,GAAG,KAAK;;IAExC,CAAC,CAAC;IAEF,IAAI,CAACuK,YAAY,GAAG,IAAI,CAACvH,cAAc,CAACuD,GAAG,CAAC,eAAe,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MACtF,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,eAAe,CAAC,CAACwD,MAAM;MAC9D,IAAIA,MAAM,EAAE;QACR,IAAI,CAAC9J,gBAAgB,GAAG,IAAI;OAC/B,MAAM;QACH,IAAI,CAACA,gBAAgB,GAAG,KAAK;;IAErC,CAAC,CAAC;IAEF,IAAI,CAACuK,WAAW,GAAG,IAAI,CAACxH,cAAc,CAACuD,GAAG,CAAC,eAAe,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MACrF,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,eAAe,CAAC,CAACwD,MAAM;MAC9D,IAAIA,MAAM,EAAE;QACR,IAAI,CAAC7J,eAAe,GAAG,IAAI;OAC9B,MAAM;QACH,IAAI,CAACA,eAAe,GAAG,KAAK;;IAEpC,CAAC,CAAC;IAEF,IAAI,CAACuK,cAAc,GAAG,IAAI,CAACzH,cAAc,CAACuD,GAAG,CAAC,aAAa,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MACtF,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,aAAa,CAAC,CAACwD,MAAM;MAC5D,IAAIA,MAAM,EAAE;QACR,IAAI,CAAC5J,kBAAkB,GAAG,IAAI;OACjC,MAAM;QACH,IAAI,CAACA,kBAAkB,GAAG,KAAK;;IAEvC,CAAC,CAAC;IAEF,IAAI,CAACuK,WAAW,GAAG,IAAI,CAAC1H,cAAc,CAACuD,GAAG,CAAC,gBAAgB,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MACtF,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,gBAAgB,CAAC,CAACwD,MAAM;MAC/D,IAAIA,MAAM,EAAE;QACR,IAAI,CAAC3J,eAAe,GAAG,IAAI;OAC9B,MAAM;QACH,IAAI,CAACA,eAAe,GAAG,KAAK;;IAEpC,CAAC,CAAC;IAEF,IAAI,CAACuK,qBAAqB,GAAG,IAAI,CAAC3H,cAAc,CAACuD,GAAG,CAAC,gBAAgB,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MAChG,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,gBAAgB,CAAC,CAACwD,MAAM;MAC/D,IAAIA,MAAM,EAAE;QACR,IAAI,CAAC1J,yBAAyB,GAAG,IAAI;OACxC,MAAM;QACH,IAAI,CAACA,yBAAyB,GAAG,KAAK;;IAE9C,CAAC,CAAC;IAEF,IAAI,CAACuK,sBAAsB,GAAG,IAAI,CAAC5H,cAAc,CAACuD,GAAG,CAAC,iBAAiB,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MAClG,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,iBAAiB,CAAC,CAACwD,MAAM;MAChE,IAAIA,MAAM,EAAE;QACR,IAAI,CAACzJ,0BAA0B,GAAG,IAAI;OACzC,MAAM;QACH,IAAI,CAACA,0BAA0B,GAAG,KAAK;;IAE/C,CAAC,CAAC;IAEF,IAAI,CAACuK,UAAU,GAAG,IAAI,CAAC7H,cAAc,CAACuD,GAAG,CAAC,SAAS,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MAC9E,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,SAAS,CAAC,CAACwD,MAAM;MACxD,IAAIA,MAAM,EAAE;QACR,IAAI,CAACjJ,cAAc,GAAG,IAAI;OAC7B,MAAM;QACH,IAAI,CAACA,cAAc,GAAG,KAAK;;IAEnC,CAAC,CAAC;IAEF,IAAI,CAACgK,uBAAuB,GAAG,IAAI,CAAC9H,cAAc,CAACuD,GAAG,CAAC,gBAAgB,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MAClG,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,gBAAgB,CAAC,CAACwD,MAAM;MAC/D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACxJ,2BAA2B,GAAG,IAAI;OAC1C,MAAM;QACH,IAAI,CAACA,2BAA2B,GAAG,KAAK;;IAEhD,CAAC,CAAC;IAEF,IAAI,CAACwK,wBAAwB,GAAG,IAAI,CAAC/H,cAAc,CAACuD,GAAG,CAAC,eAAe,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MAClG,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,eAAe,CAAC,CAACwD,MAAM;MAC9D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACvJ,4BAA4B,GAAG,IAAI;OAC3C,MAAM;QACH,IAAI,CAACA,4BAA4B,GAAG,KAAK;;IAEjD,CAAC,CAAC;IAEF,IAAI,CAACwK,yBAAyB,GAAG,IAAI,CAAChI,cAAc,CAACuD,GAAG,CAAC,WAAW,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MAC/F,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,WAAW,CAAC,CAACwD,MAAM;MAC1D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACtJ,6BAA6B,GAAG,IAAI;OAC5C,MAAM;QACH,IAAI,CAACA,6BAA6B,GAAG,KAAK;;IAElD,CAAC,CAAC;IAEF,IAAI,CAACwK,0BAA0B,GAAG,IAAI,CAACjI,cAAc,CAACuD,GAAG,CAAC,eAAe,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MACpG,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,eAAe,CAAC,CAACwD,MAAM;MAC9D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACrJ,8BAA8B,GAAG,IAAI;OAC7C,MAAM;QACH,IAAI,CAACA,8BAA8B,GAAG,KAAK;;IAEnD,CAAC,CAAC;IAEF,IAAI,CAACwK,kBAAkB,GAAG,IAAI,CAAClI,cAAc,CAACuD,GAAG,CAAC,cAAc,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MAC3F,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,cAAc,CAAC,CAACwD,MAAM;MAC7D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACpJ,sBAAsB,GAAG,IAAI;OACrC,MAAM;QACH,IAAI,CAACA,sBAAsB,GAAG,KAAK;;IAE3C,CAAC,CAAC;IAEF,IAAI,CAACwK,mBAAmB,GAAG,IAAI,CAACnI,cAAc,CAACuD,GAAG,CAAC,eAAe,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MAC7F,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,eAAe,CAAC,CAACwD,MAAM;MAC9D,IAAIA,MAAM,EAAE;QACR,IAAI,CAACnJ,uBAAuB,GAAG,IAAI;OACtC,MAAM;QACH,IAAI,CAACA,uBAAuB,GAAG,KAAK;;IAE5C,CAAC,CAAC;IAEF,IAAI,CAACwK,SAAS,GAAG,IAAI,CAACpI,cAAc,CAACuD,GAAG,CAAC,YAAY,CAAC,CAACsD,aAAa,CAACC,SAAS,CAAC,MAAK;MAChF,MAAMC,MAAM,GAAG,IAAI,CAAC/G,cAAc,CAACuD,GAAG,CAAC,YAAY,CAAC,CAACwD,MAAM;MAC3D,IAAIA,MAAM,EAAE;QACR,IAAI,CAAClJ,aAAa,GAAG,IAAI;OAC5B,MAAM;QACH,IAAI,CAACA,aAAa,GAAG,KAAK;;IAElC,CAAC,CAAC;IAEF,IAAI,CAACwK,IAAI,GAAG;MAACC,IAAI,EAAE,YAAY;MAAEpC,UAAU,EAAE;IAAG,CAAC;EACrD;EAEAqC,WAAWA,CAAA;IACP,IAAI,CAAC3B,WAAW,CAAC4B,WAAW,EAAE;IAC9B,IAAI,CAACxB,YAAY,CAACwB,WAAW,EAAE;IAC/B,IAAI,CAACvB,eAAe,CAACuB,WAAW,EAAE;IAClC,IAAI,CAACtB,eAAe,CAACsB,WAAW,EAAE;IAClC,IAAI,CAACrB,kBAAkB,CAACqB,WAAW,EAAE;IACrC,IAAI,CAACpB,mBAAmB,CAACoB,WAAW,EAAE;IACtC,IAAI,CAACnB,YAAY,CAACmB,WAAW,EAAE;IAC/B,IAAI,CAAClB,eAAe,CAACkB,WAAW,EAAE;IAClC,IAAI,CAACjB,YAAY,CAACiB,WAAW,EAAE;IAC/B,IAAI,CAAChB,WAAW,CAACgB,WAAW,EAAE;IAC9B,IAAI,CAACf,cAAc,CAACe,WAAW,EAAE;IACjC,IAAI,CAACd,WAAW,CAACc,WAAW,EAAE;IAC9B,IAAI,CAACb,qBAAqB,CAACa,WAAW,EAAE;IACxC,IAAI,CAACZ,sBAAsB,CAACY,WAAW,EAAE;IACzC,IAAI,CAACV,uBAAuB,CAACU,WAAW,EAAE;IAC1C,IAAI,CAACT,wBAAwB,CAACS,WAAW,EAAE;IAC3C,IAAI,CAACR,yBAAyB,CAACQ,WAAW,EAAE;IAC5C,IAAI,CAACP,0BAA0B,CAACO,WAAW,EAAE;IAC7C,IAAI,CAACN,kBAAkB,CAACM,WAAW,EAAE;IACrC,IAAI,CAACL,mBAAmB,CAACK,WAAW,EAAE;IACtC,IAAI,CAACJ,SAAS,CAACI,WAAW,EAAE;EAChC;EAEA3O,4BAA4BA,CAAA;IACxB,IAAI4O,iBAAiB,GAAG,IAAI,CAACzI,cAAc,CAACuD,GAAG,CAAC,cAAc,CAAC,CAAC1C,KAAK;IACrE,IAAI,CAAC/E,YAAY,GAAG,IAAI,CAAClD,SAAS,CAACyL,MAAM,CAAEqE,IAAI,IAC3CD,iBAAiB,CAACzF,QAAQ,CAAC0F,IAAI,CAACxI,IAAI,CAAC,CACxC;IACD;IACA,IAAI,IAAI,CAAC4E,gBAAgB,IAAI,IAAI,EAAC;MAC9B,IAAI,CAACA,gBAAgB,GAAG,CAAC;;IAE7B,IAAI,IAAI,CAACE,cAAc,IAAI,IAAI,EAAC;MAC5B,IAAI,CAACA,cAAc,GAAG,EAAE;;IAE5B,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,EAAC;MACxB,IAAI,CAACA,UAAU,GAAG,SAAS;;IAE/B,IAAI,CAACrJ,cAAc,CAAC9C,YAAY,GAAG,IAAI,CAACgD,YAAY,CAAChH,GAAG,CAACyP,CAAC,IAAIA,CAAC,CAACrE,IAAI,CAAC;IACrE,IAAI,CAAC6E,UAAU,CAAC,IAAI,CAACD,gBAAgB,EAAE,IAAI,CAACE,cAAc,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACrJ,cAAc,CAAC;IACjG,IAAI,CAACmC,8BAA8B,GAAG,IAAI;EAC9C;EAGAgH,UAAUA,CAAC4D,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM;IAClC,IAAK/E,EAAE,GAAG,IAAI;IACZ,IAAI,CAACe,gBAAgB,GAAG6D,IAAI;IAC5B,IAAI,CAAC3D,cAAc,GAAG4D,KAAK;IAC3B,IAAIC,IAAI,IAAI,IAAI,IAAIA,IAAI,IAAIE,SAAS,EAAC;MAClC,IAAI,CAAC9D,UAAU,GAAG,SAAS;MAC3B4D,IAAI,GAAG,SAAS;KACnB,MAAK;MACF,IAAI,CAAC5D,UAAU,GAAG4D,IAAI;;IAE1B,IAAIG,UAAU,GAAG;MACbL,IAAI;MACJvC,IAAI,EAAEwC,KAAK;MACXC,IAAI;MACJ/P,YAAY,EAAE,IAAI,CAACgD,YAAY,CAAChH,GAAG,CAACyP,CAAC,IAAIA,CAAC,CAACrE,IAAI;KAClD;IACD+I,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtN,cAAc,CAAC,CAACuN,OAAO,CAACxR,GAAG,IAAG;MAC3C,IAAG,IAAI,CAACiE,cAAc,CAACjE,GAAG,CAAC,IAAI,IAAI,EAAC;QAChCqR,UAAU,CAACrR,GAAG,CAAC,GAAG,IAAI,CAACiE,cAAc,CAACjE,GAAG,CAAC;;IAElD,CAAC,CAAC;IACF,IAAI,IAAI,CAACiE,cAAc,CAAC9C,YAAY,IAAI,IAAI,EAAC;MACzCkQ,UAAU,CAAClQ,YAAY,GAAG,IAAI,CAACgD,YAAY,CAAChH,GAAG,CAACyP,CAAC,IAAIA,CAAC,CAACrE,IAAI,CAAC;;IAEhE,IAAI,CAACjC,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACD,eAAe,CAAC;IACnD,IAAI,CAAC7B,iBAAiB,CAACiN,mBAAmB,CAACJ,UAAU,EAAG3G,QAAQ,IAAI;MAChE0B,EAAE,CAACyB,iBAAiB,GAAG;QACfC,OAAO,EAAEpD,QAAQ,CAACoD,OAAO;QACzBC,KAAK,EAAErD,QAAQ,CAACgH;OACnB;MACL,IAAI,CAACrL,eAAe,GAAG,CAAC,GAAG,IAAI,CAACC,kBAAkB,CAAC;IACnD,CAAC,CAAC;EAEV;EAGAvF,cAAcA,CAAA;IACV,IAAIyL,gBAAgB,GAAG,IAAI,CAACnE,cAAc,CAACuD,GAAG,CAAC,cAAc,CAAC,CAAC1C,KAAK;IACpE,IAAID,WAAW,GAAG,IAAI,CAACZ,cAAc,CAACuD,GAAG,CAAC,aAAa,CAAC,CAAC1C,KAAK;IAC9D,IAAID,WAAW,IAAI,GAAG,EAAC;MACnB,IAAIuD,gBAAgB,CAACD,MAAM,GAAG,CAAC,EAAC;QAC5B,IAAI,CAAC7F,UAAU,GAAG,IAAI;OACzB,MAAK;QACF,IAAI,CAACA,UAAU,GAAG,KAAK;;;EAGnC;;;uBAruBSpC,mBAAmB,EAAA3G,EAAA,CAAAgU,iBAAA,CAiGRpU,iBAAiB,GAAAI,EAAA,CAAAgU,iBAAA,CACjBnU,cAAc,GAAAG,EAAA,CAAAgU,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlU,EAAA,CAAAgU,iBAAA,CAAAhU,EAAA,CAAAmU,QAAA;IAAA;EAAA;;;YAlGzBxN,mBAAmB;MAAAyN,SAAA;MAAAC,QAAA,GAAArU,EAAA,CAAAsU,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC7BhC5U,EAAA,CAAAC,cAAA,aAAqG;UAEzDD,EAAA,CAAAE,MAAA,GAAiD;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC3FH,EAAA,CAAA8D,SAAA,sBAAoF;UACxF9D,EAAA,CAAAG,YAAA,EAAM;UAEVH,EAAA,CAAAC,cAAA,gBAAuD;UACND,EAAA,CAAA0B,UAAA,oBAAAoT,oDAAA;YAAA,OAAUD,GAAA,CAAAvG,UAAA,EAAY;UAAA,EAAC;UAChEtO,EAAA,CAAAC,cAAA,aAA4E;UAGsBD,EAAA,CAAAE,MAAA,IAAsD;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/KH,EAAA,CAAAC,cAAA,eAAqC;UACjCD,EAAA,CAAA8D,SAAA,iBAAqG;UACzG9D,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,eAA4E;UACxED,EAAA,CAAA8D,SAAA,eAAqC;UACrC9D,EAAA,CAAAC,cAAA,eAAmC;UAC/BD,EAAA,CAAA2D,UAAA,KAAAoR,mCAAA,kBAEM;UACN/U,EAAA,CAAA2D,UAAA,KAAAqR,mCAAA,kBAEM;UACNhV,EAAA,CAAA2D,UAAA,KAAAsR,mCAAA,kBAEM;UACNjV,EAAA,CAAA2D,UAAA,KAAAuR,mCAAA,kBAEM;UACVlV,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,eAA6E;UACaD,EAAA,CAAAE,MAAA,IAAsD;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/KH,EAAA,CAAAC,cAAA,eAAqC;UACjCD,EAAA,CAAA8D,SAAA,iBAAoG;UACxG9D,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,eAA4E;UACxED,EAAA,CAAA8D,SAAA,eAAqC;UACrC9D,EAAA,CAAAC,cAAA,eAAmC;UAC/BD,EAAA,CAAA2D,UAAA,KAAAwR,mCAAA,kBAEM;UACNnV,EAAA,CAAA2D,UAAA,KAAAyR,mCAAA,kBAEM;UACNpV,EAAA,CAAA2D,UAAA,KAAA0R,mCAAA,kBAEM;UACNrV,EAAA,CAAA2D,UAAA,KAAA2R,mCAAA,kBAEM;UACVtV,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,eAA6E;UACqBD,EAAA,CAAAE,MAAA,IAA0D;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3LH,EAAA,CAAAC,cAAA,eAAqC;UACjCD,EAAA,CAAA8D,SAAA,iBAAyH;UAC7H9D,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,eAA4E;UACxED,EAAA,CAAA8D,SAAA,eAAqC;UACrC9D,EAAA,CAAAC,cAAA,eAAmC;UAC/BD,EAAA,CAAA2D,UAAA,KAAA4R,mCAAA,kBAEM;UACNvV,EAAA,CAAA2D,UAAA,KAAA6R,mCAAA,kBAEM;UACNxV,EAAA,CAAA2D,UAAA,KAAA8R,mCAAA,kBAEM;UACVzV,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,eAA6E;UACqBD,EAAA,CAAAE,MAAA,IAA0D;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3LH,EAAA,CAAAC,cAAA,eAAqC;UACjCD,EAAA,CAAA8D,SAAA,sBAAwL;UAC5L9D,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,eAA4E;UACxED,EAAA,CAAA8D,SAAA,eAAqC;UACrC9D,EAAA,CAAAC,cAAA,eAAmC;UAC/BD,EAAA,CAAA2D,UAAA,KAAA+R,mCAAA,kBAEM;UACV1V,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,eAAuE;UAC0BD,EAAA,CAAAE,MAAA,IAAyD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9JH,EAAA,CAAAC,cAAA,eAAqC;UACjCD,EAAA,CAAA8D,SAAA,oBAA0I;UAC9I9D,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,eAA4E;UACxED,EAAA,CAAA8D,SAAA,eAAqC;UACrC9D,EAAA,CAAAC,cAAA,eAAmC;UAC/BD,EAAA,CAAA2D,UAAA,KAAAgS,mCAAA,kBAEM;UACV3V,EAAA,CAAAG,YAAA,EAAM;UAGdH,EAAA,CAAAC,cAAA,cAA4B;UAE6ED,EAAA,CAAAE,MAAA,IAA6D;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjMH,EAAA,CAAAC,cAAA,eAAqC;UACsDD,EAAA,CAAA0B,UAAA,qBAAAkU,uDAAAhU,MAAA;YAAA,OAAWiT,GAAA,CAAAtH,UAAA,CAAA3L,MAAA,CAAkB;UAAA,EAAC,mBAAAiU,qDAAAjU,MAAA;YAAA,OAAUiT,GAAA,CAAAjH,eAAA,CAAAhM,MAAA,CAAuB;UAAA,EAAjC;UAArH5B,EAAA,CAAAG,YAAA,EAAsM;UAE1MH,EAAA,CAAAC,cAAA,eAAmD;UAC/CD,EAAA,CAAAE,MAAA,IAAsD;UAAAF,EAAA,CAAA8D,SAAA,UAAK;UAAC9D,EAAA,CAAAE,MAAA,IAChE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,eAA4E;UACxED,EAAA,CAAA8D,SAAA,eAAqC;UACrC9D,EAAA,CAAAC,cAAA,eAAmC;UAC/BD,EAAA,CAAA2D,UAAA,KAAAmS,mCAAA,kBAEM;UACN9V,EAAA,CAAA2D,UAAA,KAAAoS,mCAAA,kBAEM;UACN/V,EAAA,CAAA2D,UAAA,KAAAqS,mCAAA,kBAEM;UACVhW,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,eAA0D;UAElDD,EAAA,CAAA2D,UAAA,KAAAsS,mCAAA,kBAGM;UACVjW,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAwE;UAC4CD,EAAA,CAAAE,MAAA,IAAwD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChLH,EAAA,CAAAC,cAAA,eAAqC;UACjCD,EAAA,CAAA8D,SAAA,yBAAwF;UAC5F9D,EAAA,CAAAG,YAAA,EAAM;UAGdH,EAAA,CAAAC,cAAA,eAA8E;UACaD,EAAA,CAAAE,MAAA,IAAmD;UAAAF,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7KH,EAAA,CAAAC,cAAA,gBAAqC;UACjCD,EAAA,CAAA8D,SAAA,uBAA8K;UAClL9D,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAA8D,SAAA,gBACM;UACV9D,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,gBAA4E;UACxED,EAAA,CAAA8D,SAAA,gBAAqC;UACrC9D,EAAA,CAAAC,cAAA,gBAAmC;UAC/BD,EAAA,CAAA2D,UAAA,MAAAuS,oCAAA,kBAEM;UACVlW,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,gBAA6E;UACsBD,EAAA,CAAAE,MAAA,KAAsD;UAAAF,EAAA,CAAAC,cAAA,iBAA2B;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACxLH,EAAA,CAAAC,cAAA,gBAAqC;UACkDD,EAAA,CAAA0B,UAAA,qBAAAyU,wDAAAvU,MAAA;YAAA,OAAWiT,GAAA,CAAAtH,UAAA,CAAA3L,MAAA,CAAkB;UAAA,EAAC,mBAAAwU,sDAAAxU,MAAA;YAAA,OAAUiT,GAAA,CAAAjH,eAAA,CAAAhM,MAAA,CAAuB;UAAA,EAAjC;UAAjH5B,EAAA,CAAAG,YAAA,EAA0L;UAE9LH,EAAA,CAAA2D,UAAA,MAAA0S,oCAAA,kBAEM;UACNrW,EAAA,CAAA2D,UAAA,MAAA2S,oCAAA,kBAEM;UACNtW,EAAA,CAAA2D,UAAA,MAAA4S,oCAAA,kBAEM;UACVvW,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,gBAA4E;UACxED,EAAA,CAAA8D,SAAA,gBAAqC;UACrC9D,EAAA,CAAAC,cAAA,gBAAmC;UAC/BD,EAAA,CAAA2D,UAAA,MAAA6S,oCAAA,kBAEM;UACNxW,EAAA,CAAA2D,UAAA,MAAA8S,oCAAA,kBAEM;UACNzW,EAAA,CAAA2D,UAAA,MAAA+S,oCAAA,kBAEM;UACV1W,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,gBAA8E;UACmBD,EAAA,CAAAE,MAAA,KAAyD;UAAAF,EAAA,CAAAC,cAAA,iBAA2B;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACzLH,EAAA,CAAAC,cAAA,gBAAqC;UACrBD,EAAA,CAAA0B,UAAA,sBAAAiV,8DAAA/U,MAAA;YAAA,OAAYiT,GAAA,CAAA7G,gBAAA,CAAApM,MAAA,CAAwB;UAAA,EAAC;UAA2J5B,EAAA,CAAAG,YAAA,EAAa;UAE7NH,EAAA,CAAA8D,SAAA,gBACM;UACV9D,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,gBAA4E;UACxED,EAAA,CAAA8D,SAAA,gBAAqC;UACrC9D,EAAA,CAAAC,cAAA,gBAAmC;UAC/BD,EAAA,CAAA2D,UAAA,MAAAiT,oCAAA,kBAEM;UACV5W,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAA2D,UAAA,MAAAkT,oCAAA,kBAgBM;UACN7W,EAAA,CAAAC,cAAA,gBAAsD;UAClDD,EAAA,CAAA8D,SAAA,gBAAqC;UACrC9D,EAAA,CAAAC,cAAA,gBAAmC;UAC/BD,EAAA,CAAA2D,UAAA,MAAAmT,oCAAA,kBAEM;UACV9W,EAAA,CAAAG,YAAA,EAAM;UAEVH,EAAA,CAAA2D,UAAA,MAAAoT,oCAAA,kBAiBM;UAGV/W,EAAA,CAAAG,YAAA,EAAM;UAEVH,EAAA,CAAAC,cAAA,eAAiB;UAAAD,EAAA,CAAAE,MAAA,KAAkD;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxEH,EAAA,CAAAC,cAAA,eAA4E;UAGgCD,EAAA,CAAAE,MAAA,KAAsD;UAAAF,EAAA,CAAAC,cAAA,iBAA2B;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACzLH,EAAA,CAAAC,cAAA,gBAAqC;UACoDD,EAAA,CAAA0B,UAAA,qBAAAsV,wDAAApV,MAAA;YAAA,OAAWiT,GAAA,CAAAtH,UAAA,CAAA3L,MAAA,CAAkB;UAAA,EAAC,mBAAAqV,sDAAArV,MAAA;YAAA,OAAWiT,GAAA,CAAAjH,eAAA,CAAAhM,MAAA,CAAuB;UAAA,EAAlC;UAAnH5B,EAAA,CAAAG,YAAA,EAA6L;UAEjMH,EAAA,CAAAC,cAAA,gBAAmD;UAC/CD,EAAA,CAAAE,MAAA,eACJ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,gBAA4E;UACxED,EAAA,CAAA8D,SAAA,gBAAqC;UACrC9D,EAAA,CAAAC,cAAA,gBAAmC;UAC/BD,EAAA,CAAA2D,UAAA,MAAAuT,oCAAA,kBAEM;UACNlX,EAAA,CAAA2D,UAAA,MAAAwT,oCAAA,kBAEM;UACNnX,EAAA,CAAA2D,UAAA,MAAAyT,oCAAA,kBAEM;UACVpX,EAAA,CAAAG,YAAA,EAAM;UAEVH,EAAA,CAAAC,cAAA,gBAA6E;UACuBD,EAAA,CAAAE,MAAA,KAA2D;UAAAF,EAAA,CAAAC,cAAA,iBAA2B;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9LH,EAAA,CAAAC,cAAA,gBAAqC;UACsCD,EAAA,CAAA0B,UAAA,qBAAA2V,wDAAAzV,MAAA;YAAA,OAAWiT,GAAA,CAAAtH,UAAA,CAAA3L,MAAA,CAAkB;UAAA,EAAC,mBAAA0V,sDAAA1V,MAAA;YAAA,OAAWiT,GAAA,CAAAjH,eAAA,CAAAhM,MAAA,CAAuB;UAAA,EAAlC;UAArG5B,EAAA,CAAAG,YAAA,EAA8K;UAElLH,EAAA,CAAAC,cAAA,gBAAmD;UAC/CD,EAAA,CAAAE,MAAA,eACJ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,gBAA4E;UACxED,EAAA,CAAA8D,SAAA,gBAAqC;UACrC9D,EAAA,CAAAC,cAAA,gBAAmC;UAC/BD,EAAA,CAAA2D,UAAA,MAAA4T,oCAAA,kBAEM;UACNvX,EAAA,CAAA2D,UAAA,MAAA6T,oCAAA,kBAEM;UACNxX,EAAA,CAAA2D,UAAA,MAAA8T,oCAAA,kBAEM;UACVzX,EAAA,CAAAG,YAAA,EAAM;UAuBdH,EAAA,CAAAC,cAAA,gBAAoB;UAE8ED,EAAA,CAAAE,MAAA,KAA2D;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7JH,EAAA,CAAAC,cAAA,gBAAqC;UACmDD,EAAA,CAAA0B,UAAA,qBAAAgW,wDAAA9V,MAAA;YAAA,OAAWiT,GAAA,CAAAtH,UAAA,CAAA3L,MAAA,CAAkB;UAAA,EAAC,mBAAA+V,sDAAA/V,MAAA;YAAA,OAAUiT,GAAA,CAAAjH,eAAA,CAAAhM,MAAA,CAAuB;UAAA,EAAjC;UAAlH5B,EAAA,CAAAG,YAAA,EAAiM;UAIzMH,EAAA,CAAAC,cAAA,gBAA4E;UACxED,EAAA,CAAA8D,SAAA,gBAAqC;UACrC9D,EAAA,CAAAC,cAAA,gBAAmC;UAC/BD,EAAA,CAAA2D,UAAA,MAAAiU,oCAAA,kBAEM;UACN5X,EAAA,CAAA2D,UAAA,MAAAkU,oCAAA,kBAEM;UACV7X,EAAA,CAAAG,YAAA,EAAM;UAEVH,EAAA,CAAAC,cAAA,gBAA6E;UACuBD,EAAA,CAAAE,MAAA,KAA4D;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpKH,EAAA,CAAAC,cAAA,gBAAqC;UACqDD,EAAA,CAAA0B,UAAA,qBAAAoW,wDAAAlW,MAAA;YAAA,OAAWiT,GAAA,CAAAtH,UAAA,CAAA3L,MAAA,CAAkB;UAAA,EAAC,mBAAAmW,sDAAAnW,MAAA;YAAA,OAAWiT,GAAA,CAAAjH,eAAA,CAAAhM,MAAA,CAAuB;UAAA,EAAlC;UAApH5B,EAAA,CAAAG,YAAA,EAAoM;UAK5MH,EAAA,CAAAC,cAAA,gBAA4E;UACxED,EAAA,CAAA8D,SAAA,gBAAqC;UACrC9D,EAAA,CAAAC,cAAA,gBAAmC;UAC/BD,EAAA,CAAA2D,UAAA,MAAAqU,oCAAA,kBAEM;UACNhY,EAAA,CAAA2D,UAAA,MAAAsU,oCAAA,kBAEM;UACVjY,EAAA,CAAAG,YAAA,EAAM;UAIlBH,EAAA,CAAAC,cAAA,gBAAgD;UAC5BD,EAAA,CAAAE,MAAA,KAAsD;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3EH,EAAA,CAAAC,cAAA,0BAA0G;UAAvDD,EAAA,CAAA0B,UAAA,2BAAAwW,sEAAAtW,MAAA;YAAA,OAAAiT,GAAA,CAAA7L,UAAA,GAAApH,MAAA;UAAA,EAAwB,sBAAAuW,iEAAA;YAAA,OAAatD,GAAA,CAAAxG,cAAA,EAAgB;UAAA,EAA7B;UAA+BrO,EAAA,CAAAG,YAAA,EAAgB;UAE9HH,EAAA,CAAAC,cAAA,gBAA6F;UAEoDD,EAAA,CAAAE,MAAA,KAAwD;UAAAF,EAAA,CAAAC,cAAA,iBAA2B;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpOH,EAAA,CAAAC,cAAA,gBAAqC;UAC2ED,EAAA,CAAA0B,UAAA,qBAAA0W,wDAAAxW,MAAA;YAAA,OAAWiT,GAAA,CAAAtH,UAAA,CAAA3L,MAAA,CAAkB;UAAA,EAAC,mBAAAyW,sDAAAzW,MAAA;YAAA,OAAWiT,GAAA,CAAAjH,eAAA,CAAAhM,MAAA,CAAuB;UAAA,EAAlC;UAA1I5B,EAAA,CAAAG,YAAA,EAA+O;UAEnPH,EAAA,CAAAC,cAAA,gBAAqG;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC5GH,EAAA,CAAAC,cAAA,gBAAqC;UACkDD,EAAA,CAAA0B,UAAA,qBAAA4W,wDAAA1W,MAAA;YAAA,OAAWiT,GAAA,CAAAtH,UAAA,CAAA3L,MAAA,CAAkB;UAAA,EAAC,mBAAA2W,sDAAA3W,MAAA;YAAA,OAAWiT,GAAA,CAAAjH,eAAA,CAAAhM,MAAA,CAAuB;UAAA,EAAlC;UAAjH5B,EAAA,CAAAG,YAAA,EAAsN;UAE1NH,EAAA,CAAAC,cAAA,gBAA6F;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAG3GH,EAAA,CAAAC,cAAA,gBAAsD;UAClDD,EAAA,CAAA8D,SAAA,gBAAgG;UAChG9D,EAAA,CAAAC,cAAA,gBAAkC;UAC9BD,EAAA,CAAA2D,UAAA,MAAA6U,oCAAA,kBAEM;UACNxY,EAAA,CAAA2D,UAAA,MAAA8U,oCAAA,kBAEM;UACNzY,EAAA,CAAA2D,UAAA,MAAA+U,oCAAA,kBAEM;UAIV1Y,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAqD;UACjDD,EAAA,CAAA2D,UAAA,MAAAgV,oCAAA,kBAEM;UACN3Y,EAAA,CAAA2D,UAAA,MAAAiV,oCAAA,kBAEM;UACN5Y,EAAA,CAAA2D,UAAA,MAAAkV,oCAAA,kBAEM;UAIV7Y,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,gBAA6E;UAC2DD,EAAA,CAAAE,MAAA,KAA0D;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtMH,EAAA,CAAAC,cAAA,gBAAqC;UAC0CD,EAAA,CAAA0B,UAAA,qBAAAoX,wDAAAlX,MAAA;YAAA,OAAWiT,GAAA,CAAAtH,UAAA,CAAA3L,MAAA,CAAkB;UAAA,EAAC,mBAAAmX,sDAAAnX,MAAA;YAAA,OAAWiT,GAAA,CAAAjH,eAAA,CAAAhM,MAAA,CAAuB;UAAA,EAAlC;UAAzG5B,EAAA,CAAAG,YAAA,EAAiN;UAErNH,EAAA,CAAAC,cAAA,gBAAqG;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC5GH,EAAA,CAAAC,cAAA,gBAAqC;UACkDD,EAAA,CAAA0B,UAAA,qBAAAsX,wDAAApX,MAAA;YAAA,OAAWiT,GAAA,CAAAtH,UAAA,CAAA3L,MAAA,CAAkB;UAAA,EAAC,mBAAAqX,sDAAArX,MAAA;YAAA,OAAWiT,GAAA,CAAAjH,eAAA,CAAAhM,MAAA,CAAuB;UAAA,EAAlC;UAAjH5B,EAAA,CAAAG,YAAA,EAAyN;UAE7NH,EAAA,CAAAC,cAAA,gBAA6F;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAG7GH,EAAA,CAAAC,cAAA,gBAAsD;UAClDD,EAAA,CAAA8D,SAAA,gBAAgG;UAChG9D,EAAA,CAAAC,cAAA,gBAAkC;UAC9BD,EAAA,CAAA2D,UAAA,MAAAuV,oCAAA,kBAEM;UACNlZ,EAAA,CAAA2D,UAAA,MAAAwV,oCAAA,kBAEM;UACVnZ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAqD;UACjDD,EAAA,CAAA2D,UAAA,MAAAyV,oCAAA,kBAEM;UACNpZ,EAAA,CAAA2D,UAAA,MAAA0V,oCAAA,kBAEM;UACVrZ,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,gBAAsE;UACqED,EAAA,CAAAE,MAAA,KAA6D;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5MH,EAAA,CAAAC,cAAA,gBAAsC;UACgDD,EAAA,CAAA0B,UAAA,qBAAA4X,wDAAA1X,MAAA;YAAA,OAAWiT,GAAA,CAAAtH,UAAA,CAAA3L,MAAA,CAAkB;UAAA,EAAC,mBAAA2X,sDAAA3X,MAAA;YAAA,OAAUiT,GAAA,CAAAjH,eAAA,CAAAhM,MAAA,CAAuB;UAAA,EAAjC;UAAhH5B,EAAA,CAAAG,YAAA,EAA0N;UAE9NH,EAAA,CAAA8D,SAAA,gBAA2G;UAI/G9D,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,gBAAsD;UAClDD,EAAA,CAAA8D,SAAA,gBAA8F;UAC9F9D,EAAA,CAAAC,cAAA,gBAAsD;UAClDD,EAAA,CAAA2D,UAAA,MAAA6V,oCAAA,kBAEM;UACNxZ,EAAA,CAAA2D,UAAA,MAAA8V,oCAAA,kBAEM;UACVzZ,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,gBAAsE;UACsED,EAAA,CAAAE,MAAA,KAA8D;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9MH,EAAA,CAAAC,cAAA,gBAAmD;UACoCD,EAAA,CAAA0B,UAAA,qBAAAgY,wDAAA9X,MAAA;YAAA,OAAWiT,GAAA,CAAAtH,UAAA,CAAA3L,MAAA,CAAkB;UAAA,EAAC,mBAAA+X,sDAAA/X,MAAA;YAAA,OAAWiT,GAAA,CAAAjH,eAAA,CAAAhM,MAAA,CAAuB;UAAA,EAAlC;UAAjH5B,EAAA,CAAAG,YAAA,EAA4N;UAEhOH,EAAA,CAAA8D,SAAA,gBAA2G;UAI/G9D,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,gBAAsD;UAClDD,EAAA,CAAA8D,SAAA,gBAA8F;UAC9F9D,EAAA,CAAAC,cAAA,gBAAsD;UAClDD,EAAA,CAAA2D,UAAA,MAAAiW,oCAAA,kBAEM;UACN5Z,EAAA,CAAA2D,UAAA,MAAAkW,oCAAA,kBAEM;UACV7Z,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,gBAA4E;UAC6DD,EAAA,CAAAE,MAAA,KAAoD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjMH,EAAA,CAAAC,cAAA,gBAAgD;UACiCD,EAAA,CAAA0B,UAAA,qBAAAoY,wDAAAlY,MAAA;YAAA,OAAWiT,GAAA,CAAAtH,UAAA,CAAA3L,MAAA,CAAkB;UAAA,EAAC,mBAAAmY,sDAAAnY,MAAA;YAAA,OAAWiT,GAAA,CAAAjH,eAAA,CAAAhM,MAAA,CAAuB;UAAA,EAAlC;UAA3G5B,EAAA,CAAAG,YAAA,EAA4M;UAEhNH,EAAA,CAAA8D,SAAA,gBAA2G;UAI/G9D,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,gBAAsD;UAClDD,EAAA,CAAA8D,SAAA,gBAA8F;UAC9F9D,EAAA,CAAAC,cAAA,gBAAsD;UAClDD,EAAA,CAAA2D,UAAA,MAAAqW,oCAAA,kBAEM;UACNha,EAAA,CAAA2D,UAAA,MAAAsW,oCAAA,kBAEM;UACVja,EAAA,CAAAG,YAAA,EAAM;UAGdH,EAAA,CAAAC,cAAA,gBAA4D;UACjCD,EAAA,CAAA8D,SAAA,mBAA4I;UAAA9D,EAAA,CAAAG,YAAA,EAAI;UACvKH,EAAA,CAAA8D,SAAA,mBAA6L;UACjM9D,EAAA,CAAAG,YAAA,EAAM;UAIdH,EAAA,CAAAC,cAAA,iBAAqE;UAAlCD,EAAA,CAAA0B,UAAA,sBAAAwY,wDAAA;YAAA,OAAYrF,GAAA,CAAAtF,kBAAA,EAAoB;UAAA,EAAC;UAChEvP,EAAA,CAAAC,cAAA,gBAA2D;UACwBD,EAAA,CAAA0B,UAAA,2BAAAyY,iEAAAvY,MAAA;YAAA,OAAAiT,GAAA,CAAApM,8BAAA,GAAA7G,MAAA;UAAA,EAA4C;UACvH5B,EAAA,CAAAC,cAAA,gBAAkB;UAMCD,EAAA,CAAA0B,UAAA,2BAAA0Y,8DAAAxY,MAAA;YAAA,OAAAiT,GAAA,CAAAvO,cAAA,CAAA+J,QAAA,GAAAzO,MAAA;UAAA,EAAqC;UAF5C5B,EAAA,CAAAG,YAAA,EAKE;UACFH,EAAA,CAAAC,cAAA,kBAA0B;UAAAD,EAAA,CAAAE,MAAA,KAAsD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAIhGH,EAAA,CAAAC,cAAA,gBAAmB;UAIJD,EAAA,CAAA0B,UAAA,2BAAA2Y,8DAAAzY,MAAA;YAAA,OAAAiT,GAAA,CAAAvO,cAAA,CAAAgK,QAAA,GAAA1O,MAAA;UAAA,EAAqC;UAF5C5B,EAAA,CAAAG,YAAA,EAKE;UACFH,EAAA,CAAAC,cAAA,kBAA0B;UAAAD,EAAA,CAAAE,MAAA,KAAsD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAIhGH,EAAA,CAAAC,cAAA,gBAA2H;UACvHD,EAAA,CAAA2D,UAAA,MAAA2W,qCAAA,mBAUO;UACPta,EAAA,CAAA2D,UAAA,MAAA4W,qCAAA,oBAAwH;UAC5Hva,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,iBAAwB;UACpBD,EAAA,CAAA8D,SAAA,sBAGY;UAChB9D,EAAA,CAAAG,YAAA,EAAM;UAEVH,EAAA,CAAAC,cAAA,wBAaC;UATGD,EAAA,CAAA0B,UAAA,+BAAA8Y,uEAAA5Y,MAAA;YAAA,OAAAiT,GAAA,CAAAnM,eAAA,GAAA9G,MAAA;UAAA,EAAiC;UASpC5B,EAAA,CAAAG,YAAA,EAAa;;;UAtlBkBH,EAAA,CAAAI,SAAA,GAAiD;UAAjDJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,yBAAiD;UAC9CR,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAmC,UAAA,UAAA0S,GAAA,CAAAnE,KAAA,CAAe,SAAAmE,GAAA,CAAA9B,IAAA;UAI1C/S,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAmC,UAAA,cAAA0S,GAAA,CAAAnK,cAAA,CAA4B;UAI0D1K,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,8BAAsD;UAEzER,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAmC,UAAA,gBAAA0S,GAAA,CAAAtR,WAAA,CAAAgG,QAAA,CAAoC;UAO7FvJ,EAAA,CAAAI,SAAA,GAAwE;UAAxEJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA1N,eAAA,IAAA0N,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,SAAAwM,QAAA,aAAwE;UAGxEza,EAAA,CAAAI,SAAA,GAAyE;UAAzEJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA1N,eAAA,IAAA0N,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,SAAAwM,QAAA,cAAyE;UAGzEza,EAAA,CAAAI,SAAA,GAAiF;UAAjFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA1N,eAAA,IAAA0N,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,SAAAwM,QAAA,sBAAiF;UAGjFza,EAAA,CAAAI,SAAA,GAAsE;UAAtEJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA1N,eAAA,IAAA0N,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,SAAAwM,QAAA,WAAsE;UAOMza,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,8BAAsD;UAEzER,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAmC,UAAA,gBAAA0S,GAAA,CAAAtR,WAAA,CAAAiG,QAAA,CAAoC;UAO7FxJ,EAAA,CAAAI,SAAA,GAAyE;UAAzEJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAzN,gBAAA,IAAAyN,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,SAAAwM,QAAA,aAAyE;UAGzEza,EAAA,CAAAI,SAAA,GAA0E;UAA1EJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAzN,gBAAA,IAAAyN,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,SAAAwM,QAAA,cAA0E;UAG1Eza,EAAA,CAAAI,SAAA,GAAkF;UAAlFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAzN,gBAAA,IAAAyN,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,SAAAwM,QAAA,sBAAkF;UAGlFza,EAAA,CAAAI,SAAA,GAAuE;UAAvEJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAzN,gBAAA,IAAAyN,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,SAAAwM,QAAA,WAAuE;UAOaza,EAAA,CAAAI,SAAA,GAA0D;UAA1DJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,kCAA0D;UAErER,EAAA,CAAAI,SAAA,GAAwC;UAAxCJ,EAAA,CAAAmC,UAAA,gBAAA0S,GAAA,CAAAtR,WAAA,CAAAkG,YAAA,CAAwC;UAOjHzJ,EAAA,CAAAI,SAAA,GAAoF;UAApFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAxN,mBAAA,IAAAwN,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,iBAAAwM,QAAA,aAAoF;UAGpFza,EAAA,CAAAI,SAAA,GAAqF;UAArFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAxN,mBAAA,IAAAwN,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,iBAAAwM,QAAA,cAAqF;UAGrFza,EAAA,CAAAI,SAAA,GAA6F;UAA7FJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAxN,mBAAA,IAAAwN,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,iBAAAwM,QAAA,sBAA6F;UAOTza,EAAA,CAAAI,SAAA,GAA0D;UAA1DJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,kCAA0D;UAEzGR,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAmC,UAAA,YAAA0S,GAAA,CAAA3D,aAAA,CAAyB,gBAAA2D,GAAA,CAAAtR,WAAA,CAAAmG,YAAA;UAO9D1J,EAAA,CAAAI,SAAA,GAAoF;UAApFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAvN,mBAAA,IAAAuN,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,iBAAAwM,QAAA,aAAoF;UAODza,EAAA,CAAAI,SAAA,GAAyD;UAAzDJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,iCAAyD;UAEtER,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAAmC,UAAA,gBAAA0S,GAAA,CAAAtR,WAAA,CAAAoG,WAAA,CAAuC;UAO7G3J,EAAA,CAAAI,SAAA,GAAmF;UAAnFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAhN,kBAAA,IAAAgN,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,gBAAAwM,QAAA,cAAmF;UAQIza,EAAA,CAAAI,SAAA,GAA6D;UAA7DJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,qCAA6D;UAEDR,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAmC,UAAA,gBAAA0S,GAAA,CAAAtR,WAAA,CAAAqG,eAAA,CAA2C;UAGpM5J,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAK,kBAAA,MAAAwU,GAAA,CAAAtU,WAAA,CAAAC,SAAA,kCAAsD;UAAMR,EAAA,CAAAI,SAAA,GAChE;UADgEJ,EAAA,CAAAK,kBAAA,MAAAwU,GAAA,CAAAtU,WAAA,CAAAC,SAAA,6BAChE;UAMUR,EAAA,CAAAI,SAAA,GAA0F;UAA1FJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAtN,sBAAA,IAAAsN,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,oBAAAwM,QAAA,aAA0F;UAG1Fza,EAAA,CAAAI,SAAA,GAAqF;UAArFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAtN,sBAAA,IAAAsN,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,oBAAAwM,QAAA,QAAqF;UAGrFza,EAAA,CAAAI,SAAA,GAAqF;UAArFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAtN,sBAAA,IAAAsN,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,oBAAAwM,QAAA,QAAqF;UAQjEza,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAmC,UAAA,YAAA0S,GAAA,CAAA5L,cAAA,CAAiB;UAMqEjJ,EAAA,CAAAI,SAAA,GAAwD;UAAxDJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,gCAAwD;UAOrFR,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,2BAAmD;UAE1FR,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAmC,UAAA,YAAA0S,GAAA,CAAAzD,MAAA,CAAkB,gBAAAyD,GAAA,CAAAtR,WAAA,CAAAwG,SAAA;UASxD/J,EAAA,CAAAI,SAAA,GAAkF;UAAlFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAlN,gBAAA,IAAAkN,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,kBAAAwM,QAAA,aAAkF;UAOGza,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,8BAAsD;UAEGR,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAmC,UAAA,gBAAA0S,GAAA,CAAAtR,WAAA,CAAAyG,QAAA,CAAoC;UAEtLhK,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,kBAAA1C,KAAA,QAAsD;UAGtDvL,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,kBAAA1C,KAAA,QAAsD;UAGtDvL,EAAA,CAAAI,SAAA,GAA0G;UAA1GJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,kBAAA1C,KAAA,WAAAsJ,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,kBAAA1C,KAAA,QAA0G;UAQtGvL,EAAA,CAAAI,SAAA,GAAiF;UAAjFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAjN,eAAA,IAAAiN,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,kBAAAwM,QAAA,aAAiF;UAGjFza,EAAA,CAAAI,SAAA,GAA4E;UAA5EJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAjN,eAAA,IAAAiN,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,kBAAAwM,QAAA,QAA4E;UAG5Eza,EAAA,CAAAI,SAAA,GAA4E;UAA5EJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAjN,eAAA,IAAAiN,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,kBAAAwM,QAAA,QAA4E;UAOOza,EAAA,CAAAI,SAAA,GAAyD;UAAzDJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,iCAAyD;UAElER,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAmC,UAAA,YAAA0S,GAAA,CAAA1D,YAAA,CAAwB,gBAAA0D,GAAA,CAAAtR,WAAA,CAAAuG,SAAA;UASlG9J,EAAA,CAAAI,SAAA,GAAgF;UAAhFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAApN,gBAAA,IAAAoN,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,gBAAAwM,QAAA,aAAgF;UAMxFza,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA/L,UAAA,CAAgB;UAoBR9I,EAAA,CAAAI,SAAA,GAAoF;UAApFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAnN,mBAAA,IAAAmN,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,iBAAAwM,QAAA,aAAoF;UAK5Fza,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA9L,UAAA,CAAgB;UAsBb/I,EAAA,CAAAI,SAAA,GAAkD;UAAlDJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,0BAAkD;UAIyCR,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,8BAAsD;UAEKR,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAmC,UAAA,gBAAA0S,GAAA,CAAAtR,WAAA,CAAA0G,QAAA,CAAoC;UAUrLjK,EAAA,CAAAI,SAAA,GAAkF;UAAlFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA/M,eAAA,IAAA+M,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,mBAAAwM,QAAA,aAAkF;UAGlFza,EAAA,CAAAI,SAAA,GAA6E;UAA7EJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA/M,eAAA,IAAA+M,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,mBAAAwM,QAAA,QAA6E;UAG7Eza,EAAA,CAAAI,SAAA,GAA6E;UAA7EJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA/M,eAAA,IAAA+M,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,mBAAAwM,QAAA,QAA6E;UAMSza,EAAA,CAAAI,SAAA,GAA2D;UAA3DJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,mCAA2D;UAEdR,EAAA,CAAAI,SAAA,GAAmC;UAAnCJ,EAAA,CAAAmC,UAAA,gBAAA0S,GAAA,CAAAtR,WAAA,CAAAkH,OAAA,CAAmC;UAUtKzK,EAAA,CAAAI,SAAA,GAA0E;UAA1EJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAArM,cAAA,IAAAqM,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,YAAAwM,QAAA,aAA0E;UAG1Eza,EAAA,CAAAI,SAAA,GAAqE;UAArEJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAArM,cAAA,IAAAqM,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,YAAAwM,QAAA,QAAqE;UAGrEza,EAAA,CAAAI,SAAA,GAAqE;UAArEJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAArM,cAAA,IAAAqM,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,YAAAwM,QAAA,QAAqE;UA4BWza,EAAA,CAAAI,SAAA,GAA2D;UAA3DJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,mCAA2D;UAEKR,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAAmC,UAAA,gBAAA0S,GAAA,CAAAtR,WAAA,CAAA2G,aAAA,CAAyC;UAOzLlK,EAAA,CAAAI,SAAA,GAAuF;UAAvFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA9M,yBAAA,IAAA8M,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,mBAAAwM,QAAA,QAAuF;UAGvFza,EAAA,CAAAI,SAAA,GAAuF;UAAvFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA9M,yBAAA,IAAA8M,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,mBAAAwM,QAAA,QAAuF;UAMDza,EAAA,CAAAI,SAAA,GAA4D;UAA5DJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,oCAA4D;UAEAR,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAmC,UAAA,gBAAA0S,GAAA,CAAAtR,WAAA,CAAA4G,cAAA,CAA0C;UAQ5LnK,EAAA,CAAAI,SAAA,GAAyF;UAAzFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA7M,0BAAA,IAAA6M,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,oBAAAwM,QAAA,QAAyF;UAGzFza,EAAA,CAAAI,SAAA,GAAyF;UAAzFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA7M,0BAAA,IAAA6M,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,oBAAAwM,QAAA,QAAyF;UAQ3Fza,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,8BAAsD;UACnBR,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAmC,UAAA,YAAA0S,GAAA,CAAA7L,UAAA,CAAwB;UAIvChJ,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAA0a,WAAA,WAAA7F,GAAA,CAAA7L,UAAA,oBAA8C;UAA2DhJ,EAAA,CAAAI,SAAA,GAAwD;UAAxDJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,gCAAwD;UAEfR,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAmC,UAAA,cAAA0S,GAAA,CAAA7L,UAAA,CAAwB,gBAAA6L,GAAA,CAAAtR,WAAA,CAAA6G,UAAA;UAE7KpK,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAA0a,WAAA,WAAA7F,GAAA,CAAA7L,UAAA,oBAA8C;UAE8EhJ,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAmC,UAAA,cAAA0S,GAAA,CAAA7L,UAAA,CAAwB,gBAAA6L,GAAA,CAAAtR,WAAA,CAAA6G,UAAA;UAE5KpK,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAA0a,WAAA,WAAA7F,GAAA,CAAA7L,UAAA,oBAA8C;UAMzChJ,EAAA,CAAAI,SAAA,GAA8F;UAA9FJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA5M,2BAAA,IAAA4M,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,mBAAAwM,QAAA,aAA8F;UAG9Fza,EAAA,CAAAI,SAAA,GAAyF;UAAzFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA5M,2BAAA,IAAA4M,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,mBAAAwM,QAAA,QAAyF;UAGzFza,EAAA,CAAAI,SAAA,GAAyF;UAAzFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA5M,2BAAA,IAAA4M,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,mBAAAwM,QAAA,QAAyF;UAQzFza,EAAA,CAAAI,SAAA,GAA8F;UAA9FJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA3M,4BAAA,IAAA2M,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,kBAAAwM,QAAA,aAA8F;UAG9Fza,EAAA,CAAAI,SAAA,GAAyF;UAAzFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA3M,4BAAA,IAAA2M,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,kBAAAwM,QAAA,QAAyF;UAGzFza,EAAA,CAAAI,SAAA,GAAyF;UAAzFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA3M,4BAAA,IAAA2M,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,kBAAAwM,QAAA,QAAyF;UAUxEza,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAA0a,WAAA,WAAA7F,GAAA,CAAA7L,UAAA,oBAA8C;UAA2DhJ,EAAA,CAAAI,SAAA,GAA0D;UAA1DJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,kCAA0D;UAE7CR,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAmC,UAAA,cAAA0S,GAAA,CAAA7L,UAAA,CAAwB,gBAAA6L,GAAA,CAAAtR,WAAA,CAAA8G,aAAA;UAE5IrK,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAA0a,WAAA,WAAA7F,GAAA,CAAA7L,UAAA,oBAA8C;UAE8EhJ,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAmC,UAAA,cAAA0S,GAAA,CAAA7L,UAAA,CAAwB,gBAAA6L,GAAA,CAAAtR,WAAA,CAAA8G,aAAA;UAE5KrK,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAA0a,WAAA,WAAA7F,GAAA,CAAA7L,UAAA,oBAA8C;UAMzChJ,EAAA,CAAAI,SAAA,GAAsF;UAAtFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA1M,6BAAA,IAAA0M,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,cAAAwM,QAAA,QAAsF;UAGtFza,EAAA,CAAAI,SAAA,GAAsF;UAAtFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA1M,6BAAA,IAAA0M,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,cAAAwM,QAAA,QAAsF;UAKtFza,EAAA,CAAAI,SAAA,GAA2F;UAA3FJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAzM,8BAAA,IAAAyM,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,kBAAAwM,QAAA,QAA2F;UAG3Fza,EAAA,CAAAI,SAAA,GAA2F;UAA3FJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAzM,8BAAA,IAAAyM,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,kBAAAwM,QAAA,QAA2F;UAOvEza,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAA0a,WAAA,WAAA7F,GAAA,CAAA7L,UAAA,oBAA8C;UAA2DhJ,EAAA,CAAAI,SAAA,GAA6D;UAA7DJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,qCAA6D;UAE5CR,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAmC,UAAA,cAAA0S,GAAA,CAAA7L,UAAA,CAAwB,gBAAA6L,GAAA,CAAAtR,WAAA,CAAA+G,eAAA;UAEnJtK,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAA0a,WAAA,WAAA7F,GAAA,CAAA7L,UAAA,oBAA8C;UAGtEhJ,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAA0a,WAAA,WAAA7F,GAAA,CAAA7L,UAAA,oBAA8C;UAMzChJ,EAAA,CAAAI,SAAA,GAAkF;UAAlFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAxM,sBAAA,IAAAwM,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,iBAAAwM,QAAA,QAAkF;UAGlFza,EAAA,CAAAI,SAAA,GAAkF;UAAlFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAxM,sBAAA,IAAAwM,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,iBAAAwM,QAAA,QAAkF;UAO7Dza,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAA0a,WAAA,WAAA7F,GAAA,CAAA7L,UAAA,oBAA8C;UAA2DhJ,EAAA,CAAAI,SAAA,GAA8D;UAA9DJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,sCAA8D;UAE7CR,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAmC,UAAA,cAAA0S,GAAA,CAAA7L,UAAA,CAAwB,gBAAA6L,GAAA,CAAAtR,WAAA,CAAAgH,gBAAA;UAEpJvK,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAA0a,WAAA,WAAA7F,GAAA,CAAA7L,UAAA,oBAA8C;UAGtEhJ,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAA0a,WAAA,WAAA7F,GAAA,CAAA7L,UAAA,oBAA8C;UAMzChJ,EAAA,CAAAI,SAAA,GAAoF;UAApFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAvM,uBAAA,IAAAuM,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,kBAAAwM,QAAA,QAAoF;UAGpFza,EAAA,CAAAI,SAAA,GAAoF;UAApFJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAvM,uBAAA,IAAAuM,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,kBAAAwM,QAAA,QAAoF;UAOlEza,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAA0a,WAAA,WAAA7F,GAAA,CAAA7L,UAAA,oBAA8C;UAA2DhJ,EAAA,CAAAI,SAAA,GAAoD;UAApDJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,4BAAoD;UAEtCR,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAmC,UAAA,cAAA0S,GAAA,CAAA7L,UAAA,CAAwB,gBAAA6L,GAAA,CAAAtR,WAAA,CAAAiH,MAAA;UAE9IxK,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAA0a,WAAA,WAAA7F,GAAA,CAAA7L,UAAA,oBAA8C;UAGtEhJ,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAA0a,WAAA,WAAA7F,GAAA,CAAA7L,UAAA,oBAA8C;UAMzChJ,EAAA,CAAAI,SAAA,GAAuE;UAAvEJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAtM,aAAA,IAAAsM,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,eAAAwM,QAAA,QAAuE;UAGvEza,EAAA,CAAAI,SAAA,GAAuE;UAAvEJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAAtM,aAAA,IAAAsM,GAAA,CAAAnK,cAAA,CAAAuD,GAAA,eAAAwM,QAAA,QAAuE;UAO9Cza,EAAA,CAAAI,SAAA,GAAuD;UAAvDJ,EAAA,CAAAmC,UAAA,UAAA0S,GAAA,CAAAtU,WAAA,CAAAC,SAAA,yBAAuD;UAC9ER,EAAA,CAAAI,SAAA,GAAqD;UAArDJ,EAAA,CAAAmC,UAAA,UAAA0S,GAAA,CAAAtU,WAAA,CAAAC,SAAA,uBAAqD,aAAAqU,GAAA,CAAA5N,iBAAA,IAAA4N,GAAA,CAAA3N,iBAAA,IAAA2N,GAAA,CAAAnK,cAAA,CAAAiQ,OAAA;UAK3E3a,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAAmC,UAAA,cAAA0S,GAAA,CAAArE,cAAA,CAA4B;UAEiHxQ,EAAA,CAAAI,SAAA,GAA4B;UAA5BJ,EAAA,CAAA4a,UAAA,CAAA5a,EAAA,CAAAwB,eAAA,MAAAqZ,GAAA,EAA4B;UAA7J7a,EAAA,CAAAmC,UAAA,WAAA0S,GAAA,CAAAtU,WAAA,CAAAC,SAAA,qCAAoE,YAAAqU,GAAA,CAAApM,8BAAA;UAOvDzI,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAmC,UAAA,YAAA0S,GAAA,CAAAvO,cAAA,CAAA+J,QAAA,CAAqC;UAIlBrQ,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,8BAAsD;UAQzER,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAmC,UAAA,YAAA0S,GAAA,CAAAvO,cAAA,CAAAgK,QAAA,CAAqC;UAIlBtQ,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAuC,iBAAA,CAAAsS,GAAA,CAAAtU,WAAA,CAAAC,SAAA,8BAAsD;UAIrER,EAAA,CAAAI,SAAA,GAAuG;UAAvGJ,EAAA,CAAA8a,UAAA,CAAAjG,GAAA,CAAA7Q,QAAA,IAAA6Q,GAAA,CAAA5Q,WAAA,CAAAC,KAAA,iEAAuG;UACzFlE,EAAA,CAAAI,SAAA,GAAmC;UAAnCJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA7Q,QAAA,IAAA6Q,GAAA,CAAA5Q,WAAA,CAAAC,KAAA,CAAmC;UAWzDlE,EAAA,CAAAI,SAAA,GAAmC;UAAnCJ,EAAA,CAAAmC,UAAA,SAAA0S,GAAA,CAAA7Q,QAAA,IAAA6Q,GAAA,CAAA5Q,WAAA,CAAAC,KAAA,CAAmC;UAU9ClE,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAmC,UAAA,iBAAgB,eAAA0S,GAAA,CAAArF,gBAAA,cAAAqF,GAAA,CAAAnF,cAAA,iBAAAmF,GAAA,CAAAnM,eAAA,aAAAmM,GAAA,CAAAhE,eAAA,aAAAgE,GAAA,CAAA3E,iBAAA,aAAA2E,GAAA,CAAAhF,6BAAA,cAAAgF,GAAA,CAAApF,UAAA,CAAAsL,IAAA,CAAAlG,GAAA,yBAAA7U,EAAA,CAAAwB,eAAA,MAAAwZ,GAAA,oCAAAnG,GAAA,CAAAtB,IAAA,YAAAsB,GAAA,CAAAvO,cAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}