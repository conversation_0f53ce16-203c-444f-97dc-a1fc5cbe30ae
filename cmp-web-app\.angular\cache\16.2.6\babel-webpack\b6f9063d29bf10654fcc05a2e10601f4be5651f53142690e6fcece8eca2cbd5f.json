{"ast": null, "code": "import { transData } from \"src/i18n\";\nimport * as i0 from \"@angular/core\";\nexport class TranslateService {\n  constructor() {\n    this.lang = localStorage.getItem(\"lang\") || \"vi\";\n  }\n  translate(path, params, textReplace) {\n    this.lang = localStorage.getItem(\"lang\") || \"vi\";\n    try {\n      let arrayPath = path.split(\"\\.\");\n      let value = transData[this.lang];\n      if (value === undefined || value === null) {\n        value = transData[\"vi\"];\n      }\n      arrayPath.forEach(el => value = value[el]);\n      if (typeof value === 'string') {\n        if (params != undefined && params != null) {\n          let keys = Object.keys(params);\n          keys.forEach(el => {\n            while (value.indexOf(`\\$\\{${el}\\}`) >= 0) {\n              value = value.replace(`\\$\\{${el}\\}`, params[el]);\n            }\n          });\n        }\n        return value;\n      }\n    } catch (error) {\n      // console.error(error);\n    }\n    if (textReplace) {\n      return textReplace;\n    } else {\n      return path;\n    }\n  }\n  static {\n    this.ɵfac = function TranslateService_Factory(t) {\n      return new (t || TranslateService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TranslateService,\n      factory: TranslateService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["transData", "TranslateService", "constructor", "lang", "localStorage", "getItem", "translate", "path", "params", "textReplace", "arrayPath", "split", "value", "undefined", "for<PERSON>ach", "el", "keys", "Object", "indexOf", "replace", "error", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\service\\comon\\translate.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\r\nimport { transData } from \"src/i18n\";\r\n@Injectable({ providedIn: 'root'})\r\nexport class TranslateService{\r\n    public lang: string;\r\n    constructor(){\r\n        this.lang = localStorage.getItem(\"lang\") || \"vi\";\r\n    }\r\n\r\n    public translate(path: string, params?: {[key: string]: any}, textReplace?:string): string{\r\n        this.lang = localStorage.getItem(\"lang\") || \"vi\";\r\n        try {\r\n            let arrayPath = path.split(\"\\.\");\r\n            let value = transData[this.lang];\r\n            if(value === undefined || value === null){\r\n                value = transData[\"vi\"];\r\n            }\r\n            arrayPath.forEach(el => value = value[el]);\r\n            if(typeof(value) === 'string'){\r\n                if(params != undefined && params != null){\r\n                    let keys = Object.keys(params);\r\n                    keys.forEach(el => {\r\n                        while(value.indexOf(`\\$\\{${el}\\}`)>=0){\r\n                            value = value.replace( `\\$\\{${el}\\}`, params[el])\r\n                        }\r\n                    })\r\n                }\r\n                return value;\r\n            }\r\n        } catch (error) {\r\n            // console.error(error);\r\n        }\r\n        if(textReplace){\r\n            return textReplace;\r\n        }else{\r\n            return path;\r\n        }\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,UAAU;;AAEpC,OAAM,MAAOC,gBAAgB;EAEzBC,YAAA;IACI,IAAI,CAACC,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI;EACpD;EAEOC,SAASA,CAACC,IAAY,EAAEC,MAA6B,EAAEC,WAAmB;IAC7E,IAAI,CAACN,IAAI,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI;IAChD,IAAI;MACA,IAAIK,SAAS,GAAGH,IAAI,CAACI,KAAK,CAAC,IAAI,CAAC;MAChC,IAAIC,KAAK,GAAGZ,SAAS,CAAC,IAAI,CAACG,IAAI,CAAC;MAChC,IAAGS,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAC;QACrCA,KAAK,GAAGZ,SAAS,CAAC,IAAI,CAAC;;MAE3BU,SAAS,CAACI,OAAO,CAACC,EAAE,IAAIH,KAAK,GAAGA,KAAK,CAACG,EAAE,CAAC,CAAC;MAC1C,IAAG,OAAOH,KAAM,KAAK,QAAQ,EAAC;QAC1B,IAAGJ,MAAM,IAAIK,SAAS,IAAIL,MAAM,IAAI,IAAI,EAAC;UACrC,IAAIQ,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACR,MAAM,CAAC;UAC9BQ,IAAI,CAACF,OAAO,CAACC,EAAE,IAAG;YACd,OAAMH,KAAK,CAACM,OAAO,CAAC,OAAOH,EAAE,IAAI,CAAC,IAAE,CAAC,EAAC;cAClCH,KAAK,GAAGA,KAAK,CAACO,OAAO,CAAE,OAAOJ,EAAE,IAAI,EAAEP,MAAM,CAACO,EAAE,CAAC,CAAC;;UAEzD,CAAC,CAAC;;QAEN,OAAOH,KAAK;;KAEnB,CAAC,OAAOQ,KAAK,EAAE;MACZ;IAAA;IAEJ,IAAGX,WAAW,EAAC;MACX,OAAOA,WAAW;KACrB,MAAI;MACD,OAAOF,IAAI;;EAEnB;;;uBAlCSN,gBAAgB;IAAA;EAAA;;;aAAhBA,gBAAgB;MAAAoB,OAAA,EAAhBpB,gBAAgB,CAAAqB,IAAA;MAAAC,UAAA,EADH;IAAM;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}