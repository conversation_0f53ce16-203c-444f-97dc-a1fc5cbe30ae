import {Component, OnInit, AfterContentChecked, Inject, Injector, HostListener} from "@angular/core";
import {MenuItem} from "primeng/api";
import {TranslateService} from "../../../service/comon/translate.service";
import {FormBuilder } from "@angular/forms";
import {MessageCommonService} from "../../../service/comon/message-common.service";;
import {CONSTANTS} from "../../../service/comon/constants";
import {CustomerService} from "../../../service/customer/CustomerService";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";
import {UtilService} from "../../../service/comon/util.service";
import {ApnSimService} from "../../../service/apn/ApnSimService";
import {BaseComponent} from "@fullcalendar/core/internal";
import {ComponentBase} from "../../../component.base";
import {SimService} from "../../../service/sim/SimService";
@Component({
    selector: "app-apn-sim-list",
    templateUrl: './app.apnsim.list.component.html'
})
export class AppApnSimListComponent extends ComponentBase implements OnInit, AfterContentChecked{
    items: MenuItem[];
    home: MenuItem
    searchInfo: {
        msisdn: string|null,
        deviceImei: string|null,
        status: any,
        customer: string|null,
        contractDateFrom: Date|null,
        contractDateTo: Date|null,
        apnId: string|null,
    };
    formSearchSim: any;
    pageNumber: number;
    pageSize: number;
    sort: string;
    dataSet: {
        content: Array<any>,
        total: number
    };
    searchInfoStandard:any;
    maxDateFrom: Date|number|string|null = new Date();
    minDateTo: Date|number|string|null = null;
    maxDateTo: Date|number|string|null = new Date();
    statuSims: Array<{value:string|number|Array<string>|Array<number>, name: string}>;
    listCustomer: Array<any>;
    listCustomerOrigin: Array<any>;
    columns: Array<ColumnInfo>;
    optionTable: OptionTable;
    selectItems: Array<{imsi:number,groupName:string|null,[key:string]:any}>;
    detailSim:any = {};
    detailStatusSim: any={};
    detailCustomer:any={};
    detailRatingPlan: any={};
    detailContract: any={};
    detailAPN: any={};
    isShowPopupDetailSim: boolean = false;
    isShowPopupDetailAPNSim: boolean = false;
    simId: string;
    apnId: number | string;
    detailApnSim: {
        vpnChannelName: string|null,
        pdpcp: string|null,
        epsProfileId: string|null,
        msisdn: string|null,
        ipType: string|null,
        ip: string|null,
        apnId: string|null,
        apnStatus: string|null,
        deviceImei: string|null,
        customerName: string|null,
        contractCode: Date|string|null,
        contractDate: Date|string|null,
        status: string|null,
        note: string|null,
    };
    fieldsToDisplay = [
        // 'imsi',
        'vpnChannelName',
        'pdpcp',
        'epsProfileId',
        'msisdn',
        'ipType',
        'ip',
        'apnId',
        // 'apnStatus',
        'deviceImei',
        'customerName',
        'contractCode',
        'contractDate',
        'status',
        'note',
    ];

    isMobileView: boolean = false;

    @HostListener('window:resize', [])
    onResize() {
          this.checkIfMobile();
        }

    checkIfMobile() {
      this.isMobileView = window.innerWidth <= 440;
    }

    // Dynamically get a style for vnpt-select on apn-simlist
    getBoxSelectStyle(): {[key: string]: any} {
    if (this.isMobileView) {
        return {
            left: 'unset',
            right: 'unset',
            top: '52px',
            display: 'flex',
            'flex-wrap': 'wrap',
            width: '80vw',
        };
    } else {
        return {
            left: 'unset',
            right: 'unset',
            top: 'unset',
            };
        }
    }
    constructor(
                @Inject(ApnSimService) private apnSimService: ApnSimService,
                @Inject(CustomerService) private customerService: CustomerService,
                @Inject(SimService) private simService: SimService,
                private injector: Injector,
                private formBuilder: FormBuilder) {
        super(injector);
    }
    ngOnInit(): void {
        let me = this;
        this.selectItems = [];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.items =[{label: this.tranService.translate("global.menu.apnsim")}, {label: this.tranService.translate("global.menu.apnsimlist")}]
        this.searchInfo = {
            msisdn: null,
            deviceImei: null,
            status: null,
            customer: null,
            contractDateFrom: null,
            contractDateTo: null,
            apnId: null,
        };
        this.formSearchSim = this.formBuilder.group(this.searchInfo);
        this.pageNumber = 0;
        this.pageSize= 10;
        this.sort = "msisdn,asc"
        this.dataSet ={
            content: [],
            total: 0
        }
        this.searchInfoStandard = {
            msisdn: null,
            deviceImei: null,
            status: null,
            customer: null,
            contractDateFrom: null,
            contractDateTo: null,
            apnId: null,
        };
        this.detailSim = {};

        this.statuSims = [
            {
                value: [CONSTANTS.SIM_STATUS.ACTIVATED],
                name: this.tranService.translate("sim.status.activated")
            },
            {
                value: [CONSTANTS.SIM_STATUS.INACTIVED],
                name: this.tranService.translate("sim.status.inactivated")
            },
            {
                value: [CONSTANTS.SIM_STATUS.DEACTIVATED],
                name: this.tranService.translate("sim.status.deactivated")
            },
            {
                value: [CONSTANTS.SIM_STATUS.PURGED],
                name: this.tranService.translate("sim.status.purged")
            },
            {
                value: [15 + CONSTANTS.SIM_STATUS.ACTIVATED, 15 + CONSTANTS.SIM_STATUS.READY],
                name: this.tranService.translate("sim.status.processingChangePlan")
            },
            {
                value: [10 + CONSTANTS.SIM_STATUS.ACTIVATED, 10 + CONSTANTS.SIM_STATUS.READY],
                name: this.tranService.translate("sim.status.processingRegisterPlan")
            },
            {
                value: [20 + CONSTANTS.SIM_STATUS.ACTIVATED, 20 + CONSTANTS.SIM_STATUS.READY],
                name: this.tranService.translate("sim.status.waitingCancelPlan")
            },
        ]
        this.columns = [{
            name: this.tranService.translate("sim.label.sothuebao"),
            key: "msisdn",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true,
            style:{
                cursor: "pointer",
             color: "var(--mainColorText)"
            },
            funcClick(id, item) {
                me.simId = id.toString();
                me.getDetailSim();
                me.isShowPopupDetailSim = true;
            },
        }, {
            name: this.tranService.translate("sim.label.rangeIp"),
            key: "ip",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("sim.label.maapn"),
            key: "apnId",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true,
            style:{
                cursor: "pointer",
             color: "var(--mainColorText)"
            },
            funcClick(id, item) {
                me.apnId = id;
                me.getDetailApnSim();
                me.isShowPopupDetailAPNSim = true;
            },
        },
            {
            name: this.tranService.translate("sim.label.imeiDevice"),
            key: "deviceImei",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("sim.label.khachhang"),
            key: "customerName",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("sim.label.mahopdong"),
            key: "contractCode",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("sim.label.ngaylamhopdong"),
            key: "contractDate",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true,
            funcConvertText:(value)=>{
                return me.utilService.convertDateToString(new Date(value));
            }
        }, {
            name: this.tranService.translate("sim.label.trangthaisim"),
            key: "status",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true,
            funcGetClassname: (value) => {
                if(value == 0){
                    return ['p-1' , "border-round", "border-400", "text-color","inline-block"];
                }else if(value == CONSTANTS.SIM_STATUS.READY){
                    // return ['p-1', "bg-blue-600", "border-round","inline-block"];
                    return ['p-2', "text-green-800", "bg-green-100","border-round","inline-block"];
                }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
                    return ['p-2', 'text-green-800', "bg-green-100","border-round","inline-block"];
                }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
                    return ['p-2', 'text-yellow-800', "bg-yellow-100", "border-round","inline-block"];
                }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
                    return ['p-2', 'text-indigo-600', "bg-indigo-100", "border-round","inline-block"];
                }else if(value == CONSTANTS.SIM_STATUS.PURGED){
                    return ['p-2', 'text-red-700', "bg-red-100", "border-round","inline-block"];
                }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
                    return ['p-2', 'text-cyan-800', "bg-cyan-100", "border-round","inline-block"];
                }else if(value == 10 + CONSTANTS.SIM_STATUS.DEACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.INACTIVED){
                    return ['p-2', 'text-teal-800', "bg-teal-100", "border-round","inline-block"];
                }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
                    return ['p-2', 'text-orange-700', "bg-orange-100", "border-round","inline-block"];
                }
                return [];
            },
            funcConvertText: (value)=>{
                if(value == 0){
                    return me.tranService.translate("sim.status.inventory");
                }else if(value == CONSTANTS.SIM_STATUS.READY){
                    // return me.tranService.translate("sim.status.ready");
                    return me.tranService.translate("sim.status.activated");
                }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
                    return me.tranService.translate("sim.status.activated");
                }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
                    return me.tranService.translate("sim.status.deactivated");
                }else if(value == CONSTANTS.SIM_STATUS.PURGED){
                    return me.tranService.translate("sim.status.purged");
                }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
                    return me.tranService.translate("sim.status.inactivated");
                }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){
                    return this.tranService.translate("sim.status.processingChangePlan");
                }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
                    return this.tranService.translate("sim.status.processingRegisterPlan");
                }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
                    return this.tranService.translate("sim.status.waitingCancelPlan");
                }
                return "";
            },
            style:{
                color: "white"
            }
        }
        ]
        this.optionTable = {
            hasClearSelected:false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: true,
        }
        this.pageNumber = 0;
        this.pageSize= 10;
        this.sort = "msisdn,asc"

        this.dataSet ={
            content: [],
            total: 0
        }
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
        this.checkIfMobile();
    }

    onChangeDateFrom(value){
        if(value){
            this.minDateTo = value;
        }else{
            this.minDateTo = null
        }
    }
    onChangeDateTo(value){
        if(value){
            this.maxDateFrom = value;
        }else{
            this.maxDateFrom = new Date();
        }
    }
    onSubmitSearch(){
        let me = this;
        this.pageNumber = 0;
        this.search(0, this.pageSize, this.sort, this.searchInfo);
    }
    updateParams(dataParams){
        Object.keys(this.searchInfo).forEach(key => {
            if(this.searchInfo[key] != null){
                if(key == "contractDateFrom"){
                    dataParams["contractDateFrom"] = this.searchInfo.contractDateFrom.getTime();
                }else if(key == "contractDateTo"){
                    dataParams["contractDateTo"] = this.searchInfo.contractDateTo.getTime();
                }else{
                    dataParams[key] = this.searchInfo[key];
                }
            }
        })
    }
    search(page, limit, sort, params){
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let me = this;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        this.updateParams(dataParams);
        me.messageCommonService.onload();
        this.apnSimService.search(dataParams, (response)=>{
            me.dataSet = {
                content: response.content,
                total: response.totalElements,
            }
            me.searchInfoStandard = {...me.searchInfo}
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    };

    getNameStatus(value){
        if(value == 0){
            return this.tranService.translate("sim.status.inventory");
        }else if(value == CONSTANTS.SIM_STATUS.READY){
            // return this.tranService.translate("sim.status.ready");
            return this.tranService.translate("sim.status.activated");
        }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
            return this.tranService.translate("sim.status.activated");
        }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
            return this.tranService.translate("sim.status.deactivated");
        }else if(value == CONSTANTS.SIM_STATUS.PURGED){
            return this.tranService.translate("sim.status.purged");
        }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
            return this.tranService.translate("sim.status.inactivated");
        }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){
            return this.tranService.translate("sim.status.processingChangePlan");
        }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
            return this.tranService.translate("sim.status.processingRegisterPlan");
        }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
            return this.tranService.translate("sim.status.waitingCancelPlan");
        }
        return "";
    };

    getClassStatus(value){
        if(value == 0){
            return ['p-1' , "border-round", "border-400", "text-color","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.READY){
            // return ['p-1', "bg-blue-600", "border-round","inline-block"];
            return ['p-2', "text-green-800", "bg-green-100","border-round","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
            return ['p-2', 'text-green-800', "bg-green-100","border-round","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
            return ['p-2', 'text-yellow-800', "bg-yellow-100", "border-round","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
            return ['p-2', 'text-indigo-600', "bg-indigo-100", "border-round","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.PURGED){
            return ['p-2', 'text-red-700', "bg-red-100", "border-round","inline-block"];
        }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
            return ['p-2', 'text-cyan-800', "bg-cyan-100", "border-round","inline-block"];
        }else if(value == 10 + CONSTANTS.SIM_STATUS.DEACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.INACTIVED){
            return ['p-2', 'text-teal-800', "bg-teal-100", "border-round","inline-block"];
        }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
            return ['p-2', 'text-orange-700', "bg-orange-100", "border-round","inline-block"];
        }
        return [];
    };

    getServiceType(value) {
        if(value == CONSTANTS.SERVICE_TYPE.PREPAID) return this.tranService.translate("sim.serviceType.prepaid")
        else if(value == CONSTANTS.SERVICE_TYPE.POSTPAID) return this.tranService.translate("sim.serviceType.postpaid")
        else return ""
    };

    getDetailSim(){
        let me = this;
        // this.messageCommonService.onload();
        me.simService.getById(me.simId, (response)=>{
            me.detailSim = {
                ...response
            }
            me.getStatusSim();
            me.getDetailCustomer();
            me.getDetailRatingPlan();
            me.getDetailContract();
            me.getDetailApn();
            me.simService.getConnectionStatus([me.simId], (resp)=>{
                me.detailSim.connectionStatus = resp[0].userstate
            }, ()=>{})
        }, null,()=>{
            this.messageCommonService.offload();
        })
    }

    getStatusSim(){
        let me = this;
        this.simService.getDetailStatus(this.detailSim.msisdn, (response)=>{
            me.detailStatusSim =  {
                statusData: response.gprsStatus == 1,
                statusReceiveCall: response.icStatus == 1,
                statusSendCall: response.ocStatus == 1,
                statusWorldCall: response.iddStatus == 1,
                statusReceiveSms: response.smtStatus == 1,
                statusSendSms: response.smoStatus == 1
            };
        },()=>{})
    }

    getDetailCustomer(){
        this.detailCustomer = {
            name: this.detailSim.customerName,
            code: this.detailSim.customerCode
        }
    }

    getDetailRatingPlan(){
        this.simService.getDetailPlanSim(this.detailSim.msisdn, (response)=>{
            this.detailRatingPlan = {
                ...response
            }
        }, ()=>{})

    }

    getDetailContract(){
        this.simService.getDetailContract(this.utilService.stringToStrBase64(this.detailSim.contractCode), (response)=>{
            this.detailContract = response;
        }, ()=>{})
    }

    getDetailApn(){
        this.detailAPN = {
            code: this.detailSim.apnCode,
            type: "Kết nối bằng 3G",
            ip: 0,
            rangeIp: this.detailSim.ip
        }
    }

    getTitle(key: string): string {
        switch (key) {
            case 'imsi':
                return this.tranService.translate("sim.label.imsi");
            case 'vpnChannelName':
                return this.tranService.translate("sim.label.vpnchannelname");
            case 'pdpcp':
                return this.tranService.translate("sim.label.pdpcp");
            case 'epsProfileId':
                return this.tranService.translate("sim.label.epsprofileid");
            case 'msisdn':
                return this.tranService.translate("sim.label.sothuebao");
            case 'ipType':
                return this.tranService.translate("sim.label.iptype");
            case 'ip':
                return this.tranService.translate("sim.label.ip");
            case 'apnId':
                return this.tranService.translate("sim.label.maapn");
            case 'apnStatus':
                return this.tranService.translate("account.label.status");
            case 'deviceImei':
                return this.tranService.translate("sim.label.imeiDevice");
            case 'customerName':
                return this.tranService.translate("sim.label.khachhang");
            case 'contractCode':
                return this.tranService.translate("sim.label.mahopdong");
            case 'contractDate':
                return this.tranService.translate("sim.label.ngaylamhopdong");
            case 'status':
                return this.tranService.translate("sim.label.trangthaisim");
            case 'note':
                return this.tranService.translate("sim.label.note");
            default:
                return key;
        }
    }

    getContent(key: string): any {
        switch (key) {
            case 'status':
            {
                let value = Number(this.detailApnSim[key]);
                if(value == 0){
                    return this.tranService.translate("sim.status.inventory");
                }else if(value == CONSTANTS.SIM_STATUS.READY){
                    // return this.tranService.translate("sim.status.ready");
                    return this.tranService.translate("sim.status.activated");
                }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
                    return this.tranService.translate("sim.status.activated");
                }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
                    return this.tranService.translate("sim.status.deactivated");
                }else if(value == CONSTANTS.SIM_STATUS.PURGED){
                    return this.tranService.translate("sim.status.purged");
                }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
                    return this.tranService.translate("sim.status.inactivated");
                }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){
                    return this.tranService.translate("sim.status.processingChangePlan");
                }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
                    return this.tranService.translate("sim.status.processingRegisterPlan");
                }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
                    return this.tranService.translate("sim.status.waitingCancelPlan");
                }
                return "";
            }
                break;
            case 'ipType':
            {
                if (Number(this.detailApnSim[key]) == CONSTANTS.IP_TYPE.STATIC) {
                    return this.tranService.translate("sim.label.staticIp");
                } else if(Number(this.detailApnSim[key]) == CONSTANTS.IP_TYPE.DYNAMIC) {
                    return this.tranService.translate("sim.label.dynamicIp");
                } else {
                    return this.detailApnSim[key];
                }
            }
                break;
            case 'contractDate':
                return this.utilService.convertDateToString(new Date(this.detailApnSim[key]));
            default:
                return this.detailApnSim[key];
        }
    };


    getDetailApnSim() {
        this.apnSimService.detail(this.apnId, (response)=>{
            this.detailApnSim = response;
        })
    }

    ngAfterContentChecked(): void {
    }
}
