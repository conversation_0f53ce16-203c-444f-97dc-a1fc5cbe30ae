{"ast": null, "code": "import { PermissionService } from \"src/app/service/permission/permission.service\";\nimport { ComponentBase } from \"../../component.base\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"primeng/breadcrumb\";\nimport * as i3 from \"primeng/inputtext\";\nimport * as i4 from \"primeng/button\";\nimport * as i5 from \"../common-module/table/table.component\";\nimport * as i6 from \"primeng/dropdown\";\nimport * as i7 from \"primeng/panel\";\nimport * as i8 from \"src/app/service/permission/permission.service\";\nexport class PermissionListComponent extends ComponentBase {\n  constructor(permissionService, formBuilder, injector) {\n    super(injector);\n    this.permissionService = permissionService;\n    this.formBuilder = formBuilder;\n    this.injector = injector;\n    this.listOriginData = [];\n  }\n  ngOnInit() {\n    let me = this;\n    this.items = [{\n      label: this.tranService.translate(\"global.menu.accountmgmt\")\n    }, {\n      label: this.tranService.translate(\"global.menu.listpermission\")\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.searchInfo = {\n      objectKey: null,\n      permissionName: null\n    };\n    this.formSearchPermission = this.formBuilder.group(this.searchInfo);\n    this.selectItems = [];\n    this.pageNumber = 0;\n    this.pageSize = 10;\n    this.sort = \"objectName,asc\", this.optionTable = {\n      hasClearSelected: true,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false\n    };\n    this.columns = [{\n      name: this.tranService.translate(\"account.label.permission.name\"),\n      key: \"permissionName\",\n      size: \"55%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"account.label.permission.object\"),\n      key: \"objectName\",\n      size: \"45%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }];\n    this.prepareData();\n  }\n  onSubmitSearch() {\n    this.pageNumber = 0;\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n  }\n  prepareData() {\n    this.listObjectKey = [];\n    let me = this;\n    this.messageCommonService.onload();\n    this.permissionService.search({}, this.handleData.bind(this), null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  handleData(response) {\n    let me = this;\n    let listObjKey = [];\n    this.listOriginData = response.map(el => {\n      if (!listObjKey.includes(el.objectKey)) {\n        listObjKey.push(el.objectKey);\n        this.listObjectKey.push({\n          name: me.tranService.translate(`permission.${el.objectKey}.${el.objectKey}`, null, el.objectKey),\n          value: el.objectKey\n        });\n      }\n      return {\n        ...el,\n        objectName: me.tranService.translate(`permission.${el.objectKey}.${el.objectKey}`, null, el.objectKey),\n        permissionName: me.tranService.translate(`permission.${el.objectKey}.${el.permissionKey}`, null, el.description)\n      };\n    });\n    this.listObjectKey = this.listObjectKey.sort((a, b) => me.convertTextViToEnUpperCase(a.name) > me.convertTextViToEnUpperCase(b.name) ? 1 : -1);\n    // console.log(this.listObjectKey);\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n  }\n  search(page, size, sort, params) {\n    let me = this;\n    this.messageCommonService.onload();\n    setTimeout(function () {\n      me.messageCommonService.offload();\n    }, 500);\n    this.pageNumber = page;\n    this.pageSize = size;\n    this.sort = sort;\n    this.listOriginData = this.listOriginData.sort((a, b) => {\n      let fieldSort = me.sort.split(',')[0];\n      let direction = me.sort.split(',')[1];\n      return a[fieldSort].toUpperCase().localeCompare(b[fieldSort].toUpperCase()) == 1 ? direction == 'asc' ? 1 : -1 : direction == 'asc' ? -1 : 1;\n    });\n    this.listFilter = this.listOriginData.filter(el => {\n      let okObjectKey = me.searchInfo.objectKey == null || el.objectKey == me.searchInfo.objectKey;\n      let okPermissionName = me.searchInfo.permissionName == null || me.searchInfo.permissionName.trim() == '' || this.checkSearchEnVi(el.permissionName, me.searchInfo.permissionName);\n      return okObjectKey && okPermissionName;\n    });\n    this.dataSet = {\n      content: this.listFilter.slice(page * size, page * size + size),\n      total: this.listFilter.length\n    };\n  }\n  convertTextViToEnUpperCase(text) {\n    text = text.toUpperCase();\n    let result = \"\";\n    for (let i = 0; i < text.length; i++) {\n      if ([\"A\", \"Á\", \"À\", \"Ã\", \"Ả\", \"Ạ\", \"Ă\", \"Ắ\", \"Ằ\", \"Ẳ\", \"Ẵ\", \"Ặ\", \"Â\", \"Ấ\", \"Ầ\", \"Ẩ\", \"Ẫ\", \"Ậ\"].includes(text.charAt(i).toString())) {\n        result += \"A\";\n      } else if ([\"E\", \"Ê\", \"È\", \"Ề\", \"É\", \"Ế\", \"Ẻ\", \"Ể\", \"Ẽ\", \"Ễ\", \"Ẹ\", \"Ệ\"].includes(text.charAt(i).toString())) {\n        result += \"E\";\n      } else if ([\"I\", \"Ì\", \"Í\", \"Ỉ\", \"Ĩ\", \"Ị\"].includes(text.charAt(i).toString())) {\n        result += \"I\";\n      } else if ([\"O\", \"Ô\", \"Ơ\", \"Ò\", \"Ồ\", \"Ờ\", \"Ó\", \"Ố\", \"Ớ\", \"Ỏ\", \"Ổ\", \"Ở\", \"Õ\", \"Ỗ\", \"Ỡ\", \"Ọ\", \"Ộ\", \"Ợ\"].includes(text.charAt(i).toString())) {\n        result += \"O\";\n      } else if ([\"U\", \"Ư\", \"Ù\", \"Ừ\", \"Ú\", \"Ứ\", \"Ủ\", \"Ử\", \"Ũ\", \"Ữ\", \"Ụ\", \"Ự\"].includes(text.charAt(i).toString())) {\n        result += \"U\";\n      } else if ([\"Y\", \"Ỳ\", \"Ý\", \"Ỷ\", \"Ỹ\", \"Ỵ\"].includes(text.charAt(i).toString())) {\n        result += \"Y\";\n      } else if ([\"D\", \"Đ\"].includes(text.charAt(i).toString())) {\n        result += \"D\";\n      } else {\n        result += text.charAt(i).toString();\n      }\n    }\n    return result;\n  }\n  checkSearchEnVi(text, search) {\n    search = search.toUpperCase();\n    if (this.tranService.lang == 'vi') {\n      let searchVi = this.convertTextViToEnUpperCase(search);\n      if (searchVi == search) {\n        return this.convertTextViToEnUpperCase(text).indexOf(searchVi) >= 0;\n      } else {\n        return text.toUpperCase().indexOf(search) >= 0;\n      }\n    } else {\n      return text.toUpperCase().indexOf(search) >= 0;\n    }\n  }\n  static {\n    this.ɵfac = function PermissionListComponent_Factory(t) {\n      return new (t || PermissionListComponent)(i0.ɵɵdirectiveInject(PermissionService), i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PermissionListComponent,\n      selectors: [[\"app-permission-list\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 21,\n      vars: 24,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"pb-2\", \"pt-3\", \"vnpt-field-set\", 3, \"formGroup\", \"ngSubmit\"], [3, \"toggleable\", \"header\"], [1, \"grid\", \"search-grid-3\"], [1, \"col-3\"], [1, \"p-float-label\"], [\"styleClass\", \"w-full\", \"id\", \"objectKey\", \"formControlName\", \"objectKey\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"ngModelChange\"], [\"for\", \"type\", 1, \"label-dropdown\"], [\"pInputText\", \"\", \"id\", \"permissionName\", \"formControlName\", \"permissionName\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"permissionName\"], [1, \"col-3\", \"pb-0\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"submit\"], [3, \"fieldId\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"pageNumber\", \"pageSize\", \"sort\", \"params\", \"labelTable\", \"selectItemsChange\"]],\n      template: function PermissionListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"form\", 4);\n          i0.ɵɵlistener(\"ngSubmit\", function PermissionListComponent_Template_form_ngSubmit_5_listener() {\n            return ctx.onSubmitSearch();\n          });\n          i0.ɵɵelementStart(6, \"p-panel\", 5)(7, \"div\", 6)(8, \"div\", 7)(9, \"span\", 8)(10, \"p-dropdown\", 9);\n          i0.ɵɵlistener(\"ngModelChange\", function PermissionListComponent_Template_p_dropdown_ngModelChange_10_listener($event) {\n            return ctx.searchInfo.objectKey = $event;\n          })(\"ngModelChange\", function PermissionListComponent_Template_p_dropdown_ngModelChange_10_listener() {\n            return ctx.onSubmitSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"label\", 10);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 7)(14, \"span\", 8)(15, \"input\", 11);\n          i0.ɵɵlistener(\"ngModelChange\", function PermissionListComponent_Template_input_ngModelChange_15_listener($event) {\n            return ctx.searchInfo.permissionName = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"label\", 12);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 13);\n          i0.ɵɵelement(19, \"p-button\", 14);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(20, \"table-vnpt\", 15);\n          i0.ɵɵlistener(\"selectItemsChange\", function PermissionListComponent_Template_table_vnpt_selectItemsChange_20_listener($event) {\n            return ctx.selectItems = $event;\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.menu.listpermission\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formSearchPermission);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"toggleable\", true)(\"header\", ctx.tranService.translate(\"global.text.filter\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.searchInfo.objectKey)(\"options\", ctx.listObjectKey);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.label.permission.object\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.permissionName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.label.permission.name\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"fieldId\", \"id\")(\"selectItems\", ctx.selectItems)(\"columns\", ctx.columns)(\"dataSet\", ctx.dataSet)(\"options\", ctx.optionTable)(\"loadData\", ctx.search.bind(ctx))(\"pageNumber\", ctx.pageNumber)(\"pageSize\", ctx.pageSize)(\"sort\", ctx.sort)(\"params\", ctx.searchInfo)(\"labelTable\", ctx.tranService.translate(\"global.menu.listpermission\"));\n        }\n      },\n      dependencies: [i2.Breadcrumb, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.InputText, i4.Button, i5.TableVnptComponent, i6.Dropdown, i7.Panel]\n    });\n  }\n}", "map": {"version": 3, "names": ["PermissionService", "ComponentBase", "PermissionListComponent", "constructor", "permissionService", "formBuilder", "injector", "listOriginData", "ngOnInit", "me", "items", "label", "tranService", "translate", "home", "icon", "routerLink", "searchInfo", "object<PERSON>ey", "permissionName", "formSearchPermission", "group", "selectItems", "pageNumber", "pageSize", "sort", "optionTable", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "columns", "name", "key", "size", "align", "isShow", "isSort", "prepareData", "onSubmitSearch", "search", "listObjectKey", "messageCommonService", "onload", "handleData", "bind", "offload", "response", "listObjKey", "map", "el", "includes", "push", "value", "objectName", "<PERSON><PERSON><PERSON>", "description", "a", "b", "convertTextViToEnUpperCase", "page", "params", "setTimeout", "fieldSort", "split", "direction", "toUpperCase", "localeCompare", "listFilter", "filter", "okObjectKey", "okPermissionName", "trim", "checkSearchEnVi", "dataSet", "content", "slice", "total", "length", "text", "result", "i", "char<PERSON>t", "toString", "lang", "searchVi", "indexOf", "i0", "ɵɵdirectiveInject", "i1", "FormBuilder", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "PermissionListComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "PermissionListComponent_Template_form_ngSubmit_5_listener", "PermissionListComponent_Template_p_dropdown_ngModelChange_10_listener", "$event", "PermissionListComponent_Template_input_ngModelChange_15_listener", "PermissionListComponent_Template_table_vnpt_selectItemsChange_20_listener", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\permission\\app.permisstion.list.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\permission\\app.permission.list.component.html"], "sourcesContent": ["import {Component, Inject, Injector, OnInit} from \"@angular/core\";\r\nimport { FormBuilder } from \"@angular/forms\";\r\nimport { ActivatedRoute, Router } from \"@angular/router\";\r\nimport { MenuItem } from \"primeng/api\";\r\nimport { MessageCommonService } from \"src/app/service/comon/message-common.service\";\r\nimport { TranslateService } from \"src/app/service/comon/translate.service\";\r\nimport { UtilService } from \"src/app/service/comon/util.service\";\r\nimport { PermissionService } from \"src/app/service/permission/permission.service\";\r\nimport { ColumnInfo, OptionTable } from \"../common-module/table/table.component\";\r\nimport {ComponentBase} from \"../../component.base\";\r\n\r\n@Component({\r\n    selector: \"app-permission-list\",\r\n    templateUrl: \"./app.permission.list.component.html\"\r\n})\r\nexport class PermissionListComponent extends ComponentBase implements OnInit{\r\n    searchInfo: {\r\n        objectKey: string | null,\r\n        permissionName: string | null,\r\n    }\r\n    listObjectKey: Array<any>;\r\n    items: MenuItem[];\r\n    home: MenuItem;\r\n    selectItems: Array<any>;\r\n    columns: Array<ColumnInfo>;\r\n    dataSet: {\r\n        content: Array<any>,\r\n        total: number\r\n    };\r\n    optionTable: OptionTable;\r\n    pageNumber: number;\r\n    pageSize: number;\r\n    sort: string;\r\n    formSearchPermission: any;\r\n    listOriginData: Array<any> = [];\r\n    listFilter: Array<any>;\r\n    constructor(\r\n                @Inject(PermissionService) private permissionService: PermissionService,\r\n                private formBuilder: FormBuilder,\r\n                private injector: Injector)\r\n    {\r\n        super(injector);\r\n    }\r\n\r\n    ngOnInit(): void {\r\n        let me = this;\r\n        this.items = [{ label: this.tranService.translate(\"global.menu.accountmgmt\") }, { label: this.tranService.translate(\"global.menu.listpermission\") }];\r\n        this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n        this.searchInfo = {\r\n            objectKey: null,\r\n            permissionName: null,\r\n        }\r\n\r\n        this.formSearchPermission = this.formBuilder.group(this.searchInfo);\r\n        this.selectItems = [];\r\n        this.pageNumber = 0;\r\n        this.pageSize = 10;\r\n        this.sort = \"objectName,asc\",\r\n\r\n        this.optionTable = {\r\n            hasClearSelected: true,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false\r\n        }\r\n        this.columns = [\r\n            {\r\n                name: this.tranService.translate(\"account.label.permission.name\"),\r\n                key: \"permissionName\",\r\n                size: \"55%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"account.label.permission.object\"),\r\n                key: \"objectName\",\r\n                size: \"45%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n            },\r\n        ]\r\n        this.prepareData();\r\n    }\r\n\r\n    onSubmitSearch(){\r\n        this.pageNumber = 0;\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n    }\r\n\r\n    prepareData(){\r\n        this.listObjectKey = [];\r\n        let me = this;\r\n        this.messageCommonService.onload();\r\n        this.permissionService.search({}, this.handleData.bind(this), null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    handleData(response){\r\n        let me = this;\r\n        let listObjKey = [];\r\n        this.listOriginData = response.map(el => {\r\n            if(!listObjKey.includes(el.objectKey)){\r\n                listObjKey.push(el.objectKey);\r\n                this.listObjectKey.push({\r\n                    name: me.tranService.translate(`permission.${el.objectKey}.${el.objectKey}`, null, el.objectKey),\r\n                    value: el.objectKey\r\n                })\r\n            }\r\n            return {\r\n                ...el,\r\n                objectName: me.tranService.translate(`permission.${el.objectKey}.${el.objectKey}`, null, el.objectKey),\r\n                permissionName: me.tranService.translate(`permission.${el.objectKey}.${el.permissionKey}`, null, el.description)\r\n            }\r\n        });\r\n        this.listObjectKey = this.listObjectKey.sort((a,b) => me.convertTextViToEnUpperCase(a.name) > me.convertTextViToEnUpperCase(b.name) ? 1 : -1);\r\n        // console.log(this.listObjectKey);\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n    }\r\n\r\n    search(page, size, sort, params){\r\n        let me = this;\r\n        this.messageCommonService.onload();\r\n        setTimeout(function(){\r\n            me.messageCommonService.offload();\r\n        }, 500);\r\n        this.pageNumber = page;\r\n        this.pageSize = size;\r\n        this.sort = sort;\r\n        this.listOriginData = this.listOriginData.sort((a, b) =>{\r\n            let fieldSort = me.sort.split(',')[0];\r\n            let direction = me.sort.split(',')[1];\r\n            return a[fieldSort].toUpperCase().localeCompare(b[fieldSort].toUpperCase()) == 1  ? ((direction == 'asc')?1:-1) : ((direction == 'asc')?-1:1)\r\n        });\r\n        this.listFilter = this.listOriginData.filter(el => {\r\n            let okObjectKey = me.searchInfo.objectKey == null || (el.objectKey == me.searchInfo.objectKey);\r\n            let okPermissionName = me.searchInfo.permissionName == null || me.searchInfo.permissionName.trim() == '' || this.checkSearchEnVi(el.permissionName, me.searchInfo.permissionName);\r\n            return okObjectKey && okPermissionName;\r\n        })\r\n        this.dataSet = {\r\n            content: this.listFilter.slice(page*size, page*size+size),\r\n            total: this.listFilter.length\r\n        }\r\n    }\r\n    convertTextViToEnUpperCase(text:string):string{\r\n        text = text.toUpperCase();\r\n        let result = \"\";\r\n        for(let i = 0;i<text.length;i++){\r\n            if([\"A\",\"Á\",\"À\",\"Ã\",\"Ả\",\"Ạ\",\"Ă\",\"Ắ\",\"Ằ\",\"Ẳ\",\"Ẵ\",\"Ặ\",\"Â\",\"Ấ\",\"Ầ\",\"Ẩ\",\"Ẫ\",\"Ậ\"].includes(text.charAt(i).toString())){\r\n                result += \"A\";\r\n            }else if([\"E\",\"Ê\",\"È\",\"Ề\",\"É\",\"Ế\",\"Ẻ\",\"Ể\",\"Ẽ\",\"Ễ\",\"Ẹ\",\"Ệ\"].includes(text.charAt(i).toString())){\r\n                result += \"E\"\r\n            }else if([\"I\",\"Ì\",\"Í\",\"Ỉ\",\"Ĩ\",\"Ị\"].includes(text.charAt(i).toString())){\r\n                result += \"I\"\r\n            }else if([\"O\",\"Ô\",\"Ơ\",\"Ò\",\"Ồ\",\"Ờ\",\"Ó\",\"Ố\",\"Ớ\",\"Ỏ\",\"Ổ\",\"Ở\",\"Õ\",\"Ỗ\",\"Ỡ\",\"Ọ\",\"Ộ\",\"Ợ\"].includes(text.charAt(i).toString())){\r\n                result += \"O\"\r\n            }else if([\"U\",\"Ư\",\"Ù\",\"Ừ\",\"Ú\",\"Ứ\",\"Ủ\",\"Ử\",\"Ũ\",\"Ữ\",\"Ụ\",\"Ự\"].includes(text.charAt(i).toString())){\r\n                result += \"U\"\r\n            }else if([\"Y\",\"Ỳ\",\"Ý\",\"Ỷ\",\"Ỹ\",\"Ỵ\"].includes(text.charAt(i).toString())){\r\n                result += \"Y\"\r\n            }else if([\"D\",\"Đ\"].includes(text.charAt(i).toString())){\r\n                result += \"D\";\r\n            }else{\r\n                result += text.charAt(i).toString();\r\n            }\r\n        }\r\n        return result;\r\n    }\r\n    checkSearchEnVi(text, search){\r\n        search = search.toUpperCase();\r\n        if(this.tranService.lang == 'vi'){\r\n            let searchVi = this.convertTextViToEnUpperCase(search);\r\n            if(searchVi == search){\r\n                return this.convertTextViToEnUpperCase(text).indexOf(searchVi) >= 0;\r\n            }else{\r\n                return text.toUpperCase().indexOf(search) >= 0;\r\n            }\r\n        }else{\r\n            return text.toUpperCase().indexOf(search) >= 0;\r\n        }\r\n    }\r\n}\r\n", "<style>\r\n    /* .col-3{\r\n        padding: 10px;\r\n    } */\r\n</style>\r\n\r\n<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{this.tranService.translate(\"global.menu.listpermission\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n<!--    <div class=\"col-5 flex flex-row justify-content-end align-items-center\">-->\r\n\r\n<!--    </div>-->\r\n</div>\r\n\r\n<form [formGroup]=\"formSearchPermission\" (ngSubmit)=\"onSubmitSearch()\" class=\"pb-2 pt-3 vnpt-field-set\">\r\n    <p-panel [toggleable]=\"true\" [header]=\"tranService.translate('global.text.filter')\">\r\n        <div class=\"grid search-grid-3\">\r\n            <!-- doi tuong tac dong -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\" [showClear]=\"true\"\r\n                            id=\"objectKey\" [autoDisplayFirst]=\"false\"\r\n                            [(ngModel)]=\"searchInfo.objectKey\"\r\n                            formControlName=\"objectKey\"\r\n                            [options]=\"listObjectKey\"\r\n                            optionLabel=\"name\"\r\n                            optionValue=\"value\"\r\n                            (ngModelChange)=\"onSubmitSearch()\"\r\n                    ></p-dropdown>\r\n                    <label class=\"label-dropdown\" for=\"type\">{{tranService.translate(\"account.label.permission.object\")}}</label>\r\n                </span>\r\n            </div>\r\n             <!-- ten quyen -->\r\n             <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                            pInputText id=\"permissionName\"\r\n                            [(ngModel)]=\"searchInfo.permissionName\"\r\n                            formControlName=\"permissionName\"\r\n                    />\r\n                    <label htmlFor=\"permissionName\">{{tranService.translate(\"account.label.permission.name\")}}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-3 pb-0\">\r\n                <p-button icon=\"pi pi-search\"\r\n                            styleClass=\"p-button-rounded p-button-secondary p-button-text button-search\"\r\n                            type=\"submit\"\r\n                ></p-button>\r\n            </div>\r\n        </div>\r\n    </p-panel>\r\n</form>\r\n\r\n<table-vnpt\r\n    [fieldId]=\"'id'\"\r\n    [(selectItems)]=\"selectItems\"\r\n    [columns]=\"columns\"\r\n    [dataSet]=\"dataSet\"\r\n    [options]=\"optionTable\"\r\n    [loadData]=\"search.bind(this)\"\r\n    [pageNumber]=\"pageNumber\"\r\n    [pageSize]=\"pageSize\"\r\n    [sort]=\"sort\"\r\n    [params]=\"searchInfo\"\r\n    [labelTable]=\"this.tranService.translate('global.menu.listpermission')\"\r\n></table-vnpt>\r\n"], "mappings": "AAOA,SAASA,iBAAiB,QAAQ,+CAA+C;AAEjF,SAAQC,aAAa,QAAO,sBAAsB;;;;;;;;;;AAMlD,OAAM,MAAOC,uBAAwB,SAAQD,aAAa;EAqBtDE,YAC+CC,iBAAoC,EAC/DC,WAAwB,EACxBC,QAAkB;IAElC,KAAK,CAACA,QAAQ,CAAC;IAJ4B,KAAAF,iBAAiB,GAAjBA,iBAAiB;IAC5C,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAL5B,KAAAC,cAAc,GAAe,EAAE;EAQ/B;EAEAC,QAAQA,CAAA;IACJ,IAAIC,EAAE,GAAG,IAAI;IACb,IAAI,CAACC,KAAK,GAAG,CAAC;MAAEC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,yBAAyB;IAAC,CAAE,EAAE;MAAEF,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,4BAA4B;IAAC,CAAE,CAAC;IACpJ,IAAI,CAACC,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;IACnD,IAAI,CAACC,UAAU,GAAG;MACdC,SAAS,EAAE,IAAI;MACfC,cAAc,EAAE;KACnB;IAED,IAAI,CAACC,oBAAoB,GAAG,IAAI,CAACf,WAAW,CAACgB,KAAK,CAAC,IAAI,CAACJ,UAAU,CAAC;IACnE,IAAI,CAACK,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,IAAI,GAAG,gBAAgB,EAE5B,IAAI,CAACC,WAAW,GAAG;MACfC,gBAAgB,EAAE,IAAI;MACtBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;IACD,IAAI,CAACC,OAAO,GAAG,CACX;MACIC,IAAI,EAAE,IAAI,CAACpB,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;MACjEoB,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIL,IAAI,EAAE,IAAI,CAACpB,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;MACnEoB,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IACD,IAAI,CAACC,WAAW,EAAE;EACtB;EAEAC,cAAcA,CAAA;IACV,IAAI,CAAChB,UAAU,GAAG,CAAC;IACnB,IAAI,CAACiB,MAAM,CAAC,IAAI,CAACjB,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACR,UAAU,CAAC;EAC3E;EAEAqB,WAAWA,CAAA;IACP,IAAI,CAACG,aAAa,GAAG,EAAE;IACvB,IAAIhC,EAAE,GAAG,IAAI;IACb,IAAI,CAACiC,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAI,CAACvC,iBAAiB,CAACoC,MAAM,CAAC,EAAE,EAAE,IAAI,CAACI,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAI;MACpEpC,EAAE,CAACiC,oBAAoB,CAACI,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAF,UAAUA,CAACG,QAAQ;IACf,IAAItC,EAAE,GAAG,IAAI;IACb,IAAIuC,UAAU,GAAG,EAAE;IACnB,IAAI,CAACzC,cAAc,GAAGwC,QAAQ,CAACE,GAAG,CAACC,EAAE,IAAG;MACpC,IAAG,CAACF,UAAU,CAACG,QAAQ,CAACD,EAAE,CAAChC,SAAS,CAAC,EAAC;QAClC8B,UAAU,CAACI,IAAI,CAACF,EAAE,CAAChC,SAAS,CAAC;QAC7B,IAAI,CAACuB,aAAa,CAACW,IAAI,CAAC;UACpBpB,IAAI,EAAEvB,EAAE,CAACG,WAAW,CAACC,SAAS,CAAC,cAAcqC,EAAE,CAAChC,SAAS,IAAIgC,EAAE,CAAChC,SAAS,EAAE,EAAE,IAAI,EAAEgC,EAAE,CAAChC,SAAS,CAAC;UAChGmC,KAAK,EAAEH,EAAE,CAAChC;SACb,CAAC;;MAEN,OAAO;QACH,GAAGgC,EAAE;QACLI,UAAU,EAAE7C,EAAE,CAACG,WAAW,CAACC,SAAS,CAAC,cAAcqC,EAAE,CAAChC,SAAS,IAAIgC,EAAE,CAAChC,SAAS,EAAE,EAAE,IAAI,EAAEgC,EAAE,CAAChC,SAAS,CAAC;QACtGC,cAAc,EAAEV,EAAE,CAACG,WAAW,CAACC,SAAS,CAAC,cAAcqC,EAAE,CAAChC,SAAS,IAAIgC,EAAE,CAACK,aAAa,EAAE,EAAE,IAAI,EAAEL,EAAE,CAACM,WAAW;OAClH;IACL,CAAC,CAAC;IACF,IAAI,CAACf,aAAa,GAAG,IAAI,CAACA,aAAa,CAAChB,IAAI,CAAC,CAACgC,CAAC,EAACC,CAAC,KAAKjD,EAAE,CAACkD,0BAA0B,CAACF,CAAC,CAACzB,IAAI,CAAC,GAAGvB,EAAE,CAACkD,0BAA0B,CAACD,CAAC,CAAC1B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7I;IACA,IAAI,CAACQ,MAAM,CAAC,IAAI,CAACjB,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACR,UAAU,CAAC;EAC3E;EAEAuB,MAAMA,CAACoB,IAAI,EAAE1B,IAAI,EAAET,IAAI,EAAEoC,MAAM;IAC3B,IAAIpD,EAAE,GAAG,IAAI;IACb,IAAI,CAACiC,oBAAoB,CAACC,MAAM,EAAE;IAClCmB,UAAU,CAAC;MACPrD,EAAE,CAACiC,oBAAoB,CAACI,OAAO,EAAE;IACrC,CAAC,EAAE,GAAG,CAAC;IACP,IAAI,CAACvB,UAAU,GAAGqC,IAAI;IACtB,IAAI,CAACpC,QAAQ,GAAGU,IAAI;IACpB,IAAI,CAACT,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAClB,cAAc,GAAG,IAAI,CAACA,cAAc,CAACkB,IAAI,CAAC,CAACgC,CAAC,EAAEC,CAAC,KAAI;MACpD,IAAIK,SAAS,GAAGtD,EAAE,CAACgB,IAAI,CAACuC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACrC,IAAIC,SAAS,GAAGxD,EAAE,CAACgB,IAAI,CAACuC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACrC,OAAOP,CAAC,CAACM,SAAS,CAAC,CAACG,WAAW,EAAE,CAACC,aAAa,CAACT,CAAC,CAACK,SAAS,CAAC,CAACG,WAAW,EAAE,CAAC,IAAI,CAAC,GAAMD,SAAS,IAAI,KAAK,GAAE,CAAC,GAAC,CAAC,CAAC,GAAMA,SAAS,IAAI,KAAK,GAAE,CAAC,CAAC,GAAC,CAAE;IACjJ,CAAC,CAAC;IACF,IAAI,CAACG,UAAU,GAAG,IAAI,CAAC7D,cAAc,CAAC8D,MAAM,CAACnB,EAAE,IAAG;MAC9C,IAAIoB,WAAW,GAAG7D,EAAE,CAACQ,UAAU,CAACC,SAAS,IAAI,IAAI,IAAKgC,EAAE,CAAChC,SAAS,IAAIT,EAAE,CAACQ,UAAU,CAACC,SAAU;MAC9F,IAAIqD,gBAAgB,GAAG9D,EAAE,CAACQ,UAAU,CAACE,cAAc,IAAI,IAAI,IAAIV,EAAE,CAACQ,UAAU,CAACE,cAAc,CAACqD,IAAI,EAAE,IAAI,EAAE,IAAI,IAAI,CAACC,eAAe,CAACvB,EAAE,CAAC/B,cAAc,EAAEV,EAAE,CAACQ,UAAU,CAACE,cAAc,CAAC;MACjL,OAAOmD,WAAW,IAAIC,gBAAgB;IAC1C,CAAC,CAAC;IACF,IAAI,CAACG,OAAO,GAAG;MACXC,OAAO,EAAE,IAAI,CAACP,UAAU,CAACQ,KAAK,CAAChB,IAAI,GAAC1B,IAAI,EAAE0B,IAAI,GAAC1B,IAAI,GAACA,IAAI,CAAC;MACzD2C,KAAK,EAAE,IAAI,CAACT,UAAU,CAACU;KAC1B;EACL;EACAnB,0BAA0BA,CAACoB,IAAW;IAClCA,IAAI,GAAGA,IAAI,CAACb,WAAW,EAAE;IACzB,IAAIc,MAAM,GAAG,EAAE;IACf,KAAI,IAAIC,CAAC,GAAG,CAAC,EAACA,CAAC,GAACF,IAAI,CAACD,MAAM,EAACG,CAAC,EAAE,EAAC;MAC5B,IAAG,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,CAAC9B,QAAQ,CAAC4B,IAAI,CAACG,MAAM,CAACD,CAAC,CAAC,CAACE,QAAQ,EAAE,CAAC,EAAC;QAC7GH,MAAM,IAAI,GAAG;OAChB,MAAK,IAAG,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,CAAC7B,QAAQ,CAAC4B,IAAI,CAACG,MAAM,CAACD,CAAC,CAAC,CAACE,QAAQ,EAAE,CAAC,EAAC;QAC3FH,MAAM,IAAI,GAAG;OAChB,MAAK,IAAG,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,CAAC7B,QAAQ,CAAC4B,IAAI,CAACG,MAAM,CAACD,CAAC,CAAC,CAACE,QAAQ,EAAE,CAAC,EAAC;QACnEH,MAAM,IAAI,GAAG;OAChB,MAAK,IAAG,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,CAAC7B,QAAQ,CAAC4B,IAAI,CAACG,MAAM,CAACD,CAAC,CAAC,CAACE,QAAQ,EAAE,CAAC,EAAC;QACnHH,MAAM,IAAI,GAAG;OAChB,MAAK,IAAG,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,CAAC7B,QAAQ,CAAC4B,IAAI,CAACG,MAAM,CAACD,CAAC,CAAC,CAACE,QAAQ,EAAE,CAAC,EAAC;QAC3FH,MAAM,IAAI,GAAG;OAChB,MAAK,IAAG,CAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,CAAC,CAAC7B,QAAQ,CAAC4B,IAAI,CAACG,MAAM,CAACD,CAAC,CAAC,CAACE,QAAQ,EAAE,CAAC,EAAC;QACnEH,MAAM,IAAI,GAAG;OAChB,MAAK,IAAG,CAAC,GAAG,EAAC,GAAG,CAAC,CAAC7B,QAAQ,CAAC4B,IAAI,CAACG,MAAM,CAACD,CAAC,CAAC,CAACE,QAAQ,EAAE,CAAC,EAAC;QACnDH,MAAM,IAAI,GAAG;OAChB,MAAI;QACDA,MAAM,IAAID,IAAI,CAACG,MAAM,CAACD,CAAC,CAAC,CAACE,QAAQ,EAAE;;;IAG3C,OAAOH,MAAM;EACjB;EACAP,eAAeA,CAACM,IAAI,EAAEvC,MAAM;IACxBA,MAAM,GAAGA,MAAM,CAAC0B,WAAW,EAAE;IAC7B,IAAG,IAAI,CAACtD,WAAW,CAACwE,IAAI,IAAI,IAAI,EAAC;MAC7B,IAAIC,QAAQ,GAAG,IAAI,CAAC1B,0BAA0B,CAACnB,MAAM,CAAC;MACtD,IAAG6C,QAAQ,IAAI7C,MAAM,EAAC;QAClB,OAAO,IAAI,CAACmB,0BAA0B,CAACoB,IAAI,CAAC,CAACO,OAAO,CAACD,QAAQ,CAAC,IAAI,CAAC;OACtE,MAAI;QACD,OAAON,IAAI,CAACb,WAAW,EAAE,CAACoB,OAAO,CAAC9C,MAAM,CAAC,IAAI,CAAC;;KAErD,MAAI;MACD,OAAOuC,IAAI,CAACb,WAAW,EAAE,CAACoB,OAAO,CAAC9C,MAAM,CAAC,IAAI,CAAC;;EAEtD;;;uBAvKStC,uBAAuB,EAAAqF,EAAA,CAAAC,iBAAA,CAsBZxF,iBAAiB,GAAAuF,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,QAAA;IAAA;EAAA;;;YAtB5BzF,uBAAuB;MAAA0F,SAAA;MAAAC,QAAA,GAAAN,EAAA,CAAAO,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTpCb,EAAA,CAAAe,cAAA,aAAqG;UAEzDf,EAAA,CAAAgB,MAAA,GAA4D;UAAAhB,EAAA,CAAAiB,YAAA,EAAM;UACtGjB,EAAA,CAAAkB,SAAA,sBAAoF;UACxFlB,EAAA,CAAAiB,YAAA,EAAM;UAMVjB,EAAA,CAAAe,cAAA,cAAwG;UAA/Df,EAAA,CAAAmB,UAAA,sBAAAC,0DAAA;YAAA,OAAYN,GAAA,CAAA9D,cAAA,EAAgB;UAAA,EAAC;UAClEgD,EAAA,CAAAe,cAAA,iBAAoF;UAO5Df,EAAA,CAAAmB,UAAA,2BAAAE,sEAAAC,MAAA;YAAA,OAAAR,GAAA,CAAApF,UAAA,CAAAC,SAAA,GAAA2F,MAAA;UAAA,EAAkC,2BAAAD,sEAAA;YAAA,OAKjBP,GAAA,CAAA9D,cAAA,EAAgB;UAAA,EALC;UAMzCgD,EAAA,CAAAiB,YAAA,EAAa;UACdjB,EAAA,CAAAe,cAAA,iBAAyC;UAAAf,EAAA,CAAAgB,MAAA,IAA4D;UAAAhB,EAAA,CAAAiB,YAAA,EAAQ;UAIpHjB,EAAA,CAAAe,cAAA,cAAmB;UAIJf,EAAA,CAAAmB,UAAA,2BAAAI,iEAAAD,MAAA;YAAA,OAAAR,GAAA,CAAApF,UAAA,CAAAE,cAAA,GAAA0F,MAAA;UAAA,EAAuC;UAF/CtB,EAAA,CAAAiB,YAAA,EAIE;UACFjB,EAAA,CAAAe,cAAA,iBAAgC;UAAAf,EAAA,CAAAgB,MAAA,IAA0D;UAAAhB,EAAA,CAAAiB,YAAA,EAAQ;UAG1GjB,EAAA,CAAAe,cAAA,eAAwB;UACpBf,EAAA,CAAAkB,SAAA,oBAGY;UAChBlB,EAAA,CAAAiB,YAAA,EAAM;UAKlBjB,EAAA,CAAAe,cAAA,sBAYC;UAVGf,EAAA,CAAAmB,UAAA,+BAAAK,0EAAAF,MAAA;YAAA,OAAAR,GAAA,CAAA/E,WAAA,GAAAuF,MAAA;UAAA,EAA6B;UAUhCtB,EAAA,CAAAiB,YAAA,EAAa;;;UA3D8BjB,EAAA,CAAAyB,SAAA,GAA4D;UAA5DzB,EAAA,CAAA0B,iBAAA,CAAAZ,GAAA,CAAAzF,WAAA,CAAAC,SAAA,+BAA4D;UACzD0E,EAAA,CAAAyB,SAAA,GAAe;UAAfzB,EAAA,CAAA2B,UAAA,UAAAb,GAAA,CAAA3F,KAAA,CAAe,SAAA2F,GAAA,CAAAvF,IAAA;UAOxDyE,EAAA,CAAAyB,SAAA,GAAkC;UAAlCzB,EAAA,CAAA2B,UAAA,cAAAb,GAAA,CAAAjF,oBAAA,CAAkC;UAC3BmE,EAAA,CAAAyB,SAAA,GAAmB;UAAnBzB,EAAA,CAAA2B,UAAA,oBAAmB,WAAAb,GAAA,CAAAzF,WAAA,CAAAC,SAAA;UAKoB0E,EAAA,CAAAyB,SAAA,GAAkB;UAAlBzB,EAAA,CAAA2B,UAAA,mBAAkB,uCAAAb,GAAA,CAAApF,UAAA,CAAAC,SAAA,aAAAmF,GAAA,CAAA5D,aAAA;UAST8C,EAAA,CAAAyB,SAAA,GAA4D;UAA5DzB,EAAA,CAAA0B,iBAAA,CAAAZ,GAAA,CAAAzF,WAAA,CAAAC,SAAA,oCAA4D;UAQ7F0E,EAAA,CAAAyB,SAAA,GAAuC;UAAvCzB,EAAA,CAAA2B,UAAA,YAAAb,GAAA,CAAApF,UAAA,CAAAE,cAAA,CAAuC;UAGfoE,EAAA,CAAAyB,SAAA,GAA0D;UAA1DzB,EAAA,CAAA0B,iBAAA,CAAAZ,GAAA,CAAAzF,WAAA,CAAAC,SAAA,kCAA0D;UAc1G0E,EAAA,CAAAyB,SAAA,GAAgB;UAAhBzB,EAAA,CAAA2B,UAAA,iBAAgB,gBAAAb,GAAA,CAAA/E,WAAA,aAAA+E,GAAA,CAAAtE,OAAA,aAAAsE,GAAA,CAAA3B,OAAA,aAAA2B,GAAA,CAAA3E,WAAA,cAAA2E,GAAA,CAAA7D,MAAA,CAAAK,IAAA,CAAAwD,GAAA,iBAAAA,GAAA,CAAA9E,UAAA,cAAA8E,GAAA,CAAA7E,QAAA,UAAA6E,GAAA,CAAA5E,IAAA,YAAA4E,GAAA,CAAApF,UAAA,gBAAAoF,GAAA,CAAAzF,WAAA,CAAAC,SAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}