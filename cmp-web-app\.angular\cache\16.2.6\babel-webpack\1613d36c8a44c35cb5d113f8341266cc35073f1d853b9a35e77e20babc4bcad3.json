{"ast": null, "code": "import { ComponentBase } from \"../../../../component.base\";\nimport { TicketService } from \"../../../../service/ticket/TicketService\";\nimport { AccountService } from \"../../../../service/account/AccountService\";\nimport { Validators } from \"@angular/forms\";\nimport { CONSTANTS } from \"../../../../service/comon/constants\";\nimport { SimTicketService } from \"../../../../service/ticket/SimTicketService\";\nimport { LogHandleTicketService } from \"../../../../service/ticket/LogHandleTicketService\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/breadcrumb\";\nimport * as i4 from \"primeng/tooltip\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/inputtext\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"../../../common-module/table/table.component\";\nimport * as i9 from \"../../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i10 from \"primeng/calendar\";\nimport * as i11 from \"primeng/dropdown\";\nimport * as i12 from \"primeng/card\";\nimport * as i13 from \"primeng/dialog\";\nimport * as i14 from \"primeng/inputtextarea\";\nimport * as i15 from \"primeng/panel\";\nimport * as i16 from \"primeng/inputnumber\";\nimport * as i17 from \"primeng/table\";\nimport * as i18 from \"../../../../service/ticket/TicketService\";\nimport * as i19 from \"../../../../service/account/AccountService\";\nimport * as i20 from \"../../../../service/ticket/SimTicketService\";\nimport * as i21 from \"../../../../service/ticket/LogHandleTicketService\";\nfunction ListOrderSimTicketComponent_p_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 108);\n    i0.ɵɵlistener(\"click\", function ListOrderSimTicketComponent_p_button_6_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.showModalCreate());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"global.button.create\"));\n  }\n}\nfunction ListOrderSimTicketComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"span\", 11)(2, \"p-dropdown\", 109);\n    i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_div_10_Template_p_dropdown_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.searchInfo.provinceCode = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 110);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"showClear\", true)(\"filter\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx_r1.searchInfo.provinceCode)(\"required\", false)(\"options\", ctx_r1.listProvince);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.tranService.translate(\"account.label.province\"));\n  }\n}\nfunction ListOrderSimTicketComponent_table_vnpt_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"table-vnpt\", 111);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"tableId\", \"tableTicketConfigList\")(\"fieldId\", \"provinceCode\")(\"columns\", ctx_r2.columns)(\"dataSet\", ctx_r2.dataSet)(\"options\", ctx_r2.optionTable)(\"pageNumber\", ctx_r2.pageNumber)(\"loadData\", ctx_r2.search.bind(ctx_r2))(\"pageSize\", ctx_r2.pageSize)(\"sort\", ctx_r2.sort)(\"params\", ctx_r2.searchInfo)(\"labelTable\", ctx_r2.tranService.translate(\"ticket.menu.requestList\"));\n  }\n}\nfunction ListOrderSimTicketComponent_table_vnpt_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"table-vnpt\", 111);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"tableId\", \"tableTicketConfigList\")(\"fieldId\", \"provinceCode\")(\"columns\", ctx_r3.columns)(\"dataSet\", ctx_r3.dataSet)(\"options\", ctx_r3.optionTable)(\"pageNumber\", ctx_r3.pageNumber)(\"loadData\", ctx_r3.search.bind(ctx_r3))(\"pageSize\", ctx_r3.pageSize)(\"sort\", ctx_r3.sort)(\"params\", ctx_r3.searchInfo)(\"labelTable\", ctx_r3.tranService.translate(\"ticket.menu.requestList\"));\n  }\n}\nfunction ListOrderSimTicketComponent_small_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.tranService.translate(\"global.message.required\"));\n  }\n}\nconst _c0 = function () {\n  return {\n    len: 255\n  };\n};\nfunction ListOrderSimTicketComponent_small_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListOrderSimTicketComponent_small_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r6.tranService.translate(\"global.message.formatContainVN\"));\n  }\n}\nfunction ListOrderSimTicketComponent_small_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListOrderSimTicketComponent_small_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r8.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListOrderSimTicketComponent_small_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r9.tranService.translate(\"global.message.invalidEmail\"));\n  }\n}\nfunction ListOrderSimTicketComponent_small_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r10.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListOrderSimTicketComponent_small_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r11.tranService.translate(\"ticket.message.invalidPhone\"));\n  }\n}\nfunction ListOrderSimTicketComponent_small_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r12.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListOrderSimTicketComponent_small_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r13.tranService.translate(\"ticket.message.maxQuantity\"));\n  }\n}\nfunction ListOrderSimTicketComponent_small_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r14.tranService.translate(\"ticket.message.minQuantity\"));\n  }\n}\nfunction ListOrderSimTicketComponent_small_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r15.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListOrderSimTicketComponent_small_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r16.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListOrderSimTicketComponent_small_113_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r17.tranService.translate(\"global.message.formatCode\"));\n  }\n}\nfunction ListOrderSimTicketComponent_small_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r18.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListOrderSimTicketComponent_small_123_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r19.tranService.translate(\"global.message.formatCode\"));\n  }\n}\nfunction ListOrderSimTicketComponent_small_132_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r20.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListOrderSimTicketComponent_small_133_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r21.tranService.translate(\"global.message.formatCode\"));\n  }\n}\nfunction ListOrderSimTicketComponent_p_button_136_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-button\", 112);\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r22.formTicketSim.invalid || ctx_r22.typeRequest == \"create\" && (ctx_r22.errorMinQuantity || !ctx_r22.selectedProvince || !ctx_r22.selectedDistrict || !ctx_r22.selectedCommune || ctx_r22.errorMaxQuantity))(\"label\", ctx_r22.tranService.translate(\"global.button.save\"));\n  }\n}\nfunction ListOrderSimTicketComponent_div_142_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 113)(1, \"label\", 30);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 31);\n    i0.ɵɵtext(4, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 32)(6, \"p-dropdown\", 114);\n    i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_div_142_Template_p_dropdown_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r43);\n      const ctx_r42 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r42.ticket.provinceCode = $event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r23.tranService.translate(\"account.label.province\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"showClear\", false)(\"filter\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx_r23.ticket.provinceCode)(\"options\", ctx_r23.listProvince)(\"disabled\", true)(\"readonly\", true);\n  }\n}\nconst _c1 = function () {\n  return [\"p-2\", \"text-white\", \"bg-cyan-300\", \"border-round\", \"inline-block\"];\n};\nfunction ListOrderSimTicketComponent_span_175_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r24.getValueStatus(ctx_r24.ticket.statusOld));\n  }\n}\nconst _c2 = function () {\n  return [\"p-2\", \"text-white\", \"bg-bluegray-500\", \"border-round\", \"inline-block\"];\n};\nfunction ListOrderSimTicketComponent_span_176_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c2));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r25.getValueStatus(ctx_r25.ticket.statusOld));\n  }\n}\nconst _c3 = function () {\n  return [\"p-2\", \"text-white\", \"bg-orange-400\", \"border-round\", \"inline-block\"];\n};\nfunction ListOrderSimTicketComponent_span_177_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c3));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r26.getValueStatus(ctx_r26.ticket.statusOld));\n  }\n}\nconst _c4 = function () {\n  return [\"p-2\", \"text-white\", \"bg-red-500\", \"border-round\", \"inline-block\"];\n};\nfunction ListOrderSimTicketComponent_span_178_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c4));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r27.getValueStatus(ctx_r27.ticket.statusOld));\n  }\n}\nconst _c5 = function () {\n  return [\"p-2\", \"text-white\", \"bg-green-500\", \"border-round\", \"inline-block\"];\n};\nfunction ListOrderSimTicketComponent_span_179_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c5));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r28.getValueStatus(ctx_r28.ticket.statusOld));\n  }\n}\nfunction ListOrderSimTicketComponent_small_183_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r29.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListOrderSimTicketComponent_div_184_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ListOrderSimTicketComponent_div_184_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"label\", 115);\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, ListOrderSimTicketComponent_div_184_small_3_Template, 2, 0, \"small\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 32)(5, \"textarea\", 116);\n    i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_div_184_Template_textarea_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r45 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r45.ticket.cause = $event);\n    })(\"input\", function ListOrderSimTicketComponent_div_184_Template_textarea_input_5_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.checkVisibleAndRequired());\n    })(\"keydown\", function ListOrderSimTicketComponent_div_184_Template_textarea_keydown_5_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r48 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r48.onKeyDownCause($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r30.tranService.translate(\"ticket.label.processingNotes\"), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r30.ticket.status != null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"autoResize\", false)(\"ngModel\", ctx_r30.ticket.cause)(\"maxlength\", 255)(\"required\", ctx_r30.isRequiredNote)(\"readonly\", ctx_r30.typeRequest == \"view\")(\"placeholder\", ctx_r30.tranService.translate(\"ticket.label.processingNotes\"));\n  }\n}\nfunction ListOrderSimTicketComponent_small_188_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r31.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListOrderSimTicketComponent_small_189_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r32.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListOrderSimTicketComponent_small_224_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r33 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r33.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListOrderSimTicketComponent_div_228_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 122);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 106);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 106);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 106);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r49 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r49.tranService.translate(\"global.text.stt\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r49.tranService.translate(\"account.text.account\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r49.tranService.translate(\"global.button.changeStatus\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r49.tranService.translate(\"account.label.time\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r49.tranService.translate(\"ticket.label.content\"));\n  }\n}\nfunction ListOrderSimTicketComponent_div_228_ng_template_9_input_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r59 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 126);\n    i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_div_228_ng_template_9_input_11_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const note_r51 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(note_r51.content = $event);\n    })(\"keydown\", function ListOrderSimTicketComponent_div_228_ng_template_9_input_11_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const note_r51 = i0.ɵɵnextContext().$implicit;\n      const ctx_r60 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r60.onKeyDownNoteContent($event, note_r51));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const note_r51 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", note_r51.content)(\"required\", true)(\"maxLength\", 255);\n  }\n}\nfunction ListOrderSimTicketComponent_div_228_ng_template_9_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 127);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const note_r51 = i0.ɵɵnextContext().$implicit;\n    const ctx_r54 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"pTooltip\", note_r51.content);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r54.getValueNote(note_r51.content));\n  }\n}\nfunction ListOrderSimTicketComponent_div_228_ng_template_9_small_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r55 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r55.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListOrderSimTicketComponent_div_228_ng_template_9_small_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r56 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r56.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListOrderSimTicketComponent_div_228_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 123)(1, \"td\", 122);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 106);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 106);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\", 106);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtemplate(11, ListOrderSimTicketComponent_div_228_ng_template_9_input_11_Template, 1, 3, \"input\", 124);\n    i0.ɵɵtemplate(12, ListOrderSimTicketComponent_div_228_ng_template_9_span_12_Template, 2, 2, \"span\", 125);\n    i0.ɵɵtemplate(13, ListOrderSimTicketComponent_div_228_ng_template_9_small_13_Template, 2, 1, \"small\", 36);\n    i0.ɵɵtemplate(14, ListOrderSimTicketComponent_div_228_ng_template_9_small_14_Template, 2, 2, \"small\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const note_r51 = ctx.$implicit;\n    const i_r52 = ctx.rowIndex;\n    const ctx_r50 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r50.mapForm[note_r51.id]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r52 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(note_r51.userName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r50.getValueStatus(note_r51.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 9, note_r51.createdDate, \"HH:mm:ss dd/MM/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r50.typeRequest == \"update\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r50.typeRequest == \"view\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r50.mapForm[note_r51.id].controls.content.dirty && (ctx_r50.mapForm[note_r51.id].controls.content.errors == null ? null : ctx_r50.mapForm[note_r51.id].controls.content.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r50.mapForm[note_r51.id].controls.content.errors == null ? null : ctx_r50.mapForm[note_r51.id].controls.content.errors.maxLength);\n  }\n}\nfunction ListOrderSimTicketComponent_div_228_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 29)(2, \"label\", 117)(3, \"b\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"div\", 118)(6, \"div\")(7, \"p-table\", 119);\n    i0.ɵɵtemplate(8, ListOrderSimTicketComponent_div_228_ng_template_8_Template, 11, 5, \"ng-template\", 120);\n    i0.ɵɵtemplate(9, ListOrderSimTicketComponent_div_228_ng_template_9_Template, 15, 12, \"ng-template\", 121);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r34.tranService.translate(\"ticket.label.listNotes\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", ctx_r34.listNotes);\n  }\n}\nfunction ListOrderSimTicketComponent_div_236_ng_template_3_th_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r66 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r66.tranService.translate(\"global.text.action\"));\n  }\n}\nfunction ListOrderSimTicketComponent_div_236_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ListOrderSimTicketComponent_div_236_ng_template_3_th_5_Template, 2, 1, \"th\", 84);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r64 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r64.tranService.translate(\"global.text.stt\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r64.tranService.translate(\"ticket.label.imsi\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r64.typeRequest == \"update\");\n  }\n}\nfunction ListOrderSimTicketComponent_div_236_ng_template_4_td_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r72 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\")(1, \"p-button\", 130);\n    i0.ɵɵlistener(\"click\", function ListOrderSimTicketComponent_div_236_ng_template_4_td_5_Template_p_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r72);\n      const i_r68 = i0.ɵɵnextContext().rowIndex;\n      const ctx_r70 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r70.removeImsi(i_r68));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r69 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"hidden\", !ctx_r69.isShowImsiInput);\n  }\n}\nfunction ListOrderSimTicketComponent_div_236_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ListOrderSimTicketComponent_div_236_ng_template_4_td_5_Template, 2, 1, \"td\", 84);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r67 = ctx.$implicit;\n    const i_r68 = ctx.rowIndex;\n    const ctx_r65 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r68 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r67);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r65.typeRequest == \"update\");\n  }\n}\nfunction ListOrderSimTicketComponent_div_236_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 128)(1, \"div\", 129)(2, \"p-table\", 119);\n    i0.ɵɵtemplate(3, ListOrderSimTicketComponent_div_236_ng_template_3_Template, 6, 3, \"ng-template\", 120);\n    i0.ɵɵtemplate(4, ListOrderSimTicketComponent_div_236_ng_template_4_Template, 6, 3, \"ng-template\", 121);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r35 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r35.listImsis);\n  }\n}\nfunction ListOrderSimTicketComponent_small_245_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r36 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r36.getFirstErrorMessage());\n  }\n}\nfunction ListOrderSimTicketComponent_div_246_p_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-button\", 132);\n  }\n  if (rf & 2) {\n    const ctx_r73 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r73.formUpdateOrderSim.invalid || !ctx_r73.isEnableButtonSave || ctx_r73.isEmptyListImsi || ctx_r73.listNotes.length > 0 && !ctx_r73.isFormValid())(\"hidden\", ctx_r73.typeRequest == \"view\")(\"label\", ctx_r73.tranService.translate(\"global.button.save\"));\n  }\n}\nconst _c6 = function (a0) {\n  return [a0];\n};\nfunction ListOrderSimTicketComponent_div_246_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r75 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"p-button\", 59);\n    i0.ɵɵlistener(\"click\", function ListOrderSimTicketComponent_div_246_Template_p_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r75);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.isShowUpdateRequest = false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, ListOrderSimTicketComponent_div_246_p_button_2_Template, 1, 3, \"p-button\", 131);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r37 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", ctx_r37.tranService.translate(\"global.button.cancel\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r37.checkAuthen(i0.ɵɵpureFunction1(2, _c6, ctx_r37.CONSTANTS.PERMISSIONS.TICKET.UPDATE)));\n  }\n}\nconst _c7 = function () {\n  return {\n    width: \"700px\"\n  };\n};\nconst _c8 = function () {\n  return {\n    width: \"1000px\"\n  };\n};\nconst _c9 = function () {\n  return {\n    type: 3\n  };\n};\nconst _c10 = function () {\n  return {\n    standalone: true\n  };\n};\nconst _c11 = function () {\n  return {\n    width: \"50rem\"\n  };\n};\nconst _c12 = function () {\n  return {\n    \"1199px\": \"75vw\",\n    \"575px\": \"90vw\"\n  };\n};\nexport class ListOrderSimTicketComponent extends ComponentBase {\n  constructor(ticketService, accountService, simTicketService, logHandleTicketService, cdr, formBuilder, injector) {\n    super(injector);\n    this.ticketService = ticketService;\n    this.accountService = accountService;\n    this.simTicketService = simTicketService;\n    this.logHandleTicketService = logHandleTicketService;\n    this.cdr = cdr;\n    this.formBuilder = formBuilder;\n    this.injector = injector;\n    this.maxDateFrom = new Date();\n    this.minDateTo = null;\n    this.maxDateTo = new Date();\n    this.selectedProvince = false;\n    this.selectedDistrict = false;\n    this.selectedCommune = false;\n    this.disableSelectDistrict = true;\n    this.disableSelectCommune = true;\n    this.listImsis = [];\n    this.mapForm = {};\n    this.changeTable = false;\n    this.CONSTANTS = CONSTANTS;\n  }\n  ngOnInit() {\n    let me = this;\n    me.changeTable = false;\n    this.userInfo = this.sessionService.userInfo;\n    this.searchHistoryOrder = {\n      fromDate: null,\n      toDate: null\n    };\n    this.formSearchHistoryOrder = this.formBuilder.group(this.searchHistoryOrder);\n    this.isShowCreateRequest = false;\n    this.isShowUpdateRequest = false;\n    this.isEnableButtonSave = false;\n    this.typeRequest = 'create';\n    this.userType = CONSTANTS.USER_TYPE;\n    this.listImsis = [];\n    this.listNotes = [];\n    this.newImsi = '';\n    this.ticket = {\n      id: null,\n      contactName: null,\n      contactEmail: null,\n      contactPhone: null,\n      content: null,\n      note: null,\n      cause: null,\n      type: CONSTANTS.REQUEST_TYPE.ORDER_SIM,\n      status: null,\n      statusOld: null,\n      assigneeId: null,\n      address: null,\n      quantity: null,\n      commune: null,\n      district: null,\n      province: null,\n      detailAddress: null,\n      createdBy: null,\n      provinceCode: null\n    };\n    this.formMailInput = this.formBuilder.group({\n      imsi: \"\"\n    });\n    this.listTicketType = [{\n      label: this.tranService.translate('ticket.type.orderSim'),\n      value: 2\n    }], this.listTicketStatusSelected = [];\n    this.mapTicketStatus = {\n      0: [{\n        label: me.tranService.translate('ticket.status.received'),\n        value: 1\n      }, {\n        label: me.tranService.translate('ticket.status.reject'),\n        value: 3\n      }],\n      1: [{\n        label: me.tranService.translate('ticket.status.inProgress'),\n        value: 2\n      }, {\n        label: me.tranService.translate('ticket.status.reject'),\n        value: 3\n      }],\n      2: [{\n        label: me.tranService.translate('ticket.status.done'),\n        value: 4\n      }, {\n        label: me.tranService.translate('ticket.status.reject'),\n        value: 3\n      }]\n    };\n    this.isShowStatus = true;\n    this.isRequiredStatus = true;\n    this.isShowNote = true;\n    this.isRequiredNote = true;\n    this.isShowAssignee = true;\n    this.isRequiredAssignee = true;\n    this.isShowListImsi = false;\n    this.isShowListNote = true;\n    this.isShowImsiInput = true;\n    this.isEmptyListImsi = false;\n    this.isEnableButtonSave = false;\n    this.isShowOrder = false;\n    this.isShowMesReqImsi = false;\n    this.isShowMesMaxLenImsi = false;\n    this.isShowMesExistImsi = false;\n    this.listTicketStatus = [{\n      label: me.tranService.translate('ticket.status.new'),\n      value: 0\n    }, {\n      label: me.tranService.translate('ticket.status.received'),\n      value: 1\n    }, {\n      label: me.tranService.translate('ticket.status.inProgress'),\n      value: 2\n    }, {\n      label: me.tranService.translate('ticket.status.reject'),\n      value: 3\n    }, {\n      label: me.tranService.translate('ticket.status.done'),\n      value: 4\n    }];\n    this.searchInfo = {\n      provinceCode: null,\n      email: null,\n      contactPhone: null,\n      contactName: null,\n      type: CONSTANTS.REQUEST_TYPE.ORDER_SIM,\n      status: null,\n      dateTo: null,\n      dateFrom: null\n    };\n    this.optionAddress = {\n      provinceCode: 1,\n      districtCode: 1\n    };\n    this.columns = [{\n      name: this.tranService.translate(\"ticket.label.province\"),\n      key: \"provinceName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: this.userInfo.type == CONSTANTS.USER_TYPE.ADMIN,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"ticket.label.customerName\"),\n      key: \"contactName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      isShowTooltip: true,\n      style: {\n        display: 'inline-block',\n        maxWidth: '350px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.email\"),\n      key: \"contactEmail\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      isShowTooltip: true,\n      style: {\n        display: 'inline-block',\n        maxWidth: '350px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.phone\"),\n      key: \"contactPhone\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"ticket.label.quantity\"),\n      key: \"quantity\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"ticket.label.deliveryAddress\"),\n      key: \"address\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      isShowTooltip: true,\n      style: {\n        display: 'inline-block',\n        maxWidth: '350px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.content\"),\n      key: \"content\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      isShowTooltip: true,\n      style: {\n        display: 'inline-block',\n        maxWidth: '350px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.createdDate\"),\n      key: \"createdDate\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        if (value == null) return null;\n        return me.utilService.convertDateToString(new Date(value));\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.updatedDate\"),\n      key: \"updatedDate\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        if (value == null) return null;\n        return me.utilService.convertDateToString(new Date(value));\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.updateBy\"),\n      key: \"updatedByName\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"ticket.label.status\"),\n      key: \"status\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcGetClassname: value => {\n        if (value == CONSTANTS.REQUEST_STATUS.NEW) {\n          return ['p-2', 'text-white', \"bg-cyan-300\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\n          return ['p-2', 'text-white', \"bg-bluegray-500\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\n          return ['p-2', 'text-white', \"bg-orange-400\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\n          return ['p-2', 'text-white', \"bg-red-500\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\n          return ['p-2', 'text-white', \"bg-green-500\", \"border-round\", \"inline-block\"];\n        }\n        return '';\n      },\n      funcConvertText: function (value) {\n        if (value == CONSTANTS.REQUEST_STATUS.NEW) {\n          return me.tranService.translate(\"ticket.status.new\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\n          return me.tranService.translate(\"ticket.status.received\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\n          return me.tranService.translate(\"ticket.status.inProgress\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\n          return me.tranService.translate(\"ticket.status.reject\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\n          return me.tranService.translate(\"ticket.status.done\");\n        }\n        return \"\";\n      }\n    }];\n    this.columnsHistory = [{\n      name: this.tranService.translate(\"ticket.label.time\"),\n      key: \"createdDate\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: false,\n      funcConvertText: function (value) {\n        return me.utilService.convertLongDateToString(value);\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.implementer\"),\n      key: \"userName\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"ticket.label.content\"),\n      key: \"status\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: false,\n      funcGetClassname: value => {\n        if (value == CONSTANTS.REQUEST_STATUS.NEW) {\n          return ['p-2', 'text-white', \"bg-cyan-300\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\n          return ['p-2', 'text-white', \"bg-bluegray-500\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\n          return ['p-2', 'text-white', \"bg-orange-400\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\n          return ['p-2', 'text-white', \"bg-red-500\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\n          return ['p-2', 'text-white', \"bg-green-500\", \"border-round\", \"inline-block\"];\n        }\n        return '';\n      },\n      funcConvertText: function (value) {\n        if (value == CONSTANTS.REQUEST_STATUS.NEW) {\n          return me.tranService.translate(\"ticket.status.new\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\n          return me.tranService.translate(\"ticket.status.received\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\n          return me.tranService.translate(\"ticket.status.inProgress\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\n          return me.tranService.translate(\"ticket.status.reject\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\n          return me.tranService.translate(\"ticket.status.done\");\n        }\n        return \"\";\n      }\n    }];\n    this.optionTableHistory = {\n      hasShowIndex: true,\n      paginator: false\n    };\n    this.dataSetHistory = {\n      content: [],\n      total: 0\n    }, this.optionTable = {\n      hasClearSelected: false,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false,\n      action: [{\n        icon: \"pi pi-info-circle\",\n        tooltip: this.tranService.translate(\"global.button.view\"),\n        func: function (id, item) {\n          me.handleRequest(id, item, 'view');\n        }\n      }, {\n        icon: \"pi pi-file-edit\",\n        tooltip: this.tranService.translate(\"global.button.edit\"),\n        func: function (id, item) {\n          me.handleRequest(id, item, 'update');\n        },\n        funcAppear: function (id, item) {\n          if (me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER || me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN) return false;\n          if (!item.updatedBy && !item.assigneeId && (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE || me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;\n          if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy !== me.userInfo.id || me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.assigneeId != null) {\n            return false;\n          }\n          if (me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;else return false;\n        }\n      }, {\n        icon: \"pi pi-eye\",\n        tooltip: this.tranService.translate(\"ticket.label.orderHistory\"),\n        func: function (id, item) {\n          me.openModalOrder(id, item);\n        }\n      }]\n    };\n    this.pageNumber = 0;\n    this.pageSize = 10;\n    this.sort = \"createdDate,desc\";\n    this.dataSet = {\n      content: [],\n      total: 0\n    }, this.formSearchTicket = this.formBuilder.group(this.searchInfo);\n    this.formTicketSim = this.formBuilder.group(this.ticket);\n    this.formUpdateOrderSim = this.formBuilder.group(this.ticket);\n    this.getListProvince();\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n    this.errorMinQuantity = false;\n    this.errorMaxQuantity = false;\n    this.listActivatedAccount = [];\n  }\n  search(page, limit, sort, params) {\n    let me = this;\n    me.changeTable = false;\n    this.pageNumber = page;\n    this.pageSize = limit;\n    this.sort = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    Object.keys(this.searchInfo).forEach(key => {\n      if (this.searchInfo[key] != null) {\n        if (key == \"dateFrom\") {\n          dataParams[\"dateFrom\"] = this.searchInfo.dateFrom.getTime();\n        } else if (key == \"dateTo\") {\n          dataParams[\"dateTo\"] = this.searchInfo.dateTo.getTime();\n        } else {\n          dataParams[key] = this.searchInfo[key];\n        }\n      }\n    });\n    this.dataSet = {\n      content: [],\n      total: 0\n    };\n    me.messageCommonService.onload();\n    this.ticketService.searchTicket(dataParams, response => {\n      me.dataSet = {\n        content: response.content,\n        total: response.totalElements\n      };\n      if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE || me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) {\n        let listAssigneeId = Array.from(new Set(me.dataSet.content.filter(item => item.assigneeId !== null).map(item => item.assigneeId)));\n        me.dataSet.content.forEach(item => {\n          if (item.updateBy !== null) {\n            listAssigneeId.push(item.updateBy);\n          }\n        });\n        const statusCheckListId = Array.from(new Set(listAssigneeId));\n        me.accountService.getListActivatedAccount(statusCheckListId, response => {\n          me.listActivatedAccount = response;\n          me.optionTable = {\n            hasClearSelected: false,\n            hasShowChoose: false,\n            hasShowIndex: true,\n            hasShowToggleColumn: false,\n            action: [{\n              icon: \"pi pi-info-circle\",\n              tooltip: this.tranService.translate(\"global.button.view\"),\n              func: function (id, item) {\n                me.handleRequest(id, item, 'view');\n              }\n            }, {\n              icon: \"pi pi-file-edit\",\n              tooltip: this.tranService.translate(\"global.button.edit\"),\n              func: function (id, item) {\n                me.handleRequest(id, item, 'update');\n              },\n              funcAppear: function (id, item) {\n                //admin và khách hàng không được sửa\n                if (me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER || me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN) return false;\n                if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE]) && (me.listActivatedAccount === undefined || me.listActivatedAccount == null)) return true;\n                if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE]) && item.assigneeId != null && me.listActivatedAccount.includes(item.assigneeId)) return false;\n                if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE]) && item.assigneeId == null && item.updatedBy != null && me.listActivatedAccount.includes(item.updatedBy) && item.updatedBy != me.userInfo.id) return false;\n                if (!item.updatedBy && !item.assigneeId && (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE || me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;\n                if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE]) && (item.assigneeId != null && !me.listActivatedAccount.includes(item.assigneeId) || item.updatedBy != null && !me.listActivatedAccount.includes(item.updatedBy))) return true;\n                if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy !== me.userInfo.id || me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.assigneeId != null) {\n                  return false;\n                }\n                if (me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;else return false;\n              }\n            }, {\n              icon: \"pi pi-eye\",\n              tooltip: this.tranService.translate(\"ticket.label.orderHistory\"),\n              func: function (id, item) {\n                me.openModalOrder(id, item);\n              }\n            }]\n          };\n          me.changeTable = true;\n        });\n      }\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  resetTicket() {\n    let me = this;\n    this.ticket = {\n      id: null,\n      contactName: null,\n      contactEmail: null,\n      contactPhone: null,\n      content: null,\n      note: null,\n      cause: null,\n      type: CONSTANTS.REQUEST_TYPE.ORDER_SIM,\n      status: null,\n      statusOld: null,\n      assigneeId: null,\n      address: null,\n      quantity: null,\n      province: null,\n      district: null,\n      commune: null,\n      detailAddress: null,\n      createdBy: null,\n      provinceCode: null\n    };\n    me.selectedProvince = false;\n    me.selectedDistrict = false;\n    me.selectedCommune = false;\n  }\n  onSubmitSearch() {\n    this.pageNumber = 0;\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n  }\n  onSubmitSearchHistory() {\n    this.searchHistory(0, 99999999, this.sort, this.searchHistoryOrder);\n  }\n  searchHistory(page, limit, sort, params) {\n    let me = this;\n    let dataParams = {\n      page: page,\n      size: limit,\n      ticketId: me.itemInTable.id\n    };\n    Object.keys(params).forEach(key => {\n      if (params[key] != null) {\n        if (key == \"fromDate\") {\n          dataParams[\"logDateFrom\"] = params.fromDate.getTime();\n        } else if (key == \"toDate\") {\n          dataParams[\"logDateTo\"] = params.toDate.getTime();\n        } else {\n          dataParams[key] = params[key];\n        }\n      }\n    });\n    me.messageCommonService.onload();\n    this.logHandleTicketService.search(dataParams, response => {\n      me.dataSetHistory = {\n        content: response.content,\n        total: response.totalElements\n      };\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  getListProvince() {\n    this.accountService.getListProvince(response => {\n      this.listProvince = response.map(el => {\n        return {\n          ...el,\n          display: `${el.code} - ${el.name}`\n        };\n      });\n    });\n  }\n  // tạo yêu cầu đặt sim\n  createOrderSim() {\n    let me = this;\n    this.messageCommonService.onload();\n    let bodySend = {\n      contactName: this.ticket.contactName,\n      contactEmail: this.ticket.contactEmail,\n      contactPhone: this.ticket.contactPhone,\n      content: this.ticket.content != null ? this.ticket.content.trim() : null,\n      address: this.ticket.detailAddress + \", \" + this.ticket.commune.name + \", \" + this.ticket.district.name + \", \" + this.ticket.province.name,\n      note: this.ticket.note != null ? this.ticket.note.trim() : null,\n      type: this.ticket.type,\n      quantity: this.ticket.quantity\n    };\n    this.ticketService.createTicket(bodySend, resp => {\n      me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n      me.isShowCreateRequest = false;\n      me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n      // get mail admin tinh\n      // me.ticketService.getListAssignee({email : '', provinceCode : this.userInfo.provinceCode, page : 0, size: 99999999}, (respAssignee)=>{\n      //     let listProvinceConfig = respAssignee.content;\n      //     let array = []\n      //     for (let user of listProvinceConfig) {\n      //         array.push({\n      //             userId: user.id,\n      //             ticketId: resp.id\n      //         })\n      //     }\n      //     me.ticketService.sendMailNotify(array);\n      // })\n      // nếu KH đc gán cho GDV thì gửi mail cho GDV và danh sách admin đc cấu hình không thì chỉ gửi mail cho danh sách admin đc cấu hình\n      // get mail admin tinh dc cau hinh\n      me.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, resp1 => {\n        let array = [];\n        for (let info of resp1.emailInfos) {\n          array.push({\n            userId: info.userId,\n            ticketId: resp.id\n          });\n        }\n        if (resp?.assigneeId) {\n          array.push({\n            userId: resp.assigneeId,\n            ticketId: resp.id\n          });\n        }\n        me.ticketService.sendMailNotify(array);\n      });\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  updateOrderSim() {\n    let me = this;\n    me.messageCommonService.onload();\n    let bodySend = {\n      contactName: this.ticket.contactName,\n      contactEmail: this.ticket.contactEmail,\n      contactPhone: this.ticket.contactPhone,\n      content: this.ticket.content,\n      address: this.ticket.address,\n      note: this.ticket.note,\n      type: this.ticket.type,\n      status: this.ticket.status,\n      cause: this.ticket.cause,\n      assigneeId: this.ticket.assigneeId,\n      quantity: this.ticket.quantity,\n      listLog: this.listNotes\n    };\n    let createSimTicketBody = {\n      ticketId: this.ticket.id,\n      userCustomerId: this.ticket.createdBy,\n      userHandleId: this.userInfo.id,\n      imsis: this.listImsis\n    };\n    // update ticket\n    this.ticketService.updateTicket(this.ticket.id, bodySend, resp => {\n      me.isShowCreateRequest = false;\n      me.isShowUpdateRequest = false;\n      // console.log(resp);\n      if (resp.assigneeId != null && resp.assigneeId != undefined) {\n        me.ticketService.sendMailNotify([{\n          userId: resp.assigneeId,\n          ticketId: resp.id\n        }]);\n      }\n      if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.IN_PROGRESS && me.ticket.status == CONSTANTS.REQUEST_STATUS.DONE) {\n        me.simTicketService.create(createSimTicketBody, res => {\n          me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n          me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n        });\n      } else {\n        me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n      }\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  showModalCreate() {\n    let me = this;\n    this.isShowCreateRequest = true;\n    this.typeRequest = 'create';\n    this.resetTicket();\n    if (this.userInfo.type === CONSTANTS.USER_TYPE.CUSTOMER) {\n      this.ticket.contactName = this.userInfo.fullName;\n      this.ticket.contactPhone = this.userInfo.phone;\n      this.ticket.contactEmail = this.userInfo.email;\n    }\n    me.selectedProvince = false;\n    me.selectedDistrict = false;\n    me.selectedCommune = false;\n    this.formTicketSim = this.formBuilder.group(this.ticket);\n  }\n  handleRequest(id, item, typeRequest) {\n    let me = this;\n    me.typeRequest = typeRequest;\n    this.isShowCreateRequest = false;\n    this.isShowUpdateRequest = true;\n    this.ticketService.getDetailTicket(item.id, resp => {\n      this.ticket = {\n        id: resp.id,\n        contactName: resp.contactName,\n        contactEmail: resp.contactEmail,\n        contactPhone: resp.contactPhone,\n        content: resp.content,\n        note: resp.note,\n        cause: resp.cause,\n        type: resp.type,\n        status: null,\n        statusOld: resp.status,\n        assigneeId: resp.assigneeId,\n        quantity: resp.quantity,\n        address: resp.address,\n        commune: null,\n        district: null,\n        province: null,\n        detailAddress: null,\n        createdBy: resp.createdBy,\n        provinceCode: resp.provinceCode\n      };\n      this.logHandleTicketService.search({\n        ticketId: this.ticket.id\n      }, res => {\n        this.listNotes = res.content;\n        // for (let note of this.listNotes) {\n        //     this.mapForm[note.id] = this.formBuilder.group(note);\n        // }\n        this.listNotes.forEach(note => {\n          this.mapForm[note.id] = this.formBuilder.group({\n            content: ['', [Validators.required, Validators.maxLength(255), this.noWhitespaceValidator()]]\n          });\n        });\n        me.initVisibleAndRequired();\n      });\n      this.simTicketService.search({\n        ticketId: this.ticket.id,\n        size: 1000\n      }, res => {\n        let imsis = [];\n        res.content.forEach(item => {\n          imsis.push(item.imsi);\n        });\n        this.listImsis = imsis;\n        me.initVisibleAndRequired();\n      });\n      this.formUpdateOrderSim = this.formBuilder.group(this.ticket);\n      me.initVisibleAndRequired();\n    });\n    this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, resp => {\n      this.listEmail = resp.emailInfos;\n    });\n  }\n  preventCharacter(event) {\n    if (event.ctrlKey) {\n      return;\n    }\n    if (event.keyCode == 8 || event.keyCode == 13 || event.keyCode == 37 || event.keyCode == 39) {\n      return;\n    }\n    // Chặn ký tự 'e', 'E' và dấu '+'\n    if (event.keyCode == 69 || event.keyCode == 101 || event.keyCode == 107 || event.keyCode == 187) {\n      event.preventDefault();\n    }\n    if (event.keyCode < 48 || event.keyCode > 57) {\n      event.preventDefault();\n    }\n  }\n  checkQuantity(event) {\n    let me = this;\n    if (event.value < 1) {\n      me.errorMinQuantity = true;\n    } else {\n      me.errorMinQuantity = false;\n    }\n    if (event.value > 99999) {\n      me.errorMaxQuantity = true;\n    } else {\n      me.errorMaxQuantity = false;\n    }\n  }\n  onSelectProvince(event) {\n    let me = this;\n    if (me.ticket.province != null) {\n      me.ticket.district = null;\n      me.ticket.commune = null;\n      me.optionAddress.provinceCode = event.code;\n      me.disableSelectDistrict = false;\n      me.disableSelectCommune = true;\n      me.selectedProvince = true;\n    } else {\n      me.optionAddress.provinceCode = 1;\n      me.ticket.district = null;\n      me.ticket.commune = null;\n      me.disableSelectDistrict = true;\n      me.disableSelectCommune = true;\n      me.selectedProvince = false;\n    }\n  }\n  onSelectDistrict(event) {\n    let me = this;\n    if (me.ticket.district != null) {\n      me.ticket.commune = null;\n      me.optionAddress.districtCode = event.code;\n      me.disableSelectCommune = false;\n      me.selectedDistrict = true;\n    } else {\n      me.ticket.commune = null;\n      me.optionAddress.districtCode = 1;\n      me.disableSelectCommune = true;\n      me.selectedDistrict = false;\n    }\n  }\n  onSelectCommune(event) {\n    let me = this;\n    if (me.ticket.commune != null) {\n      me.selectedCommune = true;\n    } else {\n      me.selectedCommune = false;\n    }\n  }\n  onChangeDateFrom(value) {\n    if (value) {\n      this.minDateTo = value;\n    } else {\n      this.minDateTo = null;\n    }\n  }\n  onChangeDateTo(value) {\n    if (value) {\n      this.maxDateFrom = value;\n    } else {\n      this.maxDateFrom = new Date();\n    }\n  }\n  addImsi() {\n    let me = this;\n    if (this.newImsi !== undefined && this.newImsi && !isNaN(Number(this.newImsi))) {\n      this.listImsis.push(Number(this.newImsi));\n      this.newImsi = '';\n    }\n    if (this.listImsis.length == 0) {\n      this.isEmptyListImsi = true;\n    } else {\n      this.isEmptyListImsi = false;\n    }\n    this.checkVisibleAndRequired();\n  }\n  removeImsi(i) {\n    let me = this;\n    this.listImsis.splice(i, 1);\n    if (this.listImsis.length == 0 && this.ticket.status == CONSTANTS.REQUEST_STATUS.DONE) {\n      this.isEmptyListImsi = true;\n    } else {\n      this.isEmptyListImsi = false;\n    }\n    this.checkVisibleAndRequired();\n  }\n  checkImsiInput() {\n    let me = this;\n    if (this.newImsi !== undefined && this.newImsi != '') {\n      if (this.newImsi.toString().length > 18) {\n        me.isShowMesMaxLenImsi = true;\n      } else {\n        me.isShowMesMaxLenImsi = false;\n      }\n      if (this.listImsis.includes(Number(this.newImsi))) {\n        me.isShowMesExistImsi = true;\n      } else {\n        me.isShowMesExistImsi = false;\n      }\n    }\n  }\n  getValueStatus(value) {\n    let me = this;\n    {\n      if (value == CONSTANTS.REQUEST_STATUS.NEW) {\n        return me.tranService.translate(\"ticket.status.new\");\n      } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\n        return me.tranService.translate(\"ticket.status.received\");\n      } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\n        return me.tranService.translate(\"ticket.status.inProgress\");\n      } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\n        return me.tranService.translate(\"ticket.status.reject\");\n      } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\n        return me.tranService.translate(\"ticket.status.done\");\n      }\n      return \"\";\n    }\n  }\n  getValueNote(content) {\n    let me = this;\n    let maxContentLength = 23; // Độ dài tối đa hiển thị nội dung\n    if (content != null) return content.length > maxContentLength ? content.slice(0, maxContentLength) + '...' : content;\n  }\n  initVisibleAndRequired() {\n    let me = this;\n    // view\n    if (me.typeRequest == 'view') {\n      me.isShowStatus = false;\n      me.isRequiredStatus = false;\n      me.isShowImsiInput = false;\n      me.isShowNote = false;\n      me.isShowAssignee = false;\n      me.isShowMesReqImsi = false;\n      me.isShowMesMaxLenImsi = false;\n      if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.DONE) {\n        me.isShowListImsi = true;\n      } else {\n        me.isShowListImsi = false;\n      }\n      if (me.userInfo.type == me.userType.PROVINCE && me.ticket.assigneeId != null) {\n        me.isShowAssignee = true;\n      } else {\n        me.isShowAssignee = false;\n      }\n    }\n    if (me.typeRequest == 'update') {\n      // ghi chú, trạng thái, gán xử lý không bắt buộc\n      this.isRequiredNote = false;\n      this.isRequiredStatus = false;\n      this.isRequiredAssignee = false;\n      //tắt nút lưu\n      me.isEnableButtonSave = false;\n      //Hiển thị chuyển trạng thái, ghi chú\n      me.isShowStatus = true;\n      me.isShowNote = true;\n      //Chỉ hiển thị chuyển xử lý với admin tỉnh khi chưa chuyển xử lý\n      if (me.userInfo.type == me.userType.PROVINCE && me.ticket.assigneeId == null) {\n        me.isShowAssignee = true;\n      } else {\n        me.isShowAssignee = false;\n      }\n      // chỉ cho nhập imsi khi đang xử lý\n      if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\n        me.isShowImsiInput = true;\n        if (me.ticket.status == CONSTANTS.REQUEST_STATUS.DONE && me.listImsis.length <= 0) {\n          me.isEmptyListImsi = true;\n        } else {\n          me.isEmptyListImsi = false;\n        }\n      } else {\n        me.isShowImsiInput = false;\n      }\n      // if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.IN_PROGRESS && me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.DONE){\n      //     me.isShowListImsi = true;\n      // } else {\n      //     me.isShowListImsi = false;\n      // }\n      // Hiển thị imsi khi đang xử lý\n      if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\n        me.isShowListImsi = true;\n      } else {\n        me.isShowListImsi = false;\n      }\n      // Ẩn chuyển xử lý nếu không phải yêu cầu mới\n      if (me.ticket.statusOld != CONSTANTS.REQUEST_STATUS.NEW) {\n        me.isShowAssignee = false;\n      }\n    }\n    // trạng thái hoàn thành, từ chối\n    if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.DONE || me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.REJECT) {\n      // me.isShowStatus = false;\n      me.isShowAssignee = false;\n      if (me.userInfo.type == me.userType.PROVINCE && me.ticket.assigneeId != null) {\n        me.isShowAssignee = true;\n      } else {\n        me.isShowAssignee = false;\n      }\n    }\n    // Ân list note trống\n    if (this.listNotes.length == 0) {\n      me.isShowListNote = false;\n    } else {\n      me.isShowListNote = true;\n    }\n    me.isEnableButtonSave = true;\n  }\n  checkVisibleAndRequired() {\n    let me = this;\n    // nếu nhập trạng thái thì bắt buộc nhập ghi chú\n    if (this.ticket.status != null) {\n      me.isRequiredNote = true;\n      me.isShowAssignee = false;\n    } else {\n      me.isRequiredNote = false;\n      if (me.userInfo.type == me.userType.PROVINCE && me.ticket.assigneeId == null) {\n        me.isShowAssignee = true;\n      }\n    }\n    if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE) {\n      if (this.ticket.assigneeId == null && this.ticket.status == null) {\n        this.isShowStatus = true;\n        this.isShowNote = true;\n        this.isShowAssignee = true;\n      } else if (this.ticket.assigneeId != null && me.listActivatedAccount.includes(me.ticket.assigneeId)) {\n        this.isShowStatus = false;\n        this.isShowNote = false;\n      } else if (this.ticket.status != null || this.ticket.cause != null && me.ticket.cause.trim() != '') {\n        this.isShowStatus = true;\n        this.isShowNote = true;\n      }\n    } else if (me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) {\n      me.isShowStatus = true;\n      me.isShowNote = true;\n    }\n    if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.IN_PROGRESS && me.ticket.status == CONSTANTS.REQUEST_STATUS.DONE && this.listImsis.length == 0) {\n      me.isEmptyListImsi = true;\n      me.isShowMesReqImsi = true;\n    } else {\n      me.isEmptyListImsi = false;\n      me.isShowMesReqImsi = false;\n    }\n    if (me.ticket.status != null && me.ticket.cause != null && me.ticket.cause.trim() == '') {\n      me.isEnableButtonSave = false;\n    } else {\n      me.isEnableButtonSave = true;\n    }\n    // Ẩn chuyển xử lý nếu không phải yêu cầu mới\n    if (me.ticket.statusOld != CONSTANTS.REQUEST_STATUS.NEW) {\n      me.isShowAssignee = false;\n    }\n    this.cdr.detectChanges();\n  }\n  openModalOrder(id, item) {\n    this.isShowOrder = true;\n    this.itemInTable = item;\n    this.onSubmitSearchHistory();\n  }\n  getFirstErrorMessage() {\n    if (this.isShowMesReqImsi) {\n      return this.tranService.translate(\"global.message.required\");\n    }\n    if (this.isShowMesMaxLenImsi) {\n      return this.tranService.translate(\"ticket.message.imsiMaxLength\");\n    }\n    if (this.isShowMesExistImsi) {\n      return this.tranService.translate(\"ticket.message.imsiIsExist\");\n    }\n    return null;\n  }\n  isFormValid() {\n    return Object.values(this.mapForm).every(formGroup => formGroup.valid);\n  }\n  noWhitespaceValidator() {\n    return control => {\n      const isWhitespace = (control.value || '').trim().length === 0;\n      const isValid = !isWhitespace;\n      return isValid ? null : {\n        whitespace: true\n      };\n    };\n  }\n  onKeyDownNote(event) {\n    if (event.key === ' ' && (this.ticket.note == null || this.ticket.note != null && this.ticket.note.trim() === '')) {\n      event.preventDefault();\n    }\n    if (this.ticket.note != null && this.ticket.note.trim() != '') {\n      this.ticket.note = this.ticket.note.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  onKeyDownContent(event) {\n    if (event.key === ' ' && (this.ticket.content == null || this.ticket.content != null && this.ticket.content.trim() === '')) {\n      event.preventDefault();\n    }\n    if (this.ticket.content != null && this.ticket.content.trim() != '') {\n      this.ticket.content = this.ticket.content.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  onKeyDownCause(event) {\n    if (event.key === ' ' && (this.ticket.cause == null || this.ticket.cause != null && this.ticket.cause.trim() === '')) {\n      event.preventDefault();\n    }\n    if (this.ticket.cause != null && this.ticket.cause.trim() != '') {\n      this.ticket.cause = this.ticket.cause.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  onKeyDownNoteContent(event, note) {\n    if (event.key === ' ' && (!note.content || note.content.trim() === '')) {\n      event.preventDefault();\n    }\n    if (note.content && note.content.trim() !== '') {\n      note.content = note.content.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  static {\n    this.ɵfac = function ListOrderSimTicketComponent_Factory(t) {\n      return new (t || ListOrderSimTicketComponent)(i0.ɵɵdirectiveInject(TicketService), i0.ɵɵdirectiveInject(AccountService), i0.ɵɵdirectiveInject(SimTicketService), i0.ɵɵdirectiveInject(LogHandleTicketService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ListOrderSimTicketComponent,\n      selectors: [[\"list-order-sim-ticket\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 263,\n      vars: 243,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [\"styleClass\", \"p-button-info\", \"icon\", \"\", 3, \"label\", \"click\", 4, \"ngIf\"], [1, \"pt-3\", \"pb-2\", \"vnpt-field-set\", 3, \"formGroup\", \"ngSubmit\"], [3, \"toggleable\", \"header\"], [1, \"grid\", \"search-grid-4\"], [\"class\", \"col-3\", 4, \"ngIf\"], [1, \"col-3\"], [1, \"p-float-label\"], [\"styleClass\", \"w-full\", \"filterBy\", \"display\", \"id\", \"status\", \"formControlName\", \"status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"filterBy\", \"label\", 3, \"showClear\", \"filter\", \"autoDisplayFirst\", \"ngModel\", \"required\", \"options\", \"ngModelChange\"], [\"htmlFor\", \"status\", 1, \"label-dropdown\"], [\"pInputText\", \"\", \"id\", \"email\", \"formControlName\", \"contactName\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"email\"], [\"pInputText\", \"\", \"id\", \"phone\", \"formControlName\", \"contactPhone\", \"type\", \"number\", \"min\", \"0\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\", \"keydown\"], [\"htmlFor\", \"phone\"], [\"styleClass\", \"w-full\", \"id\", \"dateFrom\", \"formControlName\", \"dateFrom\", \"dateFormat\", \"dd/mm/yy\", 3, \"ngModel\", \"showIcon\", \"showClear\", \"maxDate\", \"ngModelChange\", \"onSelect\", \"onInput\"], [\"htmlFor\", \"dateFrom\", 1, \"label-calendar\"], [\"styleClass\", \"w-full\", \"id\", \"dateTo\", \"formControlName\", \"dateTo\", \"dateFormat\", \"dd/mm/yy\", 3, \"ngModel\", \"showIcon\", \"showClear\", \"minDate\", \"maxDate\", \"ngModelChange\", \"onSelect\", \"onInput\"], [\"htmlFor\", \"dateTo\", 1, \"label-calendar\"], [1, \"col-3\", \"pb-0\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"submit\"], [3, \"tableId\", \"fieldId\", \"columns\", \"dataSet\", \"options\", \"pageNumber\", \"loadData\", \"pageSize\", \"sort\", \"params\", \"labelTable\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\"], [3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"mt-3\", 3, \"formGroup\", \"ngSubmit\"], [1, \"flex\", \"flex-row\", \"flex-wrap\", \"justify-content-between\", \"w-full\"], [1, \"w-full\", \"field\", \"grid\"], [\"htmlFor\", \"contactName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"text-red-500\"], [1, \"col\"], [\"pInputText\", \"\", \"id\", \"contactName\", \"formControlName\", \"contactName\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"readonly\", \"ngModelChange\"], [1, \"w-full\", \"field\", \"grid\", \"text-error-field\"], [\"htmlFor\", \"fullName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [\"htmlFor\", \"email\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"contactEmail\", \"formControlName\", \"contactEmail\", \"pattern\", \"^[a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+)+(\\\\.[a-z]{2,})$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"readonly\", \"ngModelChange\"], [\"htmlFor\", \"contactEmail\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"htmlFor\", \"phone\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"contactPhone\", \"formControlName\", \"contactPhone\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"readonly\", \"ngModelChange\", \"keydown\"], [\"htmlFor\", \"quantity\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"pInputTextarea\", \"\", \"id\", \"quantity\", \"formControlName\", \"quantity\", 1, \"w-full\", 2, \"resize\", \"none\", \"padding\", \"0\", 3, \"autoResize\", \"ngModel\", \"required\", \"placeholder\", \"ngModelChange\", \"onInput\"], [\"htmlFor\", \"quantity\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [1, \"w-full\", \"mb-3\"], [1, \"flex\"], [\"objectKey\", \"provinceAddress\", \"paramKey\", \"name\", \"keyReturn\", \"code\", \"displayPattern\", \"${name}\", \"typeValue\", \"object\", 1, \"flex-1\", \"col-3\", 3, \"value\", \"placeholder\", \"isMultiChoice\", \"required\", \"disabled\", \"valueChange\", \"onchange\"], [\"objectKey\", \"districtAddress\", \"paramKey\", \"name\", \"keyReturn\", \"code\", \"displayPattern\", \"${name}\", \"typeValue\", \"object\", 1, \"flex-1\", \"col-3\", 3, \"value\", \"placeholder\", \"isMultiChoice\", \"required\", \"disabled\", \"paramDefault\", \"valueChange\", \"onchange\"], [\"objectKey\", \"communeAddress\", \"paramKey\", \"name\", \"keyReturn\", \"code\", \"displayPattern\", \"${name}\", \"typeValue\", \"object\", 1, \"flex-1\", \"col-3\", 3, \"value\", \"placeholder\", \"isMultiChoice\", \"required\", \"disabled\", \"paramDefault\", \"valueChange\", \"onchange\"], [\"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"detailAddress\", \"formControlName\", \"detailAddress\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"autoResize\", \"ngModel\", \"required\", \"maxlength\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"detailAddress\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"htmlFor\", \"content\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"content\", \"formControlName\", \"content\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"placeholder\", \"ngModelChange\", \"keydown\"], [\"htmlFor\", \"content\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"note\", \"formControlName\", \"note\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"placeholder\", \"ngModelChange\", \"keydown\"], [\"htmlFor\", \"note\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"align-items-center\", \"mt-3\"], [\"styleClass\", \"mr-2 p-button-secondary\", 3, \"label\", \"click\"], [\"type\", \"submit\", \"styleClass\", \"p-button-info\", 3, \"disabled\", \"label\", 4, \"ngIf\"], [1, \"flex\", \"dialog-ticket-sim-1\"], [1, \"flex-1\", \"col-11\"], [1, \"flex\", \"flex-row\", \"flex-wrap\", \"w-full\"], [\"class\", \"w-full field grid dialog-grid-customer-2\", 4, \"ngIf\"], [\"pInputText\", \"\", \"id\", \"contactPhone\", \"formControlName\", \"contactPhone\", \"pattern\", \"^((\\\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"placeholder\", \"readonly\", \"ngModelChange\", \"keydown\"], [\"htmlFor\", \"address\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"rows\", \"2\", \"pInputText\", \"\", \"id\", \"address\", \"formControlName\", \"address\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"placeholder\", \"readonly\", \"ngModelChange\"], [\"for\", \"status\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"styleClass\", \"w-full\", \"id\", \"type\", \"formControlName\", \"status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"required\", \"options\", \"placeholder\", \"emptyMessage\", \"ngModelChange\", \"onChange\"], [\"for\", \"statusOld\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [3, \"class\", 4, \"ngIf\"], [\"htmlFor\", \"userType\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"class\", \"w-full field grid\", 4, \"ngIf\"], [\"htmlFor\", \"cause\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"quantity\", \"formControlName\", \"quantity\", 1, \"w-full\", 3, \"ngModel\", \"placeholder\", \"readonly\", \"ngModelChange\", \"keydown\"], [\"pInputText\", \"\", \"id\", \"content\", \"formControlName\", \"content\", 1, \"w-full\", 3, \"ngModel\", \"placeholder\", \"readonly\", \"ngModelChange\", \"keydown\"], [\"pInputText\", \"\", \"id\", \"note\", \"formControlName\", \"note\", 1, \"w-full\", 3, \"ngModel\", \"placeholder\", \"readonly\", \"ngModelChange\", \"keydown\"], [\"for\", \"assigneeId\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"col\", 2, \"max-width\", \"calc(100% - 180px)\"], [\"id\", \"assigneeId\", \"objectKey\", \"account\", \"paramKey\", \"email\", \"formControlName\", \"assigneeId\", \"keyReturn\", \"id\", \"displayPattern\", \"${email}\", 3, \"value\", \"placeholder\", \"required\", \"isMultiChoice\", \"disabled\", \"paramDefault\", \"valueChange\", \"onchange\"], [\"htmlFor\", \"assigneeId\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"flex-grow-1\"], [1, \"col-fixed\", \"w-full\", \"pt-0\"], [4, \"ngIf\"], [1, \"col-4\", \"pt-0\"], [1, \"block\", \"w-full\"], [1, \"block\"], [\"class\", \"flex flex-row justify-content-between\", 4, \"ngIf\"], [1, \"grid\", \"justify-center\", \"mt-1\", \"px-auto\"], [1, \"col-11\"], [1, \"p-inputGroup\", \"justify-content-center\"], [\"type\", \"button\", \"pButton\", \"\", \"icon\", \"pi pi-plus\", 3, \"disabled\", \"click\"], [\"type\", \"number\", \"pInputText\", \"\", \"pattern\", \"[0-9]*\", \"maxlength\", \"18\", \"min\", \"0\", 3, \"ngModelOptions\", \"placeholder\", \"ngModel\", \"disabled\", \"ngModelChange\", \"input\", \"keydown\"], [1, \"col-12\", \"field\", \"grid\", \"text-error-field\"], [\"htmlFor\", \"imsi\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"col-12\"], [\"class\", \"flex flex-row justify-content-center align-items-center mt-3\", 4, \"ngIf\"], [1, \"dialog-vnpt\", 3, \"header\", \"modal\", \"draggable\", \"resizable\", \"breakpoints\", \"visible\", \"visibleChange\"], [1, \"grid\", \"search-grid-2\", \"align-items-center\"], [1, \"col-4\"], [\"styleClass\", \"w-full\", \"id\", \"dateFrom\", \"formControlName\", \"fromDate\", \"dateFormat\", \"dd/mm/yy\", 3, \"ngModel\", \"showIcon\", \"showClear\", \"maxDate\", \"ngModelChange\", \"onSelect\", \"onInput\"], [\"htmlFor\", \"dateFrom\"], [1, \"col-4\", \"ml-2\"], [\"styleClass\", \"w-full\", \"id\", \"dateTo\", \"formControlName\", \"toDate\", \"dateFormat\", \"dd/mm/yy\", 3, \"ngModel\", \"showIcon\", \"showClear\", \"minDate\", \"maxDate\", \"ngModelChange\", \"onSelect\", \"onInput\"], [\"htmlFor\", \"dateTo\"], [1, \"col-2\"], [3, \"columns\", \"dataSet\", \"options\", \"loadData\", \"pageNumber\", \"pageSize\", \"params\"], [\"styleClass\", \"p-button-info\", \"icon\", \"\", 3, \"label\", \"click\"], [\"styleClass\", \"w-full\", \"filterBy\", \"display\", \"id\", \"provinceCode\", \"formControlName\", \"provinceCode\", \"optionLabel\", \"display\", \"optionValue\", \"code\", 3, \"showClear\", \"filter\", \"autoDisplayFirst\", \"ngModel\", \"required\", \"options\", \"ngModelChange\"], [\"htmlFor\", \"provinceCode\", 1, \"label-dropdown\"], [3, \"tableId\", \"fieldId\", \"columns\", \"dataSet\", \"options\", \"pageNumber\", \"loadData\", \"pageSize\", \"sort\", \"params\", \"labelTable\"], [\"type\", \"submit\", \"styleClass\", \"p-button-info\", 3, \"disabled\", \"label\"], [1, \"w-full\", \"field\", \"grid\", \"dialog-grid-customer-2\"], [\"styleClass\", \"w-full\", \"filterBy\", \"display\", \"id\", \"provinceCode\", \"formControlName\", \"provinceCode\", \"optionLabel\", \"display\", \"optionValue\", \"code\", 3, \"showClear\", \"filter\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"disabled\", \"readonly\", \"ngModelChange\"], [\"htmlFor\", \"cause\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"rows\", \"2\", \"pInputTextarea\", \"\", \"id\", \"cause\", \"formControlName\", \"cause\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"required\", \"readonly\", \"placeholder\", \"ngModelChange\", \"input\", \"keydown\"], [1, \"col-fixed\", 2, \"height\", \"fit-content\"], [1, \"p-grid\", \"p-justify-center\"], [3, \"value\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"col-1\"], [3, \"formGroup\"], [\"class\", \"w-full\", \"pInputText\", \"\", \"id\", \"content\", \"formControlName\", \"content\", 3, \"ngModel\", \"required\", \"maxLength\", \"ngModelChange\", \"keydown\", 4, \"ngIf\"], [3, \"pTooltip\", 4, \"ngIf\"], [\"pInputText\", \"\", \"id\", \"content\", \"formControlName\", \"content\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"ngModelChange\", \"keydown\"], [3, \"pTooltip\"], [1, \"flex\", \"flex-row\", \"justify-content-between\"], [1, \"w-full\"], [\"icon\", \"pi pi-trash\", 3, \"hidden\", \"click\"], [\"type\", \"submit\", \"styleClass\", \"p-button-info\", 3, \"disabled\", \"hidden\", \"label\", 4, \"ngIf\"], [\"type\", \"submit\", \"styleClass\", \"p-button-info\", 3, \"disabled\", \"hidden\", \"label\"]],\n      template: function ListOrderSimTicketComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵtemplate(6, ListOrderSimTicketComponent_p_button_6_Template, 1, 1, \"p-button\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"form\", 6);\n          i0.ɵɵlistener(\"ngSubmit\", function ListOrderSimTicketComponent_Template_form_ngSubmit_7_listener() {\n            return ctx.onSubmitSearch();\n          });\n          i0.ɵɵelementStart(8, \"p-panel\", 7)(9, \"div\", 8);\n          i0.ɵɵtemplate(10, ListOrderSimTicketComponent_div_10_Template, 5, 7, \"div\", 9);\n          i0.ɵɵelementStart(11, \"div\", 10)(12, \"span\", 11)(13, \"p-dropdown\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_p_dropdown_ngModelChange_13_listener($event) {\n            return ctx.searchInfo.status = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"label\", 13);\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 10)(17, \"span\", 11)(18, \"input\", 14);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_input_ngModelChange_18_listener($event) {\n            return ctx.searchInfo.contactName = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"label\", 15);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 10)(22, \"span\", 11)(23, \"input\", 16);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_input_ngModelChange_23_listener($event) {\n            return ctx.searchInfo.contactPhone = $event;\n          })(\"keydown\", function ListOrderSimTicketComponent_Template_input_keydown_23_listener($event) {\n            return ctx.preventCharacter($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"label\", 17);\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"div\", 10)(27, \"span\", 11)(28, \"p-calendar\", 18);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_p_calendar_ngModelChange_28_listener($event) {\n            return ctx.searchInfo.dateFrom = $event;\n          })(\"onSelect\", function ListOrderSimTicketComponent_Template_p_calendar_onSelect_28_listener() {\n            return ctx.onChangeDateFrom(ctx.searchInfo.dateFrom);\n          })(\"onInput\", function ListOrderSimTicketComponent_Template_p_calendar_onInput_28_listener() {\n            return ctx.onChangeDateFrom(ctx.searchInfo.dateFrom);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"label\", 19);\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 10)(32, \"span\", 11)(33, \"p-calendar\", 20);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_p_calendar_ngModelChange_33_listener($event) {\n            return ctx.searchInfo.dateTo = $event;\n          })(\"onSelect\", function ListOrderSimTicketComponent_Template_p_calendar_onSelect_33_listener() {\n            return ctx.onChangeDateTo(ctx.searchInfo.dateTo);\n          })(\"onInput\", function ListOrderSimTicketComponent_Template_p_calendar_onInput_33_listener() {\n            return ctx.onChangeDateTo(ctx.searchInfo.dateTo);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"label\", 21);\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"div\", 22);\n          i0.ɵɵelement(37, \"p-button\", 23);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(38, ListOrderSimTicketComponent_table_vnpt_38_Template, 1, 11, \"table-vnpt\", 24);\n          i0.ɵɵtemplate(39, ListOrderSimTicketComponent_table_vnpt_39_Template, 1, 11, \"table-vnpt\", 24);\n          i0.ɵɵelementStart(40, \"div\", 25)(41, \"p-dialog\", 26);\n          i0.ɵɵlistener(\"visibleChange\", function ListOrderSimTicketComponent_Template_p_dialog_visibleChange_41_listener($event) {\n            return ctx.isShowCreateRequest = $event;\n          });\n          i0.ɵɵelementStart(42, \"form\", 27);\n          i0.ɵɵlistener(\"ngSubmit\", function ListOrderSimTicketComponent_Template_form_ngSubmit_42_listener() {\n            return ctx.createOrderSim();\n          });\n          i0.ɵɵelementStart(43, \"div\", 28)(44, \"div\", 29)(45, \"label\", 30);\n          i0.ɵɵtext(46);\n          i0.ɵɵelementStart(47, \"span\", 31);\n          i0.ɵɵtext(48, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"div\", 32)(50, \"input\", 33);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_input_ngModelChange_50_listener($event) {\n            return ctx.ticket.contactName = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(51, \"div\", 34);\n          i0.ɵɵelement(52, \"label\", 35);\n          i0.ɵɵelementStart(53, \"div\", 32);\n          i0.ɵɵtemplate(54, ListOrderSimTicketComponent_small_54_Template, 2, 1, \"small\", 36);\n          i0.ɵɵtemplate(55, ListOrderSimTicketComponent_small_55_Template, 2, 2, \"small\", 36);\n          i0.ɵɵtemplate(56, ListOrderSimTicketComponent_small_56_Template, 2, 1, \"small\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 29)(58, \"label\", 37);\n          i0.ɵɵtext(59);\n          i0.ɵɵelementStart(60, \"span\", 31);\n          i0.ɵɵtext(61, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(62, \"div\", 32)(63, \"input\", 38);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_input_ngModelChange_63_listener($event) {\n            return ctx.ticket.contactEmail = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(64, \"div\", 34);\n          i0.ɵɵelement(65, \"label\", 39);\n          i0.ɵɵelementStart(66, \"div\", 32);\n          i0.ɵɵtemplate(67, ListOrderSimTicketComponent_small_67_Template, 2, 1, \"small\", 36);\n          i0.ɵɵtemplate(68, ListOrderSimTicketComponent_small_68_Template, 2, 2, \"small\", 36);\n          i0.ɵɵtemplate(69, ListOrderSimTicketComponent_small_69_Template, 2, 1, \"small\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(70, \"div\", 29)(71, \"label\", 40);\n          i0.ɵɵtext(72);\n          i0.ɵɵelementStart(73, \"span\", 31);\n          i0.ɵɵtext(74, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(75, \"div\", 32)(76, \"input\", 41);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_input_ngModelChange_76_listener($event) {\n            return ctx.ticket.contactPhone = $event;\n          })(\"keydown\", function ListOrderSimTicketComponent_Template_input_keydown_76_listener($event) {\n            return ctx.preventCharacter($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(77, \"div\", 34);\n          i0.ɵɵelement(78, \"label\", 40);\n          i0.ɵɵelementStart(79, \"div\", 32);\n          i0.ɵɵtemplate(80, ListOrderSimTicketComponent_small_80_Template, 2, 1, \"small\", 36);\n          i0.ɵɵtemplate(81, ListOrderSimTicketComponent_small_81_Template, 2, 1, \"small\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"div\", 29)(83, \"label\", 42);\n          i0.ɵɵtext(84);\n          i0.ɵɵelementStart(85, \"span\", 31);\n          i0.ɵɵtext(86, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(87, \"div\", 32)(88, \"p-inputNumber\", 43);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_p_inputNumber_ngModelChange_88_listener($event) {\n            return ctx.ticket.quantity = $event;\n          })(\"onInput\", function ListOrderSimTicketComponent_Template_p_inputNumber_onInput_88_listener($event) {\n            return ctx.checkQuantity($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(89, \"div\", 34);\n          i0.ɵɵelement(90, \"label\", 44);\n          i0.ɵɵelementStart(91, \"div\", 32);\n          i0.ɵɵtemplate(92, ListOrderSimTicketComponent_small_92_Template, 2, 1, \"small\", 36);\n          i0.ɵɵtemplate(93, ListOrderSimTicketComponent_small_93_Template, 2, 1, \"small\", 36);\n          i0.ɵɵtemplate(94, ListOrderSimTicketComponent_small_94_Template, 2, 1, \"small\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(95, \"div\", 29)(96, \"label\", 45);\n          i0.ɵɵtext(97);\n          i0.ɵɵelementStart(98, \"span\", 31);\n          i0.ɵɵtext(99, \"*\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(100, \"p-card\", 46)(101, \"div\", 47)(102, \"vnpt-select\", 48);\n          i0.ɵɵlistener(\"valueChange\", function ListOrderSimTicketComponent_Template_vnpt_select_valueChange_102_listener($event) {\n            return ctx.ticket.province = $event;\n          })(\"onchange\", function ListOrderSimTicketComponent_Template_vnpt_select_onchange_102_listener($event) {\n            return ctx.onSelectProvince($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"vnpt-select\", 49);\n          i0.ɵɵlistener(\"valueChange\", function ListOrderSimTicketComponent_Template_vnpt_select_valueChange_103_listener($event) {\n            return ctx.ticket.district = $event;\n          })(\"onchange\", function ListOrderSimTicketComponent_Template_vnpt_select_onchange_103_listener($event) {\n            return ctx.onSelectDistrict($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"vnpt-select\", 50);\n          i0.ɵɵlistener(\"valueChange\", function ListOrderSimTicketComponent_Template_vnpt_select_valueChange_104_listener($event) {\n            return ctx.ticket.commune = $event;\n          })(\"onchange\", function ListOrderSimTicketComponent_Template_vnpt_select_onchange_104_listener($event) {\n            return ctx.onSelectCommune($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(105, \"div\", 47)(106, \"div\", 32)(107, \"input\", 51);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_input_ngModelChange_107_listener($event) {\n            return ctx.ticket.detailAddress = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(108, \"div\", 34);\n          i0.ɵɵelement(109, \"label\", 52);\n          i0.ɵɵelementStart(110, \"div\", 32);\n          i0.ɵɵtemplate(111, ListOrderSimTicketComponent_small_111_Template, 2, 2, \"small\", 36);\n          i0.ɵɵtemplate(112, ListOrderSimTicketComponent_small_112_Template, 2, 1, \"small\", 36);\n          i0.ɵɵtemplate(113, ListOrderSimTicketComponent_small_113_Template, 2, 1, \"small\", 36);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(114, \"div\", 29)(115, \"label\", 53);\n          i0.ɵɵtext(116);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"div\", 32)(118, \"textarea\", 54);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_textarea_ngModelChange_118_listener($event) {\n            return ctx.ticket.content = $event;\n          })(\"keydown\", function ListOrderSimTicketComponent_Template_textarea_keydown_118_listener($event) {\n            return ctx.onKeyDownContent($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(119, \"div\", 34);\n          i0.ɵɵelement(120, \"label\", 55);\n          i0.ɵɵelementStart(121, \"div\", 32);\n          i0.ɵɵtemplate(122, ListOrderSimTicketComponent_small_122_Template, 2, 2, \"small\", 36);\n          i0.ɵɵtemplate(123, ListOrderSimTicketComponent_small_123_Template, 2, 1, \"small\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(124, \"div\", 29)(125, \"label\", 53);\n          i0.ɵɵtext(126);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(127, \"div\", 32)(128, \"textarea\", 56);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_textarea_ngModelChange_128_listener($event) {\n            return ctx.ticket.note = $event;\n          })(\"keydown\", function ListOrderSimTicketComponent_Template_textarea_keydown_128_listener($event) {\n            return ctx.onKeyDownNote($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(129, \"div\", 34);\n          i0.ɵɵelement(130, \"label\", 57);\n          i0.ɵɵelementStart(131, \"div\", 32);\n          i0.ɵɵtemplate(132, ListOrderSimTicketComponent_small_132_Template, 2, 2, \"small\", 36);\n          i0.ɵɵtemplate(133, ListOrderSimTicketComponent_small_133_Template, 2, 1, \"small\", 36);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(134, \"div\", 58)(135, \"p-button\", 59);\n          i0.ɵɵlistener(\"click\", function ListOrderSimTicketComponent_Template_p_button_click_135_listener() {\n            return ctx.isShowCreateRequest = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(136, ListOrderSimTicketComponent_p_button_136_Template, 1, 2, \"p-button\", 60);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(137, \"p-dialog\", 26);\n          i0.ɵɵlistener(\"visibleChange\", function ListOrderSimTicketComponent_Template_p_dialog_visibleChange_137_listener($event) {\n            return ctx.isShowUpdateRequest = $event;\n          });\n          i0.ɵɵelementStart(138, \"form\", 27);\n          i0.ɵɵlistener(\"ngSubmit\", function ListOrderSimTicketComponent_Template_form_ngSubmit_138_listener() {\n            return ctx.updateOrderSim();\n          });\n          i0.ɵɵelementStart(139, \"div\", 61)(140, \"div\", 62)(141, \"div\", 63);\n          i0.ɵɵtemplate(142, ListOrderSimTicketComponent_div_142_Template, 7, 8, \"div\", 64);\n          i0.ɵɵelementStart(143, \"div\", 29)(144, \"label\", 30);\n          i0.ɵɵtext(145);\n          i0.ɵɵelementStart(146, \"span\", 31);\n          i0.ɵɵtext(147, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(148, \"div\", 32)(149, \"input\", 33);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_input_ngModelChange_149_listener($event) {\n            return ctx.ticket.contactName = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(150, \"div\", 29)(151, \"label\", 40);\n          i0.ɵɵtext(152);\n          i0.ɵɵelementStart(153, \"span\", 31);\n          i0.ɵɵtext(154, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(155, \"div\", 32)(156, \"input\", 65);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_input_ngModelChange_156_listener($event) {\n            return ctx.ticket.contactPhone = $event;\n          })(\"keydown\", function ListOrderSimTicketComponent_Template_input_keydown_156_listener($event) {\n            return ctx.preventCharacter($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(157, \"div\", 29)(158, \"label\", 66);\n          i0.ɵɵtext(159);\n          i0.ɵɵelementStart(160, \"span\", 31);\n          i0.ɵɵtext(161, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(162, \"div\", 32)(163, \"textarea\", 67);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_textarea_ngModelChange_163_listener($event) {\n            return ctx.ticket.address = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(164, \"div\", 29)(165, \"label\", 68);\n          i0.ɵɵtext(166);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(167, \"div\", 32)(168, \"p-dropdown\", 69);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_p_dropdown_ngModelChange_168_listener($event) {\n            return ctx.ticket.status = $event;\n          })(\"onChange\", function ListOrderSimTicketComponent_Template_p_dropdown_onChange_168_listener() {\n            return ctx.checkVisibleAndRequired();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(169, \"div\", 29)(170, \"label\", 70);\n          i0.ɵɵtext(171);\n          i0.ɵɵelementStart(172, \"span\", 31);\n          i0.ɵɵtext(173, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(174, \"div\", 32);\n          i0.ɵɵtemplate(175, ListOrderSimTicketComponent_span_175_Template, 2, 4, \"span\", 71);\n          i0.ɵɵtemplate(176, ListOrderSimTicketComponent_span_176_Template, 2, 4, \"span\", 71);\n          i0.ɵɵtemplate(177, ListOrderSimTicketComponent_span_177_Template, 2, 4, \"span\", 71);\n          i0.ɵɵtemplate(178, ListOrderSimTicketComponent_span_178_Template, 2, 4, \"span\", 71);\n          i0.ɵɵtemplate(179, ListOrderSimTicketComponent_span_179_Template, 2, 4, \"span\", 71);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(180, \"div\", 34);\n          i0.ɵɵelement(181, \"label\", 72);\n          i0.ɵɵelementStart(182, \"div\", 32);\n          i0.ɵɵtemplate(183, ListOrderSimTicketComponent_small_183_Template, 2, 1, \"small\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(184, ListOrderSimTicketComponent_div_184_Template, 6, 8, \"div\", 73);\n          i0.ɵɵelementStart(185, \"div\", 34);\n          i0.ɵɵelement(186, \"label\", 74);\n          i0.ɵɵelementStart(187, \"div\", 32);\n          i0.ɵɵtemplate(188, ListOrderSimTicketComponent_small_188_Template, 2, 2, \"small\", 36);\n          i0.ɵɵtemplate(189, ListOrderSimTicketComponent_small_189_Template, 2, 1, \"small\", 36);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(190, \"div\", 62)(191, \"div\", 63)(192, \"div\", 29)(193, \"label\", 37);\n          i0.ɵɵtext(194);\n          i0.ɵɵelementStart(195, \"span\", 31);\n          i0.ɵɵtext(196, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(197, \"div\", 32)(198, \"input\", 38);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_input_ngModelChange_198_listener($event) {\n            return ctx.ticket.contactEmail = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(199, \"div\", 29)(200, \"label\", 53);\n          i0.ɵɵtext(201);\n          i0.ɵɵelementStart(202, \"span\", 31);\n          i0.ɵɵtext(203, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(204, \"div\", 32)(205, \"input\", 75);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_input_ngModelChange_205_listener($event) {\n            return ctx.ticket.quantity = $event;\n          })(\"keydown\", function ListOrderSimTicketComponent_Template_input_keydown_205_listener($event) {\n            return ctx.preventCharacter($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(206, \"div\", 29)(207, \"label\", 53);\n          i0.ɵɵtext(208);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(209, \"div\", 32)(210, \"input\", 76);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_input_ngModelChange_210_listener($event) {\n            return ctx.ticket.content = $event;\n          })(\"keydown\", function ListOrderSimTicketComponent_Template_input_keydown_210_listener($event) {\n            return ctx.preventCharacter($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(211, \"div\", 29)(212, \"label\", 53);\n          i0.ɵɵtext(213);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(214, \"div\", 32)(215, \"input\", 77);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_input_ngModelChange_215_listener($event) {\n            return ctx.ticket.note = $event;\n          })(\"keydown\", function ListOrderSimTicketComponent_Template_input_keydown_215_listener($event) {\n            return ctx.preventCharacter($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(216, \"div\", 29)(217, \"label\", 78);\n          i0.ɵɵtext(218);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(219, \"div\", 79)(220, \"vnpt-select\", 80);\n          i0.ɵɵlistener(\"valueChange\", function ListOrderSimTicketComponent_Template_vnpt_select_valueChange_220_listener($event) {\n            return ctx.ticket.assigneeId = $event;\n          })(\"onchange\", function ListOrderSimTicketComponent_Template_vnpt_select_onchange_220_listener() {\n            return ctx.checkVisibleAndRequired();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(221, \"div\", 34);\n          i0.ɵɵelement(222, \"label\", 81);\n          i0.ɵɵelementStart(223, \"div\", 32);\n          i0.ɵɵtemplate(224, ListOrderSimTicketComponent_small_224_Template, 2, 1, \"small\", 36);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(225, \"div\", 47)(226, \"div\", 82)(227, \"div\", 83);\n          i0.ɵɵtemplate(228, ListOrderSimTicketComponent_div_228_Template, 10, 2, \"div\", 84);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(229, \"div\", 85)(230, \"div\", 86)(231, \"div\", 87)(232, \"div\", 29)(233, \"label\", 45)(234, \"b\");\n          i0.ɵɵtext(235);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(236, ListOrderSimTicketComponent_div_236_Template, 5, 1, \"div\", 88);\n          i0.ɵɵelementStart(237, \"div\", 89)(238, \"div\", 90)(239, \"div\", 91)(240, \"button\", 92);\n          i0.ɵɵlistener(\"click\", function ListOrderSimTicketComponent_Template_button_click_240_listener() {\n            return ctx.addImsi();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(241, \"input\", 93);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_input_ngModelChange_241_listener($event) {\n            return ctx.newImsi = $event;\n          })(\"input\", function ListOrderSimTicketComponent_Template_input_input_241_listener() {\n            return ctx.checkImsiInput();\n          })(\"keydown\", function ListOrderSimTicketComponent_Template_input_keydown_241_listener($event) {\n            return ctx.preventCharacter($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(242, \"div\", 94);\n          i0.ɵɵelement(243, \"label\", 95);\n          i0.ɵɵelementStart(244, \"div\", 96);\n          i0.ɵɵtemplate(245, ListOrderSimTicketComponent_small_245_Template, 2, 1, \"small\", 36);\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵtemplate(246, ListOrderSimTicketComponent_div_246_Template, 3, 4, \"div\", 97);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(247, \"p-dialog\", 98);\n          i0.ɵɵlistener(\"visibleChange\", function ListOrderSimTicketComponent_Template_p_dialog_visibleChange_247_listener($event) {\n            return ctx.isShowOrder = $event;\n          });\n          i0.ɵɵelementStart(248, \"form\", 6);\n          i0.ɵɵlistener(\"ngSubmit\", function ListOrderSimTicketComponent_Template_form_ngSubmit_248_listener() {\n            return ctx.onSubmitSearchHistory();\n          });\n          i0.ɵɵelementStart(249, \"div\", 99)(250, \"div\", 100)(251, \"span\", 11)(252, \"p-calendar\", 101);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_p_calendar_ngModelChange_252_listener($event) {\n            return ctx.searchHistoryOrder.fromDate = $event;\n          })(\"onSelect\", function ListOrderSimTicketComponent_Template_p_calendar_onSelect_252_listener() {\n            return ctx.onChangeDateFrom(ctx.searchHistoryOrder.fromDate);\n          })(\"onInput\", function ListOrderSimTicketComponent_Template_p_calendar_onInput_252_listener() {\n            return ctx.onChangeDateFrom(ctx.searchHistoryOrder.fromDate);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(253, \"label\", 102);\n          i0.ɵɵtext(254);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(255, \"div\", 103)(256, \"span\", 11)(257, \"p-calendar\", 104);\n          i0.ɵɵlistener(\"ngModelChange\", function ListOrderSimTicketComponent_Template_p_calendar_ngModelChange_257_listener($event) {\n            return ctx.searchHistoryOrder.toDate = $event;\n          })(\"onSelect\", function ListOrderSimTicketComponent_Template_p_calendar_onSelect_257_listener() {\n            return ctx.onChangeDateTo(ctx.searchHistoryOrder.toDate);\n          })(\"onInput\", function ListOrderSimTicketComponent_Template_p_calendar_onInput_257_listener() {\n            return ctx.onChangeDateTo(ctx.searchHistoryOrder.toDate);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(258, \"label\", 105);\n          i0.ɵɵtext(259);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(260, \"div\", 106);\n          i0.ɵɵelement(261, \"p-button\", 23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelement(262, \"table-vnpt\", 107);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.menu.orderSim\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo.type == ctx.userType.CUSTOMER && ctx.checkAuthen(i0.ɵɵpureFunction1(233, _c6, ctx.CONSTANTS.PERMISSIONS.TICKET.CREATE)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formSearchTicket);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"toggleable\", true)(\"header\", ctx.tranService.translate(\"global.text.filter\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo.type == ctx.userType.ADMIN);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"showClear\", true)(\"filter\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.searchInfo.status)(\"required\", false)(\"options\", ctx.listTicketStatus)(\"filter\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.status\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.contactName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.fullName\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.contactPhone);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.phone\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.dateFrom)(\"showIcon\", true)(\"showClear\", true)(\"maxDate\", ctx.maxDateFrom);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.dateFrom\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.dateTo)(\"showIcon\", true)(\"showClear\", true)(\"minDate\", ctx.minDateTo)(\"maxDate\", ctx.maxDateTo);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.dateTo\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.changeTable);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.changeTable);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(235, _c7));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"ticket.label.createRequest\"))(\"visible\", ctx.isShowCreateRequest)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formTicketSim);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.customerName\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.ticket.contactName)(\"required\", true)(\"maxLength\", 50)(\"placeholder\", ctx.tranService.translate(\"account.text.inputFullname\"))(\"readonly\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactName.dirty && (ctx.formTicketSim.controls.contactName.errors == null ? null : ctx.formTicketSim.controls.contactName.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactName.errors == null ? null : ctx.formTicketSim.controls.contactName.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactName.errors == null ? null : ctx.formTicketSim.controls.contactName.errors.pattern);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.email\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.ticket.contactEmail)(\"required\", true)(\"maxLength\", 50)(\"placeholder\", ctx.tranService.translate(\"account.text.inputEmail\"))(\"readonly\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactEmail.dirty && (ctx.formTicketSim.controls.contactEmail.errors == null ? null : ctx.formTicketSim.controls.contactEmail.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactEmail.errors == null ? null : ctx.formTicketSim.controls.contactEmail.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactEmail.errors == null ? null : ctx.formTicketSim.controls.contactEmail.errors.pattern);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.phone\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.ticket.contactPhone)(\"required\", true)(\"maxLength\", 12)(\"placeholder\", ctx.tranService.translate(\"account.text.inputPhone\"))(\"readonly\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactPhone.dirty && (ctx.formTicketSim.controls.contactPhone.errors == null ? null : ctx.formTicketSim.controls.contactPhone.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactPhone.errors == null ? null : ctx.formTicketSim.controls.contactPhone.errors.pattern);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", ctx.tranService.translate(\"ticket.label.quantity\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"autoResize\", false)(\"ngModel\", ctx.ticket.quantity)(\"required\", true)(\"placeholder\", ctx.tranService.translate(\"ticket.label.quantity\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.quantity.dirty && (ctx.formTicketSim.controls.quantity.errors == null ? null : ctx.formTicketSim.controls.quantity.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMaxQuantity);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMinQuantity);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", ctx.tranService.translate(\"ticket.label.deliveryAddress\"), \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"value\", ctx.ticket.province)(\"placeholder\", ctx.tranService.translate(\"ticket.label.province\"))(\"isMultiChoice\", false)(\"required\", true)(\"disabled\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"value\", ctx.ticket.district)(\"placeholder\", ctx.tranService.translate(\"ticket.label.district\"))(\"isMultiChoice\", false)(\"required\", true)(\"disabled\", ctx.disableSelectDistrict)(\"paramDefault\", ctx.optionAddress);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"value\", ctx.ticket.commune)(\"placeholder\", ctx.tranService.translate(\"ticket.label.commune\"))(\"isMultiChoice\", false)(\"required\", true)(\"disabled\", ctx.disableSelectCommune)(\"paramDefault\", ctx.optionAddress);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"autoResize\", false)(\"ngModel\", ctx.ticket.detailAddress)(\"required\", true)(\"maxlength\", 255)(\"placeholder\", ctx.tranService.translate(\"ticket.label.detailAddress\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.detailAddress.errors == null ? null : ctx.formTicketSim.controls.detailAddress.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.detailAddress.dirty && (ctx.formTicketSim.controls.detailAddress.errors == null ? null : ctx.formTicketSim.controls.detailAddress.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.detailAddress.errors == null ? null : ctx.formTicketSim.controls.detailAddress.errors.pattern);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.content\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"autoResize\", false)(\"ngModel\", ctx.ticket.content)(\"maxlength\", 255)(\"placeholder\", ctx.tranService.translate(\"ticket.label.content\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.content.errors == null ? null : ctx.formTicketSim.controls.content.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.content.errors == null ? null : ctx.formTicketSim.controls.content.errors.pattern);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.note\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"autoResize\", false)(\"ngModel\", ctx.ticket.note)(\"maxlength\", 255)(\"placeholder\", ctx.tranService.translate(\"ticket.label.note\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.note.errors == null ? null : ctx.formTicketSim.controls.note.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.note.errors == null ? null : ctx.formTicketSim.controls.note.errors.pattern);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.cancel\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkAuthen(i0.ɵɵpureFunction1(236, _c6, ctx.CONSTANTS.PERMISSIONS.TICKET.CREATE)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(238, _c8));\n          i0.ɵɵproperty(\"header\", ctx.typeRequest == \"view\" ? ctx.tranService.translate(\"ticket.label.viewOrderSim\") : ctx.tranService.translate(\"ticket.label.updateOrderSim\"))(\"visible\", ctx.isShowUpdateRequest)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formUpdateOrderSim);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo.type == ctx.userType.ADMIN && ctx.typeRequest == \"view\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.customerName\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.ticket.contactName)(\"required\", true)(\"maxLength\", 50)(\"placeholder\", ctx.tranService.translate(\"account.text.inputFullname\"))(\"readonly\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.phone\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.ticket.contactPhone)(\"required\", true)(\"placeholder\", ctx.tranService.translate(\"account.text.inputPhone\"))(\"readonly\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.deliveryAddress\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.ticket.address)(\"required\", true)(\"placeholder\", ctx.tranService.translate(\"ticket.label.address\"))(\"readonly\", true);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.isShowStatus ? \"\" : \"hidden\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.status\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", true)(\"ngModel\", ctx.ticket.status)(\"required\", ctx.isRequiredStatus)(\"options\", ctx.ticket.statusOld !== null ? ctx.mapTicketStatus[ctx.ticket.statusOld] : ctx.listTicketStatus)(\"placeholder\", ctx.tranService.translate(\"ticket.label.status\"))(\"emptyMessage\", ctx.tranService.translate(\"global.text.nodata\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.typeRequest == \"view\" ? \"\" : \"hidden\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.status\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.ticket.statusOld == ctx.CONSTANTS.REQUEST_STATUS.NEW);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.ticket.statusOld == ctx.CONSTANTS.REQUEST_STATUS.RECEIVED);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.ticket.statusOld == ctx.CONSTANTS.REQUEST_STATUS.IN_PROGRESS);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.ticket.statusOld == ctx.CONSTANTS.REQUEST_STATUS.REJECT);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.ticket.statusOld == ctx.CONSTANTS.REQUEST_STATUS.DONE);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.status.dirty && (ctx.formTicketSim.controls.status.errors == null ? null : ctx.formTicketSim.controls.status.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowNote);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.cause.errors == null ? null : ctx.formTicketSim.controls.cause.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.cause.dirty && (ctx.formTicketSim.controls.cause.errors == null ? null : ctx.formTicketSim.controls.cause.errors.required));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.email\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.ticket.contactEmail)(\"required\", true)(\"maxLength\", 50)(\"placeholder\", ctx.tranService.translate(\"account.text.inputEmail\"))(\"readonly\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", ctx.tranService.translate(\"ticket.label.quantity\"), \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.ticket.quantity)(\"placeholder\", ctx.tranService.translate(\"ticket.label.quantity\"))(\"readonly\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.content\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.ticket.content)(\"placeholder\", ctx.tranService.translate(\"ticket.label.content\"))(\"readonly\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.note\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.ticket.note)(\"placeholder\", ctx.tranService.translate(\"ticket.label.note\"))(\"readonly\", true);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.isShowAssignee ? \"\" : \"hidden\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.transferProcessing\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.ticket.assigneeId)(\"placeholder\", ctx.tranService.translate(\"ticket.label.transferProcessing\"))(\"required\", ctx.isRequiredAssignee)(\"isMultiChoice\", false)(\"disabled\", ctx.typeRequest == \"view\")(\"paramDefault\", i0.ɵɵpureFunction0(239, _c9));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.assigneeId.dirty && (ctx.formTicketSim.controls.assigneeId.errors == null ? null : ctx.formTicketSim.controls.assigneeId.errors.required));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowListNote);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassMap(ctx.isShowListImsi ? \"\" : \"hidden\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.listImsis\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowListImsi);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.isShowImsiInput ? \"\" : \"hidden\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.listImsis.length >= ctx.ticket.quantity || ctx.isShowMesMaxLenImsi || ctx.newImsi == \"\" || ctx.isShowMesExistImsi);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(240, _c10))(\"placeholder\", ctx.tranService.translate(\"ticket.text.addImsi\"))(\"ngModel\", ctx.newImsi)(\"disabled\", ctx.listImsis.length >= ctx.ticket.quantity);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.getFirstErrorMessage());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(241, _c11));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"ticket.label.historyOrder\"))(\"modal\", true)(\"draggable\", false)(\"resizable\", false)(\"breakpoints\", i0.ɵɵpureFunction0(242, _c12))(\"visible\", ctx.isShowOrder);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formSearchHistoryOrder);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchHistoryOrder.fromDate)(\"showIcon\", true)(\"showClear\", true)(\"maxDate\", ctx.maxDateFrom);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.dateFrom\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchHistoryOrder.toDate)(\"showIcon\", true)(\"showClear\", true)(\"minDate\", ctx.minDateTo)(\"maxDate\", ctx.maxDateTo);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.dateTo\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"columns\", ctx.columnsHistory)(\"dataSet\", ctx.dataSetHistory)(\"options\", ctx.optionTableHistory)(\"loadData\", ctx.searchHistory.bind(ctx))(\"pageNumber\", 0)(\"pageSize\", 999999999999)(\"params\", ctx.searchHistoryOrder);\n        }\n      },\n      dependencies: [i2.NgIf, i3.Breadcrumb, i4.Tooltip, i5.PrimeTemplate, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MaxLengthValidator, i1.PatternValidator, i1.MinValidator, i1.NgModel, i1.FormGroupDirective, i1.FormControlName, i6.InputText, i7.ButtonDirective, i7.Button, i8.TableVnptComponent, i9.VnptCombobox, i10.Calendar, i11.Dropdown, i12.Card, i13.Dialog, i14.InputTextarea, i15.Panel, i16.InputNumber, i17.Table, i2.DatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["ComponentBase", "TicketService", "AccountService", "Validators", "CONSTANTS", "SimTicketService", "LogHandleTicketService", "i0", "ɵɵelementStart", "ɵɵlistener", "ListOrderSimTicketComponent_p_button_6_Template_p_button_click_0_listener", "ɵɵrestoreView", "_r39", "ctx_r38", "ɵɵnextContext", "ɵɵresetView", "showModalCreate", "ɵɵelementEnd", "ɵɵproperty", "ctx_r0", "tranService", "translate", "ListOrderSimTicketComponent_div_10_Template_p_dropdown_ngModelChange_2_listener", "$event", "_r41", "ctx_r40", "searchInfo", "provinceCode", "ɵɵtext", "ɵɵadvance", "ctx_r1", "listProvince", "ɵɵtextInterpolate", "ɵɵelement", "ctx_r2", "columns", "dataSet", "optionTable", "pageNumber", "search", "bind", "pageSize", "sort", "ctx_r3", "ctx_r4", "ctx_r5", "ɵɵpureFunction0", "_c0", "ctx_r6", "ctx_r7", "ctx_r8", "ctx_r9", "ctx_r10", "ctx_r11", "ctx_r12", "ctx_r13", "ctx_r14", "ctx_r15", "ctx_r16", "ctx_r17", "ctx_r18", "ctx_r19", "ctx_r20", "ctx_r21", "ctx_r22", "formTicketSim", "invalid", "typeRequest", "errorMinQuantity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedDistrict", "selected<PERSON>om<PERSON><PERSON>", "errorMaxQuantity", "ListOrderSimTicketComponent_div_142_Template_p_dropdown_ngModelChange_6_listener", "_r43", "ctx_r42", "ticket", "ctx_r23", "ɵɵclassMap", "_c1", "ctx_r24", "getValueStatus", "statusOld", "_c2", "ctx_r25", "_c3", "ctx_r26", "_c4", "ctx_r27", "_c5", "ctx_r28", "ctx_r29", "ɵɵtemplate", "ListOrderSimTicketComponent_div_184_small_3_Template", "ListOrderSimTicketComponent_div_184_Template_textarea_ngModelChange_5_listener", "_r46", "ctx_r45", "cause", "ListOrderSimTicketComponent_div_184_Template_textarea_input_5_listener", "ctx_r47", "checkVisibleAndRequired", "ListOrderSimTicketComponent_div_184_Template_textarea_keydown_5_listener", "ctx_r48", "onKeyDownCause", "ɵɵtextInterpolate1", "ctx_r30", "status", "isRequiredNote", "ctx_r31", "ctx_r32", "ctx_r33", "ctx_r49", "ListOrderSimTicketComponent_div_228_ng_template_9_input_11_Template_input_ngModelChange_0_listener", "_r59", "note_r51", "$implicit", "content", "ListOrderSimTicketComponent_div_228_ng_template_9_input_11_Template_input_keydown_0_listener", "ctx_r60", "onKeyDownNoteContent", "ctx_r54", "getValueNote", "ctx_r55", "ctx_r56", "ListOrderSimTicketComponent_div_228_ng_template_9_input_11_Template", "ListOrderSimTicketComponent_div_228_ng_template_9_span_12_Template", "ListOrderSimTicketComponent_div_228_ng_template_9_small_13_Template", "ListOrderSimTicketComponent_div_228_ng_template_9_small_14_Template", "ctx_r50", "mapForm", "id", "i_r52", "userName", "ɵɵpipeBind2", "createdDate", "controls", "dirty", "errors", "required", "max<PERSON><PERSON><PERSON>", "ListOrderSimTicketComponent_div_228_ng_template_8_Template", "ListOrderSimTicketComponent_div_228_ng_template_9_Template", "ctx_r34", "listNotes", "ctx_r66", "ListOrderSimTicketComponent_div_236_ng_template_3_th_5_Template", "ctx_r64", "ListOrderSimTicketComponent_div_236_ng_template_4_td_5_Template_p_button_click_1_listener", "_r72", "i_r68", "rowIndex", "ctx_r70", "removeImsi", "ctx_r69", "isShowImsiInput", "ListOrderSimTicketComponent_div_236_ng_template_4_td_5_Template", "item_r67", "ctx_r65", "ListOrderSimTicketComponent_div_236_ng_template_3_Template", "ListOrderSimTicketComponent_div_236_ng_template_4_Template", "ctx_r35", "listImsis", "ctx_r36", "getFirstErrorMessage", "ctx_r73", "formUpdateOrderSim", "isEnableButtonSave", "isEmptyListImsi", "length", "isFormValid", "ListOrderSimTicketComponent_div_246_Template_p_button_click_1_listener", "_r75", "ctx_r74", "isShowUpdateRequest", "ListOrderSimTicketComponent_div_246_p_button_2_Template", "ctx_r37", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵpureFunction1", "_c6", "PERMISSIONS", "TICKET", "UPDATE", "ListOrderSimTicketComponent", "constructor", "ticketService", "accountService", "simTicketService", "logHandleTicketService", "cdr", "formBuilder", "injector", "maxDateFrom", "Date", "minDateTo", "maxDateTo", "disableSelectDistrict", "disableSelectCommune", "changeTable", "ngOnInit", "me", "userInfo", "sessionService", "searchHistoryOrder", "fromDate", "toDate", "formSearchHistoryOrder", "group", "isShowCreateRequest", "userType", "USER_TYPE", "newImsi", "contactName", "contactEmail", "contactPhone", "note", "type", "REQUEST_TYPE", "ORDER_SIM", "assigneeId", "address", "quantity", "commune", "district", "province", "detail<PERSON><PERSON><PERSON>", "created<PERSON>y", "formMailInput", "imsi", "listTicketType", "label", "value", "listTicketStatusSelected", "mapTicketStatus", "isShowStatus", "isRequiredStatus", "isShowNote", "isShowAssignee", "isRequiredAssignee", "isShowListImsi", "isShowListNote", "isShowOrder", "isShowMesReqImsi", "isShowMesMaxLenImsi", "isShowMesExistImsi", "listTicketStatus", "email", "dateTo", "dateFrom", "optionAddress", "districtCode", "name", "key", "size", "align", "isShow", "ADMIN", "isSort", "isShowTooltip", "style", "display", "max<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "funcConvertText", "utilService", "convertDateToString", "funcGetClassname", "REQUEST_STATUS", "NEW", "RECEIVED", "IN_PROGRESS", "REJECT", "DONE", "columnsHistory", "convertLongDateToString", "optionTableHistory", "hasShowIndex", "paginator", "dataSetHistory", "total", "hasClearSelected", "hasShowChoose", "hasShowToggleColumn", "action", "icon", "tooltip", "func", "item", "handleRequest", "funcAppear", "CUSTOMER", "updatedBy", "PROVINCE", "DISTRICT", "openModalOrder", "formSearchTicket", "getListProvince", "listActivatedAccount", "page", "limit", "params", "dataParams", "Object", "keys", "for<PERSON>ach", "getTime", "messageCommonService", "onload", "searchTicket", "response", "totalElements", "listAssigneeId", "Array", "from", "Set", "filter", "map", "updateBy", "push", "statusCheckListId", "getListActivatedAccount", "undefined", "includes", "offload", "resetTicket", "onSubmitSearch", "onSubmitSearchHistory", "searchHistory", "ticketId", "itemInTable", "el", "code", "createOrderSim", "bodySend", "trim", "createTicket", "resp", "success", "getDetailTicketConfig", "resp1", "array", "info", "emailInfos", "userId", "sendMailNotify", "updateOrderSim", "listLog", "createSimTicketBody", "userCustomerId", "userHandleId", "imsis", "updateTicket", "create", "res", "fullName", "phone", "getDetailTicket", "noWhitespaceValidator", "initVisibleAndRequired", "listEmail", "preventCharacter", "event", "ctrl<PERSON>ey", "keyCode", "preventDefault", "checkQuantity", "onSelectProvince", "onSelectDistrict", "onSelectCommune", "onChangeDateFrom", "onChangeDateTo", "addImsi", "isNaN", "Number", "i", "splice", "checkImsiInput", "toString", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slice", "detectChanges", "values", "every", "formGroup", "valid", "control", "isWhitespace", "<PERSON><PERSON><PERSON><PERSON>", "whitespace", "onKeyDownNote", "trimStart", "replace", "onKeyDownContent", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "FormBuilder", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "ListOrderSimTicketComponent_Template", "rf", "ctx", "ListOrderSimTicketComponent_p_button_6_Template", "ListOrderSimTicketComponent_Template_form_ngSubmit_7_listener", "ListOrderSimTicketComponent_div_10_Template", "ListOrderSimTicketComponent_Template_p_dropdown_ngModelChange_13_listener", "ListOrderSimTicketComponent_Template_input_ngModelChange_18_listener", "ListOrderSimTicketComponent_Template_input_ngModelChange_23_listener", "ListOrderSimTicketComponent_Template_input_keydown_23_listener", "ListOrderSimTicketComponent_Template_p_calendar_ngModelChange_28_listener", "ListOrderSimTicketComponent_Template_p_calendar_onSelect_28_listener", "ListOrderSimTicketComponent_Template_p_calendar_onInput_28_listener", "ListOrderSimTicketComponent_Template_p_calendar_ngModelChange_33_listener", "ListOrderSimTicketComponent_Template_p_calendar_onSelect_33_listener", "ListOrderSimTicketComponent_Template_p_calendar_onInput_33_listener", "ListOrderSimTicketComponent_table_vnpt_38_Template", "ListOrderSimTicketComponent_table_vnpt_39_Template", "ListOrderSimTicketComponent_Template_p_dialog_visibleChange_41_listener", "ListOrderSimTicketComponent_Template_form_ngSubmit_42_listener", "ListOrderSimTicketComponent_Template_input_ngModelChange_50_listener", "ListOrderSimTicketComponent_small_54_Template", "ListOrderSimTicketComponent_small_55_Template", "ListOrderSimTicketComponent_small_56_Template", "ListOrderSimTicketComponent_Template_input_ngModelChange_63_listener", "ListOrderSimTicketComponent_small_67_Template", "ListOrderSimTicketComponent_small_68_Template", "ListOrderSimTicketComponent_small_69_Template", "ListOrderSimTicketComponent_Template_input_ngModelChange_76_listener", "ListOrderSimTicketComponent_Template_input_keydown_76_listener", "ListOrderSimTicketComponent_small_80_Template", "ListOrderSimTicketComponent_small_81_Template", "ListOrderSimTicketComponent_Template_p_inputNumber_ngModelChange_88_listener", "ListOrderSimTicketComponent_Template_p_inputNumber_onInput_88_listener", "ListOrderSimTicketComponent_small_92_Template", "ListOrderSimTicketComponent_small_93_Template", "ListOrderSimTicketComponent_small_94_Template", "ListOrderSimTicketComponent_Template_vnpt_select_valueChange_102_listener", "ListOrderSimTicketComponent_Template_vnpt_select_onchange_102_listener", "ListOrderSimTicketComponent_Template_vnpt_select_valueChange_103_listener", "ListOrderSimTicketComponent_Template_vnpt_select_onchange_103_listener", "ListOrderSimTicketComponent_Template_vnpt_select_valueChange_104_listener", "ListOrderSimTicketComponent_Template_vnpt_select_onchange_104_listener", "ListOrderSimTicketComponent_Template_input_ngModelChange_107_listener", "ListOrderSimTicketComponent_small_111_Template", "ListOrderSimTicketComponent_small_112_Template", "ListOrderSimTicketComponent_small_113_Template", "ListOrderSimTicketComponent_Template_textarea_ngModelChange_118_listener", "ListOrderSimTicketComponent_Template_textarea_keydown_118_listener", "ListOrderSimTicketComponent_small_122_Template", "ListOrderSimTicketComponent_small_123_Template", "ListOrderSimTicketComponent_Template_textarea_ngModelChange_128_listener", "ListOrderSimTicketComponent_Template_textarea_keydown_128_listener", "ListOrderSimTicketComponent_small_132_Template", "ListOrderSimTicketComponent_small_133_Template", "ListOrderSimTicketComponent_Template_p_button_click_135_listener", "ListOrderSimTicketComponent_p_button_136_Template", "ListOrderSimTicketComponent_Template_p_dialog_visibleChange_137_listener", "ListOrderSimTicketComponent_Template_form_ngSubmit_138_listener", "ListOrderSimTicketComponent_div_142_Template", "ListOrderSimTicketComponent_Template_input_ngModelChange_149_listener", "ListOrderSimTicketComponent_Template_input_ngModelChange_156_listener", "ListOrderSimTicketComponent_Template_input_keydown_156_listener", "ListOrderSimTicketComponent_Template_textarea_ngModelChange_163_listener", "ListOrderSimTicketComponent_Template_p_dropdown_ngModelChange_168_listener", "ListOrderSimTicketComponent_Template_p_dropdown_onChange_168_listener", "ListOrderSimTicketComponent_span_175_Template", "ListOrderSimTicketComponent_span_176_Template", "ListOrderSimTicketComponent_span_177_Template", "ListOrderSimTicketComponent_span_178_Template", "ListOrderSimTicketComponent_span_179_Template", "ListOrderSimTicketComponent_small_183_Template", "ListOrderSimTicketComponent_div_184_Template", "ListOrderSimTicketComponent_small_188_Template", "ListOrderSimTicketComponent_small_189_Template", "ListOrderSimTicketComponent_Template_input_ngModelChange_198_listener", "ListOrderSimTicketComponent_Template_input_ngModelChange_205_listener", "ListOrderSimTicketComponent_Template_input_keydown_205_listener", "ListOrderSimTicketComponent_Template_input_ngModelChange_210_listener", "ListOrderSimTicketComponent_Template_input_keydown_210_listener", "ListOrderSimTicketComponent_Template_input_ngModelChange_215_listener", "ListOrderSimTicketComponent_Template_input_keydown_215_listener", "ListOrderSimTicketComponent_Template_vnpt_select_valueChange_220_listener", "ListOrderSimTicketComponent_Template_vnpt_select_onchange_220_listener", "ListOrderSimTicketComponent_small_224_Template", "ListOrderSimTicketComponent_div_228_Template", "ListOrderSimTicketComponent_div_236_Template", "ListOrderSimTicketComponent_Template_button_click_240_listener", "ListOrderSimTicketComponent_Template_input_ngModelChange_241_listener", "ListOrderSimTicketComponent_Template_input_input_241_listener", "ListOrderSimTicketComponent_Template_input_keydown_241_listener", "ListOrderSimTicketComponent_small_245_Template", "ListOrderSimTicketComponent_div_246_Template", "ListOrderSimTicketComponent_Template_p_dialog_visibleChange_247_listener", "ListOrderSimTicketComponent_Template_form_ngSubmit_248_listener", "ListOrderSimTicketComponent_Template_p_calendar_ngModelChange_252_listener", "ListOrderSimTicketComponent_Template_p_calendar_onSelect_252_listener", "ListOrderSimTicketComponent_Template_p_calendar_onInput_252_listener", "ListOrderSimTicketComponent_Template_p_calendar_ngModelChange_257_listener", "ListOrderSimTicketComponent_Template_p_calendar_onSelect_257_listener", "ListOrderSimTicketComponent_Template_p_calendar_onInput_257_listener", "items", "home", "CREATE", "ɵɵstyleMap", "_c7", "pattern", "_c8", "_c9", "_c10", "_c11", "_c12"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\ticket\\list\\order-sim\\app.list.order-sim.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\ticket\\list\\order-sim\\app.list.order-sim.component.html"], "sourcesContent": ["import {ChangeDetectorRef, Component, Inject, Injector, OnInit} from \"@angular/core\";\r\nimport {ComponentBase} from \"../../../../component.base\";\r\nimport {TicketService} from \"../../../../service/ticket/TicketService\";\r\nimport {AccountService} from \"../../../../service/account/AccountService\";\r\nimport {AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators} from \"@angular/forms\";\r\nimport {MenuItem} from \"primeng/api\";\r\nimport {ColumnInfo, OptionTable} from \"../../../common-module/table/table.component\";\r\nimport {CONSTANTS} from \"../../../../service/comon/constants\";\r\nimport {SimTicketService} from \"../../../../service/ticket/SimTicketService\";\r\nimport {LogHandleTicketService} from \"../../../../service/ticket/LogHandleTicketService\";\r\nimport {b, el} from \"@fullcalendar/core/internal-common\";\r\nimport fa from \"suneditor/src/lang/fa\";\r\n\r\n@Component({\r\n    selector: \"list-order-sim-ticket\",\r\n    templateUrl: './app.list.order-sim.component.html'\r\n})\r\n\r\nexport class ListOrderSimTicketComponent extends ComponentBase implements OnInit {\r\n    items: MenuItem[];\r\n    home: MenuItem\r\n    searchInfo: {\r\n        provinceCode: string | null,\r\n        email: string | null,\r\n        contactPhone: string | null,\r\n        contactName: string | null,\r\n        type: number | null,\r\n        status: any | null,\r\n        dateFrom: Date | null,\r\n        dateTo: Date | null,\r\n    };\r\n    columns: Array<ColumnInfo>;\r\n    dataSet: {\r\n        content: Array<any>,\r\n        total: number\r\n    };\r\n    optionAddress: {\r\n        provinceCode: number | null,\r\n        districtCode: number | null,\r\n    }\r\n    selectItems: Array<any>;\r\n    optionTable: OptionTable;\r\n    pageNumber: number;\r\n    pageSize: number;\r\n    sort: string;\r\n    formSearchTicket: any;\r\n    listProvince: Array<any>;\r\n    listTicketType: Array<any>;\r\n    listTicketStatus: Array<any>;\r\n    listTicketStatusSelected: Array<any>;\r\n    mapTicketStatus: any;\r\n    listEmail\r\n        : Array<any>;\r\n    isShowCreateRequest: boolean;\r\n    isShowUpdateRequest: boolean;\r\n    formTicketSim: any;\r\n    formUpdateOrderSim: any\r\n    maxDateFrom: Date | number | string | null = new Date();\r\n    minDateTo: Date | number | string | null = null;\r\n    maxDateTo: Date | number | string | null = new Date();\r\n    ticket: {\r\n        id: number\r\n        contactName: string | null,\r\n        contactEmail: string | null,\r\n        contactPhone: string | null,\r\n        content: string | null,\r\n        note: string | null,\r\n        cause: string | null,\r\n        type: number | null, // 0: thay thế sim, 1: test sim, 2: order sim\r\n        status: number | null,\r\n        statusOld?: number | null,\r\n        assigneeId: number | null\r\n        detailAddress: string | null,\r\n        address: string | null,\r\n        quantity: number | null\r\n        province: any | null,\r\n        district: any | null,\r\n        commune: any | null,\r\n        createdBy: any | null,\r\n        provinceCode: string| null,\r\n    };\r\n    listNotes: any[];\r\n    typeRequest: string\r\n    userInfo: any\r\n    userType: any\r\n    errorMinQuantity: boolean\r\n    errorMaxQuantity: boolean\r\n    selectedProvince: boolean = false;\r\n    selectedDistrict: boolean = false;\r\n    selectedCommune: boolean = false;\r\n    disableSelectDistrict: boolean = true\r\n    disableSelectCommune: boolean = true\r\n    listImsis: number[] = [];\r\n    newImsi: string;\r\n    isShowStatus: boolean\r\n    isRequiredStatus: boolean\r\n    isShowNote: boolean\r\n    isRequiredNote: boolean\r\n    isShowAssignee: boolean\r\n    isRequiredAssignee: boolean\r\n    isShowListImsi: boolean\r\n    isShowListNote: boolean\r\n    isShowImsiInput: boolean\r\n    isEmptyListImsi: boolean\r\n    isEnableButtonSave: boolean\r\n    isShowOrder: boolean\r\n    formSearchHistoryOrder: any;\r\n    isShowMesReqImsi: boolean;\r\n    isShowMesMaxLenImsi: boolean;\r\n    isShowMesExistImsi: boolean;\r\n    searchHistoryOrder: {\r\n        fromDate: Date | null,\r\n        toDate: Date | null\r\n    }\r\n    itemInTable: any\r\n    columnsHistory: any\r\n    optionTableHistory: any\r\n    dataSetHistory: any\r\n    mapForm: any = {}\r\n    formMailInput: any\r\n    listActivatedAccount: number[];\r\n    changeTable: boolean = false;\r\n    constructor(\r\n        @Inject(TicketService) private ticketService: TicketService,\r\n        @Inject(AccountService) private accountService: AccountService,\r\n        @Inject(SimTicketService) private simTicketService: SimTicketService,\r\n        @Inject(LogHandleTicketService) private logHandleTicketService: LogHandleTicketService,\r\n        private cdr: ChangeDetectorRef,\r\n        private formBuilder: FormBuilder,\r\n        private injector: Injector) {\r\n        super(injector);\r\n    }\r\n\r\n    ngOnInit() {\r\n        let me = this;\r\n        me.changeTable = false;\r\n        this.userInfo = this.sessionService.userInfo;\r\n        this.searchHistoryOrder = {\r\n            fromDate: null,\r\n            toDate: null\r\n        }\r\n        this.formSearchHistoryOrder = this.formBuilder.group(this.searchHistoryOrder);\r\n        this.isShowCreateRequest = false;\r\n        this.isShowUpdateRequest = false;\r\n        this.isEnableButtonSave = false;\r\n        this.typeRequest = 'create'\r\n        this.userType = CONSTANTS.USER_TYPE;\r\n        this.listImsis = [];\r\n        this.listNotes = [];\r\n        this.newImsi = '';\r\n        this.ticket = {\r\n            id: null,\r\n            contactName: null,\r\n            contactEmail: null,\r\n            contactPhone: null,\r\n            content: null,\r\n            note: null,\r\n            cause: null,\r\n            type: CONSTANTS.REQUEST_TYPE.ORDER_SIM, // 0: thay thế sim, 1: test sim, 2: đặt mua sim\r\n            status: null,\r\n            statusOld: null,\r\n            assigneeId: null,\r\n            address: null,\r\n            quantity: null,\r\n            commune: null,\r\n            district: null,\r\n            province: null,\r\n            detailAddress: null,\r\n            createdBy: null,\r\n            provinceCode: null,\r\n        };\r\n        this.formMailInput = this.formBuilder.group({imsi: \"\"});\r\n        this.listTicketType = [\r\n            {\r\n                label: this.tranService.translate('ticket.type.orderSim'),\r\n                value: 2\r\n            }\r\n        ],\r\n            this.listTicketStatusSelected = []\r\n        this.mapTicketStatus = {\r\n            0: [{\r\n                label: me.tranService.translate('ticket.status.received'),\r\n                value: 1\r\n            },\r\n                {\r\n                    label: me.tranService.translate('ticket.status.reject'),\r\n                    value: 3\r\n                },\r\n            ],\r\n            1: [\r\n                {\r\n                    label: me.tranService.translate('ticket.status.inProgress'),\r\n                    value: 2\r\n                },\r\n                {\r\n                    label: me.tranService.translate('ticket.status.reject'),\r\n                    value: 3\r\n                }\r\n            ],\r\n            2: [\r\n                {\r\n                    label: me.tranService.translate('ticket.status.done'),\r\n                    value: 4\r\n                },\r\n                {\r\n                    label: me.tranService.translate('ticket.status.reject'),\r\n                    value: 3\r\n                }\r\n            ]\r\n        }\r\n        this.isShowStatus = true;\r\n        this.isRequiredStatus = true;\r\n        this.isShowNote = true;\r\n        this.isRequiredNote = true;\r\n        this.isShowAssignee = true;\r\n        this.isRequiredAssignee = true;\r\n        this.isShowListImsi = false;\r\n        this.isShowListNote = true;\r\n        this.isShowImsiInput = true;\r\n        this.isEmptyListImsi = false;\r\n        this.isEnableButtonSave = false;\r\n        this.isShowOrder = false;\r\n        this.isShowMesReqImsi = false;\r\n        this.isShowMesMaxLenImsi = false;\r\n        this.isShowMesExistImsi = false;\r\n        this.listTicketStatus = [\r\n            {\r\n                label: me.tranService.translate('ticket.status.new'),\r\n                value: 0\r\n            },\r\n            {\r\n                label: me.tranService.translate('ticket.status.received'),\r\n                value: 1\r\n            },\r\n            {\r\n                label: me.tranService.translate('ticket.status.inProgress'),\r\n                value: 2\r\n            },\r\n            {\r\n                label: me.tranService.translate('ticket.status.reject'),\r\n                value: 3\r\n            },\r\n            {\r\n                label: me.tranService.translate('ticket.status.done'),\r\n                value: 4\r\n            }\r\n        ]\r\n        this.searchInfo = {\r\n            provinceCode: null,\r\n            email: null,\r\n            contactPhone: null,\r\n            contactName: null,\r\n            type: CONSTANTS.REQUEST_TYPE.ORDER_SIM,\r\n            status: null,\r\n            dateTo: null,\r\n            dateFrom: null,\r\n        }\r\n        this.optionAddress = {\r\n            provinceCode: 1,\r\n            districtCode: 1,\r\n        }\r\n        this.columns = [\r\n            {\r\n                name: this.tranService.translate(\"ticket.label.province\"),\r\n                key: \"provinceName\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: this.userInfo.type == CONSTANTS.USER_TYPE.ADMIN,\r\n                isSort: true\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ticket.label.customerName\"),\r\n                key: \"contactName\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                isShowTooltip: true,\r\n                style: {\r\n                    display: 'inline-block',\r\n                    maxWidth: '350px',\r\n                    overflow: 'hidden',\r\n                    textOverflow: 'ellipsis'\r\n                }\r\n            }, {\r\n                name: this.tranService.translate(\"ticket.label.email\"),\r\n                key: \"contactEmail\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                isShowTooltip: true,\r\n                style: {\r\n                    display: 'inline-block',\r\n                    maxWidth: '350px',\r\n                    overflow: 'hidden',\r\n                    textOverflow: 'ellipsis'\r\n                }\r\n            }, {\r\n                name: this.tranService.translate(\"ticket.label.phone\"),\r\n                key: \"contactPhone\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true\r\n            }, {\r\n                name: this.tranService.translate(\"ticket.label.quantity\"),\r\n                key: \"quantity\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true\r\n            }, {\r\n                name: this.tranService.translate(\"ticket.label.deliveryAddress\"),\r\n                key: \"address\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                isShowTooltip: true,\r\n                style: {\r\n                    display: 'inline-block',\r\n                    maxWidth: '350px',\r\n                    overflow: 'hidden',\r\n                    textOverflow: 'ellipsis'\r\n                }\r\n            }, {\r\n                name: this.tranService.translate(\"ticket.label.content\"),\r\n                key: \"content\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                isShowTooltip: true,\r\n                style: {\r\n                    display: 'inline-block',\r\n                    maxWidth: '350px',\r\n                    overflow: 'hidden',\r\n                    textOverflow: 'ellipsis'\r\n                }\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ticket.label.createdDate\"),\r\n                key: \"createdDate\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcConvertText(value) {\r\n                    if (value == null) return null;\r\n                    return me.utilService.convertDateToString(new Date(value))\r\n                },\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ticket.label.updatedDate\"),\r\n                key: \"updatedDate\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcConvertText(value) {\r\n                    if (value == null) return null;\r\n                    return me.utilService.convertDateToString(new Date(value))\r\n                },\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ticket.label.updateBy\"),\r\n                key: \"updatedByName\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true\r\n            }, {\r\n                name: this.tranService.translate(\"ticket.label.status\"),\r\n                key: \"status\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcGetClassname: (value) => {\r\n                    if (value == CONSTANTS.REQUEST_STATUS.NEW) {\r\n                        return ['p-2', 'text-white', \"bg-cyan-300\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\r\n                        return ['p-2', 'text-white', \"bg-bluegray-500\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\r\n                        return ['p-2', 'text-white', \"bg-orange-400\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\r\n                        return ['p-2', 'text-white', \"bg-red-500\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\r\n                        return ['p-2', 'text-white', \"bg-green-500\", \"border-round\", \"inline-block\"];\r\n                    }\r\n                    return '';\r\n                },\r\n                funcConvertText: function (value) {\r\n                    if (value == CONSTANTS.REQUEST_STATUS.NEW) {\r\n                        return me.tranService.translate(\"ticket.status.new\");\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\r\n                        return me.tranService.translate(\"ticket.status.received\");\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\r\n                        return me.tranService.translate(\"ticket.status.inProgress\");\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\r\n                        return me.tranService.translate(\"ticket.status.reject\");\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\r\n                        return me.tranService.translate(\"ticket.status.done\");\r\n                    }\r\n                    return \"\";\r\n                }\r\n            }\r\n        ];\r\n\r\n        this.columnsHistory = [\r\n            {\r\n                name: this.tranService.translate(\"ticket.label.time\"),\r\n                key: \"createdDate\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n                funcConvertText: function (value) {\r\n                    return me.utilService.convertLongDateToString(value);\r\n                }\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ticket.label.implementer\"),\r\n                key: \"userName\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ticket.label.content\"),\r\n                key: \"status\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n                funcGetClassname: (value) => {\r\n                    if (value == CONSTANTS.REQUEST_STATUS.NEW) {\r\n                        return ['p-2', 'text-white', \"bg-cyan-300\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\r\n                        return ['p-2', 'text-white', \"bg-bluegray-500\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\r\n                        return ['p-2', 'text-white', \"bg-orange-400\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\r\n                        return ['p-2', 'text-white', \"bg-red-500\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\r\n                        return ['p-2', 'text-white', \"bg-green-500\", \"border-round\", \"inline-block\"];\r\n                    }\r\n                    return '';\r\n                },\r\n                funcConvertText: function (value) {\r\n                    if (value == CONSTANTS.REQUEST_STATUS.NEW) {\r\n                        return me.tranService.translate(\"ticket.status.new\");\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\r\n                        return me.tranService.translate(\"ticket.status.received\");\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\r\n                        return me.tranService.translate(\"ticket.status.inProgress\");\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\r\n                        return me.tranService.translate(\"ticket.status.reject\");\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\r\n                        return me.tranService.translate(\"ticket.status.done\");\r\n                    }\r\n                    return \"\";\r\n                }\r\n            }\r\n\r\n        ];\r\n\r\n        this.optionTableHistory = {\r\n            hasShowIndex: true,\r\n            paginator: false\r\n        }\r\n\r\n        this.dataSetHistory = {\r\n            content: [],\r\n            total: 0\r\n        },\r\n\r\n\r\n            this.optionTable = {\r\n                hasClearSelected: false,\r\n                hasShowChoose: false,\r\n                hasShowIndex: true,\r\n                hasShowToggleColumn: false,\r\n                action: [\r\n                    {\r\n                        icon: \"pi pi-info-circle\",\r\n                        tooltip: this.tranService.translate(\"global.button.view\"),\r\n                        func: function (id, item) {\r\n                            me.handleRequest(id, item, 'view')\r\n                        },\r\n                    },\r\n                    {\r\n                        icon: \"pi pi-file-edit\",\r\n                        tooltip: this.tranService.translate(\"global.button.edit\"),\r\n                        func: function (id, item) {\r\n                            me.handleRequest(id, item, 'update')\r\n                        },\r\n                        funcAppear: function (id, item) {\r\n                            if (me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER || me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN) return false;\r\n                            if (!item.updatedBy && !item.assigneeId && (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE || me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;\r\n                            if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy !== me.userInfo.id) ||\r\n                                (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.assigneeId != null)) {\r\n                                return false;\r\n                            }\r\n                            if (me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true\r\n                            else return false;\r\n                        }\r\n                    },\r\n                    {\r\n                        icon: \"pi pi-eye\",\r\n                        tooltip: this.tranService.translate(\"ticket.label.orderHistory\"),\r\n                        func: function (id, item) {\r\n                            me.openModalOrder(id, item)\r\n                        },\r\n                    },\r\n                ]\r\n            }\r\n        this.pageNumber = 0;\r\n        this.pageSize = 10;\r\n        this.sort = \"createdDate,desc\";\r\n        this.dataSet = {\r\n            content: [],\r\n            total: 0\r\n        },\r\n            this.formSearchTicket = this.formBuilder.group(this.searchInfo);\r\n        this.formTicketSim = this.formBuilder.group(this.ticket);\r\n        this.formUpdateOrderSim = this.formBuilder.group(this.ticket);\r\n        this.getListProvince();\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n        this.errorMinQuantity = false;\r\n        this.errorMaxQuantity = false;\r\n        this.listActivatedAccount = [];\r\n    }\r\n\r\n    search(page, limit, sort, params) {\r\n        let me = this;\r\n        me.changeTable = false\r\n        this.pageNumber = page;\r\n        this.pageSize = limit;\r\n        this.sort = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        Object.keys(this.searchInfo).forEach(key => {\r\n            if (this.searchInfo[key] != null) {\r\n                if (key == \"dateFrom\") {\r\n                    dataParams[\"dateFrom\"] = this.searchInfo.dateFrom.getTime();\r\n                } else if (key == \"dateTo\") {\r\n                    dataParams[\"dateTo\"] = this.searchInfo.dateTo.getTime();\r\n                } else {\r\n                    dataParams[key] = this.searchInfo[key];\r\n                }\r\n            }\r\n        })\r\n        this.dataSet = {\r\n            content: [],\r\n            total: 0\r\n        }\r\n        me.messageCommonService.onload();\r\n        this.ticketService.searchTicket(dataParams, (response) => {\r\n            me.dataSet = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n            if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE ||\r\n                me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) {\r\n                let listAssigneeId = Array.from(new Set(me.dataSet.content.filter(item => item.assigneeId !== null)\r\n                    .map(item => item.assigneeId as number)));\r\n\r\n                me.dataSet.content.forEach(item => {\r\n                    if (item.updateBy !== null) {\r\n                        listAssigneeId.push(item.updateBy as number);\r\n                    }\r\n                });\r\n\r\n                const statusCheckListId = Array.from(new Set(listAssigneeId));\r\n\r\n                me.accountService.getListActivatedAccount(statusCheckListId, (response) => {\r\n                    me.listActivatedAccount = response;\r\n                    me.optionTable = {\r\n                        hasClearSelected: false,\r\n                        hasShowChoose: false,\r\n                        hasShowIndex: true,\r\n                        hasShowToggleColumn: false,\r\n                        action: [\r\n                            {\r\n                                icon: \"pi pi-info-circle\",\r\n                                tooltip: this.tranService.translate(\"global.button.view\"),\r\n                                func: function (id, item) {\r\n                                    me.handleRequest(id, item, 'view')\r\n                                },\r\n                            },\r\n                            {\r\n                                icon: \"pi pi-file-edit\",\r\n                                tooltip: this.tranService.translate(\"global.button.edit\"),\r\n                                func: function (id, item) {\r\n                                    me.handleRequest(id, item, 'update')\r\n                                },\r\n                                funcAppear: function (id, item) {\r\n                                    //admin và khách hàng không được sửa\r\n                                    if (me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER || me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN) return false;\r\n                                    if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE]) && ( me.listActivatedAccount === undefined || me.listActivatedAccount == null )) return true;\r\n                                    if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) && ((item.assigneeId != null && me.listActivatedAccount.includes(item.assigneeId)))) return false;\r\n            if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) && ((item.assigneeId == null && item.updatedBy != null && me.listActivatedAccount.includes(item.updatedBy) && item.updatedBy != me.userInfo.id))) return false;\r\n                                    if (!item.updatedBy && !item.assigneeId && (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE || me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;\r\n                                    if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) && ((item.assigneeId != null && !me.listActivatedAccount.includes(item.assigneeId)) || (item.updatedBy != null && !me.listActivatedAccount.includes(item.updatedBy)))) return true;\r\n                                    if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy !== me.userInfo.id) ||\r\n                                        (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.assigneeId != null)) {\r\n                                        return false;\r\n                                    }\r\n                                    if (me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true\r\n                                    else return false;\r\n                                }\r\n                            },\r\n                            {\r\n                                icon: \"pi pi-eye\",\r\n                                tooltip: this.tranService.translate(\"ticket.label.orderHistory\"),\r\n                                func: function (id, item) {\r\n                                    me.openModalOrder(id, item)\r\n                                },\r\n                            },\r\n                        ]\r\n                    }\r\n                    me.changeTable = true\r\n                })\r\n            }\r\n        }, null, () => {\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    resetTicket() {\r\n        let me = this;\r\n        this.ticket = {\r\n            id: null,\r\n            contactName: null,\r\n            contactEmail: null,\r\n            contactPhone: null,\r\n            content: null,\r\n            note: null,\r\n            cause: null,\r\n            type: CONSTANTS.REQUEST_TYPE.ORDER_SIM, // 0: thay thế sim,\r\n            status: null,\r\n            statusOld: null,\r\n            assigneeId: null,\r\n            address: null,\r\n            quantity: null,\r\n            province: null,\r\n            district: null,\r\n            commune: null,\r\n            detailAddress: null,\r\n            createdBy: null,\r\n            provinceCode: null,\r\n        };\r\n        me.selectedProvince = false;\r\n        me.selectedDistrict = false;\r\n        me.selectedCommune = false;\r\n    }\r\n\r\n    onSubmitSearch() {\r\n        this.pageNumber = 0;\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n    }\r\n\r\n    onSubmitSearchHistory() {\r\n        this.searchHistory(0, 99999999, this.sort, this.searchHistoryOrder);\r\n    }\r\n\r\n\r\n    searchHistory(page, limit, sort, params) {\r\n        let me = this;\r\n        let dataParams = {\r\n            page: page,\r\n            size: limit,\r\n            ticketId: me.itemInTable.id\r\n        }\r\n        Object.keys(params).forEach(key => {\r\n            if (params[key] != null) {\r\n                if (key == \"fromDate\") {\r\n                    dataParams[\"logDateFrom\"] = params.fromDate.getTime();\r\n                } else if (key == \"toDate\") {\r\n                    dataParams[\"logDateTo\"] = params.toDate.getTime();\r\n                } else {\r\n                    dataParams[key] = params[key];\r\n                }\r\n            }\r\n        })\r\n        me.messageCommonService.onload();\r\n        this.logHandleTicketService.search(dataParams, (response) => {\r\n            me.dataSetHistory = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n        }, null, () => {\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    getListProvince() {\r\n        this.accountService.getListProvince((response) => {\r\n            this.listProvince = response.map(el => {\r\n                return {\r\n                    ...el,\r\n                    display: `${el.code} - ${el.name}`\r\n                }\r\n            })\r\n        })\r\n    }\r\n\r\n    // tạo yêu cầu đặt sim\r\n    createOrderSim() {\r\n        let me = this;\r\n        this.messageCommonService.onload()\r\n        let bodySend = {\r\n            contactName: this.ticket.contactName,\r\n            contactEmail: this.ticket.contactEmail,\r\n            contactPhone: this.ticket.contactPhone,\r\n            content: this.ticket.content != null ? this.ticket.content.trim() : null,\r\n            address: this.ticket.detailAddress + \", \" + this.ticket.commune.name + \", \" + this.ticket.district.name + \", \" + this.ticket.province.name,\r\n            note: this.ticket.note != null ?this.ticket.note.trim() : null ,\r\n            type: this.ticket.type,\r\n            quantity: this.ticket.quantity,\r\n        }\r\n        this.ticketService.createTicket(bodySend, (resp) => {\r\n            me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n            me.isShowCreateRequest = false\r\n            me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)\r\n            // get mail admin tinh\r\n            // me.ticketService.getListAssignee({email : '', provinceCode : this.userInfo.provinceCode, page : 0, size: 99999999}, (respAssignee)=>{\r\n            //     let listProvinceConfig = respAssignee.content;\r\n            //     let array = []\r\n            //     for (let user of listProvinceConfig) {\r\n            //         array.push({\r\n            //             userId: user.id,\r\n            //             ticketId: resp.id\r\n            //         })\r\n            //     }\r\n            //     me.ticketService.sendMailNotify(array);\r\n            // })\r\n            // nếu KH đc gán cho GDV thì gửi mail cho GDV và danh sách admin đc cấu hình không thì chỉ gửi mail cho danh sách admin đc cấu hình\r\n            // get mail admin tinh dc cau hinh\r\n            me.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp1) => {\r\n                let array = []\r\n                for (let info of resp1.emailInfos) {\r\n                    array.push({\r\n                        userId: info.userId,\r\n                        ticketId: resp.id\r\n                    })\r\n                }\r\n                if (resp?.assigneeId) {\r\n                    array.push({\r\n                        userId: resp.assigneeId,\r\n                        ticketId: resp.id\r\n                    })\r\n                }\r\n                me.ticketService.sendMailNotify(array);\r\n            })\r\n        }, null, () => {\r\n            me.messageCommonService.offload()\r\n        })\r\n    }\r\n\r\n    updateOrderSim() {\r\n        let me = this;\r\n        me.messageCommonService.onload();\r\n        let bodySend = {\r\n            contactName: this.ticket.contactName,\r\n            contactEmail: this.ticket.contactEmail,\r\n            contactPhone: this.ticket.contactPhone,\r\n            content: this.ticket.content,\r\n            address: this.ticket.address,\r\n            note: this.ticket.note,\r\n            type: this.ticket.type,\r\n            status: this.ticket.status,\r\n            cause: this.ticket.cause,\r\n            assigneeId: this.ticket.assigneeId,\r\n            quantity: this.ticket.quantity,\r\n            listLog: this.listNotes,\r\n        }\r\n        let createSimTicketBody = {\r\n            ticketId: this.ticket.id,\r\n            userCustomerId: this.ticket.createdBy,\r\n            userHandleId: this.userInfo.id,\r\n            imsis: this.listImsis,\r\n        }\r\n        // update ticket\r\n\r\n        this.ticketService.updateTicket(this.ticket.id, bodySend, (resp) => {\r\n            me.isShowCreateRequest = false\r\n            me.isShowUpdateRequest = false;\r\n            // console.log(resp);\r\n\r\n            if (resp.assigneeId != null && resp.assigneeId != undefined) {\r\n                me.ticketService.sendMailNotify([{\r\n                    userId: resp.assigneeId,\r\n                    ticketId: resp.id\r\n                }])\r\n            }\r\n            if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.IN_PROGRESS && me.ticket.status == CONSTANTS.REQUEST_STATUS.DONE) {\r\n                me.simTicketService.create(createSimTicketBody, (res) => {\r\n                    me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n                    me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)\r\n\r\n                })\r\n            } else {\r\n                me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n                me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)\r\n            }\r\n        }, null, () => {\r\n            me.messageCommonService.offload()\r\n        })\r\n    }\r\n\r\n    showModalCreate() {\r\n        let me = this;\r\n        this.isShowCreateRequest = true\r\n        this.typeRequest = 'create'\r\n        this.resetTicket()\r\n        if (this.userInfo.type === CONSTANTS.USER_TYPE.CUSTOMER) {\r\n            this.ticket.contactName = this.userInfo.fullName;\r\n            this.ticket.contactPhone = this.userInfo.phone;\r\n            this.ticket.contactEmail = this.userInfo.email;\r\n        }\r\n        me.selectedProvince = false;\r\n        me.selectedDistrict = false;\r\n        me.selectedCommune = false;\r\n        this.formTicketSim = this.formBuilder.group(this.ticket)\r\n    }\r\n\r\n    handleRequest(id, item, typeRequest: string) {\r\n        let me = this\r\n        me.typeRequest = typeRequest;\r\n        this.isShowCreateRequest = false;\r\n        this.isShowUpdateRequest = true;\r\n        this.ticketService.getDetailTicket(item.id, (resp) => {\r\n            this.ticket = {\r\n                id: resp.id,\r\n                contactName: resp.contactName,\r\n                contactEmail: resp.contactEmail,\r\n                contactPhone: resp.contactPhone,\r\n                content: resp.content,\r\n                note: resp.note,\r\n                cause: resp.cause,\r\n                type: resp.type, // 0: thay thế sim, 1: test sim, 2: order sim\r\n                status: null,\r\n                statusOld: resp.status,\r\n                assigneeId: resp.assigneeId,\r\n                quantity: resp.quantity,\r\n                address: resp.address,\r\n                commune: null,\r\n                district: null,\r\n                province: null,\r\n                detailAddress: null,\r\n                createdBy: resp.createdBy,\r\n                provinceCode: resp.provinceCode,\r\n            }\r\n            this.logHandleTicketService.search({ticketId: this.ticket.id}, (res) => {\r\n                this.listNotes = res.content;\r\n                // for (let note of this.listNotes) {\r\n                //     this.mapForm[note.id] = this.formBuilder.group(note);\r\n                // }\r\n                this.listNotes.forEach(note => {\r\n                    this.mapForm[note.id] = this.formBuilder.group({\r\n                        content: ['', [Validators.required, Validators.maxLength(255), this.noWhitespaceValidator()]]\r\n                    });\r\n                });\r\n                me.initVisibleAndRequired();\r\n            })\r\n            this.simTicketService.search({ticketId: this.ticket.id, size: 1000}, (res) => {\r\n                    let imsis: number[] = [];\r\n                    res.content.forEach(item => {\r\n                        imsis.push(item.imsi);\r\n                    })\r\n                    this.listImsis = imsis;\r\n                    me.initVisibleAndRequired();\r\n                }\r\n            )\r\n            this.formUpdateOrderSim = this.formBuilder.group(this.ticket);\r\n            me.initVisibleAndRequired();\r\n        })\r\n\r\n        this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp) => {\r\n            this.listEmail = resp.emailInfos;\r\n        })\r\n    }\r\n\r\n    preventCharacter(event) {\r\n        if (event.ctrlKey) {\r\n            return;\r\n        }\r\n        if (event.keyCode == 8 || event.keyCode == 13 || event.keyCode == 37 || event.keyCode == 39) {\r\n            return;\r\n        }\r\n        // Chặn ký tự 'e', 'E' và dấu '+'\r\n        if (event.keyCode == 69 || event.keyCode == 101 || event.keyCode == 107 || event.keyCode == 187) {\r\n            event.preventDefault();\r\n        }\r\n        if (event.keyCode < 48 || event.keyCode > 57) {\r\n            event.preventDefault();\r\n        }\r\n    }\r\n\r\n    checkQuantity(event) {\r\n        let me = this;\r\n        if (event.value < 1) {\r\n            me.errorMinQuantity = true;\r\n        } else {\r\n            me.errorMinQuantity = false;\r\n        }\r\n        if (event.value > 99999) {\r\n            me.errorMaxQuantity = true;\r\n        } else {\r\n            me.errorMaxQuantity = false;\r\n        }\r\n    }\r\n\r\n    onSelectProvince(event) {\r\n        let me = this;\r\n        if (me.ticket.province != null) {\r\n            me.ticket.district = null\r\n            me.ticket.commune = null\r\n            me.optionAddress.provinceCode = event.code;\r\n            me.disableSelectDistrict = false;\r\n            me.disableSelectCommune = true;\r\n            me.selectedProvince = true;\r\n        } else {\r\n            me.optionAddress.provinceCode = 1\r\n            me.ticket.district = null\r\n            me.ticket.commune = null\r\n            me.disableSelectDistrict = true;\r\n            me.disableSelectCommune = true;\r\n            me.selectedProvince = false;\r\n        }\r\n    }\r\n\r\n    onSelectDistrict(event) {\r\n        let me = this;\r\n        if (me.ticket.district != null) {\r\n            me.ticket.commune = null\r\n            me.optionAddress.districtCode = event.code;\r\n            me.disableSelectCommune = false;\r\n            me.selectedDistrict = true;\r\n        } else {\r\n            me.ticket.commune = null\r\n            me.optionAddress.districtCode = 1;\r\n            me.disableSelectCommune = true;\r\n            me.selectedDistrict = false\r\n        }\r\n    }\r\n\r\n    onSelectCommune(event) {\r\n        let me = this;\r\n        if (me.ticket.commune != null) {\r\n            me.selectedCommune = true\r\n        } else {\r\n            me.selectedCommune = false\r\n        }\r\n    }\r\n\r\n    onChangeDateFrom(value) {\r\n        if (value) {\r\n            this.minDateTo = value;\r\n        } else {\r\n            this.minDateTo = null\r\n        }\r\n    }\r\n\r\n    onChangeDateTo(value) {\r\n        if (value) {\r\n            this.maxDateFrom = value;\r\n        } else {\r\n            this.maxDateFrom = new Date();\r\n        }\r\n    }\r\n\r\n    addImsi() {\r\n        let me = this;\r\n        if (this.newImsi !== undefined && this.newImsi && !isNaN(Number(this.newImsi))) {\r\n            this.listImsis.push(Number(this.newImsi));\r\n            this.newImsi = '';\r\n        }\r\n        if (this.listImsis.length == 0) {\r\n            this.isEmptyListImsi = true\r\n        } else {\r\n            this.isEmptyListImsi = false\r\n        }\r\n        this.checkVisibleAndRequired()\r\n    }\r\n\r\n    removeImsi(i: number) {\r\n        let me = this;\r\n        this.listImsis.splice(i, 1);\r\n        if (this.listImsis.length == 0 && this.ticket.status == CONSTANTS.REQUEST_STATUS.DONE) {\r\n            this.isEmptyListImsi = true\r\n        } else {\r\n            this.isEmptyListImsi = false\r\n        }\r\n        this.checkVisibleAndRequired()\r\n    }\r\n\r\n    checkImsiInput() {\r\n        let me = this\r\n        if (this.newImsi !== undefined && this.newImsi != '') {\r\n            if (this.newImsi.toString().length > 18) {\r\n                me.isShowMesMaxLenImsi = true\r\n            } else {\r\n                me.isShowMesMaxLenImsi = false\r\n            }\r\n            if (this.listImsis.includes(Number((this.newImsi)))) {\r\n                me.isShowMesExistImsi = true;\r\n            } else {\r\n                me.isShowMesExistImsi = false;\r\n            }\r\n        }\r\n    }\r\n\r\n    getValueStatus(value) {\r\n        let me = this;\r\n        {\r\n            if (value == CONSTANTS.REQUEST_STATUS.NEW) {\r\n                return me.tranService.translate(\"ticket.status.new\");\r\n            } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\r\n                return me.tranService.translate(\"ticket.status.received\");\r\n            } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\r\n                return me.tranService.translate(\"ticket.status.inProgress\");\r\n            } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\r\n                return me.tranService.translate(\"ticket.status.reject\");\r\n            } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\r\n                return me.tranService.translate(\"ticket.status.done\");\r\n            }\r\n            return \"\";\r\n        }\r\n    }\r\n\r\n    getValueNote(content) {\r\n        let me = this;\r\n        let maxContentLength = 23; // Độ dài tối đa hiển thị nội dung\r\n        if (content != null)\r\n        return content.length > maxContentLength ? content.slice(0, maxContentLength) + '...' : content;\r\n    }\r\n\r\n    initVisibleAndRequired() {\r\n        let me = this;\r\n        // view\r\n        if (me.typeRequest == 'view') {\r\n            me.isShowStatus = false;\r\n            me.isRequiredStatus = false;\r\n            me.isShowImsiInput = false;\r\n            me.isShowNote = false;\r\n            me.isShowAssignee = false;\r\n            me.isShowMesReqImsi = false;\r\n            me.isShowMesMaxLenImsi = false;\r\n            if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.DONE) {\r\n                me.isShowListImsi = true;\r\n            } else {\r\n                me.isShowListImsi = false;\r\n            }\r\n            if (me.userInfo.type == me.userType.PROVINCE && me.ticket.assigneeId != null) {\r\n                me.isShowAssignee = true;\r\n            } else {\r\n                me.isShowAssignee = false;\r\n            }\r\n        }\r\n        if (me.typeRequest == 'update') {\r\n            // ghi chú, trạng thái, gán xử lý không bắt buộc\r\n            this.isRequiredNote = false\r\n            this.isRequiredStatus = false\r\n            this.isRequiredAssignee = false;\r\n\r\n            //tắt nút lưu\r\n            me.isEnableButtonSave = false;\r\n\r\n            //Hiển thị chuyển trạng thái, ghi chú\r\n            me.isShowStatus = true;\r\n            me.isShowNote = true;\r\n\r\n            //Chỉ hiển thị chuyển xử lý với admin tỉnh khi chưa chuyển xử lý\r\n            if (me.userInfo.type == me.userType.PROVINCE && me.ticket.assigneeId == null) {\r\n                me.isShowAssignee = true;\r\n            } else {\r\n                me.isShowAssignee = false;\r\n            }\r\n\r\n            // chỉ cho nhập imsi khi đang xử lý\r\n            if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\r\n                me.isShowImsiInput = true;\r\n                if (me.ticket.status == CONSTANTS.REQUEST_STATUS.DONE && me.listImsis.length <= 0) {\r\n                    me.isEmptyListImsi = true\r\n                } else {\r\n                    me.isEmptyListImsi = false;\r\n                }\r\n            } else {\r\n                me.isShowImsiInput = false;\r\n            }\r\n\r\n            // if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.IN_PROGRESS && me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.DONE){\r\n            //     me.isShowListImsi = true;\r\n            // } else {\r\n            //     me.isShowListImsi = false;\r\n            // }\r\n\r\n            // Hiển thị imsi khi đang xử lý\r\n            if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\r\n                me.isShowListImsi = true;\r\n            } else {\r\n                me.isShowListImsi = false;\r\n            }\r\n            // Ẩn chuyển xử lý nếu không phải yêu cầu mới\r\n            if (me.ticket.statusOld != CONSTANTS.REQUEST_STATUS.NEW) {\r\n                me.isShowAssignee = false;\r\n            }\r\n\r\n        }\r\n\r\n        // trạng thái hoàn thành, từ chối\r\n        if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.DONE || me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.REJECT) {\r\n            // me.isShowStatus = false;\r\n            me.isShowAssignee = false;\r\n\r\n            if (me.userInfo.type == me.userType.PROVINCE && me.ticket.assigneeId != null) {\r\n                me.isShowAssignee = true;\r\n            } else {\r\n                me.isShowAssignee = false;\r\n            }\r\n        }\r\n\r\n\r\n        // Ân list note trống\r\n        if (this.listNotes.length == 0) {\r\n            me.isShowListNote = false\r\n        } else {\r\n            me.isShowListNote = true;\r\n        }\r\n        me.isEnableButtonSave = true;\r\n    }\r\n\r\n    checkVisibleAndRequired() {\r\n        let me = this;\r\n        // nếu nhập trạng thái thì bắt buộc nhập ghi chú\r\n        if (this.ticket.status != null) {\r\n            me.isRequiredNote = true;\r\n            me.isShowAssignee = false\r\n        } else {\r\n            me.isRequiredNote = false;\r\n            if (me.userInfo.type == me.userType.PROVINCE && me.ticket.assigneeId == null) {\r\n                me.isShowAssignee = true;\r\n            }\r\n        }\r\n\r\n        if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE) {\r\n            if (this.ticket.assigneeId == null && this.ticket.status == null) {\r\n                this.isShowStatus = true\r\n                this.isShowNote = true\r\n                this.isShowAssignee = true\r\n            } else if (this.ticket.assigneeId != null && me.listActivatedAccount.includes(me.ticket.assigneeId)) {\r\n                this.isShowStatus = false\r\n                this.isShowNote = false\r\n            } else if (this.ticket.status != null || (this.ticket.cause != null && me.ticket.cause.trim() != '')) {\r\n                this.isShowStatus = true\r\n                this.isShowNote = true\r\n            }\r\n        } else if (me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) {\r\n            me.isShowStatus = true\r\n            me.isShowNote = true\r\n        }\r\n\r\n        if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.IN_PROGRESS && me.ticket.status == CONSTANTS.REQUEST_STATUS.DONE && this.listImsis.length == 0) {\r\n            me.isEmptyListImsi = true\r\n            me.isShowMesReqImsi = true\r\n        } else {\r\n            me.isEmptyListImsi = false;\r\n            me.isShowMesReqImsi = false;\r\n        }\r\n\r\n        if (me.ticket.status!= null && (me.ticket.cause != null && me.ticket.cause.trim() == '')) {\r\n            me.isEnableButtonSave = false;\r\n        } else {\r\n            me.isEnableButtonSave = true;\r\n        }\r\n\r\n        // Ẩn chuyển xử lý nếu không phải yêu cầu mới\r\n        if (me.ticket.statusOld != CONSTANTS.REQUEST_STATUS.NEW) {\r\n            me.isShowAssignee = false;\r\n        }\r\n\r\n        this.cdr.detectChanges();\r\n    }\r\n\r\n    openModalOrder(id, item) {\r\n        this.isShowOrder = true;\r\n        this.itemInTable = item;\r\n        this.onSubmitSearchHistory()\r\n    }\r\n\r\n    getFirstErrorMessage(): string | null {\r\n        if (this.isShowMesReqImsi) {\r\n            return this.tranService.translate(\"global.message.required\");\r\n        }\r\n        if (this.isShowMesMaxLenImsi) {\r\n            return this.tranService.translate(\"ticket.message.imsiMaxLength\");\r\n        }\r\n        if (this.isShowMesExistImsi) {\r\n            return this.tranService.translate(\"ticket.message.imsiIsExist\");\r\n        }\r\n        return null;\r\n    }\r\n    isFormValid() {\r\n        return Object.values(this.mapForm).every((formGroup: FormGroup) => formGroup.valid);\r\n    }\r\n    noWhitespaceValidator(): ValidatorFn {\r\n        return (control: AbstractControl): ValidationErrors | null => {\r\n            const isWhitespace = (control.value || '').trim().length === 0;\r\n            const isValid = !isWhitespace;\r\n            return isValid ? null : {whitespace: true};\r\n        }\r\n    };\r\n    onKeyDownNote(event): void {\r\n        if (event.key === ' ' && (this.ticket.note == null || this.ticket.note != null && this.ticket.note.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (this.ticket.note != null && this.ticket.note.trim() != '') {\r\n            this.ticket.note = this.ticket.note.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n    onKeyDownContent(event) {\r\n        if (event.key === ' ' && (this.ticket.content == null || this.ticket.content != null && this.ticket.content.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (this.ticket.content != null && this.ticket.content.trim() != '') {\r\n            this.ticket.content = this.ticket.content.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n    onKeyDownCause(event) {\r\n        if (event.key === ' ' && (this.ticket.cause == null || this.ticket.cause != null && this.ticket.cause.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (this.ticket.cause != null && this.ticket.cause.trim() != '') {\r\n            this.ticket.cause = this.ticket.cause.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n    onKeyDownNoteContent(event: KeyboardEvent, note: any): void {\r\n        if (event.key === ' ' && (!note.content || note.content.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (note.content && note.content.trim() !== '') {\r\n            note.content = note.content.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n    protected readonly CONSTANTS = CONSTANTS;\r\n}\r\n", "<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{ tranService.translate(\"ticket.menu.orderSim\") }}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"col-5 flex flex-row justify-content-end align-items-center\">\r\n        <p-button styleClass=\"p-button-info\"\r\n                  *ngIf=\"userInfo.type == userType.CUSTOMER && checkAuthen([CONSTANTS.PERMISSIONS.TICKET.CREATE])\"\r\n                  [label]=\"tranService.translate('global.button.create')\"\r\n                  (click)=\"showModalCreate()\" icon=\"\">\r\n        </p-button>\r\n    </div>\r\n</div>\r\n\r\n<form [formGroup]=\"formSearchTicket\" (ngSubmit)=\"onSubmitSearch()\" class=\"pt-3 pb-2 vnpt-field-set\">\r\n    <p-panel [toggleable]=\"true\" [header]=\"tranService.translate('global.text.filter')\">\r\n        <div class=\"grid search-grid-4\">\r\n            <!-- ma tinh -->\r\n            <div *ngIf=\"this.userInfo.type == this.userType.ADMIN\" class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\"\r\n                                [showClear]=\"true\" [filter]=\"true\" filterBy=\"display\"\r\n                                id=\"provinceCode\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"searchInfo.provinceCode\"\r\n                                [required]=\"false\"\r\n                                formControlName=\"provinceCode\"\r\n                                [options]=\"listProvince\"\r\n                                optionLabel=\"display\"\r\n                                optionValue=\"code\"\r\n                    ></p-dropdown>\r\n                    <label class=\"label-dropdown\" htmlFor=\"provinceCode\">{{ tranService.translate(\"account.label.province\") }}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\"\r\n                                [showClear]=\"true\" [filter]=\"true\" filterBy=\"display\"\r\n                                id=\"status\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"searchInfo.status\"\r\n                                [required]=\"false\"\r\n                                formControlName=\"status\"\r\n                                [options]=\"listTicketStatus\"\r\n                                optionLabel=\"label\"\r\n                                optionValue=\"value\"\r\n                                [filter] = true\r\n                                filterBy = \"label\"\r\n                    ></p-dropdown>\r\n                    <label class=\"label-dropdown\" htmlFor=\"status\">{{ tranService.translate(\"ticket.label.status\") }}</label>\r\n                </span>\r\n            </div>\r\n            <!-- email -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                           pInputText id=\"email\"\r\n                           [(ngModel)]=\"searchInfo.contactName\"\r\n                           formControlName=\"contactName\"\r\n                    />\r\n                    <label htmlFor=\"email\">{{ tranService.translate(\"ticket.label.fullName\") }}</label>\r\n                </span>\r\n            </div>\r\n            <!-- phone -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                           pInputText id=\"phone\"\r\n                           [(ngModel)]=\"searchInfo.contactPhone\"\r\n                           formControlName=\"contactPhone\"\r\n                           type=\"number\"\r\n                           (keydown)=\"preventCharacter($event)\"\r\n                           min = 0\r\n                    />\r\n                    <label htmlFor=\"phone\">{{ tranService.translate(\"ticket.label.phone\") }}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <p-calendar styleClass=\"w-full\"\r\n                                id=\"dateFrom\"\r\n                                [(ngModel)]=\"searchInfo.dateFrom\"\r\n                                formControlName=\"dateFrom\"\r\n                                [showIcon]=\"true\"\r\n                                [showClear]=\"true\"\r\n                                dateFormat=\"dd/mm/yy\"\r\n                                [maxDate]=\"maxDateFrom\"\r\n                                (onSelect)=\"onChangeDateFrom(searchInfo.dateFrom)\"\r\n                                (onInput)=\"onChangeDateFrom(searchInfo.dateFrom)\"\r\n                    ></p-calendar>\r\n                    <label class=\"label-calendar\" htmlFor=\"dateFrom\">{{ tranService.translate(\"ticket.label.dateFrom\") }}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <p-calendar styleClass=\"w-full\"\r\n                                id=\"dateTo\"\r\n                                [(ngModel)]=\"searchInfo.dateTo\"\r\n                                formControlName=\"dateTo\"\r\n                                [showIcon]=\"true\"\r\n                                [showClear]=\"true\"\r\n                                dateFormat=\"dd/mm/yy\"\r\n                                [minDate]=\"minDateTo\"\r\n                                [maxDate]=\"maxDateTo\"\r\n                                (onSelect)=\"onChangeDateTo(searchInfo.dateTo)\"\r\n                                (onInput)=\"onChangeDateTo(searchInfo.dateTo)\"\r\n                    />\r\n                    <label class=\"label-calendar\" htmlFor=\"dateTo\">{{ tranService.translate(\"ticket.label.dateTo\") }}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-3 pb-0\">\r\n                <p-button icon=\"pi pi-search\"\r\n                          styleClass=\"p-button-rounded p-button-secondary p-button-text button-search\"\r\n                          type=\"submit\"\r\n                ></p-button>\r\n            </div>\r\n        </div>\r\n    </p-panel>\r\n</form>\r\n\r\n<table-vnpt *ngIf=\"!changeTable\"\r\n    [tableId]=\"'tableTicketConfigList'\"\r\n    [fieldId]=\"'provinceCode'\"\r\n    [columns]=\"columns\"\r\n    [dataSet]=\"dataSet\"\r\n    [options]=\"optionTable\"\r\n    [pageNumber]=\"pageNumber\"\r\n    [loadData]=\"search.bind(this)\"\r\n    [pageSize]=\"pageSize\"\r\n    [sort]=\"sort\"\r\n    [params]=\"searchInfo\"\r\n    [labelTable]=\"tranService.translate('ticket.menu.requestList')\"\r\n></table-vnpt>\r\n<table-vnpt *ngIf=\"changeTable\"\r\n    [tableId]=\"'tableTicketConfigList'\"\r\n    [fieldId]=\"'provinceCode'\"\r\n    [columns]=\"columns\"\r\n    [dataSet]=\"dataSet\"\r\n    [options]=\"optionTable\"\r\n    [pageNumber]=\"pageNumber\"\r\n    [loadData]=\"search.bind(this)\"\r\n    [pageSize]=\"pageSize\"\r\n    [sort]=\"sort\"\r\n    [params]=\"searchInfo\"\r\n    [labelTable]=\"tranService.translate('ticket.menu.requestList')\"\r\n></table-vnpt>\r\n<!--    dialog tạo yêu cầu đặt sim-->\r\n<div class=\"flex justify-content-center\">\r\n    <p-dialog [header]=\"tranService.translate('ticket.label.createRequest')\" [(visible)]=\"isShowCreateRequest\"\r\n              [modal]=\"true\" [style]=\"{ width: '700px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n        <form class=\"mt-3\" [formGroup]=\"formTicketSim\" (ngSubmit)=\"createOrderSim()\">\r\n            <div class=\"flex flex-row flex-wrap justify-content-between w-full\">\r\n                <!-- contactName -->\r\n                <div class=\"w-full field grid\">\r\n                    <label htmlFor=\"contactName\" class=\"col-fixed\"\r\n                           style=\"width:180px\">{{ tranService.translate(\"ticket.label.customerName\") }}<span\r\n                        class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\">\r\n                        <input class=\"w-full\"\r\n                               pInputText id=\"contactName\"\r\n                               [(ngModel)]=\"ticket.contactName\"\r\n                               formControlName=\"contactName\"\r\n                               [required]=\"true\"\r\n                               [maxLength]=\"50\"\r\n                               pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$\"\r\n                               [placeholder]=\"tranService.translate('account.text.inputFullname')\"\r\n                               [readonly]=\"true\"\r\n                        />\r\n                    </div>\r\n                </div>\r\n                <!-- error fullname -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"fullName\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.contactName.dirty && formTicketSim.controls.contactName.errors?.required\">{{ tranService.translate(\"global.message.required\") }}</small>\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.contactName.errors?.maxLength\">{{ tranService.translate(\"global.message.maxLength\", {len: 255}) }}</small>\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.contactName.errors?.pattern\">{{ tranService.translate(\"global.message.formatContainVN\") }}</small>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- email -->\r\n                <div class=\"w-full field grid\">\r\n                    <label htmlFor=\"email\" class=\"col-fixed\"\r\n                           style=\"width:180px\">{{ tranService.translate(\"ticket.label.email\") }}<span\r\n                        class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\">\r\n                        <input class=\"w-full\"\r\n                               pInputText id=\"contactEmail\"\r\n                               [(ngModel)]=\"ticket.contactEmail\"\r\n                               formControlName=\"contactEmail\"\r\n                               [required]=\"true\"\r\n                               [maxLength]=\"50\"\r\n                               pattern=\"^[a-z0-9]+[a-z0-9\\-\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\-\\._]*[a-z0-9]+)+(\\.[a-z]{2,})$\"\r\n                               [placeholder]=\"tranService.translate('account.text.inputEmail')\"\r\n                               [readonly]=\"true\"\r\n                        />\r\n                    </div>\r\n                </div>\r\n                <!-- error contactEmail -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"contactEmail\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.contactEmail.dirty && formTicketSim.controls.contactEmail.errors?.required\">{{ tranService.translate(\"global.message.required\") }}</small>\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.contactEmail.errors?.maxLength\">{{ tranService.translate(\"global.message.maxLength\", {len: 255}) }}</small>\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.contactEmail.errors?.pattern\">{{ tranService.translate(\"global.message.invalidEmail\") }}</small>\r\n                        <!--                            <small class=\"text-red-500\" *ngIf=\"isEmailExisted\">{{tranService.translate(\"global.message.exists\",{type: tranService.translate(\"account.label.email\").toLowerCase()})}}</small> -->\r\n                    </div>\r\n                </div>\r\n                <!-- phone -->\r\n                <div class=\"w-full field grid\">\r\n                    <label htmlFor=\"phone\" class=\"col-fixed\"\r\n                           style=\"width:180px\">{{ tranService.translate(\"ticket.label.phone\") }}<span\r\n                        class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\">\r\n                        <input class=\"w-full\"\r\n                               pInputText id=\"contactPhone\"\r\n                               [(ngModel)]=\"ticket.contactPhone\"\r\n                               formControlName=\"contactPhone\"\r\n                               [required]=\"true\"\r\n                               (keydown)=\"preventCharacter($event)\"\r\n                               [maxLength]=\"12\"\r\n                               [placeholder]=\"tranService.translate('account.text.inputPhone')\"\r\n                               [readonly]=\"true\"\r\n                        />\r\n                    </div>\r\n                </div>\r\n                <!-- error phone -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"phone\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.contactPhone.dirty && formTicketSim.controls.contactPhone.errors?.required\">{{ tranService.translate(\"global.message.required\") }}</small>\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.contactPhone.errors?.pattern\">{{ tranService.translate(\"ticket.message.invalidPhone\") }}</small>\r\n                    </div>\r\n                </div>\r\n                <!-- quantity-->\r\n                <div class=\"w-full field grid\">\r\n                    <label htmlFor=\"quantity\" class=\"col-fixed\"\r\n                           style=\"width:180px;height: fit-content;\">{{ tranService.translate(\"ticket.label.quantity\") }}\r\n                        <span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\">\r\n                        <p-inputNumber class=\"w-full\" style=\"resize: none; padding: 0;\"\r\n                                       [autoResize]=\"false\"\r\n                                       pInputTextarea id=\"quantity\"\r\n                                       [(ngModel)]=\"ticket.quantity\"\r\n                                       formControlName=\"quantity\"\r\n                                       [required]=true\r\n                                       [placeholder]=\"tranService.translate('ticket.label.quantity')\"\r\n                                       (onInput)=\"checkQuantity($event)\"\r\n                        ></p-inputNumber>\r\n                    </div>\r\n                </div>\r\n                <!-- error quantity -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"quantity\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.quantity.dirty && formTicketSim.controls.quantity.errors?.required\">{{ tranService.translate(\"global.message.required\") }}</small>\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"errorMaxQuantity\">{{ tranService.translate(\"ticket.message.maxQuantity\") }}</small>\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"errorMinQuantity\">{{ tranService.translate(\"ticket.message.minQuantity\") }}</small>\r\n                    </div>\r\n                </div>\r\n                <!--                 address-->\r\n                <div class=\"w-full field grid\">\r\n                    <label class=\"col-fixed\"\r\n                           style=\"width:180px;height: fit-content;\">{{ tranService.translate(\"ticket.label.deliveryAddress\") }}\r\n                        <span class=\"text-red-500\">*</span></label>\r\n                </div>\r\n                <p-card class=\"w-full mb-3\">\r\n                    <div class=\"flex\">\r\n\r\n                        <vnpt-select\r\n                            [(value)]=\"ticket.province\"\r\n                            [placeholder]=\"tranService.translate('ticket.label.province')\"\r\n                            objectKey=\"provinceAddress\"\r\n                            paramKey=\"name\"\r\n                            keyReturn=\"code\"\r\n                            displayPattern=\"${name}\"\r\n                            typeValue=\"object\"\r\n                            [isMultiChoice]=\"false\"\r\n                            [required]=\"true\"\r\n                            [disabled]=\"false\"\r\n                            (onchange)=\"onSelectProvince($event)\"\r\n                            class=\"flex-1 col-3\">\r\n                        </vnpt-select>\r\n                        <vnpt-select class=\"flex-1 col-3\"\r\n                                     [(value)]=\"ticket.district\"\r\n                                     [placeholder]=\"tranService.translate('ticket.label.district')\"\r\n                                     objectKey=\"districtAddress\"\r\n                                     paramKey=\"name\"\r\n                                     keyReturn=\"code\"\r\n                                     displayPattern=\"${name}\"\r\n                                     typeValue=\"object\"\r\n                                     [isMultiChoice]=\"false\"\r\n                                     [required]=\"true\"\r\n                                     [disabled]=\"disableSelectDistrict\"\r\n                                     [paramDefault]=\"optionAddress\"\r\n                                     (onchange)=\"onSelectDistrict($event)\"\r\n                        >\r\n                        </vnpt-select>\r\n\r\n                        <vnpt-select class=\"flex-1 col-3\"\r\n                                     [(value)]=\"ticket.commune\"\r\n                                     [placeholder]=\"tranService.translate('ticket.label.commune')\"\r\n                                     objectKey=\"communeAddress\"\r\n                                     paramKey=\"name\"\r\n                                     keyReturn=\"code\"\r\n                                     displayPattern=\"${name}\"\r\n                                     typeValue=\"object\"\r\n                                     [isMultiChoice]=\"false\"\r\n                                     [required]=\"true\"\r\n                                     [disabled]=\"disableSelectCommune\"\r\n                                     [paramDefault]=\"optionAddress\"\r\n                                     (onchange)=\"onSelectCommune($event)\"\r\n                        >\r\n                        </vnpt-select>\r\n\r\n                    </div>\r\n                    <div class=\"flex\">\r\n                        <div class=\"col\">\r\n                            <input class=\"w-full\" style=\"resize: none;\"\r\n                                   rows=\"5\"\r\n                                   [autoResize]=\"false\"\r\n                                   pInputTextarea id=\"detailAddress\"\r\n                                   [(ngModel)]=\"ticket.detailAddress\"\r\n                                   formControlName=\"detailAddress\"\r\n                                   pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$\"\r\n                                   [required]=\"true\"\r\n                                   [maxlength]=\"255\"\r\n                                   [placeholder]=\"tranService.translate('ticket.label.detailAddress')\"\r\n                            />\r\n\r\n                        </div>\r\n                    </div>\r\n                    <!-- error detailAddress -->\r\n                    <div class=\"w-full field grid text-error-field\">\r\n                        <label htmlFor=\"detailAddress\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                        <div class=\"col\">\r\n                            <small class=\"text-red-500\"\r\n                                   *ngIf=\"formTicketSim.controls.detailAddress.errors?.maxLength\">{{ tranService.translate(\"global.message.maxLength\", {len: 255}) }}</small>\r\n                            <small class=\"text-red-500\"\r\n                                   *ngIf=\"formTicketSim.controls.detailAddress.dirty && formTicketSim.controls.detailAddress.errors?.required\">{{ tranService.translate(\"global.message.required\") }}</small>\r\n                            <small class=\"text-red-500\"\r\n                                   *ngIf=\"formTicketSim.controls.detailAddress.errors?.pattern\">{{ tranService.translate(\"global.message.formatCode\") }}</small>\r\n                        </div>\r\n                    </div>\r\n                </p-card>\r\n                <!-- content-->\r\n                <div class=\"w-full field grid\">\r\n                    <label htmlFor=\"content\" class=\"col-fixed\"\r\n                           style=\"width:180px;height: fit-content;\">{{ tranService.translate(\"ticket.label.content\") }}</label>\r\n                    <div class=\"col\">\r\n                            <textarea class=\"w-full\" style=\"resize: none;\"\r\n                                      rows=\"5\"\r\n                                      [autoResize]=\"false\"\r\n                                      pInputTextarea id=\"content\"\r\n                                      [(ngModel)]=\"ticket.content\"\r\n                                      formControlName=\"content\"\r\n                                      pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$\"\r\n                                      [maxlength]=\"255\"\r\n                                      (keydown)=\"onKeyDownContent($event)\"\r\n                                      [placeholder]=\"tranService.translate('ticket.label.content')\"\r\n                            ></textarea>\r\n                    </div>\r\n                </div>\r\n                <!-- error content -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"content\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.content.errors?.maxLength\">{{ tranService.translate(\"global.message.maxLength\", {len: 255}) }}</small>\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.content.errors?.pattern\">{{ tranService.translate(\"global.message.formatCode\") }}</small>\r\n                    </div>\r\n\r\n                </div>\r\n\r\n                <!-- note-->\r\n                <div class=\"w-full field grid\">\r\n                    <label htmlFor=\"content\" class=\"col-fixed\"\r\n                           style=\"width:180px;height: fit-content;\">{{ tranService.translate(\"ticket.label.note\") }}</label>\r\n                    <div class=\"col\">\r\n                            <textarea class=\"w-full\" style=\"resize: none;\"\r\n                                      rows=\"5\"\r\n                                      [autoResize]=\"false\"\r\n                                      pInputTextarea id=\"note\"\r\n                                      [(ngModel)]=\"ticket.note\"\r\n                                      formControlName=\"note\"\r\n                                      pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$\"\r\n                                      [maxlength]=\"255\"\r\n                                      (keydown)=\"onKeyDownNote($event)\"\r\n                                      [placeholder]=\"tranService.translate('ticket.label.note')\"\r\n                            ></textarea>\r\n                    </div>\r\n                </div>\r\n                <!-- error note -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"note\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.note.errors?.maxLength\">{{ tranService.translate(\"global.message.maxLength\", {len: 255}) }}</small>\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.note.errors?.pattern\">{{ tranService.translate(\"global.message.formatCode\") }}</small>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex flex-row justify-content-center align-items-center mt-3\">\r\n                <p-button styleClass=\"mr-2 p-button-secondary\" [label]=\"tranService.translate('global.button.cancel')\"\r\n                          (click)=\"isShowCreateRequest = false\"></p-button>\r\n                <p-button type=\"submit\" styleClass=\"p-button-info\" *ngIf=\"checkAuthen([CONSTANTS.PERMISSIONS.TICKET.CREATE])\"\r\n                          [disabled]=\"formTicketSim.invalid || typeRequest == 'create' && (errorMinQuantity || !selectedProvince || !selectedDistrict || !selectedCommune || errorMaxQuantity)\"\r\n                          [label]=\"tranService.translate('global.button.save')\"></p-button>\r\n            </div>\r\n        </form>\r\n    </p-dialog>\r\n    <!--    dialog xử lý yêu cầu-->\r\n    <p-dialog\r\n        [header]=\"typeRequest == 'view' ? tranService.translate('ticket.label.viewOrderSim') :tranService.translate('ticket.label.updateOrderSim')\"\r\n        [(visible)]=\"isShowUpdateRequest\"\r\n        [modal]=\"true\" [style]=\"{ width: '1000px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n        <form class=\"mt-3\" [formGroup]=\"formUpdateOrderSim\" (ngSubmit)=\"updateOrderSim()\">\r\n            <div class=\"flex dialog-ticket-sim-1\">\r\n                <div class=\"flex-1 col-11\">\r\n                    <div class=\"flex flex-row flex-wrap w-full\">\r\n                        <div class=\"w-full field grid dialog-grid-customer-2\" *ngIf=\"this.userInfo.type == this.userType.ADMIN && typeRequest == 'view'\">\r\n                            <label htmlFor=\"contactName\" class=\"col-fixed\"\r\n                                   style=\"width:180px\">{{ tranService.translate(\"account.label.province\") }}<span\r\n                                class=\"text-red-500\">*</span></label>\r\n                            <div class=\"col\">\r\n                                <p-dropdown styleClass=\"w-full\"\r\n                                            [showClear]=\"false\" [filter]=\"true\" filterBy=\"display\"\r\n                                            id=\"provinceCode\" [autoDisplayFirst]=\"false\"\r\n                                            [(ngModel)]=\"ticket.provinceCode\"\r\n                                            formControlName=\"provinceCode\"\r\n                                            [options]=\"listProvince\"\r\n                                            optionLabel=\"display\"\r\n                                            optionValue=\"code\"\r\n                                            [disabled]=\"true\"\r\n                                            [readonly]=\"true\"\r\n                                ></p-dropdown>\r\n                            </div>\r\n                        </div>\r\n                        <!-- contactName -->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"contactName\" class=\"col-fixed\"\r\n                                   style=\"width:180px\">{{ tranService.translate(\"ticket.label.customerName\") }}<span\r\n                                class=\"text-red-500\">*</span></label>\r\n                            <div class=\"col\">\r\n                                <input class=\"w-full\"\r\n                                       pInputText id=\"contactName\"\r\n                                       [(ngModel)]=\"ticket.contactName\"\r\n                                       formControlName=\"contactName\"\r\n                                       [required]=\"true\"\r\n                                       [maxLength]=\"50\"\r\n                                       pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$\"\r\n                                       [placeholder]=\"tranService.translate('account.text.inputFullname')\"\r\n                                       [readonly]=\"true\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n\r\n                        <!-- phone-->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"phone\" class=\"col-fixed\"\r\n                                   style=\"width:180px\">{{ tranService.translate(\"ticket.label.phone\") }}<span\r\n                                class=\"text-red-500\">*</span></label>\r\n                            <div class=\"col\">\r\n                                <input class=\"w-full\"\r\n                                       pInputText id=\"contactPhone\"\r\n                                       [(ngModel)]=\"ticket.contactPhone\"\r\n                                       formControlName=\"contactPhone\"\r\n                                       [required]=\"true\"\r\n                                       pattern=\"^((\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\"\r\n                                       (keydown)=\"preventCharacter($event)\"\r\n                                       [placeholder]=\"tranService.translate('account.text.inputPhone')\"\r\n                                       [readonly]=\"true\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n\r\n                        <!-- address-->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"address\" class=\"col-fixed\"\r\n                                   style=\"width:180px\">{{ tranService.translate(\"ticket.label.deliveryAddress\") }}<span\r\n                                class=\"text-red-500\">*</span></label>\r\n                            <div class=\"col\">\r\n                                <textarea class=\"w-full\"\r\n                                          rows=\"2\"\r\n                                          pInputText id=\"address\"\r\n                                          [(ngModel)]=\"ticket.address\"\r\n                                          formControlName=\"address\"\r\n                                          [required]=\"true\"\r\n                                          [placeholder]=\"tranService.translate('ticket.label.address')\"\r\n                                          [readonly]=\"true\"\r\n                                ></textarea>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"w-full field grid\" [class]=\"isShowStatus ? '' : 'hidden'\">\r\n                            <label for=\"status\" class=\"col-fixed\"\r\n                                   style=\"width:180px\">{{ tranService.translate(\"ticket.label.status\") }}</label>\r\n                            <div class=\"col\">\r\n                                <p-dropdown styleClass=\"w-full\"\r\n                                            [showClear]=\"true\"\r\n                                            id=\"type\" [autoDisplayFirst]=\"true\"\r\n                                            [(ngModel)]=\"ticket.status\"\r\n                                            [required]=\"isRequiredStatus\"\r\n                                            formControlName=\"status\"\r\n                                            [options]=\"ticket.statusOld !== null ? mapTicketStatus[ticket.statusOld] : listTicketStatus\"\r\n                                            optionLabel=\"label\"\r\n                                            optionValue=\"value\"\r\n                                            [placeholder]=\"tranService.translate('ticket.label.status')\"\r\n                                            [emptyMessage]=\"tranService.translate('global.text.nodata')\"\r\n                                            (onChange)=\"checkVisibleAndRequired()\"\r\n                                ></p-dropdown>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"w-full field grid\"\r\n                             [class]=\"this.typeRequest == 'view' ? '' : 'hidden'\">\r\n                            <label for=\"statusOld\" class=\"col-fixed\"\r\n                                   style=\"width:180px\">{{ tranService.translate(\"ticket.label.status\") }}<span\r\n                                class=\"text-red-500\">*</span></label>\r\n                            <div class=\"col\">\r\n<!--                                <p-dropdown styleClass=\"w-full\"-->\r\n<!--                                            id=\"statusOld\" [autoDisplayFirst]=\"true\"-->\r\n<!--                                            [(ngModel)]=\"ticket.statusOld\"-->\r\n<!--                                            [required]=\"true\"-->\r\n<!--                                            formControlName=\"statusOld\"-->\r\n<!--                                            [options]=\"listTicketStatus\"-->\r\n<!--                                            optionLabel=\"label\"-->\r\n<!--                                            optionValue=\"value\"-->\r\n<!--                                            [placeholder]=\"tranService.translate('ticket.label.status')\"-->\r\n<!--                                            [readonly]=true-->\r\n<!--                                ></p-dropdown>-->\r\n                                <span *ngIf=\"ticket.statusOld == CONSTANTS.REQUEST_STATUS.NEW\" [class]=\"['p-2', 'text-white', 'bg-cyan-300', 'border-round', 'inline-block']\">{{getValueStatus(ticket.statusOld)}}</span>\r\n                                <span *ngIf=\"ticket.statusOld == CONSTANTS.REQUEST_STATUS.RECEIVED\"  [class]=\"['p-2', 'text-white', 'bg-bluegray-500', 'border-round', 'inline-block']\">{{getValueStatus(ticket.statusOld)}}</span>\r\n                                <span *ngIf=\"ticket.statusOld == CONSTANTS.REQUEST_STATUS.IN_PROGRESS\"  [class]=\"['p-2', 'text-white', 'bg-orange-400', 'border-round', 'inline-block']\">{{getValueStatus(ticket.statusOld)}}</span>\r\n                                <span *ngIf=\"ticket.statusOld == CONSTANTS.REQUEST_STATUS.REJECT\"  [class]=\"['p-2', 'text-white', 'bg-red-500', 'border-round', 'inline-block']\">{{getValueStatus(ticket.statusOld)}}</span>\r\n                                <span *ngIf=\"ticket.statusOld == CONSTANTS.REQUEST_STATUS.DONE\"  [class]=\"['p-2', 'text-white', 'bg-green-500', 'border-round', 'inline-block']\">{{getValueStatus(ticket.statusOld)}}</span>\r\n                            </div>\r\n                        </div>\r\n                        <!-- error trang thai yeu cau -->\r\n                        <div class=\"w-full field grid text-error-field\">\r\n                            <label htmlFor=\"userType\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                            <div class=\"col\">\r\n                                <small class=\"text-red-500\"\r\n                                       *ngIf=\"formTicketSim.controls.status.dirty && formTicketSim.controls.status.errors?.required\">{{ tranService.translate(\"global.message.required\") }}</small>\r\n                            </div>\r\n                        </div>\r\n                        <!-- note admin-->\r\n                        <div *ngIf=\"this.isShowNote\"\r\n                             class=\"w-full field grid\">\r\n                            <label htmlFor=\"cause\" class=\"col-fixed\"\r\n                                   style=\"width:180px;height: fit-content;\">{{ tranService.translate(\"ticket.label.processingNotes\") }}\r\n                                <small class=\"text-red-500\" *ngIf=\"this.ticket.status != null\">*</small></label>\r\n                            <div class=\"col\">\r\n                            <textarea class=\"w-full\" style=\"resize: none;\"\r\n                                      rows=\"2\"\r\n                                      [autoResize]=\"false\"\r\n                                      pInputTextarea id=\"cause\"\r\n                                      [(ngModel)]=\"ticket.cause\"\r\n                                      formControlName=\"cause\"\r\n                                      [maxlength]=\"255\"\r\n                                      [required]=\"this.isRequiredNote\"\r\n                                      [readonly]=\"this.typeRequest == 'view'\"\r\n                                      (input)=\"checkVisibleAndRequired()\"\r\n                                      (keydown)=\"onKeyDownCause($event)\"\r\n                                      [placeholder]=\"tranService.translate('ticket.label.processingNotes')\"\r\n                            ></textarea>\r\n                            </div>\r\n                        </div>\r\n                        <!-- error note admin -->\r\n                        <div class=\"w-full field grid text-error-field\">\r\n                            <label htmlFor=\"cause\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                            <div class=\"col\">\r\n                                <small class=\"text-red-500\"\r\n                                       *ngIf=\"formTicketSim.controls.cause.errors?.maxLength\">{{ tranService.translate(\"global.message.maxLength\", {len: 255}) }}</small>\r\n                                <small class=\"text-red-500\"\r\n                                       *ngIf=\"formTicketSim.controls.cause.dirty && formTicketSim.controls.cause.errors?.required\">{{ tranService.translate(\"global.message.required\") }}</small>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"flex-1 col-11\">\r\n                    <div class=\"flex flex-row flex-wrap w-full\">\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"email\" class=\"col-fixed\"\r\n                                   style=\"width:180px\">{{ tranService.translate(\"ticket.label.email\") }}<span\r\n                                class=\"text-red-500\">*</span></label>\r\n                            <div class=\"col\">\r\n                                <input class=\"w-full\"\r\n                                       pInputText id=\"contactEmail\"\r\n                                       [(ngModel)]=\"ticket.contactEmail\"\r\n                                       formControlName=\"contactEmail\"\r\n                                       [required]=\"true\"\r\n                                       [maxLength]=\"50\"\r\n                                       pattern=\"^[a-z0-9]+[a-z0-9\\-\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\-\\._]*[a-z0-9]+)+(\\.[a-z]{2,})$\"\r\n                                       [placeholder]=\"tranService.translate('account.text.inputEmail')\"\r\n                                       [readonly]=\"true\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n\r\n                        <!-- quantity-->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"content\" class=\"col-fixed\"\r\n                                   style=\"width:180px;height: fit-content;\">{{ tranService.translate(\"ticket.label.quantity\") }}\r\n                                <span class=\"text-red-500\">*</span></label>\r\n                            <div class=\"col\">\r\n                                <input class=\"w-full\"\r\n                                       pInputText id=\"quantity\"\r\n                                       [(ngModel)]=\"ticket.quantity\"\r\n                                       formControlName=\"quantity\"\r\n                                       (keydown)=\"preventCharacter($event)\"\r\n                                       [placeholder]=\"tranService.translate('ticket.label.quantity')\"\r\n                                       [readonly]=\"true\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n\r\n\r\n                        <!-- content-->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"content\" class=\"col-fixed\"\r\n                                   style=\"width:180px;height: fit-content;\">{{ tranService.translate(\"ticket.label.content\") }}</label>\r\n                            <div class=\"col\">\r\n                                <input class=\"w-full\"\r\n                                       pInputText id=\"content\"\r\n                                       [(ngModel)]=\"ticket.content\"\r\n                                       formControlName=\"content\"\r\n                                       (keydown)=\"preventCharacter($event)\"\r\n                                       [placeholder]=\"tranService.translate('ticket.label.content')\"\r\n                                       [readonly]=\"true\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n                        <!-- note-->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"content\" class=\"col-fixed\"\r\n                                   style=\"width:180px;height: fit-content;\">{{ tranService.translate(\"ticket.label.note\") }}</label>\r\n                            <div class=\"col\">\r\n                                <input class=\"w-full\"\r\n                                       pInputText id=\"note\"\r\n                                       [(ngModel)]=\"ticket.note\"\r\n                                       formControlName=\"note\"\r\n                                       (keydown)=\"preventCharacter($event)\"\r\n                                       [placeholder]=\"tranService.translate('ticket.label.note')\"\r\n                                       [readonly]=\"true\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n                        <!-- chuyen xu ly-->\r\n                        <div class=\"w-full field grid\" [class]=\"isShowAssignee ? '' : 'hidden'\">\r\n                            <label for=\"assigneeId\" class=\"col-fixed\"\r\n                                   style=\"width:180px\">{{ tranService.translate(\"ticket.label.transferProcessing\") }}</label>\r\n                            <div class=\"col\" style=\"max-width: calc(100% - 180px)\">\r\n                                <vnpt-select\r\n                                    id=\"assigneeId\"\r\n                                    [(value)]=\"ticket.assigneeId\"\r\n                                    [placeholder]=\"tranService.translate('ticket.label.transferProcessing')\"\r\n                                    objectKey=\"account\"\r\n                                    paramKey=\"email\"\r\n                                    formControlName=\"assigneeId\"\r\n                                    [required]=\"isRequiredAssignee\"\r\n                                    keyReturn=\"id\"\r\n                                    displayPattern=\"${email}\"\r\n                                    (onchange)=\"checkVisibleAndRequired()\"\r\n                                    [isMultiChoice]=\"false\"\r\n                                    [disabled]=\"typeRequest == 'view'\"\r\n                                    [paramDefault]=\"{type : 3}\"\r\n                                ></vnpt-select>\r\n                            </div>\r\n                        </div>\r\n                        <!-- error chuyen xu ly-->\r\n                        <div class=\"w-full field grid text-error-field\">\r\n                            <label htmlFor=\"assigneeId\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                            <div class=\"col\">\r\n                                <small class=\"text-red-500\"\r\n                                       *ngIf=\"formTicketSim.controls.assigneeId.dirty && formTicketSim.controls.assigneeId.errors?.required\">{{ tranService.translate(\"global.message.required\") }}</small>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"flex\">\r\n                <div class=\"flex-grow-1\">\r\n                    <div class=\" col-fixed w-full pt-0\">\r\n                        <div *ngIf=\"isShowListNote\">\r\n                            <div class=\"w-full field grid\">\r\n                                <label class=\"col-fixed\"\r\n                                       style=\"height: fit-content;\"><b>{{ tranService.translate(\"ticket.label.listNotes\") }}</b>\r\n                                </label>\r\n                            </div>\r\n                            <div class=\"p-grid p-justify-center\">\r\n                                <div>\r\n                                    <p-table [value]=\"this.listNotes\">\r\n                                        <ng-template pTemplate=\"header\">\r\n                                            <tr>\r\n                                                <th class=\"col-1\">{{ tranService.translate('global.text.stt') }}</th>\r\n                                                <th class=\"col-2\">{{ tranService.translate('account.text.account') }}</th>\r\n                                                <th class=\"col-2\">{{ tranService.translate('global.button.changeStatus') }}</th>\r\n                                                <th class=\"col-2\">{{ tranService.translate('account.label.time') }}</th>\r\n                                                <th>{{ tranService.translate('ticket.label.content') }}</th>\r\n                                            </tr>\r\n                                        </ng-template>\r\n                                        <ng-template pTemplate=\"body\" let-note let-i=\"rowIndex\">\r\n                                            <tr [formGroup]=\"mapForm[note.id]\">\r\n                                                <td class=\"col-1\">{{ i + 1 }}</td>\r\n                                                <td class=\"col-2\">{{ note.userName }}</td>\r\n                                                <td class=\"col-2\">{{ getValueStatus(note.status) }}</td>\r\n                                                <td class=\"col-2\">{{ note.createdDate | date:\"HH:mm:ss dd/MM/yyyy\" }}</td>\r\n                                                <td>\r\n                                                    <input *ngIf=\"typeRequest == 'update'\" class=\"w-full\"\r\n                                                           pInputText id=\"content\"\r\n                                                           [(ngModel)]=\"note.content\"\r\n                                                           formControlName=\"content\"\r\n                                                           [required]=\"true\"\r\n                                                           [maxLength]=\"255\"\r\n                                                           (keydown)=\"onKeyDownNoteContent($event, note)\"\r\n                                                    />\r\n                                                    <span *ngIf=\"typeRequest == 'view'\"\r\n                                                          [pTooltip]=\"note.content\">{{ getValueNote(note.content) }}</span>\r\n                                                    <small class=\"text-red-500\"\r\n                                                           *ngIf=\"mapForm[note.id].controls.content.dirty && mapForm[note.id].controls.content.errors?.required\">{{ tranService.translate(\"global.message.required\") }}</small>\r\n                                                    <small class=\"text-red-500\"\r\n                                                           *ngIf=\"mapForm[note.id].controls.content.errors?.maxLength\">{{ tranService.translate(\"global.message.maxLength\", {len: 255}) }}</small>\r\n                                                </td>\r\n                                            </tr>\r\n                                        </ng-template>\r\n                                    </p-table>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"col-4 pt-0\">\r\n                    <div class=\"block w-full\">\r\n                        <div class=\"block \">\r\n                            <div class=\"w-full field grid\" [class]=\"isShowListImsi ? '' : 'hidden'\">\r\n                                <label class=\"col-fixed\"\r\n                                       style=\"width:180px;height: fit-content;\"><b>{{ tranService.translate(\"ticket.label.listImsis\") }}</b>\r\n                                </label>\r\n                            </div>\r\n                            <div class=\"flex flex-row justify-content-between\" *ngIf=\"isShowListImsi\">\r\n                                <div class=\"w-full\">\r\n                                    <p-table [value]=\"this.listImsis\">\r\n                                        <ng-template pTemplate=\"header\">\r\n                                            <tr>\r\n                                                <th>{{ tranService.translate('global.text.stt') }}</th>\r\n                                                <th>{{ tranService.translate('ticket.label.imsi') }}</th>\r\n                                                <th *ngIf=\"typeRequest == 'update'\">{{ tranService.translate('global.text.action') }}</th>\r\n                                            </tr>\r\n                                        </ng-template>\r\n                                        <ng-template pTemplate=\"body\" let-item let-i=\"rowIndex\">\r\n                                            <tr>\r\n                                                <td>{{ i + 1 }}</td>\r\n                                                <td>{{ item }}</td>\r\n                                                <td *ngIf=\"typeRequest == 'update'\">\r\n                                                    <p-button icon=\"pi pi-trash\" (click)=\"removeImsi(i)\"\r\n                                                              [hidden]=\"!isShowImsiInput\"></p-button>\r\n                                                </td>\r\n                                            </tr>\r\n                                        </ng-template>\r\n                                    </p-table>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"grid justify-center mt-1 px-auto\" [class]=\"isShowImsiInput ? '' : 'hidden'\">\r\n                                <div class=\"col-11\">\r\n                                    <div class=\"p-inputGroup justify-content-center \">\r\n                                        <button type=\"button\" pButton icon=\"pi pi-plus\" (click)=\"addImsi()\"\r\n                                                [disabled]=\"listImsis.length >= ticket.quantity || isShowMesMaxLenImsi || newImsi == '' || isShowMesExistImsi\"></button>\r\n                                        <input type=\"number\"\r\n                                               [ngModelOptions]=\"{standalone: true}\" pInputText\r\n                                               [placeholder]=\"tranService.translate('ticket.text.addImsi')\"\r\n                                               [(ngModel)]=\"newImsi\"\r\n                                               pattern=\"[0-9]*\"\r\n                                               maxlength=\"18\"\r\n                                               [disabled]=\"listImsis.length >= ticket.quantity\"\r\n                                               (input)=\"checkImsiInput()\"\r\n                                               (keydown)=\"preventCharacter($event)\"\r\n                                               min=\"0\"\r\n                                        />\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"col-12 field grid text-error-field\">\r\n                                    <label htmlFor=\"imsi\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                                    <div class=\"col-12\">\r\n                                        <small class=\"text-red-500\"\r\n                                               *ngIf=\"getFirstErrorMessage()\">{{ getFirstErrorMessage() }}</small>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <!--                                        <div class=\"flex-1\">-->\r\n                    <!--                                            <div class=\"field  px-4 pt-4  flex-row \">-->\r\n                    <!--                                                &lt;!&ndash; email &ndash;&gt;-->\r\n                    <!--                                                <form [formGroup]=\"formMailInput\">-->\r\n                    <!--                                                    <div class=\"field grid px-4 pt-4 flex flex-row flex-nowrap\">-->\r\n                    <!--                                                        &lt;!&ndash; email &ndash;&gt;-->\r\n                    <!--                                                        <label htmlFor=\"email\" class=\"col-fixed\" style=\"width:100px\">{{tranService.translate(\"alert.receiving.emails\")}}<span class=\"text-red-500\"></span></label>-->\r\n                    <!--                                                        <div class=\"col-8\" >-->\r\n                    <!--                                                            <input class=\"w-full\"-->\r\n                    <!--                                                                   pInputText id=\"email\"-->\r\n                    <!--                                                                   [(ngModel)]=\"newImsi\"-->\r\n                    <!--                                                                   [maxLength]=\"255\"-->\r\n                    <!--                                                                   [placeholder]=\"tranService.translate('ticket.label.imsi')\"-->\r\n                    <!--                                                                   pattern=\"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$\"-->\r\n                    <!--                                                            />-->\r\n                    <!--                                                        </div>-->\r\n                    <!--                                                        <button pButton class=\"p-button-outlined\" type=\"button\" icon=\"pi pi-plus-circle add-icon-size\" style=\"width: 41px; height: 36px\" (click)=\"addImsi()\" [disabled]=\"formMailInput.invalid\"></button>-->\r\n                    <!--                                                    </div>-->\r\n\r\n                    <!--                                                </form>-->\r\n                    <!--                                                <div class=\"field  px-4 pt-4  flex-row \">-->\r\n                    <!--                                                    <table-vnpt-->\r\n                    <!--                                                        [fieldId]=\"'id'\"-->\r\n                    <!--                                                        [columns]=\"columns\"-->\r\n                    <!--                                                        [dataSet]=\"dataSet\"-->\r\n                    <!--                                                        [options]=\"optionTable\"-->\r\n                    <!--                                                        [loadData]=\"search.bind(this)\"-->\r\n                    <!--                                                        [scrollHeight]=\"'200px'\"-->\r\n                    <!--                                                    ></table-vnpt>-->\r\n                    <!--                                                </div>-->\r\n                    <!--                                            </div>-->\r\n                    <!--                                        </div>-->\r\n                </div>\r\n\r\n            </div>\r\n            <div class=\"flex flex-row justify-content-center align-items-center mt-3\" *ngIf=\"typeRequest == 'update'\">\r\n                <p-button styleClass=\"mr-2 p-button-secondary\" [label]=\"tranService.translate('global.button.cancel')\"\r\n                          (click)=\"isShowUpdateRequest = false\"></p-button>\r\n                <p-button type=\"submit\" styleClass=\"p-button-info\" *ngIf=\"checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])\"\r\n                          [disabled]=\"formUpdateOrderSim.invalid || !isEnableButtonSave || isEmptyListImsi || (this.listNotes.length > 0 && !isFormValid())\"\r\n                          [hidden]=\"typeRequest == 'view'\"\r\n                          [label]=\"tranService.translate('global.button.save')\"></p-button>\r\n            </div>\r\n\r\n        </form>\r\n    </p-dialog>\r\n</div>\r\n\r\n<p-dialog class=\"dialog-vnpt\" [header]=\"tranService.translate('ticket.label.historyOrder')\" [modal]=\"true\"\r\n          [draggable]=\"false\" [resizable]=\"false\"\r\n          [breakpoints]=\"{ '1199px': '75vw', '575px': '90vw' }\" [(visible)]=\"isShowOrder\" [style]=\"{ width: '50rem' }\">\r\n    <form [formGroup]=\"formSearchHistoryOrder\" (ngSubmit)=\"onSubmitSearchHistory()\" class=\"pt-3 pb-2 vnpt-field-set\">\r\n        <div class=\"grid search-grid-2  align-items-center\">\r\n            <div class=\"col-4\">\r\n                <span class=\"p-float-label\">\r\n                    <p-calendar styleClass=\"w-full\"\r\n                                id=\"dateFrom\"\r\n                                [(ngModel)]=\"searchHistoryOrder.fromDate\"\r\n                                formControlName=\"fromDate\"\r\n                                [showIcon]=\"true\"\r\n                                [showClear]=\"true\"\r\n                                dateFormat=\"dd/mm/yy\"\r\n                                [maxDate]=\"maxDateFrom\"\r\n                                (onSelect)=\"onChangeDateFrom(searchHistoryOrder.fromDate)\"\r\n                                (onInput)=\"onChangeDateFrom(searchHistoryOrder.fromDate)\"\r\n                    ></p-calendar>\r\n                    <label htmlFor=\"dateFrom\">{{ tranService.translate(\"ticket.label.dateFrom\") }}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-4 ml-2\">\r\n                <span class=\"p-float-label\">\r\n                    <p-calendar styleClass=\"w-full\"\r\n                                id=\"dateTo\"\r\n                                [(ngModel)]=\"searchHistoryOrder.toDate\"\r\n                                formControlName=\"toDate\"\r\n                                [showIcon]=\"true\"\r\n                                [showClear]=\"true\"\r\n                                dateFormat=\"dd/mm/yy\"\r\n                                [minDate]=\"minDateTo\"\r\n                                [maxDate]=\"maxDateTo\"\r\n                                (onSelect)=\"onChangeDateTo(searchHistoryOrder.toDate)\"\r\n                                (onInput)=\"onChangeDateTo(searchHistoryOrder.toDate)\"\r\n                    />\r\n                    <label htmlFor=\"dateTo\">{{ tranService.translate(\"ticket.label.dateTo\") }}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-2\">\r\n                <p-button icon=\"pi pi-search\"\r\n                          styleClass=\"p-button-rounded p-button-secondary p-button-text button-search\"\r\n                          type=\"submit\"\r\n                ></p-button>\r\n            </div>\r\n        </div>\r\n\r\n    </form>\r\n\r\n    <table-vnpt\r\n        [columns]=\"columnsHistory\"\r\n        [dataSet]=\"dataSetHistory\"\r\n        [options]=\"optionTableHistory\"\r\n        [loadData]=\"searchHistory.bind(this)\"\r\n        [pageNumber]=\"0\"\r\n        [pageSize]=\"999999999999\"\r\n        [params]=\"searchHistoryOrder\"\r\n    ></table-vnpt>\r\n\r\n</p-dialog>\r\n"], "mappings": "AACA,SAAQA,aAAa,QAAO,4BAA4B;AACxD,SAAQC,aAAa,QAAO,0CAA0C;AACtE,SAAQC,cAAc,QAAO,4CAA4C;AACzE,SAAgFC,UAAU,QAAO,gBAAgB;AAGjH,SAAQC,SAAS,QAAO,qCAAqC;AAC7D,SAAQC,gBAAgB,QAAO,6CAA6C;AAC5E,SAAQC,sBAAsB,QAAO,mDAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;ICHhFC,EAAA,CAAAC,cAAA,oBAG8C;IAApCD,EAAA,CAAAE,UAAA,mBAAAC,0EAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,OAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IACrCT,EAAA,CAAAU,YAAA,EAAW;;;;IAFDV,EAAA,CAAAW,UAAA,UAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,yBAAuD;;;;;;IAU7Dd,EAAA,CAAAC,cAAA,cAAqE;IAKjDD,EAAA,CAAAE,UAAA,2BAAAa,gFAAAC,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAa,IAAA;MAAA,MAAAC,OAAA,GAAAlB,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAU,OAAA,CAAAC,UAAA,CAAAC,YAAA,GAAAJ,MAAA,CACxC;IAAA,EADgE;IAMhDhB,EAAA,CAAAU,YAAA,EAAa;IACdV,EAAA,CAAAC,cAAA,iBAAqD;IAAAD,EAAA,CAAAqB,MAAA,GAAqD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IATtGV,EAAA,CAAAsB,SAAA,GAAkB;IAAlBtB,EAAA,CAAAW,UAAA,mBAAkB,uDAAAY,MAAA,CAAAJ,UAAA,CAAAC,YAAA,gCAAAG,MAAA,CAAAC,YAAA;IASuBxB,EAAA,CAAAsB,SAAA,GAAqD;IAArDtB,EAAA,CAAAyB,iBAAA,CAAAF,MAAA,CAAAV,WAAA,CAAAC,SAAA,2BAAqD;;;;;IAwF9Hd,EAAA,CAAA0B,SAAA,sBAYc;;;;IAXV1B,EAAA,CAAAW,UAAA,oCAAmC,uCAAAgB,MAAA,CAAAC,OAAA,aAAAD,MAAA,CAAAE,OAAA,aAAAF,MAAA,CAAAG,WAAA,gBAAAH,MAAA,CAAAI,UAAA,cAAAJ,MAAA,CAAAK,MAAA,CAAAC,IAAA,CAAAN,MAAA,eAAAA,MAAA,CAAAO,QAAA,UAAAP,MAAA,CAAAQ,IAAA,YAAAR,MAAA,CAAAR,UAAA,gBAAAQ,MAAA,CAAAd,WAAA,CAAAC,SAAA;;;;;IAYvCd,EAAA,CAAA0B,SAAA,sBAYc;;;;IAXV1B,EAAA,CAAAW,UAAA,oCAAmC,uCAAAyB,MAAA,CAAAR,OAAA,aAAAQ,MAAA,CAAAP,OAAA,aAAAO,MAAA,CAAAN,WAAA,gBAAAM,MAAA,CAAAL,UAAA,cAAAK,MAAA,CAAAJ,MAAA,CAAAC,IAAA,CAAAG,MAAA,eAAAA,MAAA,CAAAF,QAAA,UAAAE,MAAA,CAAAD,IAAA,YAAAC,MAAA,CAAAjB,UAAA,gBAAAiB,MAAA,CAAAvB,WAAA,CAAAC,SAAA;;;;;IAwCfd,EAAA,CAAAC,cAAA,gBAC+G;IAAAD,EAAA,CAAAqB,MAAA,GAAsD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA9DV,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAyB,iBAAA,CAAAY,MAAA,CAAAxB,WAAA,CAAAC,SAAA,4BAAsD;;;;;;;;;;IACrKd,EAAA,CAAAC,cAAA,gBACoE;IAAAD,EAAA,CAAAqB,MAAA,GAAmE;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA3EV,EAAA,CAAAsB,SAAA,GAAmE;IAAnEtB,EAAA,CAAAyB,iBAAA,CAAAa,MAAA,CAAAzB,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAuC,eAAA,IAAAC,GAAA,GAAmE;;;;;IACvIxC,EAAA,CAAAC,cAAA,gBACkE;IAAAD,EAAA,CAAAqB,MAAA,GAA6D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAArEV,EAAA,CAAAsB,SAAA,GAA6D;IAA7DtB,EAAA,CAAAyB,iBAAA,CAAAgB,MAAA,CAAA5B,WAAA,CAAAC,SAAA,mCAA6D;;;;;IA0B/Hd,EAAA,CAAAC,cAAA,gBACiH;IAAAD,EAAA,CAAAqB,MAAA,GAAsD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA9DV,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAyB,iBAAA,CAAAiB,MAAA,CAAA7B,WAAA,CAAAC,SAAA,4BAAsD;;;;;IACvKd,EAAA,CAAAC,cAAA,gBACqE;IAAAD,EAAA,CAAAqB,MAAA,GAAmE;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA3EV,EAAA,CAAAsB,SAAA,GAAmE;IAAnEtB,EAAA,CAAAyB,iBAAA,CAAAkB,MAAA,CAAA9B,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAuC,eAAA,IAAAC,GAAA,GAAmE;;;;;IACxIxC,EAAA,CAAAC,cAAA,gBACmE;IAAAD,EAAA,CAAAqB,MAAA,GAA0D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAlEV,EAAA,CAAAsB,SAAA,GAA0D;IAA1DtB,EAAA,CAAAyB,iBAAA,CAAAmB,MAAA,CAAA/B,WAAA,CAAAC,SAAA,gCAA0D;;;;;IA0B7Hd,EAAA,CAAAC,cAAA,gBACiH;IAAAD,EAAA,CAAAqB,MAAA,GAAsD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA9DV,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAyB,iBAAA,CAAAoB,OAAA,CAAAhC,WAAA,CAAAC,SAAA,4BAAsD;;;;;IACvKd,EAAA,CAAAC,cAAA,gBACmE;IAAAD,EAAA,CAAAqB,MAAA,GAA0D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAlEV,EAAA,CAAAsB,SAAA,GAA0D;IAA1DtB,EAAA,CAAAyB,iBAAA,CAAAqB,OAAA,CAAAjC,WAAA,CAAAC,SAAA,gCAA0D;;;;;IAwB7Hd,EAAA,CAAAC,cAAA,gBACyG;IAAAD,EAAA,CAAAqB,MAAA,GAAsD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA9DV,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAyB,iBAAA,CAAAsB,OAAA,CAAAlC,WAAA,CAAAC,SAAA,4BAAsD;;;;;IAC/Jd,EAAA,CAAAC,cAAA,gBACgC;IAAAD,EAAA,CAAAqB,MAAA,GAAyD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAjEV,EAAA,CAAAsB,SAAA,GAAyD;IAAzDtB,EAAA,CAAAyB,iBAAA,CAAAuB,OAAA,CAAAnC,WAAA,CAAAC,SAAA,+BAAyD;;;;;IACzFd,EAAA,CAAAC,cAAA,gBACgC;IAAAD,EAAA,CAAAqB,MAAA,GAAyD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAjEV,EAAA,CAAAsB,SAAA,GAAyD;IAAzDtB,EAAA,CAAAyB,iBAAA,CAAAwB,OAAA,CAAApC,WAAA,CAAAC,SAAA,+BAAyD;;;;;IA+ErFd,EAAA,CAAAC,cAAA,gBACsE;IAAAD,EAAA,CAAAqB,MAAA,GAAmE;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA3EV,EAAA,CAAAsB,SAAA,GAAmE;IAAnEtB,EAAA,CAAAyB,iBAAA,CAAAyB,OAAA,CAAArC,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAuC,eAAA,IAAAC,GAAA,GAAmE;;;;;IACzIxC,EAAA,CAAAC,cAAA,gBACmH;IAAAD,EAAA,CAAAqB,MAAA,GAAsD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA9DV,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAyB,iBAAA,CAAA0B,OAAA,CAAAtC,WAAA,CAAAC,SAAA,4BAAsD;;;;;IACzKd,EAAA,CAAAC,cAAA,gBACoE;IAAAD,EAAA,CAAAqB,MAAA,GAAwD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAhEV,EAAA,CAAAsB,SAAA,GAAwD;IAAxDtB,EAAA,CAAAyB,iBAAA,CAAA2B,OAAA,CAAAvC,WAAA,CAAAC,SAAA,8BAAwD;;;;;IA0BhId,EAAA,CAAAC,cAAA,gBACgE;IAAAD,EAAA,CAAAqB,MAAA,GAAmE;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA3EV,EAAA,CAAAsB,SAAA,GAAmE;IAAnEtB,EAAA,CAAAyB,iBAAA,CAAA4B,OAAA,CAAAxC,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAuC,eAAA,IAAAC,GAAA,GAAmE;;;;;IACnIxC,EAAA,CAAAC,cAAA,gBAC8D;IAAAD,EAAA,CAAAqB,MAAA,GAAwD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAhEV,EAAA,CAAAsB,SAAA,GAAwD;IAAxDtB,EAAA,CAAAyB,iBAAA,CAAA6B,OAAA,CAAAzC,WAAA,CAAAC,SAAA,8BAAwD;;;;;IA2BtHd,EAAA,CAAAC,cAAA,gBAC6D;IAAAD,EAAA,CAAAqB,MAAA,GAAmE;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA3EV,EAAA,CAAAsB,SAAA,GAAmE;IAAnEtB,EAAA,CAAAyB,iBAAA,CAAA8B,OAAA,CAAA1C,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAuC,eAAA,IAAAC,GAAA,GAAmE;;;;;IAChIxC,EAAA,CAAAC,cAAA,gBAC2D;IAAAD,EAAA,CAAAqB,MAAA,GAAwD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAhEV,EAAA,CAAAsB,SAAA,GAAwD;IAAxDtB,EAAA,CAAAyB,iBAAA,CAAA+B,OAAA,CAAA3C,WAAA,CAAAC,SAAA,8BAAwD;;;;;IAO3Hd,EAAA,CAAA0B,SAAA,oBAE2E;;;;IADjE1B,EAAA,CAAAW,UAAA,aAAA8C,OAAA,CAAAC,aAAA,CAAAC,OAAA,IAAAF,OAAA,CAAAG,WAAA,iBAAAH,OAAA,CAAAI,gBAAA,KAAAJ,OAAA,CAAAK,gBAAA,KAAAL,OAAA,CAAAM,gBAAA,KAAAN,OAAA,CAAAO,eAAA,IAAAP,OAAA,CAAAQ,gBAAA,EAAqK,UAAAR,OAAA,CAAA5C,WAAA,CAAAC,SAAA;;;;;;IAcvKd,EAAA,CAAAC,cAAA,eAAiI;IAElGD,EAAA,CAAAqB,MAAA,GAAqD;IAAArB,EAAA,CAAAC,cAAA,eACvD;IAAAD,EAAA,CAAAqB,MAAA,QAAC;IAAArB,EAAA,CAAAU,YAAA,EAAO;IACjCV,EAAA,CAAAC,cAAA,cAAiB;IAIDD,EAAA,CAAAE,UAAA,2BAAAgE,iFAAAlD,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA+D,IAAA;MAAA,MAAAC,OAAA,GAAApE,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAA4D,OAAA,CAAAC,MAAA,CAAAjD,YAAA,GAAAJ,MAAA,CACpD;IAAA,EADwE;IAO5ChB,EAAA,CAAAU,YAAA,EAAa;;;;IAbSV,EAAA,CAAAsB,SAAA,GAAqD;IAArDtB,EAAA,CAAAyB,iBAAA,CAAA6C,OAAA,CAAAzD,WAAA,CAAAC,SAAA,2BAAqD;IAIhEd,EAAA,CAAAsB,SAAA,GAAmB;IAAnBtB,EAAA,CAAAW,UAAA,oBAAmB,uDAAA2D,OAAA,CAAAD,MAAA,CAAAjD,YAAA,aAAAkD,OAAA,CAAA9C,YAAA;;;;;;;;IAyG/BxB,EAAA,CAAAC,cAAA,WAA8I;IAAAD,EAAA,CAAAqB,MAAA,GAAoC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA1HV,EAAA,CAAAuE,UAAA,CAAAvE,EAAA,CAAAuC,eAAA,IAAAiC,GAAA,EAA8E;IAACxE,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAyB,iBAAA,CAAAgD,OAAA,CAAAC,cAAA,CAAAD,OAAA,CAAAJ,MAAA,CAAAM,SAAA,EAAoC;;;;;;;;IAClL3E,EAAA,CAAAC,cAAA,WAAwJ;IAAAD,EAAA,CAAAqB,MAAA,GAAoC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA9HV,EAAA,CAAAuE,UAAA,CAAAvE,EAAA,CAAAuC,eAAA,IAAAqC,GAAA,EAAkF;IAAC5E,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAyB,iBAAA,CAAAoD,OAAA,CAAAH,cAAA,CAAAG,OAAA,CAAAR,MAAA,CAAAM,SAAA,EAAoC;;;;;;;;IAC5L3E,EAAA,CAAAC,cAAA,WAAyJ;IAAAD,EAAA,CAAAqB,MAAA,GAAoC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA5HV,EAAA,CAAAuE,UAAA,CAAAvE,EAAA,CAAAuC,eAAA,IAAAuC,GAAA,EAAgF;IAAC9E,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAyB,iBAAA,CAAAsD,OAAA,CAAAL,cAAA,CAAAK,OAAA,CAAAV,MAAA,CAAAM,SAAA,EAAoC;;;;;;;;IAC7L3E,EAAA,CAAAC,cAAA,WAAiJ;IAAAD,EAAA,CAAAqB,MAAA,GAAoC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAAzHV,EAAA,CAAAuE,UAAA,CAAAvE,EAAA,CAAAuC,eAAA,IAAAyC,GAAA,EAA6E;IAAChF,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAyB,iBAAA,CAAAwD,OAAA,CAAAP,cAAA,CAAAO,OAAA,CAAAZ,MAAA,CAAAM,SAAA,EAAoC;;;;;;;;IACrL3E,EAAA,CAAAC,cAAA,WAAiJ;IAAAD,EAAA,CAAAqB,MAAA,GAAoC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA3HV,EAAA,CAAAuE,UAAA,CAAAvE,EAAA,CAAAuC,eAAA,IAAA2C,GAAA,EAA+E;IAAClF,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAyB,iBAAA,CAAA0D,OAAA,CAAAT,cAAA,CAAAS,OAAA,CAAAd,MAAA,CAAAM,SAAA,EAAoC;;;;;IAOrL3E,EAAA,CAAAC,cAAA,gBACqG;IAAAD,EAAA,CAAAqB,MAAA,GAAsD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA9DV,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAyB,iBAAA,CAAA2D,OAAA,CAAAvE,WAAA,CAAAC,SAAA,4BAAsD;;;;;IAQ3Jd,EAAA,CAAAC,cAAA,gBAA+D;IAAAD,EAAA,CAAAqB,MAAA,QAAC;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;;;IAJhFV,EAAA,CAAAC,cAAA,cAC+B;IAEqBD,EAAA,CAAAqB,MAAA,GAC5C;IAAArB,EAAA,CAAAqF,UAAA,IAAAC,oDAAA,oBAAwE;IAAAtF,EAAA,CAAAU,YAAA,EAAQ;IACpFV,EAAA,CAAAC,cAAA,cAAiB;IAKPD,EAAA,CAAAE,UAAA,2BAAAqF,+EAAAvE,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAoF,IAAA;MAAA,MAAAC,OAAA,GAAAzF,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAiF,OAAA,CAAApB,MAAA,CAAAqB,KAAA,GAAA1E,MAAA,CAC9C;IAAA,EAD2D,mBAAA2E,uEAAA;MAAA3F,EAAA,CAAAI,aAAA,CAAAoF,IAAA;MAAA,MAAAI,OAAA,GAAA5F,EAAA,CAAAO,aAAA;MAAA,OAKjBP,EAAA,CAAAQ,WAAA,CAAAoF,OAAA,CAAAC,uBAAA,EAAyB;IAAA,EALR,qBAAAC,yEAAA9E,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAoF,IAAA;MAAA,MAAAO,OAAA,GAAA/F,EAAA,CAAAO,aAAA;MAAA,OAMfP,EAAA,CAAAQ,WAAA,CAAAuF,OAAA,CAAAC,cAAA,CAAAhF,MAAA,CAAsB;IAAA,EANP;IAQnChB,EAAA,CAAAU,YAAA,EAAW;;;;IAfoCV,EAAA,CAAAsB,SAAA,GAC5C;IAD4CtB,EAAA,CAAAiG,kBAAA,KAAAC,OAAA,CAAArF,WAAA,CAAAC,SAAA,sCAC5C;IAA6Bd,EAAA,CAAAsB,SAAA,GAAgC;IAAhCtB,EAAA,CAAAW,UAAA,SAAAuF,OAAA,CAAA7B,MAAA,CAAA8B,MAAA,SAAgC;IAIvDnG,EAAA,CAAAsB,SAAA,GAAoB;IAApBtB,EAAA,CAAAW,UAAA,qBAAoB,YAAAuF,OAAA,CAAA7B,MAAA,CAAAqB,KAAA,gCAAAQ,OAAA,CAAAE,cAAA,cAAAF,OAAA,CAAAtC,WAAA,2BAAAsC,OAAA,CAAArF,WAAA,CAAAC,SAAA;;;;;IAiB1Bd,EAAA,CAAAC,cAAA,gBAC8D;IAAAD,EAAA,CAAAqB,MAAA,GAAmE;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA3EV,EAAA,CAAAsB,SAAA,GAAmE;IAAnEtB,EAAA,CAAAyB,iBAAA,CAAA4E,OAAA,CAAAxF,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAuC,eAAA,IAAAC,GAAA,GAAmE;;;;;IACjIxC,EAAA,CAAAC,cAAA,gBACmG;IAAAD,EAAA,CAAAqB,MAAA,GAAsD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA9DV,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAyB,iBAAA,CAAA6E,OAAA,CAAAzF,WAAA,CAAAC,SAAA,4BAAsD;;;;;IAmGzJd,EAAA,CAAAC,cAAA,gBAC6G;IAAAD,EAAA,CAAAqB,MAAA,GAAsD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA9DV,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAyB,iBAAA,CAAA8E,OAAA,CAAA1F,WAAA,CAAAC,SAAA,4BAAsD;;;;;IAoBvJd,EAAA,CAAAC,cAAA,SAAI;IACkBD,EAAA,CAAAqB,MAAA,GAA8C;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACrEV,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAqB,MAAA,GAAmD;IAAArB,EAAA,CAAAU,YAAA,EAAK;IAC1EV,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAqB,MAAA,GAAyD;IAAArB,EAAA,CAAAU,YAAA,EAAK;IAChFV,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAqB,MAAA,GAAiD;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACxEV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,IAAmD;IAAArB,EAAA,CAAAU,YAAA,EAAK;;;;IAJ1CV,EAAA,CAAAsB,SAAA,GAA8C;IAA9CtB,EAAA,CAAAyB,iBAAA,CAAA+E,OAAA,CAAA3F,WAAA,CAAAC,SAAA,oBAA8C;IAC9Cd,EAAA,CAAAsB,SAAA,GAAmD;IAAnDtB,EAAA,CAAAyB,iBAAA,CAAA+E,OAAA,CAAA3F,WAAA,CAAAC,SAAA,yBAAmD;IACnDd,EAAA,CAAAsB,SAAA,GAAyD;IAAzDtB,EAAA,CAAAyB,iBAAA,CAAA+E,OAAA,CAAA3F,WAAA,CAAAC,SAAA,+BAAyD;IACzDd,EAAA,CAAAsB,SAAA,GAAiD;IAAjDtB,EAAA,CAAAyB,iBAAA,CAAA+E,OAAA,CAAA3F,WAAA,CAAAC,SAAA,uBAAiD;IAC/Dd,EAAA,CAAAsB,SAAA,GAAmD;IAAnDtB,EAAA,CAAAyB,iBAAA,CAAA+E,OAAA,CAAA3F,WAAA,CAAAC,SAAA,yBAAmD;;;;;;IAUnDd,EAAA,CAAAC,cAAA,iBAOE;IALKD,EAAA,CAAAE,UAAA,2BAAAuG,mGAAAzF,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAsG,IAAA;MAAA,MAAAC,QAAA,GAAA3G,EAAA,CAAAO,aAAA,GAAAqG,SAAA;MAAA,OAAa5G,EAAA,CAAAQ,WAAA,CAAAmG,QAAA,CAAAE,OAAA,GAAA7F,MAAA,CACnE;IAAA,EADgF,qBAAA8F,6FAAA9F,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAsG,IAAA;MAAA,MAAAC,QAAA,GAAA3G,EAAA,CAAAO,aAAA,GAAAqG,SAAA;MAAA,MAAAG,OAAA,GAAA/G,EAAA,CAAAO,aAAA;MAAA,OAIfP,EAAA,CAAAQ,WAAA,CAAAuG,OAAA,CAAAC,oBAAA,CAAAhG,MAAA,EAAA2F,QAAA,CAAkC;IAAA,EAJnB;IAFjC3G,EAAA,CAAAU,YAAA,EAOE;;;;IALKV,EAAA,CAAAW,UAAA,YAAAgG,QAAA,CAAAE,OAAA,CAA0B;;;;;IAMjC7G,EAAA,CAAAC,cAAA,gBACgC;IAAAD,EAAA,CAAAqB,MAAA,GAAgC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;;IAAjEV,EAAA,CAAAW,UAAA,aAAAgG,QAAA,CAAAE,OAAA,CAAyB;IAAC7G,EAAA,CAAAsB,SAAA,GAAgC;IAAhCtB,EAAA,CAAAyB,iBAAA,CAAAwF,OAAA,CAAAC,YAAA,CAAAP,QAAA,CAAAE,OAAA,EAAgC;;;;;IAChE7G,EAAA,CAAAC,cAAA,gBAC6G;IAAAD,EAAA,CAAAqB,MAAA,GAAsD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA9DV,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAyB,iBAAA,CAAA0F,OAAA,CAAAtG,WAAA,CAAAC,SAAA,4BAAsD;;;;;IACnKd,EAAA,CAAAC,cAAA,gBACmE;IAAAD,EAAA,CAAAqB,MAAA,GAAmE;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA3EV,EAAA,CAAAsB,SAAA,GAAmE;IAAnEtB,EAAA,CAAAyB,iBAAA,CAAA2F,OAAA,CAAAvG,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAuC,eAAA,IAAAC,GAAA,GAAmE;;;;;IAnB9IxC,EAAA,CAAAC,cAAA,cAAmC;IACbD,EAAA,CAAAqB,MAAA,GAAW;IAAArB,EAAA,CAAAU,YAAA,EAAK;IAClCV,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAqB,MAAA,GAAmB;IAAArB,EAAA,CAAAU,YAAA,EAAK;IAC1CV,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAqB,MAAA,GAAiC;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACxDV,EAAA,CAAAC,cAAA,cAAkB;IAAAD,EAAA,CAAAqB,MAAA,GAAmD;;IAAArB,EAAA,CAAAU,YAAA,EAAK;IAC1EV,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAqF,UAAA,KAAAgC,mEAAA,qBAOE;IACFrH,EAAA,CAAAqF,UAAA,KAAAiC,kEAAA,oBACuE;IACvEtH,EAAA,CAAAqF,UAAA,KAAAkC,mEAAA,oBAC2K;IAC3KvH,EAAA,CAAAqF,UAAA,KAAAmC,mEAAA,oBAC8I;IAClJxH,EAAA,CAAAU,YAAA,EAAK;;;;;;IApBLV,EAAA,CAAAW,UAAA,cAAA8G,OAAA,CAAAC,OAAA,CAAAf,QAAA,CAAAgB,EAAA,EAA8B;IACZ3H,EAAA,CAAAsB,SAAA,GAAW;IAAXtB,EAAA,CAAAyB,iBAAA,CAAAmG,KAAA,KAAW;IACX5H,EAAA,CAAAsB,SAAA,GAAmB;IAAnBtB,EAAA,CAAAyB,iBAAA,CAAAkF,QAAA,CAAAkB,QAAA,CAAmB;IACnB7H,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAyB,iBAAA,CAAAgG,OAAA,CAAA/C,cAAA,CAAAiC,QAAA,CAAAR,MAAA,EAAiC;IACjCnG,EAAA,CAAAsB,SAAA,GAAmD;IAAnDtB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA8H,WAAA,OAAAnB,QAAA,CAAAoB,WAAA,yBAAmD;IAEzD/H,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAW,UAAA,SAAA8G,OAAA,CAAA7D,WAAA,aAA6B;IAQ9B5D,EAAA,CAAAsB,SAAA,GAA2B;IAA3BtB,EAAA,CAAAW,UAAA,SAAA8G,OAAA,CAAA7D,WAAA,WAA2B;IAG1B5D,EAAA,CAAAsB,SAAA,GAAmG;IAAnGtB,EAAA,CAAAW,UAAA,SAAA8G,OAAA,CAAAC,OAAA,CAAAf,QAAA,CAAAgB,EAAA,EAAAK,QAAA,CAAAnB,OAAA,CAAAoB,KAAA,KAAAR,OAAA,CAAAC,OAAA,CAAAf,QAAA,CAAAgB,EAAA,EAAAK,QAAA,CAAAnB,OAAA,CAAAqB,MAAA,kBAAAT,OAAA,CAAAC,OAAA,CAAAf,QAAA,CAAAgB,EAAA,EAAAK,QAAA,CAAAnB,OAAA,CAAAqB,MAAA,CAAAC,QAAA,EAAmG;IAEnGnI,EAAA,CAAAsB,SAAA,GAAyD;IAAzDtB,EAAA,CAAAW,UAAA,SAAA8G,OAAA,CAAAC,OAAA,CAAAf,QAAA,CAAAgB,EAAA,EAAAK,QAAA,CAAAnB,OAAA,CAAAqB,MAAA,kBAAAT,OAAA,CAAAC,OAAA,CAAAf,QAAA,CAAAgB,EAAA,EAAAK,QAAA,CAAAnB,OAAA,CAAAqB,MAAA,CAAAE,SAAA,CAAyD;;;;;IAtC7FpI,EAAA,CAAAC,cAAA,UAA4B;IAGmBD,EAAA,CAAAqB,MAAA,GAAqD;IAAArB,EAAA,CAAAU,YAAA,EAAI;IAGpGV,EAAA,CAAAC,cAAA,eAAqC;IAGzBD,EAAA,CAAAqF,UAAA,IAAAgD,0DAAA,4BAQc;IACdrI,EAAA,CAAAqF,UAAA,IAAAiD,0DAAA,6BAuBc;IAClBtI,EAAA,CAAAU,YAAA,EAAU;;;;IAvCyBV,EAAA,CAAAsB,SAAA,GAAqD;IAArDtB,EAAA,CAAAyB,iBAAA,CAAA8G,OAAA,CAAA1H,WAAA,CAAAC,SAAA,2BAAqD;IAK/Ed,EAAA,CAAAsB,SAAA,GAAwB;IAAxBtB,EAAA,CAAAW,UAAA,UAAA4H,OAAA,CAAAC,SAAA,CAAwB;;;;;IAuDrBxI,EAAA,CAAAC,cAAA,SAAoC;IAAAD,EAAA,CAAAqB,MAAA,GAAiD;IAAArB,EAAA,CAAAU,YAAA,EAAK;;;;IAAtDV,EAAA,CAAAsB,SAAA,GAAiD;IAAjDtB,EAAA,CAAAyB,iBAAA,CAAAgH,OAAA,CAAA5H,WAAA,CAAAC,SAAA,uBAAiD;;;;;IAHzFd,EAAA,CAAAC,cAAA,SAAI;IACID,EAAA,CAAAqB,MAAA,GAA8C;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACvDV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,GAAgD;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACzDV,EAAA,CAAAqF,UAAA,IAAAqD,+DAAA,iBAA0F;IAC9F1I,EAAA,CAAAU,YAAA,EAAK;;;;IAHGV,EAAA,CAAAsB,SAAA,GAA8C;IAA9CtB,EAAA,CAAAyB,iBAAA,CAAAkH,OAAA,CAAA9H,WAAA,CAAAC,SAAA,oBAA8C;IAC9Cd,EAAA,CAAAsB,SAAA,GAAgD;IAAhDtB,EAAA,CAAAyB,iBAAA,CAAAkH,OAAA,CAAA9H,WAAA,CAAAC,SAAA,sBAAgD;IAC/Cd,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAW,UAAA,SAAAgI,OAAA,CAAA/E,WAAA,aAA6B;;;;;;IAOlC5D,EAAA,CAAAC,cAAA,SAAoC;IACHD,EAAA,CAAAE,UAAA,mBAAA0I,0FAAA;MAAA5I,EAAA,CAAAI,aAAA,CAAAyI,IAAA;MAAA,MAAAC,KAAA,GAAA9I,EAAA,CAAAO,aAAA,GAAAwI,QAAA;MAAA,MAAAC,OAAA,GAAAhJ,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAwI,OAAA,CAAAC,UAAA,CAAAH,KAAA,CAAa;IAAA,EAAC;IACd9I,EAAA,CAAAU,YAAA,EAAW;;;;IAAvCV,EAAA,CAAAsB,SAAA,GAA2B;IAA3BtB,EAAA,CAAAW,UAAA,YAAAuI,OAAA,CAAAC,eAAA,CAA2B;;;;;IAL7CnJ,EAAA,CAAAC,cAAA,SAAI;IACID,EAAA,CAAAqB,MAAA,GAAW;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACpBV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,GAAU;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACnBV,EAAA,CAAAqF,UAAA,IAAA+D,+DAAA,iBAGK;IACTpJ,EAAA,CAAAU,YAAA,EAAK;;;;;;IANGV,EAAA,CAAAsB,SAAA,GAAW;IAAXtB,EAAA,CAAAyB,iBAAA,CAAAqH,KAAA,KAAW;IACX9I,EAAA,CAAAsB,SAAA,GAAU;IAAVtB,EAAA,CAAAyB,iBAAA,CAAA4H,QAAA,CAAU;IACTrJ,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAW,UAAA,SAAA2I,OAAA,CAAA1F,WAAA,aAA6B;;;;;IAdtD5D,EAAA,CAAAC,cAAA,eAA0E;IAG9DD,EAAA,CAAAqF,UAAA,IAAAkE,0DAAA,2BAMc;IACdvJ,EAAA,CAAAqF,UAAA,IAAAmE,0DAAA,2BASc;IAClBxJ,EAAA,CAAAU,YAAA,EAAU;;;;IAlBDV,EAAA,CAAAsB,SAAA,GAAwB;IAAxBtB,EAAA,CAAAW,UAAA,UAAA8I,OAAA,CAAAC,SAAA,CAAwB;;;;;IA0C7B1J,EAAA,CAAAC,cAAA,gBACsC;IAAAD,EAAA,CAAAqB,MAAA,GAA4B;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAApCV,EAAA,CAAAsB,SAAA,GAA4B;IAA5BtB,EAAA,CAAAyB,iBAAA,CAAAkI,OAAA,CAAAC,oBAAA,GAA4B;;;;;IA4C1F5J,EAAA,CAAA0B,SAAA,oBAG2E;;;;IAFjE1B,EAAA,CAAAW,UAAA,aAAAkJ,OAAA,CAAAC,kBAAA,CAAAnG,OAAA,KAAAkG,OAAA,CAAAE,kBAAA,IAAAF,OAAA,CAAAG,eAAA,IAAAH,OAAA,CAAArB,SAAA,CAAAyB,MAAA,SAAAJ,OAAA,CAAAK,WAAA,GAAkI,WAAAL,OAAA,CAAAjG,WAAA,qBAAAiG,OAAA,CAAAhJ,WAAA,CAAAC,SAAA;;;;;;;;;IAJhJd,EAAA,CAAAC,cAAA,cAA0G;IAE5FD,EAAA,CAAAE,UAAA,mBAAAiK,uEAAA;MAAAnK,EAAA,CAAAI,aAAA,CAAAgK,IAAA;MAAA,MAAAC,OAAA,GAAArK,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAA6J,OAAA,CAAAC,mBAAA,GAA+B,KAAK;IAAA,EAAC;IAACtK,EAAA,CAAAU,YAAA,EAAW;IAC3DV,EAAA,CAAAqF,UAAA,IAAAkF,uDAAA,wBAG2E;IAC/EvK,EAAA,CAAAU,YAAA,EAAM;;;;IAN6CV,EAAA,CAAAsB,SAAA,GAAuD;IAAvDtB,EAAA,CAAAW,UAAA,UAAA6J,OAAA,CAAA3J,WAAA,CAAAC,SAAA,yBAAuD;IAElDd,EAAA,CAAAsB,SAAA,GAAwD;IAAxDtB,EAAA,CAAAW,UAAA,SAAA6J,OAAA,CAAAC,WAAA,CAAAzK,EAAA,CAAA0K,eAAA,IAAAC,GAAA,EAAAH,OAAA,CAAA3K,SAAA,CAAA+K,WAAA,CAAAC,MAAA,CAAAC,MAAA,GAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ADvzB5H,OAAM,MAAOC,2BAA4B,SAAQtL,aAAa;EAwG1DuL,YACmCC,aAA4B,EAC3BC,cAA8B,EAC5BC,gBAAkC,EAC5BC,sBAA8C,EAC9EC,GAAsB,EACtBC,WAAwB,EACxBC,QAAkB;IAC1B,KAAK,CAACA,QAAQ,CAAC;IAPgB,KAAAN,aAAa,GAAbA,aAAa;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACZ,KAAAC,gBAAgB,GAAhBA,gBAAgB;IACV,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtD,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAxEpB,KAAAC,WAAW,GAAkC,IAAIC,IAAI,EAAE;IACvD,KAAAC,SAAS,GAAkC,IAAI;IAC/C,KAAAC,SAAS,GAAkC,IAAIF,IAAI,EAAE;IA4BrD,KAAA3H,gBAAgB,GAAY,KAAK;IACjC,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAA4H,qBAAqB,GAAY,IAAI;IACrC,KAAAC,oBAAoB,GAAY,IAAI;IACpC,KAAAnC,SAAS,GAAa,EAAE;IA0BxB,KAAAhC,OAAO,GAAQ,EAAE;IAGjB,KAAAoE,WAAW,GAAY,KAAK;IAmnCT,KAAAjM,SAAS,GAAGA,SAAS;EAzmCxC;EAEAkM,QAAQA,CAAA;IACJ,IAAIC,EAAE,GAAG,IAAI;IACbA,EAAE,CAACF,WAAW,GAAG,KAAK;IACtB,IAAI,CAACG,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACD,QAAQ;IAC5C,IAAI,CAACE,kBAAkB,GAAG;MACtBC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE;KACX;IACD,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAAChB,WAAW,CAACiB,KAAK,CAAC,IAAI,CAACJ,kBAAkB,CAAC;IAC7E,IAAI,CAACK,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAAClC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACP,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACnG,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAAC6I,QAAQ,GAAG5M,SAAS,CAAC6M,SAAS;IACnC,IAAI,CAAChD,SAAS,GAAG,EAAE;IACnB,IAAI,CAAClB,SAAS,GAAG,EAAE;IACnB,IAAI,CAACmE,OAAO,GAAG,EAAE;IACjB,IAAI,CAACtI,MAAM,GAAG;MACVsD,EAAE,EAAE,IAAI;MACRiF,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE,IAAI;MAClBjG,OAAO,EAAE,IAAI;MACbkG,IAAI,EAAE,IAAI;MACVrH,KAAK,EAAE,IAAI;MACXsH,IAAI,EAAEnN,SAAS,CAACoN,YAAY,CAACC,SAAS;MACtC/G,MAAM,EAAE,IAAI;MACZxB,SAAS,EAAE,IAAI;MACfwI,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,aAAa,EAAE,IAAI;MACnBC,SAAS,EAAE,IAAI;MACftM,YAAY,EAAE;KACjB;IACD,IAAI,CAACuM,aAAa,GAAG,IAAI,CAACrC,WAAW,CAACiB,KAAK,CAAC;MAACqB,IAAI,EAAE;IAAE,CAAC,CAAC;IACvD,IAAI,CAACC,cAAc,GAAG,CAClB;MACIC,KAAK,EAAE,IAAI,CAACjN,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACzDiN,KAAK,EAAE;KACV,CACJ,EACG,IAAI,CAACC,wBAAwB,GAAG,EAAE;IACtC,IAAI,CAACC,eAAe,GAAG;MACnB,CAAC,EAAE,CAAC;QACAH,KAAK,EAAE9B,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;QACzDiN,KAAK,EAAE;OACV,EACG;QACID,KAAK,EAAE9B,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QACvDiN,KAAK,EAAE;OACV,CACJ;MACD,CAAC,EAAE,CACC;QACID,KAAK,EAAE9B,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;QAC3DiN,KAAK,EAAE;OACV,EACD;QACID,KAAK,EAAE9B,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QACvDiN,KAAK,EAAE;OACV,CACJ;MACD,CAAC,EAAE,CACC;QACID,KAAK,EAAE9B,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACrDiN,KAAK,EAAE;OACV,EACD;QACID,KAAK,EAAE9B,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QACvDiN,KAAK,EAAE;OACV;KAER;IACD,IAAI,CAACG,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAAChI,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACiI,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACrF,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACa,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACD,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAAC0E,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,gBAAgB,GAAG,CACpB;MACIf,KAAK,EAAE9B,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;MACpDiN,KAAK,EAAE;KACV,EACD;MACID,KAAK,EAAE9B,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MACzDiN,KAAK,EAAE;KACV,EACD;MACID,KAAK,EAAE9B,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC3DiN,KAAK,EAAE;KACV,EACD;MACID,KAAK,EAAE9B,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACvDiN,KAAK,EAAE;KACV,EACD;MACID,KAAK,EAAE9B,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;MACrDiN,KAAK,EAAE;KACV,CACJ;IACD,IAAI,CAAC5M,UAAU,GAAG;MACdC,YAAY,EAAE,IAAI;MAClB0N,KAAK,EAAE,IAAI;MACXhC,YAAY,EAAE,IAAI;MAClBF,WAAW,EAAE,IAAI;MACjBI,IAAI,EAAEnN,SAAS,CAACoN,YAAY,CAACC,SAAS;MACtC/G,MAAM,EAAE,IAAI;MACZ4I,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE;KACb;IACD,IAAI,CAACC,aAAa,GAAG;MACjB7N,YAAY,EAAE,CAAC;MACf8N,YAAY,EAAE;KACjB;IACD,IAAI,CAACtN,OAAO,GAAG,CACX;MACIuN,IAAI,EAAE,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;MACzDsO,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI,CAACtD,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAAC8C,KAAK;MACvDC,MAAM,EAAE;KACX,EACD;MACIN,IAAI,EAAE,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7DsO,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZC,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAE;QACHC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;;KAErB,EAAE;MACCZ,IAAI,EAAE,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;MACtDsO,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZC,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAE;QACHC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;;KAErB,EAAE;MACCZ,IAAI,EAAE,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;MACtDsO,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE;KACX,EAAE;MACCN,IAAI,EAAE,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;MACzDsO,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE;KACX,EAAE;MACCN,IAAI,EAAE,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC;MAChEsO,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZC,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAE;QACHC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;;KAErB,EAAE;MACCZ,IAAI,EAAE,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACxDsO,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZC,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAE;QACHC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;;KAErB,EACD;MACIZ,IAAI,EAAE,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC5DsO,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZO,eAAeA,CAACjC,KAAK;QACjB,IAAIA,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI;QAC9B,OAAO/B,EAAE,CAACiE,WAAW,CAACC,mBAAmB,CAAC,IAAIzE,IAAI,CAACsC,KAAK,CAAC,CAAC;MAC9D;KACH,EACD;MACIoB,IAAI,EAAE,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC5DsO,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZO,eAAeA,CAACjC,KAAK;QACjB,IAAIA,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI;QAC9B,OAAO/B,EAAE,CAACiE,WAAW,CAACC,mBAAmB,CAAC,IAAIzE,IAAI,CAACsC,KAAK,CAAC,CAAC;MAC9D;KACH,EACD;MACIoB,IAAI,EAAE,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;MACzDsO,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE;KACX,EAAE;MACCN,IAAI,EAAE,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MACvDsO,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZU,gBAAgB,EAAGpC,KAAK,IAAI;QACxB,IAAIA,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACC,GAAG,EAAE;UACvC,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,CAAC;SAC9E,MAAM,IAAItC,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACE,QAAQ,EAAE;UACnD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,iBAAiB,EAAE,cAAc,EAAE,cAAc,CAAC;SAClF,MAAM,IAAIvC,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACG,WAAW,EAAE;UACtD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,CAAC;SAChF,MAAM,IAAIxC,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACI,MAAM,EAAE;UACjD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,CAAC;SAC7E,MAAM,IAAIzC,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACK,IAAI,EAAE;UAC/C,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC;;QAEhF,OAAO,EAAE;MACb,CAAC;MACDT,eAAe,EAAE,SAAAA,CAAUjC,KAAK;QAC5B,IAAIA,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACC,GAAG,EAAE;UACvC,OAAOrE,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;SACvD,MAAM,IAAIiN,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACE,QAAQ,EAAE;UACnD,OAAOtE,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;SAC5D,MAAM,IAAIiN,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACG,WAAW,EAAE;UACtD,OAAOvE,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;SAC9D,MAAM,IAAIiN,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACI,MAAM,EAAE;UACjD,OAAOxE,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SAC1D,MAAM,IAAIiN,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACK,IAAI,EAAE;UAC/C,OAAOzE,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;;QAEzD,OAAO,EAAE;MACb;KACH,CACJ;IAED,IAAI,CAAC4P,cAAc,GAAG,CAClB;MACIvB,IAAI,EAAE,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;MACrDsO,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,KAAK;MACbO,eAAe,EAAE,SAAAA,CAAUjC,KAAK;QAC5B,OAAO/B,EAAE,CAACiE,WAAW,CAACU,uBAAuB,CAAC5C,KAAK,CAAC;MACxD;KACH,EACD;MACIoB,IAAI,EAAE,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC5DsO,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE;KACX,EACD;MACIN,IAAI,EAAE,IAAI,CAACtO,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACxDsO,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,KAAK;MACbU,gBAAgB,EAAGpC,KAAK,IAAI;QACxB,IAAIA,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACC,GAAG,EAAE;UACvC,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,CAAC;SAC9E,MAAM,IAAItC,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACE,QAAQ,EAAE;UACnD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,iBAAiB,EAAE,cAAc,EAAE,cAAc,CAAC;SAClF,MAAM,IAAIvC,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACG,WAAW,EAAE;UACtD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,CAAC;SAChF,MAAM,IAAIxC,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACI,MAAM,EAAE;UACjD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,CAAC;SAC7E,MAAM,IAAIzC,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACK,IAAI,EAAE;UAC/C,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC;;QAEhF,OAAO,EAAE;MACb,CAAC;MACDT,eAAe,EAAE,SAAAA,CAAUjC,KAAK;QAC5B,IAAIA,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACC,GAAG,EAAE;UACvC,OAAOrE,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;SACvD,MAAM,IAAIiN,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACE,QAAQ,EAAE;UACnD,OAAOtE,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;SAC5D,MAAM,IAAIiN,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACG,WAAW,EAAE;UACtD,OAAOvE,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;SAC9D,MAAM,IAAIiN,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACI,MAAM,EAAE;UACjD,OAAOxE,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SAC1D,MAAM,IAAIiN,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACK,IAAI,EAAE;UAC/C,OAAOzE,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;;QAEzD,OAAO,EAAE;MACb;KACH,CAEJ;IAED,IAAI,CAAC8P,kBAAkB,GAAG;MACtBC,YAAY,EAAE,IAAI;MAClBC,SAAS,EAAE;KACd;IAED,IAAI,CAACC,cAAc,GAAG;MAClBlK,OAAO,EAAE,EAAE;MACXmK,KAAK,EAAE;KACV,EAGG,IAAI,CAAClP,WAAW,GAAG;MACfmP,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,KAAK;MACpBL,YAAY,EAAE,IAAI;MAClBM,mBAAmB,EAAE,KAAK;MAC1BC,MAAM,EAAE,CACJ;QACIC,IAAI,EAAE,mBAAmB;QACzBC,OAAO,EAAE,IAAI,CAACzQ,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACzDyQ,IAAI,EAAE,SAAAA,CAAU5J,EAAE,EAAE6J,IAAI;UACpBxF,EAAE,CAACyF,aAAa,CAAC9J,EAAE,EAAE6J,IAAI,EAAE,MAAM,CAAC;QACtC;OACH,EACD;QACIH,IAAI,EAAE,iBAAiB;QACvBC,OAAO,EAAE,IAAI,CAACzQ,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACzDyQ,IAAI,EAAE,SAAAA,CAAU5J,EAAE,EAAE6J,IAAI;UACpBxF,EAAE,CAACyF,aAAa,CAAC9J,EAAE,EAAE6J,IAAI,EAAE,QAAQ,CAAC;QACxC,CAAC;QACDE,UAAU,EAAE,SAAAA,CAAU/J,EAAE,EAAE6J,IAAI;UAC1B,IAAIxF,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAACiF,QAAQ,IAAI3F,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAAC8C,KAAK,EAAE,OAAO,KAAK;UACnH,IAAI,CAACgC,IAAI,CAACI,SAAS,IAAI,CAACJ,IAAI,CAACrE,UAAU,KAAKnB,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAACmF,QAAQ,IAAI7F,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAACoF,QAAQ,CAAC,IAAI9F,EAAE,CAACvB,WAAW,CAAC,CAAC5K,SAAS,CAAC+K,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI;UACvN,IAAKkB,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAACmF,QAAQ,IAAIL,IAAI,CAACI,SAAS,KAAK5F,EAAE,CAACC,QAAQ,CAACtE,EAAE,IACrFqE,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAACmF,QAAQ,IAAIL,IAAI,CAACrE,UAAU,IAAI,IAAK,EAAE;YAC/E,OAAO,KAAK;;UAEhB,IAAInB,EAAE,CAACvB,WAAW,CAAC,CAAC5K,SAAS,CAAC+K,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,MACjE,OAAO,KAAK;QACrB;OACH,EACD;QACIuG,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE,IAAI,CAACzQ,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;QAChEyQ,IAAI,EAAE,SAAAA,CAAU5J,EAAE,EAAE6J,IAAI;UACpBxF,EAAE,CAAC+F,cAAc,CAACpK,EAAE,EAAE6J,IAAI,CAAC;QAC/B;OACH;KAER;IACL,IAAI,CAACzP,UAAU,GAAG,CAAC;IACnB,IAAI,CAACG,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,IAAI,GAAG,kBAAkB;IAC9B,IAAI,CAACN,OAAO,GAAG;MACXgF,OAAO,EAAE,EAAE;MACXmK,KAAK,EAAE;KACV,EACG,IAAI,CAACgB,gBAAgB,GAAG,IAAI,CAAC1G,WAAW,CAACiB,KAAK,CAAC,IAAI,CAACpL,UAAU,CAAC;IACnE,IAAI,CAACuC,aAAa,GAAG,IAAI,CAAC4H,WAAW,CAACiB,KAAK,CAAC,IAAI,CAAClI,MAAM,CAAC;IACxD,IAAI,CAACyF,kBAAkB,GAAG,IAAI,CAACwB,WAAW,CAACiB,KAAK,CAAC,IAAI,CAAClI,MAAM,CAAC;IAC7D,IAAI,CAAC4N,eAAe,EAAE;IACtB,IAAI,CAACjQ,MAAM,CAAC,IAAI,CAACD,UAAU,EAAE,IAAI,CAACG,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAAChB,UAAU,CAAC;IACvE,IAAI,CAAC0C,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACI,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACiO,oBAAoB,GAAG,EAAE;EAClC;EAEAlQ,MAAMA,CAACmQ,IAAI,EAAEC,KAAK,EAAEjQ,IAAI,EAAEkQ,MAAM;IAC5B,IAAIrG,EAAE,GAAG,IAAI;IACbA,EAAE,CAACF,WAAW,GAAG,KAAK;IACtB,IAAI,CAAC/J,UAAU,GAAGoQ,IAAI;IACtB,IAAI,CAACjQ,QAAQ,GAAGkQ,KAAK;IACrB,IAAI,CAACjQ,IAAI,GAAGA,IAAI;IAChB,IAAImQ,UAAU,GAAG;MACbH,IAAI;MACJ9C,IAAI,EAAE+C,KAAK;MACXjQ;KACH;IACDoQ,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrR,UAAU,CAAC,CAACsR,OAAO,CAACrD,GAAG,IAAG;MACvC,IAAI,IAAI,CAACjO,UAAU,CAACiO,GAAG,CAAC,IAAI,IAAI,EAAE;QAC9B,IAAIA,GAAG,IAAI,UAAU,EAAE;UACnBkD,UAAU,CAAC,UAAU,CAAC,GAAG,IAAI,CAACnR,UAAU,CAAC6N,QAAQ,CAAC0D,OAAO,EAAE;SAC9D,MAAM,IAAItD,GAAG,IAAI,QAAQ,EAAE;UACxBkD,UAAU,CAAC,QAAQ,CAAC,GAAG,IAAI,CAACnR,UAAU,CAAC4N,MAAM,CAAC2D,OAAO,EAAE;SAC1D,MAAM;UACHJ,UAAU,CAAClD,GAAG,CAAC,GAAG,IAAI,CAACjO,UAAU,CAACiO,GAAG,CAAC;;;IAGlD,CAAC,CAAC;IACF,IAAI,CAACvN,OAAO,GAAG;MACXgF,OAAO,EAAE,EAAE;MACXmK,KAAK,EAAE;KACV;IACDhF,EAAE,CAAC2G,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI,CAAC3H,aAAa,CAAC4H,YAAY,CAACP,UAAU,EAAGQ,QAAQ,IAAI;MACrD9G,EAAE,CAACnK,OAAO,GAAG;QACTgF,OAAO,EAAEiM,QAAQ,CAACjM,OAAO;QACzBmK,KAAK,EAAE8B,QAAQ,CAACC;OACnB;MACD,IAAI/G,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAACmF,QAAQ,IAChD7F,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAACoF,QAAQ,EAAE;QAClD,IAAIkB,cAAc,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACnH,EAAE,CAACnK,OAAO,CAACgF,OAAO,CAACuM,MAAM,CAAC5B,IAAI,IAAIA,IAAI,CAACrE,UAAU,KAAK,IAAI,CAAC,CAC9FkG,GAAG,CAAC7B,IAAI,IAAIA,IAAI,CAACrE,UAAoB,CAAC,CAAC,CAAC;QAE7CnB,EAAE,CAACnK,OAAO,CAACgF,OAAO,CAAC4L,OAAO,CAACjB,IAAI,IAAG;UAC9B,IAAIA,IAAI,CAAC8B,QAAQ,KAAK,IAAI,EAAE;YACxBN,cAAc,CAACO,IAAI,CAAC/B,IAAI,CAAC8B,QAAkB,CAAC;;QAEpD,CAAC,CAAC;QAEF,MAAME,iBAAiB,GAAGP,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACH,cAAc,CAAC,CAAC;QAE7DhH,EAAE,CAACd,cAAc,CAACuI,uBAAuB,CAACD,iBAAiB,EAAGV,QAAQ,IAAI;UACtE9G,EAAE,CAACkG,oBAAoB,GAAGY,QAAQ;UAClC9G,EAAE,CAAClK,WAAW,GAAG;YACbmP,gBAAgB,EAAE,KAAK;YACvBC,aAAa,EAAE,KAAK;YACpBL,YAAY,EAAE,IAAI;YAClBM,mBAAmB,EAAE,KAAK;YAC1BC,MAAM,EAAE,CACJ;cACIC,IAAI,EAAE,mBAAmB;cACzBC,OAAO,EAAE,IAAI,CAACzQ,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;cACzDyQ,IAAI,EAAE,SAAAA,CAAU5J,EAAE,EAAE6J,IAAI;gBACpBxF,EAAE,CAACyF,aAAa,CAAC9J,EAAE,EAAE6J,IAAI,EAAE,MAAM,CAAC;cACtC;aACH,EACD;cACIH,IAAI,EAAE,iBAAiB;cACvBC,OAAO,EAAE,IAAI,CAACzQ,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;cACzDyQ,IAAI,EAAE,SAAAA,CAAU5J,EAAE,EAAE6J,IAAI;gBACpBxF,EAAE,CAACyF,aAAa,CAAC9J,EAAE,EAAE6J,IAAI,EAAE,QAAQ,CAAC;cACxC,CAAC;cACDE,UAAU,EAAE,SAAAA,CAAU/J,EAAE,EAAE6J,IAAI;gBAC1B;gBACA,IAAIxF,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAACiF,QAAQ,IAAI3F,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAAC8C,KAAK,EAAE,OAAO,KAAK;gBACnH,IAAIxD,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAACmF,QAAQ,IAAI7F,EAAE,CAACvB,WAAW,CAAC,CAAC5K,SAAS,CAAC+K,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,KAAMkB,EAAE,CAACkG,oBAAoB,KAAKwB,SAAS,IAAI1H,EAAE,CAACkG,oBAAoB,IAAI,IAAI,CAAE,EAAE,OAAO,IAAI;gBAC1M,IAAKlG,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAACmF,QAAQ,IAAI7F,EAAE,CAACvB,WAAW,CAAC,CAAC5K,SAAS,CAAC+K,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,IAAO0G,IAAI,CAACrE,UAAU,IAAI,IAAI,IAAInB,EAAE,CAACkG,oBAAoB,CAACyB,QAAQ,CAACnC,IAAI,CAACrE,UAAU,CAAG,EAAE,OAAO,KAAK;gBACzO,IAAKnB,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAACmF,QAAQ,IAAI7F,EAAE,CAACvB,WAAW,CAAC,CAAC5K,SAAS,CAAC+K,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,IAAO0G,IAAI,CAACrE,UAAU,IAAI,IAAI,IAAIqE,IAAI,CAACI,SAAS,IAAI,IAAI,IAAI5F,EAAE,CAACkG,oBAAoB,CAACyB,QAAQ,CAACnC,IAAI,CAACI,SAAS,CAAC,IAAIJ,IAAI,CAACI,SAAS,IAAI5F,EAAE,CAACC,QAAQ,CAACtE,EAAI,EAAE,OAAO,KAAK;gBACtP,IAAI,CAAC6J,IAAI,CAACI,SAAS,IAAI,CAACJ,IAAI,CAACrE,UAAU,KAAKnB,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAACmF,QAAQ,IAAI7F,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAACoF,QAAQ,CAAC,IAAI9F,EAAE,CAACvB,WAAW,CAAC,CAAC5K,SAAS,CAAC+K,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI;gBACvN,IAAKkB,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAACmF,QAAQ,IAAI7F,EAAE,CAACvB,WAAW,CAAC,CAAC5K,SAAS,CAAC+K,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,KAAO0G,IAAI,CAACrE,UAAU,IAAI,IAAI,IAAI,CAACnB,EAAE,CAACkG,oBAAoB,CAACyB,QAAQ,CAACnC,IAAI,CAACrE,UAAU,CAAC,IAAMqE,IAAI,CAACI,SAAS,IAAI,IAAI,IAAI,CAAC5F,EAAE,CAACkG,oBAAoB,CAACyB,QAAQ,CAACnC,IAAI,CAACI,SAAS,CAAE,CAAC,EAAE,OAAO,IAAI;gBAClS,IAAK5F,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAACmF,QAAQ,IAAIL,IAAI,CAACI,SAAS,KAAK5F,EAAE,CAACC,QAAQ,CAACtE,EAAE,IACrFqE,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAACmF,QAAQ,IAAIL,IAAI,CAACrE,UAAU,IAAI,IAAK,EAAE;kBAC/E,OAAO,KAAK;;gBAEhB,IAAInB,EAAE,CAACvB,WAAW,CAAC,CAAC5K,SAAS,CAAC+K,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,MACjE,OAAO,KAAK;cACrB;aACH,EACD;cACIuG,IAAI,EAAE,WAAW;cACjBC,OAAO,EAAE,IAAI,CAACzQ,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;cAChEyQ,IAAI,EAAE,SAAAA,CAAU5J,EAAE,EAAE6J,IAAI;gBACpBxF,EAAE,CAAC+F,cAAc,CAACpK,EAAE,EAAE6J,IAAI,CAAC;cAC/B;aACH;WAER;UACDxF,EAAE,CAACF,WAAW,GAAG,IAAI;QACzB,CAAC,CAAC;;IAEV,CAAC,EAAE,IAAI,EAAE,MAAK;MACVE,EAAE,CAAC2G,oBAAoB,CAACiB,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACP,IAAI7H,EAAE,GAAG,IAAI;IACb,IAAI,CAAC3H,MAAM,GAAG;MACVsD,EAAE,EAAE,IAAI;MACRiF,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE,IAAI;MAClBjG,OAAO,EAAE,IAAI;MACbkG,IAAI,EAAE,IAAI;MACVrH,KAAK,EAAE,IAAI;MACXsH,IAAI,EAAEnN,SAAS,CAACoN,YAAY,CAACC,SAAS;MACtC/G,MAAM,EAAE,IAAI;MACZxB,SAAS,EAAE,IAAI;MACfwI,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdG,QAAQ,EAAE,IAAI;MACdD,QAAQ,EAAE,IAAI;MACdD,OAAO,EAAE,IAAI;MACbG,aAAa,EAAE,IAAI;MACnBC,SAAS,EAAE,IAAI;MACftM,YAAY,EAAE;KACjB;IACD4K,EAAE,CAAClI,gBAAgB,GAAG,KAAK;IAC3BkI,EAAE,CAACjI,gBAAgB,GAAG,KAAK;IAC3BiI,EAAE,CAAChI,eAAe,GAAG,KAAK;EAC9B;EAEA8P,cAAcA,CAAA;IACV,IAAI,CAAC/R,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,MAAM,CAAC,IAAI,CAACD,UAAU,EAAE,IAAI,CAACG,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAAChB,UAAU,CAAC;EAC3E;EAEA4S,qBAAqBA,CAAA;IACjB,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC7R,IAAI,EAAE,IAAI,CAACgK,kBAAkB,CAAC;EACvE;EAGA6H,aAAaA,CAAC7B,IAAI,EAAEC,KAAK,EAAEjQ,IAAI,EAAEkQ,MAAM;IACnC,IAAIrG,EAAE,GAAG,IAAI;IACb,IAAIsG,UAAU,GAAG;MACbH,IAAI,EAAEA,IAAI;MACV9C,IAAI,EAAE+C,KAAK;MACX6B,QAAQ,EAAEjI,EAAE,CAACkI,WAAW,CAACvM;KAC5B;IACD4K,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,OAAO,CAACrD,GAAG,IAAG;MAC9B,IAAIiD,MAAM,CAACjD,GAAG,CAAC,IAAI,IAAI,EAAE;QACrB,IAAIA,GAAG,IAAI,UAAU,EAAE;UACnBkD,UAAU,CAAC,aAAa,CAAC,GAAGD,MAAM,CAACjG,QAAQ,CAACsG,OAAO,EAAE;SACxD,MAAM,IAAItD,GAAG,IAAI,QAAQ,EAAE;UACxBkD,UAAU,CAAC,WAAW,CAAC,GAAGD,MAAM,CAAChG,MAAM,CAACqG,OAAO,EAAE;SACpD,MAAM;UACHJ,UAAU,CAAClD,GAAG,CAAC,GAAGiD,MAAM,CAACjD,GAAG,CAAC;;;IAGzC,CAAC,CAAC;IACFpD,EAAE,CAAC2G,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI,CAACxH,sBAAsB,CAACpJ,MAAM,CAACsQ,UAAU,EAAGQ,QAAQ,IAAI;MACxD9G,EAAE,CAAC+E,cAAc,GAAG;QAChBlK,OAAO,EAAEiM,QAAQ,CAACjM,OAAO;QACzBmK,KAAK,EAAE8B,QAAQ,CAACC;OACnB;IACL,CAAC,EAAE,IAAI,EAAE,MAAK;MACV/G,EAAE,CAAC2G,oBAAoB,CAACiB,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEA3B,eAAeA,CAAA;IACX,IAAI,CAAC/G,cAAc,CAAC+G,eAAe,CAAEa,QAAQ,IAAI;MAC7C,IAAI,CAACtR,YAAY,GAAGsR,QAAQ,CAACO,GAAG,CAACc,EAAE,IAAG;QAClC,OAAO;UACH,GAAGA,EAAE;UACLvE,OAAO,EAAE,GAAGuE,EAAE,CAACC,IAAI,MAAMD,EAAE,CAAChF,IAAI;SACnC;MACL,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEA;EACAkF,cAAcA,CAAA;IACV,IAAIrI,EAAE,GAAG,IAAI;IACb,IAAI,CAAC2G,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAI0B,QAAQ,GAAG;MACX1H,WAAW,EAAE,IAAI,CAACvI,MAAM,CAACuI,WAAW;MACpCC,YAAY,EAAE,IAAI,CAACxI,MAAM,CAACwI,YAAY;MACtCC,YAAY,EAAE,IAAI,CAACzI,MAAM,CAACyI,YAAY;MACtCjG,OAAO,EAAE,IAAI,CAACxC,MAAM,CAACwC,OAAO,IAAI,IAAI,GAAG,IAAI,CAACxC,MAAM,CAACwC,OAAO,CAAC0N,IAAI,EAAE,GAAG,IAAI;MACxEnH,OAAO,EAAE,IAAI,CAAC/I,MAAM,CAACoJ,aAAa,GAAG,IAAI,GAAG,IAAI,CAACpJ,MAAM,CAACiJ,OAAO,CAAC6B,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC9K,MAAM,CAACkJ,QAAQ,CAAC4B,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC9K,MAAM,CAACmJ,QAAQ,CAAC2B,IAAI;MAC1IpC,IAAI,EAAE,IAAI,CAAC1I,MAAM,CAAC0I,IAAI,IAAI,IAAI,GAAE,IAAI,CAAC1I,MAAM,CAAC0I,IAAI,CAACwH,IAAI,EAAE,GAAG,IAAI;MAC9DvH,IAAI,EAAE,IAAI,CAAC3I,MAAM,CAAC2I,IAAI;MACtBK,QAAQ,EAAE,IAAI,CAAChJ,MAAM,CAACgJ;KACzB;IACD,IAAI,CAACpC,aAAa,CAACuJ,YAAY,CAACF,QAAQ,EAAGG,IAAI,IAAI;MAC/CzI,EAAE,CAAC2G,oBAAoB,CAAC+B,OAAO,CAAC1I,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;MACvFkL,EAAE,CAACQ,mBAAmB,GAAG,KAAK;MAC9BR,EAAE,CAAChK,MAAM,CAACgK,EAAE,CAACjK,UAAU,EAAEiK,EAAE,CAAC9J,QAAQ,EAAE8J,EAAE,CAAC7J,IAAI,EAAE6J,EAAE,CAAC7K,UAAU,CAAC;MAC7D;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA6K,EAAE,CAACf,aAAa,CAAC0J,qBAAqB,CAAC3I,EAAE,CAACC,QAAQ,CAAC7K,YAAY,EAAGwT,KAAK,IAAI;QACvE,IAAIC,KAAK,GAAG,EAAE;QACd,KAAK,IAAIC,IAAI,IAAIF,KAAK,CAACG,UAAU,EAAE;UAC/BF,KAAK,CAACtB,IAAI,CAAC;YACPyB,MAAM,EAAEF,IAAI,CAACE,MAAM;YACnBf,QAAQ,EAAEQ,IAAI,CAAC9M;WAClB,CAAC;;QAEN,IAAI8M,IAAI,EAAEtH,UAAU,EAAE;UAClB0H,KAAK,CAACtB,IAAI,CAAC;YACPyB,MAAM,EAAEP,IAAI,CAACtH,UAAU;YACvB8G,QAAQ,EAAEQ,IAAI,CAAC9M;WAClB,CAAC;;QAENqE,EAAE,CAACf,aAAa,CAACgK,cAAc,CAACJ,KAAK,CAAC;MAC1C,CAAC,CAAC;IACN,CAAC,EAAE,IAAI,EAAE,MAAK;MACV7I,EAAE,CAAC2G,oBAAoB,CAACiB,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAsB,cAAcA,CAAA;IACV,IAAIlJ,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC2G,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI0B,QAAQ,GAAG;MACX1H,WAAW,EAAE,IAAI,CAACvI,MAAM,CAACuI,WAAW;MACpCC,YAAY,EAAE,IAAI,CAACxI,MAAM,CAACwI,YAAY;MACtCC,YAAY,EAAE,IAAI,CAACzI,MAAM,CAACyI,YAAY;MACtCjG,OAAO,EAAE,IAAI,CAACxC,MAAM,CAACwC,OAAO;MAC5BuG,OAAO,EAAE,IAAI,CAAC/I,MAAM,CAAC+I,OAAO;MAC5BL,IAAI,EAAE,IAAI,CAAC1I,MAAM,CAAC0I,IAAI;MACtBC,IAAI,EAAE,IAAI,CAAC3I,MAAM,CAAC2I,IAAI;MACtB7G,MAAM,EAAE,IAAI,CAAC9B,MAAM,CAAC8B,MAAM;MAC1BT,KAAK,EAAE,IAAI,CAACrB,MAAM,CAACqB,KAAK;MACxByH,UAAU,EAAE,IAAI,CAAC9I,MAAM,CAAC8I,UAAU;MAClCE,QAAQ,EAAE,IAAI,CAAChJ,MAAM,CAACgJ,QAAQ;MAC9B8H,OAAO,EAAE,IAAI,CAAC3M;KACjB;IACD,IAAI4M,mBAAmB,GAAG;MACtBnB,QAAQ,EAAE,IAAI,CAAC5P,MAAM,CAACsD,EAAE;MACxB0N,cAAc,EAAE,IAAI,CAAChR,MAAM,CAACqJ,SAAS;MACrC4H,YAAY,EAAE,IAAI,CAACrJ,QAAQ,CAACtE,EAAE;MAC9B4N,KAAK,EAAE,IAAI,CAAC7L;KACf;IACD;IAEA,IAAI,CAACuB,aAAa,CAACuK,YAAY,CAAC,IAAI,CAACnR,MAAM,CAACsD,EAAE,EAAE2M,QAAQ,EAAGG,IAAI,IAAI;MAC/DzI,EAAE,CAACQ,mBAAmB,GAAG,KAAK;MAC9BR,EAAE,CAAC1B,mBAAmB,GAAG,KAAK;MAC9B;MAEA,IAAImK,IAAI,CAACtH,UAAU,IAAI,IAAI,IAAIsH,IAAI,CAACtH,UAAU,IAAIuG,SAAS,EAAE;QACzD1H,EAAE,CAACf,aAAa,CAACgK,cAAc,CAAC,CAAC;UAC7BD,MAAM,EAAEP,IAAI,CAACtH,UAAU;UACvB8G,QAAQ,EAAEQ,IAAI,CAAC9M;SAClB,CAAC,CAAC;;MAEP,IAAIqE,EAAE,CAAC3H,MAAM,CAACM,SAAS,IAAI9E,SAAS,CAACuQ,cAAc,CAACG,WAAW,IAAIvE,EAAE,CAAC3H,MAAM,CAAC8B,MAAM,IAAItG,SAAS,CAACuQ,cAAc,CAACK,IAAI,EAAE;QAClHzE,EAAE,CAACb,gBAAgB,CAACsK,MAAM,CAACL,mBAAmB,EAAGM,GAAG,IAAI;UACpD1J,EAAE,CAAC2G,oBAAoB,CAAC+B,OAAO,CAAC1I,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;UACvFkL,EAAE,CAAChK,MAAM,CAACgK,EAAE,CAACjK,UAAU,EAAEiK,EAAE,CAAC9J,QAAQ,EAAE8J,EAAE,CAAC7J,IAAI,EAAE6J,EAAE,CAAC7K,UAAU,CAAC;QAEjE,CAAC,CAAC;OACL,MAAM;QACH6K,EAAE,CAAC2G,oBAAoB,CAAC+B,OAAO,CAAC1I,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QACvFkL,EAAE,CAAChK,MAAM,CAACgK,EAAE,CAACjK,UAAU,EAAEiK,EAAE,CAAC9J,QAAQ,EAAE8J,EAAE,CAAC7J,IAAI,EAAE6J,EAAE,CAAC7K,UAAU,CAAC;;IAErE,CAAC,EAAE,IAAI,EAAE,MAAK;MACV6K,EAAE,CAAC2G,oBAAoB,CAACiB,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAnT,eAAeA,CAAA;IACX,IAAIuL,EAAE,GAAG,IAAI;IACb,IAAI,CAACQ,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC5I,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAACiQ,WAAW,EAAE;IAClB,IAAI,IAAI,CAAC5H,QAAQ,CAACe,IAAI,KAAKnN,SAAS,CAAC6M,SAAS,CAACiF,QAAQ,EAAE;MACrD,IAAI,CAACtN,MAAM,CAACuI,WAAW,GAAG,IAAI,CAACX,QAAQ,CAAC0J,QAAQ;MAChD,IAAI,CAACtR,MAAM,CAACyI,YAAY,GAAG,IAAI,CAACb,QAAQ,CAAC2J,KAAK;MAC9C,IAAI,CAACvR,MAAM,CAACwI,YAAY,GAAG,IAAI,CAACZ,QAAQ,CAAC6C,KAAK;;IAElD9C,EAAE,CAAClI,gBAAgB,GAAG,KAAK;IAC3BkI,EAAE,CAACjI,gBAAgB,GAAG,KAAK;IAC3BiI,EAAE,CAAChI,eAAe,GAAG,KAAK;IAC1B,IAAI,CAACN,aAAa,GAAG,IAAI,CAAC4H,WAAW,CAACiB,KAAK,CAAC,IAAI,CAAClI,MAAM,CAAC;EAC5D;EAEAoN,aAAaA,CAAC9J,EAAE,EAAE6J,IAAI,EAAE5N,WAAmB;IACvC,IAAIoI,EAAE,GAAG,IAAI;IACbA,EAAE,CAACpI,WAAW,GAAGA,WAAW;IAC5B,IAAI,CAAC4I,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAAClC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACW,aAAa,CAAC4K,eAAe,CAACrE,IAAI,CAAC7J,EAAE,EAAG8M,IAAI,IAAI;MACjD,IAAI,CAACpQ,MAAM,GAAG;QACVsD,EAAE,EAAE8M,IAAI,CAAC9M,EAAE;QACXiF,WAAW,EAAE6H,IAAI,CAAC7H,WAAW;QAC7BC,YAAY,EAAE4H,IAAI,CAAC5H,YAAY;QAC/BC,YAAY,EAAE2H,IAAI,CAAC3H,YAAY;QAC/BjG,OAAO,EAAE4N,IAAI,CAAC5N,OAAO;QACrBkG,IAAI,EAAE0H,IAAI,CAAC1H,IAAI;QACfrH,KAAK,EAAE+O,IAAI,CAAC/O,KAAK;QACjBsH,IAAI,EAAEyH,IAAI,CAACzH,IAAI;QACf7G,MAAM,EAAE,IAAI;QACZxB,SAAS,EAAE8P,IAAI,CAACtO,MAAM;QACtBgH,UAAU,EAAEsH,IAAI,CAACtH,UAAU;QAC3BE,QAAQ,EAAEoH,IAAI,CAACpH,QAAQ;QACvBD,OAAO,EAAEqH,IAAI,CAACrH,OAAO;QACrBE,OAAO,EAAE,IAAI;QACbC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,aAAa,EAAE,IAAI;QACnBC,SAAS,EAAE+G,IAAI,CAAC/G,SAAS;QACzBtM,YAAY,EAAEqT,IAAI,CAACrT;OACtB;MACD,IAAI,CAACgK,sBAAsB,CAACpJ,MAAM,CAAC;QAACiS,QAAQ,EAAE,IAAI,CAAC5P,MAAM,CAACsD;MAAE,CAAC,EAAG+N,GAAG,IAAI;QACnE,IAAI,CAAClN,SAAS,GAAGkN,GAAG,CAAC7O,OAAO;QAC5B;QACA;QACA;QACA,IAAI,CAAC2B,SAAS,CAACiK,OAAO,CAAC1F,IAAI,IAAG;UAC1B,IAAI,CAACrF,OAAO,CAACqF,IAAI,CAACpF,EAAE,CAAC,GAAG,IAAI,CAAC2D,WAAW,CAACiB,KAAK,CAAC;YAC3C1F,OAAO,EAAE,CAAC,EAAE,EAAE,CAACjH,UAAU,CAACuI,QAAQ,EAAEvI,UAAU,CAACwI,SAAS,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC0N,qBAAqB,EAAE,CAAC;WAC/F,CAAC;QACN,CAAC,CAAC;QACF9J,EAAE,CAAC+J,sBAAsB,EAAE;MAC/B,CAAC,CAAC;MACF,IAAI,CAAC5K,gBAAgB,CAACnJ,MAAM,CAAC;QAACiS,QAAQ,EAAE,IAAI,CAAC5P,MAAM,CAACsD,EAAE;QAAE0H,IAAI,EAAE;MAAI,CAAC,EAAGqG,GAAG,IAAI;QACrE,IAAIH,KAAK,GAAa,EAAE;QACxBG,GAAG,CAAC7O,OAAO,CAAC4L,OAAO,CAACjB,IAAI,IAAG;UACvB+D,KAAK,CAAChC,IAAI,CAAC/B,IAAI,CAAC5D,IAAI,CAAC;QACzB,CAAC,CAAC;QACF,IAAI,CAAClE,SAAS,GAAG6L,KAAK;QACtBvJ,EAAE,CAAC+J,sBAAsB,EAAE;MAC/B,CAAC,CACJ;MACD,IAAI,CAACjM,kBAAkB,GAAG,IAAI,CAACwB,WAAW,CAACiB,KAAK,CAAC,IAAI,CAAClI,MAAM,CAAC;MAC7D2H,EAAE,CAAC+J,sBAAsB,EAAE;IAC/B,CAAC,CAAC;IAEF,IAAI,CAAC9K,aAAa,CAAC0J,qBAAqB,CAAC3I,EAAE,CAACC,QAAQ,CAAC7K,YAAY,EAAGqT,IAAI,IAAI;MACxE,IAAI,CAACuB,SAAS,GAAGvB,IAAI,CAACM,UAAU;IACpC,CAAC,CAAC;EACN;EAEAkB,gBAAgBA,CAACC,KAAK;IAClB,IAAIA,KAAK,CAACC,OAAO,EAAE;MACf;;IAEJ,IAAID,KAAK,CAACE,OAAO,IAAI,CAAC,IAAIF,KAAK,CAACE,OAAO,IAAI,EAAE,IAAIF,KAAK,CAACE,OAAO,IAAI,EAAE,IAAIF,KAAK,CAACE,OAAO,IAAI,EAAE,EAAE;MACzF;;IAEJ;IACA,IAAIF,KAAK,CAACE,OAAO,IAAI,EAAE,IAAIF,KAAK,CAACE,OAAO,IAAI,GAAG,IAAIF,KAAK,CAACE,OAAO,IAAI,GAAG,IAAIF,KAAK,CAACE,OAAO,IAAI,GAAG,EAAE;MAC7FF,KAAK,CAACG,cAAc,EAAE;;IAE1B,IAAIH,KAAK,CAACE,OAAO,GAAG,EAAE,IAAIF,KAAK,CAACE,OAAO,GAAG,EAAE,EAAE;MAC1CF,KAAK,CAACG,cAAc,EAAE;;EAE9B;EAEAC,aAAaA,CAACJ,KAAK;IACf,IAAIlK,EAAE,GAAG,IAAI;IACb,IAAIkK,KAAK,CAACnI,KAAK,GAAG,CAAC,EAAE;MACjB/B,EAAE,CAACnI,gBAAgB,GAAG,IAAI;KAC7B,MAAM;MACHmI,EAAE,CAACnI,gBAAgB,GAAG,KAAK;;IAE/B,IAAIqS,KAAK,CAACnI,KAAK,GAAG,KAAK,EAAE;MACrB/B,EAAE,CAAC/H,gBAAgB,GAAG,IAAI;KAC7B,MAAM;MACH+H,EAAE,CAAC/H,gBAAgB,GAAG,KAAK;;EAEnC;EAEAsS,gBAAgBA,CAACL,KAAK;IAClB,IAAIlK,EAAE,GAAG,IAAI;IACb,IAAIA,EAAE,CAAC3H,MAAM,CAACmJ,QAAQ,IAAI,IAAI,EAAE;MAC5BxB,EAAE,CAAC3H,MAAM,CAACkJ,QAAQ,GAAG,IAAI;MACzBvB,EAAE,CAAC3H,MAAM,CAACiJ,OAAO,GAAG,IAAI;MACxBtB,EAAE,CAACiD,aAAa,CAAC7N,YAAY,GAAG8U,KAAK,CAAC9B,IAAI;MAC1CpI,EAAE,CAACJ,qBAAqB,GAAG,KAAK;MAChCI,EAAE,CAACH,oBAAoB,GAAG,IAAI;MAC9BG,EAAE,CAAClI,gBAAgB,GAAG,IAAI;KAC7B,MAAM;MACHkI,EAAE,CAACiD,aAAa,CAAC7N,YAAY,GAAG,CAAC;MACjC4K,EAAE,CAAC3H,MAAM,CAACkJ,QAAQ,GAAG,IAAI;MACzBvB,EAAE,CAAC3H,MAAM,CAACiJ,OAAO,GAAG,IAAI;MACxBtB,EAAE,CAACJ,qBAAqB,GAAG,IAAI;MAC/BI,EAAE,CAACH,oBAAoB,GAAG,IAAI;MAC9BG,EAAE,CAAClI,gBAAgB,GAAG,KAAK;;EAEnC;EAEA0S,gBAAgBA,CAACN,KAAK;IAClB,IAAIlK,EAAE,GAAG,IAAI;IACb,IAAIA,EAAE,CAAC3H,MAAM,CAACkJ,QAAQ,IAAI,IAAI,EAAE;MAC5BvB,EAAE,CAAC3H,MAAM,CAACiJ,OAAO,GAAG,IAAI;MACxBtB,EAAE,CAACiD,aAAa,CAACC,YAAY,GAAGgH,KAAK,CAAC9B,IAAI;MAC1CpI,EAAE,CAACH,oBAAoB,GAAG,KAAK;MAC/BG,EAAE,CAACjI,gBAAgB,GAAG,IAAI;KAC7B,MAAM;MACHiI,EAAE,CAAC3H,MAAM,CAACiJ,OAAO,GAAG,IAAI;MACxBtB,EAAE,CAACiD,aAAa,CAACC,YAAY,GAAG,CAAC;MACjClD,EAAE,CAACH,oBAAoB,GAAG,IAAI;MAC9BG,EAAE,CAACjI,gBAAgB,GAAG,KAAK;;EAEnC;EAEA0S,eAAeA,CAACP,KAAK;IACjB,IAAIlK,EAAE,GAAG,IAAI;IACb,IAAIA,EAAE,CAAC3H,MAAM,CAACiJ,OAAO,IAAI,IAAI,EAAE;MAC3BtB,EAAE,CAAChI,eAAe,GAAG,IAAI;KAC5B,MAAM;MACHgI,EAAE,CAAChI,eAAe,GAAG,KAAK;;EAElC;EAEA0S,gBAAgBA,CAAC3I,KAAK;IAClB,IAAIA,KAAK,EAAE;MACP,IAAI,CAACrC,SAAS,GAAGqC,KAAK;KACzB,MAAM;MACH,IAAI,CAACrC,SAAS,GAAG,IAAI;;EAE7B;EAEAiL,cAAcA,CAAC5I,KAAK;IAChB,IAAIA,KAAK,EAAE;MACP,IAAI,CAACvC,WAAW,GAAGuC,KAAK;KAC3B,MAAM;MACH,IAAI,CAACvC,WAAW,GAAG,IAAIC,IAAI,EAAE;;EAErC;EAEAmL,OAAOA,CAAA;IACH,IAAI5K,EAAE,GAAG,IAAI;IACb,IAAI,IAAI,CAACW,OAAO,KAAK+G,SAAS,IAAI,IAAI,CAAC/G,OAAO,IAAI,CAACkK,KAAK,CAACC,MAAM,CAAC,IAAI,CAACnK,OAAO,CAAC,CAAC,EAAE;MAC5E,IAAI,CAACjD,SAAS,CAAC6J,IAAI,CAACuD,MAAM,CAAC,IAAI,CAACnK,OAAO,CAAC,CAAC;MACzC,IAAI,CAACA,OAAO,GAAG,EAAE;;IAErB,IAAI,IAAI,CAACjD,SAAS,CAACO,MAAM,IAAI,CAAC,EAAE;MAC5B,IAAI,CAACD,eAAe,GAAG,IAAI;KAC9B,MAAM;MACH,IAAI,CAACA,eAAe,GAAG,KAAK;;IAEhC,IAAI,CAACnE,uBAAuB,EAAE;EAClC;EAEAoD,UAAUA,CAAC8N,CAAS;IAChB,IAAI/K,EAAE,GAAG,IAAI;IACb,IAAI,CAACtC,SAAS,CAACsN,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;IAC3B,IAAI,IAAI,CAACrN,SAAS,CAACO,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC5F,MAAM,CAAC8B,MAAM,IAAItG,SAAS,CAACuQ,cAAc,CAACK,IAAI,EAAE;MACnF,IAAI,CAACzG,eAAe,GAAG,IAAI;KAC9B,MAAM;MACH,IAAI,CAACA,eAAe,GAAG,KAAK;;IAEhC,IAAI,CAACnE,uBAAuB,EAAE;EAClC;EAEAoR,cAAcA,CAAA;IACV,IAAIjL,EAAE,GAAG,IAAI;IACb,IAAI,IAAI,CAACW,OAAO,KAAK+G,SAAS,IAAI,IAAI,CAAC/G,OAAO,IAAI,EAAE,EAAE;MAClD,IAAI,IAAI,CAACA,OAAO,CAACuK,QAAQ,EAAE,CAACjN,MAAM,GAAG,EAAE,EAAE;QACrC+B,EAAE,CAAC2C,mBAAmB,GAAG,IAAI;OAChC,MAAM;QACH3C,EAAE,CAAC2C,mBAAmB,GAAG,KAAK;;MAElC,IAAI,IAAI,CAACjF,SAAS,CAACiK,QAAQ,CAACmD,MAAM,CAAE,IAAI,CAACnK,OAAQ,CAAC,CAAC,EAAE;QACjDX,EAAE,CAAC4C,kBAAkB,GAAG,IAAI;OAC/B,MAAM;QACH5C,EAAE,CAAC4C,kBAAkB,GAAG,KAAK;;;EAGzC;EAEAlK,cAAcA,CAACqJ,KAAK;IAChB,IAAI/B,EAAE,GAAG,IAAI;IACb;MACI,IAAI+B,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACC,GAAG,EAAE;QACvC,OAAOrE,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;OACvD,MAAM,IAAIiN,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACE,QAAQ,EAAE;QACnD,OAAOtE,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;OAC5D,MAAM,IAAIiN,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACG,WAAW,EAAE;QACtD,OAAOvE,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;OAC9D,MAAM,IAAIiN,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACI,MAAM,EAAE;QACjD,OAAOxE,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;OAC1D,MAAM,IAAIiN,KAAK,IAAIlO,SAAS,CAACuQ,cAAc,CAACK,IAAI,EAAE;QAC/C,OAAOzE,EAAE,CAACnL,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;;MAEzD,OAAO,EAAE;;EAEjB;EAEAoG,YAAYA,CAACL,OAAO;IAChB,IAAImF,EAAE,GAAG,IAAI;IACb,IAAImL,gBAAgB,GAAG,EAAE,CAAC,CAAC;IAC3B,IAAItQ,OAAO,IAAI,IAAI,EACnB,OAAOA,OAAO,CAACoD,MAAM,GAAGkN,gBAAgB,GAAGtQ,OAAO,CAACuQ,KAAK,CAAC,CAAC,EAAED,gBAAgB,CAAC,GAAG,KAAK,GAAGtQ,OAAO;EACnG;EAEAkP,sBAAsBA,CAAA;IAClB,IAAI/J,EAAE,GAAG,IAAI;IACb;IACA,IAAIA,EAAE,CAACpI,WAAW,IAAI,MAAM,EAAE;MAC1BoI,EAAE,CAACkC,YAAY,GAAG,KAAK;MACvBlC,EAAE,CAACmC,gBAAgB,GAAG,KAAK;MAC3BnC,EAAE,CAAC7C,eAAe,GAAG,KAAK;MAC1B6C,EAAE,CAACoC,UAAU,GAAG,KAAK;MACrBpC,EAAE,CAACqC,cAAc,GAAG,KAAK;MACzBrC,EAAE,CAAC0C,gBAAgB,GAAG,KAAK;MAC3B1C,EAAE,CAAC2C,mBAAmB,GAAG,KAAK;MAC9B,IAAI3C,EAAE,CAAC3H,MAAM,CAACM,SAAS,IAAI9E,SAAS,CAACuQ,cAAc,CAACK,IAAI,EAAE;QACtDzE,EAAE,CAACuC,cAAc,GAAG,IAAI;OAC3B,MAAM;QACHvC,EAAE,CAACuC,cAAc,GAAG,KAAK;;MAE7B,IAAIvC,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAIhB,EAAE,CAACS,QAAQ,CAACoF,QAAQ,IAAI7F,EAAE,CAAC3H,MAAM,CAAC8I,UAAU,IAAI,IAAI,EAAE;QAC1EnB,EAAE,CAACqC,cAAc,GAAG,IAAI;OAC3B,MAAM;QACHrC,EAAE,CAACqC,cAAc,GAAG,KAAK;;;IAGjC,IAAIrC,EAAE,CAACpI,WAAW,IAAI,QAAQ,EAAE;MAC5B;MACA,IAAI,CAACwC,cAAc,GAAG,KAAK;MAC3B,IAAI,CAAC+H,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACG,kBAAkB,GAAG,KAAK;MAE/B;MACAtC,EAAE,CAACjC,kBAAkB,GAAG,KAAK;MAE7B;MACAiC,EAAE,CAACkC,YAAY,GAAG,IAAI;MACtBlC,EAAE,CAACoC,UAAU,GAAG,IAAI;MAEpB;MACA,IAAIpC,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAIhB,EAAE,CAACS,QAAQ,CAACoF,QAAQ,IAAI7F,EAAE,CAAC3H,MAAM,CAAC8I,UAAU,IAAI,IAAI,EAAE;QAC1EnB,EAAE,CAACqC,cAAc,GAAG,IAAI;OAC3B,MAAM;QACHrC,EAAE,CAACqC,cAAc,GAAG,KAAK;;MAG7B;MACA,IAAIrC,EAAE,CAAC3H,MAAM,CAACM,SAAS,IAAI9E,SAAS,CAACuQ,cAAc,CAACG,WAAW,EAAE;QAC7DvE,EAAE,CAAC7C,eAAe,GAAG,IAAI;QACzB,IAAI6C,EAAE,CAAC3H,MAAM,CAAC8B,MAAM,IAAItG,SAAS,CAACuQ,cAAc,CAACK,IAAI,IAAIzE,EAAE,CAACtC,SAAS,CAACO,MAAM,IAAI,CAAC,EAAE;UAC/E+B,EAAE,CAAChC,eAAe,GAAG,IAAI;SAC5B,MAAM;UACHgC,EAAE,CAAChC,eAAe,GAAG,KAAK;;OAEjC,MAAM;QACHgC,EAAE,CAAC7C,eAAe,GAAG,KAAK;;MAG9B;MACA;MACA;MACA;MACA;MAEA;MACA,IAAI6C,EAAE,CAAC3H,MAAM,CAACM,SAAS,IAAI9E,SAAS,CAACuQ,cAAc,CAACG,WAAW,EAAE;QAC7DvE,EAAE,CAACuC,cAAc,GAAG,IAAI;OAC3B,MAAM;QACHvC,EAAE,CAACuC,cAAc,GAAG,KAAK;;MAE7B;MACA,IAAIvC,EAAE,CAAC3H,MAAM,CAACM,SAAS,IAAI9E,SAAS,CAACuQ,cAAc,CAACC,GAAG,EAAE;QACrDrE,EAAE,CAACqC,cAAc,GAAG,KAAK;;;IAKjC;IACA,IAAIrC,EAAE,CAAC3H,MAAM,CAACM,SAAS,IAAI9E,SAAS,CAACuQ,cAAc,CAACK,IAAI,IAAIzE,EAAE,CAAC3H,MAAM,CAACM,SAAS,IAAI9E,SAAS,CAACuQ,cAAc,CAACI,MAAM,EAAE;MAChH;MACAxE,EAAE,CAACqC,cAAc,GAAG,KAAK;MAEzB,IAAIrC,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAIhB,EAAE,CAACS,QAAQ,CAACoF,QAAQ,IAAI7F,EAAE,CAAC3H,MAAM,CAAC8I,UAAU,IAAI,IAAI,EAAE;QAC1EnB,EAAE,CAACqC,cAAc,GAAG,IAAI;OAC3B,MAAM;QACHrC,EAAE,CAACqC,cAAc,GAAG,KAAK;;;IAKjC;IACA,IAAI,IAAI,CAAC7F,SAAS,CAACyB,MAAM,IAAI,CAAC,EAAE;MAC5B+B,EAAE,CAACwC,cAAc,GAAG,KAAK;KAC5B,MAAM;MACHxC,EAAE,CAACwC,cAAc,GAAG,IAAI;;IAE5BxC,EAAE,CAACjC,kBAAkB,GAAG,IAAI;EAChC;EAEAlE,uBAAuBA,CAAA;IACnB,IAAImG,EAAE,GAAG,IAAI;IACb;IACA,IAAI,IAAI,CAAC3H,MAAM,CAAC8B,MAAM,IAAI,IAAI,EAAE;MAC5B6F,EAAE,CAAC5F,cAAc,GAAG,IAAI;MACxB4F,EAAE,CAACqC,cAAc,GAAG,KAAK;KAC5B,MAAM;MACHrC,EAAE,CAAC5F,cAAc,GAAG,KAAK;MACzB,IAAI4F,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAIhB,EAAE,CAACS,QAAQ,CAACoF,QAAQ,IAAI7F,EAAE,CAAC3H,MAAM,CAAC8I,UAAU,IAAI,IAAI,EAAE;QAC1EnB,EAAE,CAACqC,cAAc,GAAG,IAAI;;;IAIhC,IAAIrC,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAACmF,QAAQ,EAAE;MAClD,IAAI,IAAI,CAACxN,MAAM,CAAC8I,UAAU,IAAI,IAAI,IAAI,IAAI,CAAC9I,MAAM,CAAC8B,MAAM,IAAI,IAAI,EAAE;QAC9D,IAAI,CAAC+H,YAAY,GAAG,IAAI;QACxB,IAAI,CAACE,UAAU,GAAG,IAAI;QACtB,IAAI,CAACC,cAAc,GAAG,IAAI;OAC7B,MAAM,IAAI,IAAI,CAAChK,MAAM,CAAC8I,UAAU,IAAI,IAAI,IAAInB,EAAE,CAACkG,oBAAoB,CAACyB,QAAQ,CAAC3H,EAAE,CAAC3H,MAAM,CAAC8I,UAAU,CAAC,EAAE;QACjG,IAAI,CAACe,YAAY,GAAG,KAAK;QACzB,IAAI,CAACE,UAAU,GAAG,KAAK;OAC1B,MAAM,IAAI,IAAI,CAAC/J,MAAM,CAAC8B,MAAM,IAAI,IAAI,IAAK,IAAI,CAAC9B,MAAM,CAACqB,KAAK,IAAI,IAAI,IAAIsG,EAAE,CAAC3H,MAAM,CAACqB,KAAK,CAAC6O,IAAI,EAAE,IAAI,EAAG,EAAE;QAClG,IAAI,CAACrG,YAAY,GAAG,IAAI;QACxB,IAAI,CAACE,UAAU,GAAG,IAAI;;KAE7B,MAAM,IAAIpC,EAAE,CAACC,QAAQ,CAACe,IAAI,IAAInN,SAAS,CAAC6M,SAAS,CAACoF,QAAQ,EAAE;MACzD9F,EAAE,CAACkC,YAAY,GAAG,IAAI;MACtBlC,EAAE,CAACoC,UAAU,GAAG,IAAI;;IAGxB,IAAIpC,EAAE,CAAC3H,MAAM,CAACM,SAAS,IAAI9E,SAAS,CAACuQ,cAAc,CAACG,WAAW,IAAIvE,EAAE,CAAC3H,MAAM,CAAC8B,MAAM,IAAItG,SAAS,CAACuQ,cAAc,CAACK,IAAI,IAAI,IAAI,CAAC/G,SAAS,CAACO,MAAM,IAAI,CAAC,EAAE;MAChJ+B,EAAE,CAAChC,eAAe,GAAG,IAAI;MACzBgC,EAAE,CAAC0C,gBAAgB,GAAG,IAAI;KAC7B,MAAM;MACH1C,EAAE,CAAChC,eAAe,GAAG,KAAK;MAC1BgC,EAAE,CAAC0C,gBAAgB,GAAG,KAAK;;IAG/B,IAAI1C,EAAE,CAAC3H,MAAM,CAAC8B,MAAM,IAAG,IAAI,IAAK6F,EAAE,CAAC3H,MAAM,CAACqB,KAAK,IAAI,IAAI,IAAIsG,EAAE,CAAC3H,MAAM,CAACqB,KAAK,CAAC6O,IAAI,EAAE,IAAI,EAAG,EAAE;MACtFvI,EAAE,CAACjC,kBAAkB,GAAG,KAAK;KAChC,MAAM;MACHiC,EAAE,CAACjC,kBAAkB,GAAG,IAAI;;IAGhC;IACA,IAAIiC,EAAE,CAAC3H,MAAM,CAACM,SAAS,IAAI9E,SAAS,CAACuQ,cAAc,CAACC,GAAG,EAAE;MACrDrE,EAAE,CAACqC,cAAc,GAAG,KAAK;;IAG7B,IAAI,CAAChD,GAAG,CAACgM,aAAa,EAAE;EAC5B;EAEAtF,cAAcA,CAACpK,EAAE,EAAE6J,IAAI;IACnB,IAAI,CAAC/C,WAAW,GAAG,IAAI;IACvB,IAAI,CAACyF,WAAW,GAAG1C,IAAI;IACvB,IAAI,CAACuC,qBAAqB,EAAE;EAChC;EAEAnK,oBAAoBA,CAAA;IAChB,IAAI,IAAI,CAAC8E,gBAAgB,EAAE;MACvB,OAAO,IAAI,CAAC7N,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;;IAEhE,IAAI,IAAI,CAAC6N,mBAAmB,EAAE;MAC1B,OAAO,IAAI,CAAC9N,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC;;IAErE,IAAI,IAAI,CAAC8N,kBAAkB,EAAE;MACzB,OAAO,IAAI,CAAC/N,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;;IAEnE,OAAO,IAAI;EACf;EACAoJ,WAAWA,CAAA;IACP,OAAOqI,MAAM,CAAC+E,MAAM,CAAC,IAAI,CAAC5P,OAAO,CAAC,CAAC6P,KAAK,CAAEC,SAAoB,IAAKA,SAAS,CAACC,KAAK,CAAC;EACvF;EACA3B,qBAAqBA,CAAA;IACjB,OAAQ4B,OAAwB,IAA6B;MACzD,MAAMC,YAAY,GAAG,CAACD,OAAO,CAAC3J,KAAK,IAAI,EAAE,EAAEwG,IAAI,EAAE,CAACtK,MAAM,KAAK,CAAC;MAC9D,MAAM2N,OAAO,GAAG,CAACD,YAAY;MAC7B,OAAOC,OAAO,GAAG,IAAI,GAAG;QAACC,UAAU,EAAE;MAAI,CAAC;IAC9C,CAAC;EACL;EACAC,aAAaA,CAAC5B,KAAK;IACf,IAAIA,KAAK,CAAC9G,GAAG,KAAK,GAAG,KAAK,IAAI,CAAC/K,MAAM,CAAC0I,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC1I,MAAM,CAAC0I,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC1I,MAAM,CAAC0I,IAAI,CAACwH,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MAC/G2B,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAI,IAAI,CAAChS,MAAM,CAAC0I,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC1I,MAAM,CAAC0I,IAAI,CAACwH,IAAI,EAAE,IAAI,EAAE,EAAE;MAC3D,IAAI,CAAClQ,MAAM,CAAC0I,IAAI,GAAG,IAAI,CAAC1I,MAAM,CAAC0I,IAAI,CAACgL,SAAS,EAAE,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MACvE;;EAER;EACAC,gBAAgBA,CAAC/B,KAAK;IAClB,IAAIA,KAAK,CAAC9G,GAAG,KAAK,GAAG,KAAK,IAAI,CAAC/K,MAAM,CAACwC,OAAO,IAAI,IAAI,IAAI,IAAI,CAACxC,MAAM,CAACwC,OAAO,IAAI,IAAI,IAAI,IAAI,CAACxC,MAAM,CAACwC,OAAO,CAAC0N,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MACxH2B,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAI,IAAI,CAAChS,MAAM,CAACwC,OAAO,IAAI,IAAI,IAAI,IAAI,CAACxC,MAAM,CAACwC,OAAO,CAAC0N,IAAI,EAAE,IAAI,EAAE,EAAE;MACjE,IAAI,CAAClQ,MAAM,CAACwC,OAAO,GAAG,IAAI,CAACxC,MAAM,CAACwC,OAAO,CAACkR,SAAS,EAAE,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MAC7E;;EAER;EACAhS,cAAcA,CAACkQ,KAAK;IAChB,IAAIA,KAAK,CAAC9G,GAAG,KAAK,GAAG,KAAK,IAAI,CAAC/K,MAAM,CAACqB,KAAK,IAAI,IAAI,IAAI,IAAI,CAACrB,MAAM,CAACqB,KAAK,IAAI,IAAI,IAAI,IAAI,CAACrB,MAAM,CAACqB,KAAK,CAAC6O,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MAClH2B,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAI,IAAI,CAAChS,MAAM,CAACqB,KAAK,IAAI,IAAI,IAAI,IAAI,CAACrB,MAAM,CAACqB,KAAK,CAAC6O,IAAI,EAAE,IAAI,EAAE,EAAE;MAC7D,IAAI,CAAClQ,MAAM,CAACqB,KAAK,GAAG,IAAI,CAACrB,MAAM,CAACqB,KAAK,CAACqS,SAAS,EAAE,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MACzE;;EAER;EACAhR,oBAAoBA,CAACkP,KAAoB,EAAEnJ,IAAS;IAChD,IAAImJ,KAAK,CAAC9G,GAAG,KAAK,GAAG,KAAK,CAACrC,IAAI,CAAClG,OAAO,IAAIkG,IAAI,CAAClG,OAAO,CAAC0N,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MACpE2B,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAItJ,IAAI,CAAClG,OAAO,IAAIkG,IAAI,CAAClG,OAAO,CAAC0N,IAAI,EAAE,KAAK,EAAE,EAAE;MAC5CxH,IAAI,CAAClG,OAAO,GAAGkG,IAAI,CAAClG,OAAO,CAACkR,SAAS,EAAE,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MAC/D;;EAER;;;uBAztCSjN,2BAA2B,EAAA/K,EAAA,CAAAkY,iBAAA,CAyGxBxY,aAAa,GAAAM,EAAA,CAAAkY,iBAAA,CACbvY,cAAc,GAAAK,EAAA,CAAAkY,iBAAA,CACdpY,gBAAgB,GAAAE,EAAA,CAAAkY,iBAAA,CAChBnY,sBAAsB,GAAAC,EAAA,CAAAkY,iBAAA,CAAAlY,EAAA,CAAAmY,iBAAA,GAAAnY,EAAA,CAAAkY,iBAAA,CAAAE,EAAA,CAAAC,WAAA,GAAArY,EAAA,CAAAkY,iBAAA,CAAAlY,EAAA,CAAAsY,QAAA;IAAA;EAAA;;;YA5GzBvN,2BAA2B;MAAAwN,SAAA;MAAAC,QAAA,GAAAxY,EAAA,CAAAyY,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClBxC/Y,EAAA,CAAAC,cAAA,aAAqG;UAEzDD,EAAA,CAAAqB,MAAA,GAAmD;UAAArB,EAAA,CAAAU,YAAA,EAAM;UAC7FV,EAAA,CAAA0B,SAAA,sBAAoF;UACxF1B,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,aAAwE;UACpED,EAAA,CAAAqF,UAAA,IAAA4T,+CAAA,sBAIW;UACfjZ,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,cAAoG;UAA/DD,EAAA,CAAAE,UAAA,sBAAAgZ,8DAAA;YAAA,OAAYF,GAAA,CAAAlF,cAAA,EAAgB;UAAA,EAAC;UAC9D9T,EAAA,CAAAC,cAAA,iBAAoF;UAG5ED,EAAA,CAAAqF,UAAA,KAAA8T,2CAAA,iBAcM;UACNnZ,EAAA,CAAAC,cAAA,eAAmB;UAKCD,EAAA,CAAAE,UAAA,2BAAAkZ,0EAAApY,MAAA;YAAA,OAAAgY,GAAA,CAAA7X,UAAA,CAAAgF,MAAA,GAAAnF,MAAA;UAAA,EAA+B;UAQ1ChB,EAAA,CAAAU,YAAA,EAAa;UACdV,EAAA,CAAAC,cAAA,iBAA+C;UAAAD,EAAA,CAAAqB,MAAA,IAAkD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAIjHV,EAAA,CAAAC,cAAA,eAAmB;UAIJD,EAAA,CAAAE,UAAA,2BAAAmZ,qEAAArY,MAAA;YAAA,OAAAgY,GAAA,CAAA7X,UAAA,CAAAyL,WAAA,GAAA5L,MAAA;UAAA,EAAoC;UAF3ChB,EAAA,CAAAU,YAAA,EAIE;UACFV,EAAA,CAAAC,cAAA,iBAAuB;UAAAD,EAAA,CAAAqB,MAAA,IAAoD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAI3FV,EAAA,CAAAC,cAAA,eAAmB;UAIJD,EAAA,CAAAE,UAAA,2BAAAoZ,qEAAAtY,MAAA;YAAA,OAAAgY,GAAA,CAAA7X,UAAA,CAAA2L,YAAA,GAAA9L,MAAA;UAAA,EAAqC,qBAAAuY,+DAAAvY,MAAA;YAAA,OAG1BgY,GAAA,CAAA/C,gBAAA,CAAAjV,MAAA,CAAwB;UAAA,EAHE;UAF5ChB,EAAA,CAAAU,YAAA,EAOE;UACFV,EAAA,CAAAC,cAAA,iBAAuB;UAAAD,EAAA,CAAAqB,MAAA,IAAiD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAGxFV,EAAA,CAAAC,cAAA,eAAmB;UAICD,EAAA,CAAAE,UAAA,2BAAAsZ,0EAAAxY,MAAA;YAAA,OAAAgY,GAAA,CAAA7X,UAAA,CAAA6N,QAAA,GAAAhO,MAAA;UAAA,EAAiC,sBAAAyY,qEAAA;YAAA,OAMrBT,GAAA,CAAAtC,gBAAA,CAAAsC,GAAA,CAAA7X,UAAA,CAAA6N,QAAA,CAAqC;UAAA,EANhB,qBAAA0K,oEAAA;YAAA,OAOtBV,GAAA,CAAAtC,gBAAA,CAAAsC,GAAA,CAAA7X,UAAA,CAAA6N,QAAA,CAAqC;UAAA,EAPf;UAQ5ChP,EAAA,CAAAU,YAAA,EAAa;UACdV,EAAA,CAAAC,cAAA,iBAAiD;UAAAD,EAAA,CAAAqB,MAAA,IAAoD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAGrHV,EAAA,CAAAC,cAAA,eAAmB;UAICD,EAAA,CAAAE,UAAA,2BAAAyZ,0EAAA3Y,MAAA;YAAA,OAAAgY,GAAA,CAAA7X,UAAA,CAAA4N,MAAA,GAAA/N,MAAA;UAAA,EAA+B,sBAAA4Y,qEAAA;YAAA,OAOnBZ,GAAA,CAAArC,cAAA,CAAAqC,GAAA,CAAA7X,UAAA,CAAA4N,MAAA,CAAiC;UAAA,EAPd,qBAAA8K,oEAAA;YAAA,OAQpBb,GAAA,CAAArC,cAAA,CAAAqC,GAAA,CAAA7X,UAAA,CAAA4N,MAAA,CAAiC;UAAA,EARb;UAF3C/O,EAAA,CAAAU,YAAA,EAWE;UACFV,EAAA,CAAAC,cAAA,iBAA+C;UAAAD,EAAA,CAAAqB,MAAA,IAAkD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAGjHV,EAAA,CAAAC,cAAA,eAAwB;UACpBD,EAAA,CAAA0B,SAAA,oBAGY;UAChB1B,EAAA,CAAAU,YAAA,EAAM;UAKlBV,EAAA,CAAAqF,UAAA,KAAAyU,kDAAA,0BAYc;UACd9Z,EAAA,CAAAqF,UAAA,KAAA0U,kDAAA,0BAYc;UAEd/Z,EAAA,CAAAC,cAAA,eAAyC;UACoCD,EAAA,CAAAE,UAAA,2BAAA8Z,wEAAAhZ,MAAA;YAAA,OAAAgY,GAAA,CAAAxM,mBAAA,GAAAxL,MAAA;UAAA,EAAiC;UAEtGhB,EAAA,CAAAC,cAAA,gBAA6E;UAA9BD,EAAA,CAAAE,UAAA,sBAAA+Z,+DAAA;YAAA,OAAYjB,GAAA,CAAA3E,cAAA,EAAgB;UAAA,EAAC;UACxErU,EAAA,CAAAC,cAAA,eAAoE;UAIjCD,EAAA,CAAAqB,MAAA,IAAwD;UAAArB,EAAA,CAAAC,cAAA,gBAC1D;UAAAD,EAAA,CAAAqB,MAAA,SAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UACjCV,EAAA,CAAAC,cAAA,eAAiB;UAGND,EAAA,CAAAE,UAAA,2BAAAga,qEAAAlZ,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAAuI,WAAA,GAAA5L,MAAA;UAAA,EAAgC;UAFvChB,EAAA,CAAAU,YAAA,EASE;UAIVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA0B,SAAA,iBAAwE;UACxE1B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAqF,UAAA,KAAA8U,6CAAA,oBAC6K;UAC7Kna,EAAA,CAAAqF,UAAA,KAAA+U,6CAAA,oBAC+I;UAC/Ipa,EAAA,CAAAqF,UAAA,KAAAgV,6CAAA,oBACuI;UAC3Ira,EAAA,CAAAU,YAAA,EAAM;UAIVV,EAAA,CAAAC,cAAA,eAA+B;UAEAD,EAAA,CAAAqB,MAAA,IAAiD;UAAArB,EAAA,CAAAC,cAAA,gBACnD;UAAAD,EAAA,CAAAqB,MAAA,SAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UACjCV,EAAA,CAAAC,cAAA,eAAiB;UAGND,EAAA,CAAAE,UAAA,2BAAAoa,qEAAAtZ,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAAwI,YAAA,GAAA7L,MAAA;UAAA,EAAiC;UAFxChB,EAAA,CAAAU,YAAA,EASE;UAIVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA0B,SAAA,iBAA4E;UAC5E1B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAqF,UAAA,KAAAkV,6CAAA,oBAC+K;UAC/Kva,EAAA,CAAAqF,UAAA,KAAAmV,6CAAA,oBACgJ;UAChJxa,EAAA,CAAAqF,UAAA,KAAAoV,6CAAA,oBACqI;UAEzIza,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAA+B;UAEAD,EAAA,CAAAqB,MAAA,IAAiD;UAAArB,EAAA,CAAAC,cAAA,gBACnD;UAAAD,EAAA,CAAAqB,MAAA,SAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UACjCV,EAAA,CAAAC,cAAA,eAAiB;UAGND,EAAA,CAAAE,UAAA,2BAAAwa,qEAAA1Z,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAAyI,YAAA,GAAA9L,MAAA;UAAA,EAAiC,qBAAA2Z,+DAAA3Z,MAAA;YAAA,OAGtBgY,GAAA,CAAA/C,gBAAA,CAAAjV,MAAA,CAAwB;UAAA,EAHF;UAFxChB,EAAA,CAAAU,YAAA,EASE;UAIVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA0B,SAAA,iBAAqE;UACrE1B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAqF,UAAA,KAAAuV,6CAAA,oBAC+K;UAC/K5a,EAAA,CAAAqF,UAAA,KAAAwV,6CAAA,oBACqI;UACzI7a,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAA+B;UAEqBD,EAAA,CAAAqB,MAAA,IAC5C;UAAArB,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAqB,MAAA,SAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UACvCV,EAAA,CAAAC,cAAA,eAAiB;UAIED,EAAA,CAAAE,UAAA,2BAAA4a,6EAAA9Z,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAAgJ,QAAA,GAAArM,MAAA;UAAA,EAA6B,qBAAA+Z,uEAAA/Z,MAAA;YAAA,OAIlBgY,GAAA,CAAA1C,aAAA,CAAAtV,MAAA,CAAqB;UAAA,EAJH;UAK3ChB,EAAA,CAAAU,YAAA,EAAgB;UAIzBV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA0B,SAAA,iBAAwE;UACxE1B,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAqF,UAAA,KAAA2V,6CAAA,oBACuK;UACvKhb,EAAA,CAAAqF,UAAA,KAAA4V,6CAAA,oBACiG;UACjGjb,EAAA,CAAAqF,UAAA,KAAA6V,6CAAA,oBACiG;UACrGlb,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAA+B;UAEqBD,EAAA,CAAAqB,MAAA,IAC5C;UAAArB,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAqB,MAAA,SAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UAE3CV,EAAA,CAAAC,cAAA,mBAA4B;UAIhBD,EAAA,CAAAE,UAAA,yBAAAib,0EAAAna,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAAmJ,QAAA,GAAAxM,MAAA;UAAA,EAA2B,sBAAAoa,uEAAApa,MAAA;YAAA,OAUfgY,GAAA,CAAAzC,gBAAA,CAAAvV,MAAA,CAAwB;UAAA,EAVT;UAY/BhB,EAAA,CAAAU,YAAA,EAAc;UACdV,EAAA,CAAAC,cAAA,wBAaC;UAZYD,EAAA,CAAAE,UAAA,yBAAAmb,0EAAAra,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAAkJ,QAAA,GAAAvM,MAAA;UAAA,EAA2B,sBAAAsa,uEAAAta,MAAA;YAAA,OAWfgY,GAAA,CAAAxC,gBAAA,CAAAxV,MAAA,CAAwB;UAAA,EAXT;UAaxChB,EAAA,CAAAU,YAAA,EAAc;UAEdV,EAAA,CAAAC,cAAA,wBAaC;UAZYD,EAAA,CAAAE,UAAA,yBAAAqb,0EAAAva,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAAiJ,OAAA,GAAAtM,MAAA;UAAA,EAA0B,sBAAAwa,uEAAAxa,MAAA;YAAA,OAWdgY,GAAA,CAAAvC,eAAA,CAAAzV,MAAA,CAAuB;UAAA,EAXT;UAavChB,EAAA,CAAAU,YAAA,EAAc;UAGlBV,EAAA,CAAAC,cAAA,gBAAkB;UAMHD,EAAA,CAAAE,UAAA,2BAAAub,sEAAAza,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAAoJ,aAAA,GAAAzM,MAAA;UAAA,EAAkC;UAJzChB,EAAA,CAAAU,YAAA,EAUE;UAKVV,EAAA,CAAAC,cAAA,gBAAgD;UAC5CD,EAAA,CAAA0B,SAAA,kBAA6E;UAC7E1B,EAAA,CAAAC,cAAA,gBAAiB;UACbD,EAAA,CAAAqF,UAAA,MAAAqW,8CAAA,oBACiJ;UACjJ1b,EAAA,CAAAqF,UAAA,MAAAsW,8CAAA,oBACiL;UACjL3b,EAAA,CAAAqF,UAAA,MAAAuW,8CAAA,oBACoI;UACxI5b,EAAA,CAAAU,YAAA,EAAM;UAIdV,EAAA,CAAAC,cAAA,gBAA+B;UAEqBD,EAAA,CAAAqB,MAAA,KAAmD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAC3GV,EAAA,CAAAC,cAAA,gBAAiB;UAKCD,EAAA,CAAAE,UAAA,2BAAA2b,yEAAA7a,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAAwC,OAAA,GAAA7F,MAAA;UAAA,EAA4B,qBAAA8a,mEAAA9a,MAAA;YAAA,OAIjBgY,GAAA,CAAAf,gBAAA,CAAAjX,MAAA,CAAwB;UAAA,EAJP;UAMrChB,EAAA,CAAAU,YAAA,EAAW;UAIxBV,EAAA,CAAAC,cAAA,gBAAgD;UAC5CD,EAAA,CAAA0B,SAAA,kBAAuE;UACvE1B,EAAA,CAAAC,cAAA,gBAAiB;UACbD,EAAA,CAAAqF,UAAA,MAAA0W,8CAAA,oBAC2I;UAC3I/b,EAAA,CAAAqF,UAAA,MAAA2W,8CAAA,oBAC8H;UAClIhc,EAAA,CAAAU,YAAA,EAAM;UAKVV,EAAA,CAAAC,cAAA,gBAA+B;UAEqBD,EAAA,CAAAqB,MAAA,KAAgD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UACxGV,EAAA,CAAAC,cAAA,gBAAiB;UAKCD,EAAA,CAAAE,UAAA,2BAAA+b,yEAAAjb,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAA0I,IAAA,GAAA/L,MAAA;UAAA,EAAyB,qBAAAkb,mEAAAlb,MAAA;YAAA,OAIdgY,GAAA,CAAAlB,aAAA,CAAA9W,MAAA,CAAqB;UAAA,EAJP;UAMlChB,EAAA,CAAAU,YAAA,EAAW;UAIxBV,EAAA,CAAAC,cAAA,gBAAgD;UAC5CD,EAAA,CAAA0B,SAAA,kBAAoE;UACpE1B,EAAA,CAAAC,cAAA,gBAAiB;UACbD,EAAA,CAAAqF,UAAA,MAAA8W,8CAAA,oBACwI;UACxInc,EAAA,CAAAqF,UAAA,MAAA+W,8CAAA,oBAC2H;UAC/Hpc,EAAA,CAAAU,YAAA,EAAM;UAGdV,EAAA,CAAAC,cAAA,gBAA0E;UAE5DD,EAAA,CAAAE,UAAA,mBAAAmc,iEAAA;YAAA,OAAArD,GAAA,CAAAxM,mBAAA,GAA+B,KAAK;UAAA,EAAC;UAACxM,EAAA,CAAAU,YAAA,EAAW;UAC3DV,EAAA,CAAAqF,UAAA,MAAAiX,iDAAA,uBAE2E;UAC/Etc,EAAA,CAAAU,YAAA,EAAM;UAIdV,EAAA,CAAAC,cAAA,qBAGyF;UADrFD,EAAA,CAAAE,UAAA,2BAAAqc,yEAAAvb,MAAA;YAAA,OAAAgY,GAAA,CAAA1O,mBAAA,GAAAtJ,MAAA;UAAA,EAAiC;UAEjChB,EAAA,CAAAC,cAAA,iBAAkF;UAA9BD,EAAA,CAAAE,UAAA,sBAAAsc,gEAAA;YAAA,OAAYxD,GAAA,CAAA9D,cAAA,EAAgB;UAAA,EAAC;UAC7ElV,EAAA,CAAAC,cAAA,gBAAsC;UAG1BD,EAAA,CAAAqF,UAAA,MAAAoX,4CAAA,kBAiBM;UAENzc,EAAA,CAAAC,cAAA,gBAA+B;UAEAD,EAAA,CAAAqB,MAAA,KAAwD;UAAArB,EAAA,CAAAC,cAAA,iBAC1D;UAAAD,EAAA,CAAAqB,MAAA,UAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UACjCV,EAAA,CAAAC,cAAA,gBAAiB;UAGND,EAAA,CAAAE,UAAA,2BAAAwc,sEAAA1b,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAAuI,WAAA,GAAA5L,MAAA;UAAA,EAAgC;UAFvChB,EAAA,CAAAU,YAAA,EASE;UAKVV,EAAA,CAAAC,cAAA,gBAA+B;UAEAD,EAAA,CAAAqB,MAAA,KAAiD;UAAArB,EAAA,CAAAC,cAAA,iBACnD;UAAAD,EAAA,CAAAqB,MAAA,UAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UACjCV,EAAA,CAAAC,cAAA,gBAAiB;UAGND,EAAA,CAAAE,UAAA,2BAAAyc,sEAAA3b,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAAyI,YAAA,GAAA9L,MAAA;UAAA,EAAiC,qBAAA4b,gEAAA5b,MAAA;YAAA,OAItBgY,GAAA,CAAA/C,gBAAA,CAAAjV,MAAA,CAAwB;UAAA,EAJF;UAFxChB,EAAA,CAAAU,YAAA,EASE;UAKVV,EAAA,CAAAC,cAAA,gBAA+B;UAEAD,EAAA,CAAAqB,MAAA,KAA2D;UAAArB,EAAA,CAAAC,cAAA,iBAC7D;UAAAD,EAAA,CAAAqB,MAAA,UAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UACjCV,EAAA,CAAAC,cAAA,gBAAiB;UAIHD,EAAA,CAAAE,UAAA,2BAAA2c,yEAAA7b,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAA+I,OAAA,GAAApM,MAAA;UAAA,EAA4B;UAKrChB,EAAA,CAAAU,YAAA,EAAW;UAIpBV,EAAA,CAAAC,cAAA,gBAAsE;UAEvCD,EAAA,CAAAqB,MAAA,KAAkD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UACrFV,EAAA,CAAAC,cAAA,gBAAiB;UAIDD,EAAA,CAAAE,UAAA,2BAAA4c,2EAAA9b,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAA8B,MAAA,GAAAnF,MAAA;UAAA,EAA2B,sBAAA+b,sEAAA;YAAA,OAQf/D,GAAA,CAAAnT,uBAAA,EAAyB;UAAA,EARV;UAStC7F,EAAA,CAAAU,YAAA,EAAa;UAItBV,EAAA,CAAAC,cAAA,gBAC0D;UAE3BD,EAAA,CAAAqB,MAAA,KAAkD;UAAArB,EAAA,CAAAC,cAAA,iBACpD;UAAAD,EAAA,CAAAqB,MAAA,UAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UACjCV,EAAA,CAAAC,cAAA,gBAAiB;UAYbD,EAAA,CAAAqF,UAAA,MAAA2X,6CAAA,mBAAyL;UACzLhd,EAAA,CAAAqF,UAAA,MAAA4X,6CAAA,mBAAmM;UACnMjd,EAAA,CAAAqF,UAAA,MAAA6X,6CAAA,mBAAoM;UACpMld,EAAA,CAAAqF,UAAA,MAAA8X,6CAAA,mBAA4L;UAC5Lnd,EAAA,CAAAqF,UAAA,MAAA+X,6CAAA,mBAA4L;UAChMpd,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,gBAAgD;UAC5CD,EAAA,CAAA0B,SAAA,kBAAwE;UACxE1B,EAAA,CAAAC,cAAA,gBAAiB;UACbD,EAAA,CAAAqF,UAAA,MAAAgY,8CAAA,oBACmK;UACvKrd,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAqF,UAAA,MAAAiY,4CAAA,kBAoBM;UAENtd,EAAA,CAAAC,cAAA,gBAAgD;UAC5CD,EAAA,CAAA0B,SAAA,kBAAqE;UACrE1B,EAAA,CAAAC,cAAA,gBAAiB;UACbD,EAAA,CAAAqF,UAAA,MAAAkY,8CAAA,oBACyI;UACzIvd,EAAA,CAAAqF,UAAA,MAAAmY,8CAAA,oBACiK;UACrKxd,EAAA,CAAAU,YAAA,EAAM;UAIlBV,EAAA,CAAAC,cAAA,gBAA2B;UAIYD,EAAA,CAAAqB,MAAA,KAAiD;UAAArB,EAAA,CAAAC,cAAA,iBACnD;UAAAD,EAAA,CAAAqB,MAAA,UAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UACjCV,EAAA,CAAAC,cAAA,gBAAiB;UAGND,EAAA,CAAAE,UAAA,2BAAAud,sEAAAzc,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAAwI,YAAA,GAAA7L,MAAA;UAAA,EAAiC;UAFxChB,EAAA,CAAAU,YAAA,EASE;UAKVV,EAAA,CAAAC,cAAA,gBAA+B;UAEqBD,EAAA,CAAAqB,MAAA,KAC5C;UAAArB,EAAA,CAAAC,cAAA,iBAA2B;UAAAD,EAAA,CAAAqB,MAAA,UAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UACvCV,EAAA,CAAAC,cAAA,gBAAiB;UAGND,EAAA,CAAAE,UAAA,2BAAAwd,sEAAA1c,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAAgJ,QAAA,GAAArM,MAAA;UAAA,EAA6B,qBAAA2c,gEAAA3c,MAAA;YAAA,OAElBgY,GAAA,CAAA/C,gBAAA,CAAAjV,MAAA,CAAwB;UAAA,EAFN;UAFpChB,EAAA,CAAAU,YAAA,EAOE;UAMVV,EAAA,CAAAC,cAAA,gBAA+B;UAEqBD,EAAA,CAAAqB,MAAA,KAAmD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAC3GV,EAAA,CAAAC,cAAA,gBAAiB;UAGND,EAAA,CAAAE,UAAA,2BAAA0d,sEAAA5c,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAAwC,OAAA,GAAA7F,MAAA;UAAA,EAA4B,qBAAA6c,gEAAA7c,MAAA;YAAA,OAEjBgY,GAAA,CAAA/C,gBAAA,CAAAjV,MAAA,CAAwB;UAAA,EAFP;UAFnChB,EAAA,CAAAU,YAAA,EAOE;UAIVV,EAAA,CAAAC,cAAA,gBAA+B;UAEqBD,EAAA,CAAAqB,MAAA,KAAgD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UACxGV,EAAA,CAAAC,cAAA,gBAAiB;UAGND,EAAA,CAAAE,UAAA,2BAAA4d,sEAAA9c,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAA0I,IAAA,GAAA/L,MAAA;UAAA,EAAyB,qBAAA+c,gEAAA/c,MAAA;YAAA,OAEdgY,GAAA,CAAA/C,gBAAA,CAAAjV,MAAA,CAAwB;UAAA,EAFV;UAFhChB,EAAA,CAAAU,YAAA,EAOE;UAIVV,EAAA,CAAAC,cAAA,gBAAwE;UAEzCD,EAAA,CAAAqB,MAAA,KAA8D;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UACjGV,EAAA,CAAAC,cAAA,gBAAuD;UAG/CD,EAAA,CAAAE,UAAA,yBAAA8d,0EAAAhd,MAAA;YAAA,OAAAgY,GAAA,CAAA3U,MAAA,CAAA8I,UAAA,GAAAnM,MAAA;UAAA,EAA6B,sBAAAid,uEAAA;YAAA,OAQjBjF,GAAA,CAAAnT,uBAAA,EAAyB;UAAA,EARR;UAYhC7F,EAAA,CAAAU,YAAA,EAAc;UAIvBV,EAAA,CAAAC,cAAA,gBAAgD;UAC5CD,EAAA,CAAA0B,SAAA,kBAA0E;UAC1E1B,EAAA,CAAAC,cAAA,gBAAiB;UACbD,EAAA,CAAAqF,UAAA,MAAA6Y,8CAAA,oBAC2K;UAC/Kle,EAAA,CAAAU,YAAA,EAAM;UAMtBV,EAAA,CAAAC,cAAA,gBAAkB;UAGND,EAAA,CAAAqF,UAAA,MAAA8Y,4CAAA,mBA6CM;UACVne,EAAA,CAAAU,YAAA,EAAM;UAEVV,EAAA,CAAAC,cAAA,gBAAwB;UAK2CD,EAAA,CAAAqB,MAAA,KAAqD;UAAArB,EAAA,CAAAU,YAAA,EAAI;UAGhHV,EAAA,CAAAqF,UAAA,MAAA+Y,4CAAA,kBAsBM;UACNpe,EAAA,CAAAC,cAAA,gBAAwF;UAG5BD,EAAA,CAAAE,UAAA,mBAAAme,+DAAA;YAAA,OAASrF,GAAA,CAAApC,OAAA,EAAS;UAAA,EAAC;UACoD5W,EAAA,CAAAU,YAAA,EAAS;UAChIV,EAAA,CAAAC,cAAA,kBAUE;UAPKD,EAAA,CAAAE,UAAA,2BAAAoe,sEAAAtd,MAAA;YAAA,OAAAgY,GAAA,CAAArM,OAAA,GAAA3L,MAAA;UAAA,EAAqB,mBAAAud,8DAAA;YAAA,OAIZvF,GAAA,CAAA/B,cAAA,EAAgB;UAAA,EAJJ,qBAAAuH,gEAAAxd,MAAA;YAAA,OAKVgY,GAAA,CAAA/C,gBAAA,CAAAjV,MAAA,CAAwB;UAAA,EALd;UAH5BhB,EAAA,CAAAU,YAAA,EAUE;UAGVV,EAAA,CAAAC,cAAA,gBAAgD;UAC5CD,EAAA,CAAA0B,SAAA,kBAAoE;UACpE1B,EAAA,CAAAC,cAAA,gBAAoB;UAChBD,EAAA,CAAAqF,UAAA,MAAAoZ,8CAAA,oBAC0E;UAC9Eze,EAAA,CAAAU,YAAA,EAAM;UAwC9BV,EAAA,CAAAqF,UAAA,MAAAqZ,4CAAA,kBAOM;UAEV1e,EAAA,CAAAU,YAAA,EAAO;UAIfV,EAAA,CAAAC,cAAA,qBAEuH;UAAvDD,EAAA,CAAAE,UAAA,2BAAAye,yEAAA3d,MAAA;YAAA,OAAAgY,GAAA,CAAAvK,WAAA,GAAAzN,MAAA;UAAA,EAAyB;UACrFhB,EAAA,CAAAC,cAAA,gBAAiH;UAAtED,EAAA,CAAAE,UAAA,sBAAA0e,gEAAA;YAAA,OAAY5F,GAAA,CAAAjF,qBAAA,EAAuB;UAAA,EAAC;UAC3E/T,EAAA,CAAAC,cAAA,gBAAoD;UAK5BD,EAAA,CAAAE,UAAA,2BAAA2e,2EAAA7d,MAAA;YAAA,OAAAgY,GAAA,CAAA7M,kBAAA,CAAAC,QAAA,GAAApL,MAAA;UAAA,EAAyC,sBAAA8d,sEAAA;YAAA,OAM7B9F,GAAA,CAAAtC,gBAAA,CAAAsC,GAAA,CAAA7M,kBAAA,CAAAC,QAAA,CAA6C;UAAA,EANhB,qBAAA2S,qEAAA;YAAA,OAO9B/F,GAAA,CAAAtC,gBAAA,CAAAsC,GAAA,CAAA7M,kBAAA,CAAAC,QAAA,CAA6C;UAAA,EAPf;UAQpDpM,EAAA,CAAAU,YAAA,EAAa;UACdV,EAAA,CAAAC,cAAA,mBAA0B;UAAAD,EAAA,CAAAqB,MAAA,KAAoD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAG9FV,EAAA,CAAAC,cAAA,iBAAwB;UAIJD,EAAA,CAAAE,UAAA,2BAAA8e,2EAAAhe,MAAA;YAAA,OAAAgY,GAAA,CAAA7M,kBAAA,CAAAE,MAAA,GAAArL,MAAA;UAAA,EAAuC,sBAAAie,sEAAA;YAAA,OAO3BjG,GAAA,CAAArC,cAAA,CAAAqC,GAAA,CAAA7M,kBAAA,CAAAE,MAAA,CAAyC;UAAA,EAPd,qBAAA6S,qEAAA;YAAA,OAQ5BlG,GAAA,CAAArC,cAAA,CAAAqC,GAAA,CAAA7M,kBAAA,CAAAE,MAAA,CAAyC;UAAA,EARb;UAFnDrM,EAAA,CAAAU,YAAA,EAWE;UACFV,EAAA,CAAAC,cAAA,mBAAwB;UAAAD,EAAA,CAAAqB,MAAA,KAAkD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAG1FV,EAAA,CAAAC,cAAA,iBAAmB;UACfD,EAAA,CAAA0B,SAAA,qBAGY;UAChB1B,EAAA,CAAAU,YAAA,EAAM;UAKdV,EAAA,CAAA0B,SAAA,wBAQc;UAElB1B,EAAA,CAAAU,YAAA,EAAW;;;UA34BiCV,EAAA,CAAAsB,SAAA,GAAmD;UAAnDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,yBAAmD;UAChDd,EAAA,CAAAsB,SAAA,GAAe;UAAftB,EAAA,CAAAW,UAAA,UAAAqY,GAAA,CAAAmG,KAAA,CAAe,SAAAnG,GAAA,CAAAoG,IAAA;UAI3Cpf,EAAA,CAAAsB,SAAA,GAA8F;UAA9FtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAA/M,QAAA,CAAAe,IAAA,IAAAgM,GAAA,CAAAvM,QAAA,CAAAkF,QAAA,IAAAqH,GAAA,CAAAvO,WAAA,CAAAzK,EAAA,CAAA0K,eAAA,MAAAC,GAAA,EAAAqO,GAAA,CAAAnZ,SAAA,CAAA+K,WAAA,CAAAC,MAAA,CAAAwU,MAAA,GAA8F;UAO3Grf,EAAA,CAAAsB,SAAA,GAA8B;UAA9BtB,EAAA,CAAAW,UAAA,cAAAqY,GAAA,CAAAhH,gBAAA,CAA8B;UACvBhS,EAAA,CAAAsB,SAAA,GAAmB;UAAnBtB,EAAA,CAAAW,UAAA,oBAAmB,WAAAqY,GAAA,CAAAnY,WAAA,CAAAC,SAAA;UAGdd,EAAA,CAAAsB,SAAA,GAA+C;UAA/CtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAA/M,QAAA,CAAAe,IAAA,IAAAgM,GAAA,CAAAvM,QAAA,CAAA+C,KAAA,CAA+C;UAkBjCxP,EAAA,CAAAsB,SAAA,GAAkB;UAAlBtB,EAAA,CAAAW,UAAA,mBAAkB,uDAAAqY,GAAA,CAAA7X,UAAA,CAAAgF,MAAA,gCAAA6S,GAAA,CAAAnK,gBAAA;UAWiB7O,EAAA,CAAAsB,SAAA,GAAkD;UAAlDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,wBAAkD;UAQ1Fd,EAAA,CAAAsB,SAAA,GAAoC;UAApCtB,EAAA,CAAAW,UAAA,YAAAqY,GAAA,CAAA7X,UAAA,CAAAyL,WAAA,CAAoC;UAGpB5M,EAAA,CAAAsB,SAAA,GAAoD;UAApDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,0BAAoD;UAQpEd,EAAA,CAAAsB,SAAA,GAAqC;UAArCtB,EAAA,CAAAW,UAAA,YAAAqY,GAAA,CAAA7X,UAAA,CAAA2L,YAAA,CAAqC;UAMrB9M,EAAA,CAAAsB,SAAA,GAAiD;UAAjDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,uBAAiD;UAO5Dd,EAAA,CAAAsB,SAAA,GAAiC;UAAjCtB,EAAA,CAAAW,UAAA,YAAAqY,GAAA,CAAA7X,UAAA,CAAA6N,QAAA,CAAiC,iDAAAgK,GAAA,CAAAxN,WAAA;UASIxL,EAAA,CAAAsB,SAAA,GAAoD;UAApDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,0BAAoD;UAOzFd,EAAA,CAAAsB,SAAA,GAA+B;UAA/BtB,EAAA,CAAAW,UAAA,YAAAqY,GAAA,CAAA7X,UAAA,CAAA4N,MAAA,CAA+B,iDAAAiK,GAAA,CAAAtN,SAAA,aAAAsN,GAAA,CAAArN,SAAA;UAUI3L,EAAA,CAAAsB,SAAA,GAAkD;UAAlDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,wBAAkD;UAaxGd,EAAA,CAAAsB,SAAA,GAAkB;UAAlBtB,EAAA,CAAAW,UAAA,UAAAqY,GAAA,CAAAlN,WAAA,CAAkB;UAalB9L,EAAA,CAAAsB,SAAA,GAAiB;UAAjBtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAlN,WAAA,CAAiB;UAgBD9L,EAAA,CAAAsB,SAAA,GAA4B;UAA5BtB,EAAA,CAAAsf,UAAA,CAAAtf,EAAA,CAAAuC,eAAA,MAAAgd,GAAA,EAA4B;UAD3Cvf,EAAA,CAAAW,UAAA,WAAAqY,GAAA,CAAAnY,WAAA,CAAAC,SAAA,+BAA8D,YAAAkY,GAAA,CAAAxM,mBAAA;UAEjDxM,EAAA,CAAAsB,SAAA,GAA2B;UAA3BtB,EAAA,CAAAW,UAAA,cAAAqY,GAAA,CAAAtV,aAAA,CAA2B;UAKP1D,EAAA,CAAAsB,SAAA,GAAwD;UAAxDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,8BAAwD;UAKxEd,EAAA,CAAAsB,SAAA,GAAgC;UAAhCtB,EAAA,CAAAW,UAAA,YAAAqY,GAAA,CAAA3U,MAAA,CAAAuI,WAAA,CAAgC,mDAAAoM,GAAA,CAAAnY,WAAA,CAAAC,SAAA;UAe/Bd,EAAA,CAAAsB,SAAA,GAAqG;UAArGtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA4E,WAAA,CAAA3E,KAAA,KAAA+Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA4E,WAAA,CAAA1E,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA4E,WAAA,CAAA1E,MAAA,CAAAC,QAAA,EAAqG;UAErGnI,EAAA,CAAAsB,SAAA,GAA0D;UAA1DtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA4E,WAAA,CAAA1E,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA4E,WAAA,CAAA1E,MAAA,CAAAE,SAAA,CAA0D;UAE1DpI,EAAA,CAAAsB,SAAA,GAAwD;UAAxDtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA4E,WAAA,CAAA1E,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA4E,WAAA,CAAA1E,MAAA,CAAAsX,OAAA,CAAwD;UAOzCxf,EAAA,CAAAsB,SAAA,GAAiD;UAAjDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,uBAAiD;UAKjEd,EAAA,CAAAsB,SAAA,GAAiC;UAAjCtB,EAAA,CAAAW,UAAA,YAAAqY,GAAA,CAAA3U,MAAA,CAAAwI,YAAA,CAAiC,mDAAAmM,GAAA,CAAAnY,WAAA,CAAAC,SAAA;UAehCd,EAAA,CAAAsB,SAAA,GAAuG;UAAvGtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA6E,YAAA,CAAA5E,KAAA,KAAA+Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA6E,YAAA,CAAA3E,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA6E,YAAA,CAAA3E,MAAA,CAAAC,QAAA,EAAuG;UAEvGnI,EAAA,CAAAsB,SAAA,GAA2D;UAA3DtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA6E,YAAA,CAAA3E,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA6E,YAAA,CAAA3E,MAAA,CAAAE,SAAA,CAA2D;UAE3DpI,EAAA,CAAAsB,SAAA,GAAyD;UAAzDtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA6E,YAAA,CAAA3E,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA6E,YAAA,CAAA3E,MAAA,CAAAsX,OAAA,CAAyD;UAO1Cxf,EAAA,CAAAsB,SAAA,GAAiD;UAAjDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,uBAAiD;UAKjEd,EAAA,CAAAsB,SAAA,GAAiC;UAAjCtB,EAAA,CAAAW,UAAA,YAAAqY,GAAA,CAAA3U,MAAA,CAAAyI,YAAA,CAAiC,mDAAAkM,GAAA,CAAAnY,WAAA,CAAAC,SAAA;UAehCd,EAAA,CAAAsB,SAAA,GAAuG;UAAvGtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA8E,YAAA,CAAA7E,KAAA,KAAA+Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA8E,YAAA,CAAA5E,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA8E,YAAA,CAAA5E,MAAA,CAAAC,QAAA,EAAuG;UAEvGnI,EAAA,CAAAsB,SAAA,GAAyD;UAAzDtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA8E,YAAA,CAAA5E,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA8E,YAAA,CAAA5E,MAAA,CAAAsX,OAAA,CAAyD;UAMrBxf,EAAA,CAAAsB,SAAA,GAC5C;UAD4CtB,EAAA,CAAAiG,kBAAA,KAAA+S,GAAA,CAAAnY,WAAA,CAAAC,SAAA,+BAC5C;UAGed,EAAA,CAAAsB,SAAA,GAAoB;UAApBtB,EAAA,CAAAW,UAAA,qBAAoB,YAAAqY,GAAA,CAAA3U,MAAA,CAAAgJ,QAAA,mCAAA2L,GAAA,CAAAnY,WAAA,CAAAC,SAAA;UAe3Bd,EAAA,CAAAsB,SAAA,GAA+F;UAA/FtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAqF,QAAA,CAAApF,KAAA,KAAA+Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAqF,QAAA,CAAAnF,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAqF,QAAA,CAAAnF,MAAA,CAAAC,QAAA,EAA+F;UAE/FnI,EAAA,CAAAsB,SAAA,GAAsB;UAAtBtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAA/U,gBAAA,CAAsB;UAEtBjE,EAAA,CAAAsB,SAAA,GAAsB;UAAtBtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAnV,gBAAA,CAAsB;UAMc7D,EAAA,CAAAsB,SAAA,GAC5C;UAD4CtB,EAAA,CAAAiG,kBAAA,KAAA+S,GAAA,CAAAnY,WAAA,CAAAC,SAAA,sCAC5C;UAMId,EAAA,CAAAsB,SAAA,GAA2B;UAA3BtB,EAAA,CAAAW,UAAA,UAAAqY,GAAA,CAAA3U,MAAA,CAAAmJ,QAAA,CAA2B,gBAAAwL,GAAA,CAAAnY,WAAA,CAAAC,SAAA;UAclBd,EAAA,CAAAsB,SAAA,GAA2B;UAA3BtB,EAAA,CAAAW,UAAA,UAAAqY,GAAA,CAAA3U,MAAA,CAAAkJ,QAAA,CAA2B,gBAAAyL,GAAA,CAAAnY,WAAA,CAAAC,SAAA,iFAAAkY,GAAA,CAAApN,qBAAA,kBAAAoN,GAAA,CAAA/J,aAAA;UAgB3BjP,EAAA,CAAAsB,SAAA,GAA0B;UAA1BtB,EAAA,CAAAW,UAAA,UAAAqY,GAAA,CAAA3U,MAAA,CAAAiJ,OAAA,CAA0B,gBAAA0L,GAAA,CAAAnY,WAAA,CAAAC,SAAA,gFAAAkY,GAAA,CAAAnN,oBAAA,kBAAAmN,GAAA,CAAA/J,aAAA;UAoB5BjP,EAAA,CAAAsB,SAAA,GAAoB;UAApBtB,EAAA,CAAAW,UAAA,qBAAoB,YAAAqY,GAAA,CAAA3U,MAAA,CAAAoJ,aAAA,qDAAAuL,GAAA,CAAAnY,WAAA,CAAAC,SAAA;UAiBnBd,EAAA,CAAAsB,SAAA,GAA4D;UAA5DtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAyF,aAAA,CAAAvF,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAyF,aAAA,CAAAvF,MAAA,CAAAE,SAAA,CAA4D;UAE5DpI,EAAA,CAAAsB,SAAA,GAAyG;UAAzGtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAyF,aAAA,CAAAxF,KAAA,KAAA+Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAyF,aAAA,CAAAvF,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAyF,aAAA,CAAAvF,MAAA,CAAAC,QAAA,EAAyG;UAEzGnI,EAAA,CAAAsB,SAAA,GAA0D;UAA1DtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAyF,aAAA,CAAAvF,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAyF,aAAA,CAAAvF,MAAA,CAAAsX,OAAA,CAA0D;UAO1Bxf,EAAA,CAAAsB,SAAA,GAAmD;UAAnDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,yBAAmD;UAIjFd,EAAA,CAAAsB,SAAA,GAAoB;UAApBtB,EAAA,CAAAW,UAAA,qBAAoB,YAAAqY,GAAA,CAAA3U,MAAA,CAAAwC,OAAA,mCAAAmS,GAAA,CAAAnY,WAAA,CAAAC,SAAA;UAgB1Bd,EAAA,CAAAsB,SAAA,GAAsD;UAAtDtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAnB,OAAA,CAAAqB,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAnB,OAAA,CAAAqB,MAAA,CAAAE,SAAA,CAAsD;UAEtDpI,EAAA,CAAAsB,SAAA,GAAoD;UAApDtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAnB,OAAA,CAAAqB,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAnB,OAAA,CAAAqB,MAAA,CAAAsX,OAAA,CAAoD;UAQhBxf,EAAA,CAAAsB,SAAA,GAAgD;UAAhDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,sBAAgD;UAI9Ed,EAAA,CAAAsB,SAAA,GAAoB;UAApBtB,EAAA,CAAAW,UAAA,qBAAoB,YAAAqY,GAAA,CAAA3U,MAAA,CAAA0I,IAAA,mCAAAiM,GAAA,CAAAnY,WAAA,CAAAC,SAAA;UAgB1Bd,EAAA,CAAAsB,SAAA,GAAmD;UAAnDtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA+E,IAAA,CAAA7E,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA+E,IAAA,CAAA7E,MAAA,CAAAE,SAAA,CAAmD;UAEnDpI,EAAA,CAAAsB,SAAA,GAAiD;UAAjDtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA+E,IAAA,CAAA7E,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA+E,IAAA,CAAA7E,MAAA,CAAAsX,OAAA,CAAiD;UAKlBxf,EAAA,CAAAsB,SAAA,GAAuD;UAAvDtB,EAAA,CAAAW,UAAA,UAAAqY,GAAA,CAAAnY,WAAA,CAAAC,SAAA,yBAAuD;UAElDd,EAAA,CAAAsB,SAAA,GAAwD;UAAxDtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAvO,WAAA,CAAAzK,EAAA,CAAA0K,eAAA,MAAAC,GAAA,EAAAqO,GAAA,CAAAnZ,SAAA,CAAA+K,WAAA,CAAAC,MAAA,CAAAwU,MAAA,GAAwD;UAUrGrf,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAsf,UAAA,CAAAtf,EAAA,CAAAuC,eAAA,MAAAkd,GAAA,EAA6B;UAF5Czf,EAAA,CAAAW,UAAA,WAAAqY,GAAA,CAAApV,WAAA,aAAAoV,GAAA,CAAAnY,WAAA,CAAAC,SAAA,gCAAAkY,GAAA,CAAAnY,WAAA,CAAAC,SAAA,gCAA2I,YAAAkY,GAAA,CAAA1O,mBAAA;UAGxHtK,EAAA,CAAAsB,SAAA,GAAgC;UAAhCtB,EAAA,CAAAW,UAAA,cAAAqY,GAAA,CAAAlP,kBAAA,CAAgC;UAIoB9J,EAAA,CAAAsB,SAAA,GAAwE;UAAxEtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAA/M,QAAA,CAAAe,IAAA,IAAAgM,GAAA,CAAAvM,QAAA,CAAA+C,KAAA,IAAAwJ,GAAA,CAAApV,WAAA,WAAwE;UAqBhG5D,EAAA,CAAAsB,SAAA,GAAwD;UAAxDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,8BAAwD;UAKxEd,EAAA,CAAAsB,SAAA,GAAgC;UAAhCtB,EAAA,CAAAW,UAAA,YAAAqY,GAAA,CAAA3U,MAAA,CAAAuI,WAAA,CAAgC,mDAAAoM,GAAA,CAAAnY,WAAA,CAAAC,SAAA;UAchBd,EAAA,CAAAsB,SAAA,GAAiD;UAAjDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,uBAAiD;UAKjEd,EAAA,CAAAsB,SAAA,GAAiC;UAAjCtB,EAAA,CAAAW,UAAA,YAAAqY,GAAA,CAAA3U,MAAA,CAAAyI,YAAA,CAAiC,kCAAAkM,GAAA,CAAAnY,WAAA,CAAAC,SAAA;UAcjBd,EAAA,CAAAsB,SAAA,GAA2D;UAA3DtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,iCAA2D;UAMxEd,EAAA,CAAAsB,SAAA,GAA4B;UAA5BtB,EAAA,CAAAW,UAAA,YAAAqY,GAAA,CAAA3U,MAAA,CAAA+I,OAAA,CAA4B,kCAAA4L,GAAA,CAAAnY,WAAA,CAAAC,SAAA;UASfd,EAAA,CAAAsB,SAAA,GAAsC;UAAtCtB,EAAA,CAAAuE,UAAA,CAAAyU,GAAA,CAAA9K,YAAA,iBAAsC;UAEtClO,EAAA,CAAAsB,SAAA,GAAkD;UAAlDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,wBAAkD;UAG7Dd,EAAA,CAAAsB,SAAA,GAAkB;UAAlBtB,EAAA,CAAAW,UAAA,mBAAkB,sCAAAqY,GAAA,CAAA3U,MAAA,CAAA8B,MAAA,cAAA6S,GAAA,CAAA7K,gBAAA,aAAA6K,GAAA,CAAA3U,MAAA,CAAAM,SAAA,YAAAqU,GAAA,CAAA/K,eAAA,CAAA+K,GAAA,CAAA3U,MAAA,CAAAM,SAAA,IAAAqU,GAAA,CAAAnK,gBAAA,iBAAAmK,GAAA,CAAAnY,WAAA,CAAAC,SAAA,yCAAAkY,GAAA,CAAAnY,WAAA,CAAAC,SAAA;UAgBjCd,EAAA,CAAAsB,SAAA,GAAoD;UAApDtB,EAAA,CAAAuE,UAAA,CAAAyU,GAAA,CAAApV,WAAA,2BAAoD;UAE1B5D,EAAA,CAAAsB,SAAA,GAAkD;UAAlDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,wBAAkD;UAclEd,EAAA,CAAAsB,SAAA,GAAsD;UAAtDtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAA3U,MAAA,CAAAM,SAAA,IAAAqU,GAAA,CAAAnZ,SAAA,CAAAuQ,cAAA,CAAAC,GAAA,CAAsD;UACtDrQ,EAAA,CAAAsB,SAAA,GAA2D;UAA3DtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAA3U,MAAA,CAAAM,SAAA,IAAAqU,GAAA,CAAAnZ,SAAA,CAAAuQ,cAAA,CAAAE,QAAA,CAA2D;UAC3DtQ,EAAA,CAAAsB,SAAA,GAA8D;UAA9DtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAA3U,MAAA,CAAAM,SAAA,IAAAqU,GAAA,CAAAnZ,SAAA,CAAAuQ,cAAA,CAAAG,WAAA,CAA8D;UAC9DvQ,EAAA,CAAAsB,SAAA,GAAyD;UAAzDtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAA3U,MAAA,CAAAM,SAAA,IAAAqU,GAAA,CAAAnZ,SAAA,CAAAuQ,cAAA,CAAAI,MAAA,CAAyD;UACzDxQ,EAAA,CAAAsB,SAAA,GAAuD;UAAvDtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAA3U,MAAA,CAAAM,SAAA,IAAAqU,GAAA,CAAAnZ,SAAA,CAAAuQ,cAAA,CAAAK,IAAA,CAAuD;UAQtDzQ,EAAA,CAAAsB,SAAA,GAA2F;UAA3FtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA7B,MAAA,CAAA8B,KAAA,KAAA+Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA7B,MAAA,CAAA+B,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAA7B,MAAA,CAAA+B,MAAA,CAAAC,QAAA,EAA2F;UAIrGnI,EAAA,CAAAsB,SAAA,GAAqB;UAArBtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAA5K,UAAA,CAAqB;UA0BXpO,EAAA,CAAAsB,SAAA,GAAoD;UAApDtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAtC,KAAA,CAAAwC,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAtC,KAAA,CAAAwC,MAAA,CAAAE,SAAA,CAAoD;UAEpDpI,EAAA,CAAAsB,SAAA,GAAyF;UAAzFtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAtC,KAAA,CAAAuC,KAAA,KAAA+Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAtC,KAAA,CAAAwC,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAtC,KAAA,CAAAwC,MAAA,CAAAC,QAAA,EAAyF;UAS1EnI,EAAA,CAAAsB,SAAA,GAAiD;UAAjDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,uBAAiD;UAKjEd,EAAA,CAAAsB,SAAA,GAAiC;UAAjCtB,EAAA,CAAAW,UAAA,YAAAqY,GAAA,CAAA3U,MAAA,CAAAwI,YAAA,CAAiC,mDAAAmM,GAAA,CAAAnY,WAAA,CAAAC,SAAA;UAcId,EAAA,CAAAsB,SAAA,GAC5C;UAD4CtB,EAAA,CAAAiG,kBAAA,KAAA+S,GAAA,CAAAnY,WAAA,CAAAC,SAAA,+BAC5C;UAIOd,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAW,UAAA,YAAAqY,GAAA,CAAA3U,MAAA,CAAAgJ,QAAA,CAA6B,gBAAA2L,GAAA,CAAAnY,WAAA,CAAAC,SAAA;UAaQd,EAAA,CAAAsB,SAAA,GAAmD;UAAnDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,yBAAmD;UAIxFd,EAAA,CAAAsB,SAAA,GAA4B;UAA5BtB,EAAA,CAAAW,UAAA,YAAAqY,GAAA,CAAA3U,MAAA,CAAAwC,OAAA,CAA4B,gBAAAmS,GAAA,CAAAnY,WAAA,CAAAC,SAAA;UAWSd,EAAA,CAAAsB,SAAA,GAAgD;UAAhDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,sBAAgD;UAIrFd,EAAA,CAAAsB,SAAA,GAAyB;UAAzBtB,EAAA,CAAAW,UAAA,YAAAqY,GAAA,CAAA3U,MAAA,CAAA0I,IAAA,CAAyB,gBAAAiM,GAAA,CAAAnY,WAAA,CAAAC,SAAA;UASTd,EAAA,CAAAsB,SAAA,GAAwC;UAAxCtB,EAAA,CAAAuE,UAAA,CAAAyU,GAAA,CAAA3K,cAAA,iBAAwC;UAExCrO,EAAA,CAAAsB,SAAA,GAA8D;UAA9DtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,oCAA8D;UAIjFd,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAW,UAAA,UAAAqY,GAAA,CAAA3U,MAAA,CAAA8I,UAAA,CAA6B,gBAAA6L,GAAA,CAAAnY,WAAA,CAAAC,SAAA,iDAAAkY,GAAA,CAAA1K,kBAAA,sCAAA0K,GAAA,CAAApV,WAAA,4BAAA5D,EAAA,CAAAuC,eAAA,MAAAmd,GAAA;UAoBzB1f,EAAA,CAAAsB,SAAA,GAAmG;UAAnGtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAmF,UAAA,CAAAlF,KAAA,KAAA+Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAmF,UAAA,CAAAjF,MAAA,kBAAA8Q,GAAA,CAAAtV,aAAA,CAAAsE,QAAA,CAAAmF,UAAA,CAAAjF,MAAA,CAAAC,QAAA,EAAmG;UAU7GnI,EAAA,CAAAsB,SAAA,GAAoB;UAApBtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAxK,cAAA,CAAoB;UAmDSxO,EAAA,CAAAsB,SAAA,GAAwC;UAAxCtB,EAAA,CAAAuE,UAAA,CAAAyU,GAAA,CAAAzK,cAAA,iBAAwC;UAEhBvO,EAAA,CAAAsB,SAAA,GAAqD;UAArDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,2BAAqD;UAGxDd,EAAA,CAAAsB,SAAA,GAAoB;UAApBtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAAzK,cAAA,CAAoB;UAuB1BvO,EAAA,CAAAsB,SAAA,GAAyC;UAAzCtB,EAAA,CAAAuE,UAAA,CAAAyU,GAAA,CAAA7P,eAAA,iBAAyC;UAInEnJ,EAAA,CAAAsB,SAAA,GAA8G;UAA9GtB,EAAA,CAAAW,UAAA,aAAAqY,GAAA,CAAAtP,SAAA,CAAAO,MAAA,IAAA+O,GAAA,CAAA3U,MAAA,CAAAgJ,QAAA,IAAA2L,GAAA,CAAArK,mBAAA,IAAAqK,GAAA,CAAArM,OAAA,UAAAqM,GAAA,CAAApK,kBAAA,CAA8G;UAE/G5O,EAAA,CAAAsB,SAAA,GAAqC;UAArCtB,EAAA,CAAAW,UAAA,mBAAAX,EAAA,CAAAuC,eAAA,MAAAod,IAAA,EAAqC,gBAAA3G,GAAA,CAAAnY,WAAA,CAAAC,SAAA,oCAAAkY,GAAA,CAAArM,OAAA,cAAAqM,GAAA,CAAAtP,SAAA,CAAAO,MAAA,IAAA+O,GAAA,CAAA3U,MAAA,CAAAgJ,QAAA;UAgBpCrN,EAAA,CAAAsB,SAAA,GAA4B;UAA5BtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAApP,oBAAA,GAA4B;UAyCW5J,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAW,UAAA,SAAAqY,GAAA,CAAApV,WAAA,aAA6B;UAe1B5D,EAAA,CAAAsB,SAAA,GAA4B;UAA5BtB,EAAA,CAAAsf,UAAA,CAAAtf,EAAA,CAAAuC,eAAA,MAAAqd,IAAA,EAA4B;UAFxF5f,EAAA,CAAAW,UAAA,WAAAqY,GAAA,CAAAnY,WAAA,CAAAC,SAAA,8BAA6D,uEAAAd,EAAA,CAAAuC,eAAA,MAAAsd,IAAA,cAAA7G,GAAA,CAAAvK,WAAA;UAGjFzO,EAAA,CAAAsB,SAAA,GAAoC;UAApCtB,EAAA,CAAAW,UAAA,cAAAqY,GAAA,CAAA1M,sBAAA,CAAoC;UAMdtM,EAAA,CAAAsB,SAAA,GAAyC;UAAzCtB,EAAA,CAAAW,UAAA,YAAAqY,GAAA,CAAA7M,kBAAA,CAAAC,QAAA,CAAyC,iDAAA4M,GAAA,CAAAxN,WAAA;UAS3BxL,EAAA,CAAAsB,SAAA,GAAoD;UAApDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,0BAAoD;UAOlEd,EAAA,CAAAsB,SAAA,GAAuC;UAAvCtB,EAAA,CAAAW,UAAA,YAAAqY,GAAA,CAAA7M,kBAAA,CAAAE,MAAA,CAAuC,iDAAA2M,GAAA,CAAAtN,SAAA,aAAAsN,GAAA,CAAArN,SAAA;UAU3B3L,EAAA,CAAAsB,SAAA,GAAkD;UAAlDtB,EAAA,CAAAyB,iBAAA,CAAAuX,GAAA,CAAAnY,WAAA,CAAAC,SAAA,wBAAkD;UActFd,EAAA,CAAAsB,SAAA,GAA0B;UAA1BtB,EAAA,CAAAW,UAAA,YAAAqY,GAAA,CAAAtI,cAAA,CAA0B,YAAAsI,GAAA,CAAAjI,cAAA,aAAAiI,GAAA,CAAApI,kBAAA,cAAAoI,GAAA,CAAAhF,aAAA,CAAA/R,IAAA,CAAA+W,GAAA,wDAAAA,GAAA,CAAA7M,kBAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}