<div *ngIf="reportDetail">
    <div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
        <div class="">
            <div class="text-xl font-bold mb-1">{{tranService.translate(this.reportDetail.name)}}</div>
            <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
        </div>
        <div class="col-3 flex flex-row justify-content-end align-items-center report-dynamic-button-div">
            <p-button *ngIf="reportDetail.enablePreview" [disabled]="!checkValidForm()" styleClass="p-button-secondary mr-2" [label]="tranService.translate('global.button.preview')" icon="pi pi-search" (onClick)="onSubmitSearch()"></p-button>
            <p-button [disabled]="!checkValidForm()" styleClass="p-button-info" [label]="tranService.translate('global.button.export')" icon="pi pi-download" (onClick)="export()"></p-button>
        </div>
    </div>

    <form *ngIf="formSearch" [formGroup]="formSearch" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set">
        <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
            <div class="grid">
                <div class="col-3" *ngFor="let param of listParameters">
                    <span class="p-float-label relative"
                          [class]="formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required ? 'report-param-required-error' : ''"
                          *ngIf="param.prType == paramTypes.NUMBER"
                    >
                        <input class="w-full" pInputText (keydown)="preventCharacter($event)" [(ngModel)]="searchInfo[param.prKey]" [formControlName]="param.prKey" [required]="param.required" (ngModelChange)="checkIsNumberOrNull(param.prKey)"/>
                        <!-- <p-inputNumber [useGrouping]="false" mode="decimal" class="w-full" [(ngModel)]="searchInfo[param.prKey]" [required]="param.required" [formControlName]="param.prKey"></p-inputNumber> -->
                        <label [htmlFor]="param.prKey">{{param.prDisplayName}}<span *ngIf="param.required" class="text-red-500">*</span></label>
                        <small class="text-red-500" *ngIf="formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required">
                            {{tranService.translate("global.message.required")}}
                        </small>
                    </span>
                    <span
                        class="p-float-label relative"
                        *ngIf="param.prType == paramTypes.STRING && param.isAutoComplete == false"
                        [class]="formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required ? 'report-param-required-error' : ''"
                    >
                        <input class="w-full" pInputText  [(ngModel)]="searchInfo[param.prKey]" [formControlName]="param.prKey" [required]="param.required"/>
                        <label [htmlFor]="param.prKey">{{param.prDisplayName}}<span *ngIf="param.required" class="text-red-500">*</span></label>
                        <small class="text-red-500" *ngIf="formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required">
                            {{tranService.translate("global.message.required")}}
                        </small>
                    </span>
                    <span
                        class="p-float-label relative"
                        *ngIf="param.prType == paramTypes.DATE"
                        [class]="formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required ? 'report-param-required-error' : ''"
                    >
                        <p-calendar styleClass="w-full"
                                    [(ngModel)]="searchInfo[param.prKey]"
                                    [showClear]="true"
                                    [showIcon]="true"
                                    [dateFormat]="param.dateType == dateTypes.MONTH ? 'mm/yy' : 'dd/mm/yy'"
                                    [hourFormat]="param.dateType == dateTypes.DATETIME ? 'hh:mm:ss' : ''"
                                    [view]="param.dateType == dateTypes.MONTH ? 'month' : 'date'"
                                    [showTime]="param.dateType == dateTypes.DATETIME" [showSeconds]="param.dateType == dateTypes.DATETIME"
                                    [showClear]="true" [showIcon]="true" appendTo="body"
                                    [formControlName]="param.prKey"
                                    [placeholder]="tranService.translate('global.text.inputDate')"
                                    [required]="param.required"
                        ></p-calendar>
                        <label [htmlFor]="param.prKey">{{param.prDisplayName}}<span *ngIf="param.required" class="text-red-500">*</span></label>
                        <small class="text-red-500" *ngIf="formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required">
                            {{tranService.translate("global.message.required")}}
                        </small>
                    </span>
                    <span
                        class="p-float-label relative"
                        *ngIf="param.prType == paramTypes.TIMESTAMP"
                        [class]="formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required ? 'report-param-required-error' : ''"
                    >
                        <p-calendar styleClass="w-full"
                                    [(ngModel)]="searchInfo[param.prKey]"
                                    [showClear]="true"
                                    [showIcon]="true"
                                    dateFormat="dd/mm/yy"
                                    hourFormat="hh:mm:ss"
                                    [showTime]="true" [showSeconds]="true"
                                    [showClear]="true" [showIcon]="true" appendTo="body"
                                    [formControlName]="param.prKey"
                                    [placeholder]="tranService.translate('global.text.inputDate')"
                                    [required]="param.required"
                        ></p-calendar>
                        <label [htmlFor]="param.prKey">{{param.prDisplayName}}<span *ngIf="param.required" class="text-red-500">*</span></label>
                        <small class="text-red-500" *ngIf="formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required">
                            {{tranService.translate("global.message.required")}}
                        </small>
                    </span>
                    <div class="relative" *ngIf="param.prType == paramTypes.LIST_NUMBER || param.prType == paramTypes.LIST_STRING || (param.prType == paramTypes.STRING && param.isAutoComplete == true)" >
                        <vnpt-select
                            [control]="param.control"
                            class="w-full"
                            [(value)]="searchInfo[param.prKey]"
                            [placeholder]="param.prDisplayName"
                            [isAutoComplete]="param.prType == paramTypes.STRING"
                            [isMultiChoice]="param.isMultiChoice"
                            [options]="param.valueList"
                            [objectKey]="param.queryInfo.objectKey"
                            [paramKey]="(param.isAutoComplete ? param.queryInfo.input : 'display')"
                            [keyReturn]="(param.isAutoComplete ? param.queryInfo.output : 'value')"
                            [displayPattern]="(param.isAutoComplete ? param.queryInfo.displayPattern : '${display}')"
                            [lazyLoad]="param.isAutoComplete"
                            [paramDefault]="((param.isAutoComplete && param.queryParam) ? getParamDefault(param) : {})"
                            typeValue="primitive"
                            [floatLabel]="true"
                            [required]="param.required"
                            [showTextRequired]="param.required"
                        ></vnpt-select>
                        <small class="text-red-500" *ngIf="param.control.dirty && param.control.error.required">
                            {{tranService.translate("global.message.required")}}
                        </small>
                    </div>
                </div>
                <div class="col-3 pb-0">
                    <!-- <p-button icon="pi pi-search"
                                styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                                type="submit"
                    ></p-button> -->
                </div>
            </div>
        </p-panel>
    </form>

    <div class="w-full custom-tabMenu" style="padding: 2px;margin-top: 12px;">
        <p-tabMenu [model]="menuTable" [activeItem]="defaultTableActive"></p-tabMenu>
    </div>
    <div class="w-full" style="padding: 2px;" *ngFor="let table of tables">
        <table-vnpt *ngIf="activeTable == table.id"
            [fieldId]="'id'"
            [(selectItems)]="mapTable[table.id].selectItems"
            [columns]="mapTable[table.id].columns"
            [dataSet]="mapTable[table.id].dataSet"
            [options]="mapTable[table.id].optionTable"
            [loadData]="mapTable[table.id].loadData.bind(this)"
            [pageNumber]="mapTable[table.id].pageNumber"
            [pageSize]="mapTable[table.id].pageSize"
            [sort]="mapTable[table.id].sort"
            [params]="mapTable[table.id].searchInfo"
            [labelTable]="table.tableName"
        ></table-vnpt>
    </div>
</div>
