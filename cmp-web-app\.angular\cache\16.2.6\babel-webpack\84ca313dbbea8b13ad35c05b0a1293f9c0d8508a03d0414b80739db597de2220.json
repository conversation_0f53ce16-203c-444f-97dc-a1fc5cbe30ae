{"ast": null, "code": "import { TicketService } from \"src/app/service/ticket/TicketService\";\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\nimport { ComponentBase } from \"src/app/component.base\";\nimport { AccountService } from \"../../../../service/account/AccountService\";\nimport { Validators } from \"@angular/forms\";\nimport { LogHandleTicketService } from \"../../../../service/ticket/LogHandleTicketService\";\nimport { ComboLazyControl } from \"../../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/breadcrumb\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/inputtext\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"../../../common-module/table/table.component\";\nimport * as i8 from \"../../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/dialog\";\nimport * as i11 from \"primeng/inputtextarea\";\nimport * as i12 from \"primeng/panel\";\nimport * as i13 from \"primeng/table\";\nimport * as i14 from \"src/app/service/ticket/TicketService\";\nimport * as i15 from \"../../../../service/account/AccountService\";\nimport * as i16 from \"../../../../service/ticket/LogHandleTicketService\";\nfunction ListDiagnoseTicketComponent_p_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 52);\n    i0.ɵɵlistener(\"click\", function ListDiagnoseTicketComponent_p_button_6_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.showModalCreate());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"global.button.create\"));\n  }\n}\nfunction ListDiagnoseTicketComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"span\", 11)(2, \"p-dropdown\", 54);\n    i0.ɵɵlistener(\"ngModelChange\", function ListDiagnoseTicketComponent_div_10_Template_p_dropdown_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.searchInfo.provinceCode = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 55);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"showClear\", true)(\"filter\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx_r1.searchInfo.provinceCode)(\"required\", false)(\"options\", ctx_r1.listProvince);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.tranService.translate(\"account.label.province\"));\n  }\n}\nfunction ListDiagnoseTicketComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\", 56);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"account.label.province\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.getProvinceName(ctx_r2.ticket.provinceCode));\n  }\n}\nfunction ListDiagnoseTicketComponent_input_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 57);\n    i0.ɵɵlistener(\"ngModelChange\", function ListDiagnoseTicketComponent_input_43_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.ticket.contactName = $event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.ticket.contactName)(\"required\", true)(\"maxLength\", ctx_r3.maxlengthContactName)(\"placeholder\", ctx_r3.tranService.translate(\"account.text.inputFullname\"))(\"readonly\", true);\n  }\n}\nfunction ListDiagnoseTicketComponent_span_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.ticket.contactName);\n  }\n}\nfunction ListDiagnoseTicketComponent_small_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.tranService.translate(\"global.message.required\"));\n  }\n}\nconst _c0 = function () {\n  return {\n    len: 255\n  };\n};\nfunction ListDiagnoseTicketComponent_small_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r6.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListDiagnoseTicketComponent_small_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.tranService.translate(\"global.message.formatContainVN\"));\n  }\n}\nfunction ListDiagnoseTicketComponent_input_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 59);\n    i0.ɵɵlistener(\"ngModelChange\", function ListDiagnoseTicketComponent_input_55_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.ticket.contactEmail = $event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngModel\", ctx_r8.ticket.contactEmail)(\"required\", true)(\"maxLength\", 50)(\"readonly\", true)(\"placeholder\", ctx_r8.tranService.translate(\"account.text.inputEmail\"));\n  }\n}\nfunction ListDiagnoseTicketComponent_span_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r9.ticket.contactEmail);\n  }\n}\nfunction ListDiagnoseTicketComponent_small_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r10.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListDiagnoseTicketComponent_small_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r11.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListDiagnoseTicketComponent_small_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r12.tranService.translate(\"global.message.invalidEmail\"));\n  }\n}\nfunction ListDiagnoseTicketComponent_input_67_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 60);\n    i0.ɵɵlistener(\"ngModelChange\", function ListDiagnoseTicketComponent_input_67_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.ticket.contactPhone = $event);\n    })(\"keydown\", function ListDiagnoseTicketComponent_input_67_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.preventCharacter($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngModel\", ctx_r13.ticket.contactPhone)(\"required\", true)(\"maxLength\", 11)(\"readonly\", true)(\"placeholder\", ctx_r13.tranService.translate(\"account.text.inputPhone\"));\n  }\n}\nfunction ListDiagnoseTicketComponent_span_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r14.ticket.contactPhone);\n  }\n}\nfunction ListDiagnoseTicketComponent_small_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r15.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListDiagnoseTicketComponent_small_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r16.tranService.translate(\"ticket.message.invalidPhone\"));\n  }\n}\nconst _c1 = function () {\n  return {};\n};\nfunction ListDiagnoseTicketComponent_div_74_vnpt_select_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"vnpt-select\", 63);\n    i0.ɵɵlistener(\"valueChange\", function ListDiagnoseTicketComponent_div_74_vnpt_select_6_Template_vnpt_select_valueChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r44 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r44.ticket.sim = $event);\n    })(\"onchange\", function ListDiagnoseTicketComponent_div_74_vnpt_select_6_Template_vnpt_select_onchange_0_listener($event) {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r46 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r46.changeDiagnoseNumber($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"control\", ctx_r42.controlComboSelect)(\"value\", ctx_r42.ticket.sim)(\"placeholder\", ctx_r42.tranService.translate(\"ticket.diagnose.label.number\"))(\"isMultiChoice\", false)(\"required\", true)(\"paramDefault\", i0.ɵɵpureFunction0(6, _c1));\n  }\n}\nfunction ListDiagnoseTicketComponent_div_74_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r43 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r43.ticket.sim);\n  }\n}\nfunction ListDiagnoseTicketComponent_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\", 61);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 58);\n    i0.ɵɵtext(4, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 35);\n    i0.ɵɵtemplate(6, ListDiagnoseTicketComponent_div_74_vnpt_select_6_Template, 1, 7, \"vnpt-select\", 62);\n    i0.ɵɵtemplate(7, ListDiagnoseTicketComponent_div_74_span_7_Template, 2, 1, \"span\", 45);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.tranService.translate(\"ticket.diagnose.label.number\"), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.typeRequest == \"create\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.typeRequest == \"update\" || ctx_r17.typeRequest == \"detail\");\n  }\n}\nfunction ListDiagnoseTicketComponent_div_75_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r47 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r47.tranService.translate(\"global.message.required\"), \" \");\n  }\n}\nfunction ListDiagnoseTicketComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"label\", 65);\n    i0.ɵɵelementStart(2, \"div\", 35);\n    i0.ɵɵtemplate(3, ListDiagnoseTicketComponent_div_75_small_3_Template, 2, 1, \"small\", 66);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.controlComboSelect.dirty && ctx_r18.controlComboSelect.error.required);\n  }\n}\nfunction ListDiagnoseTicketComponent_textarea_80_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"textarea\", 68);\n    i0.ɵɵlistener(\"ngModelChange\", function ListDiagnoseTicketComponent_textarea_80_Template_textarea_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r48 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r48.ticket.content = $event);\n    })(\"keydown\", function ListDiagnoseTicketComponent_textarea_80_Template_textarea_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r50.onKeyDownContent($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"autoResize\", false)(\"ngModel\", ctx_r19.ticket.content)(\"maxlength\", 255)(\"placeholder\", ctx_r19.tranService.translate(\"ticket.diagnose.label.content\"));\n  }\n}\nfunction ListDiagnoseTicketComponent_span_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r20.ticket.content);\n  }\n}\nfunction ListDiagnoseTicketComponent_small_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r21.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListDiagnoseTicketComponent_textarea_90_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"textarea\", 69);\n    i0.ɵɵlistener(\"ngModelChange\", function ListDiagnoseTicketComponent_textarea_90_Template_textarea_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r51 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r51.ticket.note = $event);\n    })(\"keydown\", function ListDiagnoseTicketComponent_textarea_90_Template_textarea_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r53 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r53.onKeyDownNote($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"autoResize\", false)(\"ngModel\", ctx_r22.ticket.note)(\"maxlength\", 255)(\"placeholder\", ctx_r22.tranService.translate(\"ticket.label.note\"));\n  }\n}\nfunction ListDiagnoseTicketComponent_span_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 64);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r23.ticket.note);\n  }\n}\nfunction ListDiagnoseTicketComponent_small_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r24.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListDiagnoseTicketComponent_div_96_p_dropdown_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dropdown\", 73);\n    i0.ɵɵlistener(\"ngModelChange\", function ListDiagnoseTicketComponent_div_96_p_dropdown_4_Template_p_dropdown_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r60 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r60.ticket.status = $event);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r54 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", true)(\"ngModel\", ctx_r54.ticket.status)(\"options\", ctx_r54.ticket.statusOld !== null ? ctx_r54.mapTicketStatus[ctx_r54.ticket.statusOld] : ctx_r54.listTicketStatus)(\"placeholder\", ctx_r54.tranService.translate(\"ticket.label.status\"))(\"emptyMessage\", ctx_r54.tranService.translate(\"global.text.nodata\"));\n  }\n}\nconst _c2 = function () {\n  return [\"p-2\", \"text-white\", \"bg-cyan-300\", \"border-round\", \"inline-block\"];\n};\nfunction ListDiagnoseTicketComponent_div_96_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r55 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c2));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r55.getValueStatus(ctx_r55.ticket.statusOld));\n  }\n}\nconst _c3 = function () {\n  return [\"p-2\", \"text-white\", \"bg-bluegray-500\", \"border-round\", \"inline-block\"];\n};\nfunction ListDiagnoseTicketComponent_div_96_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r56 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c3));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r56.getValueStatus(ctx_r56.ticket.statusOld));\n  }\n}\nconst _c4 = function () {\n  return [\"p-2\", \"text-white\", \"bg-orange-400\", \"border-round\", \"inline-block\"];\n};\nfunction ListDiagnoseTicketComponent_div_96_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r57 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c4));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r57.getValueStatus(ctx_r57.ticket.statusOld));\n  }\n}\nconst _c5 = function () {\n  return [\"p-2\", \"text-white\", \"bg-red-500\", \"border-round\", \"inline-block\"];\n};\nfunction ListDiagnoseTicketComponent_div_96_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r58 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c5));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r58.getValueStatus(ctx_r58.ticket.statusOld));\n  }\n}\nconst _c6 = function () {\n  return [\"p-2\", \"text-white\", \"bg-green-500\", \"border-round\", \"inline-block\"];\n};\nfunction ListDiagnoseTicketComponent_div_96_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r59 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(i0.ɵɵpureFunction0(3, _c6));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r59.getValueStatus(ctx_r59.ticket.statusOld));\n  }\n}\nfunction ListDiagnoseTicketComponent_div_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\", 70);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵtemplate(4, ListDiagnoseTicketComponent_div_96_p_dropdown_4_Template, 1, 6, \"p-dropdown\", 71);\n    i0.ɵɵtemplate(5, ListDiagnoseTicketComponent_div_96_span_5_Template, 2, 4, \"span\", 72);\n    i0.ɵɵtemplate(6, ListDiagnoseTicketComponent_div_96_span_6_Template, 2, 4, \"span\", 72);\n    i0.ɵɵtemplate(7, ListDiagnoseTicketComponent_div_96_span_7_Template, 2, 4, \"span\", 72);\n    i0.ɵɵtemplate(8, ListDiagnoseTicketComponent_div_96_span_8_Template, 2, 4, \"span\", 72);\n    i0.ɵɵtemplate(9, ListDiagnoseTicketComponent_div_96_span_9_Template, 2, 4, \"span\", 72);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r25.userInfo.type == ctx_r25.userType.PROVINCE && ctx_r25.typeRequest != \"detail\" ? ctx_r25.ticket.assigneeId == null || ctx_r25.ticket.assigneeId != null && !ctx_r25.listActivatedAccount.includes(ctx_r25.ticket.assigneeId) ? \"\" : \"hidden\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r25.tranService.translate(\"ticket.label.status\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.typeRequest == \"update\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.typeRequest == \"detail\" && ctx_r25.ticket.statusOld == 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.typeRequest == \"detail\" && ctx_r25.ticket.statusOld == 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.typeRequest == \"detail\" && ctx_r25.ticket.statusOld == 2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.typeRequest == \"detail\" && ctx_r25.ticket.statusOld == 3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.typeRequest == \"detail\" && ctx_r25.ticket.statusOld == 4);\n  }\n}\nfunction ListDiagnoseTicketComponent_div_97_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r62 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r62.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListDiagnoseTicketComponent_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"label\", 74);\n    i0.ɵɵelementStart(2, \"div\", 35);\n    i0.ɵɵtemplate(3, ListDiagnoseTicketComponent_div_97_small_3_Template, 2, 1, \"small\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r26.formTicketSim.controls.status.dirty && (ctx_r26.formTicketSim.controls.status.errors == null ? null : ctx_r26.formTicketSim.controls.status.errors.required));\n  }\n}\nfunction ListDiagnoseTicketComponent_div_98_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 58);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ListDiagnoseTicketComponent_div_98_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r65 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\", 56);\n    i0.ɵɵtext(2);\n    i0.ɵɵtemplate(3, ListDiagnoseTicketComponent_div_98_span_3_Template, 2, 0, \"span\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 35)(5, \"input\", 75);\n    i0.ɵɵlistener(\"ngModelChange\", function ListDiagnoseTicketComponent_div_98_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r64 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r64.ticket.cause = $event);\n    })(\"keydown\", function ListDiagnoseTicketComponent_div_98_Template_input_keydown_5_listener($event) {\n      i0.ɵɵrestoreView(_r65);\n      const ctx_r66 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r66.onKeyDownCause($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r27.userInfo.type == ctx_r27.userType.PROVINCE ? ctx_r27.ticket.assigneeId == null || ctx_r27.ticket.assigneeId != null && !ctx_r27.listActivatedAccount.includes(ctx_r27.ticket.assigneeId) ? \"\" : \"hidden\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r27.tranService.translate(\"ticket.label.processingNotes\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r27.ticket.status);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r27.ticket.cause)(\"required\", ctx_r27.ticket.status != null)(\"maxLength\", 255)(\"placeholder\", ctx_r27.tranService.translate(\"ticket.label.processingNotes\"));\n  }\n}\nfunction ListDiagnoseTicketComponent_div_99_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r67 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r67.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListDiagnoseTicketComponent_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"label\", 34);\n    i0.ɵɵelementStart(2, \"div\", 35);\n    i0.ɵɵtemplate(3, ListDiagnoseTicketComponent_div_99_small_3_Template, 2, 1, \"small\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r28.formTicketSim.controls.cause.dirty && (ctx_r28.formTicketSim.controls.cause.errors == null ? null : ctx_r28.formTicketSim.controls.cause.errors.required) || ctx_r28.ticket.status != null && (ctx_r28.ticket.cause == null || ctx_r28.ticket.cause.trim() == \"\"));\n  }\n}\nfunction ListDiagnoseTicketComponent_div_100_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 80);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 81);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r68 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r68.tranService.translate(\"global.text.stt\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r68.tranService.translate(\"account.text.account\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r68.tranService.translate(\"global.button.changeStatus\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r68.tranService.translate(\"account.label.time\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r68.tranService.translate(\"ticket.label.content\"));\n  }\n}\nfunction ListDiagnoseTicketComponent_div_100_ng_template_5_input_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r78 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 84);\n    i0.ɵɵlistener(\"ngModelChange\", function ListDiagnoseTicketComponent_div_100_ng_template_5_input_11_Template_input_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r78);\n      const note_r70 = i0.ɵɵnextContext().$implicit;\n      return i0.ɵɵresetView(note_r70.content = $event);\n    })(\"keydown\", function ListDiagnoseTicketComponent_div_100_ng_template_5_input_11_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r78);\n      const note_r70 = i0.ɵɵnextContext().$implicit;\n      const ctx_r79 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r79.onKeyDownNoteContent($event, note_r70));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const note_r70 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", note_r70.content)(\"required\", true)(\"maxLength\", 255);\n  }\n}\nfunction ListDiagnoseTicketComponent_div_100_ng_template_5_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const note_r70 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(note_r70.content);\n  }\n}\nfunction ListDiagnoseTicketComponent_div_100_ng_template_5_small_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r74 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r74.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ListDiagnoseTicketComponent_div_100_ng_template_5_small_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 58);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r75 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r75.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListDiagnoseTicketComponent_div_100_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 82)(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtemplate(11, ListDiagnoseTicketComponent_div_100_ng_template_5_input_11_Template, 1, 3, \"input\", 83);\n    i0.ɵɵtemplate(12, ListDiagnoseTicketComponent_div_100_ng_template_5_span_12_Template, 2, 1, \"span\", 32);\n    i0.ɵɵtemplate(13, ListDiagnoseTicketComponent_div_100_ng_template_5_small_13_Template, 2, 1, \"small\", 36);\n    i0.ɵɵtemplate(14, ListDiagnoseTicketComponent_div_100_ng_template_5_small_14_Template, 2, 2, \"small\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const note_r70 = ctx.$implicit;\n    const i_r71 = ctx.rowIndex;\n    const ctx_r69 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"formGroup\", ctx_r69.mapForm[note_r70.id]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r71 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(note_r70.userName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r69.getValueStatus(note_r70.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 9, note_r70.createdDate, \"HH:mm:ss dd/MM/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r69.typeRequest == \"update\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r69.typeRequest == \"detail\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r69.mapForm[note_r70.id].controls.content.dirty && (ctx_r69.mapForm[note_r70.id].controls.content.errors == null ? null : ctx_r69.mapForm[note_r70.id].controls.content.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r69.mapForm[note_r70.id].controls.content.errors == null ? null : ctx_r69.mapForm[note_r70.id].controls.content.errors.maxLength);\n  }\n}\nconst _c7 = function () {\n  return {\n    \"min-width\": \"50rem\"\n  };\n};\nfunction ListDiagnoseTicketComponent_div_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"label\", 76);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-table\", 77);\n    i0.ɵɵtemplate(4, ListDiagnoseTicketComponent_div_100_ng_template_4_Template, 11, 5, \"ng-template\", 78);\n    i0.ɵɵtemplate(5, ListDiagnoseTicketComponent_div_100_ng_template_5_Template, 15, 12, \"ng-template\", 79);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r29.tranService.translate(\"ticket.label.listNote\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r29.listNotes)(\"tableStyle\", i0.ɵɵpureFunction0(3, _c7));\n  }\n}\nfunction ListDiagnoseTicketComponent_div_101_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r84 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"p-button\", 86);\n    i0.ɵɵlistener(\"click\", function ListDiagnoseTicketComponent_div_101_Template_p_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r84);\n      const ctx_r83 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r83.isShowCreateRequest = false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(2, \"p-button\", 87);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", ctx_r30.tranService.translate(\"global.button.cancel\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r30.formTicketSim.invalid || ctx_r30.typeRequest == \"create\" && !ctx_r30.isValidDiagnoseNumber || ctx_r30.ticket.status != null && ctx_r30.ticket.cause != null && ctx_r30.ticket.cause.trim() == \"\" || ctx_r30.listNotes.length > 0 && !ctx_r30.isFormValid())(\"label\", ctx_r30.tranService.translate(\"global.button.save\"));\n  }\n}\nconst _c8 = function (a0) {\n  return [a0];\n};\nconst _c9 = function () {\n  return {\n    width: \"800px\",\n    overflowY: \"scroll\",\n    maxHeight: \"80%\"\n  };\n};\nconst _c10 = function () {\n  return {\n    \"1199px\": \"75vw\",\n    \"575px\": \"90vw\"\n  };\n};\nexport class ListDiagnoseTicketComponent extends ComponentBase {\n  constructor(ticketService, accountService, logHandleTicketService, formBuilder, injector) {\n    super(injector);\n    this.ticketService = ticketService;\n    this.accountService = accountService;\n    this.logHandleTicketService = logHandleTicketService;\n    this.formBuilder = formBuilder;\n    this.injector = injector;\n    this.maxlengthContactName = 255;\n    this.isValidDiagnoseNumber = false;\n    this.oldTicket = {};\n    this.isShowTableNote = false;\n    this.mapForm = {};\n    this.titlePopup = '';\n    this.controlComboSelect = new ComboLazyControl();\n    this.CONSTANTS = CONSTANTS;\n  }\n  ngOnInit() {\n    let me = this;\n    this.userInfo = this.sessionService.userInfo;\n    this.isShowCreateRequest = false;\n    this.typeRequest = 'create';\n    this.userType = CONSTANTS.USER_TYPE;\n    this.listNotes = [];\n    this.ticket = {\n      id: null,\n      contactName: null,\n      contactEmail: null,\n      contactPhone: null,\n      content: null,\n      note: null,\n      cause: null,\n      type: CONSTANTS.REQUEST_TYPE.DIAGNOSE,\n      sim: null,\n      status: null,\n      statusOld: null,\n      assigneeId: null,\n      provinceCode: null\n    };\n    this.mapTicketStatus = {\n      0: [{\n        label: me.tranService.translate('ticket.status.received'),\n        value: 1\n      }, {\n        label: me.tranService.translate('ticket.status.inProgress'),\n        value: 2\n      }, {\n        label: me.tranService.translate('ticket.status.done'),\n        value: 4\n      }, {\n        label: me.tranService.translate('ticket.status.reject'),\n        value: 3\n      }],\n      1: [{\n        label: me.tranService.translate('ticket.status.inProgress'),\n        value: 2\n      }, {\n        label: me.tranService.translate('ticket.status.done'),\n        value: 4\n      }, {\n        label: me.tranService.translate('ticket.status.reject'),\n        value: 3\n      }],\n      2: [{\n        label: me.tranService.translate('ticket.status.done'),\n        value: 4\n      }, {\n        label: me.tranService.translate('ticket.status.reject'),\n        value: 3\n      }]\n    };\n    this.listTicketStatus = [{\n      label: me.tranService.translate('ticket.status.new'),\n      value: 0\n    }, {\n      label: me.tranService.translate('ticket.status.received'),\n      value: 1\n    }, {\n      label: me.tranService.translate('ticket.status.inProgress'),\n      value: 2\n    }, {\n      label: me.tranService.translate('ticket.status.reject'),\n      value: 3\n    }, {\n      label: me.tranService.translate('ticket.status.done'),\n      value: 4\n    }];\n    this.searchInfo = {\n      provinceCode: null,\n      email: null,\n      contactPhone: null,\n      contactEmail: null,\n      type: CONSTANTS.REQUEST_TYPE.DIAGNOSE,\n      status: null,\n      sim: null\n    };\n    this.columns = [{\n      name: this.tranService.translate(\"ticket.label.province\"),\n      key: \"provinceName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: this.userInfo.type == CONSTANTS.USER_TYPE.ADMIN,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"ticket.diagnose.label.name\"),\n      key: \"contactName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      isShowTooltip: true,\n      style: {\n        display: 'inline-block',\n        maxWidth: '350px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.diagnose.label.email\"),\n      key: \"contactEmail\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      isShowTooltip: true,\n      style: {\n        display: 'inline-block',\n        maxWidth: '350px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.diagnose.label.phone\"),\n      key: \"contactPhone\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"ticket.diagnose.label.diagnoseNumber\"),\n      key: \"sim\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"ticket.diagnose.label.content\"),\n      key: \"content\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      isShowTooltip: true,\n      style: {\n        display: 'inline-block',\n        maxWidth: '350px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.createdDate\"),\n      key: \"createdDate\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        return me.utilService.convertDateToString(new Date(value));\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.updatedDate\"),\n      key: \"updatedDate\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        return value ? me.utilService.convertDateToString(new Date(value)) : \"\";\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.updateBy\"),\n      key: \"updatedByName\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      isShowTooltip: true,\n      style: {\n        display: 'inline-block',\n        maxWidth: '350px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.status\"),\n      key: \"status\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcGetClassname: value => {\n        if (value == CONSTANTS.REQUEST_STATUS.NEW) {\n          return ['p-2', 'text-white', \"bg-cyan-300\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\n          return ['p-2', 'text-white', \"bg-bluegray-500\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\n          return ['p-2', 'text-white', \"bg-orange-400\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\n          return ['p-2', 'text-white', \"bg-red-500\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\n          return ['p-2', 'text-white', \"bg-green-500\", \"border-round\", \"inline-block\"];\n        }\n        return '';\n      },\n      funcConvertText: function (value) {\n        if (value == CONSTANTS.REQUEST_STATUS.NEW) {\n          return me.tranService.translate(\"ticket.status.new\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\n          return me.tranService.translate(\"ticket.status.received\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\n          return me.tranService.translate(\"ticket.status.inProgress\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\n          return me.tranService.translate(\"ticket.status.reject\");\n        } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\n          return me.tranService.translate(\"ticket.status.done\");\n        }\n        return \"\";\n      }\n    }];\n    this.optionTable = {\n      hasClearSelected: false,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false,\n      action: [{\n        icon: \"pi pi-info-circle\",\n        tooltip: this.tranService.translate(\"global.button.view\"),\n        func: function (id, item) {\n          me.handleDetailRequest(id, item);\n        }\n      }, {\n        icon: \"pi pi-window-maximize\",\n        tooltip: this.tranService.translate(\"global.button.edit\"),\n        func: function (id, item) {\n          me.handleEditRequest(id, item);\n        },\n        funcAppear: function (id, item) {\n          //admin + kh + gdv không câ nhật\n          if (me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER || me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) {\n            return false;\n          }\n          // chưa cap nhat + chua gan + co quyen thi cap nhat\n          if (!item.updatedBy && !item.assigneeId && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;\n          //da duoc cap nhat boi tinh khac thi khong cap nhat\n          if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy != undefined && item.updatedBy != null && item.updatedBy !== me.userInfo.id) {\n            return false;\n          }\n          //la acc tinh co quyen cap nhat\n          if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;else {\n            return false;\n          }\n        }\n      }]\n    };\n    this.pageNumber = 0;\n    this.pageSize = 10;\n    this.sort = \"status,asc\";\n    this.dataSet = {\n      content: [],\n      total: 0\n    };\n    this.formSearchTicket = this.formBuilder.group(this.searchInfo);\n    this.formTicketSim = this.formBuilder.group(this.ticket);\n    this.getListProvince();\n    this.listActivatedAccount = [];\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n  }\n  getValueStatus(value) {\n    let me = this;\n    {\n      if (value == CONSTANTS.REQUEST_STATUS.NEW) {\n        return me.tranService.translate(\"ticket.status.new\");\n      } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\n        return me.tranService.translate(\"ticket.status.received\");\n      } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\n        return me.tranService.translate(\"ticket.status.inProgress\");\n      } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\n        return me.tranService.translate(\"ticket.status.reject\");\n      } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\n        return me.tranService.translate(\"ticket.status.done\");\n      }\n      return \"\";\n    }\n  }\n  getValueDate(value) {\n    let me = this;\n    // console.log(value)\n    return me.utilService.convertDateToString(new Date(value));\n  }\n  search(page, limit, sort, params) {\n    let me = this;\n    this.pageNumber = page;\n    this.pageSize = limit;\n    this.sort = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    Object.keys(this.searchInfo).forEach(key => {\n      if (this.searchInfo[key] != null) {\n        dataParams[key] = this.searchInfo[key];\n      }\n    });\n    this.dataSet = {\n      content: [],\n      total: 0\n    };\n    me.messageCommonService.onload();\n    this.ticketService.searchTicket(dataParams, response => {\n      me.dataSet = {\n        content: response.content,\n        total: response.totalElements\n      };\n      // if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE ||\n      //     me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) {\n      //     let listAssigneeId = Array.from(new Set(me.dataSet.content.filter(item => item.assigneeId !== null)\n      //         .map(item => item.assigneeId as number)));\n      //\n      //     me.dataSet.content.forEach(item => {\n      //         if (item.updateBy !== null) {\n      //             listAssigneeId.push(item.updateBy as number);\n      //         }\n      //     });\n      //\n      //     const statusCheckListId = Array.from(new Set(listAssigneeId));\n      //\n      //     me.accountService.getListActivatedAccount(statusCheckListId, (response) => {\n      //         me.listActivatedAccount = response;\n      //         this.optionTable = {\n      //             hasClearSelected: false,\n      //             hasShowChoose: false,\n      //             hasShowIndex: true,\n      //             hasShowToggleColumn: false,\n      //             action: [\n      //                 {\n      //                     icon: \"pi pi-info-circle\",\n      //                     tooltip: this.tranService.translate(\"global.button.view\"),\n      //                     func: function (id, item) {\n      //                         me.handleDetailRequest(id, item)\n      //                     }\n      //                 },\n      //                 {\n      //                     icon: \"pi pi-window-maximize\",\n      //                     tooltip: this.tranService.translate(\"global.button.edit\"),\n      //                     func: function (id, item) {\n      //                         me.handleEditRequest(id, item)\n      //                     },\n      //                     funcAppear: function (id, item) {\n      //                         //admin + kh + gdv không câ nhật\n      //                         if (me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER || me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) {\n      //                             return false; }\n      //                         // chưa cap nhat + chua gan + co quyen thi cap nhat\n      //                         if (!item.updatedBy && !item.assigneeId && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;\n      //                         //da duoc cap nhat boi tinh khac thi khong cap nhat\n      //                         if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy!= undefined &&  item.updatedBy != null && item.updatedBy !== me.userInfo.id) {\n      //                             return false;\n      //                         }\n      //                         //la acc tinh co quyen cap nhat\n      //                         if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true\n      //                         else {\n      //                             return false;\n      //                         }\n      //                     }\n      //                 }\n      //             ]\n      //         }\n      //         me.changeTable = true;\n      //     })\n      // }\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  resetTicket() {\n    this.ticket = {\n      id: null,\n      contactName: null,\n      contactEmail: null,\n      contactPhone: null,\n      content: null,\n      note: null,\n      cause: null,\n      type: CONSTANTS.REQUEST_TYPE.DIAGNOSE,\n      sim: null,\n      status: null,\n      statusOld: null,\n      assigneeId: null,\n      provinceCode: null\n    };\n    this.isValidDiagnoseNumber = false;\n  }\n  onSubmitSearch() {\n    this.pageNumber = 0;\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n  }\n  getListProvince() {\n    this.accountService.getListProvince(response => {\n      this.listProvince = response.map(el => {\n        return {\n          ...el,\n          display: `${el.code} - ${el.name}`\n        };\n      });\n    });\n  }\n  getProvinceName(provinceCode) {\n    const province = this.listProvince.find(el => el.code === provinceCode);\n    return province ? province.code + ' - ' + province.name : \"\";\n  }\n  // tạo sửa yêu cầu\n  createOrUpdateRequest() {\n    if (this.messageCommonService.isloading == true || this.isShowCreateRequest == false) return;\n    let me = this;\n    this.messageCommonService.onload();\n    if (this.typeRequest == 'create') {\n      let bodySend = {\n        contactName: this.ticket.contactName,\n        contactEmail: this.ticket.contactEmail,\n        contactPhone: this.ticket.contactPhone,\n        content: this.ticket.content,\n        note: this.ticket.note,\n        type: this.ticket.type,\n        sim: this.ticket.type == CONSTANTS.REQUEST_TYPE.DIAGNOSE ? this.ticket.sim : null\n      };\n      if (bodySend.contactPhone != null) {\n        if (bodySend.contactPhone.startsWith('0')) {\n          bodySend.contactPhone = \"84\" + bodySend.contactPhone.substring(1, bodySend.contactPhone.length);\n        } else if (bodySend.contactPhone.length == 9 || bodySend.contactPhone.length == 10) {\n          bodySend.contactPhone = \"84\" + bodySend.contactPhone;\n        }\n      }\n      this.ticketService.createTicket(bodySend, resp => {\n        me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n        me.isShowCreateRequest = false;\n        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n        // nếu KH đc gán cho GDV thì gửi mail cho GDV và danh sách admin đc cấu hình không thì chỉ gửi mail cho danh sách admin đc cấu hình\n        // get mail admin tinh dc cau hinh\n        me.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, resp1 => {\n          let array = [];\n          for (let info of resp1.emailInfos) {\n            array.push({\n              userId: info.userId,\n              ticketId: resp.id\n            });\n          }\n          if (resp?.assigneeId) {\n            array.push({\n              userId: resp.assigneeId,\n              ticketId: resp.id\n            });\n          }\n          me.ticketService.sendMailNotify(array);\n        });\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n    } else if (this.typeRequest == 'update') {\n      let bodySend = {\n        contactName: this.ticket.contactName,\n        contactEmail: this.ticket.contactEmail,\n        contactPhone: this.ticket.contactPhone,\n        content: this.ticket.content,\n        note: this.ticket.note,\n        type: this.ticket.type,\n        sim: this.ticket.type == CONSTANTS.REQUEST_TYPE.DIAGNOSE ? this.ticket.sim : null,\n        status: this.ticket.status,\n        cause: this.ticket.cause,\n        assigneeId: this.ticket.assigneeId,\n        listLog: this.listNotes\n      };\n      if (bodySend.contactPhone != null) {\n        if (bodySend.contactPhone.startsWith('0')) {\n          bodySend.contactPhone = \"84\" + bodySend.contactPhone.substring(1, bodySend.contactPhone.length);\n        } else if (bodySend.contactPhone.length == 9 || bodySend.contactPhone.length == 10) {\n          bodySend.contactPhone = \"84\" + bodySend.contactPhone;\n        }\n      }\n      // update ticket\n      this.ticketService.updateTicket(this.ticket.id, bodySend, resp => {\n        me.isShowCreateRequest = false;\n        me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n        if (resp.assigneeId != null && resp.assigneeId != undefined) {\n          me.ticketService.sendMailNotify([{\n            userId: resp.assigneeId,\n            ticketId: resp.id\n          }]);\n        }\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n    }\n  }\n  showModalCreate() {\n    this.isShowCreateRequest = true;\n    this.typeRequest = 'create';\n    this.titlePopup = this.tranService.translate('ticket.label.createRequest');\n    this.resetTicket();\n    // auto fill thong tin khi tao\n    if (this.userInfo.type === CONSTANTS.USER_TYPE.CUSTOMER) {\n      this.ticket.contactName = this.userInfo.fullName.substring(0, this.maxlengthContactName);\n      this.ticket.contactPhone = this.userInfo.phone;\n      this.ticket.contactEmail = this.userInfo.email;\n    }\n    this.formTicketSim = this.formBuilder.group(this.ticket);\n  }\n  handleEditRequest(id, item) {\n    let me = this;\n    this.formTicketSim.reset();\n    this.titlePopup = this.tranService.translate('ticket.label.updateRequest');\n    this.typeRequest = 'update';\n    this.isShowCreateRequest = true;\n    this.ticketService.getDetailTicket(item.id, resp => {\n      me.ticket = {\n        id: resp.id,\n        contactName: resp.contactName,\n        contactEmail: resp.contactEmail,\n        contactPhone: resp.contactPhone,\n        content: resp.content,\n        note: resp.note,\n        cause: resp.cause,\n        type: resp.type,\n        sim: resp.sim,\n        status: null,\n        statusOld: resp.status,\n        assigneeId: resp.assigneeId,\n        provinceCode: resp.provinceCode\n      };\n      me.oldTicket = {\n        ...me.ticket\n      };\n      me.formTicketSim = me.formBuilder.group(me.ticket);\n      // lấy list note\n      this.logHandleTicketService.search({\n        ticketId: item.id\n      }, res => {\n        // console.log(res.content)\n        this.listNotes = res.content;\n        // for(let note of this.listNotes) {\n        //   this.mapForm[note.id] = this.formBuilder.group(note);\n        // }\n        this.listNotes.forEach(note => {\n          this.mapForm[note.id] = this.formBuilder.group({\n            content: ['', [Validators.required, Validators.maxLength(255), this.noWhitespaceValidator()]]\n          });\n        });\n        me.isShowTableNote = true;\n      });\n    });\n    this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, resp => {\n      me.listEmail = resp.emailInfos;\n    });\n  }\n  handleDetailRequest(id, item) {\n    let me = this;\n    this.formTicketSim.reset();\n    this.titlePopup = this.tranService.translate('ticket.label.viewDetailDiagnose');\n    this.typeRequest = 'detail';\n    this.isShowCreateRequest = true;\n    this.ticketService.getDetailTicket(item.id, resp => {\n      me.ticket = {\n        id: resp.id,\n        contactName: resp.contactName,\n        contactEmail: resp.contactEmail,\n        contactPhone: resp.contactPhone,\n        content: resp.content,\n        note: resp.note,\n        cause: resp.cause,\n        type: resp.type,\n        sim: resp.sim,\n        status: null,\n        statusOld: resp.status,\n        assigneeId: resp.assigneeId,\n        provinceCode: resp.provinceCode\n      };\n      me.oldTicket = {\n        ...me.ticket\n      };\n      me.formTicketSim = me.formBuilder.group(me.ticket);\n      // lấy list note\n      this.logHandleTicketService.search({\n        ticketId: item.id\n      }, res => {\n        // console.log(res.content)\n        this.listNotes = res.content;\n        for (let note of this.listNotes) {\n          this.mapForm[note.id] = this.formBuilder.group(note);\n        }\n        me.isShowTableNote = true;\n      });\n    });\n    this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, resp => {\n      me.listEmail = resp.emailInfos;\n    });\n  }\n  preventCharacter(event) {\n    if (event.ctrlKey) {\n      return;\n    }\n    if (event.keyCode == 8 || event.keyCode == 13 || event.keyCode == 37 || event.keyCode == 39) {\n      return;\n    }\n    if (event.keyCode < 48 || event.keyCode > 57) {\n      event.preventDefault();\n    }\n    // Chặn ký tự 'e', 'E' và dấu '+'\n    if (event.keyCode == 69 || event.keyCode == 101 || event.keyCode == 107 || event.keyCode == 187) {\n      event.preventDefault();\n    }\n  }\n  onInputSim(sim) {\n    let regex = /^84[0-9]{9,10}$/;\n    // console.log(regex.test(sim))\n    if (!regex.test(sim)) {\n      this.isValidDiagnoseNumber = false;\n      return;\n    } else {\n      this.isValidDiagnoseNumber = true;\n    }\n  }\n  isFormValid() {\n    return Object.values(this.mapForm).every(formGroup => formGroup.valid);\n  }\n  noWhitespaceValidator() {\n    return control => {\n      const isWhitespace = (control.value || '').trim().length === 0;\n      const isValid = !isWhitespace;\n      return isValid ? null : {\n        whitespace: true\n      };\n    };\n  }\n  onKeyDownNote(event) {\n    if (event.key === ' ' && (this.ticket.note == null || this.ticket.note != null && this.ticket.note.trim() === '')) {\n      event.preventDefault();\n    }\n    if (this.ticket.note != null && this.ticket.note.trim() != '') {\n      this.ticket.note = this.ticket.note.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  onKeyDownContent(event) {\n    if (event.key === ' ' && (this.ticket.content == null || this.ticket.content != null && this.ticket.content.trim() === '')) {\n      event.preventDefault();\n    }\n    if (this.ticket.content != null && this.ticket.content.trim() != '') {\n      this.ticket.content = this.ticket.content.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  onKeyDownNoteContent(event, note) {\n    if (event.key === ' ' && (!note.content || note.content.trim() === '')) {\n      event.preventDefault();\n    }\n    if (note.content && note.content.trim() !== '') {\n      note.content = note.content.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  onKeyDownCause(event) {\n    if (event.key === ' ' && (this.ticket.cause == null || this.ticket.cause != null && this.ticket.cause.trim() === '')) {\n      event.preventDefault();\n    }\n    if (this.ticket.cause != null && this.ticket.cause.trim() != '') {\n      this.ticket.cause = this.ticket.cause.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  changeDiagnoseNumber($event) {\n    if ($event !== null && $event !== undefined) {\n      this.onInputSim($event);\n      if (this.isValidDiagnoseNumber) {\n        this.ticket.sim = $event;\n      }\n    } else {\n      this.isValidDiagnoseNumber = false;\n    }\n  }\n  static {\n    this.ɵfac = function ListDiagnoseTicketComponent_Factory(t) {\n      return new (t || ListDiagnoseTicketComponent)(i0.ɵɵdirectiveInject(TicketService), i0.ɵɵdirectiveInject(AccountService), i0.ɵɵdirectiveInject(LogHandleTicketService), i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ListDiagnoseTicketComponent,\n      selectors: [[\"ticket-diagnose-list\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 102,\n      vars: 80,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [\"styleClass\", \"p-button-info\", \"icon\", \"\", 3, \"label\", \"click\", 4, \"ngIf\"], [1, \"pt-3\", \"pb-2\", \"vnpt-field-set\", 3, \"formGroup\", \"ngSubmit\"], [3, \"toggleable\", \"header\"], [1, \"grid\", \"search-grid-4\"], [\"class\", \"col-2 col-4\", 4, \"ngIf\"], [1, \"col-2\"], [1, \"p-float-label\"], [\"styleClass\", \"w-full\", \"filterBy\", \"display\", \"id\", \"provinceCode\", \"formControlName\", \"status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", \"filterBy\", \"label\", 3, \"showClear\", \"filter\", \"autoDisplayFirst\", \"ngModel\", \"required\", \"options\", \"ngModelChange\"], [\"htmlFor\", \"status\"], [\"pInputText\", \"\", \"id\", \"contactEmail\", \"formControlName\", \"contactEmail\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"email\"], [\"pInputText\", \"\", \"id\", \"contactPhone\", \"formControlName\", \"contactPhone\", \"type\", \"number\", \"min\", \"0\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\", \"keydown\"], [\"htmlFor\", \"contactPhone\", 1, \"label-phone\"], [\"pInputText\", \"\", \"id\", \"sim\", \"formControlName\", \"sim\", \"type\", \"number\", \"min\", \"0\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\", \"keydown\"], [\"htmlFor\", \"sim\", 1, \"label-phone\"], [1, \"col-2\", \"pb-0\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"submit\"], [3, \"tableId\", \"fieldId\", \"columns\", \"dataSet\", \"options\", \"pageNumber\", \"loadData\", \"pageSize\", \"sort\", \"params\", \"labelTable\"], [1, \"flex\", \"justify-content-center\", \"dialog-vnpt\"], [3, \"breakpoints\", \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"mt-3\", 3, \"formGroup\", \"ngSubmit\"], [1, \"flex\", \"flex-row\", \"flex-wrap\", \"justify-content-between\", \"w-full\"], [\"class\", \"w-full field grid chart-grid\", 4, \"ngIf\"], [1, \"w-full\", \"field\", \"grid\", \"chart-grid\"], [\"htmlFor\", \"contactName\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [1, \"col\", 2, \"width\", \"calc(100% - 180px)\", \"overflow-wrap\", \"break-word\"], [\"class\", \"w-full\", \"pInputText\", \"\", \"id\", \"contactName\", \"formControlName\", \"contactName\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"readonly\", \"ngModelChange\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"w-full\", \"field\", \"grid\", \"text-error-field\"], [\"htmlFor\", \"fullName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"col\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [\"htmlFor\", \"email\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"class\", \"w-full\", \"pInputText\", \"\", \"id\", \"contactEmail\", \"formControlName\", \"contactEmail\", \"pattern\", \"^[a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+)+(\\\\.[a-z]{2,})$\", 3, \"ngModel\", \"required\", \"maxLength\", \"readonly\", \"placeholder\", \"ngModelChange\", 4, \"ngIf\"], [\"htmlFor\", \"email\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"htmlFor\", \"phone\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"class\", \"w-full\", \"pInputText\", \"\", \"id\", \"contactPhone\", \"formControlName\", \"contactPhone\", \"pattern\", \"^((\\\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\", 3, \"ngModel\", \"required\", \"maxLength\", \"readonly\", \"placeholder\", \"ngModelChange\", \"keydown\", 4, \"ngIf\"], [\"class\", \"w-full field grid text-error-field\", 4, \"ngIf\"], [\"htmlFor\", \"content\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"class\", \"w-full\", \"style\", \"resize: none;\", \"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"content\", \"formControlName\", \"content\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"placeholder\", \"ngModelChange\", \"keydown\", 4, \"ngIf\"], [\"style\", \"word-break: break-all\", 4, \"ngIf\"], [\"htmlFor\", \"content\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"htmlFor\", \"note\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"class\", \"w-full\", \"style\", \"resize: none;\", \"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"note\", \"formControlName\", \"note\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"placeholder\", \"ngModelChange\", \"keydown\", 4, \"ngIf\"], [\"htmlFor\", \"note\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"class\", \"w-full field grid chart-grid\", 3, \"class\", 4, \"ngIf\"], [\"class\", \"flex flex-row justify-content-center align-items-center mt-3\", 4, \"ngIf\"], [\"styleClass\", \"p-button-info\", \"icon\", \"\", 3, \"label\", \"click\"], [1, \"col-2\", \"col-4\"], [\"styleClass\", \"w-full\", \"filterBy\", \"display\", \"id\", \"provinceCode\", \"formControlName\", \"provinceCode\", \"optionLabel\", \"display\", \"optionValue\", \"code\", 3, \"showClear\", \"filter\", \"autoDisplayFirst\", \"ngModel\", \"required\", \"options\", \"ngModelChange\"], [\"htmlFor\", \"provinceCode\"], [\"htmlFor\", \"contactName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"contactName\", \"formControlName\", \"contactName\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"readonly\", \"ngModelChange\"], [1, \"text-red-500\"], [\"pInputText\", \"\", \"id\", \"contactEmail\", \"formControlName\", \"contactEmail\", \"pattern\", \"^[a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+)+(\\\\.[a-z]{2,})$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"readonly\", \"placeholder\", \"ngModelChange\"], [\"pInputText\", \"\", \"id\", \"contactPhone\", \"formControlName\", \"contactPhone\", \"pattern\", \"^((\\\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"readonly\", \"placeholder\", \"ngModelChange\", \"keydown\"], [\"htmlFor\", \"diagnoseNumber\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"display\", \"inline-block\"], [\"class\", \"w-full\", \"objectKey\", \"sim\", \"paramKey\", \"msisdn\", \"keyReturn\", \"msisdn\", \"displayPattern\", \"${msisdn}\", \"typeValue\", \"primitive\", 3, \"control\", \"value\", \"placeholder\", \"isMultiChoice\", \"required\", \"paramDefault\", \"valueChange\", \"onchange\", 4, \"ngIf\"], [\"objectKey\", \"sim\", \"paramKey\", \"msisdn\", \"keyReturn\", \"msisdn\", \"displayPattern\", \"${msisdn}\", \"typeValue\", \"primitive\", 1, \"w-full\", 3, \"control\", \"value\", \"placeholder\", \"isMultiChoice\", \"required\", \"paramDefault\", \"valueChange\", \"onchange\"], [2, \"word-break\", \"break-all\"], [\"htmlFor\", \"diagnoseNumber\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"class\", \"text-red-500 block\", 4, \"ngIf\"], [1, \"text-red-500\", \"block\"], [\"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"content\", \"formControlName\", \"content\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"placeholder\", \"ngModelChange\", \"keydown\"], [\"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"note\", \"formControlName\", \"note\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"placeholder\", \"ngModelChange\", \"keydown\"], [\"for\", \"status\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"styleClass\", \"w-full\", \"id\", \"status\", \"formControlName\", \"status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"placeholder\", \"emptyMessage\", \"ngModelChange\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [\"styleClass\", \"w-full\", \"id\", \"status\", \"formControlName\", \"status\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"placeholder\", \"emptyMessage\", \"ngModelChange\"], [\"htmlFor\", \"userType\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"cause\", \"formControlName\", \"cause\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\", \"keydown\"], [\"htmlFor\", \"content\", 1, \"col-fixed\", \"font-medium\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [2, \"width\", \"100%\", 3, \"value\", \"tableStyle\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [2, \"min-width\", \"146px\"], [2, \"min-width\", \"155px\"], [3, \"formGroup\"], [\"class\", \"w-full\", \"pInputText\", \"\", \"id\", \"content\", \"formControlName\", \"content\", 3, \"ngModel\", \"required\", \"maxLength\", \"ngModelChange\", \"keydown\", 4, \"ngIf\"], [\"pInputText\", \"\", \"id\", \"content\", \"formControlName\", \"content\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"ngModelChange\", \"keydown\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"align-items-center\", \"mt-3\"], [\"styleClass\", \"mr-2 p-button-secondary\", 3, \"label\", \"click\"], [\"type\", \"submit\", \"styleClass\", \"p-button-info\", 3, \"disabled\", \"label\"]],\n      template: function ListDiagnoseTicketComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵtemplate(6, ListDiagnoseTicketComponent_p_button_6_Template, 1, 1, \"p-button\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"form\", 6);\n          i0.ɵɵlistener(\"ngSubmit\", function ListDiagnoseTicketComponent_Template_form_ngSubmit_7_listener() {\n            return ctx.onSubmitSearch();\n          });\n          i0.ɵɵelementStart(8, \"p-panel\", 7)(9, \"div\", 8);\n          i0.ɵɵtemplate(10, ListDiagnoseTicketComponent_div_10_Template, 5, 7, \"div\", 9);\n          i0.ɵɵelementStart(11, \"div\", 10)(12, \"span\", 11)(13, \"p-dropdown\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function ListDiagnoseTicketComponent_Template_p_dropdown_ngModelChange_13_listener($event) {\n            return ctx.searchInfo.status = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"label\", 13);\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 10)(17, \"span\", 11)(18, \"input\", 14);\n          i0.ɵɵlistener(\"ngModelChange\", function ListDiagnoseTicketComponent_Template_input_ngModelChange_18_listener($event) {\n            return ctx.searchInfo.contactEmail = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"label\", 15);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 10)(22, \"span\", 11)(23, \"input\", 16);\n          i0.ɵɵlistener(\"ngModelChange\", function ListDiagnoseTicketComponent_Template_input_ngModelChange_23_listener($event) {\n            return ctx.searchInfo.contactPhone = $event;\n          })(\"keydown\", function ListDiagnoseTicketComponent_Template_input_keydown_23_listener($event) {\n            return ctx.preventCharacter($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"label\", 17);\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"div\", 10)(27, \"span\", 11)(28, \"input\", 18);\n          i0.ɵɵlistener(\"ngModelChange\", function ListDiagnoseTicketComponent_Template_input_ngModelChange_28_listener($event) {\n            return ctx.searchInfo.sim = $event;\n          })(\"keydown\", function ListDiagnoseTicketComponent_Template_input_keydown_28_listener($event) {\n            return ctx.preventCharacter($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"label\", 19);\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 20);\n          i0.ɵɵelement(32, \"p-button\", 21);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(33, \"table-vnpt\", 22);\n          i0.ɵɵelementStart(34, \"div\", 23)(35, \"p-dialog\", 24);\n          i0.ɵɵlistener(\"visibleChange\", function ListDiagnoseTicketComponent_Template_p_dialog_visibleChange_35_listener($event) {\n            return ctx.isShowCreateRequest = $event;\n          });\n          i0.ɵɵelementStart(36, \"form\", 25);\n          i0.ɵɵlistener(\"ngSubmit\", function ListDiagnoseTicketComponent_Template_form_ngSubmit_36_listener() {\n            return ctx.createOrUpdateRequest();\n          });\n          i0.ɵɵelementStart(37, \"div\", 26);\n          i0.ɵɵtemplate(38, ListDiagnoseTicketComponent_div_38_Template, 6, 2, \"div\", 27);\n          i0.ɵɵelementStart(39, \"div\", 28)(40, \"label\", 29);\n          i0.ɵɵtext(41);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 30);\n          i0.ɵɵtemplate(43, ListDiagnoseTicketComponent_input_43_Template, 1, 5, \"input\", 31);\n          i0.ɵɵtemplate(44, ListDiagnoseTicketComponent_span_44_Template, 2, 1, \"span\", 32);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\", 33);\n          i0.ɵɵelement(46, \"label\", 34);\n          i0.ɵɵelementStart(47, \"div\", 35);\n          i0.ɵɵtemplate(48, ListDiagnoseTicketComponent_small_48_Template, 2, 1, \"small\", 36);\n          i0.ɵɵtemplate(49, ListDiagnoseTicketComponent_small_49_Template, 2, 2, \"small\", 36);\n          i0.ɵɵtemplate(50, ListDiagnoseTicketComponent_small_50_Template, 2, 1, \"small\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 28)(52, \"label\", 37);\n          i0.ɵɵtext(53);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"div\", 30);\n          i0.ɵɵtemplate(55, ListDiagnoseTicketComponent_input_55_Template, 1, 5, \"input\", 38);\n          i0.ɵɵtemplate(56, ListDiagnoseTicketComponent_span_56_Template, 2, 1, \"span\", 32);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 33);\n          i0.ɵɵelement(58, \"label\", 39);\n          i0.ɵɵelementStart(59, \"div\", 35);\n          i0.ɵɵtemplate(60, ListDiagnoseTicketComponent_small_60_Template, 2, 1, \"small\", 36);\n          i0.ɵɵtemplate(61, ListDiagnoseTicketComponent_small_61_Template, 2, 2, \"small\", 36);\n          i0.ɵɵtemplate(62, ListDiagnoseTicketComponent_small_62_Template, 2, 1, \"small\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(63, \"div\", 28)(64, \"label\", 40);\n          i0.ɵɵtext(65);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"div\", 35);\n          i0.ɵɵtemplate(67, ListDiagnoseTicketComponent_input_67_Template, 1, 5, \"input\", 41);\n          i0.ɵɵtemplate(68, ListDiagnoseTicketComponent_span_68_Template, 2, 1, \"span\", 32);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(69, \"div\", 33);\n          i0.ɵɵelement(70, \"label\", 40);\n          i0.ɵɵelementStart(71, \"div\", 35);\n          i0.ɵɵtemplate(72, ListDiagnoseTicketComponent_small_72_Template, 2, 1, \"small\", 36);\n          i0.ɵɵtemplate(73, ListDiagnoseTicketComponent_small_73_Template, 2, 1, \"small\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(74, ListDiagnoseTicketComponent_div_74_Template, 8, 3, \"div\", 27);\n          i0.ɵɵtemplate(75, ListDiagnoseTicketComponent_div_75_Template, 4, 1, \"div\", 42);\n          i0.ɵɵelementStart(76, \"div\", 28)(77, \"label\", 43);\n          i0.ɵɵtext(78);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"div\", 35);\n          i0.ɵɵtemplate(80, ListDiagnoseTicketComponent_textarea_80_Template, 1, 4, \"textarea\", 44);\n          i0.ɵɵtemplate(81, ListDiagnoseTicketComponent_span_81_Template, 2, 1, \"span\", 45);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"div\", 33);\n          i0.ɵɵelement(83, \"label\", 46);\n          i0.ɵɵelementStart(84, \"div\", 35);\n          i0.ɵɵtemplate(85, ListDiagnoseTicketComponent_small_85_Template, 2, 2, \"small\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(86, \"div\", 28)(87, \"label\", 47);\n          i0.ɵɵtext(88);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"div\", 35);\n          i0.ɵɵtemplate(90, ListDiagnoseTicketComponent_textarea_90_Template, 1, 4, \"textarea\", 48);\n          i0.ɵɵtemplate(91, ListDiagnoseTicketComponent_span_91_Template, 2, 1, \"span\", 45);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(92, \"div\", 33);\n          i0.ɵɵelement(93, \"label\", 49);\n          i0.ɵɵelementStart(94, \"div\", 35);\n          i0.ɵɵtemplate(95, ListDiagnoseTicketComponent_small_95_Template, 2, 2, \"small\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(96, ListDiagnoseTicketComponent_div_96_Template, 10, 9, \"div\", 50);\n          i0.ɵɵtemplate(97, ListDiagnoseTicketComponent_div_97_Template, 4, 1, \"div\", 42);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(98, ListDiagnoseTicketComponent_div_98_Template, 6, 8, \"div\", 50);\n          i0.ɵɵtemplate(99, ListDiagnoseTicketComponent_div_99_Template, 4, 1, \"div\", 42);\n          i0.ɵɵtemplate(100, ListDiagnoseTicketComponent_div_100_Template, 6, 4, \"div\", 27);\n          i0.ɵɵtemplate(101, ListDiagnoseTicketComponent_div_101_Template, 3, 3, \"div\", 51);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.menu.diagnose\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo.type == ctx.userType.CUSTOMER && ctx.checkAuthen(i0.ɵɵpureFunction1(76, _c8, ctx.CONSTANTS.PERMISSIONS.TICKET.CREATE)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formSearchTicket);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"toggleable\", true)(\"header\", ctx.tranService.translate(\"global.text.filter\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo.type == ctx.userType.ADMIN);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"showClear\", true)(\"filter\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.searchInfo.status)(\"required\", false)(\"options\", ctx.listTicketStatus)(\"filter\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.status\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.contactEmail);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.diagnose.label.email\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.contactPhone);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.diagnose.label.phone\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.sim);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.diagnose.label.diagnoseNumber\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"tableId\", \"tableTicketConfigList\")(\"fieldId\", \"provinceCode\")(\"columns\", ctx.columns)(\"dataSet\", ctx.dataSet)(\"options\", ctx.optionTable)(\"pageNumber\", ctx.pageNumber)(\"loadData\", ctx.search.bind(ctx))(\"pageSize\", ctx.pageSize)(\"sort\", ctx.sort)(\"params\", ctx.searchInfo)(\"labelTable\", ctx.tranService.translate(\"ticket.menu.requestList\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(78, _c9));\n          i0.ɵɵproperty(\"breakpoints\", i0.ɵɵpureFunction0(79, _c10))(\"header\", ctx.titlePopup)(\"visible\", ctx.isShowCreateRequest)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formTicketSim);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo.type == ctx.userType.ADMIN && ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", ctx.tranService.translate(\"ticket.diagnose.label.name\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"create\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"detail\" || ctx.typeRequest == \"update\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactName.dirty && (ctx.formTicketSim.controls.contactName.errors == null ? null : ctx.formTicketSim.controls.contactName.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactName.errors == null ? null : ctx.formTicketSim.controls.contactName.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactName.errors == null ? null : ctx.formTicketSim.controls.contactName.errors.pattern);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\"\", ctx.tranService.translate(\"ticket.diagnose.label.email\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"create\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"detail\" || ctx.typeRequest == \"update\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactEmail.dirty && (ctx.formTicketSim.controls.contactEmail.errors == null ? null : ctx.formTicketSim.controls.contactEmail.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactEmail.errors == null ? null : ctx.formTicketSim.controls.contactEmail.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactEmail.errors == null ? null : ctx.formTicketSim.controls.contactEmail.errors.pattern);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.diagnose.label.phone\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"create\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"detail\" || ctx.typeRequest == \"update\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactPhone.dirty && (ctx.formTicketSim.controls.contactPhone.errors == null ? null : ctx.formTicketSim.controls.contactPhone.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.contactPhone.errors == null ? null : ctx.formTicketSim.controls.contactPhone.errors.pattern);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.ticket.type == ctx.CONSTANTS.REQUEST_TYPE.DIAGNOSE);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.ticket.type == ctx.CONSTANTS.REQUEST_TYPE.DIAGNOSE && ctx.typeRequest == \"create\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.diagnose.label.content\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"create\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.content.errors == null ? null : ctx.formTicketSim.controls.content.errors.maxLength);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.note\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"create\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formTicketSim.controls.note.errors == null ? null : ctx.formTicketSim.controls.note.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"detail\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"detail\" && ctx.ticket.assigneeId == null);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest == \"update\" || ctx.typeRequest == \"detail\" && (ctx.ticket.assigneeId == null || ctx.ticket.assigneeId != null && !ctx.listActivatedAccount.includes(ctx.ticket.assigneeId)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", (ctx.typeRequest == \"update\" || ctx.typeRequest == \"detail\") && ctx.listNotes && ctx.listNotes.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.typeRequest != \"detail\");\n        }\n      },\n      dependencies: [i2.NgIf, i3.Breadcrumb, i4.PrimeTemplate, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MaxLengthValidator, i1.PatternValidator, i1.MinValidator, i1.FormGroupDirective, i1.FormControlName, i5.InputText, i6.Button, i7.TableVnptComponent, i8.VnptCombobox, i9.Dropdown, i10.Dialog, i11.InputTextarea, i12.Panel, i13.Table, i2.DatePipe],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["TicketService", "CONSTANTS", "ComponentBase", "AccountService", "Validators", "LogHandleTicketService", "ComboLazyControl", "i0", "ɵɵelementStart", "ɵɵlistener", "ListDiagnoseTicketComponent_p_button_6_Template_p_button_click_0_listener", "ɵɵrestoreView", "_r32", "ctx_r31", "ɵɵnextContext", "ɵɵresetView", "showModalCreate", "ɵɵelementEnd", "ɵɵproperty", "ctx_r0", "tranService", "translate", "ListDiagnoseTicketComponent_div_10_Template_p_dropdown_ngModelChange_2_listener", "$event", "_r34", "ctx_r33", "searchInfo", "provinceCode", "ɵɵtext", "ɵɵadvance", "ctx_r1", "listProvince", "ɵɵtextInterpolate", "ctx_r2", "getProvinceName", "ticket", "ListDiagnoseTicketComponent_input_43_Template_input_ngModelChange_0_listener", "_r36", "ctx_r35", "contactName", "ctx_r3", "maxlengthContactName", "ctx_r4", "ctx_r5", "ctx_r6", "ɵɵpureFunction0", "_c0", "ctx_r7", "ListDiagnoseTicketComponent_input_55_Template_input_ngModelChange_0_listener", "_r38", "ctx_r37", "contactEmail", "ctx_r8", "ctx_r9", "ctx_r10", "ctx_r11", "ctx_r12", "ListDiagnoseTicketComponent_input_67_Template_input_ngModelChange_0_listener", "_r40", "ctx_r39", "contactPhone", "ListDiagnoseTicketComponent_input_67_Template_input_keydown_0_listener", "ctx_r41", "preventCharacter", "ctx_r13", "ctx_r14", "ctx_r15", "ctx_r16", "ListDiagnoseTicketComponent_div_74_vnpt_select_6_Template_vnpt_select_valueChange_0_listener", "_r45", "ctx_r44", "sim", "ListDiagnoseTicketComponent_div_74_vnpt_select_6_Template_vnpt_select_onchange_0_listener", "ctx_r46", "changeDiagnoseNumber", "ctx_r42", "controlComboSelect", "_c1", "ctx_r43", "ɵɵtemplate", "ListDiagnoseTicketComponent_div_74_vnpt_select_6_Template", "ListDiagnoseTicketComponent_div_74_span_7_Template", "ɵɵtextInterpolate1", "ctx_r17", "typeRequest", "ctx_r47", "ɵɵelement", "ListDiagnoseTicketComponent_div_75_small_3_Template", "ctx_r18", "dirty", "error", "required", "ListDiagnoseTicketComponent_textarea_80_Template_textarea_ngModelChange_0_listener", "_r49", "ctx_r48", "content", "ListDiagnoseTicketComponent_textarea_80_Template_textarea_keydown_0_listener", "ctx_r50", "onKeyDownContent", "ctx_r19", "ctx_r20", "ctx_r21", "ListDiagnoseTicketComponent_textarea_90_Template_textarea_ngModelChange_0_listener", "_r52", "ctx_r51", "note", "ListDiagnoseTicketComponent_textarea_90_Template_textarea_keydown_0_listener", "ctx_r53", "onKeyDownNote", "ctx_r22", "ctx_r23", "ctx_r24", "ListDiagnoseTicketComponent_div_96_p_dropdown_4_Template_p_dropdown_ngModelChange_0_listener", "_r61", "ctx_r60", "status", "ctx_r54", "statusOld", "mapTicketStatus", "listTicketStatus", "ɵɵclassMap", "_c2", "ctx_r55", "getValueStatus", "_c3", "ctx_r56", "_c4", "ctx_r57", "_c5", "ctx_r58", "_c6", "ctx_r59", "ListDiagnoseTicketComponent_div_96_p_dropdown_4_Template", "ListDiagnoseTicketComponent_div_96_span_5_Template", "ListDiagnoseTicketComponent_div_96_span_6_Template", "ListDiagnoseTicketComponent_div_96_span_7_Template", "ListDiagnoseTicketComponent_div_96_span_8_Template", "ListDiagnoseTicketComponent_div_96_span_9_Template", "ctx_r25", "userInfo", "type", "userType", "PROVINCE", "assigneeId", "listActivatedAccount", "includes", "ctx_r62", "ListDiagnoseTicketComponent_div_97_small_3_Template", "ctx_r26", "formTicketSim", "controls", "errors", "ListDiagnoseTicketComponent_div_98_span_3_Template", "ListDiagnoseTicketComponent_div_98_Template_input_ngModelChange_5_listener", "_r65", "ctx_r64", "cause", "ListDiagnoseTicketComponent_div_98_Template_input_keydown_5_listener", "ctx_r66", "onKeyDownCause", "ctx_r27", "ctx_r67", "ListDiagnoseTicketComponent_div_99_small_3_Template", "ctx_r28", "trim", "ctx_r68", "ListDiagnoseTicketComponent_div_100_ng_template_5_input_11_Template_input_ngModelChange_0_listener", "_r78", "note_r70", "$implicit", "ListDiagnoseTicketComponent_div_100_ng_template_5_input_11_Template_input_keydown_0_listener", "ctx_r79", "onKeyDownNoteContent", "ctx_r74", "ctx_r75", "ListDiagnoseTicketComponent_div_100_ng_template_5_input_11_Template", "ListDiagnoseTicketComponent_div_100_ng_template_5_span_12_Template", "ListDiagnoseTicketComponent_div_100_ng_template_5_small_13_Template", "ListDiagnoseTicketComponent_div_100_ng_template_5_small_14_Template", "ctx_r69", "mapForm", "id", "i_r71", "userName", "ɵɵpipeBind2", "createdDate", "max<PERSON><PERSON><PERSON>", "ListDiagnoseTicketComponent_div_100_ng_template_4_Template", "ListDiagnoseTicketComponent_div_100_ng_template_5_Template", "ctx_r29", "listNotes", "_c7", "ListDiagnoseTicketComponent_div_101_Template_p_button_click_1_listener", "_r84", "ctx_r83", "isShowCreateRequest", "ctx_r30", "invalid", "isValidDiagnoseNumber", "length", "isFormValid", "ListDiagnoseTicketComponent", "constructor", "ticketService", "accountService", "logHandleTicketService", "formBuilder", "injector", "oldTicket", "isShowTableNote", "titlePopup", "ngOnInit", "me", "sessionService", "USER_TYPE", "REQUEST_TYPE", "DIAGNOSE", "label", "value", "email", "columns", "name", "key", "size", "align", "isShow", "ADMIN", "isSort", "isShowTooltip", "style", "display", "max<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "funcConvertText", "utilService", "convertDateToString", "Date", "funcGetClassname", "REQUEST_STATUS", "NEW", "RECEIVED", "IN_PROGRESS", "REJECT", "DONE", "optionTable", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "action", "icon", "tooltip", "func", "item", "handleDetailRequest", "handleEditRequest", "funcAppear", "CUSTOMER", "DISTRICT", "updatedBy", "<PERSON><PERSON><PERSON><PERSON>", "PERMISSIONS", "TICKET", "UPDATE", "undefined", "pageNumber", "pageSize", "sort", "dataSet", "total", "formSearchTicket", "group", "getListProvince", "search", "getValueDate", "page", "limit", "params", "dataParams", "Object", "keys", "for<PERSON>ach", "messageCommonService", "onload", "searchTicket", "response", "totalElements", "offload", "resetTicket", "onSubmitSearch", "map", "el", "code", "province", "find", "createOrUpdateRequest", "isloading", "bodySend", "startsWith", "substring", "createTicket", "resp", "success", "getDetailTicketConfig", "resp1", "array", "info", "emailInfos", "push", "userId", "ticketId", "sendMailNotify", "listLog", "updateTicket", "fullName", "phone", "reset", "getDetailTicket", "res", "noWhitespaceValidator", "listEmail", "event", "ctrl<PERSON>ey", "keyCode", "preventDefault", "onInputSim", "regex", "test", "values", "every", "formGroup", "valid", "control", "isWhitespace", "<PERSON><PERSON><PERSON><PERSON>", "whitespace", "trimStart", "replace", "ɵɵdirectiveInject", "i1", "FormBuilder", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "ListDiagnoseTicketComponent_Template", "rf", "ctx", "ListDiagnoseTicketComponent_p_button_6_Template", "ListDiagnoseTicketComponent_Template_form_ngSubmit_7_listener", "ListDiagnoseTicketComponent_div_10_Template", "ListDiagnoseTicketComponent_Template_p_dropdown_ngModelChange_13_listener", "ListDiagnoseTicketComponent_Template_input_ngModelChange_18_listener", "ListDiagnoseTicketComponent_Template_input_ngModelChange_23_listener", "ListDiagnoseTicketComponent_Template_input_keydown_23_listener", "ListDiagnoseTicketComponent_Template_input_ngModelChange_28_listener", "ListDiagnoseTicketComponent_Template_input_keydown_28_listener", "ListDiagnoseTicketComponent_Template_p_dialog_visibleChange_35_listener", "ListDiagnoseTicketComponent_Template_form_ngSubmit_36_listener", "ListDiagnoseTicketComponent_div_38_Template", "ListDiagnoseTicketComponent_input_43_Template", "ListDiagnoseTicketComponent_span_44_Template", "ListDiagnoseTicketComponent_small_48_Template", "ListDiagnoseTicketComponent_small_49_Template", "ListDiagnoseTicketComponent_small_50_Template", "ListDiagnoseTicketComponent_input_55_Template", "ListDiagnoseTicketComponent_span_56_Template", "ListDiagnoseTicketComponent_small_60_Template", "ListDiagnoseTicketComponent_small_61_Template", "ListDiagnoseTicketComponent_small_62_Template", "ListDiagnoseTicketComponent_input_67_Template", "ListDiagnoseTicketComponent_span_68_Template", "ListDiagnoseTicketComponent_small_72_Template", "ListDiagnoseTicketComponent_small_73_Template", "ListDiagnoseTicketComponent_div_74_Template", "ListDiagnoseTicketComponent_div_75_Template", "ListDiagnoseTicketComponent_textarea_80_Template", "ListDiagnoseTicketComponent_span_81_Template", "ListDiagnoseTicketComponent_small_85_Template", "ListDiagnoseTicketComponent_textarea_90_Template", "ListDiagnoseTicketComponent_span_91_Template", "ListDiagnoseTicketComponent_small_95_Template", "ListDiagnoseTicketComponent_div_96_Template", "ListDiagnoseTicketComponent_div_97_Template", "ListDiagnoseTicketComponent_div_98_Template", "ListDiagnoseTicketComponent_div_99_Template", "ListDiagnoseTicketComponent_div_100_Template", "ListDiagnoseTicketComponent_div_101_Template", "items", "home", "ɵɵpureFunction1", "_c8", "CREATE", "bind", "ɵɵstyleMap", "_c9", "_c10", "pattern"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\ticket\\list\\diagnose\\app.list.diagnose.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\ticket\\list\\diagnose\\app.list.diagnose.component.html"], "sourcesContent": ["import {Component, Inject, Injector, OnInit} from \"@angular/core\";\r\nimport {MenuItem} from \"primeng/api\";\r\nimport {TicketService} from \"src/app/service/ticket/TicketService\";\r\nimport {ColumnInfo, OptionTable} from \"../../../common-module/table/table.component\";\r\nimport {CONSTANTS} from \"src/app/service/comon/constants\";\r\nimport {ComponentBase} from \"src/app/component.base\";\r\nimport {AccountService} from \"../../../../service/account/AccountService\";\r\nimport {\r\n    AbstractControl,\r\n    FormBuilder,\r\n    FormControl,\r\n    FormGroup,\r\n    ValidationErrors,\r\n    ValidatorFn,\r\n    Validators\r\n} from \"@angular/forms\";\r\nimport {LogHandleTicketService} from \"../../../../service/ticket/LogHandleTicketService\";\r\nimport {ComboLazyControl} from \"../../../common-module/combobox-lazyload/combobox.lazyload\";\r\n\r\n@Component({\r\n    selector: \"ticket-diagnose-list\",\r\n    templateUrl: './app.list.diagnose.component.html'\r\n})\r\nexport class ListDiagnoseTicketComponent extends ComponentBase implements OnInit {\r\n    items: MenuItem[];\r\n    home: MenuItem\r\n    searchInfo: {\r\n        provinceCode: string | null,\r\n        email: string | null,\r\n        contactPhone: string | null,\r\n        contactEmail: string | null,\r\n        type: number | null,\r\n        status: number | null,\r\n        sim: number | null,\r\n    };\r\n    columns: Array<ColumnInfo>;\r\n    dataSet: {\r\n        content: Array<any>,\r\n        total: number\r\n    };\r\n    selectItems: Array<any>;\r\n    optionTable: OptionTable;\r\n    pageNumber: number;\r\n    pageSize: number;\r\n    sort: string;\r\n    formSearchTicket: any;\r\n    listProvince: Array<any>;\r\n    listTicketType: Array<any>;\r\n    listTicketStatus: Array<any>;\r\n    mapTicketStatus: any;\r\n    listEmail: Array<any>;\r\n    isShowCreateRequest: boolean;\r\n    formTicketSim: any;\r\n    listActivatedAccount: number[];\r\n    ticket: {\r\n        id: number\r\n        contactName: string | null,\r\n        contactEmail: string | null,\r\n        contactPhone: string | null,\r\n        content: string | null,\r\n        note: string | null,\r\n        cause: string | null,\r\n        type: number | null, // 0: thay thế sim, 1: test sim\r\n        sim: any\r\n        status: number | null,\r\n        statusOld?: number | null,\r\n        assigneeId: number | null,\r\n        provinceCode: string | null,\r\n    };\r\n    maxlengthContactName: number = 255;\r\n    typeRequest: string\r\n    userInfo: any\r\n    userType: any\r\n    isValidDiagnoseNumber: boolean = false\r\n    oldTicket: any = {}\r\n    listNotes: Array<any>\r\n    isShowTableNote: boolean = false;\r\n    mapForm: any = {}\r\n    titlePopup: string = ''\r\n    controlComboSelect: ComboLazyControl = new ComboLazyControl();\r\n\r\n    constructor(\r\n        @Inject(TicketService) private ticketService: TicketService,\r\n        @Inject(AccountService) private accountService: AccountService,\r\n        @Inject(LogHandleTicketService) private logHandleTicketService: LogHandleTicketService,\r\n        private formBuilder: FormBuilder,\r\n        private injector: Injector) {\r\n        super(injector);\r\n    }\r\n\r\n    ngOnInit() {\r\n        let me = this;\r\n        this.userInfo = this.sessionService.userInfo;\r\n        this.isShowCreateRequest = false;\r\n        this.typeRequest = 'create'\r\n        this.userType = CONSTANTS.USER_TYPE;\r\n        this.listNotes = [];\r\n        this.ticket = {\r\n            id: null,\r\n            contactName: null,\r\n            contactEmail: null,\r\n            contactPhone: null,\r\n            content: null,\r\n            note: null,\r\n            cause: null,\r\n            type: CONSTANTS.REQUEST_TYPE.DIAGNOSE,\r\n            sim: null,\r\n            status: null,\r\n            statusOld: null,\r\n            assigneeId: null,\r\n            provinceCode: null,\r\n        };\r\n        this.mapTicketStatus = {\r\n            0: [{\r\n                label: me.tranService.translate('ticket.status.received'),\r\n                value: 1\r\n            },\r\n                {\r\n                    label: me.tranService.translate('ticket.status.inProgress'),\r\n                    value: 2\r\n                },\r\n                {\r\n                    label: me.tranService.translate('ticket.status.done'),\r\n                    value: 4\r\n                },\r\n                {\r\n                    label: me.tranService.translate('ticket.status.reject'),\r\n                    value: 3\r\n                },\r\n            ],\r\n            1: [\r\n                {\r\n                    label: me.tranService.translate('ticket.status.inProgress'),\r\n                    value: 2\r\n                },\r\n                {\r\n                    label: me.tranService.translate('ticket.status.done'),\r\n                    value: 4\r\n                },\r\n                {\r\n                    label: me.tranService.translate('ticket.status.reject'),\r\n                    value: 3\r\n                }\r\n            ],\r\n            2: [\r\n                {\r\n                    label: me.tranService.translate('ticket.status.done'),\r\n                    value: 4\r\n                },\r\n                {\r\n                    label: me.tranService.translate('ticket.status.reject'),\r\n                    value: 3\r\n                }\r\n            ]\r\n        }\r\n        this.listTicketStatus = [\r\n            {\r\n                label: me.tranService.translate('ticket.status.new'),\r\n                value: 0\r\n            },\r\n            {\r\n                label: me.tranService.translate('ticket.status.received'),\r\n                value: 1\r\n            },\r\n            {\r\n                label: me.tranService.translate('ticket.status.inProgress'),\r\n                value: 2\r\n            },\r\n            {\r\n                label: me.tranService.translate('ticket.status.reject'),\r\n                value: 3\r\n            },\r\n            {\r\n                label: me.tranService.translate('ticket.status.done'),\r\n                value: 4\r\n            }\r\n        ]\r\n        this.searchInfo = {\r\n            provinceCode: null,\r\n            email: null,\r\n            contactPhone: null,\r\n            contactEmail: null,\r\n            type: CONSTANTS.REQUEST_TYPE.DIAGNOSE,\r\n            status: null,\r\n            sim: null,\r\n        }\r\n        this.columns = [\r\n            {\r\n                name: this.tranService.translate(\"ticket.label.province\"),\r\n                key: \"provinceName\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: this.userInfo.type == CONSTANTS.USER_TYPE.ADMIN,\r\n                isSort: true\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ticket.diagnose.label.name\"),\r\n                key: \"contactName\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                isShowTooltip: true,\r\n                style: {\r\n                    display: 'inline-block',\r\n                    maxWidth: '350px',\r\n                    overflow: 'hidden',\r\n                    textOverflow: 'ellipsis'\r\n                }\r\n            }, {\r\n                name: this.tranService.translate(\"ticket.diagnose.label.email\"),\r\n                key: \"contactEmail\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                isShowTooltip: true,\r\n                style: {\r\n                    display: 'inline-block',\r\n                    maxWidth: '350px',\r\n                    overflow: 'hidden',\r\n                    textOverflow: 'ellipsis'\r\n                }\r\n            }, {\r\n                name: this.tranService.translate(\"ticket.diagnose.label.phone\"),\r\n                key: \"contactPhone\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ticket.diagnose.label.diagnoseNumber\"),\r\n                key: \"sim\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ticket.diagnose.label.content\"),\r\n                key: \"content\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                isShowTooltip: true,\r\n                style: {\r\n                    display: 'inline-block',\r\n                    maxWidth: '350px',\r\n                    overflow: 'hidden',\r\n                    textOverflow: 'ellipsis'\r\n                }\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ticket.label.createdDate\"),\r\n                key: \"createdDate\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcConvertText(value) {\r\n                    return me.utilService.convertDateToString(new Date(value))\r\n                },\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ticket.label.updatedDate\"),\r\n                key: \"updatedDate\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcConvertText(value) {\r\n                    return value ? me.utilService.convertDateToString(new Date(value)) : \"\"\r\n                }\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ticket.label.updateBy\"),\r\n                key: \"updatedByName\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                isShowTooltip: true,\r\n                style: {\r\n                    display: 'inline-block',\r\n                    maxWidth: '350px',\r\n                    overflow: 'hidden',\r\n                    textOverflow: 'ellipsis'\r\n                }\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ticket.label.status\"),\r\n                key: \"status\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcGetClassname: (value) => {\r\n                    if (value == CONSTANTS.REQUEST_STATUS.NEW) {\r\n                        return ['p-2', 'text-white', \"bg-cyan-300\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\r\n                        return ['p-2', 'text-white', \"bg-bluegray-500\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\r\n                        return ['p-2', 'text-white', \"bg-orange-400\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\r\n                        return ['p-2', 'text-white', \"bg-red-500\", \"border-round\", \"inline-block\"];\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\r\n                        return ['p-2', 'text-white', \"bg-green-500\", \"border-round\", \"inline-block\"];\r\n                    }\r\n                    return '';\r\n                },\r\n                funcConvertText: function (value) {\r\n                    if (value == CONSTANTS.REQUEST_STATUS.NEW) {\r\n                        return me.tranService.translate(\"ticket.status.new\");\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\r\n                        return me.tranService.translate(\"ticket.status.received\");\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\r\n                        return me.tranService.translate(\"ticket.status.inProgress\");\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\r\n                        return me.tranService.translate(\"ticket.status.reject\");\r\n                    } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\r\n                        return me.tranService.translate(\"ticket.status.done\");\r\n                    }\r\n                    return \"\";\r\n                }\r\n            }\r\n        ];\r\n\r\n        this.optionTable = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n            action: [\r\n                {\r\n                    icon: \"pi pi-info-circle\",\r\n                    tooltip: this.tranService.translate(\"global.button.view\"),\r\n                    func: function (id, item) {\r\n                        me.handleDetailRequest(id, item)\r\n                    }\r\n                },\r\n                {\r\n                    icon: \"pi pi-window-maximize\",\r\n                    tooltip: this.tranService.translate(\"global.button.edit\"),\r\n                    func: function (id, item) {\r\n                        me.handleEditRequest(id, item)\r\n                    },\r\n                    funcAppear: function (id, item) {\r\n                        //admin + kh + gdv không câ nhật\r\n                        if (me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER || me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) {\r\n                            return false; }\r\n                        // chưa cap nhat + chua gan + co quyen thi cap nhat\r\n                        if (!item.updatedBy && !item.assigneeId && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;\r\n                        //da duoc cap nhat boi tinh khac thi khong cap nhat\r\n                        if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy!= undefined &&  item.updatedBy != null && item.updatedBy !== me.userInfo.id) {\r\n                            return false;\r\n                        }\r\n                        //la acc tinh co quyen cap nhat\r\n                        if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true\r\n                        else {\r\n                            return false;\r\n                        }\r\n                    }\r\n                }\r\n            ]\r\n        }\r\n        this.pageNumber = 0;\r\n        this.pageSize = 10;\r\n        this.sort = \"status,asc\";\r\n        this.dataSet = {\r\n            content: [],\r\n            total: 0\r\n        }\r\n        this.formSearchTicket = this.formBuilder.group(this.searchInfo);\r\n        this.formTicketSim = this.formBuilder.group(this.ticket);\r\n        this.getListProvince();\r\n        this.listActivatedAccount = [];\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n    }\r\n\r\n    getValueStatus(value) {\r\n        let me = this;\r\n        {\r\n            if (value == CONSTANTS.REQUEST_STATUS.NEW) {\r\n                return me.tranService.translate(\"ticket.status.new\");\r\n            } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\r\n                return me.tranService.translate(\"ticket.status.received\");\r\n            } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\r\n                return me.tranService.translate(\"ticket.status.inProgress\");\r\n            } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\r\n                return me.tranService.translate(\"ticket.status.reject\");\r\n            } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\r\n                return me.tranService.translate(\"ticket.status.done\");\r\n            }\r\n            return \"\";\r\n        }\r\n    }\r\n\r\n    getValueDate(value) {\r\n        let me = this;\r\n        // console.log(value)\r\n        return me.utilService.convertDateToString(new Date(value))\r\n    }\r\n\r\n    search(page, limit, sort, params) {\r\n        let me = this;\r\n        this.pageNumber = page;\r\n        this.pageSize = limit;\r\n        this.sort = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        Object.keys(this.searchInfo).forEach(key => {\r\n            if (this.searchInfo[key] != null) {\r\n                dataParams[key] = this.searchInfo[key];\r\n            }\r\n        })\r\n        this.dataSet = {\r\n            content: [],\r\n            total: 0\r\n        }\r\n        me.messageCommonService.onload();\r\n        this.ticketService.searchTicket(dataParams, (response) => {\r\n            me.dataSet = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n            // if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE ||\r\n            //     me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) {\r\n            //     let listAssigneeId = Array.from(new Set(me.dataSet.content.filter(item => item.assigneeId !== null)\r\n            //         .map(item => item.assigneeId as number)));\r\n            //\r\n            //     me.dataSet.content.forEach(item => {\r\n            //         if (item.updateBy !== null) {\r\n            //             listAssigneeId.push(item.updateBy as number);\r\n            //         }\r\n            //     });\r\n            //\r\n            //     const statusCheckListId = Array.from(new Set(listAssigneeId));\r\n            //\r\n            //     me.accountService.getListActivatedAccount(statusCheckListId, (response) => {\r\n            //         me.listActivatedAccount = response;\r\n            //         this.optionTable = {\r\n            //             hasClearSelected: false,\r\n            //             hasShowChoose: false,\r\n            //             hasShowIndex: true,\r\n            //             hasShowToggleColumn: false,\r\n            //             action: [\r\n            //                 {\r\n            //                     icon: \"pi pi-info-circle\",\r\n            //                     tooltip: this.tranService.translate(\"global.button.view\"),\r\n            //                     func: function (id, item) {\r\n            //                         me.handleDetailRequest(id, item)\r\n            //                     }\r\n            //                 },\r\n            //                 {\r\n            //                     icon: \"pi pi-window-maximize\",\r\n            //                     tooltip: this.tranService.translate(\"global.button.edit\"),\r\n            //                     func: function (id, item) {\r\n            //                         me.handleEditRequest(id, item)\r\n            //                     },\r\n            //                     funcAppear: function (id, item) {\r\n            //                         //admin + kh + gdv không câ nhật\r\n            //                         if (me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER || me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) {\r\n            //                             return false; }\r\n            //                         // chưa cap nhat + chua gan + co quyen thi cap nhat\r\n            //                         if (!item.updatedBy && !item.assigneeId && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;\r\n            //                         //da duoc cap nhat boi tinh khac thi khong cap nhat\r\n            //                         if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy!= undefined &&  item.updatedBy != null && item.updatedBy !== me.userInfo.id) {\r\n            //                             return false;\r\n            //                         }\r\n            //                         //la acc tinh co quyen cap nhat\r\n            //                         if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true\r\n            //                         else {\r\n            //                             return false;\r\n            //                         }\r\n            //                     }\r\n            //                 }\r\n            //             ]\r\n            //         }\r\n            //         me.changeTable = true;\r\n            //     })\r\n            // }\r\n        }, null, () => {\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    resetTicket() {\r\n        this.ticket = {\r\n            id: null,\r\n            contactName: null,\r\n            contactEmail: null,\r\n            contactPhone: null,\r\n            content: null,\r\n            note: null,\r\n            cause: null,\r\n            type: CONSTANTS.REQUEST_TYPE.DIAGNOSE,\r\n            sim: null,\r\n            status: null,\r\n            statusOld: null,\r\n            assigneeId: null,\r\n            provinceCode: null,\r\n        };\r\n        this.isValidDiagnoseNumber = false;\r\n    }\r\n\r\n    onSubmitSearch() {\r\n        this.pageNumber = 0;\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n    }\r\n\r\n    getListProvince() {\r\n        this.accountService.getListProvince((response) => {\r\n            this.listProvince = response.map(el => {\r\n                return {\r\n                    ...el,\r\n                    display: `${el.code} - ${el.name}`\r\n                }\r\n            })\r\n        })\r\n    }\r\n\r\n    getProvinceName(provinceCode) {\r\n        const province = this.listProvince.find(el => el.code === provinceCode);\r\n        return province ? province.code + ' - ' + province.name : \"\";\r\n    }\r\n\r\n    // tạo sửa yêu cầu\r\n    createOrUpdateRequest() {\r\n        if (this.messageCommonService.isloading == true || this.isShowCreateRequest == false) return;\r\n        let me = this;\r\n        this.messageCommonService.onload()\r\n        if (this.typeRequest == 'create') {\r\n            let bodySend = {\r\n                contactName: this.ticket.contactName,\r\n                contactEmail: this.ticket.contactEmail,\r\n                contactPhone: this.ticket.contactPhone,\r\n                content: this.ticket.content,\r\n                note: this.ticket.note,\r\n                type: this.ticket.type,\r\n                sim: this.ticket.type == CONSTANTS.REQUEST_TYPE.DIAGNOSE ? this.ticket.sim : null\r\n            }\r\n            if (bodySend.contactPhone != null) {\r\n                if (bodySend.contactPhone.startsWith('0')) {\r\n                    bodySend.contactPhone = \"84\" + bodySend.contactPhone.substring(1, bodySend.contactPhone.length);\r\n                } else if (bodySend.contactPhone.length == 9 || bodySend.contactPhone.length == 10) {\r\n                    bodySend.contactPhone = \"84\" + bodySend.contactPhone;\r\n                }\r\n            }\r\n            this.ticketService.createTicket(bodySend, (resp) => {\r\n                me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n                me.isShowCreateRequest = false\r\n                me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)\r\n                // nếu KH đc gán cho GDV thì gửi mail cho GDV và danh sách admin đc cấu hình không thì chỉ gửi mail cho danh sách admin đc cấu hình\r\n                // get mail admin tinh dc cau hinh\r\n                me.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp1) => {\r\n                    let array = []\r\n                    for (let info of resp1.emailInfos) {\r\n                        array.push({\r\n                            userId: info.userId,\r\n                            ticketId: resp.id\r\n                        })\r\n                    }\r\n                    if (resp?.assigneeId) {\r\n                        array.push({\r\n                            userId: resp.assigneeId,\r\n                            ticketId: resp.id\r\n                        })\r\n                    }\r\n                    me.ticketService.sendMailNotify(array);\r\n                })\r\n            }, null, () => {\r\n                me.messageCommonService.offload()\r\n            })\r\n        } else if (this.typeRequest == 'update') {\r\n            let bodySend = {\r\n                contactName: this.ticket.contactName,\r\n                contactEmail: this.ticket.contactEmail,\r\n                contactPhone: this.ticket.contactPhone,\r\n                content: this.ticket.content,\r\n                note: this.ticket.note,\r\n                type: this.ticket.type,\r\n                sim: this.ticket.type == CONSTANTS.REQUEST_TYPE.DIAGNOSE ? this.ticket.sim : null,\r\n                status: this.ticket.status,\r\n                cause: this.ticket.cause,\r\n                assigneeId: this.ticket.assigneeId,\r\n                listLog: this.listNotes\r\n            }\r\n            if (bodySend.contactPhone != null) {\r\n                if (bodySend.contactPhone.startsWith('0')) {\r\n                    bodySend.contactPhone = \"84\" + bodySend.contactPhone.substring(1, bodySend.contactPhone.length);\r\n                } else if (bodySend.contactPhone.length == 9 || bodySend.contactPhone.length == 10) {\r\n                    bodySend.contactPhone = \"84\" + bodySend.contactPhone;\r\n                }\r\n            }\r\n            // update ticket\r\n            this.ticketService.updateTicket(this.ticket.id, bodySend, (resp) => {\r\n                me.isShowCreateRequest = false\r\n                me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n                me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)\r\n                if (resp.assigneeId != null && resp.assigneeId != undefined) {\r\n                    me.ticketService.sendMailNotify([{\r\n                        userId: resp.assigneeId,\r\n                        ticketId: resp.id\r\n                    }])\r\n                }\r\n\r\n            }, null, () => {\r\n                me.messageCommonService.offload()\r\n            })\r\n        }\r\n    }\r\n\r\n    showModalCreate() {\r\n        this.isShowCreateRequest = true\r\n        this.typeRequest = 'create'\r\n        this.titlePopup = this.tranService.translate('ticket.label.createRequest')\r\n        this.resetTicket()\r\n        // auto fill thong tin khi tao\r\n        if (this.userInfo.type === CONSTANTS.USER_TYPE.CUSTOMER) {\r\n            this.ticket.contactName = this.userInfo.fullName.substring(0, this.maxlengthContactName);\r\n            this.ticket.contactPhone = this.userInfo.phone;\r\n            this.ticket.contactEmail = this.userInfo.email;\r\n        }\r\n        this.formTicketSim = this.formBuilder.group(this.ticket)\r\n    }\r\n\r\n    handleEditRequest(id, item) {\r\n        let me = this\r\n        this.formTicketSim.reset()\r\n        this.titlePopup = this.tranService.translate('ticket.label.updateRequest')\r\n        this.typeRequest = 'update'\r\n        this.isShowCreateRequest = true;\r\n        this.ticketService.getDetailTicket(item.id, (resp) => {\r\n            me.ticket = {\r\n                id: resp.id,\r\n                contactName: resp.contactName,\r\n                contactEmail: resp.contactEmail,\r\n                contactPhone: resp.contactPhone,\r\n                content: resp.content,\r\n                note: resp.note,\r\n                cause: resp.cause,\r\n                type: resp.type, // 0: thay thế sim, 1: test sim\r\n                sim: resp.sim,\r\n                status: null,\r\n                statusOld: resp.status,\r\n                assigneeId: resp.assigneeId,\r\n                provinceCode: resp.provinceCode,\r\n            }\r\n            me.oldTicket = {...me.ticket}\r\n            me.formTicketSim = me.formBuilder.group(me.ticket)\r\n            // lấy list note\r\n            this.logHandleTicketService.search({ticketId: item.id}, (res) => {\r\n                // console.log(res.content)\r\n                this.listNotes = res.content;\r\n                // for(let note of this.listNotes) {\r\n                //   this.mapForm[note.id] = this.formBuilder.group(note);\r\n                // }\r\n                this.listNotes.forEach(note => {\r\n                    this.mapForm[note.id] = this.formBuilder.group({\r\n                        content: ['', [Validators.required, Validators.maxLength(255), this.noWhitespaceValidator()]]\r\n                    });\r\n                });\r\n                me.isShowTableNote = true;\r\n            })\r\n        })\r\n        this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp) => {\r\n            me.listEmail = resp.emailInfos;\r\n        })\r\n    }\r\n\r\n    handleDetailRequest(id, item) {\r\n        let me = this\r\n        this.formTicketSim.reset()\r\n        this.titlePopup = this.tranService.translate('ticket.label.viewDetailDiagnose')\r\n        this.typeRequest = 'detail'\r\n        this.isShowCreateRequest = true;\r\n        this.ticketService.getDetailTicket(item.id, (resp) => {\r\n            me.ticket = {\r\n                id: resp.id,\r\n                contactName: resp.contactName,\r\n                contactEmail: resp.contactEmail,\r\n                contactPhone: resp.contactPhone,\r\n                content: resp.content,\r\n                note: resp.note,\r\n                cause: resp.cause,\r\n                type: resp.type, // 0: thay thế sim, 1: test sim\r\n                sim: resp.sim,\r\n                status: null,\r\n                statusOld: resp.status,\r\n                assigneeId: resp.assigneeId,\r\n                provinceCode: resp.provinceCode,\r\n\r\n            }\r\n            me.oldTicket = {...me.ticket}\r\n            me.formTicketSim = me.formBuilder.group(me.ticket)\r\n            // lấy list note\r\n            this.logHandleTicketService.search({ticketId: item.id}, (res) => {\r\n                // console.log(res.content)\r\n                this.listNotes = res.content;\r\n                for (let note of this.listNotes) {\r\n                    this.mapForm[note.id] = this.formBuilder.group(note);\r\n                }\r\n                me.isShowTableNote = true;\r\n            })\r\n        })\r\n        this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp) => {\r\n            me.listEmail = resp.emailInfos;\r\n        })\r\n    }\r\n\r\n    preventCharacter(event) {\r\n        if (event.ctrlKey) {\r\n            return;\r\n        }\r\n        if (event.keyCode == 8 || event.keyCode == 13 || event.keyCode == 37 || event.keyCode == 39) {\r\n            return;\r\n        }\r\n        if (event.keyCode < 48 || event.keyCode > 57) {\r\n            event.preventDefault();\r\n        }\r\n        // Chặn ký tự 'e', 'E' và dấu '+'\r\n        if (event.keyCode == 69 || event.keyCode == 101 || event.keyCode == 107 || event.keyCode == 187) {\r\n            event.preventDefault();\r\n        }\r\n    }\r\n\r\n    onInputSim(sim) {\r\n        let regex = /^84[0-9]{9,10}$/;\r\n        // console.log(regex.test(sim))\r\n        if (!regex.test(sim)) {\r\n            this.isValidDiagnoseNumber = false\r\n            return\r\n        } else {\r\n            this.isValidDiagnoseNumber = true\r\n        }\r\n    }\r\n\r\n    isFormValid() {\r\n        return Object.values(this.mapForm).every((formGroup: FormGroup) => formGroup.valid);\r\n    }\r\n\r\n    noWhitespaceValidator(): ValidatorFn {\r\n        return (control: AbstractControl): ValidationErrors | null => {\r\n            const isWhitespace = (control.value || '').trim().length === 0;\r\n            const isValid = !isWhitespace;\r\n            return isValid ? null : {whitespace: true};\r\n        }\r\n    };\r\n\r\n    onKeyDownNote(event): void {\r\n        if (event.key === ' ' && (this.ticket.note == null || this.ticket.note != null && this.ticket.note.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (this.ticket.note != null && this.ticket.note.trim() != '') {\r\n            this.ticket.note = this.ticket.note.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n\r\n    onKeyDownContent(event) {\r\n        if (event.key === ' ' && (this.ticket.content == null || this.ticket.content != null && this.ticket.content.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (this.ticket.content != null && this.ticket.content.trim() != '') {\r\n            this.ticket.content = this.ticket.content.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n\r\n    onKeyDownNoteContent(event: KeyboardEvent, note: any): void {\r\n        if (event.key === ' ' && (!note.content || note.content.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (note.content && note.content.trim() !== '') {\r\n            note.content = note.content.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n\r\n    onKeyDownCause(event) {\r\n        if (event.key === ' ' && (this.ticket.cause == null || this.ticket.cause != null && this.ticket.cause.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (this.ticket.cause != null && this.ticket.cause.trim() != '') {\r\n            this.ticket.cause = this.ticket.cause.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n\r\n    changeDiagnoseNumber($event) {\r\n        if ($event !== null && $event !== undefined) {\r\n            this.onInputSim($event);\r\n            if (this.isValidDiagnoseNumber) {\r\n                this.ticket.sim = $event\r\n            }\r\n        } else {\r\n            this.isValidDiagnoseNumber = false;\r\n        }\r\n    }\r\n\r\n    protected readonly CONSTANTS = CONSTANTS;\r\n}\r\n", "<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{ tranService.translate(\"ticket.menu.diagnose\") }}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"col-5 flex flex-row justify-content-end align-items-center\">\r\n        <p-button styleClass=\"p-button-info\"\r\n                  *ngIf=\"userInfo.type == userType.CUSTOMER && checkAuthen([CONSTANTS.PERMISSIONS.TICKET.CREATE])\"\r\n                  [label]=\"tranService.translate('global.button.create')\"\r\n                  (click)=\"showModalCreate()\" icon=\"\">\r\n        </p-button>\r\n    </div>\r\n</div>\r\n\r\n<form [formGroup]=\"formSearchTicket\" (ngSubmit)=\"onSubmitSearch()\" class=\"pt-3 pb-2 vnpt-field-set\">\r\n    <p-panel [toggleable]=\"true\" [header]=\"tranService.translate('global.text.filter')\">\r\n        <div class=\"grid search-grid-4\">\r\n            <!-- ma tinh -->\r\n            <div *ngIf=\"this.userInfo.type == this.userType.ADMIN\" class=\"col-2 col-4\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\"\r\n                                [showClear]=\"true\" [filter]=\"true\" filterBy=\"display\"\r\n                                id=\"provinceCode\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"searchInfo.provinceCode\"\r\n                                [required]=\"false\"\r\n                                formControlName=\"provinceCode\"\r\n                                [options]=\"listProvince\"\r\n                                optionLabel=\"display\"\r\n                                optionValue=\"code\"\r\n                    ></p-dropdown>\r\n                    <label htmlFor=\"provinceCode\">{{ tranService.translate(\"account.label.province\") }}</label>\r\n                </span>\r\n            </div>\r\n            <!-- trạng thái ticket -->\r\n            <div class=\"col-2\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\"\r\n                                [showClear]=\"true\" [filter]=\"true\" filterBy=\"display\"\r\n                                id=\"provinceCode\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"searchInfo.status\"\r\n                                [required]=\"false\"\r\n                                formControlName=\"status\"\r\n                                [options]=\"listTicketStatus\"\r\n                                optionLabel=\"label\"\r\n                                optionValue=\"value\"\r\n                                [filter]=true\r\n                                filterBy=\"label\"\r\n                    ></p-dropdown>\r\n                    <label htmlFor=\"status\">{{ tranService.translate(\"ticket.label.status\") }}</label>\r\n                </span>\r\n            </div>\r\n            <!-- email -->\r\n            <div class=\"col-2\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                           pInputText id=\"contactEmail\"\r\n                           [(ngModel)]=\"searchInfo.contactEmail\"\r\n                           formControlName=\"contactEmail\"\r\n                    />\r\n                    <label htmlFor=\"email\">{{ tranService.translate(\"ticket.diagnose.label.email\") }}</label>\r\n                </span>\r\n            </div>\r\n            <!-- phone -->\r\n            <div class=\"col-2\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                           pInputText id=\"contactPhone\"\r\n                           [(ngModel)]=\"searchInfo.contactPhone\"\r\n                           formControlName=\"contactPhone\"\r\n                           type=\"number\"\r\n                           (keydown)=\"preventCharacter($event)\"\r\n                           min=\"0\"\r\n                    />\r\n                    <label class=\"label-phone\" htmlFor=\"contactPhone\">{{ tranService.translate(\"ticket.diagnose.label.phone\") }}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-2\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                           pInputText id=\"sim\"\r\n                           [(ngModel)]=\"searchInfo.sim\"\r\n                           formControlName=\"sim\"\r\n                           type=\"number\"\r\n                           (keydown)=\"preventCharacter($event)\"\r\n                           min=\"0\"\r\n                    />\r\n                    <label class=\"label-phone\" htmlFor=\"sim\" >{{ tranService.translate(\"ticket.diagnose.label.diagnoseNumber\") }}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-2 pb-0\">\r\n                <p-button icon=\"pi pi-search\"\r\n                          styleClass=\"p-button-rounded p-button-secondary p-button-text button-search\"\r\n                          type=\"submit\"\r\n                ></p-button>\r\n            </div>\r\n        </div>\r\n    </p-panel>\r\n</form>\r\n\r\n<table-vnpt\r\n    [tableId]=\"'tableTicketConfigList'\"\r\n    [fieldId]=\"'provinceCode'\"\r\n    [columns]=\"columns\"\r\n    [dataSet]=\"dataSet\"\r\n    [options]=\"optionTable\"\r\n    [pageNumber]=\"pageNumber\"\r\n    [loadData]=\"search.bind(this)\"\r\n    [pageSize]=\"pageSize\"\r\n    [sort]=\"sort\"\r\n    [params]=\"searchInfo\"\r\n    [labelTable]=\"tranService.translate('ticket.menu.requestList')\"\r\n></table-vnpt>\r\n<!--    dialog tạo sửa yêu cầu-->\r\n<div class=\"flex justify-content-center dialog-vnpt\">\r\n    <p-dialog [breakpoints]=\"{ '1199px': '75vw', '575px': '90vw' }\" [header]=\"titlePopup\"\r\n              [(visible)]=\"isShowCreateRequest\" [modal]=\"true\"\r\n              [style]=\"{ width: '800px', overflowY :'scroll', maxHeight : '80%' }\" [draggable]=\"false\"\r\n              [resizable]=\"false\">\r\n        <form class=\"mt-3\" [formGroup]=\"formTicketSim\" (ngSubmit)=\"createOrUpdateRequest()\">\r\n            <div class=\"flex flex-row flex-wrap justify-content-between w-full\">\r\n                <div class=\"w-full field grid chart-grid\"\r\n                     *ngIf=\"this.userInfo.type == this.userType.ADMIN && typeRequest == 'detail'\">\r\n                    <label htmlFor=\"contactName\" class=\"col-fixed\"\r\n                           style=\"width:180px\">{{ tranService.translate(\"account.label.province\") }}</label>\r\n                    <div class=\"col\">\r\n                        <span>{{ getProvinceName(ticket.provinceCode) }}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- contactName -->\r\n                <div class=\"w-full field grid chart-grid\">\r\n                    <label htmlFor=\"contactName\" class=\"col-fixed\"\r\n                           style=\"width:180px;height: fit-content\">{{ tranService.translate(\"ticket.diagnose.label.name\") }}\r\n                       </label>\r\n                    <div class=\"col\" style=\"width: calc(100% - 180px);overflow-wrap: break-word\">\r\n                        <input *ngIf=\"typeRequest == 'create'\" class=\"w-full\"\r\n                               pInputText id=\"contactName\"\r\n                               [(ngModel)]=\"ticket.contactName\"\r\n                               formControlName=\"contactName\"\r\n                               [required]=\"true\"\r\n                               [maxLength]=\"maxlengthContactName\"\r\n                               pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$\"\r\n                               [placeholder]=\"tranService.translate('account.text.inputFullname')\"\r\n                               [readonly]=\"true\"\r\n                        />\r\n                        <span *ngIf=\"typeRequest == 'detail' || typeRequest == 'update'\">{{ ticket.contactName }}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- error fullname -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"fullName\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.contactName.dirty && formTicketSim.controls.contactName.errors?.required\">{{ tranService.translate(\"global.message.required\") }}</small>\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.contactName.errors?.maxLength\">{{ tranService.translate(\"global.message.maxLength\", {len: 255}) }}</small>\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.contactName.errors?.pattern\">{{ tranService.translate(\"global.message.formatContainVN\") }}</small>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- email -->\r\n                <div class=\"w-full field grid chart-grid\">\r\n                    <label htmlFor=\"email\" class=\"col-fixed\"\r\n                           style=\"width:180px;height: fit-content\">{{ tranService.translate(\"ticket.diagnose.label.email\") }}\r\n                        </label>\r\n                    <div class=\"col\" style=\"width: calc(100% - 180px);overflow-wrap: break-word\">\r\n                        <input *ngIf=\"typeRequest == 'create'\" class=\"w-full\"\r\n                               pInputText id=\"contactEmail\"\r\n                               [(ngModel)]=\"ticket.contactEmail\"\r\n                               formControlName=\"contactEmail\"\r\n                               [required]=\"true\"\r\n                               [maxLength]=\"50\"\r\n                               [readonly]=\"true\"\r\n                               pattern=\"^[a-z0-9]+[a-z0-9\\-\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\-\\._]*[a-z0-9]+)+(\\.[a-z]{2,})$\"\r\n                               [placeholder]=\"tranService.translate('account.text.inputEmail')\"\r\n                        />\r\n                        <span *ngIf=\"typeRequest == 'detail' || typeRequest == 'update'\">{{ ticket.contactEmail }}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- error email -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"email\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.contactEmail.dirty && formTicketSim.controls.contactEmail.errors?.required\">{{ tranService.translate(\"global.message.required\") }}</small>\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.contactEmail.errors?.maxLength\">{{ tranService.translate(\"global.message.maxLength\", {len: 255}) }}</small>\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.contactEmail.errors?.pattern\">{{ tranService.translate(\"global.message.invalidEmail\") }}</small>\r\n                        <!--                            <small class=\"text-red-500\" *ngIf=\"isEmailExisted\">{{tranService.translate(\"global.message.exists\",{type: tranService.translate(\"account.label.email\").toLowerCase()})}}</small> -->\r\n                    </div>\r\n                </div>\r\n                <!-- phone -->\r\n                <div class=\"w-full field grid chart-grid\">\r\n                    <label htmlFor=\"phone\" class=\"col-fixed\"\r\n                           style=\"width:180px\">{{ tranService.translate(\"ticket.diagnose.label.phone\") }}</label>\r\n                    <div class=\"col\">\r\n                        <input *ngIf=\"typeRequest == 'create'\" class=\"w-full\"\r\n                               pInputText id=\"contactPhone\"\r\n                               [(ngModel)]=\"ticket.contactPhone\"\r\n                               formControlName=\"contactPhone\"\r\n                               [required]=\"true\"\r\n                               [maxLength]=\"11\"\r\n                               [readonly]=\"true\"\r\n                               pattern=\"^((\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\"\r\n                               (keydown)=\"preventCharacter($event)\"\r\n                               [placeholder]=\"tranService.translate('account.text.inputPhone')\"\r\n                        />\r\n                        <span *ngIf=\"typeRequest == 'detail' || typeRequest == 'update'\">{{ ticket.contactPhone }}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- error phone -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"phone\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.contactPhone.dirty && formTicketSim.controls.contactPhone.errors?.required\">{{ tranService.translate(\"global.message.required\") }}</small>\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.contactPhone.errors?.pattern\">{{ tranService.translate(\"ticket.message.invalidPhone\") }}</small>\r\n                    </div>\r\n                </div>\r\n                <!-- diagnose sim -->\r\n                <div *ngIf=\"ticket.type == CONSTANTS.REQUEST_TYPE.DIAGNOSE\" class=\"w-full field grid chart-grid\">\r\n                    <label htmlFor=\"diagnoseNumber\" class=\"col-fixed\"\r\n                           style=\"width:180px;display:inline-block\">{{ tranService.translate(\"ticket.diagnose.label.number\") }}\r\n                        <span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\">\r\n                        <vnpt-select\r\n                            *ngIf=\"typeRequest=='create'\"\r\n                            class=\"w-full\"\r\n                            [control]=\"controlComboSelect\"\r\n                            [(value)]=\"ticket.sim\"\r\n                            [placeholder]=\"tranService.translate('ticket.diagnose.label.number')\"\r\n                            objectKey=\"sim\"\r\n                            paramKey=\"msisdn\"\r\n                            keyReturn=\"msisdn\"\r\n                            displayPattern=\"${msisdn}\"\r\n                            typeValue=\"primitive\"\r\n                            [isMultiChoice]=\"false\"\r\n                            [required]=\"true\"\r\n                            [paramDefault]=\"{}\"\r\n                            (onchange)=\"changeDiagnoseNumber($event)\"\r\n                        ></vnpt-select>\r\n                        <span style=\"word-break: break-all\"\r\n                              *ngIf=\"typeRequest=='update' || typeRequest == 'detail'\">{{ ticket.sim }}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- error diagnose sim  -->\r\n                <div *ngIf=\"ticket.type == CONSTANTS.REQUEST_TYPE.DIAGNOSE && typeRequest =='create'\"\r\n                     class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"diagnoseNumber\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500 block\"\r\n                               *ngIf=\"controlComboSelect.dirty && controlComboSelect.error.required\">\r\n                            {{ tranService.translate(\"global.message.required\") }}\r\n                        </small>\r\n\r\n                        <!--                        <small class=\"text-red-500 block\" *ngIf=\"!isValidDiagnoseNumber\">{{tranService.translate(\"ticket.message.invalidPhone\")}}</small>-->\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- content-->\r\n                <div class=\"w-full field grid chart-grid\">\r\n                    <label htmlFor=\"content\" class=\"col-fixed\"\r\n                           style=\"width:180px;height: fit-content;\">{{ tranService.translate(\"ticket.diagnose.label.content\") }}</label>\r\n                    <div class=\"col\">\r\n                            <textarea *ngIf=\"typeRequest=='create'\" class=\"w-full\" style=\"resize: none;\"\r\n                                      rows=\"5\"\r\n                                      [autoResize]=\"false\"\r\n                                      pInputTextarea id=\"content\"\r\n                                      [(ngModel)]=\"ticket.content\"\r\n                                      formControlName=\"content\"\r\n                                      [maxlength]=\"255\"\r\n                                      [placeholder]=\"tranService.translate('ticket.diagnose.label.content')\"\r\n                                      (keydown)=\"onKeyDownContent($event)\"\r\n                            ></textarea>\r\n                        <span style=\"word-break: break-all\"\r\n                              *ngIf=\"typeRequest=='update' || typeRequest == 'detail'\">{{ ticket.content }}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- error content -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"content\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.content.errors?.maxLength\">{{ tranService.translate(\"global.message.maxLength\", {len: 255}) }}</small>\r\n                    </div>\r\n                </div>\r\n                <!-- note-->\r\n                <div class=\"w-full field grid chart-grid\">\r\n                    <label htmlFor=\"note\" class=\"col-fixed\"\r\n                           style=\"width:180px;height: fit-content;\">{{ tranService.translate(\"ticket.label.note\") }}</label>\r\n                    <div class=\"col\">\r\n                            <textarea *ngIf=\"typeRequest=='create'\" class=\"w-full\" style=\"resize: none;\"\r\n                                      rows=\"5\"\r\n                                      [autoResize]=\"false\"\r\n                                      pInputTextarea id=\"note\"\r\n                                      [(ngModel)]=\"ticket.note\"\r\n                                      formControlName=\"note\"\r\n                                      [maxlength]=\"255\"\r\n                                      [placeholder]=\"tranService.translate('ticket.label.note')\"\r\n                                      (keydown)=\"onKeyDownNote($event)\"\r\n                            ></textarea>\r\n                        <span style=\"word-break: break-all\"\r\n                              *ngIf=\"typeRequest=='update' || typeRequest == 'detail'\">{{ ticket.note }}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- error note -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"note\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.note.errors?.maxLength\">{{ tranService.translate(\"global.message.maxLength\", {len: 255}) }}</small>\r\n                    </div>\r\n                </div>\r\n                <!-- chuyen xu ly-->\r\n<!--                <div-->\r\n<!--                    *ngIf=\"(typeRequest=='update' && userInfo.type == userType.PROVINCE && ticket.status == null && ticket.statusOld == 0) || (userInfo.type == userType.PROVINCE && typeRequest == 'detail' && ticket.assigneeId != null)\"-->\r\n<!--                    class=\"w-full field grid\">-->\r\n<!--                    <label for=\"assignee\" class=\"col-fixed\"-->\r\n<!--                           style=\"width:180px\">{{ tranService.translate(\"ticket.label.transferProcessing\") }}</label>-->\r\n<!--                    <div class=\"col\" style=\"max-width: calc(100% - 180px); position: relative\">-->\r\n<!--                        <vnpt-select-->\r\n<!--                            class=\"w-full\"-->\r\n<!--                            id=\"assignee\"-->\r\n<!--                            [(value)]=\"ticket.assigneeId\"-->\r\n<!--                            [placeholder]=\"tranService.translate('ticket.label.transferProcessing')\"-->\r\n<!--                            [disabled]=\"ticket.assigneeId != null && typeRequest == 'detail'\"-->\r\n<!--                            objectKey=\"account\"-->\r\n<!--                            paramKey=\"email\"-->\r\n<!--                            keyReturn=\"id\"-->\r\n<!--                            displayPattern=\"${email}\"-->\r\n<!--                            [isMultiChoice]=\"false\"-->\r\n<!--                            [paramDefault]=\"{type : 3}\"-->\r\n<!--                            [stylePositionBoxSelect]=\"{bottom: '40px', left: '12px', 'min-width':'calc(100% - 20px)'}\"-->\r\n<!--                        ></vnpt-select>-->\r\n<!--                    </div>-->\r\n<!--                </div>-->\r\n<!--                &lt;!&ndash; error chuyen xu ly&ndash;&gt;-->\r\n<!--                <div-->\r\n<!--                    *ngIf=\"(typeRequest=='update' && userInfo.type == userType.PROVINCE && ticket.status == null && ticket.statusOld == 0) || typeRequest == 'detail'\"-->\r\n<!--                    class=\"w-full field grid text-error-field\">-->\r\n<!--                    <label htmlFor=\"userType\" class=\"col-fixed\" style=\"width:180px\"></label>-->\r\n<!--                    <div class=\"col\">-->\r\n<!--                        <small class=\"text-red-500\"-->\r\n<!--                               *ngIf=\"formTicketSim.controls.assigneeId.dirty && formTicketSim.controls.assigneeId.errors?.required\">{{ tranService.translate(\"global.message.required\") }}</small>-->\r\n<!--                    </div>-->\r\n<!--                </div>-->\r\n                <!-- trang thai-->\r\n                <div *ngIf=\"typeRequest=='update' || typeRequest == 'detail'\"\r\n                     [class]=\"(userInfo.type == userType.PROVINCE && typeRequest != 'detail') ? (ticket.assigneeId == null || (ticket.assigneeId != null && !listActivatedAccount.includes(ticket.assigneeId))? '': 'hidden'): ''\"\r\n                     class=\"w-full field grid chart-grid\">\r\n                    <label for=\"status\" class=\"col-fixed\"\r\n                           style=\"width:180px\">{{ tranService.translate(\"ticket.label.status\") }}</label>\r\n                    <div class=\"col\">\r\n                        <p-dropdown *ngIf=\"typeRequest=='update'\" styleClass=\"w-full\"\r\n                                    [showClear]=\"true\"\r\n                                    id=\"status\" [autoDisplayFirst]=\"true\"\r\n                                    [(ngModel)]=\"ticket.status\"\r\n                                    formControlName=\"status\"\r\n                                    [options]=\"ticket.statusOld !== null ? mapTicketStatus[ticket.statusOld] : listTicketStatus\"\r\n                                    optionLabel=\"label\"\r\n                                    optionValue=\"value\"\r\n                                    [placeholder]=\"tranService.translate('ticket.label.status')\"\r\n                                    [emptyMessage]=\"tranService.translate('global.text.nodata')\"\r\n                        ></p-dropdown>\r\n                        <span *ngIf=\"typeRequest=='detail' && ticket.statusOld == 0\"\r\n                              [class]=\"['p-2', 'text-white', 'bg-cyan-300', 'border-round', 'inline-block']\">{{ getValueStatus(ticket.statusOld) }}</span>\r\n                        <span *ngIf=\"typeRequest=='detail' && ticket.statusOld == 1\"\r\n                              [class]=\"['p-2', 'text-white', 'bg-bluegray-500', 'border-round', 'inline-block']\">{{ getValueStatus(ticket.statusOld) }}</span>\r\n                        <span *ngIf=\"typeRequest=='detail' && ticket.statusOld == 2\"\r\n                              [class]=\"['p-2', 'text-white', 'bg-orange-400', 'border-round', 'inline-block']\">{{ getValueStatus(ticket.statusOld) }}</span>\r\n                        <span *ngIf=\"typeRequest=='detail' && ticket.statusOld == 3\"\r\n                              [class]=\"['p-2', 'text-white', 'bg-red-500', 'border-round', 'inline-block']\">{{ getValueStatus(ticket.statusOld) }}</span>\r\n                        <span *ngIf=\"typeRequest=='detail' && ticket.statusOld == 4\"\r\n                              [class]=\"['p-2', 'text-white', 'bg-green-500', 'border-round', 'inline-block']\">{{ getValueStatus(ticket.statusOld) }}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- error trang thai -->\r\n                <div *ngIf=\"typeRequest=='update' || typeRequest == 'detail' && ticket.assigneeId == null\"\r\n                     class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"userType\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\"\r\n                               *ngIf=\"formTicketSim.controls.status.dirty && formTicketSim.controls.status.errors?.required\">{{ tranService.translate(\"global.message.required\") }}</small>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <!-- ghi chu xu ly-->\r\n            <div *ngIf=\"typeRequest=='update'\"\r\n                 [class]=\"userInfo.type == userType.PROVINCE ? (ticket.assigneeId == null || (ticket.assigneeId != null && !listActivatedAccount.includes(ticket.assigneeId))? '': 'hidden'): ''\"\r\n                 class=\"w-full field grid chart-grid\">\r\n                <label htmlFor=\"contactName\" class=\"col-fixed\"\r\n                       style=\"width:180px\">{{ tranService.translate(\"ticket.label.processingNotes\") }}<span\r\n                    *ngIf=\"ticket.status\" class=\"text-red-500\">*</span></label>\r\n                <div class=\"col\">\r\n                    <input class=\"w-full\"\r\n                           pInputText id=\"cause\"\r\n                           [(ngModel)]=\"ticket.cause\"\r\n                           formControlName=\"cause\"\r\n                           [required]=\"ticket.status != null\"\r\n                           [maxLength]=\"255\"\r\n                           [placeholder]=\"tranService.translate('ticket.label.processingNotes')\"\r\n                           (keydown)=\"onKeyDownCause($event)\"\r\n                    />\r\n                </div>\r\n            </div>\r\n            <!-- error ghi chu xu ly-->\r\n            <div\r\n                *ngIf=\"typeRequest=='update' || typeRequest == 'detail' && (ticket.assigneeId == null || (ticket.assigneeId != null && !listActivatedAccount.includes(ticket.assigneeId)))\"\r\n                class=\"w-full field grid text-error-field\">\r\n                <label htmlFor=\"fullName\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                <div class=\"col\">\r\n                    <small class=\"text-red-500\"\r\n                           *ngIf=\"(formTicketSim.controls.cause.dirty && formTicketSim.controls.cause.errors?.required) || (this.ticket.status != null && (this.ticket.cause == null || this.ticket.cause.trim() == ''))\">{{ tranService.translate(\"global.message.required\") }}</small>\r\n                </div>\r\n            </div>\r\n\r\n            <!--          danh sach ghi chu xu ly-->\r\n            <div *ngIf=\"(typeRequest=='update' || typeRequest == 'detail') && (listNotes && listNotes.length > 0)\"\r\n                 class=\"w-full field grid chart-grid\">\r\n                <label htmlFor=\"content\" class=\"col-fixed font-medium\"\r\n                       style=\"width:180px;height: fit-content;\">{{ tranService.translate(\"ticket.label.listNote\") }}</label>\r\n                <p-table style=\"width: 100%\" [value]=\"listNotes\" [tableStyle]=\"{ 'min-width': '50rem' }\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <tr>\r\n                            <th>{{ tranService.translate('global.text.stt') }}</th>\r\n                            <th>{{ tranService.translate('account.text.account') }}</th>\r\n                            <th style=\"min-width: 146px\">{{ tranService.translate('global.button.changeStatus') }}</th>\r\n                            <th style=\"min-width: 155px\">{{ tranService.translate('account.label.time') }}</th>\r\n                            <th>{{ tranService.translate('ticket.label.content') }}</th>\r\n                        </tr>\r\n                    </ng-template>\r\n                    <ng-template pTemplate=\"body\" let-note let-i=\"rowIndex\">\r\n                        <tr [formGroup]=\"mapForm[note.id]\">\r\n                            <td>{{ i + 1 }}</td>\r\n                            <td>{{ note.userName }}</td>\r\n                            <td>{{ getValueStatus(note.status) }}</td>\r\n                            <td>{{ note.createdDate | date:\"HH:mm:ss dd/MM/yyyy\" }}</td>\r\n                            <td>\r\n                                <input *ngIf=\"typeRequest == 'update'\" class=\"w-full\"\r\n                                       pInputText id=\"content\"\r\n                                       [(ngModel)]=\"note.content\"\r\n                                       formControlName=\"content\"\r\n                                       [required]=\"true\"\r\n                                       [maxLength]=\"255\"\r\n                                       (keydown)=\"onKeyDownNoteContent($event, note)\"\r\n                                />\r\n                                <span *ngIf=\"typeRequest == 'detail'\">{{ note.content }}</span>\r\n                                <small class=\"text-red-500\"\r\n                                       *ngIf=\"mapForm[note.id].controls.content.dirty && mapForm[note.id].controls.content.errors?.required\">{{ tranService.translate(\"global.message.required\") }}</small>\r\n                                <small class=\"text-red-500\"\r\n                                       *ngIf=\"mapForm[note.id].controls.content.errors?.maxLength\">{{ tranService.translate(\"global.message.maxLength\", {len: 255}) }}</small>\r\n\r\n                            </td>\r\n                        </tr>\r\n                    </ng-template>\r\n                </p-table>\r\n            </div>\r\n\r\n            <div *ngIf=\"typeRequest != 'detail'\" class=\"flex flex-row justify-content-center align-items-center mt-3\">\r\n                <p-button styleClass=\"mr-2 p-button-secondary\" [label]=\"tranService.translate('global.button.cancel')\"\r\n                          (click)=\"isShowCreateRequest = false\"></p-button>\r\n                <p-button type=\"submit\" styleClass=\"p-button-info\"\r\n                          [disabled]=\"formTicketSim.invalid || (typeRequest=='create' && !isValidDiagnoseNumber) || (this.ticket.status != null && this.ticket.cause != null && this.ticket.cause.trim() == '') || (this.listNotes.length > 0 && !isFormValid())\"\r\n                          [label]=\"tranService.translate('global.button.save')\"></p-button>\r\n            </div>\r\n        </form>\r\n    </p-dialog>\r\n</div>\r\n"], "mappings": "AAEA,SAAQA,aAAa,QAAO,sCAAsC;AAElE,SAAQC,SAAS,QAAO,iCAAiC;AACzD,SAAQC,aAAa,QAAO,wBAAwB;AACpD,SAAQC,cAAc,QAAO,4CAA4C;AACzE,SAOIC,UAAU,QACP,gBAAgB;AACvB,SAAQC,sBAAsB,QAAO,mDAAmD;AACxF,SAAQC,gBAAgB,QAAO,4DAA4D;;;;;;;;;;;;;;;;;;;;;ICXnFC,EAAA,CAAAC,cAAA,mBAG8C;IAApCD,EAAA,CAAAE,UAAA,mBAAAC,0EAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,OAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IACrCT,EAAA,CAAAU,YAAA,EAAW;;;;IAFDV,EAAA,CAAAW,UAAA,UAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,yBAAuD;;;;;;IAU7Dd,EAAA,CAAAC,cAAA,cAA2E;IAKvDD,EAAA,CAAAE,UAAA,2BAAAa,gFAAAC,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAa,IAAA;MAAA,MAAAC,OAAA,GAAAlB,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAU,OAAA,CAAAC,UAAA,CAAAC,YAAA,GAAAJ,MAAA,CACxC;IAAA,EADgE;IAMhDhB,EAAA,CAAAU,YAAA,EAAa;IACdV,EAAA,CAAAC,cAAA,gBAA8B;IAAAD,EAAA,CAAAqB,MAAA,GAAqD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAT/EV,EAAA,CAAAsB,SAAA,GAAkB;IAAlBtB,EAAA,CAAAW,UAAA,mBAAkB,uDAAAY,MAAA,CAAAJ,UAAA,CAAAC,YAAA,gCAAAG,MAAA,CAAAC,YAAA;IASAxB,EAAA,CAAAsB,SAAA,GAAqD;IAArDtB,EAAA,CAAAyB,iBAAA,CAAAF,MAAA,CAAAV,WAAA,CAAAC,SAAA,2BAAqD;;;;;IA0FvFd,EAAA,CAAAC,cAAA,cACkF;IAEnDD,EAAA,CAAAqB,MAAA,GAAqD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;IACxFV,EAAA,CAAAC,cAAA,cAAiB;IACPD,EAAA,CAAAqB,MAAA,GAA0C;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAFhCV,EAAA,CAAAsB,SAAA,GAAqD;IAArDtB,EAAA,CAAAyB,iBAAA,CAAAC,MAAA,CAAAb,WAAA,CAAAC,SAAA,2BAAqD;IAEtEd,EAAA,CAAAsB,SAAA,GAA0C;IAA1CtB,EAAA,CAAAyB,iBAAA,CAAAC,MAAA,CAAAC,eAAA,CAAAD,MAAA,CAAAE,MAAA,CAAAR,YAAA,EAA0C;;;;;;IAShDpB,EAAA,CAAAC,cAAA,gBASE;IAPKD,EAAA,CAAAE,UAAA,2BAAA2B,6EAAAb,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA0B,IAAA;MAAA,MAAAC,OAAA,GAAA/B,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAuB,OAAA,CAAAH,MAAA,CAAAI,WAAA,GAAAhB,MAAA,CACvC;IAAA,EAD0D;IAFvChB,EAAA,CAAAU,YAAA,EASE;;;;IAPKV,EAAA,CAAAW,UAAA,YAAAsB,MAAA,CAAAL,MAAA,CAAAI,WAAA,CAAgC,gCAAAC,MAAA,CAAAC,oBAAA,iBAAAD,MAAA,CAAApB,WAAA,CAAAC,SAAA;;;;;IAQvCd,EAAA,CAAAC,cAAA,WAAiE;IAAAD,EAAA,CAAAqB,MAAA,GAAwB;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA/BV,EAAA,CAAAsB,SAAA,GAAwB;IAAxBtB,EAAA,CAAAyB,iBAAA,CAAAU,MAAA,CAAAP,MAAA,CAAAI,WAAA,CAAwB;;;;;IAOzFhC,EAAA,CAAAC,cAAA,gBAC+G;IAAAD,EAAA,CAAAqB,MAAA,GAAsD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA9DV,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAyB,iBAAA,CAAAW,MAAA,CAAAvB,WAAA,CAAAC,SAAA,4BAAsD;;;;;;;;;;IACrKd,EAAA,CAAAC,cAAA,gBACoE;IAAAD,EAAA,CAAAqB,MAAA,GAAmE;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA3EV,EAAA,CAAAsB,SAAA,GAAmE;IAAnEtB,EAAA,CAAAyB,iBAAA,CAAAY,MAAA,CAAAxB,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAsC,eAAA,IAAAC,GAAA,GAAmE;;;;;IACvIvC,EAAA,CAAAC,cAAA,gBACkE;IAAAD,EAAA,CAAAqB,MAAA,GAA6D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAArEV,EAAA,CAAAsB,SAAA,GAA6D;IAA7DtB,EAAA,CAAAyB,iBAAA,CAAAe,MAAA,CAAA3B,WAAA,CAAAC,SAAA,mCAA6D;;;;;;IAU/Hd,EAAA,CAAAC,cAAA,gBASE;IAPKD,EAAA,CAAAE,UAAA,2BAAAuC,6EAAAzB,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAsC,IAAA;MAAA,MAAAC,OAAA,GAAA3C,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAmC,OAAA,CAAAf,MAAA,CAAAgB,YAAA,GAAA5B,MAAA,CACvC;IAAA,EAD2D;IAFxChB,EAAA,CAAAU,YAAA,EASE;;;;IAPKV,EAAA,CAAAW,UAAA,YAAAkC,MAAA,CAAAjB,MAAA,CAAAgB,YAAA,CAAiC,qEAAAC,MAAA,CAAAhC,WAAA,CAAAC,SAAA;;;;;IAQxCd,EAAA,CAAAC,cAAA,WAAiE;IAAAD,EAAA,CAAAqB,MAAA,GAAyB;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAAhCV,EAAA,CAAAsB,SAAA,GAAyB;IAAzBtB,EAAA,CAAAyB,iBAAA,CAAAqB,MAAA,CAAAlB,MAAA,CAAAgB,YAAA,CAAyB;;;;;IAO1F5C,EAAA,CAAAC,cAAA,gBACiH;IAAAD,EAAA,CAAAqB,MAAA,GAAsD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA9DV,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAyB,iBAAA,CAAAsB,OAAA,CAAAlC,WAAA,CAAAC,SAAA,4BAAsD;;;;;IACvKd,EAAA,CAAAC,cAAA,gBACqE;IAAAD,EAAA,CAAAqB,MAAA,GAAmE;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA3EV,EAAA,CAAAsB,SAAA,GAAmE;IAAnEtB,EAAA,CAAAyB,iBAAA,CAAAuB,OAAA,CAAAnC,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAsC,eAAA,IAAAC,GAAA,GAAmE;;;;;IACxIvC,EAAA,CAAAC,cAAA,gBACmE;IAAAD,EAAA,CAAAqB,MAAA,GAA0D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAlEV,EAAA,CAAAsB,SAAA,GAA0D;IAA1DtB,EAAA,CAAAyB,iBAAA,CAAAwB,OAAA,CAAApC,WAAA,CAAAC,SAAA,gCAA0D;;;;;;IAS7Hd,EAAA,CAAAC,cAAA,gBAUE;IARKD,EAAA,CAAAE,UAAA,2BAAAgD,6EAAAlC,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA+C,IAAA;MAAA,MAAAC,OAAA,GAAApD,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAA4C,OAAA,CAAAxB,MAAA,CAAAyB,YAAA,GAAArC,MAAA,CACvC;IAAA,EAD2D,qBAAAsC,uEAAAtC,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA+C,IAAA;MAAA,MAAAI,OAAA,GAAAvD,EAAA,CAAAO,aAAA;MAAA,OAMtBP,EAAA,CAAAQ,WAAA,CAAA+C,OAAA,CAAAC,gBAAA,CAAAxC,MAAA,CAAwB;IAAA,EANF;IAFxChB,EAAA,CAAAU,YAAA,EAUE;;;;IARKV,EAAA,CAAAW,UAAA,YAAA8C,OAAA,CAAA7B,MAAA,CAAAyB,YAAA,CAAiC,qEAAAI,OAAA,CAAA5C,WAAA,CAAAC,SAAA;;;;;IASxCd,EAAA,CAAAC,cAAA,WAAiE;IAAAD,EAAA,CAAAqB,MAAA,GAAyB;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAAhCV,EAAA,CAAAsB,SAAA,GAAyB;IAAzBtB,EAAA,CAAAyB,iBAAA,CAAAiC,OAAA,CAAA9B,MAAA,CAAAyB,YAAA,CAAyB;;;;;IAO1FrD,EAAA,CAAAC,cAAA,gBACiH;IAAAD,EAAA,CAAAqB,MAAA,GAAsD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA9DV,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAyB,iBAAA,CAAAkC,OAAA,CAAA9C,WAAA,CAAAC,SAAA,4BAAsD;;;;;IACvKd,EAAA,CAAAC,cAAA,gBACmE;IAAAD,EAAA,CAAAqB,MAAA,GAA0D;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAlEV,EAAA,CAAAsB,SAAA,GAA0D;IAA1DtB,EAAA,CAAAyB,iBAAA,CAAAmC,OAAA,CAAA/C,WAAA,CAAAC,SAAA,gCAA0D;;;;;;;;;IAS7Hd,EAAA,CAAAC,cAAA,sBAeC;IAXGD,EAAA,CAAAE,UAAA,yBAAA2D,6FAAA7C,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA0D,IAAA;MAAA,MAAAC,OAAA,GAAA/D,EAAA,CAAAO,aAAA;MAAA,OAAWP,EAAA,CAAAQ,WAAA,CAAAuD,OAAA,CAAAnC,MAAA,CAAAoC,GAAA,GAAAhD,MAAA,CAClC;IAAA,EAD6C,sBAAAiD,0FAAAjD,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA0D,IAAA;MAAA,MAAAI,OAAA,GAAAlE,EAAA,CAAAO,aAAA;MAAA,OAUVP,EAAA,CAAAQ,WAAA,CAAA0D,OAAA,CAAAC,oBAAA,CAAAnD,MAAA,CAA4B;IAAA,EAVlB;IAWzBhB,EAAA,CAAAU,YAAA,EAAc;;;;IAZXV,EAAA,CAAAW,UAAA,YAAAyD,OAAA,CAAAC,kBAAA,CAA8B,UAAAD,OAAA,CAAAxC,MAAA,CAAAoC,GAAA,iBAAAI,OAAA,CAAAvD,WAAA,CAAAC,SAAA,4FAAAd,EAAA,CAAAsC,eAAA,IAAAgC,GAAA;;;;;IAalCtE,EAAA,CAAAC,cAAA,eAC+D;IAAAD,EAAA,CAAAqB,MAAA,GAAgB;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAAvBV,EAAA,CAAAsB,SAAA,GAAgB;IAAhBtB,EAAA,CAAAyB,iBAAA,CAAA8C,OAAA,CAAA3C,MAAA,CAAAoC,GAAA,CAAgB;;;;;IAtBvFhE,EAAA,CAAAC,cAAA,cAAiG;IAE7CD,EAAA,CAAAqB,MAAA,GAC5C;IAAArB,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAqB,MAAA,QAAC;IAAArB,EAAA,CAAAU,YAAA,EAAO;IACvCV,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAwE,UAAA,IAAAC,yDAAA,0BAee;IACfzE,EAAA,CAAAwE,UAAA,IAAAE,kDAAA,mBACsF;IAC1F1E,EAAA,CAAAU,YAAA,EAAM;;;;IArB0CV,EAAA,CAAAsB,SAAA,GAC5C;IAD4CtB,EAAA,CAAA2E,kBAAA,KAAAC,OAAA,CAAA/D,WAAA,CAAAC,SAAA,sCAC5C;IAGKd,EAAA,CAAAsB,SAAA,GAA2B;IAA3BtB,EAAA,CAAAW,UAAA,SAAAiE,OAAA,CAAAC,WAAA,aAA2B;IAgBzB7E,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAW,UAAA,SAAAiE,OAAA,CAAAC,WAAA,gBAAAD,OAAA,CAAAC,WAAA,aAAsD;;;;;IAQ7D7E,EAAA,CAAAC,cAAA,gBAC6E;IACzED,EAAA,CAAAqB,MAAA,GACJ;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IADJV,EAAA,CAAAsB,SAAA,GACJ;IADItB,EAAA,CAAA2E,kBAAA,MAAAG,OAAA,CAAAjE,WAAA,CAAAC,SAAA,iCACJ;;;;;IAPRd,EAAA,CAAAC,cAAA,cACgD;IAC5CD,EAAA,CAAA+E,SAAA,gBAA8E;IAC9E/E,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAwE,UAAA,IAAAQ,mDAAA,oBAGQ;IAGZhF,EAAA,CAAAU,YAAA,EAAM;;;;IALMV,EAAA,CAAAsB,SAAA,GAAmE;IAAnEtB,EAAA,CAAAW,UAAA,SAAAsE,OAAA,CAAAZ,kBAAA,CAAAa,KAAA,IAAAD,OAAA,CAAAZ,kBAAA,CAAAc,KAAA,CAAAC,QAAA,CAAmE;;;;;;IAavEpF,EAAA,CAAAC,cAAA,mBASC;IALSD,EAAA,CAAAE,UAAA,2BAAAmF,mFAAArE,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAkF,IAAA;MAAA,MAAAC,OAAA,GAAAvF,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAA+E,OAAA,CAAA3D,MAAA,CAAA4D,OAAA,GAAAxE,MAAA,CAC9C;IAAA,EAD6D,qBAAAyE,6EAAAzE,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAkF,IAAA;MAAA,MAAAI,OAAA,GAAA1F,EAAA,CAAAO,aAAA;MAAA,OAIjBP,EAAA,CAAAQ,WAAA,CAAAkF,OAAA,CAAAC,gBAAA,CAAA3E,MAAA,CAAwB;IAAA,EAJP;IAKrChB,EAAA,CAAAU,YAAA,EAAW;;;;IAPFV,EAAA,CAAAW,UAAA,qBAAoB,YAAAiF,OAAA,CAAAhE,MAAA,CAAA4D,OAAA,mCAAAI,OAAA,CAAA/E,WAAA,CAAAC,SAAA;;;;;IAQlCd,EAAA,CAAAC,cAAA,eAC+D;IAAAD,EAAA,CAAAqB,MAAA,GAAoB;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA3BV,EAAA,CAAAsB,SAAA,GAAoB;IAApBtB,EAAA,CAAAyB,iBAAA,CAAAoE,OAAA,CAAAjE,MAAA,CAAA4D,OAAA,CAAoB;;;;;IAOnFxF,EAAA,CAAAC,cAAA,gBACgE;IAAAD,EAAA,CAAAqB,MAAA,GAAmE;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA3EV,EAAA,CAAAsB,SAAA,GAAmE;IAAnEtB,EAAA,CAAAyB,iBAAA,CAAAqE,OAAA,CAAAjF,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAsC,eAAA,IAAAC,GAAA,GAAmE;;;;;;IAQ/HvC,EAAA,CAAAC,cAAA,mBASC;IALSD,EAAA,CAAAE,UAAA,2BAAA6F,mFAAA/E,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA4F,IAAA;MAAA,MAAAC,OAAA,GAAAjG,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAyF,OAAA,CAAArE,MAAA,CAAAsE,IAAA,GAAAlF,MAAA,CAC9C;IAAA,EAD0D,qBAAAmF,6EAAAnF,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA4F,IAAA;MAAA,MAAAI,OAAA,GAAApG,EAAA,CAAAO,aAAA;MAAA,OAIdP,EAAA,CAAAQ,WAAA,CAAA4F,OAAA,CAAAC,aAAA,CAAArF,MAAA,CAAqB;IAAA,EAJP;IAKlChB,EAAA,CAAAU,YAAA,EAAW;;;;IAPFV,EAAA,CAAAW,UAAA,qBAAoB,YAAA2F,OAAA,CAAA1E,MAAA,CAAAsE,IAAA,mCAAAI,OAAA,CAAAzF,WAAA,CAAAC,SAAA;;;;;IAQlCd,EAAA,CAAAC,cAAA,eAC+D;IAAAD,EAAA,CAAAqB,MAAA,GAAiB;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAAxBV,EAAA,CAAAsB,SAAA,GAAiB;IAAjBtB,EAAA,CAAAyB,iBAAA,CAAA8E,OAAA,CAAA3E,MAAA,CAAAsE,IAAA,CAAiB;;;;;IAOhFlG,EAAA,CAAAC,cAAA,gBAC6D;IAAAD,EAAA,CAAAqB,MAAA,GAAmE;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA3EV,EAAA,CAAAsB,SAAA,GAAmE;IAAnEtB,EAAA,CAAAyB,iBAAA,CAAA+E,OAAA,CAAA3F,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAsC,eAAA,IAAAC,GAAA,GAAmE;;;;;;IA2ChIvC,EAAA,CAAAC,cAAA,qBAUC;IAPWD,EAAA,CAAAE,UAAA,2BAAAuG,6FAAAzF,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAsG,IAAA;MAAA,MAAAC,OAAA,GAAA3G,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAmG,OAAA,CAAA/E,MAAA,CAAAgF,MAAA,GAAA5F,MAAA,CAC5C;IAAA,EAD0D;IAOtChB,EAAA,CAAAU,YAAA,EAAa;;;;IATFV,EAAA,CAAAW,UAAA,mBAAkB,sCAAAkG,OAAA,CAAAjF,MAAA,CAAAgF,MAAA,aAAAC,OAAA,CAAAjF,MAAA,CAAAkF,SAAA,YAAAD,OAAA,CAAAE,eAAA,CAAAF,OAAA,CAAAjF,MAAA,CAAAkF,SAAA,IAAAD,OAAA,CAAAG,gBAAA,iBAAAH,OAAA,CAAAhG,WAAA,CAAAC,SAAA,yCAAA+F,OAAA,CAAAhG,WAAA,CAAAC,SAAA;;;;;;;;IAU9Bd,EAAA,CAAAC,cAAA,WACqF;IAAAD,EAAA,CAAAqB,MAAA,GAAsC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA5HV,EAAA,CAAAiH,UAAA,CAAAjH,EAAA,CAAAsC,eAAA,IAAA4E,GAAA,EAA8E;IAAClH,EAAA,CAAAsB,SAAA,GAAsC;IAAtCtB,EAAA,CAAAyB,iBAAA,CAAA0F,OAAA,CAAAC,cAAA,CAAAD,OAAA,CAAAvF,MAAA,CAAAkF,SAAA,EAAsC;;;;;;;;IAC3H9G,EAAA,CAAAC,cAAA,WACyF;IAAAD,EAAA,CAAAqB,MAAA,GAAsC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAAhIV,EAAA,CAAAiH,UAAA,CAAAjH,EAAA,CAAAsC,eAAA,IAAA+E,GAAA,EAAkF;IAACrH,EAAA,CAAAsB,SAAA,GAAsC;IAAtCtB,EAAA,CAAAyB,iBAAA,CAAA6F,OAAA,CAAAF,cAAA,CAAAE,OAAA,CAAA1F,MAAA,CAAAkF,SAAA,EAAsC;;;;;;;;IAC/H9G,EAAA,CAAAC,cAAA,WACuF;IAAAD,EAAA,CAAAqB,MAAA,GAAsC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA9HV,EAAA,CAAAiH,UAAA,CAAAjH,EAAA,CAAAsC,eAAA,IAAAiF,GAAA,EAAgF;IAACvH,EAAA,CAAAsB,SAAA,GAAsC;IAAtCtB,EAAA,CAAAyB,iBAAA,CAAA+F,OAAA,CAAAJ,cAAA,CAAAI,OAAA,CAAA5F,MAAA,CAAAkF,SAAA,EAAsC;;;;;;;;IAC7H9G,EAAA,CAAAC,cAAA,WACoF;IAAAD,EAAA,CAAAqB,MAAA,GAAsC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA3HV,EAAA,CAAAiH,UAAA,CAAAjH,EAAA,CAAAsC,eAAA,IAAAmF,GAAA,EAA6E;IAACzH,EAAA,CAAAsB,SAAA,GAAsC;IAAtCtB,EAAA,CAAAyB,iBAAA,CAAAiG,OAAA,CAAAN,cAAA,CAAAM,OAAA,CAAA9F,MAAA,CAAAkF,SAAA,EAAsC;;;;;;;;IAC1H9G,EAAA,CAAAC,cAAA,WACsF;IAAAD,EAAA,CAAAqB,MAAA,GAAsC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAA7HV,EAAA,CAAAiH,UAAA,CAAAjH,EAAA,CAAAsC,eAAA,IAAAqF,GAAA,EAA+E;IAAC3H,EAAA,CAAAsB,SAAA,GAAsC;IAAtCtB,EAAA,CAAAyB,iBAAA,CAAAmG,OAAA,CAAAR,cAAA,CAAAQ,OAAA,CAAAhG,MAAA,CAAAkF,SAAA,EAAsC;;;;;IA1BpI9G,EAAA,CAAAC,cAAA,cAE0C;IAEXD,EAAA,CAAAqB,MAAA,GAAkD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;IACrFV,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAwE,UAAA,IAAAqD,wDAAA,yBAUc;IACd7H,EAAA,CAAAwE,UAAA,IAAAsD,kDAAA,mBACkI;IAClI9H,EAAA,CAAAwE,UAAA,IAAAuD,kDAAA,mBACsI;IACtI/H,EAAA,CAAAwE,UAAA,IAAAwD,kDAAA,mBACoI;IACpIhI,EAAA,CAAAwE,UAAA,IAAAyD,kDAAA,mBACiI;IACjIjI,EAAA,CAAAwE,UAAA,IAAA0D,kDAAA,mBACmI;IACvIlI,EAAA,CAAAU,YAAA,EAAM;;;;IA1BLV,EAAA,CAAAiH,UAAA,CAAAkB,OAAA,CAAAC,QAAA,CAAAC,IAAA,IAAAF,OAAA,CAAAG,QAAA,CAAAC,QAAA,IAAAJ,OAAA,CAAAtD,WAAA,eAAAsD,OAAA,CAAAvG,MAAA,CAAA4G,UAAA,YAAAL,OAAA,CAAAvG,MAAA,CAAA4G,UAAA,aAAAL,OAAA,CAAAM,oBAAA,CAAAC,QAAA,CAAAP,OAAA,CAAAvG,MAAA,CAAA4G,UAAA,uBAA6M;IAGnLxI,EAAA,CAAAsB,SAAA,GAAkD;IAAlDtB,EAAA,CAAAyB,iBAAA,CAAA0G,OAAA,CAAAtH,WAAA,CAAAC,SAAA,wBAAkD;IAE5Dd,EAAA,CAAAsB,SAAA,GAA2B;IAA3BtB,EAAA,CAAAW,UAAA,SAAAwH,OAAA,CAAAtD,WAAA,aAA2B;IAWjC7E,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAW,UAAA,SAAAwH,OAAA,CAAAtD,WAAA,gBAAAsD,OAAA,CAAAvG,MAAA,CAAAkF,SAAA,MAAoD;IAEpD9G,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAW,UAAA,SAAAwH,OAAA,CAAAtD,WAAA,gBAAAsD,OAAA,CAAAvG,MAAA,CAAAkF,SAAA,MAAoD;IAEpD9G,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAW,UAAA,SAAAwH,OAAA,CAAAtD,WAAA,gBAAAsD,OAAA,CAAAvG,MAAA,CAAAkF,SAAA,MAAoD;IAEpD9G,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAW,UAAA,SAAAwH,OAAA,CAAAtD,WAAA,gBAAAsD,OAAA,CAAAvG,MAAA,CAAAkF,SAAA,MAAoD;IAEpD9G,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAW,UAAA,SAAAwH,OAAA,CAAAtD,WAAA,gBAAAsD,OAAA,CAAAvG,MAAA,CAAAkF,SAAA,MAAoD;;;;;IAS3D9G,EAAA,CAAAC,cAAA,gBACqG;IAAAD,EAAA,CAAAqB,MAAA,GAAsD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA9DV,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAyB,iBAAA,CAAAkH,OAAA,CAAA9H,WAAA,CAAAC,SAAA,4BAAsD;;;;;IALnKd,EAAA,CAAAC,cAAA,cACgD;IAC5CD,EAAA,CAAA+E,SAAA,gBAAwE;IACxE/E,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAwE,UAAA,IAAAoE,mDAAA,oBACmK;IACvK5I,EAAA,CAAAU,YAAA,EAAM;;;;IADMV,EAAA,CAAAsB,SAAA,GAA2F;IAA3FtB,EAAA,CAAAW,UAAA,SAAAkI,OAAA,CAAAC,aAAA,CAAAC,QAAA,CAAAnC,MAAA,CAAA1B,KAAA,KAAA2D,OAAA,CAAAC,aAAA,CAAAC,QAAA,CAAAnC,MAAA,CAAAoC,MAAA,kBAAAH,OAAA,CAAAC,aAAA,CAAAC,QAAA,CAAAnC,MAAA,CAAAoC,MAAA,CAAA5D,QAAA,EAA2F;;;;;IASrBpF,EAAA,CAAAC,cAAA,eACvC;IAAAD,EAAA,CAAAqB,MAAA,QAAC;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;;;IAL3DV,EAAA,CAAAC,cAAA,cAE0C;IAEXD,EAAA,CAAAqB,MAAA,GAA2D;IAAArB,EAAA,CAAAwE,UAAA,IAAAyE,kDAAA,mBAC/B;IAAAjJ,EAAA,CAAAU,YAAA,EAAQ;IAC/DV,EAAA,CAAAC,cAAA,cAAiB;IAGND,EAAA,CAAAE,UAAA,2BAAAgJ,2EAAAlI,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA+I,IAAA;MAAA,MAAAC,OAAA,GAAApJ,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAA4I,OAAA,CAAAxH,MAAA,CAAAyH,KAAA,GAAArI,MAAA,CACnC;IAAA,EADgD,qBAAAsI,qEAAAtI,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA+I,IAAA;MAAA,MAAAI,OAAA,GAAAvJ,EAAA,CAAAO,aAAA;MAAA,OAKfP,EAAA,CAAAQ,WAAA,CAAA+I,OAAA,CAAAC,cAAA,CAAAxI,MAAA,CAAsB;IAAA,EALP;IAFjChB,EAAA,CAAAU,YAAA,EAQE;;;;IAdLV,EAAA,CAAAiH,UAAA,CAAAwC,OAAA,CAAArB,QAAA,CAAAC,IAAA,IAAAoB,OAAA,CAAAnB,QAAA,CAAAC,QAAA,GAAAkB,OAAA,CAAA7H,MAAA,CAAA4G,UAAA,YAAAiB,OAAA,CAAA7H,MAAA,CAAA4G,UAAA,aAAAiB,OAAA,CAAAhB,oBAAA,CAAAC,QAAA,CAAAe,OAAA,CAAA7H,MAAA,CAAA4G,UAAA,uBAAgL;IAGtJxI,EAAA,CAAAsB,SAAA,GAA2D;IAA3DtB,EAAA,CAAAyB,iBAAA,CAAAgI,OAAA,CAAA5I,WAAA,CAAAC,SAAA,iCAA2D;IACjFd,EAAA,CAAAsB,SAAA,GAAmB;IAAnBtB,EAAA,CAAAW,UAAA,SAAA8I,OAAA,CAAA7H,MAAA,CAAAgF,MAAA,CAAmB;IAIb5G,EAAA,CAAAsB,SAAA,GAA0B;IAA1BtB,EAAA,CAAAW,UAAA,YAAA8I,OAAA,CAAA7H,MAAA,CAAAyH,KAAA,CAA0B,aAAAI,OAAA,CAAA7H,MAAA,CAAAgF,MAAA,2CAAA6C,OAAA,CAAA5I,WAAA,CAAAC,SAAA;;;;;IAejCd,EAAA,CAAAC,cAAA,gBACsM;IAAAD,EAAA,CAAAqB,MAAA,GAAsD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA9DV,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAyB,iBAAA,CAAAiI,OAAA,CAAA7I,WAAA,CAAAC,SAAA,4BAAsD;;;;;IANpQd,EAAA,CAAAC,cAAA,cAE+C;IAC3CD,EAAA,CAAA+E,SAAA,gBAAwE;IACxE/E,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAwE,UAAA,IAAAmF,mDAAA,oBACoQ;IACxQ3J,EAAA,CAAAU,YAAA,EAAM;;;;IADMV,EAAA,CAAAsB,SAAA,GAA4L;IAA5LtB,EAAA,CAAAW,UAAA,SAAAiJ,OAAA,CAAAd,aAAA,CAAAC,QAAA,CAAAM,KAAA,CAAAnE,KAAA,KAAA0E,OAAA,CAAAd,aAAA,CAAAC,QAAA,CAAAM,KAAA,CAAAL,MAAA,kBAAAY,OAAA,CAAAd,aAAA,CAAAC,QAAA,CAAAM,KAAA,CAAAL,MAAA,CAAA5D,QAAA,KAAAwE,OAAA,CAAAhI,MAAA,CAAAgF,MAAA,aAAAgD,OAAA,CAAAhI,MAAA,CAAAyH,KAAA,YAAAO,OAAA,CAAAhI,MAAA,CAAAyH,KAAA,CAAAQ,IAAA,UAA4L;;;;;IAWhM7J,EAAA,CAAAC,cAAA,SAAI;IACID,EAAA,CAAAqB,MAAA,GAA8C;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACvDV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,GAAmD;IAAArB,EAAA,CAAAU,YAAA,EAAK;IAC5DV,EAAA,CAAAC,cAAA,aAA6B;IAAAD,EAAA,CAAAqB,MAAA,GAAyD;IAAArB,EAAA,CAAAU,YAAA,EAAK;IAC3FV,EAAA,CAAAC,cAAA,aAA6B;IAAAD,EAAA,CAAAqB,MAAA,GAAiD;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACnFV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,IAAmD;IAAArB,EAAA,CAAAU,YAAA,EAAK;;;;IAJxDV,EAAA,CAAAsB,SAAA,GAA8C;IAA9CtB,EAAA,CAAAyB,iBAAA,CAAAqI,OAAA,CAAAjJ,WAAA,CAAAC,SAAA,oBAA8C;IAC9Cd,EAAA,CAAAsB,SAAA,GAAmD;IAAnDtB,EAAA,CAAAyB,iBAAA,CAAAqI,OAAA,CAAAjJ,WAAA,CAAAC,SAAA,yBAAmD;IAC1Bd,EAAA,CAAAsB,SAAA,GAAyD;IAAzDtB,EAAA,CAAAyB,iBAAA,CAAAqI,OAAA,CAAAjJ,WAAA,CAAAC,SAAA,+BAAyD;IACzDd,EAAA,CAAAsB,SAAA,GAAiD;IAAjDtB,EAAA,CAAAyB,iBAAA,CAAAqI,OAAA,CAAAjJ,WAAA,CAAAC,SAAA,uBAAiD;IAC1Ed,EAAA,CAAAsB,SAAA,GAAmD;IAAnDtB,EAAA,CAAAyB,iBAAA,CAAAqI,OAAA,CAAAjJ,WAAA,CAAAC,SAAA,yBAAmD;;;;;;IAUnDd,EAAA,CAAAC,cAAA,gBAOE;IALKD,EAAA,CAAAE,UAAA,2BAAA6J,mGAAA/I,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA4J,IAAA;MAAA,MAAAC,QAAA,GAAAjK,EAAA,CAAAO,aAAA,GAAA2J,SAAA;MAAA,OAAalK,EAAA,CAAAQ,WAAA,CAAAyJ,QAAA,CAAAzE,OAAA,GAAAxE,MAAA,CAC/C;IAAA,EAD4D,qBAAAmJ,6FAAAnJ,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAA4J,IAAA;MAAA,MAAAC,QAAA,GAAAjK,EAAA,CAAAO,aAAA,GAAA2J,SAAA;MAAA,MAAAE,OAAA,GAAApK,EAAA,CAAAO,aAAA;MAAA,OAIfP,EAAA,CAAAQ,WAAA,CAAA4J,OAAA,CAAAC,oBAAA,CAAArJ,MAAA,EAAAiJ,QAAA,CAAkC;IAAA,EAJnB;IAFjCjK,EAAA,CAAAU,YAAA,EAOE;;;;IALKV,EAAA,CAAAW,UAAA,YAAAsJ,QAAA,CAAAzE,OAAA,CAA0B;;;;;IAMjCxF,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAqB,MAAA,GAAkB;IAAArB,EAAA,CAAAU,YAAA,EAAO;;;;IAAzBV,EAAA,CAAAsB,SAAA,GAAkB;IAAlBtB,EAAA,CAAAyB,iBAAA,CAAAwI,QAAA,CAAAzE,OAAA,CAAkB;;;;;IACxDxF,EAAA,CAAAC,cAAA,gBAC6G;IAAAD,EAAA,CAAAqB,MAAA,GAAsD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA9DV,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAyB,iBAAA,CAAA6I,OAAA,CAAAzJ,WAAA,CAAAC,SAAA,4BAAsD;;;;;IACnKd,EAAA,CAAAC,cAAA,gBACmE;IAAAD,EAAA,CAAAqB,MAAA,GAAmE;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA3EV,EAAA,CAAAsB,SAAA,GAAmE;IAAnEtB,EAAA,CAAAyB,iBAAA,CAAA8I,OAAA,CAAA1J,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAsC,eAAA,IAAAC,GAAA,GAAmE;;;;;IAlB9IvC,EAAA,CAAAC,cAAA,aAAmC;IAC3BD,EAAA,CAAAqB,MAAA,GAAW;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACpBV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,GAAmB;IAAArB,EAAA,CAAAU,YAAA,EAAK;IAC5BV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,GAAiC;IAAArB,EAAA,CAAAU,YAAA,EAAK;IAC1CV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,GAAmD;;IAAArB,EAAA,CAAAU,YAAA,EAAK;IAC5DV,EAAA,CAAAC,cAAA,UAAI;IACAD,EAAA,CAAAwE,UAAA,KAAAgG,mEAAA,oBAOE;IACFxK,EAAA,CAAAwE,UAAA,KAAAiG,kEAAA,mBAA+D;IAC/DzK,EAAA,CAAAwE,UAAA,KAAAkG,mEAAA,oBAC2K;IAC3K1K,EAAA,CAAAwE,UAAA,KAAAmG,mEAAA,oBAC8I;IAElJ3K,EAAA,CAAAU,YAAA,EAAK;;;;;;IApBLV,EAAA,CAAAW,UAAA,cAAAiK,OAAA,CAAAC,OAAA,CAAAZ,QAAA,CAAAa,EAAA,EAA8B;IAC1B9K,EAAA,CAAAsB,SAAA,GAAW;IAAXtB,EAAA,CAAAyB,iBAAA,CAAAsJ,KAAA,KAAW;IACX/K,EAAA,CAAAsB,SAAA,GAAmB;IAAnBtB,EAAA,CAAAyB,iBAAA,CAAAwI,QAAA,CAAAe,QAAA,CAAmB;IACnBhL,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAyB,iBAAA,CAAAmJ,OAAA,CAAAxD,cAAA,CAAA6C,QAAA,CAAArD,MAAA,EAAiC;IACjC5G,EAAA,CAAAsB,SAAA,GAAmD;IAAnDtB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAAiL,WAAA,OAAAhB,QAAA,CAAAiB,WAAA,yBAAmD;IAE3ClL,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAW,UAAA,SAAAiK,OAAA,CAAA/F,WAAA,aAA6B;IAQ9B7E,EAAA,CAAAsB,SAAA,GAA6B;IAA7BtB,EAAA,CAAAW,UAAA,SAAAiK,OAAA,CAAA/F,WAAA,aAA6B;IAE5B7E,EAAA,CAAAsB,SAAA,GAAmG;IAAnGtB,EAAA,CAAAW,UAAA,SAAAiK,OAAA,CAAAC,OAAA,CAAAZ,QAAA,CAAAa,EAAA,EAAA/B,QAAA,CAAAvD,OAAA,CAAAN,KAAA,KAAA0F,OAAA,CAAAC,OAAA,CAAAZ,QAAA,CAAAa,EAAA,EAAA/B,QAAA,CAAAvD,OAAA,CAAAwD,MAAA,kBAAA4B,OAAA,CAAAC,OAAA,CAAAZ,QAAA,CAAAa,EAAA,EAAA/B,QAAA,CAAAvD,OAAA,CAAAwD,MAAA,CAAA5D,QAAA,EAAmG;IAEnGpF,EAAA,CAAAsB,SAAA,GAAyD;IAAzDtB,EAAA,CAAAW,UAAA,SAAAiK,OAAA,CAAAC,OAAA,CAAAZ,QAAA,CAAAa,EAAA,EAAA/B,QAAA,CAAAvD,OAAA,CAAAwD,MAAA,kBAAA4B,OAAA,CAAAC,OAAA,CAAAZ,QAAA,CAAAa,EAAA,EAAA/B,QAAA,CAAAvD,OAAA,CAAAwD,MAAA,CAAAmC,SAAA,CAAyD;;;;;;;;;;IAjCrFnL,EAAA,CAAAC,cAAA,cAC0C;IAEUD,EAAA,CAAAqB,MAAA,GAAoD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;IAC5GV,EAAA,CAAAC,cAAA,kBAAyF;IACrFD,EAAA,CAAAwE,UAAA,IAAA4G,0DAAA,2BAQc;IACdpL,EAAA,CAAAwE,UAAA,IAAA6G,0DAAA,4BAuBc;IAClBrL,EAAA,CAAAU,YAAA,EAAU;;;;IAnCsCV,EAAA,CAAAsB,SAAA,GAAoD;IAApDtB,EAAA,CAAAyB,iBAAA,CAAA6J,OAAA,CAAAzK,WAAA,CAAAC,SAAA,0BAAoD;IACvEd,EAAA,CAAAsB,SAAA,GAAmB;IAAnBtB,EAAA,CAAAW,UAAA,UAAA2K,OAAA,CAAAC,SAAA,CAAmB,eAAAvL,EAAA,CAAAsC,eAAA,IAAAkJ,GAAA;;;;;;IAqCpDxL,EAAA,CAAAC,cAAA,cAA0G;IAE5FD,EAAA,CAAAE,UAAA,mBAAAuL,uEAAA;MAAAzL,EAAA,CAAAI,aAAA,CAAAsL,IAAA;MAAA,MAAAC,OAAA,GAAA3L,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAmL,OAAA,CAAAC,mBAAA,GAA+B,KAAK;IAAA,EAAC;IAAC5L,EAAA,CAAAU,YAAA,EAAW;IAC3DV,EAAA,CAAA+E,SAAA,mBAE2E;IAC/E/E,EAAA,CAAAU,YAAA,EAAM;;;;IAL6CV,EAAA,CAAAsB,SAAA,GAAuD;IAAvDtB,EAAA,CAAAW,UAAA,UAAAkL,OAAA,CAAAhL,WAAA,CAAAC,SAAA,yBAAuD;IAG5Fd,EAAA,CAAAsB,SAAA,GAAuO;IAAvOtB,EAAA,CAAAW,UAAA,aAAAkL,OAAA,CAAA/C,aAAA,CAAAgD,OAAA,IAAAD,OAAA,CAAAhH,WAAA,iBAAAgH,OAAA,CAAAE,qBAAA,IAAAF,OAAA,CAAAjK,MAAA,CAAAgF,MAAA,YAAAiF,OAAA,CAAAjK,MAAA,CAAAyH,KAAA,YAAAwC,OAAA,CAAAjK,MAAA,CAAAyH,KAAA,CAAAQ,IAAA,YAAAgC,OAAA,CAAAN,SAAA,CAAAS,MAAA,SAAAH,OAAA,CAAAI,WAAA,GAAuO,UAAAJ,OAAA,CAAAhL,WAAA,CAAAC,SAAA;;;;;;;;;;;;;;;;;;;ADzbjQ,OAAM,MAAOoL,2BAA4B,SAAQvM,aAAa;EA0D1DwM,YACmCC,aAA4B,EAC3BC,cAA8B,EACtBC,sBAA8C,EAC9EC,WAAwB,EACxBC,QAAkB;IAC1B,KAAK,CAACA,QAAQ,CAAC;IALgB,KAAAJ,aAAa,GAAbA,aAAa;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACN,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtD,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAjBpB,KAAAtK,oBAAoB,GAAW,GAAG;IAIlC,KAAA6J,qBAAqB,GAAY,KAAK;IACtC,KAAAU,SAAS,GAAQ,EAAE;IAEnB,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAA7B,OAAO,GAAQ,EAAE;IACjB,KAAA8B,UAAU,GAAW,EAAE;IACvB,KAAAtI,kBAAkB,GAAqB,IAAItE,gBAAgB,EAAE;IA0tB1C,KAAAL,SAAS,GAAGA,SAAS;EAjtBxC;EAEAkN,QAAQA,CAAA;IACJ,IAAIC,EAAE,GAAG,IAAI;IACb,IAAI,CAACzE,QAAQ,GAAG,IAAI,CAAC0E,cAAc,CAAC1E,QAAQ;IAC5C,IAAI,CAACwD,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAAC/G,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAACyD,QAAQ,GAAG5I,SAAS,CAACqN,SAAS;IACnC,IAAI,CAACxB,SAAS,GAAG,EAAE;IACnB,IAAI,CAAC3J,MAAM,GAAG;MACVkJ,EAAE,EAAE,IAAI;MACR9I,WAAW,EAAE,IAAI;MACjBY,YAAY,EAAE,IAAI;MAClBS,YAAY,EAAE,IAAI;MAClBmC,OAAO,EAAE,IAAI;MACbU,IAAI,EAAE,IAAI;MACVmD,KAAK,EAAE,IAAI;MACXhB,IAAI,EAAE3I,SAAS,CAACsN,YAAY,CAACC,QAAQ;MACrCjJ,GAAG,EAAE,IAAI;MACT4C,MAAM,EAAE,IAAI;MACZE,SAAS,EAAE,IAAI;MACf0B,UAAU,EAAE,IAAI;MAChBpH,YAAY,EAAE;KACjB;IACD,IAAI,CAAC2F,eAAe,GAAG;MACnB,CAAC,EAAE,CAAC;QACAmG,KAAK,EAAEL,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;QACzDqM,KAAK,EAAE;OACV,EACG;QACID,KAAK,EAAEL,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;QAC3DqM,KAAK,EAAE;OACV,EACD;QACID,KAAK,EAAEL,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACrDqM,KAAK,EAAE;OACV,EACD;QACID,KAAK,EAAEL,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QACvDqM,KAAK,EAAE;OACV,CACJ;MACD,CAAC,EAAE,CACC;QACID,KAAK,EAAEL,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;QAC3DqM,KAAK,EAAE;OACV,EACD;QACID,KAAK,EAAEL,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACrDqM,KAAK,EAAE;OACV,EACD;QACID,KAAK,EAAEL,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QACvDqM,KAAK,EAAE;OACV,CACJ;MACD,CAAC,EAAE,CACC;QACID,KAAK,EAAEL,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACrDqM,KAAK,EAAE;OACV,EACD;QACID,KAAK,EAAEL,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QACvDqM,KAAK,EAAE;OACV;KAER;IACD,IAAI,CAACnG,gBAAgB,GAAG,CACpB;MACIkG,KAAK,EAAEL,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;MACpDqM,KAAK,EAAE;KACV,EACD;MACID,KAAK,EAAEL,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MACzDqM,KAAK,EAAE;KACV,EACD;MACID,KAAK,EAAEL,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC3DqM,KAAK,EAAE;KACV,EACD;MACID,KAAK,EAAEL,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACvDqM,KAAK,EAAE;KACV,EACD;MACID,KAAK,EAAEL,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;MACrDqM,KAAK,EAAE;KACV,CACJ;IACD,IAAI,CAAChM,UAAU,GAAG;MACdC,YAAY,EAAE,IAAI;MAClBgM,KAAK,EAAE,IAAI;MACX/J,YAAY,EAAE,IAAI;MAClBT,YAAY,EAAE,IAAI;MAClByF,IAAI,EAAE3I,SAAS,CAACsN,YAAY,CAACC,QAAQ;MACrCrG,MAAM,EAAE,IAAI;MACZ5C,GAAG,EAAE;KACR;IACD,IAAI,CAACqJ,OAAO,GAAG,CACX;MACIC,IAAI,EAAE,IAAI,CAACzM,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;MACzDyM,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI,CAACtF,QAAQ,CAACC,IAAI,IAAI3I,SAAS,CAACqN,SAAS,CAACY,KAAK;MACvDC,MAAM,EAAE;KACX,EACD;MACIN,IAAI,EAAE,IAAI,CAACzM,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;MAC9DyM,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZC,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAE;QACHC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;;KAErB,EAAE;MACCZ,IAAI,EAAE,IAAI,CAACzM,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/DyM,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZC,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAE;QACHC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;;KAErB,EAAE;MACCZ,IAAI,EAAE,IAAI,CAACzM,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/DyM,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE;KACX,EACD;MACIN,IAAI,EAAE,IAAI,CAACzM,WAAW,CAACC,SAAS,CAAC,sCAAsC,CAAC;MACxEyM,GAAG,EAAE,KAAK;MACVC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE;KACX,EACD;MACIN,IAAI,EAAE,IAAI,CAACzM,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;MACjEyM,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZC,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAE;QACHC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;;KAErB,EACD;MACIZ,IAAI,EAAE,IAAI,CAACzM,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC5DyM,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZO,eAAeA,CAAChB,KAAK;QACjB,OAAON,EAAE,CAACuB,WAAW,CAACC,mBAAmB,CAAC,IAAIC,IAAI,CAACnB,KAAK,CAAC,CAAC;MAC9D;KACH,EACD;MACIG,IAAI,EAAE,IAAI,CAACzM,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC5DyM,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZO,eAAeA,CAAChB,KAAK;QACjB,OAAOA,KAAK,GAAGN,EAAE,CAACuB,WAAW,CAACC,mBAAmB,CAAC,IAAIC,IAAI,CAACnB,KAAK,CAAC,CAAC,GAAG,EAAE;MAC3E;KACH,EACD;MACIG,IAAI,EAAE,IAAI,CAACzM,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;MACzDyM,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZC,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAE;QACHC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;;KAErB,EACD;MACIZ,IAAI,EAAE,IAAI,CAACzM,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MACvDyM,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZW,gBAAgB,EAAGpB,KAAK,IAAI;QACxB,IAAIA,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACC,GAAG,EAAE;UACvC,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,CAAC;SAC9E,MAAM,IAAItB,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACE,QAAQ,EAAE;UACnD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,iBAAiB,EAAE,cAAc,EAAE,cAAc,CAAC;SAClF,MAAM,IAAIvB,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACG,WAAW,EAAE;UACtD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,CAAC;SAChF,MAAM,IAAIxB,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACI,MAAM,EAAE;UACjD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,CAAC;SAC7E,MAAM,IAAIzB,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACK,IAAI,EAAE;UAC/C,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,CAAC;;QAEhF,OAAO,EAAE;MACb,CAAC;MACDV,eAAe,EAAE,SAAAA,CAAUhB,KAAK;QAC5B,IAAIA,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACC,GAAG,EAAE;UACvC,OAAO5B,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;SACvD,MAAM,IAAIqM,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACE,QAAQ,EAAE;UACnD,OAAO7B,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;SAC5D,MAAM,IAAIqM,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACG,WAAW,EAAE;UACtD,OAAO9B,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;SAC9D,MAAM,IAAIqM,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACI,MAAM,EAAE;UACjD,OAAO/B,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SAC1D,MAAM,IAAIqM,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACK,IAAI,EAAE;UAC/C,OAAOhC,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;;QAEzD,OAAO,EAAE;MACb;KACH,CACJ;IAED,IAAI,CAACgO,WAAW,GAAG;MACfC,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE,KAAK;MAC1BC,MAAM,EAAE,CACJ;QACIC,IAAI,EAAE,mBAAmB;QACzBC,OAAO,EAAE,IAAI,CAACxO,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACzDwO,IAAI,EAAE,SAAAA,CAAUxE,EAAE,EAAEyE,IAAI;UACpB1C,EAAE,CAAC2C,mBAAmB,CAAC1E,EAAE,EAAEyE,IAAI,CAAC;QACpC;OACH,EACD;QACIH,IAAI,EAAE,uBAAuB;QAC7BC,OAAO,EAAE,IAAI,CAACxO,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACzDwO,IAAI,EAAE,SAAAA,CAAUxE,EAAE,EAAEyE,IAAI;UACpB1C,EAAE,CAAC4C,iBAAiB,CAAC3E,EAAE,EAAEyE,IAAI,CAAC;QAClC,CAAC;QACDG,UAAU,EAAE,SAAAA,CAAU5E,EAAE,EAAEyE,IAAI;UAC1B;UACA,IAAI1C,EAAE,CAACzE,QAAQ,CAACC,IAAI,IAAI3I,SAAS,CAACqN,SAAS,CAACY,KAAK,IAAId,EAAE,CAACzE,QAAQ,CAACC,IAAI,IAAI3I,SAAS,CAACqN,SAAS,CAAC4C,QAAQ,IAAI9C,EAAE,CAACzE,QAAQ,CAACC,IAAI,IAAI3I,SAAS,CAACqN,SAAS,CAAC6C,QAAQ,EAAE;YACvJ,OAAO,KAAK;;UAChB;UACA,IAAI,CAACL,IAAI,CAACM,SAAS,IAAI,CAACN,IAAI,CAAC/G,UAAU,IAAIqE,EAAE,CAACiD,WAAW,CAAC,CAACpQ,SAAS,CAACqQ,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI;UAC7G;UACA,IAAIpD,EAAE,CAACzE,QAAQ,CAACC,IAAI,IAAI3I,SAAS,CAACqN,SAAS,CAACxE,QAAQ,IAAIgH,IAAI,CAACM,SAAS,IAAGK,SAAS,IAAKX,IAAI,CAACM,SAAS,IAAI,IAAI,IAAIN,IAAI,CAACM,SAAS,KAAKhD,EAAE,CAACzE,QAAQ,CAAC0C,EAAE,EAAE;YAChJ,OAAO,KAAK;;UAEhB;UACA,IAAI+B,EAAE,CAACzE,QAAQ,CAACC,IAAI,IAAI3I,SAAS,CAACqN,SAAS,CAACxE,QAAQ,IAAIsE,EAAE,CAACiD,WAAW,CAAC,CAACpQ,SAAS,CAACqQ,WAAW,CAACC,MAAM,CAACC,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,MACrH;YACD,OAAO,KAAK;;QAEpB;OACH;KAER;IACD,IAAI,CAACE,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,IAAI,GAAG,YAAY;IACxB,IAAI,CAACC,OAAO,GAAG;MACX9K,OAAO,EAAE,EAAE;MACX+K,KAAK,EAAE;KACV;IACD,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACjE,WAAW,CAACkE,KAAK,CAAC,IAAI,CAACtP,UAAU,CAAC;IAC/D,IAAI,CAAC2H,aAAa,GAAG,IAAI,CAACyD,WAAW,CAACkE,KAAK,CAAC,IAAI,CAAC7O,MAAM,CAAC;IACxD,IAAI,CAAC8O,eAAe,EAAE;IACtB,IAAI,CAACjI,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACkI,MAAM,CAAC,IAAI,CAACR,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAAClP,UAAU,CAAC;EAC3E;EAEAiG,cAAcA,CAAC+F,KAAK;IAChB,IAAIN,EAAE,GAAG,IAAI;IACb;MACI,IAAIM,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACC,GAAG,EAAE;QACvC,OAAO5B,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;OACvD,MAAM,IAAIqM,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACE,QAAQ,EAAE;QACnD,OAAO7B,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;OAC5D,MAAM,IAAIqM,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACG,WAAW,EAAE;QACtD,OAAO9B,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;OAC9D,MAAM,IAAIqM,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACI,MAAM,EAAE;QACjD,OAAO/B,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;OAC1D,MAAM,IAAIqM,KAAK,IAAIzN,SAAS,CAAC8O,cAAc,CAACK,IAAI,EAAE;QAC/C,OAAOhC,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;;MAEzD,OAAO,EAAE;;EAEjB;EAEA8P,YAAYA,CAACzD,KAAK;IACd,IAAIN,EAAE,GAAG,IAAI;IACb;IACA,OAAOA,EAAE,CAACuB,WAAW,CAACC,mBAAmB,CAAC,IAAIC,IAAI,CAACnB,KAAK,CAAC,CAAC;EAC9D;EAEAwD,MAAMA,CAACE,IAAI,EAAEC,KAAK,EAAET,IAAI,EAAEU,MAAM;IAC5B,IAAIlE,EAAE,GAAG,IAAI;IACb,IAAI,CAACsD,UAAU,GAAGU,IAAI;IACtB,IAAI,CAACT,QAAQ,GAAGU,KAAK;IACrB,IAAI,CAACT,IAAI,GAAGA,IAAI;IAChB,IAAIW,UAAU,GAAG;MACbH,IAAI;MACJrD,IAAI,EAAEsD,KAAK;MACXT;KACH;IACDY,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/P,UAAU,CAAC,CAACgQ,OAAO,CAAC5D,GAAG,IAAG;MACvC,IAAI,IAAI,CAACpM,UAAU,CAACoM,GAAG,CAAC,IAAI,IAAI,EAAE;QAC9ByD,UAAU,CAACzD,GAAG,CAAC,GAAG,IAAI,CAACpM,UAAU,CAACoM,GAAG,CAAC;;IAE9C,CAAC,CAAC;IACF,IAAI,CAAC+C,OAAO,GAAG;MACX9K,OAAO,EAAE,EAAE;MACX+K,KAAK,EAAE;KACV;IACD1D,EAAE,CAACuE,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI,CAACjF,aAAa,CAACkF,YAAY,CAACN,UAAU,EAAGO,QAAQ,IAAI;MACrD1E,EAAE,CAACyD,OAAO,GAAG;QACT9K,OAAO,EAAE+L,QAAQ,CAAC/L,OAAO;QACzB+K,KAAK,EAAEgB,QAAQ,CAACC;OACnB;MACD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACJ,CAAC,EAAE,IAAI,EAAE,MAAK;MACV3E,EAAE,CAACuE,oBAAoB,CAACK,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACP,IAAI,CAAC9P,MAAM,GAAG;MACVkJ,EAAE,EAAE,IAAI;MACR9I,WAAW,EAAE,IAAI;MACjBY,YAAY,EAAE,IAAI;MAClBS,YAAY,EAAE,IAAI;MAClBmC,OAAO,EAAE,IAAI;MACbU,IAAI,EAAE,IAAI;MACVmD,KAAK,EAAE,IAAI;MACXhB,IAAI,EAAE3I,SAAS,CAACsN,YAAY,CAACC,QAAQ;MACrCjJ,GAAG,EAAE,IAAI;MACT4C,MAAM,EAAE,IAAI;MACZE,SAAS,EAAE,IAAI;MACf0B,UAAU,EAAE,IAAI;MAChBpH,YAAY,EAAE;KACjB;IACD,IAAI,CAAC2K,qBAAqB,GAAG,KAAK;EACtC;EAEA4F,cAAcA,CAAA;IACV,IAAI,CAACxB,UAAU,GAAG,CAAC;IACnB,IAAI,CAACQ,MAAM,CAAC,IAAI,CAACR,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAAClP,UAAU,CAAC;EAC3E;EAEAuP,eAAeA,CAAA;IACX,IAAI,CAACrE,cAAc,CAACqE,eAAe,CAAEa,QAAQ,IAAI;MAC7C,IAAI,CAAC/P,YAAY,GAAG+P,QAAQ,CAACK,GAAG,CAACC,EAAE,IAAG;QAClC,OAAO;UACH,GAAGA,EAAE;UACL9D,OAAO,EAAE,GAAG8D,EAAE,CAACC,IAAI,MAAMD,EAAE,CAACvE,IAAI;SACnC;MACL,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEA3L,eAAeA,CAACP,YAAY;IACxB,MAAM2Q,QAAQ,GAAG,IAAI,CAACvQ,YAAY,CAACwQ,IAAI,CAACH,EAAE,IAAIA,EAAE,CAACC,IAAI,KAAK1Q,YAAY,CAAC;IACvE,OAAO2Q,QAAQ,GAAGA,QAAQ,CAACD,IAAI,GAAG,KAAK,GAAGC,QAAQ,CAACzE,IAAI,GAAG,EAAE;EAChE;EAEA;EACA2E,qBAAqBA,CAAA;IACjB,IAAI,IAAI,CAACb,oBAAoB,CAACc,SAAS,IAAI,IAAI,IAAI,IAAI,CAACtG,mBAAmB,IAAI,KAAK,EAAE;IACtF,IAAIiB,EAAE,GAAG,IAAI;IACb,IAAI,CAACuE,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAI,IAAI,CAACxM,WAAW,IAAI,QAAQ,EAAE;MAC9B,IAAIsN,QAAQ,GAAG;QACXnQ,WAAW,EAAE,IAAI,CAACJ,MAAM,CAACI,WAAW;QACpCY,YAAY,EAAE,IAAI,CAAChB,MAAM,CAACgB,YAAY;QACtCS,YAAY,EAAE,IAAI,CAACzB,MAAM,CAACyB,YAAY;QACtCmC,OAAO,EAAE,IAAI,CAAC5D,MAAM,CAAC4D,OAAO;QAC5BU,IAAI,EAAE,IAAI,CAACtE,MAAM,CAACsE,IAAI;QACtBmC,IAAI,EAAE,IAAI,CAACzG,MAAM,CAACyG,IAAI;QACtBrE,GAAG,EAAE,IAAI,CAACpC,MAAM,CAACyG,IAAI,IAAI3I,SAAS,CAACsN,YAAY,CAACC,QAAQ,GAAG,IAAI,CAACrL,MAAM,CAACoC,GAAG,GAAG;OAChF;MACD,IAAImO,QAAQ,CAAC9O,YAAY,IAAI,IAAI,EAAE;QAC/B,IAAI8O,QAAQ,CAAC9O,YAAY,CAAC+O,UAAU,CAAC,GAAG,CAAC,EAAE;UACvCD,QAAQ,CAAC9O,YAAY,GAAG,IAAI,GAAG8O,QAAQ,CAAC9O,YAAY,CAACgP,SAAS,CAAC,CAAC,EAAEF,QAAQ,CAAC9O,YAAY,CAAC2I,MAAM,CAAC;SAClG,MAAM,IAAImG,QAAQ,CAAC9O,YAAY,CAAC2I,MAAM,IAAI,CAAC,IAAImG,QAAQ,CAAC9O,YAAY,CAAC2I,MAAM,IAAI,EAAE,EAAE;UAChFmG,QAAQ,CAAC9O,YAAY,GAAG,IAAI,GAAG8O,QAAQ,CAAC9O,YAAY;;;MAG5D,IAAI,CAAC+I,aAAa,CAACkG,YAAY,CAACH,QAAQ,EAAGI,IAAI,IAAI;QAC/C1F,EAAE,CAACuE,oBAAoB,CAACoB,OAAO,CAAC3F,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QACvF+L,EAAE,CAACjB,mBAAmB,GAAG,KAAK;QAC9BiB,EAAE,CAAC8D,MAAM,CAAC9D,EAAE,CAACsD,UAAU,EAAEtD,EAAE,CAACuD,QAAQ,EAAEvD,EAAE,CAACwD,IAAI,EAAExD,EAAE,CAAC1L,UAAU,CAAC;QAC7D;QACA;QACA0L,EAAE,CAACT,aAAa,CAACqG,qBAAqB,CAAC5F,EAAE,CAACzE,QAAQ,CAAChH,YAAY,EAAGsR,KAAK,IAAI;UACvE,IAAIC,KAAK,GAAG,EAAE;UACd,KAAK,IAAIC,IAAI,IAAIF,KAAK,CAACG,UAAU,EAAE;YAC/BF,KAAK,CAACG,IAAI,CAAC;cACPC,MAAM,EAAEH,IAAI,CAACG,MAAM;cACnBC,QAAQ,EAAET,IAAI,CAACzH;aAClB,CAAC;;UAEN,IAAIyH,IAAI,EAAE/J,UAAU,EAAE;YAClBmK,KAAK,CAACG,IAAI,CAAC;cACPC,MAAM,EAAER,IAAI,CAAC/J,UAAU;cACvBwK,QAAQ,EAAET,IAAI,CAACzH;aAClB,CAAC;;UAEN+B,EAAE,CAACT,aAAa,CAAC6G,cAAc,CAACN,KAAK,CAAC;QAC1C,CAAC,CAAC;MACN,CAAC,EAAE,IAAI,EAAE,MAAK;QACV9F,EAAE,CAACuE,oBAAoB,CAACK,OAAO,EAAE;MACrC,CAAC,CAAC;KACL,MAAM,IAAI,IAAI,CAAC5M,WAAW,IAAI,QAAQ,EAAE;MACrC,IAAIsN,QAAQ,GAAG;QACXnQ,WAAW,EAAE,IAAI,CAACJ,MAAM,CAACI,WAAW;QACpCY,YAAY,EAAE,IAAI,CAAChB,MAAM,CAACgB,YAAY;QACtCS,YAAY,EAAE,IAAI,CAACzB,MAAM,CAACyB,YAAY;QACtCmC,OAAO,EAAE,IAAI,CAAC5D,MAAM,CAAC4D,OAAO;QAC5BU,IAAI,EAAE,IAAI,CAACtE,MAAM,CAACsE,IAAI;QACtBmC,IAAI,EAAE,IAAI,CAACzG,MAAM,CAACyG,IAAI;QACtBrE,GAAG,EAAE,IAAI,CAACpC,MAAM,CAACyG,IAAI,IAAI3I,SAAS,CAACsN,YAAY,CAACC,QAAQ,GAAG,IAAI,CAACrL,MAAM,CAACoC,GAAG,GAAG,IAAI;QACjF4C,MAAM,EAAE,IAAI,CAAChF,MAAM,CAACgF,MAAM;QAC1ByC,KAAK,EAAE,IAAI,CAACzH,MAAM,CAACyH,KAAK;QACxBb,UAAU,EAAE,IAAI,CAAC5G,MAAM,CAAC4G,UAAU;QAClC0K,OAAO,EAAE,IAAI,CAAC3H;OACjB;MACD,IAAI4G,QAAQ,CAAC9O,YAAY,IAAI,IAAI,EAAE;QAC/B,IAAI8O,QAAQ,CAAC9O,YAAY,CAAC+O,UAAU,CAAC,GAAG,CAAC,EAAE;UACvCD,QAAQ,CAAC9O,YAAY,GAAG,IAAI,GAAG8O,QAAQ,CAAC9O,YAAY,CAACgP,SAAS,CAAC,CAAC,EAAEF,QAAQ,CAAC9O,YAAY,CAAC2I,MAAM,CAAC;SAClG,MAAM,IAAImG,QAAQ,CAAC9O,YAAY,CAAC2I,MAAM,IAAI,CAAC,IAAImG,QAAQ,CAAC9O,YAAY,CAAC2I,MAAM,IAAI,EAAE,EAAE;UAChFmG,QAAQ,CAAC9O,YAAY,GAAG,IAAI,GAAG8O,QAAQ,CAAC9O,YAAY;;;MAG5D;MACA,IAAI,CAAC+I,aAAa,CAAC+G,YAAY,CAAC,IAAI,CAACvR,MAAM,CAACkJ,EAAE,EAAEqH,QAAQ,EAAGI,IAAI,IAAI;QAC/D1F,EAAE,CAACjB,mBAAmB,GAAG,KAAK;QAC9BiB,EAAE,CAACuE,oBAAoB,CAACoB,OAAO,CAAC3F,EAAE,CAAChM,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QACvF+L,EAAE,CAAC8D,MAAM,CAAC9D,EAAE,CAACsD,UAAU,EAAEtD,EAAE,CAACuD,QAAQ,EAAEvD,EAAE,CAACwD,IAAI,EAAExD,EAAE,CAAC1L,UAAU,CAAC;QAC7D,IAAIoR,IAAI,CAAC/J,UAAU,IAAI,IAAI,IAAI+J,IAAI,CAAC/J,UAAU,IAAI0H,SAAS,EAAE;UACzDrD,EAAE,CAACT,aAAa,CAAC6G,cAAc,CAAC,CAAC;YAC7BF,MAAM,EAAER,IAAI,CAAC/J,UAAU;YACvBwK,QAAQ,EAAET,IAAI,CAACzH;WAClB,CAAC,CAAC;;MAGX,CAAC,EAAE,IAAI,EAAE,MAAK;QACV+B,EAAE,CAACuE,oBAAoB,CAACK,OAAO,EAAE;MACrC,CAAC,CAAC;;EAEV;EAEAhR,eAAeA,CAAA;IACX,IAAI,CAACmL,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC/G,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAAC8H,UAAU,GAAG,IAAI,CAAC9L,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;IAC1E,IAAI,CAAC4Q,WAAW,EAAE;IAClB;IACA,IAAI,IAAI,CAACtJ,QAAQ,CAACC,IAAI,KAAK3I,SAAS,CAACqN,SAAS,CAAC4C,QAAQ,EAAE;MACrD,IAAI,CAAC/N,MAAM,CAACI,WAAW,GAAG,IAAI,CAACoG,QAAQ,CAACgL,QAAQ,CAACf,SAAS,CAAC,CAAC,EAAE,IAAI,CAACnQ,oBAAoB,CAAC;MACxF,IAAI,CAACN,MAAM,CAACyB,YAAY,GAAG,IAAI,CAAC+E,QAAQ,CAACiL,KAAK;MAC9C,IAAI,CAACzR,MAAM,CAACgB,YAAY,GAAG,IAAI,CAACwF,QAAQ,CAACgF,KAAK;;IAElD,IAAI,CAACtE,aAAa,GAAG,IAAI,CAACyD,WAAW,CAACkE,KAAK,CAAC,IAAI,CAAC7O,MAAM,CAAC;EAC5D;EAEA6N,iBAAiBA,CAAC3E,EAAE,EAAEyE,IAAI;IACtB,IAAI1C,EAAE,GAAG,IAAI;IACb,IAAI,CAAC/D,aAAa,CAACwK,KAAK,EAAE;IAC1B,IAAI,CAAC3G,UAAU,GAAG,IAAI,CAAC9L,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;IAC1E,IAAI,CAAC+D,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAAC+G,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACQ,aAAa,CAACmH,eAAe,CAAChE,IAAI,CAACzE,EAAE,EAAGyH,IAAI,IAAI;MACjD1F,EAAE,CAACjL,MAAM,GAAG;QACRkJ,EAAE,EAAEyH,IAAI,CAACzH,EAAE;QACX9I,WAAW,EAAEuQ,IAAI,CAACvQ,WAAW;QAC7BY,YAAY,EAAE2P,IAAI,CAAC3P,YAAY;QAC/BS,YAAY,EAAEkP,IAAI,CAAClP,YAAY;QAC/BmC,OAAO,EAAE+M,IAAI,CAAC/M,OAAO;QACrBU,IAAI,EAAEqM,IAAI,CAACrM,IAAI;QACfmD,KAAK,EAAEkJ,IAAI,CAAClJ,KAAK;QACjBhB,IAAI,EAAEkK,IAAI,CAAClK,IAAI;QACfrE,GAAG,EAAEuO,IAAI,CAACvO,GAAG;QACb4C,MAAM,EAAE,IAAI;QACZE,SAAS,EAAEyL,IAAI,CAAC3L,MAAM;QACtB4B,UAAU,EAAE+J,IAAI,CAAC/J,UAAU;QAC3BpH,YAAY,EAAEmR,IAAI,CAACnR;OACtB;MACDyL,EAAE,CAACJ,SAAS,GAAG;QAAC,GAAGI,EAAE,CAACjL;MAAM,CAAC;MAC7BiL,EAAE,CAAC/D,aAAa,GAAG+D,EAAE,CAACN,WAAW,CAACkE,KAAK,CAAC5D,EAAE,CAACjL,MAAM,CAAC;MAClD;MACA,IAAI,CAAC0K,sBAAsB,CAACqE,MAAM,CAAC;QAACqC,QAAQ,EAAEzD,IAAI,CAACzE;MAAE,CAAC,EAAG0I,GAAG,IAAI;QAC5D;QACA,IAAI,CAACjI,SAAS,GAAGiI,GAAG,CAAChO,OAAO;QAC5B;QACA;QACA;QACA,IAAI,CAAC+F,SAAS,CAAC4F,OAAO,CAACjL,IAAI,IAAG;UAC1B,IAAI,CAAC2E,OAAO,CAAC3E,IAAI,CAAC4E,EAAE,CAAC,GAAG,IAAI,CAACyB,WAAW,CAACkE,KAAK,CAAC;YAC3CjL,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC3F,UAAU,CAACuF,QAAQ,EAAEvF,UAAU,CAACsL,SAAS,CAAC,GAAG,CAAC,EAAE,IAAI,CAACsI,qBAAqB,EAAE,CAAC;WAC/F,CAAC;QACN,CAAC,CAAC;QACF5G,EAAE,CAACH,eAAe,GAAG,IAAI;MAC7B,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAACN,aAAa,CAACqG,qBAAqB,CAAC5F,EAAE,CAACzE,QAAQ,CAAChH,YAAY,EAAGmR,IAAI,IAAI;MACxE1F,EAAE,CAAC6G,SAAS,GAAGnB,IAAI,CAACM,UAAU;IAClC,CAAC,CAAC;EACN;EAEArD,mBAAmBA,CAAC1E,EAAE,EAAEyE,IAAI;IACxB,IAAI1C,EAAE,GAAG,IAAI;IACb,IAAI,CAAC/D,aAAa,CAACwK,KAAK,EAAE;IAC1B,IAAI,CAAC3G,UAAU,GAAG,IAAI,CAAC9L,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;IAC/E,IAAI,CAAC+D,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAAC+G,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACQ,aAAa,CAACmH,eAAe,CAAChE,IAAI,CAACzE,EAAE,EAAGyH,IAAI,IAAI;MACjD1F,EAAE,CAACjL,MAAM,GAAG;QACRkJ,EAAE,EAAEyH,IAAI,CAACzH,EAAE;QACX9I,WAAW,EAAEuQ,IAAI,CAACvQ,WAAW;QAC7BY,YAAY,EAAE2P,IAAI,CAAC3P,YAAY;QAC/BS,YAAY,EAAEkP,IAAI,CAAClP,YAAY;QAC/BmC,OAAO,EAAE+M,IAAI,CAAC/M,OAAO;QACrBU,IAAI,EAAEqM,IAAI,CAACrM,IAAI;QACfmD,KAAK,EAAEkJ,IAAI,CAAClJ,KAAK;QACjBhB,IAAI,EAAEkK,IAAI,CAAClK,IAAI;QACfrE,GAAG,EAAEuO,IAAI,CAACvO,GAAG;QACb4C,MAAM,EAAE,IAAI;QACZE,SAAS,EAAEyL,IAAI,CAAC3L,MAAM;QACtB4B,UAAU,EAAE+J,IAAI,CAAC/J,UAAU;QAC3BpH,YAAY,EAAEmR,IAAI,CAACnR;OAEtB;MACDyL,EAAE,CAACJ,SAAS,GAAG;QAAC,GAAGI,EAAE,CAACjL;MAAM,CAAC;MAC7BiL,EAAE,CAAC/D,aAAa,GAAG+D,EAAE,CAACN,WAAW,CAACkE,KAAK,CAAC5D,EAAE,CAACjL,MAAM,CAAC;MAClD;MACA,IAAI,CAAC0K,sBAAsB,CAACqE,MAAM,CAAC;QAACqC,QAAQ,EAAEzD,IAAI,CAACzE;MAAE,CAAC,EAAG0I,GAAG,IAAI;QAC5D;QACA,IAAI,CAACjI,SAAS,GAAGiI,GAAG,CAAChO,OAAO;QAC5B,KAAK,IAAIU,IAAI,IAAI,IAAI,CAACqF,SAAS,EAAE;UAC7B,IAAI,CAACV,OAAO,CAAC3E,IAAI,CAAC4E,EAAE,CAAC,GAAG,IAAI,CAACyB,WAAW,CAACkE,KAAK,CAACvK,IAAI,CAAC;;QAExD2G,EAAE,CAACH,eAAe,GAAG,IAAI;MAC7B,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAACN,aAAa,CAACqG,qBAAqB,CAAC5F,EAAE,CAACzE,QAAQ,CAAChH,YAAY,EAAGmR,IAAI,IAAI;MACxE1F,EAAE,CAAC6G,SAAS,GAAGnB,IAAI,CAACM,UAAU;IAClC,CAAC,CAAC;EACN;EAEArP,gBAAgBA,CAACmQ,KAAK;IAClB,IAAIA,KAAK,CAACC,OAAO,EAAE;MACf;;IAEJ,IAAID,KAAK,CAACE,OAAO,IAAI,CAAC,IAAIF,KAAK,CAACE,OAAO,IAAI,EAAE,IAAIF,KAAK,CAACE,OAAO,IAAI,EAAE,IAAIF,KAAK,CAACE,OAAO,IAAI,EAAE,EAAE;MACzF;;IAEJ,IAAIF,KAAK,CAACE,OAAO,GAAG,EAAE,IAAIF,KAAK,CAACE,OAAO,GAAG,EAAE,EAAE;MAC1CF,KAAK,CAACG,cAAc,EAAE;;IAE1B;IACA,IAAIH,KAAK,CAACE,OAAO,IAAI,EAAE,IAAIF,KAAK,CAACE,OAAO,IAAI,GAAG,IAAIF,KAAK,CAACE,OAAO,IAAI,GAAG,IAAIF,KAAK,CAACE,OAAO,IAAI,GAAG,EAAE;MAC7FF,KAAK,CAACG,cAAc,EAAE;;EAE9B;EAEAC,UAAUA,CAAC/P,GAAG;IACV,IAAIgQ,KAAK,GAAG,iBAAiB;IAC7B;IACA,IAAI,CAACA,KAAK,CAACC,IAAI,CAACjQ,GAAG,CAAC,EAAE;MAClB,IAAI,CAAC+H,qBAAqB,GAAG,KAAK;MAClC;KACH,MAAM;MACH,IAAI,CAACA,qBAAqB,GAAG,IAAI;;EAEzC;EAEAE,WAAWA,CAAA;IACP,OAAOgF,MAAM,CAACiD,MAAM,CAAC,IAAI,CAACrJ,OAAO,CAAC,CAACsJ,KAAK,CAAEC,SAAoB,IAAKA,SAAS,CAACC,KAAK,CAAC;EACvF;EAEAZ,qBAAqBA,CAAA;IACjB,OAAQa,OAAwB,IAA6B;MACzD,MAAMC,YAAY,GAAG,CAACD,OAAO,CAACnH,KAAK,IAAI,EAAE,EAAEtD,IAAI,EAAE,CAACmC,MAAM,KAAK,CAAC;MAC9D,MAAMwI,OAAO,GAAG,CAACD,YAAY;MAC7B,OAAOC,OAAO,GAAG,IAAI,GAAG;QAACC,UAAU,EAAE;MAAI,CAAC;IAC9C,CAAC;EACL;EAEApO,aAAaA,CAACsN,KAAK;IACf,IAAIA,KAAK,CAACpG,GAAG,KAAK,GAAG,KAAK,IAAI,CAAC3L,MAAM,CAACsE,IAAI,IAAI,IAAI,IAAI,IAAI,CAACtE,MAAM,CAACsE,IAAI,IAAI,IAAI,IAAI,IAAI,CAACtE,MAAM,CAACsE,IAAI,CAAC2D,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MAC/G8J,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAI,IAAI,CAAClS,MAAM,CAACsE,IAAI,IAAI,IAAI,IAAI,IAAI,CAACtE,MAAM,CAACsE,IAAI,CAAC2D,IAAI,EAAE,IAAI,EAAE,EAAE;MAC3D,IAAI,CAACjI,MAAM,CAACsE,IAAI,GAAG,IAAI,CAACtE,MAAM,CAACsE,IAAI,CAACwO,SAAS,EAAE,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MACvE;;EAER;EAEAhP,gBAAgBA,CAACgO,KAAK;IAClB,IAAIA,KAAK,CAACpG,GAAG,KAAK,GAAG,KAAK,IAAI,CAAC3L,MAAM,CAAC4D,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC5D,MAAM,CAAC4D,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC5D,MAAM,CAAC4D,OAAO,CAACqE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MACxH8J,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAI,IAAI,CAAClS,MAAM,CAAC4D,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC5D,MAAM,CAAC4D,OAAO,CAACqE,IAAI,EAAE,IAAI,EAAE,EAAE;MACjE,IAAI,CAACjI,MAAM,CAAC4D,OAAO,GAAG,IAAI,CAAC5D,MAAM,CAAC4D,OAAO,CAACkP,SAAS,EAAE,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MAC7E;;EAER;EAEAtK,oBAAoBA,CAACsJ,KAAoB,EAAEzN,IAAS;IAChD,IAAIyN,KAAK,CAACpG,GAAG,KAAK,GAAG,KAAK,CAACrH,IAAI,CAACV,OAAO,IAAIU,IAAI,CAACV,OAAO,CAACqE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MACpE8J,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAI5N,IAAI,CAACV,OAAO,IAAIU,IAAI,CAACV,OAAO,CAACqE,IAAI,EAAE,KAAK,EAAE,EAAE;MAC5C3D,IAAI,CAACV,OAAO,GAAGU,IAAI,CAACV,OAAO,CAACkP,SAAS,EAAE,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MAC/D;;EAER;EAEAnL,cAAcA,CAACmK,KAAK;IAChB,IAAIA,KAAK,CAACpG,GAAG,KAAK,GAAG,KAAK,IAAI,CAAC3L,MAAM,CAACyH,KAAK,IAAI,IAAI,IAAI,IAAI,CAACzH,MAAM,CAACyH,KAAK,IAAI,IAAI,IAAI,IAAI,CAACzH,MAAM,CAACyH,KAAK,CAACQ,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MAClH8J,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAI,IAAI,CAAClS,MAAM,CAACyH,KAAK,IAAI,IAAI,IAAI,IAAI,CAACzH,MAAM,CAACyH,KAAK,CAACQ,IAAI,EAAE,IAAI,EAAE,EAAE;MAC7D,IAAI,CAACjI,MAAM,CAACyH,KAAK,GAAG,IAAI,CAACzH,MAAM,CAACyH,KAAK,CAACqL,SAAS,EAAE,CAACC,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MACzE;;EAER;EAEAxQ,oBAAoBA,CAACnD,MAAM;IACvB,IAAIA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKkP,SAAS,EAAE;MACzC,IAAI,CAAC6D,UAAU,CAAC/S,MAAM,CAAC;MACvB,IAAI,IAAI,CAAC+K,qBAAqB,EAAE;QAC5B,IAAI,CAACnK,MAAM,CAACoC,GAAG,GAAGhD,MAAM;;KAE/B,MAAM;MACH,IAAI,CAAC+K,qBAAqB,GAAG,KAAK;;EAE1C;;;uBAhxBSG,2BAA2B,EAAAlM,EAAA,CAAA4U,iBAAA,CA2DxBnV,aAAa,GAAAO,EAAA,CAAA4U,iBAAA,CACbhV,cAAc,GAAAI,EAAA,CAAA4U,iBAAA,CACd9U,sBAAsB,GAAAE,EAAA,CAAA4U,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9U,EAAA,CAAA4U,iBAAA,CAAA5U,EAAA,CAAA+U,QAAA;IAAA;EAAA;;;YA7DzB7I,2BAA2B;MAAA8I,SAAA;MAAAC,QAAA,GAAAjV,EAAA,CAAAkV,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvBxCxV,EAAA,CAAAC,cAAA,aAAqG;UAEzDD,EAAA,CAAAqB,MAAA,GAAmD;UAAArB,EAAA,CAAAU,YAAA,EAAM;UAC7FV,EAAA,CAAA+E,SAAA,sBAAoF;UACxF/E,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,aAAwE;UACpED,EAAA,CAAAwE,UAAA,IAAAkR,+CAAA,sBAIW;UACf1V,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,cAAoG;UAA/DD,EAAA,CAAAE,UAAA,sBAAAyV,8DAAA;YAAA,OAAYF,GAAA,CAAA9D,cAAA,EAAgB;UAAA,EAAC;UAC9D3R,EAAA,CAAAC,cAAA,iBAAoF;UAG5ED,EAAA,CAAAwE,UAAA,KAAAoR,2CAAA,iBAcM;UAEN5V,EAAA,CAAAC,cAAA,eAAmB;UAKCD,EAAA,CAAAE,UAAA,2BAAA2V,0EAAA7U,MAAA;YAAA,OAAAyU,GAAA,CAAAtU,UAAA,CAAAyF,MAAA,GAAA5F,MAAA;UAAA,EAA+B;UAQ1ChB,EAAA,CAAAU,YAAA,EAAa;UACdV,EAAA,CAAAC,cAAA,iBAAwB;UAAAD,EAAA,CAAAqB,MAAA,IAAkD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAI1FV,EAAA,CAAAC,cAAA,eAAmB;UAIJD,EAAA,CAAAE,UAAA,2BAAA4V,qEAAA9U,MAAA;YAAA,OAAAyU,GAAA,CAAAtU,UAAA,CAAAyB,YAAA,GAAA5B,MAAA;UAAA,EAAqC;UAF5ChB,EAAA,CAAAU,YAAA,EAIE;UACFV,EAAA,CAAAC,cAAA,iBAAuB;UAAAD,EAAA,CAAAqB,MAAA,IAA0D;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAIjGV,EAAA,CAAAC,cAAA,eAAmB;UAIJD,EAAA,CAAAE,UAAA,2BAAA6V,qEAAA/U,MAAA;YAAA,OAAAyU,GAAA,CAAAtU,UAAA,CAAAkC,YAAA,GAAArC,MAAA;UAAA,EAAqC,qBAAAgV,+DAAAhV,MAAA;YAAA,OAG1ByU,GAAA,CAAAjS,gBAAA,CAAAxC,MAAA,CAAwB;UAAA,EAHE;UAF5ChB,EAAA,CAAAU,YAAA,EAOE;UACFV,EAAA,CAAAC,cAAA,iBAAkD;UAAAD,EAAA,CAAAqB,MAAA,IAA0D;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAG5HV,EAAA,CAAAC,cAAA,eAAmB;UAIJD,EAAA,CAAAE,UAAA,2BAAA+V,qEAAAjV,MAAA;YAAA,OAAAyU,GAAA,CAAAtU,UAAA,CAAA6C,GAAA,GAAAhD,MAAA;UAAA,EAA4B,qBAAAkV,+DAAAlV,MAAA;YAAA,OAGjByU,GAAA,CAAAjS,gBAAA,CAAAxC,MAAA,CAAwB;UAAA,EAHP;UAFnChB,EAAA,CAAAU,YAAA,EAOE;UACFV,EAAA,CAAAC,cAAA,iBAA0C;UAAAD,EAAA,CAAAqB,MAAA,IAAmE;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAG7HV,EAAA,CAAAC,cAAA,eAAwB;UACpBD,EAAA,CAAA+E,SAAA,oBAGY;UAChB/E,EAAA,CAAAU,YAAA,EAAM;UAKlBV,EAAA,CAAA+E,SAAA,sBAYc;UAEd/E,EAAA,CAAAC,cAAA,eAAqD;UAEvCD,EAAA,CAAAE,UAAA,2BAAAiW,wEAAAnV,MAAA;YAAA,OAAAyU,GAAA,CAAA7J,mBAAA,GAAA5K,MAAA;UAAA,EAAiC;UAGvChB,EAAA,CAAAC,cAAA,gBAAoF;UAArCD,EAAA,CAAAE,UAAA,sBAAAkW,+DAAA;YAAA,OAAYX,GAAA,CAAAxD,qBAAA,EAAuB;UAAA,EAAC;UAC/EjS,EAAA,CAAAC,cAAA,eAAoE;UAChED,EAAA,CAAAwE,UAAA,KAAA6R,2CAAA,kBAOM;UAENrW,EAAA,CAAAC,cAAA,eAA0C;UAESD,EAAA,CAAAqB,MAAA,IAC5C;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UACXV,EAAA,CAAAC,cAAA,eAA6E;UACzED,EAAA,CAAAwE,UAAA,KAAA8R,6CAAA,oBASE;UACFtW,EAAA,CAAAwE,UAAA,KAAA+R,4CAAA,mBAAgG;UACpGvW,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA+E,SAAA,iBAAwE;UACxE/E,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAwE,UAAA,KAAAgS,6CAAA,oBAC6K;UAC7KxW,EAAA,CAAAwE,UAAA,KAAAiS,6CAAA,oBAC+I;UAC/IzW,EAAA,CAAAwE,UAAA,KAAAkS,6CAAA,oBACuI;UAC3I1W,EAAA,CAAAU,YAAA,EAAM;UAIVV,EAAA,CAAAC,cAAA,eAA0C;UAESD,EAAA,CAAAqB,MAAA,IAC3C;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UACZV,EAAA,CAAAC,cAAA,eAA6E;UACzED,EAAA,CAAAwE,UAAA,KAAAmS,6CAAA,oBASE;UACF3W,EAAA,CAAAwE,UAAA,KAAAoS,4CAAA,mBAAiG;UACrG5W,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA+E,SAAA,iBAAqE;UACrE/E,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAwE,UAAA,KAAAqS,6CAAA,oBAC+K;UAC/K7W,EAAA,CAAAwE,UAAA,KAAAsS,6CAAA,oBACgJ;UAChJ9W,EAAA,CAAAwE,UAAA,KAAAuS,6CAAA,oBACqI;UAEzI/W,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAA0C;UAEXD,EAAA,CAAAqB,MAAA,IAA0D;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAC7FV,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAwE,UAAA,KAAAwS,6CAAA,oBAUE;UACFhX,EAAA,CAAAwE,UAAA,KAAAyS,4CAAA,mBAAiG;UACrGjX,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA+E,SAAA,iBAAqE;UACrE/E,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAwE,UAAA,KAAA0S,6CAAA,oBAC+K;UAC/KlX,EAAA,CAAAwE,UAAA,KAAA2S,6CAAA,oBACqI;UACzInX,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAwE,UAAA,KAAA4S,2CAAA,kBAwBM;UAENpX,EAAA,CAAAwE,UAAA,KAAA6S,2CAAA,kBAWM;UAGNrX,EAAA,CAAAC,cAAA,eAA0C;UAEUD,EAAA,CAAAqB,MAAA,IAA4D;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UACpHV,EAAA,CAAAC,cAAA,eAAiB;UACTD,EAAA,CAAAwE,UAAA,KAAA8S,gDAAA,uBASY;UAChBtX,EAAA,CAAAwE,UAAA,KAAA+S,4CAAA,mBAC0F;UAC9FvX,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA+E,SAAA,iBAAuE;UACvE/E,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAwE,UAAA,KAAAgT,6CAAA,oBAC2I;UAC/IxX,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAA0C;UAEUD,EAAA,CAAAqB,MAAA,IAAgD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UACxGV,EAAA,CAAAC,cAAA,eAAiB;UACTD,EAAA,CAAAwE,UAAA,KAAAiT,gDAAA,uBASY;UAChBzX,EAAA,CAAAwE,UAAA,KAAAkT,4CAAA,mBACuF;UAC3F1X,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAA+E,SAAA,iBAAoE;UACpE/E,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAwE,UAAA,KAAAmT,6CAAA,oBACwI;UAC5I3X,EAAA,CAAAU,YAAA,EAAM;UAoCVV,EAAA,CAAAwE,UAAA,KAAAoT,2CAAA,mBA4BM;UAEN5X,EAAA,CAAAwE,UAAA,KAAAqT,2CAAA,kBAOM;UACV7X,EAAA,CAAAU,YAAA,EAAM;UAENV,EAAA,CAAAwE,UAAA,KAAAsT,2CAAA,kBAiBM;UAEN9X,EAAA,CAAAwE,UAAA,KAAAuT,2CAAA,kBAQM;UAGN/X,EAAA,CAAAwE,UAAA,MAAAwT,4CAAA,kBAuCM;UAENhY,EAAA,CAAAwE,UAAA,MAAAyT,4CAAA,kBAMM;UACVjY,EAAA,CAAAU,YAAA,EAAO;;;UAjd6BV,EAAA,CAAAsB,SAAA,GAAmD;UAAnDtB,EAAA,CAAAyB,iBAAA,CAAAgU,GAAA,CAAA5U,WAAA,CAAAC,SAAA,yBAAmD;UAChDd,EAAA,CAAAsB,SAAA,GAAe;UAAftB,EAAA,CAAAW,UAAA,UAAA8U,GAAA,CAAAyC,KAAA,CAAe,SAAAzC,GAAA,CAAA0C,IAAA;UAI3CnY,EAAA,CAAAsB,SAAA,GAA8F;UAA9FtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAArN,QAAA,CAAAC,IAAA,IAAAoN,GAAA,CAAAnN,QAAA,CAAAqH,QAAA,IAAA8F,GAAA,CAAA3F,WAAA,CAAA9P,EAAA,CAAAoY,eAAA,KAAAC,GAAA,EAAA5C,GAAA,CAAA/V,SAAA,CAAAqQ,WAAA,CAAAC,MAAA,CAAAsI,MAAA,GAA8F;UAO3GtY,EAAA,CAAAsB,SAAA,GAA8B;UAA9BtB,EAAA,CAAAW,UAAA,cAAA8U,GAAA,CAAAjF,gBAAA,CAA8B;UACvBxQ,EAAA,CAAAsB,SAAA,GAAmB;UAAnBtB,EAAA,CAAAW,UAAA,oBAAmB,WAAA8U,GAAA,CAAA5U,WAAA,CAAAC,SAAA;UAGdd,EAAA,CAAAsB,SAAA,GAA+C;UAA/CtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAArN,QAAA,CAAAC,IAAA,IAAAoN,GAAA,CAAAnN,QAAA,CAAAqF,KAAA,CAA+C;UAmBjC3N,EAAA,CAAAsB,SAAA,GAAkB;UAAlBtB,EAAA,CAAAW,UAAA,mBAAkB,uDAAA8U,GAAA,CAAAtU,UAAA,CAAAyF,MAAA,gCAAA6O,GAAA,CAAAzO,gBAAA;UAWNhH,EAAA,CAAAsB,SAAA,GAAkD;UAAlDtB,EAAA,CAAAyB,iBAAA,CAAAgU,GAAA,CAAA5U,WAAA,CAAAC,SAAA,wBAAkD;UAQnEd,EAAA,CAAAsB,SAAA,GAAqC;UAArCtB,EAAA,CAAAW,UAAA,YAAA8U,GAAA,CAAAtU,UAAA,CAAAyB,YAAA,CAAqC;UAGrB5C,EAAA,CAAAsB,SAAA,GAA0D;UAA1DtB,EAAA,CAAAyB,iBAAA,CAAAgU,GAAA,CAAA5U,WAAA,CAAAC,SAAA,gCAA0D;UAQ1Ed,EAAA,CAAAsB,SAAA,GAAqC;UAArCtB,EAAA,CAAAW,UAAA,YAAA8U,GAAA,CAAAtU,UAAA,CAAAkC,YAAA,CAAqC;UAMMrD,EAAA,CAAAsB,SAAA,GAA0D;UAA1DtB,EAAA,CAAAyB,iBAAA,CAAAgU,GAAA,CAAA5U,WAAA,CAAAC,SAAA,gCAA0D;UAOrGd,EAAA,CAAAsB,SAAA,GAA4B;UAA5BtB,EAAA,CAAAW,UAAA,YAAA8U,GAAA,CAAAtU,UAAA,CAAA6C,GAAA,CAA4B;UAMOhE,EAAA,CAAAsB,SAAA,GAAmE;UAAnEtB,EAAA,CAAAyB,iBAAA,CAAAgU,GAAA,CAAA5U,WAAA,CAAAC,SAAA,yCAAmE;UAc7Hd,EAAA,CAAAsB,SAAA,GAAmC;UAAnCtB,EAAA,CAAAW,UAAA,oCAAmC,uCAAA8U,GAAA,CAAApI,OAAA,aAAAoI,GAAA,CAAAnF,OAAA,aAAAmF,GAAA,CAAA3G,WAAA,gBAAA2G,GAAA,CAAAtF,UAAA,cAAAsF,GAAA,CAAA9E,MAAA,CAAA4H,IAAA,CAAA9C,GAAA,eAAAA,GAAA,CAAArF,QAAA,UAAAqF,GAAA,CAAApF,IAAA,YAAAoF,GAAA,CAAAtU,UAAA,gBAAAsU,GAAA,CAAA5U,WAAA,CAAAC,SAAA;UAgBzBd,EAAA,CAAAsB,SAAA,GAAoE;UAApEtB,EAAA,CAAAwY,UAAA,CAAAxY,EAAA,CAAAsC,eAAA,KAAAmW,GAAA,EAAoE;UAFpEzY,EAAA,CAAAW,UAAA,gBAAAX,EAAA,CAAAsC,eAAA,KAAAoW,IAAA,EAAqD,WAAAjD,GAAA,CAAA9I,UAAA,aAAA8I,GAAA,CAAA7J,mBAAA;UAIxC5L,EAAA,CAAAsB,SAAA,GAA2B;UAA3BtB,EAAA,CAAAW,UAAA,cAAA8U,GAAA,CAAA3M,aAAA,CAA2B;UAGhC9I,EAAA,CAAAsB,SAAA,GAA0E;UAA1EtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAArN,QAAA,CAAAC,IAAA,IAAAoN,GAAA,CAAAnN,QAAA,CAAAqF,KAAA,IAAA8H,GAAA,CAAA5Q,WAAA,aAA0E;UAU7B7E,EAAA,CAAAsB,SAAA,GAC5C;UAD4CtB,EAAA,CAAA2E,kBAAA,KAAA8Q,GAAA,CAAA5U,WAAA,CAAAC,SAAA,oCAC5C;UAESd,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA5Q,WAAA,aAA6B;UAU9B7E,EAAA,CAAAsB,SAAA,GAAwD;UAAxDtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA5Q,WAAA,gBAAA4Q,GAAA,CAAA5Q,WAAA,aAAwD;UAQvD7E,EAAA,CAAAsB,SAAA,GAAqG;UAArGtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAA/G,WAAA,CAAAkD,KAAA,KAAAuQ,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAA/G,WAAA,CAAAgH,MAAA,kBAAAyM,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAA/G,WAAA,CAAAgH,MAAA,CAAA5D,QAAA,EAAqG;UAErGpF,EAAA,CAAAsB,SAAA,GAA0D;UAA1DtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAA/G,WAAA,CAAAgH,MAAA,kBAAAyM,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAA/G,WAAA,CAAAgH,MAAA,CAAAmC,SAAA,CAA0D;UAE1DnL,EAAA,CAAAsB,SAAA,GAAwD;UAAxDtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAA/G,WAAA,CAAAgH,MAAA,kBAAAyM,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAA/G,WAAA,CAAAgH,MAAA,CAAA2P,OAAA,CAAwD;UAOrB3Y,EAAA,CAAAsB,SAAA,GAC3C;UAD2CtB,EAAA,CAAA2E,kBAAA,KAAA8Q,GAAA,CAAA5U,WAAA,CAAAC,SAAA,qCAC3C;UAEQd,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA5Q,WAAA,aAA6B;UAU9B7E,EAAA,CAAAsB,SAAA,GAAwD;UAAxDtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA5Q,WAAA,gBAAA4Q,GAAA,CAAA5Q,WAAA,aAAwD;UAQvD7E,EAAA,CAAAsB,SAAA,GAAuG;UAAvGtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAAnG,YAAA,CAAAsC,KAAA,KAAAuQ,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAAnG,YAAA,CAAAoG,MAAA,kBAAAyM,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAAnG,YAAA,CAAAoG,MAAA,CAAA5D,QAAA,EAAuG;UAEvGpF,EAAA,CAAAsB,SAAA,GAA2D;UAA3DtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAAnG,YAAA,CAAAoG,MAAA,kBAAAyM,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAAnG,YAAA,CAAAoG,MAAA,CAAAmC,SAAA,CAA2D;UAE3DnL,EAAA,CAAAsB,SAAA,GAAyD;UAAzDtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAAnG,YAAA,CAAAoG,MAAA,kBAAAyM,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAAnG,YAAA,CAAAoG,MAAA,CAAA2P,OAAA,CAAyD;UAO1C3Y,EAAA,CAAAsB,SAAA,GAA0D;UAA1DtB,EAAA,CAAAyB,iBAAA,CAAAgU,GAAA,CAAA5U,WAAA,CAAAC,SAAA,gCAA0D;UAEzEd,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA5Q,WAAA,aAA6B;UAW9B7E,EAAA,CAAAsB,SAAA,GAAwD;UAAxDtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA5Q,WAAA,gBAAA4Q,GAAA,CAAA5Q,WAAA,aAAwD;UAQvD7E,EAAA,CAAAsB,SAAA,GAAuG;UAAvGtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAA1F,YAAA,CAAA6B,KAAA,KAAAuQ,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAA1F,YAAA,CAAA2F,MAAA,kBAAAyM,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAA1F,YAAA,CAAA2F,MAAA,CAAA5D,QAAA,EAAuG;UAEvGpF,EAAA,CAAAsB,SAAA,GAAyD;UAAzDtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAA1F,YAAA,CAAA2F,MAAA,kBAAAyM,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAA1F,YAAA,CAAA2F,MAAA,CAAA2P,OAAA,CAAyD;UAInE3Y,EAAA,CAAAsB,SAAA,GAAoD;UAApDtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA7T,MAAA,CAAAyG,IAAA,IAAAoN,GAAA,CAAA/V,SAAA,CAAAsN,YAAA,CAAAC,QAAA,CAAoD;UA0BpDjN,EAAA,CAAAsB,SAAA,GAA8E;UAA9EtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA7T,MAAA,CAAAyG,IAAA,IAAAoN,GAAA,CAAA/V,SAAA,CAAAsN,YAAA,CAAAC,QAAA,IAAAwI,GAAA,CAAA5Q,WAAA,aAA8E;UAgBhC7E,EAAA,CAAAsB,SAAA,GAA4D;UAA5DtB,EAAA,CAAAyB,iBAAA,CAAAgU,GAAA,CAAA5U,WAAA,CAAAC,SAAA,kCAA4D;UAEzFd,EAAA,CAAAsB,SAAA,GAA2B;UAA3BtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA5Q,WAAA,aAA2B;UAWnC7E,EAAA,CAAAsB,SAAA,GAAsD;UAAtDtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA5Q,WAAA,gBAAA4Q,GAAA,CAAA5Q,WAAA,aAAsD;UAQrD7E,EAAA,CAAAsB,SAAA,GAAsD;UAAtDtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAAvD,OAAA,CAAAwD,MAAA,kBAAAyM,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAAvD,OAAA,CAAAwD,MAAA,CAAAmC,SAAA,CAAsD;UAMlBnL,EAAA,CAAAsB,SAAA,GAAgD;UAAhDtB,EAAA,CAAAyB,iBAAA,CAAAgU,GAAA,CAAA5U,WAAA,CAAAC,SAAA,sBAAgD;UAE7Ed,EAAA,CAAAsB,SAAA,GAA2B;UAA3BtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA5Q,WAAA,aAA2B;UAWnC7E,EAAA,CAAAsB,SAAA,GAAsD;UAAtDtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA5Q,WAAA,gBAAA4Q,GAAA,CAAA5Q,WAAA,aAAsD;UAQrD7E,EAAA,CAAAsB,SAAA,GAAmD;UAAnDtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAA7C,IAAA,CAAA8C,MAAA,kBAAAyM,GAAA,CAAA3M,aAAA,CAAAC,QAAA,CAAA7C,IAAA,CAAA8C,MAAA,CAAAmC,SAAA,CAAmD;UAqC7DnL,EAAA,CAAAsB,SAAA,GAAsD;UAAtDtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA5Q,WAAA,gBAAA4Q,GAAA,CAAA5Q,WAAA,aAAsD;UA8BtD7E,EAAA,CAAAsB,SAAA,GAAmF;UAAnFtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA5Q,WAAA,gBAAA4Q,GAAA,CAAA5Q,WAAA,gBAAA4Q,GAAA,CAAA7T,MAAA,CAAA4G,UAAA,SAAmF;UAUvFxI,EAAA,CAAAsB,SAAA,GAA2B;UAA3BtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA5Q,WAAA,aAA2B;UAoB5B7E,EAAA,CAAAsB,SAAA,GAAyK;UAAzKtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA5Q,WAAA,gBAAA4Q,GAAA,CAAA5Q,WAAA,iBAAA4Q,GAAA,CAAA7T,MAAA,CAAA4G,UAAA,YAAAiN,GAAA,CAAA7T,MAAA,CAAA4G,UAAA,aAAAiN,GAAA,CAAAhN,oBAAA,CAAAC,QAAA,CAAA+M,GAAA,CAAA7T,MAAA,CAAA4G,UAAA,GAAyK;UAUxKxI,EAAA,CAAAsB,SAAA,GAA+F;UAA/FtB,EAAA,CAAAW,UAAA,UAAA8U,GAAA,CAAA5Q,WAAA,gBAAA4Q,GAAA,CAAA5Q,WAAA,iBAAA4Q,GAAA,CAAAlK,SAAA,IAAAkK,GAAA,CAAAlK,SAAA,CAAAS,MAAA,KAA+F;UAyC/FhM,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAW,UAAA,SAAA8U,GAAA,CAAA5Q,WAAA,aAA6B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}