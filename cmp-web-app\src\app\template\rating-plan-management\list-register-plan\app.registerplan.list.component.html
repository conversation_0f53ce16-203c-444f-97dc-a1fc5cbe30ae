<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.registerplan")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center responsive-container">
        <p-splitButton *ngIf="checkAuthen([allPermissions.RATING_PLAN_SIM.REGISTER_BY_FILE,allPermissions.RATING_PLAN_SIM.REGISTER_BY_LIST])" styleClass="mr-2 p-button-success equal-button-with-margin" [label]="tranService.translate('global.button.registerRatingPlan')" [model]="itemRegisters"></p-splitButton>
        <p-button styleClass="p-button-info equal-button-with-margin" [label]="tranService.translate('global.button.historyRegisterPlan')" *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_HISTORY])" icon="" [routerLink]="['/plans/registers/history']" routerLinkActive="router-link-active" ></p-button>
    </div>
</div>

<form [formGroup]="formSearch" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid search-grid-3">
            <!-- So thue bao -->
            <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="msisdn"
                           [(ngModel)]="searchInfo.msisdn"
                           formControlName="msisdn"
                    />
                    <label htmlFor="msisdn">{{tranService.translate("sim.label.sothuebao")}}</label>
                </span>
            </div>
            <!-- IMEI Thiet bi -->
            <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                            class="w-full"
                            pInputText id="imei"
                            [(ngModel)]="searchInfo.imei"
                            formControlName="imei"
                    />
                    <label htmlFor="imei">{{tranService.translate("sim.label.imeiDevice")}}</label>
                </span>
            </div>
            <!-- goi cuoc -->
            <div class="col-3">
                <div class="relative">
                    <vnpt-select
                        class="w-full"
                        [(value)]="searchInfo.ratingPlanId"
                        [placeholder]="tranService.translate('sim.label.goicuoc')"
                        objectKey="dropdownListSim"
                        paramKey="name"
                        keyReturn="id"
                        displayPattern="${name} - ${code}"
                        typeValue="primitive"
                        [paramDefault]="{type: 'ratingPlan'}"
                        [isMultiChoice]="false"
                        [floatLabel]="true"
                    ></vnpt-select>
                </div>
            </div>
            <!-- Trang thai -->
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                                id="status" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.status"
                                formControlName="status"
                                [options]="listStatus"
                                optionLabel="name"
                                optionValue="value"
                    ></p-dropdown>
                    <label class="label-dropdown" for="status">{{tranService.translate("ratingPlan.label.status")}}</label>
                </span>
            </div>
            <!-- khach hang -->
            <div class="col-3">
                <div class="relative">
                    <vnpt-select
                        class="w-full"
                        [(value)]="searchInfo.customer"
                        [placeholder]="tranService.translate('sim.label.khachhang')"
                        objectKey="dropdownListSim"
                        paramKey="name"
                        keyReturn="customerCode"
                        displayPattern="${customerName} - ${customerCode}"
                        typeValue="primitive"
                        [paramDefault]="{type: 'customer'}"
                        [isMultiChoice]="false"
                        [floatLabel]="true"
                    ></vnpt-select>
                </div>
            </div>
            <!-- ma hop dong -->
            <div class="col-3">
                <div class="relative">
                    <vnpt-select
                        class="w-full"
                        [(value)]="searchInfo.contractCode"
                        [placeholder]="tranService.translate('sim.label.mahopdong')"
                        objectKey="dropdownListSim"
                        paramKey="name"
                        keyReturn="contractCode"
                        displayPattern="${contractCode}"
                        typeValue="primitive"
                        [paramDefault]="{type: 'contract'}"
                        [floatLabel]="true"
                        [isMultiChoice]="false"
                    ></vnpt-select>
                </div>
            </div>
            <div class="col-3 pb-0 date-filter">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                            id="dateFrom"
                            [(ngModel)]="searchInfo.dateFrom"
                            formControlName="dateFrom"
                            [showIcon]="true"
                            [showClear]="true"
                            dateFormat="dd/mm/yy"
                            [maxDate]="maxDateFrom"
                            (onSelect)="onChangeDateFrom(searchInfo.dateFrom)"
                            (onInput)="onChangeDateFrom(searchInfo.dateFrom)"
                    ></p-calendar>
                    <label class="label-calendar" htmlFor="dateFrom">{{tranService.translate("sim.label.ngaylamhopdongtu")}}</label>
                </span>
            </div>
            <div class="col-3 pb-0 date-filter" style="width: calc(25% - 70px)">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                            id="dateTo"
                            [(ngModel)]="searchInfo.dateTo"
                            formControlName="dateTo"
                            [showIcon]="true"
                            [showClear]="true"
                            dateFormat="dd/mm/yy"
                            [minDate]="minDateTo"
                            [maxDate]="maxDateTo"
                            (onSelect)="onChangeDateTo(searchInfo.dateTo)"
                            (onInput)="onChangeDateTo(searchInfo.dateTo)"
                    />
                    <label class="label-calendar" htmlFor="dateTo">{{tranService.translate("sim.label.ngaylamhopdongden")}}</label>
                </span>
            </div>
            <div class="col pb-0" style="width: 5% !important;">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>

<div class="flex justify-content-center dialog-vnpt">
    <p-dialog [header]="tranService.translate('sim.text.detailSim')" [(visible)]="isShowModalDetailSim" [modal]="true" [style]="{ width: '980px' }" [draggable]="false" [resizable]="false" *ngIf="isShowModalDetailSim">
        <div class="grid grid-1 mt-1 h-auto" style="width: calc(100% + 16px);">
            <div class="col sim-detail pr-0">
                <p-card [header]="tranService.translate('sim.text.simInfo')">
                    <div class="flex flex-row justify-content-between custom-card">
                        <div class="w-6">
                            <div class="grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.sothuebao")}}</span>
                                <span class="col">{{detailSim.msisdn}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.trangthaisim")}}</span>
                                <span class="w-auto ml-3" [class]="getClassStatus(detailSim.status)">{{getNameStatus(detailSim.status)}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.imsi")}}</span>
                                <span class="col">{{detailSim.imsi}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.imeiDevice")}}</span>
                                <span class="col">{{detailSim.imei}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.maapn")}}</span>
                                <span class="col">{{detailSim.apnId}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.trangthaiketnoi")}}</span>
                                <span *ngIf="detailSim.connectionStatus!==undefined && detailSim.connectionStatus!==null && detailSim.connectionStatus!=='0' " class="ml-3 p-2 text-green-800 bg-green-100 border-round inline-block">ON</span>
                                <span *ngIf="detailSim.connectionStatus==='0'" class="ml-3 p-2 text-50 surface-500 border-round inline-block">OFF</span>
                                <span *ngIf="detailSim.connectionStatus===undefined || detailSim.connectionStatus=== null " class="ml-3 p-2 text-50 surface-500 border-round inline-block">NOT FOUND</span>
                            </div>
                        </div>
                        <div class="w-6">
                            <div class="grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.startDate")}}</span>
                                <span class="col">{{detailSim.startDate | date:'dd/MM/yyyy'}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.serviceType")}}</span>
                                <span class="w-auto ml-3">{{getServiceType(detailSim.serviceType)}}</span>
                            </div>
                        </div>
                    </div>
                </p-card>
                <p-card [header]="tranService.translate('sim.text.simStatusInfo')" styleClass="mt-3 sim-status">
                    <div class="grid">
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusData" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.data")}}</div>
                        </div>
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusReceiveCall" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.callReceived")}}</div>
                        </div>
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusSendCall" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.callSent")}}</div>
                        </div>
                    </div>
                    <div class="grid">
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusWorldCall" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.callWorld")}}</div>
                        </div>
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusReceiveSms" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.smsReceived")}}</div>
                        </div>
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusSendSms" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.smsSent")}}</div>
                        </div>
                    </div>
                </p-card>
                <!-- goi cuoc -->
                <p-card [header]="tranService.translate('sim.text.ratingPlanInfo')" styleClass="mt-3">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.tengoicuoc")}}</span>
                        <span class="col">{{detailSim.ratingPlanName}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.dataUseInMonth")}}</span>
                        <span class="col">{{this.utilService.bytesToMegabytes(detailRatingPlan?.dataUseInMonth) | number }} {{detailRatingPlan?.unit?detailRatingPlan.unit:"MB"}}</span>
                    </div>
                </p-card>
            </div>
            <div class="col sim-detail pr-0">
                <!-- hop dong -->
                <p-card [header]="tranService.translate('sim.text.contractInfo')">
                    <div class="grid mt-0">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.mahopdong")}}</span>
                        <span class="col">{{detailContract.contractCode}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.ngaylamhopdong")}}</span>
                        <span class="col">{{detailContract.contractDate | date:'dd/MM/yyyy'}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.nguoilamhopdong")}}</span>
                        <span class="col uppercase">{{detailContract.contractorInfo}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.matrungtam")}}</span>
                        <span class="col">{{detailContract.centerCode}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.dienthoailienhe")}}</span>
                        <span class="col">{{detailContract.contactPhone}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.diachilienhe")}}</span>
                        <span class="col">{{detailContract.contactAddress}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.paymentName")}}</span>
                        <span class="col uppercase">{{detailContract.paymentName}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.paymentAddress")}}</span>
                        <span class="col">{{detailContract.paymentAddress}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.routeCode")}}</span>
                        <span class="col">{{detailContract.routeCode}}</span>
                    </div>
                </p-card>
                <!-- customer -->
                <p-card [header]="tranService.translate('sim.text.customerInfo')" styleClass="mt-3">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.khachhang")}}</span>
                        <span class="col">{{detailCustomer.name}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.customerCode")}}</span>
                        <span class="col">{{detailCustomer.code}}</span>
                    </div>
                </p-card>
            </div>
        </div>
    </p-dialog>
</div>

<div class="flex justify-content-center dialog-vnpt">
    <p-dialog [header]="tranService.translate('global.menu.detailplan')" [(visible)]="isShowModalDetailPlan" [modal]="true" [style]="{ width: '980px' }" [draggable]="false" [resizable]="false" *ngIf="isShowModalDetailPlan">
        <p-card  styleClass="h-full" [style]="{'width': 'calc(100% + 16px)'}">
            <div class="col ratingPlan-detail custom-rating-detail pr-0 flex" style="border:1px solid black; margin-bottom: 20px">
                <div class="flex-1">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.planCode")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.code}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.planName")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.name}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.status")}}</span>
                        <div  class="text-white w-auto col">
                            <span [class]="getClassStatus(ratingPlanInfo.status)">{{getNameStatus(ratingPlanInfo.status)}}</span>
                        </div>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.dispatchCode")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.dispatchCode}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.customerType")}}</span>
                        <span class="inline-block col-fixed">{{getNameCustomerType(ratingPlanInfo.customerType)}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.description")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.description}}</span>
                    </div>
                    <!--                    <div class="mt-1 grid">-->
                    <!--                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.motachuaco")}}</span>-->
                    <!--                    </div>-->
                </div>
                <div class="flex-1">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.subscriptionFee")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.subscriptionFee}}&nbsp; &nbsp; &nbsp; &nbsp; {{tranService.translate("ratingPlan.text.textDong")}}&nbsp;{{tranService.translate("ratingPlan.text.vat")}}</span>
                    </div>
                    <div class="grid">
                        <span class="inline-block col-fixed">
                            <span>
                                <p-radioButton [disabled]="true" [value]="subscriptionTypes[0].ip" [(ngModel)]="ratingPlanInfo.subscriptionType" inputId="typeIp1"></p-radioButton>
                                &nbsp;
                                <span>{{tranService.translate("ratingPlan.subscriptionType.post")}}</span>
                            </span>
                        </span>
                        <span class="inline-block col-fixed radioButton2" style="padding-left: 108px">
                            <span>
                                <p-radioButton [disabled]="true" [value]="subscriptionTypes[1].ip" [(ngModel)]="ratingPlanInfo.subscriptionType" inputId="typeIp2"></p-radioButton>
                                &nbsp;
                                <span>{{tranService.translate("ratingPlan.subscriptionType.pre")}}</span>
                            </span>
                        </span>
                    </div>
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.ratingScope")}}</span>
                        <span class="inline-block col-fixed">{{getRatingScope(ratingPlanInfo.ratingScope)}}</span>
                    </div>
                    <div class="grid" *ngIf="ratingPlanInfo.ratingScope == planScopes.CUSTOMER">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.province")}}</span>
                        <span class="inline-block col-fixed" style="width: fit-content !important;">{{myProvices}}</span>
                    </div>

                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.cycle")}}</span>
                        <span class="inline-block col-fixed">{{getCycleTimeUnit(ratingPlanInfo.cycleTimeUnit)}}</span>
                    </div>
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.cycleInterval")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.cycleInterval}}</span>
                    </div>
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.reload")}}</span>
                        <div style="padding-top: 10px; padding-left: 13px">
                            <p-inputSwitch [(ngModel)]="checkedReload" [disabled]="true"></p-inputSwitch>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-1 grid">
                <span style="font-size: 20px;margin-left: 10px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.flat")}}</span>
            </div>
            <div class="col ratingPlan-detail custom-rating-detail-limit pr-0 flex"  style="border:1px solid black; margin-bottom: 20px" id = "name">
                <div class="flex-1">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.limitDataUsage")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.limitDataUsage}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.limitSmsOutside")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.limitSmsOutside}}</span>
                    </div>
                </div>
                <div class="flex-1">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.limitSmsInside")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.limitSmsInside}}</span>
                    </div>
                </div>
            </div>

            <div class="mt-1 grid">
                <span style="font-size: 20px;margin-left: 10px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.flexible")}}</span>
                <div style="padding-top: 18px">
                    <p-inputSwitch [(ngModel)]="checkedFlexible" [disabled]="true"></p-inputSwitch>
                </div>
            </div>
            <div class="col ratingPlan-detail pr-0 flex"  style="border:1px solid black;" id = "flexible">
                <div class="flex-1">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.feePerDataUnit")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.feePerDataUnit}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.squeezedSpeed")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.downSpeed}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.feeSmsInside")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.feeSmsInside}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.feeSmsOutside")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.feeSmsOutside}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.maximumFee")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.maximumFee}}</span>
                    </div>
                </div>
                <div class="flex-1">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">/&nbsp; &nbsp; &nbsp; {{ratingPlanInfo.dataRoundUnit}} &nbsp; &nbsp; &nbsp; &nbsp;   KB</span>
                    </div>
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">/&nbsp; &nbsp; &nbsp; {{ratingPlanInfo.squeezedSpeed}}</span>
                    </div>
                </div>
            </div>
        </p-card>
    </p-dialog>
</div>
<table-vnpt
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="tranService.translate('global.menu.registerplan')"
></table-vnpt>

<!-- dialog register for sim -->
<div class="flex justify-content-center dialog-push-group">
    <p-dialog [header]="headerDialogRegisterForSim" [(visible)]="isShowDialogRegisterSim" [modal]="true" [style]="{ width: '500px' }" [draggable]="false" [resizable]="false">
        <div class="w-full field grid">
            <label htmlFor="planSelected" class="col-fixed" style="width:100px">{{tranService.translate("sim.label.goicuoc")}}</label>
            <div class="col">
                <p-dropdown styleClass="w-full"
                        [showClear]="true"
                        [autoDisplayFirst]="false"
                        [(ngModel)]="planSelected"
                        [options]="listRatingPlanOrigin"
                        optionLabel="display"
                        [filter]="true" filterBy="name"
                        optionValue="id"
                        [placeholder]="tranService.translate('sim.text.selectRatingPlan')"
                ></p-dropdown>
            </div>
        </div>
        <div class="flex flex-row justify-content-center align-items-center">
            <p-button styleClass="mr-2 p-button-secondary p-button-outlined" [label]="tranService.translate('global.button.cancel')" (click)="isShowDialogRegisterSim = false"></p-button>
            <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.save')" (click)="registerForSim()" [disabled]="planSelected == null || planSelected == undefined"></p-button>
        </div>
    </p-dialog>
</div>

<!-- dialog register for group sim -->
<div class="flex justify-content-center dialog-push-group">
    <p-dialog [header]="tranService.translate('global.button.registerPlanForGroup')" [(visible)]="isShowDialogRegisterGroupSim" [modal]="true" [style]="{ width: '500px' }" [draggable]="false" [resizable]="false" styleClass="dialog-upload-device">
        <div class="w-full field grid dialog-grid">
            <label htmlFor="groupSim" class="col-fixed" style="width:100px">{{tranService.translate("sim.label.nhomsim")}}</label>
            <div class="col input-div" style="max-width: calc(100% - 100px)">
                <vnpt-select
                    class="w-full"
                    [(value)]="groupSimSelected"
                    [placeholder]="tranService.translate('sim.text.selectGroupSim')"
                    objectKey="groupSim"
                    paramKey="name"
                    keyReturn="id"
                    displayPattern="${name} - ${groupKey}"
                    typeValue="primitive"
                    [isMultiChoice]="false"
                ></vnpt-select>
            </div>
        </div>
        <div class="w-full field grid dialog-grid">
            <label htmlFor="planSelected" class="col-fixed" style="width:100px">{{tranService.translate("sim.label.goicuoc")}}</label>
            <div class="col input-div" style="max-width: calc(100% - 100px)">
                <p-dropdown styleClass="w-full"
                        [showClear]="true"
                        [autoDisplayFirst]="false"
                        [(ngModel)]="planGroupSelected"
                        [options]="listRatingPlan"
                        optionLabel="display"
                        [filter]="true" filterBy="name"
                        optionValue="id"
                        [placeholder]="tranService.translate('sim.text.selectRatingPlan')"
                ></p-dropdown>
            </div>
        </div>
        <div class="flex flex-row justify-content-center align-items-center">
            <p-button styleClass="mr-2 p-button-secondary p-button-outlined" [label]="tranService.translate('global.button.cancel')" (click)="isShowDialogRegisterGroupSim = false"></p-button>
            <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.save')" (click)="registerForGroupSim()" [disabled]="planGroupSelected == null || planGroupSelected == undefined || groupSimSelected == null || groupSimSelected == undefined"></p-button>
        </div>
    </p-dialog>
</div>


<!-- dialog result register plan for group sim -->
<!--<div class="flex justify-content-center dialog-push-group">-->
<!--    <p-dialog [header]="tranService.translate('global.text.resultRegister')" [(visible)]="isShowDialogResultRegisterGroupSim" [modal]="true" [style]="{ width: '700px' }" [draggable]="false" [resizable]="false">-->
<!--        <div class="w-full field grid">-->
<!--            <label htmlFor="groupSim" class="col-fixed col text-primary-500 font-bold white-space-normal">{{tranService.translate("ratingPlan.text.textResultSuccess", {total: totalResultRegisterGroup, success: totalSuccessResultRegisterGroup})}}</label>-->
<!--        </div>-->
<!--        <div class="w-full field grid">-->
<!--            <label class="col-fixed justify-content-end text-red-500" style="width:70px"><i class="pi pi-exclamation-triangle text-6xl"></i></label>-->
<!--            <label class="col">{{tranService.translate("ratingPlan.text.textResultFail", {total: totalResultRegisterGroup, fail: totalFailResultRegisterGroup})}}</label>-->
<!--        </div>-->
<!--        <table-vnpt-->
<!--            [fieldId]="'msisdn'"-->
<!--            [pageNumber]="pageNumberResultRegister"-->
<!--            [pageSize]="pageSizeResultRegister"-->
<!--            [(selectItems)]="selectItems"-->
<!--            [columns]="columnsResultRegisterGroup"-->
<!--            [dataSet]="dataSetResultRegisterGroup"-->
<!--            [options]="optionTableResultRegisterGroup"-->
<!--            [loadData]="pagingResultRegisterGroup.bind(this)"-->
<!--            [rowsPerPageOptions]="[5,10,20]"-->
<!--            [scrollHeight]="'400px'"-->
<!--        ></table-vnpt>-->
<!--        <div class="flex flex-row justify-content-center align-items-center">-->
<!--            <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.agree')" (click)="isShowDialogResultRegisterGroupSim = false"></p-button>-->
<!--        </div>-->
<!--    </p-dialog>-->
<!--</div>-->

<div class="flex justify-content-center dialog-push-group">
    <p-dialog [header]="tranService.translate('global.button.registerPlanForGroup')" [(visible)]="isShowDialogResultRegisterGroupSim" [modal]="true" [style]="{ width: '1000px' }" [draggable]="false" [resizable]="false">
<!--        <div class="w-full field grid">-->
<!--            <div class="col-10 flex flex-row justify-content-start align-items-center">-->
<!--                <input-file-vnpt class="w-full" [(fileObject)]="fileObject" [clearFileCallback]="clearFileCallback.bind(this)"-->
<!--                                 [options]="optionInputFile"-->
<!--                ></input-file-vnpt>-->
<!--            </div>-->
<!--            <div class="col-2 flex flex-row justify-content-end align-items-center">-->
<!--                <p-button icon="pi pi-download" [pTooltip]="tranService.translate('global.button.downloadTemp')" styleClass="p-button-outlined p-button-secondary" (click)="downloadTemplate()"></p-button>-->
<!--            </div>-->
<!--        </div>-->
        <div class="grid"><div class="col pt-0"><small class="text-red-500" *ngIf="isShowErrorGroup">{{messageErrorUpload}}</small></div></div>
        <p-table
            [paginator]="true"
            [rows]="pageSizeSimImport"
            [first]="rowFirstSimImport"
            [showCurrentPageReport]="true"
            [tableStyle]="{ 'min-width': '100%' }"
            [currentPageReportTemplate]="tranService.translate('global.text.templateTextPagination')"
            (onPage)="pagingResultSimImport($event)"
            [rowsPerPageOptions]="[5,10,20]"
            [styleClass]="'p-datatable-sm'"
            [totalRecords]="simImportsOrigin?.length"
            [lazy]="true"
            #dataTable
            [scrollHeight]="'400px'"
            [value]="simImports"
            dataKey="id"
            [tableStyle]="{ 'min-width': '50rem' }"
            *ngIf="simImportsOrigin">
            <ng-template pTemplate="header">
                <tr>
                    <th style="min-width:150px;max-width:150px">{{tranService.translate("sim.label.sothuebao")}}</th>
                    <th style="min-width:150px;max-width:150px">{{tranService.translate("sim.label.tengoicuoc")}}</th>
                    <th style="min-width: 250px;max-width: 250px;">{{tranService.translate("sim.label.description")}}</th>
                    <th style="min-width:70px;max-width:70px; text-align: center;">{{tranService.translate("global.text.action")}}</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-simImport let-editing="editing" let-i="rowIndex">
                <tr [formGroup]="mapFormSimImports[simImport.keyForm]">
                    <td style="min-width:150px;max-width:150px" [pEditableColumn]="simImport.msisdn" pEditableColumnField="msisdn">
                        <p-cellEditor>
                            <ng-template pTemplate="input">
                                <input formControlName="msisdn"
                                       style="min-width:150px;max-width:150px"
                                       pInputText type="text"
                                       [(ngModel)]="simImport.msisdn"
                                       required
                                       maxlength="20"
                                       pattern="^(\+?84)[1-9][0-9]{8,9}$"
                                       (ngModelChange)="checkValueSimImportChange(simImport)"
                                />
                            </ng-template>
                            <ng-template pTemplate="output">
                                {{ simImport.msisdn }}
                            </ng-template>
                        </p-cellEditor>
                    </td>
                    <td style="min-width:150px;max-width:150px" [pEditableColumn]="simImport.ratingPlanName" pEditableColumnField="ratingPlanName">
                        <p-cellEditor>
                            <ng-template pTemplate="input">
                                <input formControlName="ratingPlanName"
                                       style="min-width:150px;max-width:150px"
                                       pInputText type="text"
                                       [(ngModel)]="simImport.ratingPlanName"
                                       required
                                       maxlength="50"
                                       pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$"
                                       (ngModelChange)="checkValueSimImportChange(simImport)"
                                />
                            </ng-template>
                            <ng-template pTemplate="output">
                                {{ simImport.ratingPlanName }}
                            </ng-template>
                        </p-cellEditor>
                    </td>
                    <td style="min-width:200px;max-width:200px">
                            <span *ngIf="!mapFormSimImports[simImport.keyForm].invalid"
                            >{{ tranService.translate(simImport.description) }}</span>
                        <!-- chi so thue bao trong -->
                        <span *ngIf="(mapFormSimImports[simImport.keyForm].controls.msisdn.hasError('required') && !mapFormSimImports[simImport.keyForm].controls.ratingPlanName.hasError('required'))"
                        >
                                {{tranService.translate("global.message.requiredField",{field: tranService.translate("sim.label.sothuebao")})}}
                            </span>
                        <!-- chi ten goi cuoc trong -->
                        <span *ngIf="(!mapFormSimImports[simImport.keyForm].controls.msisdn.hasError('required') && mapFormSimImports[simImport.keyForm].controls.ratingPlanName.hasError('required'))"
                        >
                                {{tranService.translate("global.message.requiredField",{field: tranService.translate("sim.label.tengoicuoc")})}}
                            </span>
                        <!-- ca hai cung trong -->
                        <span *ngIf="(mapFormSimImports[simImport.keyForm].controls.msisdn.hasError('required') && mapFormSimImports[simImport.keyForm].controls.ratingPlanName.hasError('required'))"
                        >
                                {{tranService.translate("global.message.required")}}
                            </span>
                        <!-- format so thue bao -->
                        <span *ngIf="mapFormSimImports[simImport.keyForm].controls.msisdn.errors?.pattern"
                        >
                                {{tranService.translate("global.message.invalidSubsciption")}}
                            </span>
                        <!-- format ten goi cuoc -->
                        <span *ngIf="mapFormSimImports[simImport.keyForm].controls.ratingPlanName.errors?.pattern"
                        >
                                {{tranService.translate("global.message.formatContainVN")}}
                            </span>
                    </td>
                    <td style="min-width:100px;max-width:100px;text-align: center;">
                        <span [pTooltip]="tranService.translate('global.button.delete')" class="pi pi-trash" (click)="removeItemSimImport(simImport,i)"></span>
                    </td>
                </tr>
            </ng-template>
        </p-table>
        <div class="flex flex-row justify-content-center align-items-center">
            <p-button styleClass="mr-2 p-button-secondary p-button-outlined" [label]="tranService.translate('global.button.cancel')" (click)="isShowDialogResultRegisterGroupSim = false"></p-button>
            <p-button [disabled]="!checkValidListImport()" *ngIf="simImports" styleClass="p-button-info" [label]="tranService.translate('global.button.save')" (click)="registerForFile()"></p-button>
        </div>
    </p-dialog>
</div>



<!-- dialog result register plan by file -->
<div class="flex justify-content-center dialog-push-group">
    <p-dialog [header]="tranService.translate('global.button.registerPlanByFile')" [(visible)]="isShowDialogResultRegisterFile" [modal]="true" [style]="{ width: '1000px' }" [draggable]="false" [resizable]="false" styleClass="dialog-file">
        <div class="w-full field grid">
            <div class="col-10 flex flex-row justify-content-start align-items-center">
                <input-file-vnpt class="w-full upload-device-file" [(fileObject)]="fileObject" [clearFileCallback]="clearFileCallback.bind(this)"
                    [options]="optionInputFile"
                ></input-file-vnpt>
            </div>
            <div class="col-2 flex flex-row justify-content-end align-items-center">
                <p-button icon="pi pi-download" [pTooltip]="tranService.translate('global.button.downloadTemp')" styleClass="p-button-outlined p-button-secondary" (click)="downloadTemplate()"></p-button>
            </div>
        </div>
        <div class="grid"><div class="col pt-0"><small class="text-red-500" *ngIf="isShowErrorUpload">{{messageErrorUpload}}</small></div></div>
        <p-table
            [paginator]="true"
            [rows]="pageSizeSimImport"
            [first]="rowFirstSimImport"
            [showCurrentPageReport]="true"
            [tableStyle]="{ 'min-width': '100%' }"
            [currentPageReportTemplate]="tranService.translate('global.text.templateTextPagination')"
            (onPage)="pagingResultSimImport($event)"
            [rowsPerPageOptions]="[5,10,20]"
            [styleClass]="'p-datatable-sm'"
            [totalRecords]="simImportsOrigin?.length"
            [lazy]="true"
            #dataTable
            [scrollHeight]="'400px'"
            [value]="simImports"
            dataKey="id"
            [tableStyle]="{ 'min-width': '50rem' }"
            *ngIf="simImportsOrigin">
            <ng-template pTemplate="header">
                <tr>
                    <th style="min-width:150px;max-width:150px">{{tranService.translate("sim.label.sothuebao")}}</th>
                    <th style="min-width:150px;max-width:150px">{{tranService.translate("sim.label.tengoicuoc")}}</th>
                    <th style="min-width: 250px;max-width: 250px;">{{tranService.translate("sim.label.description")}}</th>
                    <th style="min-width:70px;max-width:70px; text-align: center;">{{tranService.translate("global.text.action")}}</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-simImport let-editing="editing" let-i="rowIndex">
                    <tr [formGroup]="mapFormSimImports[simImport.keyForm]">
                        <td style="min-width:150px;max-width:150px" [pEditableColumn]="simImport.msisdn" pEditableColumnField="msisdn">
                            <p-cellEditor>
                                <ng-template pTemplate="input">
                                    <input formControlName="msisdn"
                                        style="min-width:150px;max-width:150px"
                                        pInputText type="text"
                                        [(ngModel)]="simImport.msisdn"
                                        required
                                        maxlength="20"
                                        pattern="^(\+?84)[1-9][0-9]{8,9}$"
                                        (ngModelChange)="checkValueSimImportChange(simImport)"
                                    />
                                </ng-template>
                                <ng-template pTemplate="output">
                                    {{ simImport.msisdn }}
                                </ng-template>
                            </p-cellEditor>
                        </td>
                        <td style="min-width:150px;max-width:150px" [pEditableColumn]="simImport.ratingPlanName" pEditableColumnField="ratingPlanName">
                            <p-cellEditor>
                                <ng-template pTemplate="input">
                                    <input formControlName="ratingPlanName"
                                        style="min-width:150px;max-width:150px"
                                        pInputText type="text"
                                        [(ngModel)]="simImport.ratingPlanName"
                                        required
                                        maxlength="50"
                                        pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$"
                                        (ngModelChange)="checkValueSimImportChange(simImport)"
                                    />
                                </ng-template>
                                <ng-template pTemplate="output">
                                    {{ simImport.ratingPlanName }}
                                </ng-template>
                            </p-cellEditor>
                        </td>
                        <td style="min-width:200px;max-width:200px">
                            <span *ngIf="!mapFormSimImports[simImport.keyForm].invalid"
                            >{{ tranService.translate(simImport.description) }}</span>
                            <!-- chi so thue bao trong -->
                            <span *ngIf="(mapFormSimImports[simImport.keyForm].controls.msisdn.hasError('required') && !mapFormSimImports[simImport.keyForm].controls.ratingPlanName.hasError('required'))"
                            >
                                {{tranService.translate("global.message.requiredField",{field: tranService.translate("sim.label.sothuebao")})}}
                            </span>
                            <!-- chi ten goi cuoc trong -->
                            <span *ngIf="(!mapFormSimImports[simImport.keyForm].controls.msisdn.hasError('required') && mapFormSimImports[simImport.keyForm].controls.ratingPlanName.hasError('required'))"
                            >
                                {{tranService.translate("global.message.requiredField",{field: tranService.translate("sim.label.tengoicuoc")})}}
                            </span>
                            <!-- ca hai cung trong -->
                            <span *ngIf="(mapFormSimImports[simImport.keyForm].controls.msisdn.hasError('required') && mapFormSimImports[simImport.keyForm].controls.ratingPlanName.hasError('required'))"
                            >
                                {{tranService.translate("global.message.required")}}
                            </span>
                            <!-- format so thue bao -->
                            <span *ngIf="mapFormSimImports[simImport.keyForm].controls.msisdn.errors?.pattern"
                            >
                                {{tranService.translate("global.message.invalidSubsciption")}}
                            </span>
                            <!-- format ten goi cuoc -->
                            <span *ngIf="mapFormSimImports[simImport.keyForm].controls.ratingPlanName.errors?.pattern"
                            >
                                {{tranService.translate("global.message.formatContainVN")}}
                            </span>
                        </td>
                        <td style="min-width:100px;max-width:100px;text-align: center;">
                            <span [pTooltip]="tranService.translate('global.button.delete')" class="pi pi-trash" (click)="removeItemSimImport(simImport,i)"></span>
                        </td>
                    </tr>
            </ng-template>
        </p-table>
        <div class="flex flex-row justify-content-center align-items-center">
            <p-button styleClass="mr-2 p-button-secondary p-button-outlined" [label]="tranService.translate('global.button.cancel')" (click)="isShowDialogResultRegisterFile = false"></p-button>
            <p-button [disabled]="!checkValidListImport()" *ngIf="simImports" styleClass="p-button-info" [label]="tranService.translate('global.button.save')" (click)="registerForFile()"></p-button>
        </div>
    </p-dialog>
</div>
