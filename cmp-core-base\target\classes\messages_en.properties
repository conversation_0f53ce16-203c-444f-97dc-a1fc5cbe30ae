error.length=less than {0} and great than {1}
error.object.not.found={0} not found
error.field.must.be.not.null={0} must be not null
error.duplicate.name=duplicate name
error.invalid.field=field {0} invalid
error.forbbiden.resource=forbbiden resource
error.data.format = Invalid data format
error.user.not.found={0} user not found.



#Validate annotation
error.valid.assert.false=Must be false.
error.valid.assert.true=Must be true.
error.valid.decimal.max=Must be less than ${inclusive == true ? 'or equal to ':''}{value}.
error.valid.decimal.min=Must be greater than ${inclusive == true ? 'or equal to ':''}{value}.
error.valid.digits=Numeric value out of bounds (<{integer} digits>.<{fraction} digits> expected).
error.valid.email=Must be a well-formed email address.
error.valid.max=Must be less than or equal to {1}.
error.valid.min=Must be greater than or equal to {1}.
error.valid.negative=Must be less than 0.
error.valid.negative.or.zero=Must be less than or equal to 0.
error.valid.not.blank=Must not be blank.
error.valid.not.empty=Must not be empty.
error.valid.null=Must be null.
error.valid.not.null=Field cannot NULL.
error.valid.range=Must be between {2} and {1}.
error.valid.past=Must be a past date.
error.valid.past.or.present=Must be a date in the past or in the present.
error.valid.pattern=Must match "{1}".
error.valid.phone.pattern=Invalid phone number
error.valid.positive=Must be greater than 0.
error.valid.positive.or.zero=Must be greater than or equal to 0.
error.valid.size=Size must be between {2} and {1}.

error.valid.length=Size must be between {2} and {1}.


#change password user
error.invalid.email=invalid email
invalid.token=invalid token
invalid.phone.number=invalid phone number
error.invalid.password=invalid password
id.must.be.null=id must be null
error.passwords.do.not.match=passwords do not match
invalid.type=invalid type
the.new.password.must.be.different.from.the.old.one=the new password must be different from the old one

error.emailIncorect=Email incorrect
error.passwordIncorect=Password incorrect
error.accountInactive=Account inactive



#\check exist
exists={0} exists

bad.request=Bad request


#register_rate
register.rate.sim.notin.permission=Subscribers are not under management.
register.rate.fail=Register rate Fail

#user
error.cant.not.update.province.customer=Cant not update province customer account

error.report.query=query error
error.report.limit.row=report limit row error
error.report.excel.limit=Export file data exceeds 1 million lines
error.export.excel.limit= export limit row error

