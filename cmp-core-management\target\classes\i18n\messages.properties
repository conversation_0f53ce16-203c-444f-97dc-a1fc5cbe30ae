# Error page
error.title=Your request cannot be processed
error.subtitle=Sorry, an error has occurred.
error.status=Status:
error.message=Message:

# Activation email
email.activation.title=[M2M SIM] Thông tin tài khoản
email.register.request=[M2M SIM] Đăng Ký Tạo <PERSON>
email.register.success=[M2M SIM] Đăng Ký Thành Công Tài Khoản Tenant Trên Hệ Thống ONE IoT Platform Của VNPT Technology
email.register.failed=[ONE IoT Platform] Đăng Ký Không Thành Công Tài Khoản Tenant Trên Hệ Thống ONE IoT Platform Của VNPT Technology
email.activation.greeting=Xin chào {0}
email.activation.text1=Tài khoản M2M SIM của bạn vừa được tạo:
email.activation.text2=Trân trọng,
email.signature=Đội ngũ phát triển nền tảng M2M SIM

# Creation email
email.creation.text1=T<PERSON><PERSON> khoản M2M SIM của bạn vừa được tạo:

# Reset email
email.reset.title=[M2M SIM] Quên <PERSON>
email.reset.greeting=Xin chào {0}
email.reset.text2=Thân,

# Notification
notification.transfer.mention=<b>{0}</b> mentioned you in transfer <b>{1}</b>
notification.transfer.addNote=<b>{0}</b> added a note in transfer -  <b>{1}</b>
notification.transfer.change=<b>Transfer changes: </b>
notification.transfer.operationsChanges=<b>Operations changes: </b>
notification.transfer.info=<b>Transfer Information: </b>
notification.transfer.note=<b>Note: </b>
notification.transfer.attribute=<b>{0}</b>: {1} 🡺 {2}
notification.transfer.create=<b>{0}</b> created new transfer <b>{1}</b> <b>{2}</b>
notification.transfer.checkAvailability=<b>{0}</b> check availability <b>{1}</b> <b>{2}</b>
notification.transfer.unReserved=<b>{0}</b> un-reserved <b>{1}</b> <b>{2}</b>
notification.transfer.update=<b>{0}</b> updated transfer <b>{1}</b> <b>{2}</b>
notification.transfer.delete=<b>{0}</b> deleted transfer <b>{1}</b> <b>{2}</b>
notification.transfer.return=<b>{0}</b> create new return <b>{1}</b> for transfer <b>{2}</b>  <b>{3}</b>
notification.transfer.backorder=<b>{0}</b> create new back order <b>{1}</b> for transfer <b>{2}</b>  <b>{3}</b>
notification.scrap.transfer=<b>{0}</b> create scrap <b>{1}</b> for transfer <b>{2}</b> <b>{3}</b>
notification.scrap.noneTransfer=<b>{0}</b> create scrap <b>{1}</b>
notification.scrap.info = <b>Scrap Information: </b>

notification.inventory.mention=<b>{0}</b> mentioned you in inventory adjustment <b>{1}</b>
notification.inventory.addNote=<b>{0}</b> added a note in inventory adjustment <b>{1}</b>
notification.inventory.create=<b>{0}</b> created new inventory adjustment <b>{1}</b>
notification.inventory.update=<b>{0}</b> updated inventory adjustment <b>{1}</b>
notification.inventory.delete=<b>{0}</b> deleted inventory adjustment <b>{1}</b>
notification.inventory.change=<b>Inventory Adjustment changes: </b>
notification.inventory.info=<b>Inventory Adjustment Information: </b>


notification.trace.request=<b>{0}</b> yêu cầu truy xuất <b>{1}</b>: <b>{2}</b>
notification.trace.approve=<b>{0}</b> đã <b>phê duyệt</b> yêu cầu truy xuất <b>{1}</b>: <b>{2}</b>
notification.trace.reject=<b>{0}</b> đã <b>từ chối</b> yêu cầu truy xuất <b>{1}</b>: <b>{2}</b>
notification.organization.register=<b>{0}</b> đăng ký doanh nghiệp

#mail subject


#log
log.attachments= <b>Attachments:</b>
log.inventory.zero = <b>Set Quantities To Zero</b> for Inventory Adjustment 

# Field Name
created = Created
craetedBy = Created By
updated = Updated
updatedBy = Updated By
srcLocationId = Source Location
destLocationId = Dest Location
locationId = Location
locationName = Inventory Location
operationTypeId = Operation Type
partnerId = Partner
productVersionId = Project
ownerId = Owner
scheduledDate = Scheduled Date
sourceDocument = Source Document
assigneeId = Assignee
state = Status
priority = Priority
location = Location
scrapLocation = Scrap Location
doneDate = Validate Date
inventoryOf = Inventory Of
requesterId = Request By
reference = Reference
categoryId = Category
categoryName = Inventoried Categories
productId = Product
productName = Product Name
lotId = Unit ID/Serial Number
traceNumber = Inventoried Unit ID/Serial Number
packageId = Package
packageNumber = Inventoried Package
exhaustedProducts =Is Exhausted Product
sampleQuantity = Sample Quantity
inventoryDate = Inventory Date
forceAccountingDate = Force Accounting Date
assignees = Assignees
inventoryLocation = Inventory Location
all_product = All Products
one_product_category = One Product Category
select_products_manually = Select Product Manually
one_lot_number = One Unit ID/Serial Number
one_package_number = A Pack
random_sample = Random Sample
draft = Draft
in_progress = In Progress
validate = Validate
done = Done
cancelled = Cancelled
currentDemand = Current Demand
productionQuantity = Total Quantity
mo.selected.true = Selected
mo.selected.false = Not Selected
true = Yes
false = No

