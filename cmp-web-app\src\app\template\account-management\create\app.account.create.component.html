<form [formGroup]="formAccount" (keydown.enter)="$event.preventDefault()" (ngSubmit)="onSubmitCreate()" style="position: relative">
<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{this.tranService.translate("global.menu.listaccount")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center btn-section">
        <div class="flex flex-row justify-content-right align-items-center mr-6">
            <p-button [label]="tranService.translate('global.button.save')" styleClass="p-button-info" type="submit"
                      [disabled]="formAccount.invalid || isEmailExisted || isPhoneExisted || isUsernameExisted || ((userType == CONSTANTS.USER_TYPE.PROVINCE || userType == CONSTANTS.USER_TYPE.ADMIN) &&  accountInfo.userType === CONSTANTS.USER_TYPE.CUSTOMER && !accountInfo.manager) || isSubmitting"></p-button>
            <p-button [label]="tranService.translate('global.button.cancel')" styleClass="p-button-secondary p-button-outlined ml-3" (click)="closeForm()"></p-button>
        </div>
    </div>
</div>

<p-card styleClass="mt-3 responsive-pcard">
    <p-tabView (onChange)="onTabChange($event)">
        <p-tabPanel header="{{tranService.translate('account.label.generalInfo')}}">
                <div class="flex flex-row justify-content-between account-create">
                    <div style="width: 49%;">
                        <!-- username -->
                        <div class="w-full field grid">
                            <label htmlFor="accountName" class="col-fixed" style="width:180px">{{tranService.translate("account.label.username")}}<span class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="accountName"
                                       [(ngModel)]="accountInfo.accountName"
                                       formControlName="accountName"
                                       [required]="true"
                                       [maxLength]="50"
                                       pattern="^[a-zA-Z0-9\-_]*$"
                                       [placeholder]="tranService.translate('account.text.inputUsername')"
                                       (ngModelChange)="checkExistAccount('accountName')"
                                />
                            </div>
                        </div>
                        <!-- error username -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="accountName" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="formAccount.controls.accountName.dirty && formAccount.controls.accountName.errors?.required">{{tranService.translate("global.message.required")}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.accountName.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:50})}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.accountName.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>
                                <small class="text-red-500" *ngIf="isUsernameExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("account.label.username").toLowerCase()})}}</small>
                            </div>
                        </div>
                        <!-- fullname -->
                        <div class="w-full field grid">
                            <label htmlFor="fullName" class="col-fixed" style="width:180px">{{tranService.translate("account.label.fullname")}}<span class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="fullName"
                                       [(ngModel)]="accountInfo.fullName"
                                       formControlName="fullName"
                                       [required]="true"
                                       [maxLength]="255"
                                       pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$"
                                       [placeholder]="tranService.translate('account.text.inputFullname')"
                                />
                            </div>
                        </div>
                        <!-- error fullname -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="fullName" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="formAccount.controls.fullName.dirty && formAccount.controls.fullName.errors?.required">{{tranService.translate("global.message.required")}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.fullName.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.fullName.errors?.pattern">{{tranService.translate("global.message.formatContainVN")}}</small>
                            </div>
                        </div>
                        <!-- phone -->
                        <div class="w-full field grid">
                            <label htmlFor="phone" class="col-fixed" style="width:180px">{{tranService.translate("account.label.phone")}}</label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="phone"
                                       [(ngModel)]="accountInfo.phone"
                                       formControlName="phone"
                                       pattern="^((\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$"
                                       [placeholder]="tranService.translate('account.text.inputPhone')"
                                />
                            </div>
                        </div>
                        <!-- error phone -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="phone" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="formAccount.controls.phone.errors?.pattern">{{tranService.translate("global.message.invalidPhone")}}</small>
                                <small class="text-red-500" *ngIf="isPhoneExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("account.label.phone").toLowerCase()})}}</small>
                            </div>
                        </div>
                        <!-- email -->
                        <div class="w-full field grid">
                            <label htmlFor="email" class="col-fixed" style="width:180px">{{tranService.translate("account.label.email")}}<span class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="email"
                                       [(ngModel)]="accountInfo.email"
                                       formControlName="email"
                                       [required]="true"
                                       [maxLength]="255"
                                       pattern="^[a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+)+(\.[a-z]{2,})$"
                                       [placeholder]="tranService.translate('account.text.inputEmail')"
                                       (ngModelChange)="checkExistAccount('email')"
                                />
                            </div>
                        </div>
                        <!-- error email -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="email" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="formAccount.controls.email.dirty && formAccount.controls.email.errors?.required">{{tranService.translate("global.message.required")}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.email.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                                <small class="text-red-500" *ngIf="formAccount.controls.email.errors?.pattern">{{tranService.translate("global.message.invalidEmail")}}</small>
                                <small class="text-red-500" *ngIf="isEmailExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("account.label.email").toLowerCase()})}}</small>
                            </div>
                        </div>
                        <!-- description -->
                        <div class="w-full field grid">
                            <label htmlFor="description" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate("account.label.description")}}</label>
                            <div class="col">
                            <textarea  class="w-full" style="resize: none;"
                                       rows="5"
                                       [autoResize]="false"
                                       pInputTextarea id="description"
                                       [(ngModel)]="accountInfo.description"
                                       formControlName="description"
                                       [maxlength]="255"
                                       [placeholder]="tranService.translate('sim.text.inputDescription')"
                            ></textarea>
                            </div>
                        </div>
                        <!-- error description -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="description" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="formAccount.controls.description.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                            </div>
                        </div>
                    </div>
                    <div style="width: 49%;">

                        <!-- loai tai khoan -->
                        <div class="w-full field grid">
                            <label for="userType" class="col-fixed" style="width:180px">{{tranService.translate("account.label.userType")}}<span class="text-red-500">*</span></label>
                            <div class="col">
                                <p-dropdown styleClass="w-full"
                                            [showClear]="true"
                                            id="userType" [autoDisplayFirst]="false"
                                            [(ngModel)]="accountInfo.userType"
                                            [required]="true"
                                            formControlName="userType"
                                            [options]="statusAccounts"
                                            optionLabel="name"
                                            optionValue="value"
                                            [placeholder]="tranService.translate('account.text.selectUserType')"
                                            (ngModelChange)="getListRole(true)"
                                ></p-dropdown>
                            </div>
                        </div>
                        <!-- error loai tai khoan -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="userType" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="formAccount.controls.userType.dirty && formAccount.controls.userType.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            </div>
                        </div>
                        <!-- Tinh thanh pho -->
                        <div class="w-full field grid" [class]=" (accountInfo.userType == optionUserType.ADMIN) ? 'hidden' : ''">
                            <label htmlFor="province" class="col-fixed" style="width:180px">{{tranService.translate("account.label.province")}}<span class="text-red-500">*</span></label>
                            <div class="col">
                                <p-dropdown styleClass="w-full"
                                            [showClear]="userType == optionUserType.ADMIN && accountInfo.userType != optionUserType.ADMIN && accountInfo.userType != optionUserType.AGENCY"
                                            [readonly]="userType != optionUserType.ADMIN"
                                            [disabled]="userType != optionUserType.ADMIN"
                                            id="province" [autoDisplayFirst]="false"
                                            [(ngModel)]="accountInfo.province"
                                            [required]="userType == optionUserType.ADMIN && accountInfo.userType != optionUserType.ADMIN && accountInfo.userType != optionUserType.AGENCY"
                                            formControlName="province"
                                            [options]="listProvince"
                                            optionLabel="name"
                                            [filter]="true" filterBy="name"
                                            optionValue="id"
                                            [placeholder]="tranService.translate('account.text.selectProvince')"
                                            (ngModelChange)="getListCustomer(true)"
                                ></p-dropdown>
                            </div>
                        </div>
                        <!-- error tinh thanh pho -->
                        <div class="w-full field grid text-error-field" [class]="userType == optionUserType.ADMIN && accountInfo.userType != optionUserType.ADMIN && accountInfo.userType != optionUserType.AGENCY ? '' : 'hidden'">
                            <label htmlFor="province" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="formAccount.controls.province.dirty && formAccount.controls.province.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            </div>
                        </div>
                        <!-- GDV quan ly-->
                        <div class="w-full field grid" [class]="accountInfo.userType == optionUserType.CUSTOMER && (userType == optionUserType.ADMIN || userType == optionUserType.PROVINCE) ? '' : 'hidden'">
                            <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate("account.label.managerName")}}<span class="text-red-500">*</span></label>
<!--                            <div class="col" style="max-width: calc(100% - 180px) !important;">-->
                            <div class="col">
                                <vnpt-select
                                    [control]="controlComboSelectManager"
                                    class="w-full"
                                    [(value)]="accountInfo.manager"
                                    [placeholder]="tranService.translate('account.text.selectGDV')"
                                    objectKey="account"
                                    paramKey="name"
                                    keyReturn="id"
                                    displayPattern="${fullName} - ${username}"
                                    typeValue="primitive"
                                    [isMultiChoice]="false"
                                    [required] = "accountInfo.userType == optionUserType.CUSTOMER ? true :false"
                                    [paramDefault]="paramSearchManager"
                                    (onchange)="onChangeTeller()"
                                ></vnpt-select>
                            </div>
                        </div>
                        <!-- error GDV quan ly -->
                        <div class="w-full field grid text-error-field" *ngIf="accountInfo.userType == optionUserType.CUSTOMER">
                            <label htmlFor="province" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="controlComboSelectManager.dirty && controlComboSelectManager.error.required">{{tranService.translate("global.message.required")}}</small>
                            </div>
                        </div>
                        <!--                    &lt;!&ndash; ten khach hang &ndash;&gt;-->
                        <!--                    <div class="w-full field grid" [class]="accountInfo.userType == optionUserType.CUSTOMER ? '' : 'hidden'">-->
                        <!--                        <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate("account.label.customerName")}}<span class="text-red-500">*</span></label>-->
                        <!--                        <div class="col" style="max-width: calc(100% - 180px) !important;">-->
                        <!--                            <vnpt-select-->
                        <!--                                [control]="controlComboSelect"-->
                        <!--                                class="w-full"-->
                        <!--                                [(value)]="accountInfo.customers"-->
                        <!--                                [placeholder]="tranService.translate('account.text.selectCustomers')"-->
                        <!--                                objectKey="customer"-->
                        <!--                                paramKey="customerName"-->
                        <!--                                keyReturn="id"-->
                        <!--                                displayPattern="${customerName} - ${customerCode}"-->
                        <!--                                typeValue="primitive"-->
                        <!--                                [paramDefault]="paramSearchCustomerProvince"-->
                        <!--                                [required]="accountInfo.userType == optionUserType.CUSTOMER"-->
                        <!--                            ></vnpt-select>-->
                        <!--                        </div>-->
                        <!--                    </div>-->
                        <!--                    &lt;!&ndash; error khach hang &ndash;&gt;-->
                        <!--                    <div class="w-full field grid text-error-field" *ngIf="accountInfo.userType == optionUserType.CUSTOMER">-->
                        <!--                        <label htmlFor="province" class="col-fixed" style="width:180px"></label>-->
                        <!--                        <div class="col">-->
                        <!--                            <small class="text-red-500" *ngIf="controlComboSelect.dirty && controlComboSelect.error.required">{{tranService.translate("global.message.required")}}</small>-->
                        <!--                        </div>-->
                        <!--                    </div>-->

                        <!-- Danh sach tai khoan khach hang -->
                        <div class="w-full field grid" *ngIf="accountInfo.userType == optionUserType.DISTRICT">
                            <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate("account.label.customerAccount")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px);">
<!--                            <div class="col">-->
                                <vnpt-select
                                    [control]="controlComboSelectCustomerAccount"
                                    class="w-full"
                                    [(value)]="accountInfo.customerAccounts"
                                    [placeholder]="tranService.translate('account.text.selectCustomerAccount')"
                                    objectKey="account"
                                    paramKey="username"
                                    keyReturn="id"
                                    displayPattern="${fullName} - ${username}"
                                    typeValue="primitive"
                                    [paramDefault]="accountInfo.manager != null ? paramSearchCustomerAccount : paramSearchCustomerAccount"
                                    [loadData]="loadCustomerAccount.bind(this)"
                                ></vnpt-select>
                            </div>
                        </div>
                        <!-- error Danh sach tai khoan khach hang-->
                        <div class="w-full field grid text-error-field" *ngIf="accountInfo.userType == optionUserType.DISTRICT">
                            <label htmlFor="province" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="controlComboSelectCustomerAccount.dirty && controlComboSelectCustomerAccount.error.required">{{tranService.translate("global.message.required")}}</small>
                            </div>
                        </div>
                        <!-- Danh sách tài khoản khách hàng để chọn tài khoản khách hàng root -->
                        <div class="w-full field grid" *ngIf="accountInfo.userType == optionUserType.CUSTOMER &&
                        (userType == CONSTANTS.USER_TYPE.ADMIN || userType == CONSTANTS.USER_TYPE.PROVINCE || userType == CONSTANTS.USER_TYPE.DISTRICT)">
                            <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate("account.label.customerAccount")}}</label>
<!--                            <div class="col" style="max-width: calc(100% - 180px) !important;">-->
                            <div class="col">
                                <vnpt-select
                                    [control]="controlComboSelectCustomerAccount"
                                    class="w-full"
                                    [(value)]="accountInfo.accountRootId"
                                    [placeholder]="tranService.translate('account.text.selectCustomerAccount')"
                                    objectKey="account"
                                    paramKey="username"
                                    keyReturn="id"
                                    displayPattern="${fullName} - ${username}"
                                    typeValue="primitive"
                                    [paramDefault]="paramSearchCustomerAccount"
                                    [loadData]="loadCustomerAccount.bind(this)"
                                    (onchange)="getListCustomer(false)"
                                    [isMultiChoice]="false"
                                ></vnpt-select>
                            </div>
                        </div>
                        <!--                    <div class="w-full field grid" *ngIf="accountInfo.userType == optionUserType.CUSTOMER">-->
                        <!--                        <i htmlFor="roles" class="col-fixed" style="width:180px; text-decoration: underline" [ngStyle]="{color: 'var(&#45;&#45;mainColorText)', cursor: 'pointer'}" (click)="showDialogCustomer()">{{tranService.translate("account.text.addCustomer")}}<span>*</span></i>-->
                        <!--                    </div>-->
                        <!--                    <div class="w-full field grid" *ngIf="accountInfo.userType == optionUserType.CUSTOMER">-->
                        <!--                        <i htmlFor="roles" class="col-fixed" style="width:180px; text-decoration: underline" [ngStyle]="{color: 'var(&#45;&#45;mainColorText)', cursor: 'pointer'}" >{{tranService.translate("account.text.addContract")}}</i>-->
                        <!--                    </div>-->
                        <!-- nhom quyen -->
                        <div class="w-full field grid">
                            <label htmlFor="roles" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("account.label.role") }}</label>
                            <div class="col" style="max-width: calc(100% - 180px)">
                                <p-multiSelect styleClass="w-full"
                                               id="roles"
                                               [(ngModel)]="accountInfo.roles"
                                               [options]="listRole"
                                               [defaultLabel]="tranService.translate('account.text.selectRoles')"
                                               optionLabel="name"
                                               display="chip"
                                               formControlName="roles"
                                               [resetFilterOnHide]=true
                                ></p-multiSelect>
                            </div>
                        </div>
                    </div>
                </div>
<!--                <div class="flex flex-row justify-content-between align-items-center" *ngIf="accountInfo.userType == optionUserType.CUSTOMER">-->
<!--                </div>-->

        </p-tabPanel>
        <p-tabPanel header="{{tranService.translate('account.text.addCustomer')}}*" *ngIf="checkShowTabAddCustomerAndContract()">
            <div class="flex flex-row justify-content-center gap-3 mt-4">
                <input style="min-width: 35vw"  type="text" pInputText [placeholder]="tranService.translate('sim.label.quickSearch')" (keydown.enter)="$event.preventDefault(); onSearchCustomer(true)" [(ngModel)]="paramQuickSearchCustomer.keyword" [ngModelOptions]="{standalone: true}">
                <p-button icon="pi pi-search"
                          styleClass="ml-3 p-button-rounded p-button-secondary p-button-text button-search"
                          type="button"
                          (click)="onSearchCustomer(true)"
                ></p-button>
            </div>
            <div class="flex relative justify-content-center align-items-center w-full h-full">
                <div class="absolute flex justify-content-center align-items-center w-full h-full" [ngStyle]="{
                           'top': '50%',
                           'left': '50%',
                           'transform': 'translate(-50%, -50%)',
                           'z-index': '10',
                           'background': 'rgba(0, 0, 0, 0.1)'
                         }"
                     *ngIf="loadingCustomer">
                    <p-progressSpinner></p-progressSpinner>
                </div>
                <div class="w-full h-full">
                    <table-vnpt
                        [fieldId]="'id'"
                        [pageNumber]="paginationCustomer.page"
                        [pageSize]="paginationCustomer.size"
                        [(selectItems)]="selectItemCustomer"
                        [columns]="columnInfoCustomer"
                        [dataSet]="dataSetCustomer"
                        [options]="optionTableCustomer"
                        [loadData]="searchCustomer.bind(this)"
                        [rowsPerPageOptions]="[5,10,20,25,50]"
                        [scrollHeight]="'400px'"
                        [sort]="paginationCustomer.sortBy"
                        [params]="paramQuickSearchCustomer"
                        (selectItemsChange)="checkSelectItemChangeCustomer($event)"
                        (onChangeCustomSelectAllEmmiter)="onChangeSelectAllItemsCustomer()"
                        isUseCustomSelectAll="true"
                        [(customSelectAll)]="customSelectAllCustomer"
                    ></table-vnpt>
                </div>
            </div>
        </p-tabPanel>
        <p-tabPanel header="{{tranService.translate('account.text.addContract')}}" *ngIf="checkShowTabAddCustomerAndContract() && selectItemCustomer && selectItemCustomer.length > 0">
            <div class="flex flex-row justify-content-center gap-3 mt-4">
                <input style="min-width: 35vw"  type="text" pInputText [placeholder]="tranService.translate('sim.label.quickSearch')" (keydown.enter)="$event.preventDefault(); onSearchContract(true)" [(ngModel)]="paramQuickSearchContract.keyword" [ngModelOptions]="{standalone: true}">
                <p-button icon="pi pi-search"
                          styleClass="ml-3 p-button-rounded p-button-secondary p-button-text button-search"
                          type="button"
                          (click)="onSearchContract(true)"
                ></p-button>
            </div>
            <div class="flex relative justify-content-center align-items-center w-full h-full">
                <div class="absolute flex justify-content-center align-items-center w-full h-full" [ngStyle]="{
                           'top': '50%',
                           'left': '50%',
                           'transform': 'translate(-50%, -50%)',
                           'z-index': '10',
                           'background': 'rgba(0, 0, 0, 0.1)'
                         }"
                     *ngIf="loadingContract">
                    <p-progressSpinner></p-progressSpinner>
                </div>
               <div class="w-full h-full">
                    <table-vnpt
                        [fieldId]="'id'"
                        [pageNumber]="paginationContract.page"
                        [pageSize]="paginationContract.size"
                        [(selectItems)]="selectItemContract"
                        [columns]="columnInfoContract"
                        [dataSet]="dataSetContract"
                        [options]="optionTableContract"
                        [loadData]="searchContract.bind(this)"
                        [rowsPerPageOptions]="[5,10,20,25,50]"
                        [scrollHeight]="'400px'"
                        [sort]="paginationContract.sortBy"
                        [params]="paramQuickSearchContract"
                        (selectItemsChange)="checkSelectItemChangeContract($event)"
                        (onChangeCustomSelectAllEmmiter)="onChangeSelectAllItemsContract()"
                        isUseCustomSelectAll="true"
                        [(customSelectAll)]="customSelectAllContract"
                    ></table-vnpt>
               </div>
            </div>
        </p-tabPanel>

        <p-tabPanel header="{{tranService.translate('account.text.grantApi')}}" *ngIf="(checkAuthen([CONSTANTS.PERMISSIONS.THIRD_PARTY_API.GRANT_PERMISSION_3RD_API])) && accountInfo.userType == optionUserType.CUSTOMER" [pt]="'ProfileTab'">
            <div class="mb-3">
                <p-panel [showHeader]="false">
                    <div class="flex gap-2">
                        <p-radioButton
                                [label]="tranService.translate('account.text.working')"
                                value="1"
                                class="p-3"
                                [(ngModel)]="statusGrantApi"
                                inputId="1"
                                [ngModelOptions]="{standalone: true}"
                        >
                        </p-radioButton>
                        <p-radioButton
                                [label]="tranService.translate('account.text.notWorking')"
                                value="0"
                                class="p-3"
                                [(ngModel)]="statusGrantApi"
                                [ngModelOptions]="{standalone: true}"
                        >
                        </p-radioButton>
                    </div>
                    <div class="flex gap-3 align-items-center">
                        <div class="col-5">
                            <div class="flex align-items-center">
                                <label style="min-width: 100px" class="mr-3">Client ID</label>
                                <input [(ngModel)]="genGrantApi.clientId"  [disabled]="true" [ngModelOptions]="{standalone: true}" class="w-full" type="text" pInputText>
                            </div>
                        </div>
                        <div class="col-5">
                            <div class="flex align-items-center">
                                <label style="min-width: 100px" class="mr-3">Secret Key</label>
                                <div class="w-full flex align-items-center">
                                    <input class="w-full mr-2" style="padding-right: 30px;"
                                           [(ngModel)]="genGrantApi.secretKey"
                                           [ngModelOptions]="{standalone: true}"
                                           [type]="isShowSecretKey ? 'text': 'password'"
                                           pInputText
                                           [disabled]="true"
                                    />
                                    <label style="margin-left: -30px;z-index: 1;" *ngIf="isShowSecretKey == false" class="pi pi-eye toggle-password" (click)="isShowSecretKey = true"></label>
                                    <label style="margin-left: -30px;z-index: 1;" *ngIf="isShowSecretKey == true" class="pi pi-eye-slash toggle-password" (click)="isShowSecretKey = false"></label>
                                </div>
                            </div>
                        </div>
                        <div class="col-2">
                            <p-button (click)="genToken()" [label]="tranService.translate('account.text.gen')" styleClass="p-button-primary mr-2"></p-button>
                        </div>
                    </div>
                </p-panel>
            </div>
            <div>
                <p-panel [showHeader]="false" class="  " *ngIf="genGrantApi.secretKey">
                    <div class="flex gap-3 align-items-center">
                        <div class="col-3">
                            <p-dropdown class="w-full"
                                        [showClear]="true"
                                        [(ngModel)]="paramsSearchGrantApi.module"
                                        [ngModelOptions]="{standalone: true}"
                                        [options]="listModule"
                                        optionLabel="name"
                                        optionValue="value"
                                        [emptyFilterMessage]="tranService.translate('global.text.nodata')"
                                        filter="true"
                                        [placeholder]="tranService.translate('account.text.module')"
                            ></p-dropdown>
                        </div>
                        <div class="col-3">
                            <input [(ngModel)]="paramsSearchGrantApi.api" [ngModelOptions]="{standalone: true}" class="w-full mr-2" type="text" pInputText placeholder="API"/>
                        </div>
                        <p-button icon="pi pi-search"
                                  styleClass="ml-3 p-button-rounded p-button-secondary p-button-text button-search"
                                  type="button"
                                  (click)="onSearchGrantApi(true)"
                        ></p-button>
                    </div>

                    <table-vnpt
                            [fieldId]="'id'"
                            [pageNumber]="paginationGrantApi.page"
                            [pageSize]="paginationGrantApi.size"
                            [(selectItems)]="selectItemGrantApi"
                            [columns]="columnInfoGrantApi"
                            [dataSet]="dataSetGrantApi"
                            [options]="optionTableGrantApi"
                            [loadData]="searchGrantApi.bind(this)"
                            [rowsPerPageOptions]="[5,10,20,25,50]"
                            [scrollHeight]="'400px'"
                            [sort]="paginationGrantApi.sortBy"
                            [params]="paramsSearchGrantApi"
                    ></table-vnpt>
                </p-panel>
            </div>
        </p-tabPanel>
    </p-tabView>
<!--        <div class="flex flex-row justify-content-center align-items-center">-->
<!--            <p-button [label]="tranService.translate('global.button.cancel')" styleClass="p-button-secondary p-button-outlined mr-2" (click)="closeForm()"></p-button>-->
<!--            <p-button [label]="tranService.translate('global.button.save')" styleClass="p-button-info" type="submit"-->
<!--                      [disabled]="formAccount.invalid || isEmailExisted || isPhoneExisted || isUsernameExisted"></p-button>-->
<!--        </div>-->
</p-card>
</form>

