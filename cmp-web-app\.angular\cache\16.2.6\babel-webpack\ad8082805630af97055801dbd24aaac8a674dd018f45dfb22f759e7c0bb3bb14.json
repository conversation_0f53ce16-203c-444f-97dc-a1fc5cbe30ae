{"ast": null, "code": "import { ComponentBase } from \"src/app/component.base\";\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\nimport { ComboLazyControl } from \"../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/service/account/AccountService\";\nimport * as i2 from \"src/app/service/customer/CustomerService\";\nimport * as i3 from \"../../../service/contract/ContractService\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/breadcrumb\";\nimport * as i7 from \"primeng/inputtext\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"../../common-module/table/table.component\";\nimport * as i10 from \"../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i11 from \"primeng/dropdown\";\nimport * as i12 from \"primeng/card\";\nimport * as i13 from \"primeng/inputtextarea\";\nimport * as i14 from \"primeng/panel\";\nimport * as i15 from \"primeng/radiobutton\";\nimport * as i16 from \"primeng/tabview\";\nimport * as i17 from \"primeng/progressspinner\";\nfunction AppAccountEditComponent_form_0_small_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.tranService.translate(\"global.message.required\"));\n  }\n}\nconst _c0 = function () {\n  return {\n    len: 50\n  };\n};\nfunction AppAccountEditComponent_form_0_small_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction AppAccountEditComponent_form_0_small_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"global.message.formatCode\"));\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    type: a0\n  };\n};\nfunction AppAccountEditComponent_form_0_small_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.tranService.translate(\"global.message.exists\", i0.ɵɵpureFunction1(1, _c1, ctx_r4.tranService.translate(\"account.label.username\").toLowerCase())));\n  }\n}\nfunction AppAccountEditComponent_form_0_small_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.tranService.translate(\"global.message.required\"));\n  }\n}\nconst _c2 = function () {\n  return {\n    len: 255\n  };\n};\nfunction AppAccountEditComponent_form_0_small_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r6.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c2)));\n  }\n}\nfunction AppAccountEditComponent_form_0_small_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.tranService.translate(\"global.message.formatContainVN\"));\n  }\n}\nfunction AppAccountEditComponent_form_0_small_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r8.tranService.translate(\"global.message.invalidPhone\"));\n  }\n}\nfunction AppAccountEditComponent_form_0_small_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r9.tranService.translate(\"global.message.exists\", i0.ɵɵpureFunction1(1, _c1, ctx_r9.tranService.translate(\"account.label.phone\").toLowerCase())));\n  }\n}\nfunction AppAccountEditComponent_form_0_small_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r10.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction AppAccountEditComponent_form_0_small_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r11.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c2)));\n  }\n}\nfunction AppAccountEditComponent_form_0_small_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r12.tranService.translate(\"global.message.invalidEmail\"));\n  }\n}\nfunction AppAccountEditComponent_form_0_small_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r13.tranService.translate(\"global.message.exists\", i0.ɵɵpureFunction1(1, _c1, ctx_r13.tranService.translate(\"account.label.email\").toLowerCase())));\n  }\n}\nfunction AppAccountEditComponent_form_0_small_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r14.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c2)));\n  }\n}\nfunction AppAccountEditComponent_form_0_small_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r15.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction AppAccountEditComponent_form_0_div_88_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"label\", 43);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 17);\n    i0.ɵɵtext(4, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 18)(6, \"p-dropdown\", 44);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountEditComponent_form_0_div_88_Template_p_dropdown_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.accountInfo.province = $event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r16.tranService.translate(\"account.label.province\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx_r16.accountInfo.province)(\"required\", ctx_r16.userType == ctx_r16.optionUserType.ADMIN && ctx_r16.accountInfo.userType != ctx_r16.optionUserType.ADMIN && ctx_r16.accountInfo.userType != ctx_r16.optionUserType.AGENCY)(\"options\", ctx_r16.listProvince)(\"filter\", true)(\"placeholder\", ctx_r16.tranService.translate(\"account.text.selectProvince\"));\n  }\n}\nfunction AppAccountEditComponent_form_0_div_89_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r28.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction AppAccountEditComponent_form_0_div_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵelement(1, \"label\", 43);\n    i0.ɵɵelementStart(2, \"div\", 18);\n    i0.ɵɵtemplate(3, AppAccountEditComponent_form_0_div_89_small_3_Template, 2, 1, \"small\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.formAccount.controls.province.dirty && (ctx_r17.formAccount.controls.province.errors == null ? null : ctx_r17.formAccount.controls.province.errors.required));\n  }\n}\nfunction AppAccountEditComponent_form_0_span_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 17);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppAccountEditComponent_form_0_div_96_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r29.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction AppAccountEditComponent_form_0_div_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵelement(1, \"label\", 43);\n    i0.ɵɵelementStart(2, \"div\", 18);\n    i0.ɵɵtemplate(3, AppAccountEditComponent_form_0_div_96_small_3_Template, 2, 1, \"small\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.controlComboSelectManager.dirty && ctx_r19.controlComboSelectManager.error.required);\n  }\n}\nfunction AppAccountEditComponent_form_0_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"label\", 36);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 37)(4, \"vnpt-select\", 45);\n    i0.ɵɵlistener(\"valueChange\", function AppAccountEditComponent_form_0_div_97_Template_vnpt_select_valueChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.accountInfo.customerAccounts = $event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r20.tranService.translate(\"account.label.customerAccount\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"control\", ctx_r20.controlComboSelectCustomerAccount)(\"value\", ctx_r20.accountInfo.customerAccounts)(\"placeholder\", ctx_r20.tranService.translate(\"account.text.selectCustomerAccount\"))(\"paramDefault\", ctx_r20.paramSearchCustomerAccount)(\"loadData\", ctx_r20.loadCustomerAccount.bind(ctx_r20));\n  }\n}\nfunction AppAccountEditComponent_form_0_div_98_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r32.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction AppAccountEditComponent_form_0_div_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵelement(1, \"label\", 43);\n    i0.ɵɵelementStart(2, \"div\", 18);\n    i0.ɵɵtemplate(3, AppAccountEditComponent_form_0_div_98_small_3_Template, 2, 1, \"small\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r21.controlComboSelectCustomerAccount.dirty && ctx_r21.controlComboSelectCustomerAccount.error.required);\n  }\n}\nfunction AppAccountEditComponent_form_0_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"label\", 36);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 37);\n    i0.ɵɵelement(4, \"input\", 47);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r22.tranService.translate(\"account.label.customerAccount\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"value\", (ctx_r22.accountResponse == null ? null : ctx_r22.accountResponse.rootAccount == null ? null : ctx_r22.accountResponse.rootAccount.username) ? ctx_r22.accountResponse == null ? null : ctx_r22.accountResponse.rootAccount == null ? null : ctx_r22.accountResponse.rootAccount.username : ctx_r22.tranService.translate(\"account.text.selectCustomerAccount\"));\n    i0.ɵɵproperty(\"disabled\", true)(\"readonly\", true);\n  }\n}\nconst _c3 = function () {\n  return {\n    \"top\": \"50%\",\n    \"left\": \"50%\",\n    \"transform\": \"translate(-50%, -50%)\",\n    \"z-index\": \"10\",\n    \"background\": \"rgba(0, 0, 0, 0.1)\"\n  };\n};\nfunction AppAccountEditComponent_form_0_p_tabPanel_105_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nconst _c4 = function () {\n  return {\n    standalone: true\n  };\n};\nconst _c5 = function () {\n  return [5, 10, 20, 25, 50];\n};\nfunction AppAccountEditComponent_form_0_p_tabPanel_105_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 12)(1, \"div\", 48)(2, \"input\", 49);\n    i0.ɵɵlistener(\"keydown.enter\", function AppAccountEditComponent_form_0_p_tabPanel_105_Template_input_keydown_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext(2);\n      $event.preventDefault();\n      return i0.ɵɵresetView(ctx_r34.onSearchCustomer(true));\n    })(\"ngModelChange\", function AppAccountEditComponent_form_0_p_tabPanel_105_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.paramQuickSearchCustomer.keyword = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-button\", 50);\n    i0.ɵɵlistener(\"click\", function AppAccountEditComponent_form_0_p_tabPanel_105_Template_p_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.onSearchCustomer(true));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 51);\n    i0.ɵɵtemplate(5, AppAccountEditComponent_form_0_p_tabPanel_105_div_5_Template, 2, 2, \"div\", 52);\n    i0.ɵɵelementStart(6, \"div\", 53)(7, \"table-vnpt\", 54);\n    i0.ɵɵlistener(\"selectItemsChange\", function AppAccountEditComponent_form_0_p_tabPanel_105_Template_table_vnpt_selectItemsChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r38 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r38.selectItemCustomer = $event);\n    })(\"selectItemsChange\", function AppAccountEditComponent_form_0_p_tabPanel_105_Template_table_vnpt_selectItemsChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r39 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r39.checkSelectItemChangeCustomer($event));\n    })(\"onChangeCustomSelectAllEmmiter\", function AppAccountEditComponent_form_0_p_tabPanel_105_Template_table_vnpt_onChangeCustomSelectAllEmmiter_7_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r40 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r40.onChangeSelectAllItemsCustomer());\n    })(\"customSelectAllChange\", function AppAccountEditComponent_form_0_p_tabPanel_105_Template_table_vnpt_customSelectAllChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r41 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r41.customSelectAllCustomer = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate1(\"header\", \"\", ctx_r23.tranService.translate(\"account.text.addCustomer\"), \"*\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"placeholder\", ctx_r23.tranService.translate(\"sim.label.quickSearch\"))(\"ngModel\", ctx_r23.paramQuickSearchCustomer.keyword)(\"ngModelOptions\", i0.ɵɵpureFunction0(18, _c4));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r23.loadingCustomer);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"pageNumber\", ctx_r23.paginationCustomer.page)(\"pageSize\", ctx_r23.paginationCustomer.size)(\"selectItems\", ctx_r23.selectItemCustomer)(\"columns\", ctx_r23.columnInfoCustomer)(\"dataSet\", ctx_r23.dataSetCustomer)(\"options\", ctx_r23.optionTableCustomer)(\"loadData\", ctx_r23.searchCustomer.bind(ctx_r23))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(19, _c5))(\"scrollHeight\", \"400px\")(\"sort\", ctx_r23.paginationCustomer.sortBy)(\"params\", ctx_r23.paramQuickSearchCustomer)(\"customSelectAll\", ctx_r23.customSelectAllCustomer);\n  }\n}\nfunction AppAccountEditComponent_form_0_p_tabPanel_106_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nfunction AppAccountEditComponent_form_0_p_tabPanel_106_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 12)(1, \"div\", 48)(2, \"input\", 49);\n    i0.ɵɵlistener(\"keydown.enter\", function AppAccountEditComponent_form_0_p_tabPanel_106_Template_input_keydown_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r43 = i0.ɵɵnextContext(2);\n      $event.preventDefault();\n      return i0.ɵɵresetView(ctx_r43.onSearchContract(true));\n    })(\"ngModelChange\", function AppAccountEditComponent_form_0_p_tabPanel_106_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r45.paramQuickSearchContract.keyword = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-button\", 50);\n    i0.ɵɵlistener(\"click\", function AppAccountEditComponent_form_0_p_tabPanel_106_Template_p_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r46 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r46.onSearchContract(true));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 51);\n    i0.ɵɵtemplate(5, AppAccountEditComponent_form_0_p_tabPanel_106_div_5_Template, 2, 2, \"div\", 52);\n    i0.ɵɵelementStart(6, \"div\", 53)(7, \"table-vnpt\", 54);\n    i0.ɵɵlistener(\"selectItemsChange\", function AppAccountEditComponent_form_0_p_tabPanel_106_Template_table_vnpt_selectItemsChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r47 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r47.selectItemContract = $event);\n    })(\"selectItemsChange\", function AppAccountEditComponent_form_0_p_tabPanel_106_Template_table_vnpt_selectItemsChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r48.checkSelectItemChangeContract($event));\n    })(\"onChangeCustomSelectAllEmmiter\", function AppAccountEditComponent_form_0_p_tabPanel_106_Template_table_vnpt_onChangeCustomSelectAllEmmiter_7_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r49 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r49.onChangeSelectAllItemsContract());\n    })(\"customSelectAllChange\", function AppAccountEditComponent_form_0_p_tabPanel_106_Template_table_vnpt_customSelectAllChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r50 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r50.customSelectAllContract = $event);\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r24.tranService.translate(\"account.text.addContract\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"placeholder\", ctx_r24.tranService.translate(\"sim.label.quickSearch\"))(\"ngModel\", ctx_r24.paramQuickSearchContract.keyword)(\"ngModelOptions\", i0.ɵɵpureFunction0(18, _c4));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r24.loadingContract);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"pageNumber\", ctx_r24.paginationContract.page)(\"pageSize\", ctx_r24.paginationContract.size)(\"selectItems\", ctx_r24.selectItemContract)(\"columns\", ctx_r24.columnInfoContract)(\"dataSet\", ctx_r24.dataSetContract)(\"options\", ctx_r24.optionTableContract)(\"loadData\", ctx_r24.searchContract.bind(ctx_r24))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(19, _c5))(\"scrollHeight\", \"400px\")(\"sort\", ctx_r24.paginationContract.sortBy)(\"params\", ctx_r24.paramQuickSearchContract)(\"customSelectAll\", ctx_r24.customSelectAllContract);\n  }\n}\nfunction AppAccountEditComponent_form_0_p_tabPanel_107_label_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 74);\n    i0.ɵɵlistener(\"click\", function AppAccountEditComponent_form_0_p_tabPanel_107_label_18_Template_label_click_0_listener() {\n      i0.ɵɵrestoreView(_r55);\n      const ctx_r54 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r54.isShowSecretKey = true);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppAccountEditComponent_form_0_p_tabPanel_107_label_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r57 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 75);\n    i0.ɵɵlistener(\"click\", function AppAccountEditComponent_form_0_p_tabPanel_107_label_19_Template_label_click_0_listener() {\n      i0.ɵɵrestoreView(_r57);\n      const ctx_r56 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r56.isShowSecretKey = false);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppAccountEditComponent_form_0_p_tabPanel_107_p_panel_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r59 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-panel\", 58)(1, \"div\", 76)(2, \"div\", 77)(3, \"p-dropdown\", 78);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountEditComponent_form_0_p_tabPanel_107_p_panel_23_Template_p_dropdown_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r58 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r58.paramsSearchGrantApi.module = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 77)(5, \"input\", 79);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountEditComponent_form_0_p_tabPanel_107_p_panel_23_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r60 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r60.paramsSearchGrantApi.api = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p-button\", 50);\n    i0.ɵɵlistener(\"click\", function AppAccountEditComponent_form_0_p_tabPanel_107_p_panel_23_Template_p_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r61 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r61.onSearchGrantApi(true));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"table-vnpt\", 80);\n    i0.ɵɵlistener(\"selectItemsChange\", function AppAccountEditComponent_form_0_p_tabPanel_107_p_panel_23_Template_table_vnpt_selectItemsChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r59);\n      const ctx_r62 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r62.selectItemGrantApi = $event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"showHeader\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"showClear\", true)(\"ngModel\", ctx_r53.paramsSearchGrantApi.module)(\"ngModelOptions\", i0.ɵɵpureFunction0(21, _c4))(\"options\", ctx_r53.listModule)(\"emptyFilterMessage\", ctx_r53.tranService.translate(\"global.text.nodata\"))(\"placeholder\", ctx_r53.tranService.translate(\"account.text.module\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r53.paramsSearchGrantApi.api)(\"ngModelOptions\", i0.ɵɵpureFunction0(22, _c4));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"pageNumber\", ctx_r53.paginationGrantApi.page)(\"pageSize\", ctx_r53.paginationGrantApi.size)(\"selectItems\", ctx_r53.selectItemGrantApi)(\"columns\", ctx_r53.columnInfoGrantApi)(\"dataSet\", ctx_r53.dataSetGrantApi)(\"options\", ctx_r53.optionTableGrantApi)(\"loadData\", ctx_r53.searchGrantApi.bind(ctx_r53))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(23, _c5))(\"scrollHeight\", \"400px\")(\"sort\", ctx_r53.paginationGrantApi.sortBy)(\"params\", ctx_r53.paramsSearchGrantApi);\n  }\n}\nfunction AppAccountEditComponent_form_0_p_tabPanel_107_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 56)(1, \"div\", 57)(2, \"p-panel\", 58)(3, \"div\", 59)(4, \"p-radioButton\", 60);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountEditComponent_form_0_p_tabPanel_107_Template_p_radioButton_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r63 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r63.statusGrantApi = $event);\n    })(\"onClick\", function AppAccountEditComponent_form_0_p_tabPanel_107_Template_p_radioButton_onClick_4_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r65 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r65.changeGrantApiPermission());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p-radioButton\", 61);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountEditComponent_form_0_p_tabPanel_107_Template_p_radioButton_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r66 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r66.statusGrantApi = $event);\n    })(\"onClick\", function AppAccountEditComponent_form_0_p_tabPanel_107_Template_p_radioButton_onClick_5_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r67 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r67.changeGrantApiPermission());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 62)(7, \"div\", 63)(8, \"div\", 64)(9, \"label\", 65);\n    i0.ɵɵtext(10, \"Client ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 66);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountEditComponent_form_0_p_tabPanel_107_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r68 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r68.genGrantApi.clientId = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 63)(13, \"div\", 64)(14, \"label\", 65);\n    i0.ɵɵtext(15, \"Secret Key\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 67)(17, \"input\", 68);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountEditComponent_form_0_p_tabPanel_107_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r69 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r69.genGrantApi.secretKey = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, AppAccountEditComponent_form_0_p_tabPanel_107_label_18_Template, 1, 0, \"label\", 69);\n    i0.ɵɵtemplate(19, AppAccountEditComponent_form_0_p_tabPanel_107_label_19_Template, 1, 0, \"label\", 70);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 71)(21, \"p-button\", 72);\n    i0.ɵɵlistener(\"click\", function AppAccountEditComponent_form_0_p_tabPanel_107_Template_p_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r70 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r70.genToken());\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(22, \"div\");\n    i0.ɵɵtemplate(23, AppAccountEditComponent_form_0_p_tabPanel_107_p_panel_23_Template, 8, 24, \"p-panel\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r25.tranService.translate(\"account.text.grantApi\"));\n    i0.ɵɵproperty(\"pt\", \"ProfileTab\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"showHeader\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r25.tranService.translate(\"account.text.working\"))(\"ngModel\", ctx_r25.statusGrantApi)(\"ngModelOptions\", i0.ɵɵpureFunction0(20, _c4));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", ctx_r25.tranService.translate(\"account.text.notWorking\"))(\"ngModel\", ctx_r25.statusGrantApi)(\"ngModelOptions\", i0.ɵɵpureFunction0(21, _c4));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r25.genGrantApi.clientId)(\"disabled\", true)(\"ngModelOptions\", i0.ɵɵpureFunction0(22, _c4));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r25.genGrantApi.secretKey)(\"ngModelOptions\", i0.ɵɵpureFunction0(23, _c4))(\"type\", ctx_r25.isShowSecretKey ? \"text\" : \"password\")(\"disabled\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.isShowSecretKey == false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.isShowSecretKey == true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r25.tranService.translate(\"account.text.gen\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r25.genGrantApi.secretKey);\n  }\n}\nconst _c6 = function (a0) {\n  return [a0];\n};\nfunction AppAccountEditComponent_form_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r73 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 1);\n    i0.ɵɵlistener(\"keydown.enter\", function AppAccountEditComponent_form_0_Template_form_keydown_enter_0_listener($event) {\n      return $event.preventDefault();\n    })(\"ngSubmit\", function AppAccountEditComponent_form_0_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r72 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r72.onSubmitCreate());\n    });\n    i0.ɵɵelementStart(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"p-breadcrumb\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"p-button\", 8);\n    i0.ɵɵlistener(\"click\", function AppAccountEditComponent_form_0_Template_p_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.closeForm());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"p-button\", 9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"p-card\", 10)(11, \"div\")(12, \"p-tabView\", 11);\n    i0.ɵɵlistener(\"onChange\", function AppAccountEditComponent_form_0_Template_p_tabView_onChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r75 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r75.onTabChange($event));\n    })(\"activeIndexChange\", function AppAccountEditComponent_form_0_Template_p_tabView_activeIndexChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r76 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r76.activeTabIndex = $event);\n    });\n    i0.ɵɵelementStart(13, \"p-tabPanel\", 12)(14, \"div\", 13)(15, \"div\", 14)(16, \"div\", 15)(17, \"label\", 16);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementStart(19, \"span\", 17);\n    i0.ɵɵtext(20, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 18)(22, \"input\", 19);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountEditComponent_form_0_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r77 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r77.accountInfo.accountName = $event);\n    })(\"ngModelChange\", function AppAccountEditComponent_form_0_Template_input_ngModelChange_22_listener() {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r78 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r78.checkExistAccount(\"accountName\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 20);\n    i0.ɵɵelement(24, \"label\", 16);\n    i0.ɵɵelementStart(25, \"div\", 18);\n    i0.ɵɵtemplate(26, AppAccountEditComponent_form_0_small_26_Template, 2, 1, \"small\", 21);\n    i0.ɵɵtemplate(27, AppAccountEditComponent_form_0_small_27_Template, 2, 2, \"small\", 21);\n    i0.ɵɵtemplate(28, AppAccountEditComponent_form_0_small_28_Template, 2, 1, \"small\", 21);\n    i0.ɵɵtemplate(29, AppAccountEditComponent_form_0_small_29_Template, 2, 3, \"small\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 15)(31, \"label\", 22);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementStart(33, \"span\", 17);\n    i0.ɵɵtext(34, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 18)(36, \"input\", 23);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountEditComponent_form_0_Template_input_ngModelChange_36_listener($event) {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r79 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r79.accountInfo.fullName = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(37, \"div\", 20);\n    i0.ɵɵelement(38, \"label\", 22);\n    i0.ɵɵelementStart(39, \"div\", 18);\n    i0.ɵɵtemplate(40, AppAccountEditComponent_form_0_small_40_Template, 2, 1, \"small\", 21);\n    i0.ɵɵtemplate(41, AppAccountEditComponent_form_0_small_41_Template, 2, 2, \"small\", 21);\n    i0.ɵɵtemplate(42, AppAccountEditComponent_form_0_small_42_Template, 2, 1, \"small\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 15)(44, \"label\", 24);\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 18)(47, \"input\", 25);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountEditComponent_form_0_Template_input_ngModelChange_47_listener($event) {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r80 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r80.accountInfo.phone = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(48, \"div\", 20);\n    i0.ɵɵelement(49, \"label\", 24);\n    i0.ɵɵelementStart(50, \"div\", 18);\n    i0.ɵɵtemplate(51, AppAccountEditComponent_form_0_small_51_Template, 2, 1, \"small\", 21);\n    i0.ɵɵtemplate(52, AppAccountEditComponent_form_0_small_52_Template, 2, 3, \"small\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 15)(54, \"label\", 26);\n    i0.ɵɵtext(55);\n    i0.ɵɵelementStart(56, \"span\", 17);\n    i0.ɵɵtext(57, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(58, \"div\", 18)(59, \"input\", 27);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountEditComponent_form_0_Template_input_ngModelChange_59_listener($event) {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r81 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r81.accountInfo.email = $event);\n    })(\"ngModelChange\", function AppAccountEditComponent_form_0_Template_input_ngModelChange_59_listener() {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r82 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r82.checkExistAccount(\"email\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(60, \"div\", 20);\n    i0.ɵɵelement(61, \"label\", 28);\n    i0.ɵɵelementStart(62, \"div\", 18);\n    i0.ɵɵtemplate(63, AppAccountEditComponent_form_0_small_63_Template, 2, 1, \"small\", 21);\n    i0.ɵɵtemplate(64, AppAccountEditComponent_form_0_small_64_Template, 2, 2, \"small\", 21);\n    i0.ɵɵtemplate(65, AppAccountEditComponent_form_0_small_65_Template, 2, 1, \"small\", 21);\n    i0.ɵɵtemplate(66, AppAccountEditComponent_form_0_small_66_Template, 2, 3, \"small\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"div\", 15)(68, \"label\", 29);\n    i0.ɵɵtext(69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(70, \"div\", 18)(71, \"textarea\", 30);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountEditComponent_form_0_Template_textarea_ngModelChange_71_listener($event) {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r83 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r83.accountInfo.description = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(72, \"div\", 20);\n    i0.ɵɵelement(73, \"label\", 29);\n    i0.ɵɵelementStart(74, \"div\", 18);\n    i0.ɵɵtemplate(75, AppAccountEditComponent_form_0_small_75_Template, 2, 2, \"small\", 21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(76, \"div\", 14)(77, \"div\", 15)(78, \"label\", 31);\n    i0.ɵɵtext(79);\n    i0.ɵɵelementStart(80, \"span\", 17);\n    i0.ɵɵtext(81, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(82, \"div\", 18)(83, \"p-dropdown\", 32);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountEditComponent_form_0_Template_p_dropdown_ngModelChange_83_listener($event) {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r84 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r84.accountInfo.userType = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(84, \"div\", 20);\n    i0.ɵɵelement(85, \"label\", 33);\n    i0.ɵɵelementStart(86, \"div\", 18);\n    i0.ɵɵtemplate(87, AppAccountEditComponent_form_0_small_87_Template, 2, 1, \"small\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(88, AppAccountEditComponent_form_0_div_88_Template, 7, 8, \"div\", 34);\n    i0.ɵɵtemplate(89, AppAccountEditComponent_form_0_div_89_Template, 4, 1, \"div\", 35);\n    i0.ɵɵelementStart(90, \"div\", 15)(91, \"label\", 36);\n    i0.ɵɵtext(92);\n    i0.ɵɵtemplate(93, AppAccountEditComponent_form_0_span_93_Template, 2, 0, \"span\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(94, \"div\", 37)(95, \"vnpt-select\", 38);\n    i0.ɵɵlistener(\"valueChange\", function AppAccountEditComponent_form_0_Template_vnpt_select_valueChange_95_listener($event) {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r85 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r85.accountInfo.manager = $event);\n    })(\"onchange\", function AppAccountEditComponent_form_0_Template_vnpt_select_onchange_95_listener() {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r86 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r86.onChangeTeller());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(96, AppAccountEditComponent_form_0_div_96_Template, 4, 1, \"div\", 35);\n    i0.ɵɵtemplate(97, AppAccountEditComponent_form_0_div_97_Template, 5, 6, \"div\", 34);\n    i0.ɵɵtemplate(98, AppAccountEditComponent_form_0_div_98_Template, 4, 1, \"div\", 35);\n    i0.ɵɵtemplate(99, AppAccountEditComponent_form_0_div_99_Template, 5, 4, \"div\", 39);\n    i0.ɵɵelementStart(100, \"div\", 15)(101, \"label\", 36);\n    i0.ɵɵtext(102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(103, \"div\", 37)(104, \"vnpt-select\", 40);\n    i0.ɵɵlistener(\"valueChange\", function AppAccountEditComponent_form_0_Template_vnpt_select_valueChange_104_listener($event) {\n      i0.ɵɵrestoreView(_r73);\n      const ctx_r87 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r87.accountInfo.roleLst = $event);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵtemplate(105, AppAccountEditComponent_form_0_p_tabPanel_105_Template, 8, 20, \"p-tabPanel\", 41);\n    i0.ɵɵtemplate(106, AppAccountEditComponent_form_0_p_tabPanel_106_Template, 8, 20, \"p-tabPanel\", 41);\n    i0.ɵɵtemplate(107, AppAccountEditComponent_form_0_p_tabPanel_107_Template, 24, 24, \"p-tabPanel\", 42);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.formAccount);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"global.menu.listaccount\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"model\", ctx_r0.items)(\"home\", ctx_r0.home);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"global.button.cancel\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"global.button.save\"))(\"disabled\", ctx_r0.formAccount.invalid || ctx_r0.isEmailExisted || ctx_r0.isPhoneExisted || ctx_r0.isUsernameExisted);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"activeIndex\", ctx_r0.activeTabIndex);\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r0.tranService.translate(\"account.label.generalInfo\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.username\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.accountInfo.accountName)(\"required\", true)(\"maxLength\", 50)(\"placeholder\", ctx_r0.tranService.translate(\"account.text.inputUsername\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.accountName.dirty && (ctx_r0.formAccount.controls.accountName.errors == null ? null : ctx_r0.formAccount.controls.accountName.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.accountName.errors == null ? null : ctx_r0.formAccount.controls.accountName.errors.maxLength);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.accountName.errors == null ? null : ctx_r0.formAccount.controls.accountName.errors.pattern);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isUsernameExisted);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.fullname\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.accountInfo.fullName)(\"required\", true)(\"maxLength\", 255)(\"placeholder\", ctx_r0.tranService.translate(\"account.text.inputFullname\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.fullName.dirty && (ctx_r0.formAccount.controls.fullName.errors == null ? null : ctx_r0.formAccount.controls.fullName.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.fullName.errors == null ? null : ctx_r0.formAccount.controls.fullName.errors.maxLength);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.fullName.errors == null ? null : ctx_r0.formAccount.controls.fullName.errors.pattern);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.phone\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.accountInfo.phone)(\"placeholder\", ctx_r0.tranService.translate(\"account.text.inputPhone\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.phone.errors == null ? null : ctx_r0.formAccount.controls.phone.errors.pattern);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPhoneExisted);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.email\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.accountInfo.email)(\"required\", true)(\"maxLength\", 255)(\"placeholder\", ctx_r0.tranService.translate(\"account.text.inputEmail\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.email.dirty && (ctx_r0.formAccount.controls.email.errors == null ? null : ctx_r0.formAccount.controls.email.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.email.errors == null ? null : ctx_r0.formAccount.controls.email.errors.maxLength);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.email.errors == null ? null : ctx_r0.formAccount.controls.email.errors.pattern);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isEmailExisted);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.description\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"autoResize\", false)(\"ngModel\", ctx_r0.accountInfo.description)(\"maxlength\", 255)(\"placeholder\", ctx_r0.tranService.translate(\"sim.text.inputDescription\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.description.errors == null ? null : ctx_r0.formAccount.controls.description.errors.maxLength);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.userType\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx_r0.accountInfo.userType)(\"required\", true)(\"options\", ctx_r0.statusAccounts)(\"placeholder\", ctx_r0.tranService.translate(\"account.text.selectUserType\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.userType.dirty && (ctx_r0.formAccount.controls.userType.errors == null ? null : ctx_r0.formAccount.controls.userType.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.userType == ctx_r0.optionUserType.ADMIN && ctx_r0.accountInfo.userType != ctx_r0.optionUserType.ADMIN && ctx_r0.accountInfo.userType != ctx_r0.optionUserType.AGENCY);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.userType == ctx_r0.optionUserType.ADMIN && ctx_r0.accountInfo.userType != ctx_r0.optionUserType.ADMIN && ctx_r0.accountInfo.userType != ctx_r0.optionUserType.AGENCY);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.accountInfo.userType == ctx_r0.optionUserType.CUSTOMER && (ctx_r0.userType == ctx_r0.optionUserType.ADMIN || ctx_r0.userType == ctx_r0.optionUserType.PROVINCE) ? \"\" : \"hidden\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.managerName\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.accountInfo.isRootCustomer);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"control\", ctx_r0.controlComboSelectManager)(\"value\", ctx_r0.accountInfo.manager)(\"placeholder\", ctx_r0.tranService.translate(\"account.text.selectGDV\"))(\"isMultiChoice\", false)(\"paramDefault\", ctx_r0.paramSearchManager)(\"disabled\", !ctx_r0.accountInfo.isRootCustomer)(\"required\", ctx_r0.accountInfo.isRootCustomer ? true : false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.accountInfo.userType == ctx_r0.optionUserType.CUSTOMER);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.accountInfo.userType == ctx_r0.optionUserType.DISTRICT);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.accountInfo.userType == ctx_r0.optionUserType.DISTRICT);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.accountInfo.userType == ctx_r0.optionUserType.CUSTOMER && !(ctx_r0.accountResponse == null ? null : ctx_r0.accountResponse.isRootCustomer));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.role\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"control\", ctx_r0.controlComboSelectRole)(\"value\", ctx_r0.accountInfo.roleLst)(\"paramDefault\", ctx_r0.paramSearchRoleActive)(\"loadData\", ctx_r0.getListRole.bind(ctx_r0))(\"isFilterLocal\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.checkShowTabAddCustomerAndContract());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.checkShowTabAddCustomerAndContract() && ctx_r0.selectItemCustomer && ctx_r0.selectItemCustomer.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.checkAuthen(i0.ɵɵpureFunction1(80, _c6, ctx_r0.CONSTANTS.PERMISSIONS.THIRD_PARTY_API.GRANT_PERMISSION_3RD_API)) && ctx_r0.accountInfo.userType == ctx_r0.optionUserType.CUSTOMER);\n  }\n}\nexport class AppAccountEditComponent extends ComponentBase {\n  constructor(accountService, customerService, contractService, formBuilder, injector) {\n    super(injector);\n    this.accountService = accountService;\n    this.customerService = customerService;\n    this.contractService = contractService;\n    this.formBuilder = formBuilder;\n    this.isUsernameExisted = false;\n    this.isEmailExisted = false;\n    this.isPhoneExisted = false;\n    this.oldUserType = null;\n    this.paramSearchCustomerProvince = {\n      provinceCode: \"\"\n    };\n    this.paramSearchManager = {\n      type: 3,\n      provinceCode: \"\"\n    };\n    this.paramSearchCustomerAccount = {\n      managerId: null,\n      provinceCode: \"\"\n    };\n    this.controlComboSelect = new ComboLazyControl();\n    this.controlComboSelectRole = new ComboLazyControl();\n    this.controlComboSelectManager = new ComboLazyControl();\n    this.controlComboSelectCustomerAccount = new ComboLazyControl();\n    this.customSelectAllCustomer = false;\n    this.customSelectAllContract = false;\n    this.loadingCustomer = false;\n    this.loadingContract = false;\n    this.isShowSecretKey = true;\n    this.listModule = [];\n    //sẽ lưu lại list api sau khi đã chọn\n    this.selectItemGrantApi = [];\n    this.paramsSearchGrantApi = {\n      api: null,\n      module: null\n    };\n    this.genGrantApi = {\n      clientId: null,\n      secretKey: null\n    };\n    this.activeTabIndex = 0;\n    this.statusGrantApi = null;\n    this.userInfo = this.sessionService.userInfo;\n    this.accountCurrentDetail = {};\n    this.isChangeSecretKey = false;\n    this.CONSTANTS = CONSTANTS;\n  }\n  ngOnInit() {\n    if (!this.checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.UPDATE])) {\n      window.location.hash = \"/access\";\n    }\n    this.userType = this.sessionService.userInfo.type;\n    this.accountId = this.sessionService.userInfo.id;\n    this.optionUserType = CONSTANTS.USER_TYPE;\n    this.items = [{\n      label: this.tranService.translate(\"global.menu.accountmgmt\")\n    }, {\n      label: this.tranService.translate(\"global.menu.listaccount\"),\n      routerLink: \"/accounts\"\n    }, {\n      label: this.tranService.translate(\"global.button.edit\")\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    let fullTypeAccount = [{\n      name: this.tranService.translate(\"account.usertype.admin\"),\n      value: CONSTANTS.USER_TYPE.ADMIN,\n      accepts: [CONSTANTS.USER_TYPE.ADMIN]\n    },\n    // {name: this.tranService.translate(\"account.usertype.customer\"),value:CONSTANTS.USER_TYPE.CUSTOMER,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT, CONSTANTS.USER_TYPE.AGENCY, CONSTANTS.USER_TYPE.CUSTOMER]},\n    {\n      name: this.tranService.translate(\"account.usertype.customer\"),\n      value: CONSTANTS.USER_TYPE.CUSTOMER,\n      accepts: [CONSTANTS.USER_TYPE.ADMIN, CONSTANTS.USER_TYPE.PROVINCE, CONSTANTS.USER_TYPE.DISTRICT, CONSTANTS.USER_TYPE.CUSTOMER]\n    }, {\n      name: this.tranService.translate(\"account.usertype.province\"),\n      value: CONSTANTS.USER_TYPE.PROVINCE,\n      accepts: [CONSTANTS.USER_TYPE.ADMIN]\n    }, {\n      name: this.tranService.translate(\"account.usertype.district\"),\n      value: CONSTANTS.USER_TYPE.DISTRICT,\n      accepts: [CONSTANTS.USER_TYPE.ADMIN, CONSTANTS.USER_TYPE.PROVINCE]\n    }\n    // {name: this.tranService.translate(\"account.usertype.agency\"),value:CONSTANTS.USER_TYPE.AGENCY,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT]},\n    ];\n\n    this.statusAccounts = fullTypeAccount.filter(el => el.accepts.includes(this.userType));\n    this.accountInfo = {\n      accountName: null,\n      fullName: null,\n      email: null,\n      phone: null,\n      userType: this.statusAccounts[0].value,\n      province: null,\n      roles: null,\n      description: null,\n      manager: null,\n      customers: null,\n      customerAccounts: null,\n      isRootCustomer: null,\n      contracts: null\n    };\n    this.paramQuickSearchCustomer = {\n      keyword: null,\n      accountRootId: null,\n      provinceCode: this.accountInfo.province,\n      accountCustomerId: null,\n      managerId: null\n    };\n    this.columnInfoCustomer = [{\n      name: this.tranService.translate(\"customer.label.customerCode\"),\n      key: \"code\",\n      size: \"30%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"customer.label.customerName\"),\n      key: \"name\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }];\n    this.dataSetCustomer = {\n      content: [],\n      total: 0\n    };\n    this.paginationCustomer = {\n      page: 0,\n      size: 10,\n      sortBy: \"name,asc;id,asc\"\n    };\n    this.paginationGrantApi = {\n      page: 0,\n      size: 10,\n      sortBy: \"id,desc\"\n    };\n    this.optionTableCustomer = {\n      hasClearSelected: false,\n      hasShowChoose: true,\n      hasShowIndex: true,\n      hasShowToggleColumn: false\n    };\n    this.selectItemCustomer = [];\n    this.paramQuickSearchContract = {\n      keyword: null,\n      customerIds: [],\n      accountRootId: -1\n    };\n    this.columnInfoContract = [{\n      name: this.tranService.translate(\"customer.label.customerCode\"),\n      key: \"customerCode\",\n      size: \"30%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"customer.label.customerName\"),\n      key: \"customerName\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"contract.label.contractCode\"),\n      key: \"contractCode\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }];\n    this.columnInfoGrantApi = [{\n      name: \"API\",\n      key: \"name\",\n      size: \"30%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: \"Module\",\n      key: \"module\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }];\n    this.dataSetContract = {\n      content: [],\n      total: 0\n    };\n    this.dataSetGrantApi = {\n      content: [],\n      total: 0\n    };\n    this.paginationContract = {\n      page: 0,\n      size: 10,\n      sortBy: \"customerName,asc;id,asc\"\n    };\n    this.optionTableGrantApi = {\n      hasClearSelected: false,\n      hasShowChoose: true,\n      hasShowIndex: true,\n      hasShowToggleColumn: false\n    };\n    this.optionTableContract = {\n      hasClearSelected: false,\n      hasShowChoose: true,\n      hasShowIndex: true,\n      hasShowToggleColumn: false\n    };\n    this.selectItemContract = [];\n    this.deselectedContracts = new Set();\n    this.getListProvince();\n  }\n  ngAfterContentChecked() {\n    if (this.accountInfo.userType != this.oldUserType && this.formAccount) {\n      this.oldUserType = this.accountInfo.userType;\n      this.formAccount.get(\"province\").reset();\n      this.formAccount.get(\"customers\").reset();\n    }\n  }\n  checkExistAccount(type) {\n    let email = null;\n    let username = null;\n    if (type == \"accountName\") {\n      this.isUsernameExisted = false;\n      username = this.accountInfo.accountName;\n      if (username == this.accountResponse.username) return;\n    } else if (type == \"email\") {\n      this.isEmailExisted = false;\n      email = this.accountInfo.email;\n      if (email == this.accountResponse.email) return;\n    }\n    let me = this;\n    this.debounceService.set(type, this.accountService.checkAccount.bind(this.accountService), email, username, response => {\n      if (response >= 1) {\n        if (type == \"accountName\") {\n          me.isUsernameExisted = true;\n        } else {\n          me.isEmailExisted = true;\n        }\n      }\n    });\n  }\n  onSubmitCreate() {\n    let me = this;\n    if (this.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER && this.accountInfo.manager == null) {\n      this.controlComboSelectManager.dirty = true;\n      this.activeTabIndex = 0;\n      me.messageCommonService.warning(me.tranService.translate('account.message.managerRequired'));\n      return;\n    }\n    if (me.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER && me.selectItemCustomer.length == 0) {\n      me.messageCommonService.warning(me.tranService.translate('account.message.customerRequired'));\n      return;\n    }\n    let listOldCustomer = this.listCustomerOld;\n    let listOldContract = (this.accountInfo.contracts || []).map(contract => contract.id);\n    let customerAdds = (this.selectItemCustomer || []).map(customer => customer.id).filter(el => !listOldCustomer.includes(el)).map(el => {\n      return {\n        customerId: el,\n        type: 1\n      };\n    });\n    let customerDelete = listOldCustomer.filter(el => !(this.selectItemCustomer || []).map(customer => customer.id).includes(el)).map(el => {\n      return {\n        customerId: el,\n        type: -1\n      };\n    });\n    let contractAdds = (this.selectItemContract || []).map(contract => contract.id).filter(el => !listOldContract.includes(el)).map(el => {\n      return {\n        contractId: el,\n        type: 1\n      };\n    });\n    let contractDelete = listOldContract.filter(el => !(this.selectItemContract || []).map(contract => contract.id).includes(el)).map(el => {\n      return {\n        contractId: el,\n        type: -1\n      };\n    });\n    let customerUpdate = [...customerAdds, ...customerDelete];\n    let contractUpdate = [...contractAdds, ...contractDelete];\n    if (this.accountInfo.userType != CONSTANTS.USER_TYPE.CUSTOMER) {\n      this.statusGrantApi = null;\n      this.selectItemGrantApi = [];\n    }\n    let dataBody = {\n      username: this.accountInfo.accountName,\n      fullName: this.accountInfo.fullName,\n      description: this.accountInfo.description,\n      email: this.accountInfo.email,\n      phone: this.accountInfo.phone,\n      type: this.accountInfo.userType,\n      provinceCode: this.accountInfo.province,\n      // roleLst: (this.accountInfo.roles|| []).map(el => el.id),\n      roleLst: this.accountInfo.roleLst,\n      customerLst: customerUpdate,\n      // customerIdLst: (this.accountInfo.customers || [])\n      idManager: this.accountInfo.manager || null,\n      idUserManageList: this.accountInfo.customerAccounts || [],\n      contractLst: contractUpdate,\n      statusApi: this.statusGrantApi,\n      listApiId: (this.selectItemGrantApi || []).map(el => el.id),\n      secretId: this.genGrantApi.secretKey,\n      isChangeSecretKey: this.isChangeSecretKey\n    };\n    if (dataBody.phone != null) {\n      if (dataBody.phone.startsWith('0')) {\n        dataBody.phone = \"84\" + dataBody.phone.substring(1, dataBody.phone.length);\n      } else if (dataBody.phone.length == 9 || dataBody.phone.length == 10) {\n        dataBody.phone = \"84\" + dataBody.phone;\n      }\n    }\n    this.messageCommonService.onload();\n    this.accountService.updateAccount(this.accountResponse.id, dataBody, response => {\n      me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n      me.router.navigate(['/accounts/']);\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  closeForm() {\n    this.router.navigate(['/accounts']);\n  }\n  getListAppIdSelected() {\n    let me = this;\n    this.accountService.viewProfile(response => {\n      me.accountCurrentDetail = response;\n      if (this.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\n        if (this.genGrantApi.secretKey != null) {\n          me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);\n        }\n      }\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  getDetail(updateList = true) {\n    let me = this;\n    let accountid = this.route.snapshot.paramMap.get(\"id\");\n    me.messageCommonService.onload();\n    this.accountService.getById(parseInt(accountid), response => {\n      me.accountResponse = response;\n      me.listCustomerOld = (response.customers || []).map(customer => customer.customerId);\n      me.accountInfo.accountName = response.username;\n      me.accountInfo.fullName = response.fullName;\n      me.accountInfo.email = response.email;\n      me.accountInfo.description = response.description;\n      me.accountInfo.phone = response.phone;\n      me.accountInfo.province = response.provinceCode;\n      me.accountInfo.userType = response.type;\n      me.accountInfo.isRootCustomer = response.isRootCustomer;\n      me.accountInfo.customers = (response.customers || []).map(customer => ({\n        id: customer.customerId,\n        name: customer.customerName,\n        code: customer.customerCode\n      }));\n      // console.log(response.customers)\n      me.selectItemCustomer = (response.customers || []).map(customer => ({\n        id: customer.customerId,\n        name: customer.customerName,\n        code: customer.customerCode\n      }));\n      me.accountInfo.contracts = [...(response.contracts || [])];\n      me.selectItemContract = [...(response.contracts || [])];\n      me.paramQuickSearchCustomer.provinceCode = me.accountInfo.province;\n      // Nếu tài khoản khách hàng sửa khách hàng cấp dưới, truyền id tk kh cấp trên để load customer theo tk kh cấp trên\n      if (me.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\n        me.paramQuickSearchCustomer.accountRootId = me.accountId;\n      } else {\n        // nếu là tk cấp trên sửa\n        me.paramQuickSearchCustomer.managerId = me.accountResponse?.manager?.id ? me.accountResponse.manager.id : -1;\n        if (me.accountResponse.rootAccount) {\n          //nếu có tài khoản con có root thì load danh sách theo root\n          me.paramQuickSearchCustomer.accountRootId = me.accountResponse.parentId;\n          me.paramQuickSearchContract.accountRootId = me.accountResponse.parentId;\n        } else {\n          //nếu k có load thì theo gdv và tk đang xem\n          me.paramQuickSearchCustomer.accountCustomerId = me.accountResponse.id;\n        }\n      }\n      // console.log(me.selectItemContract)\n      me.paramSearchRoleActive = {\n        type: me.accountInfo.userType,\n        accountRootId: me.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER && !me.accountInfo.isRootCustomer ? me.accountResponse.rootAccount.id : -1\n      };\n      me.formAccount = me.formBuilder.group(me.accountInfo);\n      me.formAccount.controls.accountName.disable();\n      me.formAccount.controls.userType.disable();\n      if (me.accountInfo.userType == CONSTANTS.USER_TYPE.DISTRICT) {\n        me.formAccount.controls.province.disable();\n      }\n      me.paramSearchCustomerProvince = {\n        provinceCode: me.accountInfo.province\n      };\n      // me.paramSearchCustomerAccount = {managerId : response.id ,provinceCode: me.accountInfo.province}\n      // me.paramSearchManager = {type: 3, provinceCode: me.accountInfo.province}\n      if (response.type == CONSTANTS.USER_TYPE.CUSTOMER) {\n        me.formAccount.controls.province.disable();\n      }\n      me.accountInfo.roleLst = (response.roles || []).map(el => el.roleId);\n      me.statusGrantApi = response.statusApi;\n      me.selectItemGrantApi = (response.listApiId || []).map(el => ({\n        id: el\n      }));\n      me.genGrantApi.secretKey = response.secretId;\n      me.genGrantApi.clientId = response.username;\n      this.getListAppIdSelected();\n      if (updateList) {\n        me.getListCustomer(false);\n      }\n    }, null, () => {\n      // me.messageCommonService.offload();\n    });\n  }\n  getListRole(data, callback) {\n    let me = this;\n    console.log(data);\n    if (this.accountInfo.userType == this.paramSearchRoleActive.type) {\n      this.accountService.getListRole(data, response => {\n        if (callback) {\n          let dataResponse = {\n            content: response || [],\n            totalPages: 1,\n            totalElements: (response || []).length\n          };\n          callback(dataResponse);\n        }\n      });\n    } else {\n      this.paramSearchRoleActive = {\n        type: this.accountInfo.userType,\n        accountRootId: -1\n      };\n    }\n  }\n  getListCustomer(isClear = true, name = \"\") {\n    if (this.accountInfo.userType == this.optionUserType.CUSTOMER) {\n      if (isClear) {\n        this.accountInfo.customers = null;\n      } else {\n        if (this.accountResponse.customers != null && this.accountResponse.customers.length > 0) {\n          // this.accountInfo.customers = this.accountResponse.customers.map(el => el.customerId);\n        } else {\n          this.accountInfo.customers = null;\n        }\n        // chon GDV\n        if (this.accountResponse.manager != null) {\n          this.accountInfo.manager = this.accountResponse.manager.id;\n        } else {\n          this.accountInfo.manager = null;\n        }\n      }\n      let me = this;\n      this.paramSearchCustomerProvince = {\n        provinceCode: this.accountInfo.province\n      };\n      this.paramSearchManager = {\n        type: 3,\n        provinceCode: this.accountInfo.province\n      };\n      if (this.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\n        this.paramSearchCustomerProvince[\"parentId\"] = this.accountResponse[\"id\"];\n      }\n    }\n    // khi tao tai khoan GDV phai chon nhung tai khoan kh ma no' quan ly\n    if (this.accountInfo.userType == this.optionUserType.DISTRICT) {\n      if (isClear) {\n        this.accountInfo.manager = null;\n      } else {\n        // chon KH\n        if (this.accountResponse.userManages != null && this.accountResponse.userManages.length > 0) {\n          this.accountInfo.customerAccounts = this.accountResponse.userManages.filter(el => el.isRootCustomer == true).map(el => el.id);\n        } else {\n          this.accountInfo.customerAccounts = null;\n        }\n      }\n      this.paramSearchCustomerAccount = {\n        managerId: this.accountResponse.id,\n        provinceCode: this.accountInfo.province\n      };\n    }\n  }\n  getListProvince() {\n    let me = this;\n    this.accountService.getListProvince(response => {\n      me.listProvince = response.map(el => {\n        return {\n          id: el.code,\n          name: `${el.name} (${el.code})`\n        };\n      });\n      me.getDetail();\n    });\n  }\n  loadCustomerAccount(params, callback) {\n    return this.accountService.getCustomerAccount(params, callback);\n  }\n  onSearchCustomer(back) {\n    let me = this;\n    if (back) {\n      me.paginationCustomer.page = 0;\n    }\n    me.searchCustomer(me.paginationCustomer.page, me.paginationCustomer.size, me.paginationCustomer.sortBy, me.paramQuickSearchCustomer);\n  }\n  searchCustomer(page, limit, sort, params) {\n    let me = this;\n    this.paginationCustomer.page = page;\n    this.paginationCustomer.size = limit;\n    this.paginationCustomer.sortBy = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    Object.keys(this.paramQuickSearchCustomer).forEach(key => {\n      if (this.paramQuickSearchCustomer[key] != null) {\n        dataParams[key] = this.paramQuickSearchCustomer[key];\n      }\n    });\n    me.messageCommonService.onload();\n    // console.log(dataParams)\n    // console.log(\"quickSearchCustomer\")\n    this.customerService.quickSearchCustomer(dataParams, this.paramQuickSearchCustomer, response => {\n      me.dataSetCustomer = {\n        content: response.content,\n        total: response.totalElements\n      };\n      if (this.selectItemCustomer.length == response.totalElements && response.totalElements != 0) {\n        this.customSelectAllCustomer = true;\n      }\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n    // console.log(this.selectItemCustomer)\n  }\n\n  onSearchContract(back) {\n    let me = this;\n    if (back) {\n      me.paginationContract.page = 0;\n    }\n    me.paramQuickSearchContract.customerIds = (me.selectItemCustomer || []).map(customer => customer.id), me.searchContract(me.paginationContract.page, me.paginationContract.size, me.paginationContract.sortBy, me.paramQuickSearchContract);\n  }\n  searchContract(page, limit, sort, params) {\n    let me = this;\n    this.paginationContract.page = page;\n    this.paginationContract.size = limit;\n    this.paginationContract.sortBy = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    // Object.keys(this.paramQuickSearchContract).forEach(key => {\n    //     if(this.paramQuickSearchContract[key] != null){\n    //         dataParams[key] = this.paramQuickSearchContract[key];\n    //     }\n    // })\n    me.messageCommonService.onload();\n    this.contractService.quickSearchContract(dataParams, this.paramQuickSearchContract, response => {\n      me.dataSetContract = {\n        content: response.content,\n        total: response.totalElements\n      };\n      if (this.selectItemContract.length == response.totalElements && response.totalElements != 0) {\n        this.customSelectAllContract = true;\n      }\n      // console.log(response)\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  onTabChange(event) {\n    const tabName = event.originalEvent.target.innerText;\n    let me = this;\n    if (event && tabName.includes(this.tranService.translate('account.text.grantApi'))) {\n      //có giá trị như thế nào thì hiển thì nguyên như vậy\n      // if(!this.statusGrantApi && !this.genGrantApi.secretKey) {\n      //     this.statusGrantApi = 1;\n      // }\n    } else if (event && tabName.includes(this.tranService.translate('account.text.addContract'))) {\n      me.onSearchContract();\n    } else if (event && tabName.includes(this.tranService.translate('account.text.addCustomer'))) {\n      me.onSearchCustomer();\n    }\n  }\n  generateToken(n) {\n    var chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    var token = '';\n    for (var i = 0; i < n; i++) {\n      token += chars[Math.floor(Math.random() * chars.length)];\n    }\n    return token;\n  }\n  checkSelectItemChangeCustomer(event) {\n    // console.log(this.selectItemCustomer)\n    let me = this;\n    if (this.selectItemCustomer.length == this.dataSetCustomer.total) {\n      this.customSelectAllCustomer = true;\n    } else {\n      this.customSelectAllCustomer = false;\n    }\n    const currentCustomerIds = new Set((event || []).map(customer => customer.id));\n    const previousCustomerIds = new Set((this.accountInfo.customers || []).map(customer => customer.id));\n    // console.log(this.accountInfo.customers)\n    const addedCustomers = (event || []).filter(customer => !previousCustomerIds.has(customer.id));\n    const removedCustomers = (this.accountInfo.customers || []).filter(customer => !currentCustomerIds.has(customer.id));\n    this.fetchContractsByCustomerId((addedCustomers || []).map(customer => customer.id));\n    removedCustomers.forEach(customer => {\n      this.selectItemContract = (this.selectItemContract || []).filter(contract => contract.customerCode != customer.code) || [];\n    });\n    this.accountInfo.customers = event;\n  }\n  checkSelectItemChangeContract(event) {\n    if (this.selectItemContract.length == this.dataSetContract.total) {\n      this.customSelectAllContract = true;\n    } else {\n      this.customSelectAllContract = false;\n    }\n  }\n  fetchContractsByCustomerId(customerIds) {\n    let me = this;\n    this.messageCommonService.onload();\n    this.paginationContract.page = 0;\n    let dataParams = {\n      page: '0',\n      size: '10000',\n      sort: this.paginationContract.sortBy\n    };\n    this.contractService.quickSearchContract(dataParams, {\n      keyword: null,\n      provinceCode: this.accountInfo.province,\n      customerIds: customerIds\n    }, res => {\n      if (res.totalElements > 0) {\n        const newContracts = (res.content || []).filter(contract => !this.deselectedContracts.has(contract.contractCode) && !this.selectItemContract.some(existingContract => existingContract.contractCode === contract.contractCode));\n        this.selectItemContract.push(...newContracts);\n      }\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  checkShowTabAddCustomerAndContract() {\n    let me = this;\n    if (me.accountInfo.userType != CONSTANTS.USER_TYPE.CUSTOMER) return false;\n    // if (me.formAccount.invalid || me.isEmailExisted || me.isPhoneExisted || me.isUsernameExisted ) {\n    //     return false;\n    // }\n    // if (this.accountInfo.province == null || this.accountInfo.manager == null) {\n    //     return false\n    // }\n    return true;\n  }\n  onChangeSelectAllItemsCustomer() {\n    // console.log(this.selectItemCustomer);\n    let me = this;\n    let params = {\n      page: \"0\",\n      size: \"********\",\n      sort: \"name,asc;id,asc\"\n    };\n    Object.keys(this.paramQuickSearchCustomer).forEach(key => {\n      if (this.paramQuickSearchCustomer[key] != null) {\n        params[key] = this.paramQuickSearchCustomer[key];\n      }\n    });\n    this.loadingCustomer = true;\n    this.customerService.quickSearchCustomer(params, this.paramQuickSearchCustomer, response => {\n      if (this.selectItemCustomer.length == response.totalElements) {\n        this.selectItemCustomer = [];\n        this.customSelectAllCustomer = false;\n        return;\n      }\n      this.selectItemCustomer = response.content;\n      this.customSelectAllCustomer = true;\n    }, null, () => {\n      this.loadingCustomer = false;\n    });\n  }\n  onChangeSelectAllItemsContract() {\n    // console.log(this.selectItemCustomer);\n    let me = this;\n    let params = {\n      page: \"0\",\n      size: \"********\",\n      sort: \"customerName,asc;id,asc\"\n    };\n    this.loadingContract = true;\n    this.contractService.quickSearchContract(params, this.paramQuickSearchContract, response => {\n      if (this.selectItemContract.length == response.totalElements) {\n        this.selectItemContract = [];\n        this.customSelectAllContract = false;\n        return;\n      }\n      this.selectItemContract = response.content;\n      this.customSelectAllContract = true;\n    }, null, () => {\n      this.loadingContract = false;\n    });\n  }\n  onChangeTeller() {\n    let me = this;\n    if (me.accountInfo.manager) {\n      me.paramQuickSearchCustomer.managerId = me.accountInfo.manager;\n    } else {\n      me.paramSearchCustomerAccount.managerId = -1;\n    }\n    me.paginationCustomer.page = 0;\n    me.paginationContract.page = 0;\n  }\n  searchGrantApi(page, limit, sort, params) {\n    let me = this;\n    this.paginationGrantApi.page = page;\n    this.paginationGrantApi.size = limit;\n    this.paginationGrantApi.sortBy = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    Object.keys(this.paramsSearchGrantApi).forEach(key => {\n      if (this.paramsSearchGrantApi[key] != null) {\n        dataParams[key] = this.paramsSearchGrantApi[key];\n      }\n    });\n    me.messageCommonService.onload();\n    if (this.accountResponse.parentId) {\n      dataParams['userCustomerParent'] = this.accountResponse.parentId;\n      this.accountService.getListAPI2(dataParams, response => {\n        me.dataSetGrantApi = {\n          content: response.content,\n          total: response.totalElements\n        };\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n      let copyParam = {\n        ...dataParams\n      };\n      copyParam.size = ********9;\n      this.accountService.getListAPI2(copyParam, response => {\n        me.listModule = [...new Set(response.content.map(el => el.module))];\n        me.listModule = me.listModule.map(el => ({\n          name: el,\n          value: el\n        }));\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n    } else {\n      this.accountService.searchGrantApi(dataParams, response => {\n        me.dataSetGrantApi = {\n          content: response.content,\n          total: response.totalElements\n        };\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n      let copyParam = {\n        ...dataParams\n      };\n      copyParam.size = ********9;\n      this.accountService.searchGrantApi(copyParam, response => {\n        me.listModule = [...new Set(response.content.map(el => el.module))];\n        me.listModule = me.listModule.map(el => ({\n          name: el,\n          value: el\n        }));\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n    }\n  }\n  genToken() {\n    this.isChangeSecretKey = true;\n    let me = this;\n    if (this.genGrantApi.secretKey) {\n      this.genGrantApi.secretKey = this.generateToken(20);\n    } else {\n      this.genGrantApi.secretKey = this.generateToken(20);\n      me.onSearchGrantApi();\n    }\n  }\n  onSearchGrantApi(back) {\n    let me = this;\n    if (back) {\n      me.paginationGrantApi.page = 0;\n    }\n    me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);\n  }\n  static {\n    this.ɵfac = function AppAccountEditComponent_Factory(t) {\n      return new (t || AppAccountEditComponent)(i0.ɵɵdirectiveInject(i1.AccountService), i0.ɵɵdirectiveInject(i2.CustomerService), i0.ɵɵdirectiveInject(i3.ContractService), i0.ɵɵdirectiveInject(i4.FormBuilder), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppAccountEditComponent,\n      selectors: [[\"app-account-edit\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[\"style\", \"position: relative\", 3, \"formGroup\", \"keydown.enter\", \"ngSubmit\", 4, \"ngIf\"], [2, \"position\", \"relative\", 3, \"formGroup\", \"keydown.enter\", \"ngSubmit\"], [1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\", \"btn-section\"], [1, \"flex\", \"flex-row\", \"justify-content-right\", \"align-items-center\", \"mr-6\"], [\"styleClass\", \"p-button-secondary p-button-outlined mr-2\", 3, \"label\", \"click\"], [\"styleClass\", \"p-button-info\", \"type\", \"submit\", 3, \"label\", \"disabled\"], [\"styleClass\", \"mt-3 responsive-pcard\"], [3, \"activeIndex\", \"onChange\", \"activeIndexChange\"], [3, \"header\"], [1, \"flex\", \"flex-row\", \"justify-content-between\", \"account-create\"], [2, \"width\", \"49%\"], [1, \"w-full\", \"field\", \"grid\"], [\"htmlFor\", \"accountName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"text-red-500\"], [1, \"col\"], [\"pInputText\", \"\", \"id\", \"accountName\", \"formControlName\", \"accountName\", \"pattern\", \"^[a-zA-Z0-9\\\\-_]*$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [1, \"w-full\", \"field\", \"grid\", \"text-error-field\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [\"htmlFor\", \"fullName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"fullName\", \"formControlName\", \"fullName\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"phone\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"phone\", \"formControlName\", \"phone\", \"pattern\", \"^((\\\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\", 1, \"w-full\", 3, \"ngModel\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"email\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"pInputText\", \"\", \"id\", \"email\", \"formControlName\", \"email\", \"pattern\", \"^[a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+)+(\\\\.[a-z]{2,})$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"email\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"htmlFor\", \"description\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"description\", \"formControlName\", \"description\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"placeholder\", \"ngModelChange\"], [\"for\", \"userType\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"styleClass\", \"w-full\", \"id\", \"userType\", \"formControlName\", \"userType\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"required\", \"options\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"userType\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"class\", \"w-full field grid\", 4, \"ngIf\"], [\"class\", \"w-full field grid text-error-field\", 4, \"ngIf\"], [\"htmlFor\", \"roles\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"col\", 2, \"max-width\", \"calc(100% - 180px)\"], [\"objectKey\", \"account\", \"paramKey\", \"name\", \"keyReturn\", \"id\", \"displayPattern\", \"${fullName} - ${username}\", \"typeValue\", \"primitive\", 1, \"w-full\", 3, \"control\", \"value\", \"placeholder\", \"isMultiChoice\", \"paramDefault\", \"disabled\", \"required\", \"valueChange\", \"onchange\"], [\"class\", \"w-full field grid align-items-start\", 4, \"ngIf\"], [\"objectKey\", \"role\", \"paramKey\", \"name\", \"keyReturn\", \"id\", \"displayPattern\", \"${name}\", 1, \"w-full\", 3, \"control\", \"value\", \"paramDefault\", \"loadData\", \"isFilterLocal\", \"valueChange\"], [3, \"header\", 4, \"ngIf\"], [3, \"header\", \"pt\", 4, \"ngIf\"], [\"htmlFor\", \"province\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"styleClass\", \"w-full\", \"id\", \"province\", \"formControlName\", \"province\", \"optionLabel\", \"name\", \"filterBy\", \"name\", \"optionValue\", \"id\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"required\", \"options\", \"filter\", \"placeholder\", \"ngModelChange\"], [\"objectKey\", \"account\", \"paramKey\", \"username\", \"keyReturn\", \"id\", \"displayPattern\", \"${fullName} - ${username}\", \"typeValue\", \"primitive\", 1, \"w-full\", 3, \"control\", \"value\", \"placeholder\", \"paramDefault\", \"loadData\", \"valueChange\"], [1, \"w-full\", \"field\", \"grid\", \"align-items-start\"], [\"pInputText\", \"\", 1, \"w-full\", 3, \"value\", \"disabled\", \"readonly\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"gap-3\", \"mt-4\", \"resp-search-bar\"], [\"type\", \"text\", \"pInputText\", \"\", 2, \"min-width\", \"35vw\", 3, \"placeholder\", \"ngModel\", \"ngModelOptions\", \"keydown.enter\", \"ngModelChange\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"ml-3 p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"button\", 3, \"click\"], [1, \"flex\", \"relative\", \"justify-content-center\", \"align-items-center\", \"w-full\", \"h-full\"], [\"class\", \"absolute flex justify-content-center align-items-center w-full h-full\", 3, \"ngStyle\", 4, \"ngIf\"], [1, \"w-full\", \"h-full\"], [\"isUseCustomSelectAll\", \"true\", 3, \"fieldId\", \"pageNumber\", \"pageSize\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"rowsPerPageOptions\", \"scrollHeight\", \"sort\", \"params\", \"customSelectAll\", \"selectItemsChange\", \"onChangeCustomSelectAllEmmiter\", \"customSelectAllChange\"], [1, \"absolute\", \"flex\", \"justify-content-center\", \"align-items-center\", \"w-full\", \"h-full\", 3, \"ngStyle\"], [3, \"header\", \"pt\"], [1, \"mb-3\"], [3, \"showHeader\"], [1, \"flex\", \"gap-2\"], [\"value\", \"1\", \"inputId\", \"1\", 1, \"p-3\", 3, \"label\", \"ngModel\", \"ngModelOptions\", \"ngModelChange\", \"onClick\"], [\"value\", \"0\", 1, \"p-3\", 3, \"label\", \"ngModel\", \"ngModelOptions\", \"ngModelChange\", \"onClick\"], [1, \"flex\", \"gap-3\", \"align-items-center\", \"api-input-section-edit\"], [1, \"col-5\"], [1, \"flex\", \"align-items-center\"], [1, \"mr-3\", 2, \"min-width\", \"100px\"], [\"type\", \"text\", \"pInputText\", \"\", 1, \"w-full\", 3, \"ngModel\", \"disabled\", \"ngModelOptions\", \"ngModelChange\"], [1, \"w-full\", \"flex\", \"align-items-center\"], [\"pInputText\", \"\", 1, \"w-full\", \"mr-2\", 2, \"padding-right\", \"30px\", 3, \"ngModel\", \"ngModelOptions\", \"type\", \"disabled\", \"ngModelChange\"], [\"style\", \"margin-left: -30px;z-index: 1;\", \"class\", \"pi pi-eye toggle-password\", 3, \"click\", 4, \"ngIf\"], [\"style\", \"margin-left: -30px;z-index: 1;\", \"class\", \"pi pi-eye-slash toggle-password\", 3, \"click\", 4, \"ngIf\"], [1, \"col-2\", \"btn-gen\"], [\"styleClass\", \"p-button-primary mr-2\", 3, \"label\", \"click\"], [3, \"showHeader\", 4, \"ngIf\"], [1, \"pi\", \"pi-eye\", \"toggle-password\", 2, \"margin-left\", \"-30px\", \"z-index\", \"1\", 3, \"click\"], [1, \"pi\", \"pi-eye-slash\", \"toggle-password\", 2, \"margin-left\", \"-30px\", \"z-index\", \"1\", 3, \"click\"], [1, \"flex\", \"gap-3\", \"align-items-center\", \"module-search\"], [1, \"col-3\", \"dropdown-fit\"], [\"optionLabel\", \"name\", \"optionValue\", \"value\", \"filter\", \"true\", 1, \"w-full\", 3, \"showClear\", \"ngModel\", \"ngModelOptions\", \"options\", \"emptyFilterMessage\", \"placeholder\", \"ngModelChange\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"API\", 1, \"w-full\", \"mr-2\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [3, \"fieldId\", \"pageNumber\", \"pageSize\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"rowsPerPageOptions\", \"scrollHeight\", \"sort\", \"params\", \"selectItemsChange\"]],\n      template: function AppAccountEditComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AppAccountEditComponent_form_0_Template, 108, 82, \"form\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccount);\n        }\n      },\n      dependencies: [i5.NgIf, i5.NgStyle, i6.Breadcrumb, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.MaxLengthValidator, i4.PatternValidator, i4.NgModel, i4.FormGroupDirective, i4.FormControlName, i7.InputText, i8.Button, i9.TableVnptComponent, i10.VnptCombobox, i11.Dropdown, i12.Card, i13.InputTextarea, i14.Panel, i15.RadioButton, i16.TabView, i16.TabPanel, i17.ProgressSpinner],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["ComponentBase", "CONSTANTS", "ComboLazyControl", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "tranService", "translate", "ctx_r2", "ɵɵpureFunction0", "_c0", "ctx_r3", "ctx_r4", "ɵɵpureFunction1", "_c1", "toLowerCase", "ctx_r5", "ctx_r6", "_c2", "ctx_r7", "ctx_r8", "ctx_r9", "ctx_r10", "ctx_r11", "ctx_r12", "ctx_r13", "ctx_r14", "ctx_r15", "ɵɵlistener", "AppAccountEditComponent_form_0_div_88_Template_p_dropdown_ngModelChange_6_listener", "$event", "ɵɵrestoreView", "_r27", "ctx_r26", "ɵɵnextContext", "ɵɵresetView", "accountInfo", "province", "ctx_r16", "ɵɵproperty", "userType", "optionUserType", "ADMIN", "AGENCY", "listProvince", "ctx_r28", "ɵɵelement", "ɵɵtemplate", "AppAccountEditComponent_form_0_div_89_small_3_Template", "ctx_r17", "formAccount", "controls", "dirty", "errors", "required", "ctx_r29", "AppAccountEditComponent_form_0_div_96_small_3_Template", "ctx_r19", "controlComboSelectManager", "error", "AppAccountEditComponent_form_0_div_97_Template_vnpt_select_valueChange_4_listener", "_r31", "ctx_r30", "customerAccounts", "ctx_r20", "controlComboSelectCustomerAccount", "paramSearchCustomerAccount", "loadCustomerAccount", "bind", "ctx_r32", "AppAccountEditComponent_form_0_div_98_small_3_Template", "ctx_r21", "ctx_r22", "ɵɵpropertyInterpolate", "accountResponse", "rootAccount", "username", "_c3", "AppAccountEditComponent_form_0_p_tabPanel_105_Template_input_keydown_enter_2_listener", "_r35", "ctx_r34", "preventDefault", "onSearchCustomer", "AppAccountEditComponent_form_0_p_tabPanel_105_Template_input_ngModelChange_2_listener", "ctx_r36", "paramQuickSearchCustomer", "keyword", "AppAccountEditComponent_form_0_p_tabPanel_105_Template_p_button_click_3_listener", "ctx_r37", "AppAccountEditComponent_form_0_p_tabPanel_105_div_5_Template", "AppAccountEditComponent_form_0_p_tabPanel_105_Template_table_vnpt_selectItemsChange_7_listener", "ctx_r38", "selectItemCustomer", "ctx_r39", "checkSelectItemChangeCustomer", "AppAccountEditComponent_form_0_p_tabPanel_105_Template_table_vnpt_onChangeCustomSelectAllEmmiter_7_listener", "ctx_r40", "onChangeSelectAllItemsCustomer", "AppAccountEditComponent_form_0_p_tabPanel_105_Template_table_vnpt_customSelectAllChange_7_listener", "ctx_r41", "customSelectAllCustomer", "ɵɵpropertyInterpolate1", "ctx_r23", "_c4", "loadingCustomer", "paginationCustomer", "page", "size", "columnInfoCustomer", "dataSetCustomer", "optionTableCustomer", "searchCustomer", "_c5", "sortBy", "AppAccountEditComponent_form_0_p_tabPanel_106_Template_input_keydown_enter_2_listener", "_r44", "ctx_r43", "onSearchContract", "AppAccountEditComponent_form_0_p_tabPanel_106_Template_input_ngModelChange_2_listener", "ctx_r45", "paramQuickSearchContract", "AppAccountEditComponent_form_0_p_tabPanel_106_Template_p_button_click_3_listener", "ctx_r46", "AppAccountEditComponent_form_0_p_tabPanel_106_div_5_Template", "AppAccountEditComponent_form_0_p_tabPanel_106_Template_table_vnpt_selectItemsChange_7_listener", "ctx_r47", "selectItemContract", "ctx_r48", "checkSelectItemChangeContract", "AppAccountEditComponent_form_0_p_tabPanel_106_Template_table_vnpt_onChangeCustomSelectAllEmmiter_7_listener", "ctx_r49", "onChangeSelectAllItemsContract", "AppAccountEditComponent_form_0_p_tabPanel_106_Template_table_vnpt_customSelectAllChange_7_listener", "ctx_r50", "customSelectAllContract", "ctx_r24", "loadingContract", "paginationContract", "columnInfoContract", "dataSetContract", "optionTableContract", "searchContract", "AppAccountEditComponent_form_0_p_tabPanel_107_label_18_Template_label_click_0_listener", "_r55", "ctx_r54", "isShowSecretKey", "AppAccountEditComponent_form_0_p_tabPanel_107_label_19_Template_label_click_0_listener", "_r57", "ctx_r56", "AppAccountEditComponent_form_0_p_tabPanel_107_p_panel_23_Template_p_dropdown_ngModelChange_3_listener", "_r59", "ctx_r58", "paramsSearchGrantApi", "module", "AppAccountEditComponent_form_0_p_tabPanel_107_p_panel_23_Template_input_ngModelChange_5_listener", "ctx_r60", "api", "AppAccountEditComponent_form_0_p_tabPanel_107_p_panel_23_Template_p_button_click_6_listener", "ctx_r61", "onSearchGrantApi", "AppAccountEditComponent_form_0_p_tabPanel_107_p_panel_23_Template_table_vnpt_selectItemsChange_7_listener", "ctx_r62", "selectItemGrantApi", "ctx_r53", "listModule", "paginationGrantApi", "columnInfoGrantApi", "dataSetGrantApi", "optionTableGrantApi", "searchGrantApi", "AppAccountEditComponent_form_0_p_tabPanel_107_Template_p_radioButton_ngModelChange_4_listener", "_r64", "ctx_r63", "statusGrantApi", "AppAccountEditComponent_form_0_p_tabPanel_107_Template_p_radioButton_onClick_4_listener", "ctx_r65", "changeGrantApiPermission", "AppAccountEditComponent_form_0_p_tabPanel_107_Template_p_radioButton_ngModelChange_5_listener", "ctx_r66", "AppAccountEditComponent_form_0_p_tabPanel_107_Template_p_radioButton_onClick_5_listener", "ctx_r67", "AppAccountEditComponent_form_0_p_tabPanel_107_Template_input_ngModelChange_11_listener", "ctx_r68", "genGrant<PERSON>pi", "clientId", "AppAccountEditComponent_form_0_p_tabPanel_107_Template_input_ngModelChange_17_listener", "ctx_r69", "secret<PERSON>ey", "AppAccountEditComponent_form_0_p_tabPanel_107_label_18_Template", "AppAccountEditComponent_form_0_p_tabPanel_107_label_19_Template", "AppAccountEditComponent_form_0_p_tabPanel_107_Template_p_button_click_21_listener", "ctx_r70", "genToken", "AppAccountEditComponent_form_0_p_tabPanel_107_p_panel_23_Template", "ctx_r25", "AppAccountEditComponent_form_0_Template_form_keydown_enter_0_listener", "AppAccountEditComponent_form_0_Template_form_ngSubmit_0_listener", "_r73", "ctx_r72", "onSubmitCreate", "AppAccountEditComponent_form_0_Template_p_button_click_8_listener", "ctx_r74", "closeForm", "AppAccountEditComponent_form_0_Template_p_tabView_onChange_12_listener", "ctx_r75", "onTabChange", "AppAccountEditComponent_form_0_Template_p_tabView_activeIndexChange_12_listener", "ctx_r76", "activeTabIndex", "AppAccountEditComponent_form_0_Template_input_ngModelChange_22_listener", "ctx_r77", "accountName", "ctx_r78", "checkExistAccount", "AppAccountEditComponent_form_0_small_26_Template", "AppAccountEditComponent_form_0_small_27_Template", "AppAccountEditComponent_form_0_small_28_Template", "AppAccountEditComponent_form_0_small_29_Template", "AppAccountEditComponent_form_0_Template_input_ngModelChange_36_listener", "ctx_r79", "fullName", "AppAccountEditComponent_form_0_small_40_Template", "AppAccountEditComponent_form_0_small_41_Template", "AppAccountEditComponent_form_0_small_42_Template", "AppAccountEditComponent_form_0_Template_input_ngModelChange_47_listener", "ctx_r80", "phone", "AppAccountEditComponent_form_0_small_51_Template", "AppAccountEditComponent_form_0_small_52_Template", "AppAccountEditComponent_form_0_Template_input_ngModelChange_59_listener", "ctx_r81", "email", "ctx_r82", "AppAccountEditComponent_form_0_small_63_Template", "AppAccountEditComponent_form_0_small_64_Template", "AppAccountEditComponent_form_0_small_65_Template", "AppAccountEditComponent_form_0_small_66_Template", "AppAccountEditComponent_form_0_Template_textarea_ngModelChange_71_listener", "ctx_r83", "description", "AppAccountEditComponent_form_0_small_75_Template", "AppAccountEditComponent_form_0_Template_p_dropdown_ngModelChange_83_listener", "ctx_r84", "AppAccountEditComponent_form_0_small_87_Template", "AppAccountEditComponent_form_0_div_88_Template", "AppAccountEditComponent_form_0_div_89_Template", "AppAccountEditComponent_form_0_span_93_Template", "AppAccountEditComponent_form_0_Template_vnpt_select_valueChange_95_listener", "ctx_r85", "manager", "AppAccountEditComponent_form_0_Template_vnpt_select_onchange_95_listener", "ctx_r86", "onChangeTeller", "AppAccountEditComponent_form_0_div_96_Template", "AppAccountEditComponent_form_0_div_97_Template", "AppAccountEditComponent_form_0_div_98_Template", "AppAccountEditComponent_form_0_div_99_Template", "AppAccountEditComponent_form_0_Template_vnpt_select_valueChange_104_listener", "ctx_r87", "roleLst", "AppAccountEditComponent_form_0_p_tabPanel_105_Template", "AppAccountEditComponent_form_0_p_tabPanel_106_Template", "AppAccountEditComponent_form_0_p_tabPanel_107_Template", "ctx_r0", "items", "home", "invalid", "isEmailExisted", "isPhoneExisted", "isUsernameExisted", "max<PERSON><PERSON><PERSON>", "pattern", "statusAccounts", "ɵɵclassMap", "CUSTOMER", "PROVINCE", "isRootCustomer", "paramSearchManager", "DISTRICT", "controlComboSelectRole", "paramSearchRoleActive", "getListRole", "checkShowTabAddCustomerAndContract", "length", "<PERSON><PERSON><PERSON><PERSON>", "_c6", "PERMISSIONS", "THIRD_PARTY_API", "GRANT_PERMISSION_3RD_API", "AppAccountEditComponent", "constructor", "accountService", "customerService", "contractService", "formBuilder", "injector", "oldUserType", "paramSearchCustomerProvince", "provinceCode", "type", "managerId", "controlComboSelect", "userInfo", "sessionService", "accountCurrentDetail", "isChangeSecretKey", "ngOnInit", "ACCOUNT", "UPDATE", "window", "location", "hash", "accountId", "id", "USER_TYPE", "label", "routerLink", "icon", "fullTypeAccount", "name", "value", "accepts", "filter", "el", "includes", "roles", "customers", "contracts", "accountRootId", "accountCustomerId", "key", "align", "isShow", "isSort", "content", "total", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "customerIds", "deselectedContracts", "Set", "getListProvince", "ngAfterContentChecked", "get", "reset", "me", "debounceService", "set", "checkAccount", "response", "messageCommonService", "warning", "listOldCustomer", "listCustomerOld", "listOldContract", "map", "contract", "customerAdds", "customer", "customerId", "customerDelete", "contractAdds", "contractId", "contractDelete", "customerUpdate", "contractUpdate", "dataBody", "customerLst", "idManager", "idUserManageList", "contractLst", "statusApi", "listApiId", "secretId", "startsWith", "substring", "onload", "updateAccount", "success", "router", "navigate", "offload", "getListAppIdSelected", "viewProfile", "getDetail", "updateList", "accountid", "route", "snapshot", "paramMap", "getById", "parseInt", "customerName", "code", "customerCode", "parentId", "group", "disable", "roleId", "getListCustomer", "data", "callback", "console", "log", "dataResponse", "totalPages", "totalElements", "isClear", "userManages", "params", "getCustomerAccount", "back", "limit", "sort", "dataParams", "Object", "keys", "for<PERSON>ach", "quickSearchCustomer", "quickSearchContract", "event", "tabName", "originalEvent", "target", "innerText", "generateToken", "n", "chars", "token", "i", "Math", "floor", "random", "currentCustomerIds", "previousCustomerIds", "addedCustomers", "has", "removedCustomers", "fetchContractsByCustomerId", "res", "newContracts", "contractCode", "some", "existingContract", "push", "getListAPI2", "copyParam", "ɵɵdirectiveInject", "i1", "AccountService", "i2", "CustomerService", "i3", "ContractService", "i4", "FormBuilder", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "AppAccountEditComponent_Template", "rf", "ctx", "AppAccountEditComponent_form_0_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\account-management\\edit\\app.account.edit.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\account-management\\edit\\app.account.edit.component.html"], "sourcesContent": ["import { AfterContentChecked, Component, Injector, OnInit } from \"@angular/core\";\r\nimport { FormBuilder } from \"@angular/forms\";\r\nimport { MenuItem } from \"primeng/api\";\r\nimport { ComponentBase } from \"src/app/component.base\";\r\nimport { AccountService } from \"src/app/service/account/AccountService\";\r\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\r\nimport { CustomerService } from \"src/app/service/customer/CustomerService\";\r\nimport { ComboLazyControl } from \"../../common-module/combobox-lazyload/combobox.lazyload\";\r\nimport {ColumnInfo, OptionTable} from \"../../common-module/table/table.component\";\r\nimport {ContractService} from \"../../../service/contract/ContractService\";\r\n\r\n@Component({\r\n    selector: \"app-account-edit\",\r\n    templateUrl: './app.account.edit.component.html'\r\n})\r\nexport class AppAccountEditComponent extends ComponentBase implements OnInit, AfterContentChecked{\r\n    constructor(public accountService: AccountService,\r\n                private customerService: CustomerService,\r\n                private contractService: ContractService,\r\n                private formBuilder: FormBuilder,\r\n                injector: Injector) {\r\n        super(injector);\r\n    }\r\n    items: Array<MenuItem>;\r\n    home: MenuItem;\r\n    //Danh sách customer khi lấy detail, dùng so sách với kết quả cuối cùng\r\n    listCustomerOld: Array<any>;\r\n    accountInfo: {\r\n        accountName: string| null,\r\n        fullName: string|null,\r\n        email: string|null,\r\n        phone: string|null,\r\n        userType: number| null,\r\n        province: any,\r\n        roles: Array<any>,\r\n        description: string|null,\r\n        manager: any,\r\n        //Danh sách customer TRƯỚC khi chọn, bỏ chọn\r\n        customers: Array<any>,\r\n        roleLst?: Array<any>,\r\n        customerAccounts : any\r\n        isRootCustomer : boolean | null\r\n        //Danh sách contract khi lấy detail, dùng so sách với kết quả cuối cùng\r\n        contracts: Array<any>\r\n    };\r\n    formAccount: any;\r\n    statusAccounts: Array<any>;\r\n    listRole: Array<any>;\r\n    listProvince: Array<any>;\r\n    userType: number;\r\n    optionUserType: any;\r\n    isUsernameExisted: boolean = false;\r\n    isEmailExisted: boolean = false;\r\n    isPhoneExisted: boolean = false;\r\n    oldUserType: number | null = null;\r\n    accountResponse: any;\r\n    paramSearchCustomerProvince: {provinceCode: string} = {provinceCode: \"\"};\r\n    paramSearchManager :{type: number, provinceCode: string} = {type: 3, provinceCode: \"\"};\r\n    paramSearchCustomerAccount : {managerId: number, provinceCode: string} = {managerId: null, provinceCode: \"\"};\r\n    controlComboSelect: ComboLazyControl = new ComboLazyControl();\r\n    controlComboSelectRole: ComboLazyControl = new ComboLazyControl();\r\n    controlComboSelectManager : ComboLazyControl = new ComboLazyControl();\r\n    controlComboSelectCustomerAccount : ComboLazyControl = new ComboLazyControl();\r\n    paramSearchRoleActive: {type: number, accountRootId: number};\r\n    paramQuickSearchCustomer: {\r\n        keyword: string|null,\r\n        provinceCode: string|null,\r\n        accountRootId: number| null,\r\n        managerId: null | null,\r\n        accountCustomerId: number | null,\r\n    }\r\n    columnInfoCustomer: Array<ColumnInfo>;\r\n    optionTableCustomer: OptionTable;\r\n    //Lưu lại list customer SAU khi chọn, bỏ chọn\r\n    selectItemCustomer: Array<any>\r\n    //sẽ lưu lại list contract SAU chọn, bỏ chọn\r\n    selectItemContract: Array<any>\r\n    //lưu lại contract bỏ chọn\r\n    deselectedContracts: Set<string>\r\n    dataSetCustomer: {\r\n        content: Array<any>,\r\n        total: number,\r\n    }\r\n    paginationCustomer: {\r\n        page: number|null,\r\n        size: number|null,\r\n        sortBy: string|null,\r\n    }\r\n    customSelectAllCustomer = false;\r\n    customSelectAllContract = false;\r\n    loadingCustomer: boolean = false;\r\n    loadingContract: boolean = false;\r\n\r\n    paramQuickSearchContract: {\r\n        keyword: string|null,\r\n        customerIds: Array<{ id: number }>|null,\r\n        accountRootId: number | null\r\n    }\r\n    columnInfoContract: Array<ColumnInfo>;\r\n    optionTableContract: OptionTable;\r\n    dataSetContract: {\r\n        content: Array<any>,\r\n        total: number,\r\n    }\r\n    paginationContract: {\r\n        page: number|null,\r\n        size: number|null,\r\n        sortBy: string|null,\r\n    }\r\n    accountId: number;\r\n\r\n    isShowSecretKey = true\r\n    listModule = []\r\n    //sẽ lưu lại list api sau khi đã chọn\r\n    selectItemGrantApi: Array<any> = []\r\n    paginationGrantApi: {\r\n        page: number|null,\r\n        size: number|null,\r\n        sortBy: string|null,\r\n    }\r\n    columnInfoGrantApi: Array<ColumnInfo>;\r\n\r\n    dataSetGrantApi: {\r\n        content: Array<any>,\r\n        total: number,\r\n    }\r\n    optionTableGrantApi: OptionTable;\r\n\r\n    paramsSearchGrantApi = {api : null, module : null}\r\n\r\n    genGrantApi = {clientId: null, secretKey: null}\r\n\r\n    activeTabIndex = 0;\r\n\r\n    statusGrantApi : any = null;\r\n\r\n    userInfo = this.sessionService.userInfo;\r\n\r\n    accountCurrentDetail : any = {}\r\n\r\n    isChangeSecretKey : boolean = false;\r\n\r\n    ngOnInit(): void {\r\n        if (!this.checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.UPDATE])) {window.location.hash = \"/access\";}\r\n        this.userType = this.sessionService.userInfo.type;\r\n        this.accountId = this.sessionService.userInfo.id;\r\n        this.optionUserType = CONSTANTS.USER_TYPE;\r\n        this.items = [\r\n            { label: this.tranService.translate(\"global.menu.accountmgmt\") },\r\n            { label: this.tranService.translate(\"global.menu.listaccount\"), routerLink:\"/accounts\" },\r\n            { label: this.tranService.translate(\"global.button.edit\") }\r\n        ];\r\n        this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n\r\n        let fullTypeAccount = [\r\n            {name: this.tranService.translate(\"account.usertype.admin\"),value:CONSTANTS.USER_TYPE.ADMIN, accepts:[CONSTANTS.USER_TYPE.ADMIN]},\r\n            // {name: this.tranService.translate(\"account.usertype.customer\"),value:CONSTANTS.USER_TYPE.CUSTOMER,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT, CONSTANTS.USER_TYPE.AGENCY, CONSTANTS.USER_TYPE.CUSTOMER]},\r\n            {name: this.tranService.translate(\"account.usertype.customer\"),value:CONSTANTS.USER_TYPE.CUSTOMER,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT, CONSTANTS.USER_TYPE.CUSTOMER]},\r\n            {name: this.tranService.translate(\"account.usertype.province\"),value:CONSTANTS.USER_TYPE.PROVINCE,accepts:[CONSTANTS.USER_TYPE.ADMIN]},\r\n            {name: this.tranService.translate(\"account.usertype.district\"),value:CONSTANTS.USER_TYPE.DISTRICT,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE]},\r\n            // {name: this.tranService.translate(\"account.usertype.agency\"),value:CONSTANTS.USER_TYPE.AGENCY,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT]},\r\n        ]\r\n        this.statusAccounts = fullTypeAccount.filter(el => el.accepts.includes(this.userType));\r\n        this.accountInfo = {\r\n            accountName: null,\r\n            fullName: null,\r\n            email: null,\r\n            phone: null,\r\n            userType: this.statusAccounts[0].value,\r\n            province: null,\r\n            roles: null,\r\n            description: null,\r\n            manager: null,\r\n            customers: null,\r\n            customerAccounts : null,\r\n            isRootCustomer : null,\r\n            contracts: null,\r\n        }\r\n        this.paramQuickSearchCustomer = {\r\n            keyword: null,\r\n            accountRootId: null,\r\n            provinceCode: this.accountInfo.province,\r\n            accountCustomerId: null,\r\n            managerId: null,\r\n        }\r\n        this.columnInfoCustomer = [\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerCode\"),\r\n                key: \"code\",\r\n                size: \"30%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerName\"),\r\n                key: \"name\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n        ]\r\n        this.dataSetCustomer = {\r\n            content: [],\r\n            total: 0,\r\n        }\r\n        this.paginationCustomer = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"name,asc;id,asc\",\r\n        }\r\n        this.paginationGrantApi = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"id,desc\",\r\n        }\r\n        this.optionTableCustomer = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: true,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n        }\r\n        this.selectItemCustomer = []\r\n\r\n        this.paramQuickSearchContract = {\r\n            keyword: null,\r\n            customerIds: [],\r\n            accountRootId: -1,\r\n        }\r\n        this.columnInfoContract = [\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerCode\"),\r\n                key: \"customerCode\",\r\n                size: \"30%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerName\"),\r\n                key: \"customerName\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"contract.label.contractCode\"),\r\n                key: \"contractCode\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n        ]\r\n\r\n        this.columnInfoGrantApi = [\r\n            {\r\n                name: \"API\",\r\n                key: \"name\",\r\n                size: \"30%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n            },\r\n            {\r\n                name: \"Module\",\r\n                key: \"module\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n            }\r\n        ]\r\n        this.dataSetContract = {\r\n            content: [],\r\n            total: 0,\r\n        }\r\n        this.dataSetGrantApi = {\r\n            content: [],\r\n            total: 0,\r\n        }\r\n        this.paginationContract = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"customerName,asc;id,asc\",\r\n        }\r\n        this.optionTableGrantApi = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: true,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n        }\r\n        this.optionTableContract = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: true,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n        }\r\n        this.selectItemContract = []\r\n        this.deselectedContracts = new Set<string>();\r\n        this.getListProvince();\r\n    }\r\n\r\n    ngAfterContentChecked(): void {\r\n        if(this.accountInfo.userType != this.oldUserType && this.formAccount){\r\n            this.oldUserType = this.accountInfo.userType;\r\n            this.formAccount.get(\"province\").reset();\r\n            this.formAccount.get(\"customers\").reset();\r\n        }\r\n    }\r\n\r\n    checkExistAccount(type){\r\n        let email = null;\r\n        let username = null;\r\n        if(type == \"accountName\"){\r\n            this.isUsernameExisted = false;\r\n            username = this.accountInfo.accountName;\r\n            if(username == this.accountResponse.username) return;\r\n        }else if(type == \"email\"){\r\n            this.isEmailExisted = false;\r\n            email = this.accountInfo.email;\r\n            if(email == this.accountResponse.email) return;\r\n        }\r\n\r\n        let me = this;\r\n\r\n        this.debounceService.set(type, this.accountService.checkAccount.bind(this.accountService), email, username,(response)=>{\r\n            if(response >= 1){\r\n                if(type == \"accountName\"){\r\n                    me.isUsernameExisted = true;\r\n                }else{\r\n                    me.isEmailExisted = true;\r\n                }\r\n            }\r\n        })\r\n    }\r\n\r\n    onSubmitCreate(){\r\n        let me = this;\r\n        if(this.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER && this.accountInfo.manager == null) {\r\n            this.controlComboSelectManager.dirty = true\r\n            this.activeTabIndex = 0;\r\n            me.messageCommonService.warning(me.tranService.translate('account.message.managerRequired'))\r\n            return;\r\n        }\r\n        if(me.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER && me.selectItemCustomer.length == 0) {\r\n            me.messageCommonService.warning(me.tranService.translate('account.message.customerRequired'))\r\n            return;\r\n        }\r\n        let listOldCustomer = this.listCustomerOld\r\n        let listOldContract = (this.accountInfo.contracts|| []).map(contract => contract.id)\r\n        let customerAdds = ((this.selectItemCustomer|| []).map(customer => customer.id)).filter(el => !listOldCustomer.includes(el)).map(el => {\r\n            return {\r\n                customerId: el,\r\n                type: 1\r\n            }\r\n        });\r\n        let customerDelete = listOldCustomer.filter(el => !((this.selectItemCustomer|| []).map(customer => customer.id)).includes(el)).map(el => {\r\n            return {\r\n                customerId: el,\r\n                type: -1\r\n            }\r\n        });\r\n\r\n        let contractAdds = ((this.selectItemContract|| []).map(contract => contract.id)).filter(el => !listOldContract.includes(el)).map(el => {\r\n            return {\r\n                contractId: el,\r\n                type: 1\r\n            }\r\n        });\r\n        let contractDelete = listOldContract.filter(el => !((this.selectItemContract|| []).map(contract => contract.id)).includes(el)).map(el => {\r\n            return {\r\n                contractId: el,\r\n                type: -1\r\n            }\r\n        });\r\n\r\n        let customerUpdate = [...customerAdds, ...customerDelete];\r\n        let contractUpdate = [...contractAdds,...contractDelete];\r\n        if(this.accountInfo.userType != CONSTANTS.USER_TYPE.CUSTOMER) {\r\n            this.statusGrantApi = null\r\n            this.selectItemGrantApi = []\r\n        }\r\n        let dataBody = {\r\n            username: this.accountInfo.accountName,\r\n            fullName: this.accountInfo.fullName,\r\n            description: this.accountInfo.description,\r\n            email: this.accountInfo.email,\r\n            phone: this.accountInfo.phone,\r\n            type: this.accountInfo.userType,\r\n            provinceCode: this.accountInfo.province,\r\n            // roleLst: (this.accountInfo.roles|| []).map(el => el.id),\r\n            roleLst: this.accountInfo.roleLst,\r\n            customerLst: customerUpdate,\r\n            // customerIdLst: (this.accountInfo.customers || [])\r\n            idManager: this.accountInfo.manager || null,\r\n            idUserManageList: (this.accountInfo.customerAccounts || []),\r\n            contractLst: contractUpdate,\r\n            statusApi: this.statusGrantApi,\r\n            listApiId: (this.selectItemGrantApi || []).map(el=>el.id),\r\n            secretId : this.genGrantApi.secretKey,\r\n            isChangeSecretKey : this.isChangeSecretKey\r\n        }\r\n        if(dataBody.phone != null){\r\n            if(dataBody.phone.startsWith('0')){\r\n                dataBody.phone = \"84\"+dataBody.phone.substring(1, dataBody.phone.length);\r\n            }else if(dataBody.phone.length == 9 || dataBody.phone.length == 10){\r\n                dataBody.phone = \"84\"+dataBody.phone;\r\n            }\r\n        }\r\n        this.messageCommonService.onload();\r\n        this.accountService.updateAccount(this.accountResponse.id, dataBody, (response)=>{\r\n            me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n            me.router.navigate(['/accounts/']);\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    closeForm(){\r\n        this.router.navigate(['/accounts'])\r\n    }\r\n\r\n    getListAppIdSelected(){\r\n        let me = this;\r\n        this.accountService.viewProfile( (response)=>{\r\n            me.accountCurrentDetail = response;\r\n            if(this.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\r\n                if(this.genGrantApi.secretKey != null) {\r\n                    me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);\r\n                }\r\n            }\r\n        },null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    getDetail(updateList: boolean = true){\r\n        let me = this;\r\n        let accountid = this.route.snapshot.paramMap.get(\"id\");\r\n        me.messageCommonService.onload()\r\n        this.accountService.getById(parseInt(accountid), (response)=>{\r\n            me.accountResponse = response;\r\n            me.listCustomerOld = (response.customers|| []).map(customer => customer.customerId)\r\n            me.accountInfo.accountName = response.username;\r\n            me.accountInfo.fullName = response.fullName;\r\n            me.accountInfo.email = response.email;\r\n            me.accountInfo.description = response.description;\r\n            me.accountInfo.phone = response.phone;\r\n            me.accountInfo.province = response.provinceCode;\r\n            me.accountInfo.userType = response.type;\r\n            me.accountInfo.isRootCustomer = response.isRootCustomer;\r\n            me.accountInfo.customers = (response.customers|| []).map(customer => ({\r\n                id: customer.customerId,\r\n                name: customer.customerName,\r\n                code: customer.customerCode,\r\n            }))\r\n            // console.log(response.customers)\r\n            me.selectItemCustomer = (response.customers|| []).map(customer => ({\r\n                id: customer.customerId,\r\n                name: customer.customerName,\r\n                code: customer.customerCode,\r\n            }))\r\n            me.accountInfo.contracts = [...(response.contracts || [])];\r\n            me.selectItemContract = [...(response.contracts || [])];\r\n            me.paramQuickSearchCustomer.provinceCode = me.accountInfo.province;\r\n            // Nếu tài khoản khách hàng sửa khách hàng cấp dưới, truyền id tk kh cấp trên để load customer theo tk kh cấp trên\r\n            if (me.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\r\n                me.paramQuickSearchCustomer.accountRootId = me.accountId;\r\n            } else { // nếu là tk cấp trên sửa\r\n                me.paramQuickSearchCustomer.managerId = me.accountResponse?.manager?.id ? me.accountResponse.manager.id : -1;\r\n                if (me.accountResponse.rootAccount) { //nếu có tài khoản con có root thì load danh sách theo root\r\n                    me.paramQuickSearchCustomer.accountRootId = me.accountResponse.parentId\r\n                    me.paramQuickSearchContract.accountRootId = me.accountResponse.parentId\r\n                } else { //nếu k có load thì theo gdv và tk đang xem\r\n                    me.paramQuickSearchCustomer.accountCustomerId = me.accountResponse.id;\r\n                }\r\n\r\n            }\r\n            // console.log(me.selectItemContract)\r\n            me.paramSearchRoleActive = {\r\n                type: me.accountInfo.userType,\r\n                accountRootId: me.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER && !me.accountInfo.isRootCustomer ? me.accountResponse.rootAccount.id : -1\r\n            }\r\n            me.formAccount = me.formBuilder.group(me.accountInfo);\r\n            me.formAccount.controls.accountName.disable();\r\n            me.formAccount.controls.userType.disable();\r\n            if(me.accountInfo.userType ==  CONSTANTS.USER_TYPE.DISTRICT) {\r\n                me.formAccount.controls.province.disable();\r\n            }\r\n            me.paramSearchCustomerProvince = {provinceCode: me.accountInfo.province}\r\n            // me.paramSearchCustomerAccount = {managerId : response.id ,provinceCode: me.accountInfo.province}\r\n            // me.paramSearchManager = {type: 3, provinceCode: me.accountInfo.province}\r\n            if(response.type == CONSTANTS.USER_TYPE.CUSTOMER){\r\n                me.formAccount.controls.province.disable();\r\n            }\r\n            me.accountInfo.roleLst = (response.roles || []).map(el => el.roleId)\r\n            me.statusGrantApi = response.statusApi\r\n            me.selectItemGrantApi = (response.listApiId || []).map(el=> ({id: el}))\r\n            me.genGrantApi.secretKey = response.secretId\r\n            me.genGrantApi.clientId = response.username\r\n            this.getListAppIdSelected()\r\n            if(updateList){\r\n                me.getListCustomer(false);\r\n            }\r\n        }, null, ()=>{\r\n            // me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    getListRole(data?, callback?){\r\n        let me = this;\r\n        console.log(data)\r\n        if(this.accountInfo.userType == this.paramSearchRoleActive.type){\r\n            this.accountService.getListRole(data, (response)=> {\r\n                if(callback){\r\n                    let dataResponse = {\r\n                        content: (response || []),\r\n                        totalPages: 1,\r\n                        totalElements: (response || []).length\r\n                    }\r\n                    callback(dataResponse);\r\n                }\r\n            })\r\n        }else{\r\n            this.paramSearchRoleActive = {\r\n                type: this.accountInfo.userType,\r\n                accountRootId: -1,\r\n            }\r\n        }\r\n    }\r\n\r\n    getListCustomer(isClear:boolean = true, name:string=\"\"){\r\n        if(this.accountInfo.userType == this.optionUserType.CUSTOMER) {\r\n            if(isClear){\r\n                this.accountInfo.customers = null;\r\n            }else{\r\n                if(this.accountResponse.customers != null && this.accountResponse.customers.length > 0){\r\n                    // this.accountInfo.customers = this.accountResponse.customers.map(el => el.customerId);\r\n                }else{\r\n                    this.accountInfo.customers = null;\r\n                }\r\n                // chon GDV\r\n                if(this.accountResponse.manager != null){\r\n                    this.accountInfo.manager = this.accountResponse.manager.id;\r\n                }else{\r\n                    this.accountInfo.manager = null;\r\n                }\r\n            }\r\n            let me = this;\r\n            this.paramSearchCustomerProvince = {provinceCode: this.accountInfo.province}\r\n            this.paramSearchManager = {type: 3, provinceCode: this.accountInfo.province}\r\n            if(this.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER){\r\n                this.paramSearchCustomerProvince[\"parentId\"] = this.accountResponse[\"id\"]\r\n            }\r\n        }\r\n        // khi tao tai khoan GDV phai chon nhung tai khoan kh ma no' quan ly\r\n        if(this.accountInfo.userType == this.optionUserType.DISTRICT) {\r\n            if(isClear){\r\n                this.accountInfo.manager = null;\r\n            }else {\r\n                // chon KH\r\n                if(this.accountResponse.userManages != null && this.accountResponse.userManages.length > 0){\r\n                    this.accountInfo.customerAccounts = this.accountResponse.userManages.filter(el=> el.isRootCustomer == true).map(el => el.id);\r\n                }else{\r\n                    this.accountInfo.customerAccounts = null;\r\n                }\r\n            }\r\n            this.paramSearchCustomerAccount = {managerId : this.accountResponse.id ,provinceCode: this.accountInfo.province}\r\n        }\r\n    }\r\n\r\n    getListProvince(){\r\n        let me = this;\r\n        this.accountService.getListProvince((response)=>{\r\n            me.listProvince = response.map(el => {\r\n                return {\r\n                    id: el.code,\r\n                    name: `${el.name} (${el.code})`\r\n                }\r\n            });\r\n            me.getDetail();\r\n        })\r\n    }\r\n\r\n    loadCustomerAccount(params, callback) {\r\n        return this.accountService.getCustomerAccount(params, callback)\r\n    }\r\n    onSearchCustomer(back?) {\r\n        let me = this;\r\n        if(back) {\r\n            me.paginationCustomer.page = 0;\r\n        }\r\n            me.searchCustomer(me.paginationCustomer.page, me.paginationCustomer.size, me.paginationCustomer.sortBy, me.paramQuickSearchCustomer);\r\n    }\r\n    searchCustomer(page, limit, sort, params){\r\n        let me = this;\r\n        this.paginationCustomer.page = page;\r\n        this.paginationCustomer.size = limit;\r\n        this.paginationCustomer.sortBy = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        Object.keys(this.paramQuickSearchCustomer).forEach(key => {\r\n            if(this.paramQuickSearchCustomer[key] != null){\r\n                dataParams[key] = this.paramQuickSearchCustomer[key];\r\n            }\r\n        })\r\n        me.messageCommonService.onload();\r\n        // console.log(dataParams)\r\n        // console.log(\"quickSearchCustomer\")\r\n        this.customerService.quickSearchCustomer(dataParams, this.paramQuickSearchCustomer,(response)=>{\r\n            me.dataSetCustomer = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n            if(this.selectItemCustomer.length==response.totalElements && response.totalElements != 0){\r\n                this.customSelectAllCustomer = true\r\n            }\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n        // console.log(this.selectItemCustomer)\r\n    }\r\n\r\n    onSearchContract(back?) {\r\n        let me = this;\r\n        if(back) {\r\n            me.paginationContract.page = 0;\r\n        }\r\n            me.paramQuickSearchContract.customerIds = (me.selectItemCustomer|| []).map(customer => customer.id),\r\n            me.searchContract(me.paginationContract.page, me.paginationContract.size, me.paginationContract.sortBy, me.paramQuickSearchContract);\r\n    }\r\n    searchContract(page, limit, sort, params){\r\n        let me = this;\r\n        this.paginationContract.page = page;\r\n        this.paginationContract.size = limit;\r\n        this.paginationContract.sortBy = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        // Object.keys(this.paramQuickSearchContract).forEach(key => {\r\n        //     if(this.paramQuickSearchContract[key] != null){\r\n        //         dataParams[key] = this.paramQuickSearchContract[key];\r\n        //     }\r\n        // })\r\n        me.messageCommonService.onload();\r\n        this.contractService.quickSearchContract(dataParams, this.paramQuickSearchContract,(response)=>{\r\n            me.dataSetContract = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n            if(this.selectItemContract.length==response.totalElements && response.totalElements != 0){\r\n                this.customSelectAllContract = true\r\n            }\r\n            // console.log(response)\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    onTabChange(event) {\r\n        const tabName = event.originalEvent.target.innerText;\r\n        let me = this;\r\n        if (event && tabName.includes(this.tranService.translate('account.text.grantApi'))) {\r\n            //có giá trị như thế nào thì hiển thì nguyên như vậy\r\n            // if(!this.statusGrantApi && !this.genGrantApi.secretKey) {\r\n            //     this.statusGrantApi = 1;\r\n            // }\r\n        } else if (event && tabName.includes(this.tranService.translate('account.text.addContract'))) {\r\n            me.onSearchContract()\r\n        } else if (event && tabName.includes(this.tranService.translate('account.text.addCustomer'))) {\r\n            me.onSearchCustomer()\r\n        }\r\n    }\r\n\r\n    generateToken(n) {\r\n        var chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r\n        var token = '';\r\n        for(var i = 0; i < n; i++) {\r\n            token += chars[Math.floor(Math.random() * chars.length)];\r\n        }\r\n        return token;\r\n    }\r\n\r\n    checkSelectItemChangeCustomer(event: any[]) {\r\n        // console.log(this.selectItemCustomer)\r\n        let me = this;\r\n        if(this.selectItemCustomer.length==this.dataSetCustomer.total){\r\n            this.customSelectAllCustomer = true\r\n        }else{\r\n            this.customSelectAllCustomer = false\r\n        }\r\n        const currentCustomerIds = new Set((event|| []).map(customer => customer.id));\r\n        const previousCustomerIds = new Set((this.accountInfo.customers || []).map(customer => customer.id));\r\n        // console.log(this.accountInfo.customers)\r\n        const addedCustomers = (event|| []).filter(customer => !previousCustomerIds.has(customer.id));\r\n\r\n        const removedCustomers = (this.accountInfo.customers || []).filter(customer => !currentCustomerIds.has(customer.id));\r\n        this.fetchContractsByCustomerId((addedCustomers|| []).map(customer => customer.id))\r\n\r\n        removedCustomers.forEach(customer => {\r\n            this.selectItemContract = (this.selectItemContract|| []).filter(contract => contract.customerCode != customer.code) || [];\r\n        });\r\n\r\n        this.accountInfo.customers = event;\r\n\r\n    }\r\n\r\n    checkSelectItemChangeContract(event: any[]){\r\n        if(this.selectItemContract.length==this.dataSetContract.total){\r\n            this.customSelectAllContract = true\r\n        }else{\r\n            this.customSelectAllContract = false\r\n        }\r\n    }\r\n    fetchContractsByCustomerId(customerIds: number[]) {\r\n\r\n        let me = this;\r\n        this.messageCommonService.onload()\r\n        this.paginationContract.page = 0;\r\n        let dataParams = {\r\n            page: '0',\r\n            size: '10000',\r\n            sort: this.paginationContract.sortBy\r\n        }\r\n        this.contractService.quickSearchContract(dataParams,\r\n            {\r\n                keyword: null,\r\n                provinceCode: this.accountInfo.province,\r\n                customerIds: customerIds,\r\n            }, (res) => {\r\n                if (res.totalElements > 0) {\r\n                    const newContracts = (res.content || []).filter(contract => !this.deselectedContracts.has(contract.contractCode) &&\r\n                        !this.selectItemContract.some(existingContract => existingContract.contractCode === contract.contractCode));\r\n                    this.selectItemContract.push(...newContracts);\r\n                }\r\n            }, null, () => {\r\n                me.messageCommonService.offload();\r\n            })\r\n    }\r\n\r\n    checkShowTabAddCustomerAndContract() {\r\n        let me = this\r\n        if (me.accountInfo.userType != CONSTANTS.USER_TYPE.CUSTOMER)  return false;\r\n        // if (me.formAccount.invalid || me.isEmailExisted || me.isPhoneExisted || me.isUsernameExisted ) {\r\n        //     return false;\r\n        // }\r\n        // if (this.accountInfo.province == null || this.accountInfo.manager == null) {\r\n        //     return false\r\n        // }\r\n        return  true\r\n    }\r\n\r\n    onChangeSelectAllItemsCustomer(){\r\n        // console.log(this.selectItemCustomer);\r\n        let me = this;\r\n        let params = {\r\n            page: \"0\",\r\n            size: \"********\",\r\n            sort: \"name,asc;id,asc\",\r\n        }\r\n        Object.keys(this.paramQuickSearchCustomer).forEach(key => {\r\n            if(this.paramQuickSearchCustomer[key] != null){\r\n                params[key] = this.paramQuickSearchCustomer[key];\r\n            }\r\n        })\r\n        this.loadingCustomer = true\r\n        this.customerService.quickSearchCustomer(params,this.paramQuickSearchCustomer,(response)=>{\r\n            if(this.selectItemCustomer.length == response.totalElements){\r\n                this.selectItemCustomer = [];\r\n                this.customSelectAllCustomer = false\r\n                return;\r\n            }\r\n            this.selectItemCustomer = response.content\r\n            this.customSelectAllCustomer = true\r\n        },null,()=>{ this.loadingCustomer = false });\r\n    }\r\n\r\n    onChangeSelectAllItemsContract(){\r\n        // console.log(this.selectItemCustomer);\r\n        let me = this;\r\n        let params = {\r\n            page: \"0\",\r\n            size: \"********\",\r\n            sort: \"customerName,asc;id,asc\",\r\n        }\r\n        this.loadingContract = true\r\n        this.contractService.quickSearchContract(params, this.paramQuickSearchContract,(response)=>{\r\n            if(this.selectItemContract.length == response.totalElements){\r\n                this.selectItemContract = [];\r\n                this.customSelectAllContract = false\r\n                return;\r\n            }\r\n            this.selectItemContract = response.content\r\n            this.customSelectAllContract = true\r\n        }, null, ()=>{\r\n            this.loadingContract = false;\r\n        })\r\n    }\r\n\r\n    onChangeTeller() {\r\n        let me = this;\r\n        if (me.accountInfo.manager) {\r\n            me.paramQuickSearchCustomer.managerId = me.accountInfo.manager;\r\n        } else {\r\n            me.paramSearchCustomerAccount.managerId = -1;\r\n        }\r\n        me.paginationCustomer.page = 0;\r\n        me.paginationContract.page = 0;\r\n    }\r\n\r\n    searchGrantApi(page, limit, sort, params){\r\n        let me = this;\r\n        this.paginationGrantApi.page = page;\r\n        this.paginationGrantApi.size = limit;\r\n        this.paginationGrantApi.sortBy = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        Object.keys(this.paramsSearchGrantApi).forEach(key => {\r\n            if(this.paramsSearchGrantApi[key] != null){\r\n                dataParams[key] = this.paramsSearchGrantApi[key];\r\n            }\r\n        })\r\n        me.messageCommonService.onload();\r\n        if(this.accountResponse.parentId) {\r\n            dataParams['userCustomerParent'] = this.accountResponse.parentId\r\n            this.accountService.getListAPI2(dataParams,(response)=>{\r\n                me.dataSetGrantApi = {\r\n                    content: response.content,\r\n                    total: response.totalElements\r\n                }\r\n            }, null, ()=>{\r\n                me.messageCommonService.offload();\r\n            })\r\n            let copyParam = {...dataParams};\r\n            copyParam.size = ********9;\r\n            this.accountService.getListAPI2(copyParam,(response)=>{\r\n                me.listModule = [...new Set(response.content.map(el=>el.module))]\r\n                me.listModule = me.listModule.map(el=>({\r\n                    name : el,\r\n                    value : el\r\n                }))\r\n            }, null, ()=>{\r\n                me.messageCommonService.offload();\r\n            })\r\n        }else {\r\n            this.accountService.searchGrantApi(dataParams,(response)=>{\r\n                me.dataSetGrantApi = {\r\n                    content: response.content,\r\n                    total: response.totalElements\r\n                }\r\n            }, null, ()=>{\r\n                me.messageCommonService.offload();\r\n            })\r\n            let copyParam = {...dataParams};\r\n            copyParam.size = ********9;\r\n            this.accountService.searchGrantApi(copyParam,(response)=>{\r\n                me.listModule = [...new Set(response.content.map(el=>el.module))]\r\n                me.listModule = me.listModule.map(el=>({\r\n                    name : el,\r\n                    value : el\r\n                }))\r\n            }, null, ()=>{\r\n                me.messageCommonService.offload();\r\n            })\r\n        }\r\n    }\r\n\r\n    genToken(){\r\n        this.isChangeSecretKey = true;\r\n        let me = this;\r\n        if(this.genGrantApi.secretKey) {\r\n            this.genGrantApi.secretKey = this.generateToken(20);\r\n        }else {\r\n            this.genGrantApi.secretKey = this.generateToken(20);\r\n            me.onSearchGrantApi()\r\n        }\r\n    }\r\n\r\n    onSearchGrantApi(back?) {\r\n        let me = this;\r\n        if(back) {\r\n            me.paginationGrantApi.page = 0;\r\n        }\r\n        me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);\r\n    }\r\n\r\n    protected readonly CONSTANTS = CONSTANTS;\r\n}\r\n", "<form *ngIf=\"formAccount\" (keydown.enter)=\"$event.preventDefault()\" [formGroup]=\"formAccount\" (ngSubmit)=\"onSubmitCreate()\" style=\"position: relative\">\r\n<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{this.tranService.translate(\"global.menu.listaccount\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"col-5 flex flex-row justify-content-end align-items-center btn-section\">\r\n        <div class=\"flex flex-row justify-content-right align-items-center mr-6\">\r\n            <p-button [label]=\"tranService.translate('global.button.cancel')\" styleClass=\"p-button-secondary p-button-outlined mr-2\" (click)=\"closeForm()\"></p-button>\r\n            <p-button [label]=\"tranService.translate('global.button.save')\" styleClass=\"p-button-info\" type=\"submit\"\r\n                      [disabled]=\"formAccount.invalid || isEmailExisted || isPhoneExisted || isUsernameExisted\"></p-button>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n<p-card styleClass=\"mt-3 responsive-pcard\">\r\n    <div>\r\n            <p-tabView (onChange)=\"onTabChange($event)\" [(activeIndex)]=\"activeTabIndex\">\r\n                <p-tabPanel header=\"{{tranService.translate('account.label.generalInfo')}}\">\r\n                    <div class=\"flex flex-row justify-content-between account-create\">\r\n                        <div style=\"width: 49%;\">\r\n                            <!-- username -->\r\n                            <div class=\"w-full field grid\">\r\n                                <label htmlFor=\"accountName\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.username\")}}<span class=\"text-red-500\">*</span></label>\r\n                                <div class=\"col\">\r\n                                    <input class=\"w-full\"\r\n                                           pInputText id=\"accountName\"\r\n                                           [(ngModel)]=\"accountInfo.accountName\"\r\n                                           formControlName=\"accountName\"\r\n                                           [required]=\"true\"\r\n                                           [maxLength]=\"50\"\r\n                                           pattern=\"^[a-zA-Z0-9\\-_]*$\"\r\n                                           [placeholder]=\"tranService.translate('account.text.inputUsername')\"\r\n                                           (ngModelChange)=\"checkExistAccount('accountName')\"\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n                            <!-- error username -->\r\n                            <div class=\"w-full field grid text-error-field\">\r\n                                <label htmlFor=\"accountName\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                                <div class=\"col\">\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.accountName.dirty && formAccount.controls.accountName.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.accountName.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:50})}}</small>\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.accountName.errors?.pattern\">{{tranService.translate(\"global.message.formatCode\")}}</small>\r\n                                    <small class=\"text-red-500\" *ngIf=\"isUsernameExisted\">{{tranService.translate(\"global.message.exists\",{type: tranService.translate(\"account.label.username\").toLowerCase()})}}</small>\r\n                                </div>\r\n                            </div>\r\n                            <!-- fullname -->\r\n                            <div class=\"w-full field grid\">\r\n                                <label htmlFor=\"fullName\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.fullname\")}}<span class=\"text-red-500\">*</span></label>\r\n                                <div class=\"col\">\r\n                                    <input class=\"w-full\"\r\n                                           pInputText id=\"fullName\"\r\n                                           [(ngModel)]=\"accountInfo.fullName\"\r\n                                           formControlName=\"fullName\"\r\n                                           [required]=\"true\"\r\n                                           [maxLength]=\"255\"\r\n                                           pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$\"\r\n                                           [placeholder]=\"tranService.translate('account.text.inputFullname')\"\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n                            <!-- error fullname -->\r\n                            <div class=\"w-full field grid text-error-field\">\r\n                                <label htmlFor=\"fullName\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                                <div class=\"col\">\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.fullName.dirty && formAccount.controls.fullName.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.fullName.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.fullName.errors?.pattern\">{{tranService.translate(\"global.message.formatContainVN\")}}</small>\r\n                                </div>\r\n                            </div>\r\n                            <!-- phone -->\r\n                            <div class=\"w-full field grid\">\r\n                                <label htmlFor=\"phone\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.phone\")}}</label>\r\n                                <div class=\"col\">\r\n                                    <input class=\"w-full\"\r\n                                           pInputText id=\"phone\"\r\n                                           [(ngModel)]=\"accountInfo.phone\"\r\n                                           formControlName=\"phone\"\r\n                                           pattern=\"^((\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\"\r\n                                           [placeholder]=\"tranService.translate('account.text.inputPhone')\"\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n                            <!-- error phone -->\r\n                            <div class=\"w-full field grid text-error-field\">\r\n                                <label htmlFor=\"phone\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                                <div class=\"col\">\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.phone.errors?.pattern\">{{tranService.translate(\"global.message.invalidPhone\")}}</small>\r\n                                    <small class=\"text-red-500\" *ngIf=\"isPhoneExisted\">{{tranService.translate(\"global.message.exists\",{type: tranService.translate(\"account.label.phone\").toLowerCase()})}}</small>\r\n                                </div>\r\n                            </div>\r\n                            <!-- email -->\r\n                            <div class=\"w-full field grid\">\r\n                                <label htmlFor=\"email\" class=\"col-fixed\" style=\"width:180px;height: fit-content;\">{{tranService.translate(\"account.label.email\")}}<span class=\"text-red-500\">*</span></label>\r\n                                <div class=\"col\">\r\n                                    <input class=\"w-full\"\r\n                                           pInputText id=\"email\"\r\n                                           [(ngModel)]=\"accountInfo.email\"\r\n                                           formControlName=\"email\"\r\n                                           [required]=\"true\"\r\n                                           [maxLength]=\"255\"\r\n                                           pattern=\"^[a-z0-9]+[a-z0-9\\-\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\-\\._]*[a-z0-9]+)+(\\.[a-z]{2,})$\"\r\n                                           [placeholder]=\"tranService.translate('account.text.inputEmail')\"\r\n                                           (ngModelChange)=\"checkExistAccount('email')\"\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n                            <!-- error email -->\r\n                            <div class=\"w-full field grid text-error-field\">\r\n                                <label htmlFor=\"email\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                                <div class=\"col\">\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.email.dirty && formAccount.controls.email.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.email.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.email.errors?.pattern\">{{tranService.translate(\"global.message.invalidEmail\")}}</small>\r\n                                    <small class=\"text-red-500\" *ngIf=\"isEmailExisted\">{{tranService.translate(\"global.message.exists\",{type: tranService.translate(\"account.label.email\").toLowerCase()})}}</small>\r\n                                </div>\r\n                            </div>\r\n                            <!-- description -->\r\n                            <div class=\"w-full field grid\">\r\n                                <label htmlFor=\"description\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.description\")}}</label>\r\n                                <div class=\"col\">\r\n                            <textarea  class=\"w-full\" style=\"resize: none;\"\r\n                                       rows=\"5\"\r\n                                       [autoResize]=\"false\"\r\n                                       pInputTextarea id=\"description\"\r\n                                       [(ngModel)]=\"accountInfo.description\"\r\n                                       formControlName=\"description\"\r\n                                       [maxlength]=\"255\"\r\n                                       [placeholder]=\"tranService.translate('sim.text.inputDescription')\"\r\n                            ></textarea>\r\n                                </div>\r\n                            </div>\r\n                            <!-- error description -->\r\n                            <div class=\"w-full field grid text-error-field\">\r\n                                <label htmlFor=\"description\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                                <div class=\"col\">\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.description.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div style=\"width: 49%;\">\r\n                            <!-- loai tai khoan -->\r\n                            <div class=\"w-full field grid\">\r\n                                <label for=\"userType\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.userType\")}}<span class=\"text-red-500\">*</span></label>\r\n                                <div class=\"col\">\r\n                                    <p-dropdown styleClass=\"w-full\"\r\n                                                [showClear]=\"true\"\r\n                                                id=\"userType\" [autoDisplayFirst]=\"false\"\r\n                                                [(ngModel)]=\"accountInfo.userType\"\r\n                                                [required]=\"true\"\r\n                                                formControlName=\"userType\"\r\n                                                [options]=\"statusAccounts\"\r\n                                                optionLabel=\"name\"\r\n                                                optionValue=\"value\"\r\n                                                [placeholder]=\"tranService.translate('account.text.selectUserType')\"\r\n                                    ></p-dropdown>\r\n                                </div>\r\n                            </div>\r\n                            <!-- error loai tai khoan -->\r\n                            <div class=\"w-full field grid text-error-field\">\r\n                                <label htmlFor=\"userType\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                                <div class=\"col\">\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.userType.dirty && formAccount.controls.userType.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                                </div>\r\n                            </div>\r\n                            <!-- Tinh thanh pho -->\r\n                            <div class=\"w-full field grid\" *ngIf=\"userType == optionUserType.ADMIN && accountInfo.userType != optionUserType.ADMIN && accountInfo.userType != optionUserType.AGENCY\">\r\n                                <label htmlFor=\"province\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.province\")}}<span class=\"text-red-500\">*</span></label>\r\n                                <div class=\"col\">\r\n                                    <p-dropdown styleClass=\"w-full\"\r\n                                                [showClear]=\"true\"\r\n                                                id=\"province\" [autoDisplayFirst]=\"false\"\r\n                                                [(ngModel)]=\"accountInfo.province\"\r\n                                                [required]=\"userType == optionUserType.ADMIN && accountInfo.userType != optionUserType.ADMIN && accountInfo.userType != optionUserType.AGENCY\"\r\n                                                formControlName=\"province\"\r\n                                                [options]=\"listProvince\"\r\n                                                optionLabel=\"name\"\r\n                                                [filter]=\"true\" filterBy=\"name\"\r\n                                                optionValue=\"id\"\r\n                                                [placeholder]=\"tranService.translate('account.text.selectProvince')\"\r\n                                    ></p-dropdown>\r\n                                </div>\r\n                            </div>\r\n                            <!-- (ngModelChange)=\"getListCustomer(true)\" -->\r\n                            <!-- error tinh thanh pho -->\r\n                            <div class=\"w-full field grid text-error-field\" *ngIf=\"userType == optionUserType.ADMIN && accountInfo.userType != optionUserType.ADMIN && accountInfo.userType != optionUserType.AGENCY\">\r\n                                <label htmlFor=\"province\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                                <div class=\"col\">\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.province.dirty && formAccount.controls.province.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                                </div>\r\n                            </div>\r\n<!--                            &lt;!&ndash; ten khach hang &ndash;&gt;-->\r\n<!--                            <div class=\"w-full field grid\" *ngIf=\"accountInfo.userType == optionUserType.CUSTOMER\">-->\r\n<!--                                <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.customerName\")}}<span class=\"text-red-500\">*</span></label>-->\r\n<!--                                <div class=\"col\" style=\"max-width: calc(100% - 180px) !important;\">-->\r\n<!--                                    <vnpt-select-->\r\n<!--                                        [control]=\"controlComboSelect\"-->\r\n<!--                                        class=\"w-full\"-->\r\n<!--                                        [(value)]=\"accountInfo.customers\"-->\r\n<!--                                        [placeholder]=\"tranService.translate('account.text.selectCustomers')\"-->\r\n<!--                                        objectKey=\"customer\"-->\r\n<!--                                        paramKey=\"customerName\"-->\r\n<!--                                        keyReturn=\"id\"-->\r\n<!--                                        displayPattern=\"${customerName} - ${customerCode}\"-->\r\n<!--                                        typeValue=\"primitive\"-->\r\n<!--                                        [paramDefault]=\"paramSearchCustomerProvince\"-->\r\n<!--                                        [required]=\"accountInfo.userType == optionUserType.CUSTOMER\"-->\r\n<!--                                    ></vnpt-select>-->\r\n<!--                                </div>-->\r\n<!--                            </div>-->\r\n<!--                            &lt;!&ndash; error khach hang &ndash;&gt;-->\r\n<!--                            <div class=\"w-full field grid text-error-field\" *ngIf=\"accountInfo.userType == optionUserType.CUSTOMER\">-->\r\n<!--                                <label htmlFor=\"province\" class=\"col-fixed\" style=\"width:180px\"></label>-->\r\n<!--                                <div class=\"col\">-->\r\n<!--                                    <small class=\"text-red-500\" *ngIf=\"controlComboSelect.dirty && controlComboSelect.error.required\">{{tranService.translate(\"global.message.required\")}}</small>-->\r\n<!--                                </div>-->\r\n<!--                            </div>-->\r\n                            <!-- GDV quan ly-->\r\n                            <div class=\"w-full field grid\" [class]=\"accountInfo.userType == optionUserType.CUSTOMER && (userType == optionUserType.ADMIN || userType == optionUserType.PROVINCE) ? '' : 'hidden'\">\r\n                                <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.managerName\")}}<span *ngIf=\"accountInfo.isRootCustomer\" class=\"text-red-500\">*</span></label>\r\n                                <div class=\"col\" style=\"max-width: calc(100% - 180px)\">\r\n                                    <vnpt-select\r\n                                        [control]=\"controlComboSelectManager\"\r\n                                        class=\"w-full\"\r\n                                        [(value)]=\"accountInfo.manager\"\r\n                                        [placeholder]=\"tranService.translate('account.text.selectGDV')\"\r\n                                        objectKey=\"account\"\r\n                                        paramKey=\"name\"\r\n                                        keyReturn=\"id\"\r\n                                        displayPattern=\"${fullName} - ${username}\"\r\n                                        typeValue=\"primitive\"\r\n                                        [isMultiChoice]=\"false\"\r\n                                        [paramDefault]=\"paramSearchManager\"\r\n                                        [disabled]=\"!accountInfo.isRootCustomer\"\r\n                                        [required]=\"accountInfo.isRootCustomer ? true : false\"\r\n                                        (onchange)=\"onChangeTeller()\"\r\n                                    ></vnpt-select>\r\n                                </div>\r\n                            </div>\r\n                            <!-- error GDV quan ly -->\r\n                            <div class=\"w-full field grid text-error-field\" *ngIf=\"accountInfo.userType == optionUserType.CUSTOMER\">\r\n                                <label htmlFor=\"province\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                                <div class=\"col\">\r\n                                    <small class=\"text-red-500\" *ngIf=\"controlComboSelectManager.dirty && controlComboSelectManager.error.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                                </div>\r\n                            </div>\r\n                            <!-- Danh sach tai khoan khach hang -->\r\n                            <div class=\"w-full field grid\" *ngIf=\"accountInfo.userType == optionUserType.DISTRICT\">\r\n                                <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.customerAccount\")}}</label>\r\n                                <div class=\"col\" style=\"max-width: calc(100% - 180px)   \">\r\n                                    <vnpt-select\r\n                                        [control]=\"controlComboSelectCustomerAccount\"\r\n                                        class=\"w-full\"\r\n                                        [(value)]=\"accountInfo.customerAccounts\"\r\n                                        [placeholder]=\"tranService.translate('account.text.selectCustomerAccount')\"\r\n                                        objectKey=\"account\"\r\n                                        paramKey=\"username\"\r\n                                        keyReturn=\"id\"\r\n                                        displayPattern=\"${fullName} - ${username}\"\r\n                                        typeValue=\"primitive\"\r\n                                        [paramDefault]=\"paramSearchCustomerAccount\"\r\n                                        [loadData]=\"loadCustomerAccount.bind(this)\"\r\n                                    ></vnpt-select>\r\n                                </div>\r\n                            </div>\r\n                            <!-- error Danh sach tai khoan khach hang-->\r\n                            <div class=\"w-full field grid text-error-field\" *ngIf=\"accountInfo.userType == optionUserType.DISTRICT\">\r\n                                <label htmlFor=\"province\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                                <div class=\"col\">\r\n                                    <small class=\"text-red-500\" *ngIf=\"controlComboSelectCustomerAccount.dirty && controlComboSelectCustomerAccount.error.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                                </div>\r\n                            </div>\r\n                            <!-- Tài khoản khách hàng root chọn khi tạo-->\r\n                            <div class=\"w-full field grid align-items-start\" *ngIf=\"accountInfo.userType == optionUserType.CUSTOMER && !accountResponse?.isRootCustomer\">\r\n                                <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.customerAccount\")}}</label>\r\n                                <div class=\"col\" style=\"max-width: calc(100% - 180px)\">\r\n                                    <input pInputText class=\"w-full\" value=\"{{accountResponse?.rootAccount?.username ? accountResponse?.rootAccount?.username : tranService.translate('account.text.selectCustomerAccount')}}\" [disabled]=\"true\" [readonly]=\"true\"/>\r\n                                </div>\r\n                            </div>\r\n                            <!-- nhom quyen -->\r\n                            <div class=\"w-full field grid\">\r\n                                <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.role\")}}</label>\r\n                                <div class=\"col\" style=\"max-width: calc(100% - 180px)\">\r\n                                    <vnpt-select\r\n                                        class=\"w-full\"\r\n                                        [control]=\"controlComboSelectRole\"\r\n                                        [(value)]=\"accountInfo.roleLst\"\r\n                                        objectKey=\"role\"\r\n                                        paramKey=\"name\"\r\n                                        [paramDefault]=\"paramSearchRoleActive\"\r\n                                        keyReturn=\"id\"\r\n                                        displayPattern=\"${name}\"\r\n                                        [loadData]=\"getListRole.bind(this)\"\r\n                                        [isFilterLocal]=\"true\"\r\n                                    ></vnpt-select>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </p-tabPanel>\r\n                <p-tabPanel header=\"{{tranService.translate('account.text.addCustomer')}}*\" *ngIf=\"checkShowTabAddCustomerAndContract()\">\r\n                    <div class=\"flex flex-row justify-content-center gap-3 mt-4 resp-search-bar\">\r\n                        <input style=\"min-width: 35vw\"  type=\"text\" pInputText [placeholder]=\"tranService.translate('sim.label.quickSearch')\" (keydown.enter)=\"$event.preventDefault(); onSearchCustomer(true)\" [(ngModel)]=\"paramQuickSearchCustomer.keyword\" [ngModelOptions]=\"{standalone: true}\">\r\n                        <p-button icon=\"pi pi-search\"\r\n                                  styleClass=\"ml-3 p-button-rounded p-button-secondary p-button-text button-search\"\r\n                                  type=\"button\"\r\n                                  (click)=\"onSearchCustomer(true)\"\r\n                        ></p-button>\r\n                    </div>\r\n                    <div class=\"flex relative justify-content-center align-items-center w-full h-full\">\r\n                        <div class=\"absolute flex justify-content-center align-items-center w-full h-full\" [ngStyle]=\"{\r\n                           'top': '50%',\r\n                           'left': '50%',\r\n                           'transform': 'translate(-50%, -50%)',\r\n                           'z-index': '10',\r\n                           'background': 'rgba(0, 0, 0, 0.1)'\r\n                         }\"\r\n                             *ngIf=\"loadingCustomer\">\r\n                            <p-progressSpinner></p-progressSpinner>\r\n                        </div>\r\n                        <div class=\"w-full h-full\">\r\n                            <table-vnpt\r\n                                [fieldId]=\"'id'\"\r\n                                [pageNumber]=\"paginationCustomer.page\"\r\n                                [pageSize]=\"paginationCustomer.size\"\r\n                                [(selectItems)]=\"selectItemCustomer\"\r\n                                [columns]=\"columnInfoCustomer\"\r\n                                [dataSet]=\"dataSetCustomer\"\r\n                                [options]=\"optionTableCustomer\"\r\n                                [loadData]=\"searchCustomer.bind(this)\"\r\n                                [rowsPerPageOptions]=\"[5,10,20,25,50]\"\r\n                                [scrollHeight]=\"'400px'\"\r\n                                [sort]=\"paginationCustomer.sortBy\"\r\n                                [params]=\"paramQuickSearchCustomer\"\r\n                                (selectItemsChange)=\"checkSelectItemChangeCustomer($event)\"\r\n                                (onChangeCustomSelectAllEmmiter)=\"onChangeSelectAllItemsCustomer()\"\r\n                                isUseCustomSelectAll=\"true\"\r\n                                [(customSelectAll)]=\"customSelectAllCustomer\"\r\n                            ></table-vnpt>\r\n                        </div>\r\n                    </div>\r\n                </p-tabPanel>\r\n                <p-tabPanel header=\"{{tranService.translate('account.text.addContract')}}\" *ngIf=\"checkShowTabAddCustomerAndContract() && selectItemCustomer && selectItemCustomer.length > 0\">\r\n                    <div class=\"flex flex-row justify-content-center gap-3 mt-4 resp-search-bar\">\r\n                        <input style=\"min-width: 35vw\"  type=\"text\" pInputText [placeholder]=\"tranService.translate('sim.label.quickSearch')\" (keydown.enter)=\"$event.preventDefault(); onSearchContract(true)\" [(ngModel)]=\"paramQuickSearchContract.keyword\" [ngModelOptions]=\"{standalone: true}\">\r\n                        <p-button icon=\"pi pi-search\"\r\n                                  styleClass=\"ml-3 p-button-rounded p-button-secondary p-button-text button-search\"\r\n                                  type=\"button\"\r\n                                  (click)=\"onSearchContract(true)\"\r\n                        ></p-button>\r\n                    </div>\r\n                    <div class=\"flex relative justify-content-center align-items-center w-full h-full\">\r\n                        <div class=\"absolute flex justify-content-center align-items-center w-full h-full\" [ngStyle]=\"{\r\n                           'top': '50%',\r\n                           'left': '50%',\r\n                           'transform': 'translate(-50%, -50%)',\r\n                           'z-index': '10',\r\n                           'background': 'rgba(0, 0, 0, 0.1)'\r\n                         }\"\r\n                             *ngIf=\"loadingContract\">\r\n                            <p-progressSpinner></p-progressSpinner>\r\n                        </div>\r\n                        <div class=\"w-full h-full\">\r\n                            <table-vnpt\r\n                                [fieldId]=\"'id'\"\r\n                                [pageNumber]=\"paginationContract.page\"\r\n                                [pageSize]=\"paginationContract.size\"\r\n                                [(selectItems)]=\"selectItemContract\"\r\n                                [columns]=\"columnInfoContract\"\r\n                                [dataSet]=\"dataSetContract\"\r\n                                [options]=\"optionTableContract\"\r\n                                [loadData]=\"searchContract.bind(this)\"\r\n                                [rowsPerPageOptions]=\"[5,10,20,25,50]\"\r\n                                [scrollHeight]=\"'400px'\"\r\n                                [sort]=\"paginationContract.sortBy\"\r\n                                [params]=\"paramQuickSearchContract\"\r\n                                (selectItemsChange)=\"checkSelectItemChangeContract($event)\"\r\n                                (onChangeCustomSelectAllEmmiter)=\"onChangeSelectAllItemsContract()\"\r\n                                isUseCustomSelectAll=\"true\"\r\n                                [(customSelectAll)]=\"customSelectAllContract\"\r\n                            ></table-vnpt>\r\n                        </div>\r\n                    </div>\r\n                </p-tabPanel>\r\n                <p-tabPanel header=\"{{tranService.translate('account.text.grantApi')}}\" *ngIf=\"checkAuthen([CONSTANTS.PERMISSIONS.THIRD_PARTY_API.GRANT_PERMISSION_3RD_API]) && accountInfo.userType == optionUserType.CUSTOMER\" [pt]=\"'ProfileTab'\">\r\n                    <div class=\"mb-3\">\r\n                        <p-panel [showHeader]=\"false\">\r\n                            <div class=\"flex gap-2\">\r\n                                <p-radioButton\r\n                                        [label]=\"tranService.translate('account.text.working')\"\r\n                                        value=\"1\"\r\n                                        class=\"p-3\"\r\n                                        [(ngModel)]=\"statusGrantApi\"\r\n                                        inputId=\"1\"\r\n                                        (onClick)=\"changeGrantApiPermission()\"\r\n                                        [ngModelOptions]=\"{standalone: true}\"\r\n                                >\r\n                                </p-radioButton>\r\n                                <p-radioButton\r\n                                        [label]=\"tranService.translate('account.text.notWorking')\"\r\n                                        value=\"0\"\r\n                                        class=\"p-3\"\r\n                                        [(ngModel)]=\"statusGrantApi\"\r\n                                        (onClick)=\"changeGrantApiPermission()\"\r\n                                        [ngModelOptions]=\"{standalone: true}\"\r\n                                >\r\n                                </p-radioButton>\r\n                            </div>\r\n                            <div class=\"flex gap-3 align-items-center api-input-section-edit\">\r\n                                <div class=\"col-5\">\r\n                                    <div class=\"flex align-items-center\">\r\n                                        <label style=\"min-width: 100px\" class=\"mr-3\">Client ID</label>\r\n                                        <input [(ngModel)]=\"genGrantApi.clientId\"  [disabled]=\"true\" [ngModelOptions]=\"{standalone: true}\" class=\"w-full\" type=\"text\" pInputText>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"col-5\">\r\n                                    <div class=\"flex align-items-center\">\r\n                                        <label style=\"min-width: 100px\" class=\"mr-3\">Secret Key</label>\r\n                                        <div class=\"w-full flex align-items-center\">\r\n                                            <input class=\"w-full mr-2\" style=\"padding-right: 30px;\"\r\n                                                   [(ngModel)]=\"genGrantApi.secretKey\"\r\n                                                   [ngModelOptions]=\"{standalone: true}\"\r\n                                                   [type]=\"isShowSecretKey ? 'text': 'password'\"\r\n                                                   pInputText\r\n                                                   [disabled]=\"true\"\r\n                                            />\r\n                                            <label style=\"margin-left: -30px;z-index: 1;\" *ngIf=\"isShowSecretKey == false\" class=\"pi pi-eye toggle-password\" (click)=\"isShowSecretKey = true\"></label>\r\n                                            <label style=\"margin-left: -30px;z-index: 1;\" *ngIf=\"isShowSecretKey == true\" class=\"pi pi-eye-slash toggle-password\" (click)=\"isShowSecretKey = false\"></label>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"col-2 btn-gen\">\r\n                                    <p-button (click)=\"genToken()\" [label]=\"tranService.translate('account.text.gen')\" styleClass=\"p-button-primary mr-2\"></p-button>\r\n                                </div>\r\n                            </div>\r\n                        </p-panel>\r\n                    </div>\r\n                    <div>\r\n                        <p-panel [showHeader]=\"false\" *ngIf=\"genGrantApi.secretKey\">\r\n                            <div class=\"flex gap-3 align-items-center module-search\">\r\n                                <div class=\"col-3 dropdown-fit\">\r\n                                    <p-dropdown class=\"w-full\"\r\n                                                [showClear]=\"true\"\r\n                                                [(ngModel)]=\"paramsSearchGrantApi.module\"\r\n                                                [ngModelOptions]=\"{standalone: true}\"\r\n                                                [options]=\"listModule\"\r\n                                                optionLabel=\"name\"\r\n                                                optionValue=\"value\"\r\n                                                [emptyFilterMessage]=\"tranService.translate('global.text.nodata')\"\r\n                                                filter=\"true\"\r\n                                                [placeholder]=\"tranService.translate('account.text.module')\"\r\n                                    ></p-dropdown>\r\n                                </div>\r\n                                <div class=\"col-3 dropdown-fit\">\r\n                                    <input [(ngModel)]=\"paramsSearchGrantApi.api\" [ngModelOptions]=\"{standalone: true}\" class=\"w-full mr-2\" type=\"text\" pInputText placeholder=\"API\"/>\r\n                                </div>\r\n                                <p-button icon=\"pi pi-search\"\r\n                                          styleClass=\"ml-3 p-button-rounded p-button-secondary p-button-text button-search\"\r\n                                          type=\"button\"\r\n                                          (click)=\"onSearchGrantApi(true)\"\r\n                                ></p-button>\r\n                            </div>\r\n\r\n                            <table-vnpt\r\n                                    [fieldId]=\"'id'\"\r\n                                    [pageNumber]=\"paginationGrantApi.page\"\r\n                                    [pageSize]=\"paginationGrantApi.size\"\r\n                                    [(selectItems)]=\"selectItemGrantApi\"\r\n                                    [columns]=\"columnInfoGrantApi\"\r\n                                    [dataSet]=\"dataSetGrantApi\"\r\n                                    [options]=\"optionTableGrantApi\"\r\n                                    [loadData]=\"searchGrantApi.bind(this)\"\r\n                                    [rowsPerPageOptions]=\"[5,10,20,25,50]\"\r\n                                    [scrollHeight]=\"'400px'\"\r\n                                    [sort]=\"paginationGrantApi.sortBy\"\r\n                                    [params]=\"paramsSearchGrantApi\"\r\n                            ></table-vnpt>\r\n                        </p-panel>\r\n                    </div>\r\n                </p-tabPanel>\r\n            </p-tabView>\r\n\r\n<!--            <div class=\"flex flex-row justify-content-center align-items-center\">-->\r\n<!--                <p-button [label]=\"tranService.translate('global.button.cancel')\" styleClass=\"p-button-secondary p-button-outlined mr-2\" (click)=\"closeForm()\"></p-button>-->\r\n<!--                <p-button [label]=\"tranService.translate('global.button.save')\" styleClass=\"p-button-info\" type=\"submit\"-->\r\n<!--                [disabled]=\"formAccount.invalid || isEmailExisted || isPhoneExisted || isUsernameExisted ||-->\r\n<!--                accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER && accountInfo.isRootCustomer && accountInfo.manager == null && userType != CONSTANTS.USER_TYPE.DISTRICT\"></p-button>-->\r\n<!--            </div>-->\r\n    </div>\r\n</p-card>\r\n</form>\r\n"], "mappings": "AAGA,SAASA,aAAa,QAAQ,wBAAwB;AAEtD,SAASC,SAAS,QAAQ,iCAAiC;AAE3D,SAASC,gBAAgB,QAAQ,yDAAyD;;;;;;;;;;;;;;;;;;;;;ICkCtDC,EAAA,CAAAC,cAAA,gBAAgI;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,4BAAoD;;;;;;;;;;IACpLR,EAAA,CAAAC,cAAA,gBAAuF;IAAAD,EAAA,CAAAE,MAAA,GAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAtEH,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAK,iBAAA,CAAAI,MAAA,CAAAF,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAC,GAAA,GAA8D;;;;;IACrJX,EAAA,CAAAC,cAAA,gBAAqF;IAAAD,EAAA,CAAAE,MAAA,GAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA9DH,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAAO,MAAA,CAAAL,WAAA,CAAAC,SAAA,8BAAsD;;;;;;;;;;IAC3IR,EAAA,CAAAC,cAAA,gBAAsD;IAAAD,EAAA,CAAAE,MAAA,GAAwH;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAhIH,EAAA,CAAAI,SAAA,GAAwH;IAAxHJ,EAAA,CAAAK,iBAAA,CAAAQ,MAAA,CAAAN,WAAA,CAAAC,SAAA,0BAAAR,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAF,MAAA,CAAAN,WAAA,CAAAC,SAAA,2BAAAQ,WAAA,KAAwH;;;;;IAsB9KhB,EAAA,CAAAC,cAAA,gBAA0H;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAY,MAAA,CAAAV,WAAA,CAAAC,SAAA,4BAAoD;;;;;;;;;;IAC9KR,EAAA,CAAAC,cAAA,gBAAoF;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAvEH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,iBAAA,CAAAa,MAAA,CAAAX,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAS,GAAA,GAA+D;;;;;IACnJnB,EAAA,CAAAC,cAAA,gBAAkF;IAAAD,EAAA,CAAAE,MAAA,GAA2D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAnEH,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAK,iBAAA,CAAAe,MAAA,CAAAb,WAAA,CAAAC,SAAA,mCAA2D;;;;;IAoB7IR,EAAA,CAAAC,cAAA,gBAA+E;IAAAD,EAAA,CAAAE,MAAA,GAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAhEH,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAK,iBAAA,CAAAgB,MAAA,CAAAd,WAAA,CAAAC,SAAA,gCAAwD;;;;;IACvIR,EAAA,CAAAC,cAAA,gBAAmD;IAAAD,EAAA,CAAAE,MAAA,GAAqH;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA7HH,EAAA,CAAAI,SAAA,GAAqH;IAArHJ,EAAA,CAAAK,iBAAA,CAAAiB,MAAA,CAAAf,WAAA,CAAAC,SAAA,0BAAAR,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAO,MAAA,CAAAf,WAAA,CAAAC,SAAA,wBAAAQ,WAAA,KAAqH;;;;;IAuBxKhB,EAAA,CAAAC,cAAA,gBAAoH;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAkB,OAAA,CAAAhB,WAAA,CAAAC,SAAA,4BAAoD;;;;;IACxKR,EAAA,CAAAC,cAAA,gBAAiF;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAvEH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,iBAAA,CAAAmB,OAAA,CAAAjB,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAS,GAAA,GAA+D;;;;;IAChJnB,EAAA,CAAAC,cAAA,gBAA+E;IAAAD,EAAA,CAAAE,MAAA,GAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAhEH,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAK,iBAAA,CAAAoB,OAAA,CAAAlB,WAAA,CAAAC,SAAA,gCAAwD;;;;;IACvIR,EAAA,CAAAC,cAAA,gBAAmD;IAAAD,EAAA,CAAAE,MAAA,GAAqH;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA7HH,EAAA,CAAAI,SAAA,GAAqH;IAArHJ,EAAA,CAAAK,iBAAA,CAAAqB,OAAA,CAAAnB,WAAA,CAAAC,SAAA,0BAAAR,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAW,OAAA,CAAAnB,WAAA,CAAAC,SAAA,wBAAAQ,WAAA,KAAqH;;;;;IAsBxKhB,EAAA,CAAAC,cAAA,gBAAuF;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAvEH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,iBAAA,CAAAsB,OAAA,CAAApB,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAS,GAAA,GAA+D;;;;;IA0BtJnB,EAAA,CAAAC,cAAA,gBAA0H;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAuB,OAAA,CAAArB,WAAA,CAAAC,SAAA,4BAAoD;;;;;;IAItLR,EAAA,CAAAC,cAAA,cAAyK;IACrGD,EAAA,CAAAE,MAAA,GAAmD;IAAAF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtJH,EAAA,CAAAC,cAAA,cAAiB;IAIDD,EAAA,CAAA6B,UAAA,2BAAAC,mFAAAC,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAlC,EAAA,CAAAmC,aAAA;MAAA,OAAanC,EAAA,CAAAoC,WAAA,CAAAF,OAAA,CAAAG,WAAA,CAAAC,QAAA,GAAAP,MAAA,CACxD;IAAA,EAD6E;IAQ7C/B,EAAA,CAAAG,YAAA,EAAa;;;;IAb8CH,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAAkC,OAAA,CAAAhC,WAAA,CAAAC,SAAA,2BAAmD;IAGnGR,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAwC,UAAA,mBAAkB,uCAAAD,OAAA,CAAAF,WAAA,CAAAC,QAAA,cAAAC,OAAA,CAAAE,QAAA,IAAAF,OAAA,CAAAG,cAAA,CAAAC,KAAA,IAAAJ,OAAA,CAAAF,WAAA,CAAAI,QAAA,IAAAF,OAAA,CAAAG,cAAA,CAAAC,KAAA,IAAAJ,OAAA,CAAAF,WAAA,CAAAI,QAAA,IAAAF,OAAA,CAAAG,cAAA,CAAAE,MAAA,aAAAL,OAAA,CAAAM,YAAA,iCAAAN,OAAA,CAAAhC,WAAA,CAAAC,SAAA;;;;;IAkB9BR,EAAA,CAAAC,cAAA,gBAA0H;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAyC,OAAA,CAAAvC,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAHtLR,EAAA,CAAAC,cAAA,cAA0L;IACtLD,EAAA,CAAA+C,SAAA,gBAAwE;IACxE/C,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAgD,UAAA,IAAAC,sDAAA,oBAAsL;IAC1LjD,EAAA,CAAAG,YAAA,EAAM;;;;IAD2BH,EAAA,CAAAI,SAAA,GAA2F;IAA3FJ,EAAA,CAAAwC,UAAA,SAAAU,OAAA,CAAAC,WAAA,CAAAC,QAAA,CAAAd,QAAA,CAAAe,KAAA,KAAAH,OAAA,CAAAC,WAAA,CAAAC,QAAA,CAAAd,QAAA,CAAAgB,MAAA,kBAAAJ,OAAA,CAAAC,WAAA,CAAAC,QAAA,CAAAd,QAAA,CAAAgB,MAAA,CAAAC,QAAA,EAA2F;;;;;IA+BTvD,EAAA,CAAAC,cAAA,eAA8D;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAwBrLH,EAAA,CAAAC,cAAA,gBAAgH;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAmD,OAAA,CAAAjD,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAH5KR,EAAA,CAAAC,cAAA,cAAwG;IACpGD,EAAA,CAAA+C,SAAA,gBAAwE;IACxE/C,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAgD,UAAA,IAAAS,sDAAA,oBAA4K;IAChLzD,EAAA,CAAAG,YAAA,EAAM;;;;IAD2BH,EAAA,CAAAI,SAAA,GAAiF;IAAjFJ,EAAA,CAAAwC,UAAA,SAAAkB,OAAA,CAAAC,yBAAA,CAAAN,KAAA,IAAAK,OAAA,CAAAC,yBAAA,CAAAC,KAAA,CAAAL,QAAA,CAAiF;;;;;;IAItHvD,EAAA,CAAAC,cAAA,cAAuF;IACtBD,EAAA,CAAAE,MAAA,GAA0D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/HH,EAAA,CAAAC,cAAA,cAA0D;IAIlDD,EAAA,CAAA6B,UAAA,yBAAAgC,kFAAA9B,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAA8B,IAAA;MAAA,MAAAC,OAAA,GAAA/D,EAAA,CAAAmC,aAAA;MAAA,OAAWnC,EAAA,CAAAoC,WAAA,CAAA2B,OAAA,CAAA1B,WAAA,CAAA2B,gBAAA,GAAAjC,MAAA,CAC9C;IAAA,EAD2E;IAS3C/B,EAAA,CAAAG,YAAA,EAAc;;;;IAd0CH,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAK,iBAAA,CAAA4D,OAAA,CAAA1D,WAAA,CAAAC,SAAA,kCAA0D;IAG/GR,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAwC,UAAA,YAAAyB,OAAA,CAAAC,iCAAA,CAA6C,UAAAD,OAAA,CAAA5B,WAAA,CAAA2B,gBAAA,iBAAAC,OAAA,CAAA1D,WAAA,CAAAC,SAAA,wDAAAyD,OAAA,CAAAE,0BAAA,cAAAF,OAAA,CAAAG,mBAAA,CAAAC,IAAA,CAAAJ,OAAA;;;;;IAkBjDjE,EAAA,CAAAC,cAAA,gBAAgI;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAiE,OAAA,CAAA/D,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAH5LR,EAAA,CAAAC,cAAA,cAAwG;IACpGD,EAAA,CAAA+C,SAAA,gBAAwE;IACxE/C,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAgD,UAAA,IAAAuB,sDAAA,oBAA4L;IAChMvE,EAAA,CAAAG,YAAA,EAAM;;;;IAD2BH,EAAA,CAAAI,SAAA,GAAiG;IAAjGJ,EAAA,CAAAwC,UAAA,SAAAgC,OAAA,CAAAN,iCAAA,CAAAb,KAAA,IAAAmB,OAAA,CAAAN,iCAAA,CAAAN,KAAA,CAAAL,QAAA,CAAiG;;;;;IAItIvD,EAAA,CAAAC,cAAA,cAA6I;IAC5ED,EAAA,CAAAE,MAAA,GAA0D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/HH,EAAA,CAAAC,cAAA,cAAuD;IACnDD,EAAA,CAAA+C,SAAA,gBAAgO;IACpO/C,EAAA,CAAAG,YAAA,EAAM;;;;IAHuDH,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAK,iBAAA,CAAAoE,OAAA,CAAAlE,WAAA,CAAAC,SAAA,kCAA0D;IAElFR,EAAA,CAAAI,SAAA,GAAyJ;IAAzJJ,EAAA,CAAA0E,qBAAA,WAAAD,OAAA,CAAAE,eAAA,kBAAAF,OAAA,CAAAE,eAAA,CAAAC,WAAA,kBAAAH,OAAA,CAAAE,eAAA,CAAAC,WAAA,CAAAC,QAAA,IAAAJ,OAAA,CAAAE,eAAA,kBAAAF,OAAA,CAAAE,eAAA,CAAAC,WAAA,kBAAAH,OAAA,CAAAE,eAAA,CAAAC,WAAA,CAAAC,QAAA,GAAAJ,OAAA,CAAAlE,WAAA,CAAAC,SAAA,uCAAyJ;IAACR,EAAA,CAAAwC,UAAA,kBAAiB;;;;;;;;;;;;;;IAkCxNxC,EAAA,CAAAC,cAAA,cAO6B;IACzBD,EAAA,CAAA+C,SAAA,wBAAuC;IAC3C/C,EAAA,CAAAG,YAAA,EAAM;;;IAT6EH,EAAA,CAAAwC,UAAA,YAAAxC,EAAA,CAAAU,eAAA,IAAAoE,GAAA,EAMhF;;;;;;;;;;;;;;IAhBX9E,EAAA,CAAAC,cAAA,qBAAyH;IAEKD,EAAA,CAAA6B,UAAA,2BAAAkD,sFAAAhD,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAgD,IAAA;MAAA,MAAAC,OAAA,GAAAjF,EAAA,CAAAmC,aAAA;MAAiBJ,MAAA,CAAAmD,cAAA,EAAuB;MAAA,OAAElF,EAAA,CAAAoC,WAAA,CAAA6C,OAAA,CAAAE,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC,2BAAAC,sFAAArD,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAgD,IAAA;MAAA,MAAAK,OAAA,GAAArF,EAAA,CAAAmC,aAAA;MAAA,OAAcnC,EAAA,CAAAoC,WAAA,CAAAiD,OAAA,CAAAC,wBAAA,CAAAC,OAAA,GAAAxD,MAAA,CAAwC;IAAA,EAAtD;IAAvL/B,EAAA,CAAAG,YAAA,EAA6Q;IAC7QH,EAAA,CAAAC,cAAA,mBAIC;IADSD,EAAA,CAAA6B,UAAA,mBAAA2D,iFAAA;MAAAxF,EAAA,CAAAgC,aAAA,CAAAgD,IAAA;MAAA,MAAAS,OAAA,GAAAzF,EAAA,CAAAmC,aAAA;MAAA,OAASnC,EAAA,CAAAoC,WAAA,CAAAqD,OAAA,CAAAN,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC;IACzCnF,EAAA,CAAAG,YAAA,EAAW;IAEhBH,EAAA,CAAAC,cAAA,cAAmF;IAC/ED,EAAA,CAAAgD,UAAA,IAAA0C,4DAAA,kBASM;IACN1F,EAAA,CAAAC,cAAA,cAA2B;IAKnBD,EAAA,CAAA6B,UAAA,+BAAA8D,+FAAA5D,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAgD,IAAA;MAAA,MAAAY,OAAA,GAAA5F,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAAwD,OAAA,CAAAC,kBAAA,GAAA9D,MAAA;IAAA,EAAoC,+BAAA4D,+FAAA5D,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAgD,IAAA;MAAA,MAAAc,OAAA,GAAA9F,EAAA,CAAAmC,aAAA;MAAA,OASfnC,EAAA,CAAAoC,WAAA,CAAA0D,OAAA,CAAAC,6BAAA,CAAAhE,MAAA,CAAqC;IAAA,EATtB,4CAAAiE,4GAAA;MAAAhG,EAAA,CAAAgC,aAAA,CAAAgD,IAAA;MAAA,MAAAiB,OAAA,GAAAjG,EAAA,CAAAmC,aAAA;MAAA,OAUFnC,EAAA,CAAAoC,WAAA,CAAA6D,OAAA,CAAAC,8BAAA,EAAgC;IAAA,EAV9B,mCAAAC,mGAAApE,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAgD,IAAA;MAAA,MAAAoB,OAAA,GAAApG,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAAgE,OAAA,CAAAC,uBAAA,GAAAtE,MAAA;IAAA;IAavC/B,EAAA,CAAAG,YAAA,EAAa;;;;IAtCdH,EAAA,CAAAsG,sBAAA,eAAAC,OAAA,CAAAhG,WAAA,CAAAC,SAAA,kCAA+D;IAEZR,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAwC,UAAA,gBAAA+D,OAAA,CAAAhG,WAAA,CAAAC,SAAA,0BAA8D,YAAA+F,OAAA,CAAAjB,wBAAA,CAAAC,OAAA,oBAAAvF,EAAA,CAAAU,eAAA,KAAA8F,GAAA;IAe/GxG,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAwC,UAAA,SAAA+D,OAAA,CAAAE,eAAA,CAAqB;IAKnBzG,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAwC,UAAA,iBAAgB,eAAA+D,OAAA,CAAAG,kBAAA,CAAAC,IAAA,cAAAJ,OAAA,CAAAG,kBAAA,CAAAE,IAAA,iBAAAL,OAAA,CAAAV,kBAAA,aAAAU,OAAA,CAAAM,kBAAA,aAAAN,OAAA,CAAAO,eAAA,aAAAP,OAAA,CAAAQ,mBAAA,cAAAR,OAAA,CAAAS,cAAA,CAAA3C,IAAA,CAAAkC,OAAA,yBAAAvG,EAAA,CAAAU,eAAA,KAAAuG,GAAA,oCAAAV,OAAA,CAAAG,kBAAA,CAAAQ,MAAA,YAAAX,OAAA,CAAAjB,wBAAA,qBAAAiB,OAAA,CAAAF,uBAAA;;;;;IA8BxBrG,EAAA,CAAAC,cAAA,cAO6B;IACzBD,EAAA,CAAA+C,SAAA,wBAAuC;IAC3C/C,EAAA,CAAAG,YAAA,EAAM;;;IAT6EH,EAAA,CAAAwC,UAAA,YAAAxC,EAAA,CAAAU,eAAA,IAAAoE,GAAA,EAMhF;;;;;;IAhBX9E,EAAA,CAAAC,cAAA,qBAA+K;IAEjDD,EAAA,CAAA6B,UAAA,2BAAAsF,sFAAApF,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAoF,IAAA;MAAA,MAAAC,OAAA,GAAArH,EAAA,CAAAmC,aAAA;MAAiBJ,MAAA,CAAAmD,cAAA,EAAuB;MAAA,OAAElF,EAAA,CAAAoC,WAAA,CAAAiF,OAAA,CAAAC,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC,2BAAAC,sFAAAxF,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAoF,IAAA;MAAA,MAAAI,OAAA,GAAAxH,EAAA,CAAAmC,aAAA;MAAA,OAAcnC,EAAA,CAAAoC,WAAA,CAAAoF,OAAA,CAAAC,wBAAA,CAAAlC,OAAA,GAAAxD,MAAA,CAAwC;IAAA,EAAtD;IAAvL/B,EAAA,CAAAG,YAAA,EAA6Q;IAC7QH,EAAA,CAAAC,cAAA,mBAIC;IADSD,EAAA,CAAA6B,UAAA,mBAAA6F,iFAAA;MAAA1H,EAAA,CAAAgC,aAAA,CAAAoF,IAAA;MAAA,MAAAO,OAAA,GAAA3H,EAAA,CAAAmC,aAAA;MAAA,OAASnC,EAAA,CAAAoC,WAAA,CAAAuF,OAAA,CAAAL,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC;IACzCtH,EAAA,CAAAG,YAAA,EAAW;IAEhBH,EAAA,CAAAC,cAAA,cAAmF;IAC/ED,EAAA,CAAAgD,UAAA,IAAA4E,4DAAA,kBASM;IACN5H,EAAA,CAAAC,cAAA,cAA2B;IAKnBD,EAAA,CAAA6B,UAAA,+BAAAgG,+FAAA9F,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAoF,IAAA;MAAA,MAAAU,OAAA,GAAA9H,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAA0F,OAAA,CAAAC,kBAAA,GAAAhG,MAAA;IAAA,EAAoC,+BAAA8F,+FAAA9F,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAoF,IAAA;MAAA,MAAAY,OAAA,GAAAhI,EAAA,CAAAmC,aAAA;MAAA,OASfnC,EAAA,CAAAoC,WAAA,CAAA4F,OAAA,CAAAC,6BAAA,CAAAlG,MAAA,CAAqC;IAAA,EATtB,4CAAAmG,4GAAA;MAAAlI,EAAA,CAAAgC,aAAA,CAAAoF,IAAA;MAAA,MAAAe,OAAA,GAAAnI,EAAA,CAAAmC,aAAA;MAAA,OAUFnC,EAAA,CAAAoC,WAAA,CAAA+F,OAAA,CAAAC,8BAAA,EAAgC;IAAA,EAV9B,mCAAAC,mGAAAtG,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAoF,IAAA;MAAA,MAAAkB,OAAA,GAAAtI,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAAkG,OAAA,CAAAC,uBAAA,GAAAxG,MAAA;IAAA;IAavC/B,EAAA,CAAAG,YAAA,EAAa;;;;IAtCdH,EAAA,CAAA0E,qBAAA,WAAA8D,OAAA,CAAAjI,WAAA,CAAAC,SAAA,6BAA8D;IAEXR,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAwC,UAAA,gBAAAgG,OAAA,CAAAjI,WAAA,CAAAC,SAAA,0BAA8D,YAAAgI,OAAA,CAAAf,wBAAA,CAAAlC,OAAA,oBAAAvF,EAAA,CAAAU,eAAA,KAAA8F,GAAA;IAe/GxG,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAwC,UAAA,SAAAgG,OAAA,CAAAC,eAAA,CAAqB;IAKnBzI,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAwC,UAAA,iBAAgB,eAAAgG,OAAA,CAAAE,kBAAA,CAAA/B,IAAA,cAAA6B,OAAA,CAAAE,kBAAA,CAAA9B,IAAA,iBAAA4B,OAAA,CAAAT,kBAAA,aAAAS,OAAA,CAAAG,kBAAA,aAAAH,OAAA,CAAAI,eAAA,aAAAJ,OAAA,CAAAK,mBAAA,cAAAL,OAAA,CAAAM,cAAA,CAAAzE,IAAA,CAAAmE,OAAA,yBAAAxI,EAAA,CAAAU,eAAA,KAAAuG,GAAA,oCAAAuB,OAAA,CAAAE,kBAAA,CAAAxB,MAAA,YAAAsB,OAAA,CAAAf,wBAAA,qBAAAe,OAAA,CAAAD,uBAAA;;;;;;IA8DJvI,EAAA,CAAAC,cAAA,gBAAkJ;IAAjCD,EAAA,CAAA6B,UAAA,mBAAAkH,uFAAA;MAAA/I,EAAA,CAAAgC,aAAA,CAAAgH,IAAA;MAAA,MAAAC,OAAA,GAAAjJ,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAA6G,OAAA,CAAAC,eAAA,GAA2B,IAAI;IAAA,EAAC;IAAClJ,EAAA,CAAAG,YAAA,EAAQ;;;;;;IAC1JH,EAAA,CAAAC,cAAA,gBAAwJ;IAAlCD,EAAA,CAAA6B,UAAA,mBAAAsH,uFAAA;MAAAnJ,EAAA,CAAAgC,aAAA,CAAAoH,IAAA;MAAA,MAAAC,OAAA,GAAArJ,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAAiH,OAAA,CAAAH,eAAA,GAA2B,KAAK;IAAA,EAAC;IAAClJ,EAAA,CAAAG,YAAA,EAAQ;;;;;;IAWpLH,EAAA,CAAAC,cAAA,kBAA4D;IAKpCD,EAAA,CAAA6B,UAAA,2BAAAyH,sGAAAvH,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAuH,IAAA;MAAA,MAAAC,OAAA,GAAAxJ,EAAA,CAAAmC,aAAA;MAAA,OAAanC,EAAA,CAAAoC,WAAA,CAAAoH,OAAA,CAAAC,oBAAA,CAAAC,MAAA,GAAA3H,MAAA,CACxD;IAAA,EADoF;IAQpD/B,EAAA,CAAAG,YAAA,EAAa;IAElBH,EAAA,CAAAC,cAAA,cAAgC;IACrBD,EAAA,CAAA6B,UAAA,2BAAA8H,iGAAA5H,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAuH,IAAA;MAAA,MAAAK,OAAA,GAAA5J,EAAA,CAAAmC,aAAA;MAAA,OAAanC,EAAA,CAAAoC,WAAA,CAAAwH,OAAA,CAAAH,oBAAA,CAAAI,GAAA,GAAA9H,MAAA,CAAgC;IAAA,EAAP;IAA7C/B,EAAA,CAAAG,YAAA,EAAkJ;IAEtJH,EAAA,CAAAC,cAAA,mBAIC;IADSD,EAAA,CAAA6B,UAAA,mBAAAiI,4FAAA;MAAA9J,EAAA,CAAAgC,aAAA,CAAAuH,IAAA;MAAA,MAAAQ,OAAA,GAAA/J,EAAA,CAAAmC,aAAA;MAAA,OAASnC,EAAA,CAAAoC,WAAA,CAAA2H,OAAA,CAAAC,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC;IACzChK,EAAA,CAAAG,YAAA,EAAW;IAGhBH,EAAA,CAAAC,cAAA,qBAaC;IATOD,EAAA,CAAA6B,UAAA,+BAAAoI,0GAAAlI,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAuH,IAAA;MAAA,MAAAW,OAAA,GAAAlK,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAA8H,OAAA,CAAAC,kBAAA,GAAApI,MAAA;IAAA,EAAoC;IAS3C/B,EAAA,CAAAG,YAAA,EAAa;;;;IAtCTH,EAAA,CAAAwC,UAAA,qBAAoB;IAILxC,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAwC,UAAA,mBAAkB,YAAA4H,OAAA,CAAAX,oBAAA,CAAAC,MAAA,oBAAA1J,EAAA,CAAAU,eAAA,KAAA8F,GAAA,cAAA4D,OAAA,CAAAC,UAAA,wBAAAD,OAAA,CAAA7J,WAAA,CAAAC,SAAA,uCAAA4J,OAAA,CAAA7J,WAAA,CAAAC,SAAA;IAYvBR,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAwC,UAAA,YAAA4H,OAAA,CAAAX,oBAAA,CAAAI,GAAA,CAAsC,mBAAA7J,EAAA,CAAAU,eAAA,KAAA8F,GAAA;IAU7CxG,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAwC,UAAA,iBAAgB,eAAA4H,OAAA,CAAAE,kBAAA,CAAA3D,IAAA,cAAAyD,OAAA,CAAAE,kBAAA,CAAA1D,IAAA,iBAAAwD,OAAA,CAAAD,kBAAA,aAAAC,OAAA,CAAAG,kBAAA,aAAAH,OAAA,CAAAI,eAAA,aAAAJ,OAAA,CAAAK,mBAAA,cAAAL,OAAA,CAAAM,cAAA,CAAArG,IAAA,CAAA+F,OAAA,yBAAApK,EAAA,CAAAU,eAAA,KAAAuG,GAAA,oCAAAmD,OAAA,CAAAE,kBAAA,CAAApD,MAAA,YAAAkD,OAAA,CAAAX,oBAAA;;;;;;IAhFpCzJ,EAAA,CAAAC,cAAA,qBAAqO;IAQ7MD,EAAA,CAAA6B,UAAA,2BAAA8I,8FAAA5I,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAA4I,IAAA;MAAA,MAAAC,OAAA,GAAA7K,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAAyI,OAAA,CAAAC,cAAA,GAAA/I,MAAA;IAAA,EAA4B,qBAAAgJ,wFAAA;MAAA/K,EAAA,CAAAgC,aAAA,CAAA4I,IAAA;MAAA,MAAAI,OAAA,GAAAhL,EAAA,CAAAmC,aAAA;MAAA,OAEjBnC,EAAA,CAAAoC,WAAA,CAAA4I,OAAA,CAAAC,wBAAA,EAA0B;IAAA,EAFT;IAKpCjL,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,wBAOC;IAHOD,EAAA,CAAA6B,UAAA,2BAAAqJ,8FAAAnJ,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAA4I,IAAA;MAAA,MAAAO,OAAA,GAAAnL,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAA+I,OAAA,CAAAL,cAAA,GAAA/I,MAAA;IAAA,EAA4B,qBAAAqJ,wFAAA;MAAApL,EAAA,CAAAgC,aAAA,CAAA4I,IAAA;MAAA,MAAAS,OAAA,GAAArL,EAAA,CAAAmC,aAAA;MAAA,OACjBnC,EAAA,CAAAoC,WAAA,CAAAiJ,OAAA,CAAAJ,wBAAA,EAA0B;IAAA,EADT;IAIpCjL,EAAA,CAAAG,YAAA,EAAgB;IAEpBH,EAAA,CAAAC,cAAA,cAAkE;IAGTD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAC,cAAA,iBAAyI;IAAlID,EAAA,CAAA6B,UAAA,2BAAAyJ,uFAAAvJ,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAA4I,IAAA;MAAA,MAAAW,OAAA,GAAAvL,EAAA,CAAAmC,aAAA;MAAA,OAAanC,EAAA,CAAAoC,WAAA,CAAAmJ,OAAA,CAAAC,WAAA,CAAAC,QAAA,GAAA1J,MAAA,CAA4B;IAAA,EAAP;IAAzC/B,EAAA,CAAAG,YAAA,EAAyI;IAGjJH,EAAA,CAAAC,cAAA,eAAmB;IAEkCD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/DH,EAAA,CAAAC,cAAA,eAA4C;IAEjCD,EAAA,CAAA6B,UAAA,2BAAA6J,uFAAA3J,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAA4I,IAAA;MAAA,MAAAe,OAAA,GAAA3L,EAAA,CAAAmC,aAAA;MAAA,OAAanC,EAAA,CAAAoC,WAAA,CAAAuJ,OAAA,CAAAH,WAAA,CAAAI,SAAA,GAAA7J,MAAA,CAC3D;IAAA,EADiF;IAD1C/B,EAAA,CAAAG,YAAA,EAME;IACFH,EAAA,CAAAgD,UAAA,KAAA6I,+DAAA,oBAA0J;IAC1J7L,EAAA,CAAAgD,UAAA,KAAA8I,+DAAA,oBAAgK;IACpK9L,EAAA,CAAAG,YAAA,EAAM;IAGdH,EAAA,CAAAC,cAAA,eAA2B;IACbD,EAAA,CAAA6B,UAAA,mBAAAkK,kFAAA;MAAA/L,EAAA,CAAAgC,aAAA,CAAA4I,IAAA;MAAA,MAAAoB,OAAA,GAAAhM,EAAA,CAAAmC,aAAA;MAAA,OAASnC,EAAA,CAAAoC,WAAA,CAAA4J,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAAwFjM,EAAA,CAAAG,YAAA,EAAW;IAKjJH,EAAA,CAAAC,cAAA,WAAK;IACDD,EAAA,CAAAgD,UAAA,KAAAkJ,iEAAA,uBAuCU;IACdlM,EAAA,CAAAG,YAAA,EAAM;;;;IA9FEH,EAAA,CAAA0E,qBAAA,WAAAyH,OAAA,CAAA5L,WAAA,CAAAC,SAAA,0BAA2D;IAA0IR,EAAA,CAAAwC,UAAA,oBAAmB;IAEnNxC,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAwC,UAAA,qBAAoB;IAGbxC,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAwC,UAAA,UAAA2J,OAAA,CAAA5L,WAAA,CAAAC,SAAA,yBAAuD,YAAA2L,OAAA,CAAArB,cAAA,oBAAA9K,EAAA,CAAAU,eAAA,KAAA8F,GAAA;IAUvDxG,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAwC,UAAA,UAAA2J,OAAA,CAAA5L,WAAA,CAAAC,SAAA,4BAA0D,YAAA2L,OAAA,CAAArB,cAAA,oBAAA9K,EAAA,CAAAU,eAAA,KAAA8F,GAAA;IAanDxG,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAwC,UAAA,YAAA2J,OAAA,CAAAX,WAAA,CAAAC,QAAA,CAAkC,qCAAAzL,EAAA,CAAAU,eAAA,KAAA8F,GAAA;IAQ9BxG,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAwC,UAAA,YAAA2J,OAAA,CAAAX,WAAA,CAAAI,SAAA,CAAmC,mBAAA5L,EAAA,CAAAU,eAAA,KAAA8F,GAAA,WAAA2F,OAAA,CAAAjD,eAAA;IAMKlJ,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAwC,UAAA,SAAA2J,OAAA,CAAAjD,eAAA,UAA8B;IAC9BlJ,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAwC,UAAA,SAAA2J,OAAA,CAAAjD,eAAA,SAA6B;IAKrDlJ,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAwC,UAAA,UAAA2J,OAAA,CAAA5L,WAAA,CAAAC,SAAA,qBAAmD;IAM/DR,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAwC,UAAA,SAAA2J,OAAA,CAAAX,WAAA,CAAAI,SAAA,CAA2B;;;;;;;;;IAvblF5L,EAAA,CAAAC,cAAA,cAAuJ;IAA7HD,EAAA,CAAA6B,UAAA,2BAAAuK,sEAAArK,MAAA;MAAA,OAAiBA,MAAA,CAAAmD,cAAA,EAAuB;IAAA,EAAC,sBAAAmH,iEAAA;MAAArM,EAAA,CAAAgC,aAAA,CAAAsK,IAAA;MAAA,MAAAC,OAAA,GAAAvM,EAAA,CAAAmC,aAAA;MAAA,OAAuCnC,EAAA,CAAAoC,WAAA,CAAAmK,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAvD;IACnExM,EAAA,CAAAC,cAAA,aAAqG;IAEzDD,EAAA,CAAAE,MAAA,GAAyD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnGH,EAAA,CAAA+C,SAAA,sBAAoF;IACxF/C,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAoF;IAE6CD,EAAA,CAAA6B,UAAA,mBAAA4K,kEAAA;MAAAzM,EAAA,CAAAgC,aAAA,CAAAsK,IAAA;MAAA,MAAAI,OAAA,GAAA1M,EAAA,CAAAmC,aAAA;MAAA,OAASnC,EAAA,CAAAoC,WAAA,CAAAsK,OAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IAAC3M,EAAA,CAAAG,YAAA,EAAW;IAC1JH,EAAA,CAAA+C,SAAA,kBAC+G;IACnH/C,EAAA,CAAAG,YAAA,EAAM;IAIdH,EAAA,CAAAC,cAAA,kBAA2C;IAEpBD,EAAA,CAAA6B,UAAA,sBAAA+K,uEAAA7K,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAsK,IAAA;MAAA,MAAAO,OAAA,GAAA7M,EAAA,CAAAmC,aAAA;MAAA,OAAYnC,EAAA,CAAAoC,WAAA,CAAAyK,OAAA,CAAAC,WAAA,CAAA/K,MAAA,CAAmB;IAAA,EAAC,+BAAAgL,gFAAAhL,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAsK,IAAA;MAAA,MAAAU,OAAA,GAAAhN,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAA4K,OAAA,CAAAC,cAAA,GAAAlL,MAAA;IAAA;IACvC/B,EAAA,CAAAC,cAAA,sBAA4E;IAKOD,EAAA,CAAAE,MAAA,IAAmD;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzJH,EAAA,CAAAC,cAAA,eAAiB;IAGND,EAAA,CAAA6B,UAAA,2BAAAqL,wEAAAnL,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAsK,IAAA;MAAA,MAAAa,OAAA,GAAAnN,EAAA,CAAAmC,aAAA;MAAA,OAAanC,EAAA,CAAAoC,WAAA,CAAA+K,OAAA,CAAA9K,WAAA,CAAA+K,WAAA,GAAArL,MAAA,CACnD;IAAA,EAD2E,2BAAAmL,wEAAA;MAAAlN,EAAA,CAAAgC,aAAA,CAAAsK,IAAA;MAAA,MAAAe,OAAA,GAAArN,EAAA,CAAAmC,aAAA;MAAA,OAMpBnC,EAAA,CAAAoC,WAAA,CAAAiL,OAAA,CAAAC,iBAAA,CAAkB,aAAa,CAAC;IAAA,EANZ;IAF5CtN,EAAA,CAAAG,YAAA,EASE;IAIVH,EAAA,CAAAC,cAAA,eAAgD;IAC5CD,EAAA,CAAA+C,SAAA,iBAA2E;IAC3E/C,EAAA,CAAAC,cAAA,eAAiB;IACbD,EAAA,CAAAgD,UAAA,KAAAuK,gDAAA,oBAA4L;IAC5LvN,EAAA,CAAAgD,UAAA,KAAAwK,gDAAA,oBAA6J;IAC7JxN,EAAA,CAAAgD,UAAA,KAAAyK,gDAAA,oBAAmJ;IACnJzN,EAAA,CAAAgD,UAAA,KAAA0K,gDAAA,oBAAsL;IAC1L1N,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA+B;IACqCD,EAAA,CAAAE,MAAA,IAAmD;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtJH,EAAA,CAAAC,cAAA,eAAiB;IAGND,EAAA,CAAA6B,UAAA,2BAAA8L,wEAAA5L,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAsK,IAAA;MAAA,MAAAsB,OAAA,GAAA5N,EAAA,CAAAmC,aAAA;MAAA,OAAanC,EAAA,CAAAoC,WAAA,CAAAwL,OAAA,CAAAvL,WAAA,CAAAwL,QAAA,GAAA9L,MAAA,CACnD;IAAA,EADwE;IAFzC/B,EAAA,CAAAG,YAAA,EAQE;IAIVH,EAAA,CAAAC,cAAA,eAAgD;IAC5CD,EAAA,CAAA+C,SAAA,iBAAwE;IACxE/C,EAAA,CAAAC,cAAA,eAAiB;IACbD,EAAA,CAAAgD,UAAA,KAAA8K,gDAAA,oBAAsL;IACtL9N,EAAA,CAAAgD,UAAA,KAAA+K,gDAAA,oBAA2J;IAC3J/N,EAAA,CAAAgD,UAAA,KAAAgL,gDAAA,oBAAqJ;IACzJhO,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA+B;IACkCD,EAAA,CAAAE,MAAA,IAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrHH,EAAA,CAAAC,cAAA,eAAiB;IAGND,EAAA,CAAA6B,UAAA,2BAAAoM,wEAAAlM,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAsK,IAAA;MAAA,MAAA4B,OAAA,GAAAlO,EAAA,CAAAmC,aAAA;MAAA,OAAanC,EAAA,CAAAoC,WAAA,CAAA8L,OAAA,CAAA7L,WAAA,CAAA8L,KAAA,GAAApM,MAAA,CACnD;IAAA,EADqE;IAFtC/B,EAAA,CAAAG,YAAA,EAME;IAIVH,EAAA,CAAAC,cAAA,eAAgD;IAC5CD,EAAA,CAAA+C,SAAA,iBAAqE;IACrE/C,EAAA,CAAAC,cAAA,eAAiB;IACbD,EAAA,CAAAgD,UAAA,KAAAoL,gDAAA,oBAA+I;IAC/IpO,EAAA,CAAAgD,UAAA,KAAAqL,gDAAA,oBAAgL;IACpLrO,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA+B;IACuDD,EAAA,CAAAE,MAAA,IAAgD;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrKH,EAAA,CAAAC,cAAA,eAAiB;IAGND,EAAA,CAAA6B,UAAA,2BAAAyM,wEAAAvM,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAsK,IAAA;MAAA,MAAAiC,OAAA,GAAAvO,EAAA,CAAAmC,aAAA;MAAA,OAAanC,EAAA,CAAAoC,WAAA,CAAAmM,OAAA,CAAAlM,WAAA,CAAAmM,KAAA,GAAAzM,MAAA,CACnD;IAAA,EADqE,2BAAAuM,wEAAA;MAAAtO,EAAA,CAAAgC,aAAA,CAAAsK,IAAA;MAAA,MAAAmC,OAAA,GAAAzO,EAAA,CAAAmC,aAAA;MAAA,OAMdnC,EAAA,CAAAoC,WAAA,CAAAqM,OAAA,CAAAnB,iBAAA,CAAkB,OAAO,CAAC;IAAA,EANZ;IAFtCtN,EAAA,CAAAG,YAAA,EASE;IAIVH,EAAA,CAAAC,cAAA,eAAgD;IAC5CD,EAAA,CAAA+C,SAAA,iBAAqE;IACrE/C,EAAA,CAAAC,cAAA,eAAiB;IACbD,EAAA,CAAAgD,UAAA,KAAA0L,gDAAA,oBAAgL;IAChL1O,EAAA,CAAAgD,UAAA,KAAA2L,gDAAA,oBAAwJ;IACxJ3O,EAAA,CAAAgD,UAAA,KAAA4L,gDAAA,oBAA+I;IAC/I5O,EAAA,CAAAgD,UAAA,KAAA6L,gDAAA,oBAAgL;IACpL7O,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA+B;IACwCD,EAAA,CAAAE,MAAA,IAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjIH,EAAA,CAAAC,cAAA,eAAiB;IAKVD,EAAA,CAAA6B,UAAA,2BAAAiN,2EAAA/M,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAsK,IAAA;MAAA,MAAAyC,OAAA,GAAA/O,EAAA,CAAAmC,aAAA;MAAA,OAAanC,EAAA,CAAAoC,WAAA,CAAA2M,OAAA,CAAA1M,WAAA,CAAA2M,WAAA,GAAAjN,MAAA,CAC/C;IAAA,EADuE;IAI/C/B,EAAA,CAAAG,YAAA,EAAW;IAIZH,EAAA,CAAAC,cAAA,eAAgD;IAC5CD,EAAA,CAAA+C,SAAA,iBAA2E;IAC3E/C,EAAA,CAAAC,cAAA,eAAiB;IACbD,EAAA,CAAAgD,UAAA,KAAAiM,gDAAA,oBAA8J;IAClKjP,EAAA,CAAAG,YAAA,EAAM;IAGdH,EAAA,CAAAC,cAAA,eAAyB;IAG2CD,EAAA,CAAAE,MAAA,IAAmD;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClJH,EAAA,CAAAC,cAAA,eAAiB;IAIDD,EAAA,CAAA6B,UAAA,2BAAAqN,6EAAAnN,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAsK,IAAA;MAAA,MAAA6C,OAAA,GAAAnP,EAAA,CAAAmC,aAAA;MAAA,OAAanC,EAAA,CAAAoC,WAAA,CAAA+M,OAAA,CAAA9M,WAAA,CAAAI,QAAA,GAAAV,MAAA,CACxD;IAAA,EAD6E;IAO7C/B,EAAA,CAAAG,YAAA,EAAa;IAItBH,EAAA,CAAAC,cAAA,eAAgD;IAC5CD,EAAA,CAAA+C,SAAA,iBAAwE;IACxE/C,EAAA,CAAAC,cAAA,eAAiB;IACbD,EAAA,CAAAgD,UAAA,KAAAoM,gDAAA,oBAAsL;IAC1LpP,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAgD,UAAA,KAAAqM,8CAAA,kBAgBM;IAGNrP,EAAA,CAAAgD,UAAA,KAAAsM,8CAAA,kBAKM;IA4BNtP,EAAA,CAAAC,cAAA,eAAsL;IACrHD,EAAA,CAAAE,MAAA,IAAsD;IAAAF,EAAA,CAAAgD,UAAA,KAAAuM,+CAAA,mBAAsE;IAAAvP,EAAA,CAAAG,YAAA,EAAQ;IACjMH,EAAA,CAAAC,cAAA,eAAuD;IAI/CD,EAAA,CAAA6B,UAAA,yBAAA2N,4EAAAzN,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAsK,IAAA;MAAA,MAAAmD,OAAA,GAAAzP,EAAA,CAAAmC,aAAA;MAAA,OAAWnC,EAAA,CAAAoC,WAAA,CAAAqN,OAAA,CAAApN,WAAA,CAAAqN,OAAA,GAAA3N,MAAA,CAC9C;IAAA,EADkE,sBAAA4N,yEAAA;MAAA3P,EAAA,CAAAgC,aAAA,CAAAsK,IAAA;MAAA,MAAAsD,OAAA,GAAA5P,EAAA,CAAAmC,aAAA;MAAA,OAWnBnC,EAAA,CAAAoC,WAAA,CAAAwN,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAXG;IAYlC7P,EAAA,CAAAG,YAAA,EAAc;IAIvBH,EAAA,CAAAgD,UAAA,KAAA8M,8CAAA,kBAKM;IAEN9P,EAAA,CAAAgD,UAAA,KAAA+M,8CAAA,kBAiBM;IAEN/P,EAAA,CAAAgD,UAAA,KAAAgN,8CAAA,kBAKM;IAENhQ,EAAA,CAAAgD,UAAA,KAAAiN,8CAAA,kBAKM;IAENjQ,EAAA,CAAAC,cAAA,gBAA+B;IACkCD,EAAA,CAAAE,MAAA,KAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpHH,EAAA,CAAAC,cAAA,gBAAuD;IAI/CD,EAAA,CAAA6B,UAAA,yBAAAqO,6EAAAnO,MAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAsK,IAAA;MAAA,MAAA6D,OAAA,GAAAnQ,EAAA,CAAAmC,aAAA;MAAA,OAAWnC,EAAA,CAAAoC,WAAA,CAAA+N,OAAA,CAAA9N,WAAA,CAAA+N,OAAA,GAAArO,MAAA,CAC9C;IAAA,EADkE;IAQlC/B,EAAA,CAAAG,YAAA,EAAc;IAMnCH,EAAA,CAAAgD,UAAA,MAAAqN,sDAAA,0BAyCa;IACbrQ,EAAA,CAAAgD,UAAA,MAAAsN,sDAAA,0BAyCa;IACbtQ,EAAA,CAAAgD,UAAA,MAAAuN,sDAAA,2BA+Fa;IACjBvQ,EAAA,CAAAG,YAAA,EAAY;;;;IAje4CH,EAAA,CAAAwC,UAAA,cAAAgO,MAAA,CAAArN,WAAA,CAAyB;IAGjDnD,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAK,iBAAA,CAAAmQ,MAAA,CAAAjQ,WAAA,CAAAC,SAAA,4BAAyD;IACtDR,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAwC,UAAA,UAAAgO,MAAA,CAAAC,KAAA,CAAe,SAAAD,MAAA,CAAAE,IAAA;IAIxC1Q,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAwC,UAAA,UAAAgO,MAAA,CAAAjQ,WAAA,CAAAC,SAAA,yBAAuD;IACvDR,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAwC,UAAA,UAAAgO,MAAA,CAAAjQ,WAAA,CAAAC,SAAA,uBAAqD,aAAAgQ,MAAA,CAAArN,WAAA,CAAAwN,OAAA,IAAAH,MAAA,CAAAI,cAAA,IAAAJ,MAAA,CAAAK,cAAA,IAAAL,MAAA,CAAAM,iBAAA;IAQnB9Q,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAwC,UAAA,gBAAAgO,MAAA,CAAAvD,cAAA,CAAgC;IAC5DjN,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAA0E,qBAAA,WAAA8L,MAAA,CAAAjQ,WAAA,CAAAC,SAAA,8BAA+D;IAKQR,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAAmQ,MAAA,CAAAjQ,WAAA,CAAAC,SAAA,2BAAmD;IAI3GR,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAwC,UAAA,YAAAgO,MAAA,CAAAnO,WAAA,CAAA+K,WAAA,CAAqC,mDAAAoD,MAAA,CAAAjQ,WAAA,CAAAC,SAAA;IAcfR,EAAA,CAAAI,SAAA,GAAiG;IAAjGJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAgK,WAAA,CAAA/J,KAAA,KAAAmN,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAgK,WAAA,CAAA9J,MAAA,kBAAAkN,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAgK,WAAA,CAAA9J,MAAA,CAAAC,QAAA,EAAiG;IACjGvD,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAgK,WAAA,CAAA9J,MAAA,kBAAAkN,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAgK,WAAA,CAAA9J,MAAA,CAAAyN,SAAA,CAAwD;IACxD/Q,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAgK,WAAA,CAAA9J,MAAA,kBAAAkN,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAgK,WAAA,CAAA9J,MAAA,CAAA0N,OAAA,CAAsD;IACtDhR,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAAM,iBAAA,CAAuB;IAKQ9Q,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAAmQ,MAAA,CAAAjQ,WAAA,CAAAC,SAAA,2BAAmD;IAIxGR,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAwC,UAAA,YAAAgO,MAAA,CAAAnO,WAAA,CAAAwL,QAAA,CAAkC,oDAAA2C,MAAA,CAAAjQ,WAAA,CAAAC,SAAA;IAaZR,EAAA,CAAAI,SAAA,GAA2F;IAA3FJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAyK,QAAA,CAAAxK,KAAA,KAAAmN,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAyK,QAAA,CAAAvK,MAAA,kBAAAkN,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAyK,QAAA,CAAAvK,MAAA,CAAAC,QAAA,EAA2F;IAC3FvD,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAyK,QAAA,CAAAvK,MAAA,kBAAAkN,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAyK,QAAA,CAAAvK,MAAA,CAAAyN,SAAA,CAAqD;IACrD/Q,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAyK,QAAA,CAAAvK,MAAA,kBAAAkN,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAyK,QAAA,CAAAvK,MAAA,CAAA0N,OAAA,CAAmD;IAKvBhR,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,iBAAA,CAAAmQ,MAAA,CAAAjQ,WAAA,CAAAC,SAAA,wBAAgD;IAIlGR,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAwC,UAAA,YAAAgO,MAAA,CAAAnO,WAAA,CAAA8L,KAAA,CAA+B,gBAAAqC,MAAA,CAAAjQ,WAAA,CAAAC,SAAA;IAWTR,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAA+K,KAAA,CAAA7K,MAAA,kBAAAkN,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAA+K,KAAA,CAAA7K,MAAA,CAAA0N,OAAA,CAAgD;IAChDhR,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAAK,cAAA,CAAoB;IAK6B7Q,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,iBAAA,CAAAmQ,MAAA,CAAAjQ,WAAA,CAAAC,SAAA,wBAAgD;IAIvHR,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAwC,UAAA,YAAAgO,MAAA,CAAAnO,WAAA,CAAAmM,KAAA,CAA+B,oDAAAgC,MAAA,CAAAjQ,WAAA,CAAAC,SAAA;IAcTR,EAAA,CAAAI,SAAA,GAAqF;IAArFJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAoL,KAAA,CAAAnL,KAAA,KAAAmN,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAoL,KAAA,CAAAlL,MAAA,kBAAAkN,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAoL,KAAA,CAAAlL,MAAA,CAAAC,QAAA,EAAqF;IACrFvD,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAoL,KAAA,CAAAlL,MAAA,kBAAAkN,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAoL,KAAA,CAAAlL,MAAA,CAAAyN,SAAA,CAAkD;IAClD/Q,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAoL,KAAA,CAAAlL,MAAA,kBAAAkN,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAoL,KAAA,CAAAlL,MAAA,CAAA0N,OAAA,CAAgD;IAChDhR,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAAI,cAAA,CAAoB;IAKc5Q,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAAmQ,MAAA,CAAAjQ,WAAA,CAAAC,SAAA,8BAAsD;IAIlHR,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAwC,UAAA,qBAAoB,YAAAgO,MAAA,CAAAnO,WAAA,CAAA2M,WAAA,mCAAAwB,MAAA,CAAAjQ,WAAA,CAAAC,SAAA;IAaMR,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAA4L,WAAA,CAAA1L,MAAA,kBAAAkN,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAA4L,WAAA,CAAA1L,MAAA,CAAAyN,SAAA,CAAwD;IAO7B/Q,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAAmQ,MAAA,CAAAjQ,WAAA,CAAAC,SAAA,2BAAmD;IAG/FR,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAwC,UAAA,mBAAkB,uCAAAgO,MAAA,CAAAnO,WAAA,CAAAI,QAAA,+BAAA+N,MAAA,CAAAS,cAAA,iBAAAT,MAAA,CAAAjQ,WAAA,CAAAC,SAAA;IAgBDR,EAAA,CAAAI,SAAA,GAA2F;IAA3FJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAX,QAAA,CAAAY,KAAA,KAAAmN,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAX,QAAA,CAAAa,MAAA,kBAAAkN,MAAA,CAAArN,WAAA,CAAAC,QAAA,CAAAX,QAAA,CAAAa,MAAA,CAAAC,QAAA,EAA2F;IAIhGvD,EAAA,CAAAI,SAAA,GAAuI;IAAvIJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAA/N,QAAA,IAAA+N,MAAA,CAAA9N,cAAA,CAAAC,KAAA,IAAA6N,MAAA,CAAAnO,WAAA,CAAAI,QAAA,IAAA+N,MAAA,CAAA9N,cAAA,CAAAC,KAAA,IAAA6N,MAAA,CAAAnO,WAAA,CAAAI,QAAA,IAAA+N,MAAA,CAAA9N,cAAA,CAAAE,MAAA,CAAuI;IAmBtH5C,EAAA,CAAAI,SAAA,GAAuI;IAAvIJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAA/N,QAAA,IAAA+N,MAAA,CAAA9N,cAAA,CAAAC,KAAA,IAAA6N,MAAA,CAAAnO,WAAA,CAAAI,QAAA,IAAA+N,MAAA,CAAA9N,cAAA,CAAAC,KAAA,IAAA6N,MAAA,CAAAnO,WAAA,CAAAI,QAAA,IAAA+N,MAAA,CAAA9N,cAAA,CAAAE,MAAA,CAAuI;IAiCzJ5C,EAAA,CAAAI,SAAA,GAAsJ;IAAtJJ,EAAA,CAAAkR,UAAA,CAAAV,MAAA,CAAAnO,WAAA,CAAAI,QAAA,IAAA+N,MAAA,CAAA9N,cAAA,CAAAyO,QAAA,KAAAX,MAAA,CAAA/N,QAAA,IAAA+N,MAAA,CAAA9N,cAAA,CAAAC,KAAA,IAAA6N,MAAA,CAAA/N,QAAA,IAAA+N,MAAA,CAAA9N,cAAA,CAAA0O,QAAA,kBAAsJ;IACpHpR,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAAmQ,MAAA,CAAAjQ,WAAA,CAAAC,SAAA,8BAAsD;IAAOR,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAAnO,WAAA,CAAAgP,cAAA,CAAgC;IAGlJrR,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAwC,UAAA,YAAAgO,MAAA,CAAA7M,yBAAA,CAAqC,UAAA6M,MAAA,CAAAnO,WAAA,CAAAqN,OAAA,iBAAAc,MAAA,CAAAjQ,WAAA,CAAAC,SAAA,oEAAAgQ,MAAA,CAAAc,kBAAA,eAAAd,MAAA,CAAAnO,WAAA,CAAAgP,cAAA,cAAAb,MAAA,CAAAnO,WAAA,CAAAgP,cAAA;IAkBArR,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAAnO,WAAA,CAAAI,QAAA,IAAA+N,MAAA,CAAA9N,cAAA,CAAAyO,QAAA,CAAqD;IAOtEnR,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAAnO,WAAA,CAAAI,QAAA,IAAA+N,MAAA,CAAA9N,cAAA,CAAA6O,QAAA,CAAqD;IAmBpCvR,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAAnO,WAAA,CAAAI,QAAA,IAAA+N,MAAA,CAAA9N,cAAA,CAAA6O,QAAA,CAAqD;IAOpDvR,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAAnO,WAAA,CAAAI,QAAA,IAAA+N,MAAA,CAAA9N,cAAA,CAAAyO,QAAA,MAAAX,MAAA,CAAA7L,eAAA,kBAAA6L,MAAA,CAAA7L,eAAA,CAAA0M,cAAA,EAAyF;IAQ1ErR,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAK,iBAAA,CAAAmQ,MAAA,CAAAjQ,WAAA,CAAAC,SAAA,uBAA+C;IAIpGR,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAwC,UAAA,YAAAgO,MAAA,CAAAgB,sBAAA,CAAkC,UAAAhB,MAAA,CAAAnO,WAAA,CAAA+N,OAAA,kBAAAI,MAAA,CAAAiB,qBAAA,cAAAjB,MAAA,CAAAkB,WAAA,CAAArN,IAAA,CAAAmM,MAAA;IAemBxQ,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAAmB,kCAAA,GAA0C;IA0C3C3R,EAAA,CAAAI,SAAA,GAAiG;IAAjGJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAAmB,kCAAA,MAAAnB,MAAA,CAAA3K,kBAAA,IAAA2K,MAAA,CAAA3K,kBAAA,CAAA+L,MAAA,KAAiG;IA0CpG5R,EAAA,CAAAI,SAAA,GAAsI;IAAtIJ,EAAA,CAAAwC,UAAA,SAAAgO,MAAA,CAAAqB,WAAA,CAAA7R,EAAA,CAAAc,eAAA,KAAAgR,GAAA,EAAAtB,MAAA,CAAA1Q,SAAA,CAAAiS,WAAA,CAAAC,eAAA,CAAAC,wBAAA,MAAAzB,MAAA,CAAAnO,WAAA,CAAAI,QAAA,IAAA+N,MAAA,CAAA9N,cAAA,CAAAyO,QAAA,CAAsI;;;ADlX/N,OAAM,MAAOe,uBAAwB,SAAQrS,aAAa;EACtDsS,YAAmBC,cAA8B,EAC7BC,eAAgC,EAChCC,eAAgC,EAChCC,WAAwB,EAChCC,QAAkB;IAC1B,KAAK,CAACA,QAAQ,CAAC;IALA,KAAAJ,cAAc,GAAdA,cAAc;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IAgC/B,KAAAzB,iBAAiB,GAAY,KAAK;IAClC,KAAAF,cAAc,GAAY,KAAK;IAC/B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAA4B,WAAW,GAAkB,IAAI;IAEjC,KAAAC,2BAA2B,GAA2B;MAACC,YAAY,EAAE;IAAE,CAAC;IACxE,KAAArB,kBAAkB,GAAyC;MAACsB,IAAI,EAAE,CAAC;MAAED,YAAY,EAAE;IAAE,CAAC;IACtF,KAAAxO,0BAA0B,GAA+C;MAAC0O,SAAS,EAAE,IAAI;MAAEF,YAAY,EAAE;IAAE,CAAC;IAC5G,KAAAG,kBAAkB,GAAqB,IAAI/S,gBAAgB,EAAE;IAC7D,KAAAyR,sBAAsB,GAAqB,IAAIzR,gBAAgB,EAAE;IACjE,KAAA4D,yBAAyB,GAAsB,IAAI5D,gBAAgB,EAAE;IACrE,KAAAmE,iCAAiC,GAAsB,IAAInE,gBAAgB,EAAE;IA0B7E,KAAAsG,uBAAuB,GAAG,KAAK;IAC/B,KAAAkC,uBAAuB,GAAG,KAAK;IAC/B,KAAA9B,eAAe,GAAY,KAAK;IAChC,KAAAgC,eAAe,GAAY,KAAK;IAoBhC,KAAAS,eAAe,GAAG,IAAI;IACtB,KAAAmB,UAAU,GAAG,EAAE;IACf;IACA,KAAAF,kBAAkB,GAAe,EAAE;IAcnC,KAAAV,oBAAoB,GAAG;MAACI,GAAG,EAAG,IAAI;MAAEH,MAAM,EAAG;IAAI,CAAC;IAElD,KAAA8B,WAAW,GAAG;MAACC,QAAQ,EAAE,IAAI;MAAEG,SAAS,EAAE;IAAI,CAAC;IAE/C,KAAAqB,cAAc,GAAG,CAAC;IAElB,KAAAnC,cAAc,GAAS,IAAI;IAE3B,KAAAiI,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACD,QAAQ;IAEvC,KAAAE,oBAAoB,GAAS,EAAE;IAE/B,KAAAC,iBAAiB,GAAa,KAAK;IAqvBhB,KAAApT,SAAS,GAAGA,SAAS;EA32BxC;EAwHAqT,QAAQA,CAAA;IACJ,IAAI,CAAC,IAAI,CAACtB,WAAW,CAAC,CAAC/R,SAAS,CAACiS,WAAW,CAACqB,OAAO,CAACC,MAAM,CAAC,CAAC,EAAE;MAACC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,SAAS;;IAChG,IAAI,CAAC/Q,QAAQ,GAAG,IAAI,CAACuQ,cAAc,CAACD,QAAQ,CAACH,IAAI;IACjD,IAAI,CAACa,SAAS,GAAG,IAAI,CAACT,cAAc,CAACD,QAAQ,CAACW,EAAE;IAChD,IAAI,CAAChR,cAAc,GAAG5C,SAAS,CAAC6T,SAAS;IACzC,IAAI,CAAClD,KAAK,GAAG,CACT;MAAEmD,KAAK,EAAE,IAAI,CAACrT,WAAW,CAACC,SAAS,CAAC,yBAAyB;IAAC,CAAE,EAChE;MAAEoT,KAAK,EAAE,IAAI,CAACrT,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;MAAEqT,UAAU,EAAC;IAAW,CAAE,EACxF;MAAED,KAAK,EAAE,IAAI,CAACrT,WAAW,CAACC,SAAS,CAAC,oBAAoB;IAAC,CAAE,CAC9D;IACD,IAAI,CAACkQ,IAAI,GAAG;MAAEoD,IAAI,EAAE,YAAY;MAAED,UAAU,EAAE;IAAG,CAAE;IAEnD,IAAIE,eAAe,GAAG,CAClB;MAACC,IAAI,EAAE,IAAI,CAACzT,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAACyT,KAAK,EAACnU,SAAS,CAAC6T,SAAS,CAAChR,KAAK;MAAEuR,OAAO,EAAC,CAACpU,SAAS,CAAC6T,SAAS,CAAChR,KAAK;IAAC,CAAC;IACjI;IACA;MAACqR,IAAI,EAAE,IAAI,CAACzT,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAACyT,KAAK,EAACnU,SAAS,CAAC6T,SAAS,CAACxC,QAAQ;MAAC+C,OAAO,EAAC,CAACpU,SAAS,CAAC6T,SAAS,CAAChR,KAAK,EAAC7C,SAAS,CAAC6T,SAAS,CAACvC,QAAQ,EAACtR,SAAS,CAAC6T,SAAS,CAACpC,QAAQ,EAAEzR,SAAS,CAAC6T,SAAS,CAACxC,QAAQ;IAAC,CAAC,EAC9N;MAAC6C,IAAI,EAAE,IAAI,CAACzT,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAACyT,KAAK,EAACnU,SAAS,CAAC6T,SAAS,CAACvC,QAAQ;MAAC8C,OAAO,EAAC,CAACpU,SAAS,CAAC6T,SAAS,CAAChR,KAAK;IAAC,CAAC,EACtI;MAACqR,IAAI,EAAE,IAAI,CAACzT,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAACyT,KAAK,EAACnU,SAAS,CAAC6T,SAAS,CAACpC,QAAQ;MAAC2C,OAAO,EAAC,CAACpU,SAAS,CAAC6T,SAAS,CAAChR,KAAK,EAAC7C,SAAS,CAAC6T,SAAS,CAACvC,QAAQ;IAAC;IAClK;IAAA,CACH;;IACD,IAAI,CAACH,cAAc,GAAG8C,eAAe,CAACI,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACF,OAAO,CAACG,QAAQ,CAAC,IAAI,CAAC5R,QAAQ,CAAC,CAAC;IACtF,IAAI,CAACJ,WAAW,GAAG;MACf+K,WAAW,EAAE,IAAI;MACjBS,QAAQ,EAAE,IAAI;MACdW,KAAK,EAAE,IAAI;MACXL,KAAK,EAAE,IAAI;MACX1L,QAAQ,EAAE,IAAI,CAACwO,cAAc,CAAC,CAAC,CAAC,CAACgD,KAAK;MACtC3R,QAAQ,EAAE,IAAI;MACdgS,KAAK,EAAE,IAAI;MACXtF,WAAW,EAAE,IAAI;MACjBU,OAAO,EAAE,IAAI;MACb6E,SAAS,EAAE,IAAI;MACfvQ,gBAAgB,EAAG,IAAI;MACvBqN,cAAc,EAAG,IAAI;MACrBmD,SAAS,EAAE;KACd;IACD,IAAI,CAAClP,wBAAwB,GAAG;MAC5BC,OAAO,EAAE,IAAI;MACbkP,aAAa,EAAE,IAAI;MACnB9B,YAAY,EAAE,IAAI,CAACtQ,WAAW,CAACC,QAAQ;MACvCoS,iBAAiB,EAAE,IAAI;MACvB7B,SAAS,EAAE;KACd;IACD,IAAI,CAAChM,kBAAkB,GAAG,CACtB;MACImN,IAAI,EAAE,IAAI,CAACzT,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/DmU,GAAG,EAAE,MAAM;MACX/N,IAAI,EAAE,KAAK;MACXgO,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACId,IAAI,EAAE,IAAI,CAACzT,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/DmU,GAAG,EAAE,MAAM;MACX/N,IAAI,EAAE,KAAK;MACXgO,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IACD,IAAI,CAAChO,eAAe,GAAG;MACnBiO,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD,IAAI,CAACtO,kBAAkB,GAAG;MACtBC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRM,MAAM,EAAE;KACX;IACD,IAAI,CAACoD,kBAAkB,GAAG;MACtB3D,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRM,MAAM,EAAE;KACX;IACD,IAAI,CAACH,mBAAmB,GAAG;MACvBkO,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;IACD,IAAI,CAACvP,kBAAkB,GAAG,EAAE;IAE5B,IAAI,CAAC4B,wBAAwB,GAAG;MAC5BlC,OAAO,EAAE,IAAI;MACb8P,WAAW,EAAE,EAAE;MACfZ,aAAa,EAAE,CAAC;KACnB;IACD,IAAI,CAAC9L,kBAAkB,GAAG,CACtB;MACIqL,IAAI,EAAE,IAAI,CAACzT,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/DmU,GAAG,EAAE,cAAc;MACnB/N,IAAI,EAAE,KAAK;MACXgO,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACId,IAAI,EAAE,IAAI,CAACzT,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/DmU,GAAG,EAAE,cAAc;MACnB/N,IAAI,EAAE,KAAK;MACXgO,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACId,IAAI,EAAE,IAAI,CAACzT,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/DmU,GAAG,EAAE,cAAc;MACnB/N,IAAI,EAAE,KAAK;MACXgO,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IAED,IAAI,CAACvK,kBAAkB,GAAG,CACtB;MACIyJ,IAAI,EAAE,KAAK;MACXW,GAAG,EAAE,MAAM;MACX/N,IAAI,EAAE,KAAK;MACXgO,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACId,IAAI,EAAE,QAAQ;MACdW,GAAG,EAAE,QAAQ;MACb/N,IAAI,EAAE,KAAK;MACXgO,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IACD,IAAI,CAAClM,eAAe,GAAG;MACnBmM,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD,IAAI,CAACxK,eAAe,GAAG;MACnBuK,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD,IAAI,CAACtM,kBAAkB,GAAG;MACtB/B,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRM,MAAM,EAAE;KACX;IACD,IAAI,CAACuD,mBAAmB,GAAG;MACvBwK,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;IACD,IAAI,CAACvM,mBAAmB,GAAG;MACvBoM,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;IACD,IAAI,CAACrN,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACuN,mBAAmB,GAAG,IAAIC,GAAG,EAAU;IAC5C,IAAI,CAACC,eAAe,EAAE;EAC1B;EAEAC,qBAAqBA,CAAA;IACjB,IAAG,IAAI,CAACpT,WAAW,CAACI,QAAQ,IAAI,IAAI,CAACgQ,WAAW,IAAI,IAAI,CAACtP,WAAW,EAAC;MACjE,IAAI,CAACsP,WAAW,GAAG,IAAI,CAACpQ,WAAW,CAACI,QAAQ;MAC5C,IAAI,CAACU,WAAW,CAACuS,GAAG,CAAC,UAAU,CAAC,CAACC,KAAK,EAAE;MACxC,IAAI,CAACxS,WAAW,CAACuS,GAAG,CAAC,WAAW,CAAC,CAACC,KAAK,EAAE;;EAEjD;EAEArI,iBAAiBA,CAACsF,IAAI;IAClB,IAAIpE,KAAK,GAAG,IAAI;IAChB,IAAI3J,QAAQ,GAAG,IAAI;IACnB,IAAG+N,IAAI,IAAI,aAAa,EAAC;MACrB,IAAI,CAAC9B,iBAAiB,GAAG,KAAK;MAC9BjM,QAAQ,GAAG,IAAI,CAACxC,WAAW,CAAC+K,WAAW;MACvC,IAAGvI,QAAQ,IAAI,IAAI,CAACF,eAAe,CAACE,QAAQ,EAAE;KACjD,MAAK,IAAG+N,IAAI,IAAI,OAAO,EAAC;MACrB,IAAI,CAAChC,cAAc,GAAG,KAAK;MAC3BpC,KAAK,GAAG,IAAI,CAACnM,WAAW,CAACmM,KAAK;MAC9B,IAAGA,KAAK,IAAI,IAAI,CAAC7J,eAAe,CAAC6J,KAAK,EAAE;;IAG5C,IAAIoH,EAAE,GAAG,IAAI;IAEb,IAAI,CAACC,eAAe,CAACC,GAAG,CAAClD,IAAI,EAAE,IAAI,CAACR,cAAc,CAAC2D,YAAY,CAAC1R,IAAI,CAAC,IAAI,CAAC+N,cAAc,CAAC,EAAE5D,KAAK,EAAE3J,QAAQ,EAAEmR,QAAQ,IAAG;MACnH,IAAGA,QAAQ,IAAI,CAAC,EAAC;QACb,IAAGpD,IAAI,IAAI,aAAa,EAAC;UACrBgD,EAAE,CAAC9E,iBAAiB,GAAG,IAAI;SAC9B,MAAI;UACD8E,EAAE,CAAChF,cAAc,GAAG,IAAI;;;IAGpC,CAAC,CAAC;EACN;EAEApE,cAAcA,CAAA;IACV,IAAIoJ,EAAE,GAAG,IAAI;IACb,IAAG,IAAI,CAACvT,WAAW,CAACI,QAAQ,IAAI3C,SAAS,CAAC6T,SAAS,CAACxC,QAAQ,IAAI,IAAI,CAAC9O,WAAW,CAACqN,OAAO,IAAI,IAAI,EAAE;MAC9F,IAAI,CAAC/L,yBAAyB,CAACN,KAAK,GAAG,IAAI;MAC3C,IAAI,CAAC4J,cAAc,GAAG,CAAC;MACvB2I,EAAE,CAACK,oBAAoB,CAACC,OAAO,CAACN,EAAE,CAACrV,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC,CAAC;MAC5F;;IAEJ,IAAGoV,EAAE,CAACvT,WAAW,CAACI,QAAQ,IAAI3C,SAAS,CAAC6T,SAAS,CAACxC,QAAQ,IAAIyE,EAAE,CAAC/P,kBAAkB,CAAC+L,MAAM,IAAI,CAAC,EAAE;MAC7FgE,EAAE,CAACK,oBAAoB,CAACC,OAAO,CAACN,EAAE,CAACrV,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC,CAAC;MAC7F;;IAEJ,IAAI2V,eAAe,GAAG,IAAI,CAACC,eAAe;IAC1C,IAAIC,eAAe,GAAG,CAAC,IAAI,CAAChU,WAAW,CAACmS,SAAS,IAAG,EAAE,EAAE8B,GAAG,CAACC,QAAQ,IAAIA,QAAQ,CAAC7C,EAAE,CAAC;IACpF,IAAI8C,YAAY,GAAI,CAAC,IAAI,CAAC3Q,kBAAkB,IAAG,EAAE,EAAEyQ,GAAG,CAACG,QAAQ,IAAIA,QAAQ,CAAC/C,EAAE,CAAC,CAAES,MAAM,CAACC,EAAE,IAAI,CAAC+B,eAAe,CAAC9B,QAAQ,CAACD,EAAE,CAAC,CAAC,CAACkC,GAAG,CAAClC,EAAE,IAAG;MAClI,OAAO;QACHsC,UAAU,EAAEtC,EAAE;QACdxB,IAAI,EAAE;OACT;IACL,CAAC,CAAC;IACF,IAAI+D,cAAc,GAAGR,eAAe,CAAChC,MAAM,CAACC,EAAE,IAAI,CAAE,CAAC,IAAI,CAACvO,kBAAkB,IAAG,EAAE,EAAEyQ,GAAG,CAACG,QAAQ,IAAIA,QAAQ,CAAC/C,EAAE,CAAC,CAAEW,QAAQ,CAACD,EAAE,CAAC,CAAC,CAACkC,GAAG,CAAClC,EAAE,IAAG;MACpI,OAAO;QACHsC,UAAU,EAAEtC,EAAE;QACdxB,IAAI,EAAE,CAAC;OACV;IACL,CAAC,CAAC;IAEF,IAAIgE,YAAY,GAAI,CAAC,IAAI,CAAC7O,kBAAkB,IAAG,EAAE,EAAEuO,GAAG,CAACC,QAAQ,IAAIA,QAAQ,CAAC7C,EAAE,CAAC,CAAES,MAAM,CAACC,EAAE,IAAI,CAACiC,eAAe,CAAChC,QAAQ,CAACD,EAAE,CAAC,CAAC,CAACkC,GAAG,CAAClC,EAAE,IAAG;MAClI,OAAO;QACHyC,UAAU,EAAEzC,EAAE;QACdxB,IAAI,EAAE;OACT;IACL,CAAC,CAAC;IACF,IAAIkE,cAAc,GAAGT,eAAe,CAAClC,MAAM,CAACC,EAAE,IAAI,CAAE,CAAC,IAAI,CAACrM,kBAAkB,IAAG,EAAE,EAAEuO,GAAG,CAACC,QAAQ,IAAIA,QAAQ,CAAC7C,EAAE,CAAC,CAAEW,QAAQ,CAACD,EAAE,CAAC,CAAC,CAACkC,GAAG,CAAClC,EAAE,IAAG;MACpI,OAAO;QACHyC,UAAU,EAAEzC,EAAE;QACdxB,IAAI,EAAE,CAAC;OACV;IACL,CAAC,CAAC;IAEF,IAAImE,cAAc,GAAG,CAAC,GAAGP,YAAY,EAAE,GAAGG,cAAc,CAAC;IACzD,IAAIK,cAAc,GAAG,CAAC,GAAGJ,YAAY,EAAC,GAAGE,cAAc,CAAC;IACxD,IAAG,IAAI,CAACzU,WAAW,CAACI,QAAQ,IAAI3C,SAAS,CAAC6T,SAAS,CAACxC,QAAQ,EAAE;MAC1D,IAAI,CAACrG,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACX,kBAAkB,GAAG,EAAE;;IAEhC,IAAI8M,QAAQ,GAAG;MACXpS,QAAQ,EAAE,IAAI,CAACxC,WAAW,CAAC+K,WAAW;MACtCS,QAAQ,EAAE,IAAI,CAACxL,WAAW,CAACwL,QAAQ;MACnCmB,WAAW,EAAE,IAAI,CAAC3M,WAAW,CAAC2M,WAAW;MACzCR,KAAK,EAAE,IAAI,CAACnM,WAAW,CAACmM,KAAK;MAC7BL,KAAK,EAAE,IAAI,CAAC9L,WAAW,CAAC8L,KAAK;MAC7ByE,IAAI,EAAE,IAAI,CAACvQ,WAAW,CAACI,QAAQ;MAC/BkQ,YAAY,EAAE,IAAI,CAACtQ,WAAW,CAACC,QAAQ;MACvC;MACA8N,OAAO,EAAE,IAAI,CAAC/N,WAAW,CAAC+N,OAAO;MACjC8G,WAAW,EAAEH,cAAc;MAC3B;MACAI,SAAS,EAAE,IAAI,CAAC9U,WAAW,CAACqN,OAAO,IAAI,IAAI;MAC3C0H,gBAAgB,EAAG,IAAI,CAAC/U,WAAW,CAAC2B,gBAAgB,IAAI,EAAG;MAC3DqT,WAAW,EAAEL,cAAc;MAC3BM,SAAS,EAAE,IAAI,CAACxM,cAAc;MAC9ByM,SAAS,EAAE,CAAC,IAAI,CAACpN,kBAAkB,IAAI,EAAE,EAAEmM,GAAG,CAAClC,EAAE,IAAEA,EAAE,CAACV,EAAE,CAAC;MACzD8D,QAAQ,EAAG,IAAI,CAAChM,WAAW,CAACI,SAAS;MACrCsH,iBAAiB,EAAG,IAAI,CAACA;KAC5B;IACD,IAAG+D,QAAQ,CAAC9I,KAAK,IAAI,IAAI,EAAC;MACtB,IAAG8I,QAAQ,CAAC9I,KAAK,CAACsJ,UAAU,CAAC,GAAG,CAAC,EAAC;QAC9BR,QAAQ,CAAC9I,KAAK,GAAG,IAAI,GAAC8I,QAAQ,CAAC9I,KAAK,CAACuJ,SAAS,CAAC,CAAC,EAAET,QAAQ,CAAC9I,KAAK,CAACyD,MAAM,CAAC;OAC3E,MAAK,IAAGqF,QAAQ,CAAC9I,KAAK,CAACyD,MAAM,IAAI,CAAC,IAAIqF,QAAQ,CAAC9I,KAAK,CAACyD,MAAM,IAAI,EAAE,EAAC;QAC/DqF,QAAQ,CAAC9I,KAAK,GAAG,IAAI,GAAC8I,QAAQ,CAAC9I,KAAK;;;IAG5C,IAAI,CAAC8H,oBAAoB,CAAC0B,MAAM,EAAE;IAClC,IAAI,CAACvF,cAAc,CAACwF,aAAa,CAAC,IAAI,CAACjT,eAAe,CAAC+O,EAAE,EAAEuD,QAAQ,EAAGjB,QAAQ,IAAG;MAC7EJ,EAAE,CAACK,oBAAoB,CAAC4B,OAAO,CAACjC,EAAE,CAACrV,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;MACvFoV,EAAE,CAACkC,MAAM,CAACC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;IACtC,CAAC,EAAE,IAAI,EAAE,MAAI;MACTnC,EAAE,CAACK,oBAAoB,CAAC+B,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEArL,SAASA,CAAA;IACL,IAAI,CAACmL,MAAM,CAACC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACvC;EAEAE,oBAAoBA,CAAA;IAChB,IAAIrC,EAAE,GAAG,IAAI;IACb,IAAI,CAACxD,cAAc,CAAC8F,WAAW,CAAGlC,QAAQ,IAAG;MACzCJ,EAAE,CAAC3C,oBAAoB,GAAG+C,QAAQ;MAClC,IAAG,IAAI,CAAC3T,WAAW,CAACI,QAAQ,IAAI3C,SAAS,CAAC6T,SAAS,CAACxC,QAAQ,EAAE;QAC1D,IAAG,IAAI,CAAC3F,WAAW,CAACI,SAAS,IAAI,IAAI,EAAE;UACnCgK,EAAE,CAAClL,cAAc,CAACkL,EAAE,CAACtL,kBAAkB,CAAC3D,IAAI,EAAEiP,EAAE,CAACtL,kBAAkB,CAAC1D,IAAI,EAAEgP,EAAE,CAACtL,kBAAkB,CAACpD,MAAM,EAAE0O,EAAE,CAACnM,oBAAoB,CAAC;;;IAG5I,CAAC,EAAC,IAAI,EAAE,MAAI;MACRmM,EAAE,CAACK,oBAAoB,CAAC+B,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAG,SAASA,CAACC,UAAA,GAAsB,IAAI;IAChC,IAAIxC,EAAE,GAAG,IAAI;IACb,IAAIyC,SAAS,GAAG,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACC,QAAQ,CAAC9C,GAAG,CAAC,IAAI,CAAC;IACtDE,EAAE,CAACK,oBAAoB,CAAC0B,MAAM,EAAE;IAChC,IAAI,CAACvF,cAAc,CAACqG,OAAO,CAACC,QAAQ,CAACL,SAAS,CAAC,EAAGrC,QAAQ,IAAG;MACzDJ,EAAE,CAACjR,eAAe,GAAGqR,QAAQ;MAC7BJ,EAAE,CAACQ,eAAe,GAAG,CAACJ,QAAQ,CAACzB,SAAS,IAAG,EAAE,EAAE+B,GAAG,CAACG,QAAQ,IAAIA,QAAQ,CAACC,UAAU,CAAC;MACnFd,EAAE,CAACvT,WAAW,CAAC+K,WAAW,GAAG4I,QAAQ,CAACnR,QAAQ;MAC9C+Q,EAAE,CAACvT,WAAW,CAACwL,QAAQ,GAAGmI,QAAQ,CAACnI,QAAQ;MAC3C+H,EAAE,CAACvT,WAAW,CAACmM,KAAK,GAAGwH,QAAQ,CAACxH,KAAK;MACrCoH,EAAE,CAACvT,WAAW,CAAC2M,WAAW,GAAGgH,QAAQ,CAAChH,WAAW;MACjD4G,EAAE,CAACvT,WAAW,CAAC8L,KAAK,GAAG6H,QAAQ,CAAC7H,KAAK;MACrCyH,EAAE,CAACvT,WAAW,CAACC,QAAQ,GAAG0T,QAAQ,CAACrD,YAAY;MAC/CiD,EAAE,CAACvT,WAAW,CAACI,QAAQ,GAAGuT,QAAQ,CAACpD,IAAI;MACvCgD,EAAE,CAACvT,WAAW,CAACgP,cAAc,GAAG2E,QAAQ,CAAC3E,cAAc;MACvDuE,EAAE,CAACvT,WAAW,CAACkS,SAAS,GAAG,CAACyB,QAAQ,CAACzB,SAAS,IAAG,EAAE,EAAE+B,GAAG,CAACG,QAAQ,KAAK;QAClE/C,EAAE,EAAE+C,QAAQ,CAACC,UAAU;QACvB1C,IAAI,EAAEyC,QAAQ,CAACkC,YAAY;QAC3BC,IAAI,EAAEnC,QAAQ,CAACoC;OAClB,CAAC,CAAC;MACH;MACAjD,EAAE,CAAC/P,kBAAkB,GAAG,CAACmQ,QAAQ,CAACzB,SAAS,IAAG,EAAE,EAAE+B,GAAG,CAACG,QAAQ,KAAK;QAC/D/C,EAAE,EAAE+C,QAAQ,CAACC,UAAU;QACvB1C,IAAI,EAAEyC,QAAQ,CAACkC,YAAY;QAC3BC,IAAI,EAAEnC,QAAQ,CAACoC;OAClB,CAAC,CAAC;MACHjD,EAAE,CAACvT,WAAW,CAACmS,SAAS,GAAG,CAAC,IAAIwB,QAAQ,CAACxB,SAAS,IAAI,EAAE,CAAC,CAAC;MAC1DoB,EAAE,CAAC7N,kBAAkB,GAAG,CAAC,IAAIiO,QAAQ,CAACxB,SAAS,IAAI,EAAE,CAAC,CAAC;MACvDoB,EAAE,CAACtQ,wBAAwB,CAACqN,YAAY,GAAGiD,EAAE,CAACvT,WAAW,CAACC,QAAQ;MAClE;MACA,IAAIsT,EAAE,CAACnT,QAAQ,IAAI3C,SAAS,CAAC6T,SAAS,CAACxC,QAAQ,EAAE;QAC7CyE,EAAE,CAACtQ,wBAAwB,CAACmP,aAAa,GAAGmB,EAAE,CAACnC,SAAS;OAC3D,MAAM;QAAE;QACLmC,EAAE,CAACtQ,wBAAwB,CAACuN,SAAS,GAAG+C,EAAE,CAACjR,eAAe,EAAE+K,OAAO,EAAEgE,EAAE,GAAGkC,EAAE,CAACjR,eAAe,CAAC+K,OAAO,CAACgE,EAAE,GAAG,CAAC,CAAC;QAC5G,IAAIkC,EAAE,CAACjR,eAAe,CAACC,WAAW,EAAE;UAAE;UAClCgR,EAAE,CAACtQ,wBAAwB,CAACmP,aAAa,GAAGmB,EAAE,CAACjR,eAAe,CAACmU,QAAQ;UACvElD,EAAE,CAACnO,wBAAwB,CAACgN,aAAa,GAAGmB,EAAE,CAACjR,eAAe,CAACmU,QAAQ;SAC1E,MAAM;UAAE;UACLlD,EAAE,CAACtQ,wBAAwB,CAACoP,iBAAiB,GAAGkB,EAAE,CAACjR,eAAe,CAAC+O,EAAE;;;MAI7E;MACAkC,EAAE,CAACnE,qBAAqB,GAAG;QACvBmB,IAAI,EAAEgD,EAAE,CAACvT,WAAW,CAACI,QAAQ;QAC7BgS,aAAa,EAAEmB,EAAE,CAACvT,WAAW,CAACI,QAAQ,IAAI3C,SAAS,CAAC6T,SAAS,CAACxC,QAAQ,IAAI,CAACyE,EAAE,CAACvT,WAAW,CAACgP,cAAc,GAAGuE,EAAE,CAACjR,eAAe,CAACC,WAAW,CAAC8O,EAAE,GAAG,CAAC;OACnJ;MACDkC,EAAE,CAACzS,WAAW,GAAGyS,EAAE,CAACrD,WAAW,CAACwG,KAAK,CAACnD,EAAE,CAACvT,WAAW,CAAC;MACrDuT,EAAE,CAACzS,WAAW,CAACC,QAAQ,CAACgK,WAAW,CAAC4L,OAAO,EAAE;MAC7CpD,EAAE,CAACzS,WAAW,CAACC,QAAQ,CAACX,QAAQ,CAACuW,OAAO,EAAE;MAC1C,IAAGpD,EAAE,CAACvT,WAAW,CAACI,QAAQ,IAAK3C,SAAS,CAAC6T,SAAS,CAACpC,QAAQ,EAAE;QACzDqE,EAAE,CAACzS,WAAW,CAACC,QAAQ,CAACd,QAAQ,CAAC0W,OAAO,EAAE;;MAE9CpD,EAAE,CAAClD,2BAA2B,GAAG;QAACC,YAAY,EAAEiD,EAAE,CAACvT,WAAW,CAACC;MAAQ,CAAC;MACxE;MACA;MACA,IAAG0T,QAAQ,CAACpD,IAAI,IAAI9S,SAAS,CAAC6T,SAAS,CAACxC,QAAQ,EAAC;QAC7CyE,EAAE,CAACzS,WAAW,CAACC,QAAQ,CAACd,QAAQ,CAAC0W,OAAO,EAAE;;MAE9CpD,EAAE,CAACvT,WAAW,CAAC+N,OAAO,GAAG,CAAC4F,QAAQ,CAAC1B,KAAK,IAAI,EAAE,EAAEgC,GAAG,CAAClC,EAAE,IAAIA,EAAE,CAAC6E,MAAM,CAAC;MACpErD,EAAE,CAAC9K,cAAc,GAAGkL,QAAQ,CAACsB,SAAS;MACtC1B,EAAE,CAACzL,kBAAkB,GAAG,CAAC6L,QAAQ,CAACuB,SAAS,IAAI,EAAE,EAAEjB,GAAG,CAAClC,EAAE,KAAI;QAACV,EAAE,EAAEU;MAAE,CAAC,CAAC,CAAC;MACvEwB,EAAE,CAACpK,WAAW,CAACI,SAAS,GAAGoK,QAAQ,CAACwB,QAAQ;MAC5C5B,EAAE,CAACpK,WAAW,CAACC,QAAQ,GAAGuK,QAAQ,CAACnR,QAAQ;MAC3C,IAAI,CAACoT,oBAAoB,EAAE;MAC3B,IAAGG,UAAU,EAAC;QACVxC,EAAE,CAACsD,eAAe,CAAC,KAAK,CAAC;;IAEjC,CAAC,EAAE,IAAI,EAAE,MAAI;MACT;IAAA,CACH,CAAC;EACN;EAEAxH,WAAWA,CAACyH,IAAK,EAAEC,QAAS;IACxB,IAAIxD,EAAE,GAAG,IAAI;IACbyD,OAAO,CAACC,GAAG,CAACH,IAAI,CAAC;IACjB,IAAG,IAAI,CAAC9W,WAAW,CAACI,QAAQ,IAAI,IAAI,CAACgP,qBAAqB,CAACmB,IAAI,EAAC;MAC5D,IAAI,CAACR,cAAc,CAACV,WAAW,CAACyH,IAAI,EAAGnD,QAAQ,IAAG;QAC9C,IAAGoD,QAAQ,EAAC;UACR,IAAIG,YAAY,GAAG;YACfxE,OAAO,EAAGiB,QAAQ,IAAI,EAAG;YACzBwD,UAAU,EAAE,CAAC;YACbC,aAAa,EAAE,CAACzD,QAAQ,IAAI,EAAE,EAAEpE;WACnC;UACDwH,QAAQ,CAACG,YAAY,CAAC;;MAE9B,CAAC,CAAC;KACL,MAAI;MACD,IAAI,CAAC9H,qBAAqB,GAAG;QACzBmB,IAAI,EAAE,IAAI,CAACvQ,WAAW,CAACI,QAAQ;QAC/BgS,aAAa,EAAE,CAAC;OACnB;;EAET;EAEAyE,eAAeA,CAACQ,OAAA,GAAkB,IAAI,EAAE1F,IAAA,GAAY,EAAE;IAClD,IAAG,IAAI,CAAC3R,WAAW,CAACI,QAAQ,IAAI,IAAI,CAACC,cAAc,CAACyO,QAAQ,EAAE;MAC1D,IAAGuI,OAAO,EAAC;QACP,IAAI,CAACrX,WAAW,CAACkS,SAAS,GAAG,IAAI;OACpC,MAAI;QACD,IAAG,IAAI,CAAC5P,eAAe,CAAC4P,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC5P,eAAe,CAAC4P,SAAS,CAAC3C,MAAM,GAAG,CAAC,EAAC;UACnF;QAAA,CACH,MAAI;UACD,IAAI,CAACvP,WAAW,CAACkS,SAAS,GAAG,IAAI;;QAErC;QACA,IAAG,IAAI,CAAC5P,eAAe,CAAC+K,OAAO,IAAI,IAAI,EAAC;UACpC,IAAI,CAACrN,WAAW,CAACqN,OAAO,GAAG,IAAI,CAAC/K,eAAe,CAAC+K,OAAO,CAACgE,EAAE;SAC7D,MAAI;UACD,IAAI,CAACrR,WAAW,CAACqN,OAAO,GAAG,IAAI;;;MAGvC,IAAIkG,EAAE,GAAG,IAAI;MACb,IAAI,CAAClD,2BAA2B,GAAG;QAACC,YAAY,EAAE,IAAI,CAACtQ,WAAW,CAACC;MAAQ,CAAC;MAC5E,IAAI,CAACgP,kBAAkB,GAAG;QAACsB,IAAI,EAAE,CAAC;QAAED,YAAY,EAAE,IAAI,CAACtQ,WAAW,CAACC;MAAQ,CAAC;MAC5E,IAAG,IAAI,CAACD,WAAW,CAACI,QAAQ,IAAI3C,SAAS,CAAC6T,SAAS,CAACxC,QAAQ,EAAC;QACzD,IAAI,CAACuB,2BAA2B,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC/N,eAAe,CAAC,IAAI,CAAC;;;IAGjF;IACA,IAAG,IAAI,CAACtC,WAAW,CAACI,QAAQ,IAAI,IAAI,CAACC,cAAc,CAAC6O,QAAQ,EAAE;MAC1D,IAAGmI,OAAO,EAAC;QACP,IAAI,CAACrX,WAAW,CAACqN,OAAO,GAAG,IAAI;OAClC,MAAK;QACF;QACA,IAAG,IAAI,CAAC/K,eAAe,CAACgV,WAAW,IAAI,IAAI,IAAI,IAAI,CAAChV,eAAe,CAACgV,WAAW,CAAC/H,MAAM,GAAG,CAAC,EAAC;UACvF,IAAI,CAACvP,WAAW,CAAC2B,gBAAgB,GAAG,IAAI,CAACW,eAAe,CAACgV,WAAW,CAACxF,MAAM,CAACC,EAAE,IAAGA,EAAE,CAAC/C,cAAc,IAAI,IAAI,CAAC,CAACiF,GAAG,CAAClC,EAAE,IAAIA,EAAE,CAACV,EAAE,CAAC;SAC/H,MAAI;UACD,IAAI,CAACrR,WAAW,CAAC2B,gBAAgB,GAAG,IAAI;;;MAGhD,IAAI,CAACG,0BAA0B,GAAG;QAAC0O,SAAS,EAAG,IAAI,CAAClO,eAAe,CAAC+O,EAAE;QAAEf,YAAY,EAAE,IAAI,CAACtQ,WAAW,CAACC;MAAQ,CAAC;;EAExH;EAEAkT,eAAeA,CAAA;IACX,IAAII,EAAE,GAAG,IAAI;IACb,IAAI,CAACxD,cAAc,CAACoD,eAAe,CAAEQ,QAAQ,IAAG;MAC5CJ,EAAE,CAAC/S,YAAY,GAAGmT,QAAQ,CAACM,GAAG,CAAClC,EAAE,IAAG;QAChC,OAAO;UACHV,EAAE,EAAEU,EAAE,CAACwE,IAAI;UACX5E,IAAI,EAAE,GAAGI,EAAE,CAACJ,IAAI,KAAKI,EAAE,CAACwE,IAAI;SAC/B;MACL,CAAC,CAAC;MACFhD,EAAE,CAACuC,SAAS,EAAE;IAClB,CAAC,CAAC;EACN;EAEA/T,mBAAmBA,CAACwV,MAAM,EAAER,QAAQ;IAChC,OAAO,IAAI,CAAChH,cAAc,CAACyH,kBAAkB,CAACD,MAAM,EAAER,QAAQ,CAAC;EACnE;EACAjU,gBAAgBA,CAAC2U,IAAK;IAClB,IAAIlE,EAAE,GAAG,IAAI;IACb,IAAGkE,IAAI,EAAE;MACLlE,EAAE,CAAClP,kBAAkB,CAACC,IAAI,GAAG,CAAC;;IAE9BiP,EAAE,CAAC5O,cAAc,CAAC4O,EAAE,CAAClP,kBAAkB,CAACC,IAAI,EAAEiP,EAAE,CAAClP,kBAAkB,CAACE,IAAI,EAAEgP,EAAE,CAAClP,kBAAkB,CAACQ,MAAM,EAAE0O,EAAE,CAACtQ,wBAAwB,CAAC;EAC5I;EACA0B,cAAcA,CAACL,IAAI,EAAEoT,KAAK,EAAEC,IAAI,EAAEJ,MAAM;IACpC,IAAIhE,EAAE,GAAG,IAAI;IACb,IAAI,CAAClP,kBAAkB,CAACC,IAAI,GAAGA,IAAI;IACnC,IAAI,CAACD,kBAAkB,CAACE,IAAI,GAAGmT,KAAK;IACpC,IAAI,CAACrT,kBAAkB,CAACQ,MAAM,GAAG8S,IAAI;IACrC,IAAIC,UAAU,GAAG;MACbtT,IAAI;MACJC,IAAI,EAAEmT,KAAK;MACXC;KACH;IACDE,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7U,wBAAwB,CAAC,CAAC8U,OAAO,CAACzF,GAAG,IAAG;MACrD,IAAG,IAAI,CAACrP,wBAAwB,CAACqP,GAAG,CAAC,IAAI,IAAI,EAAC;QAC1CsF,UAAU,CAACtF,GAAG,CAAC,GAAG,IAAI,CAACrP,wBAAwB,CAACqP,GAAG,CAAC;;IAE5D,CAAC,CAAC;IACFiB,EAAE,CAACK,oBAAoB,CAAC0B,MAAM,EAAE;IAChC;IACA;IACA,IAAI,CAACtF,eAAe,CAACgI,mBAAmB,CAACJ,UAAU,EAAE,IAAI,CAAC3U,wBAAwB,EAAE0Q,QAAQ,IAAG;MAC3FJ,EAAE,CAAC9O,eAAe,GAAG;QACjBiO,OAAO,EAAEiB,QAAQ,CAACjB,OAAO;QACzBC,KAAK,EAAEgB,QAAQ,CAACyD;OACnB;MACD,IAAG,IAAI,CAAC5T,kBAAkB,CAAC+L,MAAM,IAAEoE,QAAQ,CAACyD,aAAa,IAAIzD,QAAQ,CAACyD,aAAa,IAAI,CAAC,EAAC;QACrF,IAAI,CAACpT,uBAAuB,GAAG,IAAI;;IAE3C,CAAC,EAAE,IAAI,EAAE,MAAI;MACTuP,EAAE,CAACK,oBAAoB,CAAC+B,OAAO,EAAE;IACrC,CAAC,CAAC;IACF;EACJ;;EAEA1Q,gBAAgBA,CAACwS,IAAK;IAClB,IAAIlE,EAAE,GAAG,IAAI;IACb,IAAGkE,IAAI,EAAE;MACLlE,EAAE,CAAClN,kBAAkB,CAAC/B,IAAI,GAAG,CAAC;;IAE9BiP,EAAE,CAACnO,wBAAwB,CAAC4N,WAAW,GAAG,CAACO,EAAE,CAAC/P,kBAAkB,IAAG,EAAE,EAAEyQ,GAAG,CAACG,QAAQ,IAAIA,QAAQ,CAAC/C,EAAE,CAAC,EACnGkC,EAAE,CAAC9M,cAAc,CAAC8M,EAAE,CAAClN,kBAAkB,CAAC/B,IAAI,EAAEiP,EAAE,CAAClN,kBAAkB,CAAC9B,IAAI,EAAEgP,EAAE,CAAClN,kBAAkB,CAACxB,MAAM,EAAE0O,EAAE,CAACnO,wBAAwB,CAAC;EAC5I;EACAqB,cAAcA,CAACnC,IAAI,EAAEoT,KAAK,EAAEC,IAAI,EAAEJ,MAAM;IACpC,IAAIhE,EAAE,GAAG,IAAI;IACb,IAAI,CAAClN,kBAAkB,CAAC/B,IAAI,GAAGA,IAAI;IACnC,IAAI,CAAC+B,kBAAkB,CAAC9B,IAAI,GAAGmT,KAAK;IACpC,IAAI,CAACrR,kBAAkB,CAACxB,MAAM,GAAG8S,IAAI;IACrC,IAAIC,UAAU,GAAG;MACbtT,IAAI;MACJC,IAAI,EAAEmT,KAAK;MACXC;KACH;IACD;IACA;IACA;IACA;IACA;IACApE,EAAE,CAACK,oBAAoB,CAAC0B,MAAM,EAAE;IAChC,IAAI,CAACrF,eAAe,CAACgI,mBAAmB,CAACL,UAAU,EAAE,IAAI,CAACxS,wBAAwB,EAAEuO,QAAQ,IAAG;MAC3FJ,EAAE,CAAChN,eAAe,GAAG;QACjBmM,OAAO,EAAEiB,QAAQ,CAACjB,OAAO;QACzBC,KAAK,EAAEgB,QAAQ,CAACyD;OACnB;MACD,IAAG,IAAI,CAAC1R,kBAAkB,CAAC6J,MAAM,IAAEoE,QAAQ,CAACyD,aAAa,IAAIzD,QAAQ,CAACyD,aAAa,IAAI,CAAC,EAAC;QACrF,IAAI,CAAClR,uBAAuB,GAAG,IAAI;;MAEvC;IACJ,CAAC,EAAE,IAAI,EAAE,MAAI;MACTqN,EAAE,CAACK,oBAAoB,CAAC+B,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAlL,WAAWA,CAACyN,KAAK;IACb,MAAMC,OAAO,GAAGD,KAAK,CAACE,aAAa,CAACC,MAAM,CAACC,SAAS;IACpD,IAAI/E,EAAE,GAAG,IAAI;IACb,IAAI2E,KAAK,IAAIC,OAAO,CAACnG,QAAQ,CAAC,IAAI,CAAC9T,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC,CAAC,EAAE;MAChF;MACA;MACA;MACA;IAAA,CACH,MAAM,IAAI+Z,KAAK,IAAIC,OAAO,CAACnG,QAAQ,CAAC,IAAI,CAAC9T,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC,CAAC,EAAE;MAC1FoV,EAAE,CAACtO,gBAAgB,EAAE;KACxB,MAAM,IAAIiT,KAAK,IAAIC,OAAO,CAACnG,QAAQ,CAAC,IAAI,CAAC9T,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC,CAAC,EAAE;MAC1FoV,EAAE,CAACzQ,gBAAgB,EAAE;;EAE7B;EAEAyV,aAAaA,CAACC,CAAC;IACX,IAAIC,KAAK,GAAG,gEAAgE;IAC5E,IAAIC,KAAK,GAAG,EAAE;IACd,KAAI,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MACvBD,KAAK,IAAID,KAAK,CAACG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGL,KAAK,CAAClJ,MAAM,CAAC,CAAC;;IAE5D,OAAOmJ,KAAK;EAChB;EAEAhV,6BAA6BA,CAACwU,KAAY;IACtC;IACA,IAAI3E,EAAE,GAAG,IAAI;IACb,IAAG,IAAI,CAAC/P,kBAAkB,CAAC+L,MAAM,IAAE,IAAI,CAAC9K,eAAe,CAACkO,KAAK,EAAC;MAC1D,IAAI,CAAC3O,uBAAuB,GAAG,IAAI;KACtC,MAAI;MACD,IAAI,CAACA,uBAAuB,GAAG,KAAK;;IAExC,MAAM+U,kBAAkB,GAAG,IAAI7F,GAAG,CAAC,CAACgF,KAAK,IAAG,EAAE,EAAEjE,GAAG,CAACG,QAAQ,IAAIA,QAAQ,CAAC/C,EAAE,CAAC,CAAC;IAC7E,MAAM2H,mBAAmB,GAAG,IAAI9F,GAAG,CAAC,CAAC,IAAI,CAAClT,WAAW,CAACkS,SAAS,IAAI,EAAE,EAAE+B,GAAG,CAACG,QAAQ,IAAIA,QAAQ,CAAC/C,EAAE,CAAC,CAAC;IACpG;IACA,MAAM4H,cAAc,GAAG,CAACf,KAAK,IAAG,EAAE,EAAEpG,MAAM,CAACsC,QAAQ,IAAI,CAAC4E,mBAAmB,CAACE,GAAG,CAAC9E,QAAQ,CAAC/C,EAAE,CAAC,CAAC;IAE7F,MAAM8H,gBAAgB,GAAG,CAAC,IAAI,CAACnZ,WAAW,CAACkS,SAAS,IAAI,EAAE,EAAEJ,MAAM,CAACsC,QAAQ,IAAI,CAAC2E,kBAAkB,CAACG,GAAG,CAAC9E,QAAQ,CAAC/C,EAAE,CAAC,CAAC;IACpH,IAAI,CAAC+H,0BAA0B,CAAC,CAACH,cAAc,IAAG,EAAE,EAAEhF,GAAG,CAACG,QAAQ,IAAIA,QAAQ,CAAC/C,EAAE,CAAC,CAAC;IAEnF8H,gBAAgB,CAACpB,OAAO,CAAC3D,QAAQ,IAAG;MAChC,IAAI,CAAC1O,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB,IAAG,EAAE,EAAEoM,MAAM,CAACoC,QAAQ,IAAIA,QAAQ,CAACsC,YAAY,IAAIpC,QAAQ,CAACmC,IAAI,CAAC,IAAI,EAAE;IAC7H,CAAC,CAAC;IAEF,IAAI,CAACvW,WAAW,CAACkS,SAAS,GAAGgG,KAAK;EAEtC;EAEAtS,6BAA6BA,CAACsS,KAAY;IACtC,IAAG,IAAI,CAACxS,kBAAkB,CAAC6J,MAAM,IAAE,IAAI,CAAChJ,eAAe,CAACoM,KAAK,EAAC;MAC1D,IAAI,CAACzM,uBAAuB,GAAG,IAAI;KACtC,MAAI;MACD,IAAI,CAACA,uBAAuB,GAAG,KAAK;;EAE5C;EACAkT,0BAA0BA,CAACpG,WAAqB;IAE5C,IAAIO,EAAE,GAAG,IAAI;IACb,IAAI,CAACK,oBAAoB,CAAC0B,MAAM,EAAE;IAClC,IAAI,CAACjP,kBAAkB,CAAC/B,IAAI,GAAG,CAAC;IAChC,IAAIsT,UAAU,GAAG;MACbtT,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,OAAO;MACboT,IAAI,EAAE,IAAI,CAACtR,kBAAkB,CAACxB;KACjC;IACD,IAAI,CAACoL,eAAe,CAACgI,mBAAmB,CAACL,UAAU,EAC/C;MACI1U,OAAO,EAAE,IAAI;MACboN,YAAY,EAAE,IAAI,CAACtQ,WAAW,CAACC,QAAQ;MACvC+S,WAAW,EAAEA;KAChB,EAAGqG,GAAG,IAAI;MACP,IAAIA,GAAG,CAACjC,aAAa,GAAG,CAAC,EAAE;QACvB,MAAMkC,YAAY,GAAG,CAACD,GAAG,CAAC3G,OAAO,IAAI,EAAE,EAAEZ,MAAM,CAACoC,QAAQ,IAAI,CAAC,IAAI,CAACjB,mBAAmB,CAACiG,GAAG,CAAChF,QAAQ,CAACqF,YAAY,CAAC,IAC5G,CAAC,IAAI,CAAC7T,kBAAkB,CAAC8T,IAAI,CAACC,gBAAgB,IAAIA,gBAAgB,CAACF,YAAY,KAAKrF,QAAQ,CAACqF,YAAY,CAAC,CAAC;QAC/G,IAAI,CAAC7T,kBAAkB,CAACgU,IAAI,CAAC,GAAGJ,YAAY,CAAC;;IAErD,CAAC,EAAE,IAAI,EAAE,MAAK;MACV/F,EAAE,CAACK,oBAAoB,CAAC+B,OAAO,EAAE;IACrC,CAAC,CAAC;EACV;EAEArG,kCAAkCA,CAAA;IAC9B,IAAIiE,EAAE,GAAG,IAAI;IACb,IAAIA,EAAE,CAACvT,WAAW,CAACI,QAAQ,IAAI3C,SAAS,CAAC6T,SAAS,CAACxC,QAAQ,EAAG,OAAO,KAAK;IAC1E;IACA;IACA;IACA;IACA;IACA;IACA,OAAQ,IAAI;EAChB;EAEAjL,8BAA8BA,CAAA;IAC1B;IACA,IAAI0P,EAAE,GAAG,IAAI;IACb,IAAIgE,MAAM,GAAG;MACTjT,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,UAAU;MAChBoT,IAAI,EAAE;KACT;IACDE,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7U,wBAAwB,CAAC,CAAC8U,OAAO,CAACzF,GAAG,IAAG;MACrD,IAAG,IAAI,CAACrP,wBAAwB,CAACqP,GAAG,CAAC,IAAI,IAAI,EAAC;QAC1CiF,MAAM,CAACjF,GAAG,CAAC,GAAG,IAAI,CAACrP,wBAAwB,CAACqP,GAAG,CAAC;;IAExD,CAAC,CAAC;IACF,IAAI,CAAClO,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC4L,eAAe,CAACgI,mBAAmB,CAACT,MAAM,EAAC,IAAI,CAACtU,wBAAwB,EAAE0Q,QAAQ,IAAG;MACtF,IAAG,IAAI,CAACnQ,kBAAkB,CAAC+L,MAAM,IAAIoE,QAAQ,CAACyD,aAAa,EAAC;QACxD,IAAI,CAAC5T,kBAAkB,GAAG,EAAE;QAC5B,IAAI,CAACQ,uBAAuB,GAAG,KAAK;QACpC;;MAEJ,IAAI,CAACR,kBAAkB,GAAGmQ,QAAQ,CAACjB,OAAO;MAC1C,IAAI,CAAC1O,uBAAuB,GAAG,IAAI;IACvC,CAAC,EAAC,IAAI,EAAC,MAAI;MAAE,IAAI,CAACI,eAAe,GAAG,KAAK;IAAC,CAAC,CAAC;EAChD;EAEA2B,8BAA8BA,CAAA;IAC1B;IACA,IAAIwN,EAAE,GAAG,IAAI;IACb,IAAIgE,MAAM,GAAG;MACTjT,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,UAAU;MAChBoT,IAAI,EAAE;KACT;IACD,IAAI,CAACvR,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC6J,eAAe,CAACgI,mBAAmB,CAACV,MAAM,EAAE,IAAI,CAACnS,wBAAwB,EAAEuO,QAAQ,IAAG;MACvF,IAAG,IAAI,CAACjO,kBAAkB,CAAC6J,MAAM,IAAIoE,QAAQ,CAACyD,aAAa,EAAC;QACxD,IAAI,CAAC1R,kBAAkB,GAAG,EAAE;QAC5B,IAAI,CAACQ,uBAAuB,GAAG,KAAK;QACpC;;MAEJ,IAAI,CAACR,kBAAkB,GAAGiO,QAAQ,CAACjB,OAAO;MAC1C,IAAI,CAACxM,uBAAuB,GAAG,IAAI;IACvC,CAAC,EAAE,IAAI,EAAE,MAAI;MACT,IAAI,CAACE,eAAe,GAAG,KAAK;IAChC,CAAC,CAAC;EACN;EAEAoH,cAAcA,CAAA;IACV,IAAI+F,EAAE,GAAG,IAAI;IACb,IAAIA,EAAE,CAACvT,WAAW,CAACqN,OAAO,EAAE;MACxBkG,EAAE,CAACtQ,wBAAwB,CAACuN,SAAS,GAAG+C,EAAE,CAACvT,WAAW,CAACqN,OAAO;KACjE,MAAM;MACHkG,EAAE,CAACzR,0BAA0B,CAAC0O,SAAS,GAAG,CAAC,CAAC;;IAEhD+C,EAAE,CAAClP,kBAAkB,CAACC,IAAI,GAAG,CAAC;IAC9BiP,EAAE,CAAClN,kBAAkB,CAAC/B,IAAI,GAAG,CAAC;EAClC;EAEA+D,cAAcA,CAAC/D,IAAI,EAAEoT,KAAK,EAAEC,IAAI,EAAEJ,MAAM;IACpC,IAAIhE,EAAE,GAAG,IAAI;IACb,IAAI,CAACtL,kBAAkB,CAAC3D,IAAI,GAAGA,IAAI;IACnC,IAAI,CAAC2D,kBAAkB,CAAC1D,IAAI,GAAGmT,KAAK;IACpC,IAAI,CAACzP,kBAAkB,CAACpD,MAAM,GAAG8S,IAAI;IACrC,IAAIC,UAAU,GAAG;MACbtT,IAAI;MACJC,IAAI,EAAEmT,KAAK;MACXC;KACH;IACDE,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1Q,oBAAoB,CAAC,CAAC2Q,OAAO,CAACzF,GAAG,IAAG;MACjD,IAAG,IAAI,CAAClL,oBAAoB,CAACkL,GAAG,CAAC,IAAI,IAAI,EAAC;QACtCsF,UAAU,CAACtF,GAAG,CAAC,GAAG,IAAI,CAAClL,oBAAoB,CAACkL,GAAG,CAAC;;IAExD,CAAC,CAAC;IACFiB,EAAE,CAACK,oBAAoB,CAAC0B,MAAM,EAAE;IAChC,IAAG,IAAI,CAAChT,eAAe,CAACmU,QAAQ,EAAE;MAC9BmB,UAAU,CAAC,oBAAoB,CAAC,GAAG,IAAI,CAACtV,eAAe,CAACmU,QAAQ;MAChE,IAAI,CAAC1G,cAAc,CAAC4J,WAAW,CAAC/B,UAAU,EAAEjE,QAAQ,IAAG;QACnDJ,EAAE,CAACpL,eAAe,GAAG;UACjBuK,OAAO,EAAEiB,QAAQ,CAACjB,OAAO;UACzBC,KAAK,EAAEgB,QAAQ,CAACyD;SACnB;MACL,CAAC,EAAE,IAAI,EAAE,MAAI;QACT7D,EAAE,CAACK,oBAAoB,CAAC+B,OAAO,EAAE;MACrC,CAAC,CAAC;MACF,IAAIiE,SAAS,GAAG;QAAC,GAAGhC;MAAU,CAAC;MAC/BgC,SAAS,CAACrV,IAAI,GAAG,SAAS;MAC1B,IAAI,CAACwL,cAAc,CAAC4J,WAAW,CAACC,SAAS,EAAEjG,QAAQ,IAAG;QAClDJ,EAAE,CAACvL,UAAU,GAAG,CAAC,GAAG,IAAIkL,GAAG,CAACS,QAAQ,CAACjB,OAAO,CAACuB,GAAG,CAAClC,EAAE,IAAEA,EAAE,CAAC1K,MAAM,CAAC,CAAC,CAAC;QACjEkM,EAAE,CAACvL,UAAU,GAAGuL,EAAE,CAACvL,UAAU,CAACiM,GAAG,CAAClC,EAAE,KAAG;UACnCJ,IAAI,EAAGI,EAAE;UACTH,KAAK,EAAGG;SACX,CAAC,CAAC;MACP,CAAC,EAAE,IAAI,EAAE,MAAI;QACTwB,EAAE,CAACK,oBAAoB,CAAC+B,OAAO,EAAE;MACrC,CAAC,CAAC;KACL,MAAK;MACF,IAAI,CAAC5F,cAAc,CAAC1H,cAAc,CAACuP,UAAU,EAAEjE,QAAQ,IAAG;QACtDJ,EAAE,CAACpL,eAAe,GAAG;UACjBuK,OAAO,EAAEiB,QAAQ,CAACjB,OAAO;UACzBC,KAAK,EAAEgB,QAAQ,CAACyD;SACnB;MACL,CAAC,EAAE,IAAI,EAAE,MAAI;QACT7D,EAAE,CAACK,oBAAoB,CAAC+B,OAAO,EAAE;MACrC,CAAC,CAAC;MACF,IAAIiE,SAAS,GAAG;QAAC,GAAGhC;MAAU,CAAC;MAC/BgC,SAAS,CAACrV,IAAI,GAAG,SAAS;MAC1B,IAAI,CAACwL,cAAc,CAAC1H,cAAc,CAACuR,SAAS,EAAEjG,QAAQ,IAAG;QACrDJ,EAAE,CAACvL,UAAU,GAAG,CAAC,GAAG,IAAIkL,GAAG,CAACS,QAAQ,CAACjB,OAAO,CAACuB,GAAG,CAAClC,EAAE,IAAEA,EAAE,CAAC1K,MAAM,CAAC,CAAC,CAAC;QACjEkM,EAAE,CAACvL,UAAU,GAAGuL,EAAE,CAACvL,UAAU,CAACiM,GAAG,CAAClC,EAAE,KAAG;UACnCJ,IAAI,EAAGI,EAAE;UACTH,KAAK,EAAGG;SACX,CAAC,CAAC;MACP,CAAC,EAAE,IAAI,EAAE,MAAI;QACTwB,EAAE,CAACK,oBAAoB,CAAC+B,OAAO,EAAE;MACrC,CAAC,CAAC;;EAEV;EAEA/L,QAAQA,CAAA;IACJ,IAAI,CAACiH,iBAAiB,GAAG,IAAI;IAC7B,IAAI0C,EAAE,GAAG,IAAI;IACb,IAAG,IAAI,CAACpK,WAAW,CAACI,SAAS,EAAE;MAC3B,IAAI,CAACJ,WAAW,CAACI,SAAS,GAAG,IAAI,CAACgP,aAAa,CAAC,EAAE,CAAC;KACtD,MAAK;MACF,IAAI,CAACpP,WAAW,CAACI,SAAS,GAAG,IAAI,CAACgP,aAAa,CAAC,EAAE,CAAC;MACnDhF,EAAE,CAAC5L,gBAAgB,EAAE;;EAE7B;EAEAA,gBAAgBA,CAAC8P,IAAK;IAClB,IAAIlE,EAAE,GAAG,IAAI;IACb,IAAGkE,IAAI,EAAE;MACLlE,EAAE,CAACtL,kBAAkB,CAAC3D,IAAI,GAAG,CAAC;;IAElCiP,EAAE,CAAClL,cAAc,CAACkL,EAAE,CAACtL,kBAAkB,CAAC3D,IAAI,EAAEiP,EAAE,CAACtL,kBAAkB,CAAC1D,IAAI,EAAEgP,EAAE,CAACtL,kBAAkB,CAACpD,MAAM,EAAE0O,EAAE,CAACnM,oBAAoB,CAAC;EACpI;;;uBAh3BSyI,uBAAuB,EAAAlS,EAAA,CAAAkc,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAApc,EAAA,CAAAkc,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAtc,EAAA,CAAAkc,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAxc,EAAA,CAAAkc,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA1c,EAAA,CAAAkc,iBAAA,CAAAlc,EAAA,CAAA2c,QAAA;IAAA;EAAA;;;YAAvBzK,uBAAuB;MAAA0K,SAAA;MAAAC,QAAA,GAAA7c,EAAA,CAAA8c,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCfpCpd,EAAA,CAAAgD,UAAA,IAAAsa,uCAAA,qBA2eO;;;UA3eAtd,EAAA,CAAAwC,UAAA,SAAA6a,GAAA,CAAAla,WAAA,CAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}