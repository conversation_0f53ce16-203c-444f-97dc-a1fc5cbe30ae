{"ast": null, "code": "import { CONSTANTS } from \"src/app/service/comon/constants\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/service/comon/util.service\";\nimport * as i3 from \"src/app/service/account/AccountService\";\nimport * as i4 from \"src/app/service/customer/CustomerService\";\nimport * as i5 from \"../../../service/contract/ContractService\";\nimport * as i6 from \"src/app/service/comon/translate.service\";\nimport * as i7 from \"src/app/service/comon/message-common.service\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"src/app/service/comon/debounce.input.service\";\nimport * as i10 from \"../../../service/session/SessionService\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"primeng/breadcrumb\";\nimport * as i13 from \"primeng/inputtext\";\nimport * as i14 from \"primeng/button\";\nimport * as i15 from \"../../common-module/table/table.component\";\nimport * as i16 from \"primeng/dropdown\";\nimport * as i17 from \"primeng/card\";\nimport * as i18 from \"primeng/inputtextarea\";\nimport * as i19 from \"primeng/tabview\";\nimport * as i20 from \"primeng/radiobutton\";\nfunction AppProfileEditComponent_p_card_7_small_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.tranService.translate(\"global.message.required\"));\n  }\n}\nconst _c0 = function () {\n  return {\n    len: 255\n  };\n};\nfunction AppProfileEditComponent_p_card_7_small_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction AppProfileEditComponent_p_card_7_small_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"global.message.formatContainVN\"));\n  }\n}\nfunction AppProfileEditComponent_p_card_7_small_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction AppProfileEditComponent_p_card_7_small_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction AppProfileEditComponent_p_card_7_small_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r6.tranService.translate(\"global.message.invalidEmail\"));\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    type: a0\n  };\n};\nfunction AppProfileEditComponent_p_card_7_small_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.tranService.translate(\"global.message.exists\", i0.ɵɵpureFunction1(1, _c1, ctx_r7.tranService.translate(\"account.label.email\").toLowerCase())));\n  }\n}\nfunction AppProfileEditComponent_p_card_7_small_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r8.tranService.translate(\"global.message.invalidPhone\"));\n  }\n}\nfunction AppProfileEditComponent_p_card_7_small_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r9.tranService.translate(\"global.message.exists\", i0.ɵɵpureFunction1(1, _c1, ctx_r9.tranService.translate(\"account.label.phone\").toLowerCase())));\n  }\n}\nfunction AppProfileEditComponent_p_card_7_small_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r10.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction AppProfileEditComponent_p_card_7_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"label\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 15)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r11.tranService.translate(\"account.label.province\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r11.accountResponse.provinceName, \" (\", ctx_r11.accountResponse.provinceCode, \")\");\n  }\n}\nconst _c2 = function () {\n  return {\n    standalone: true\n  };\n};\nconst _c3 = function () {\n  return [5, 10, 20, 25, 50];\n};\nfunction AppProfileEditComponent_p_card_7_p_tabPanel_71_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 10)(1, \"div\", 36)(2, \"input\", 37);\n    i0.ɵɵlistener(\"keydown.enter\", function AppProfileEditComponent_p_card_7_p_tabPanel_71_Template_input_keydown_enter_2_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.onSearchCustomer(true));\n    })(\"ngModelChange\", function AppProfileEditComponent_p_card_7_p_tabPanel_71_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.paramQuickSearchCustomer.keyword = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-button\", 38);\n    i0.ɵɵlistener(\"click\", function AppProfileEditComponent_p_card_7_p_tabPanel_71_Template_p_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.onSearchCustomer(true));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(4, \"table-vnpt\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r12.tranService.translate(\"global.menu.listcustomer\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"placeholder\", ctx_r12.tranService.translate(\"sim.label.quickSearch\"))(\"ngModel\", ctx_r12.paramQuickSearchCustomer.keyword)(\"ngModelOptions\", i0.ɵɵpureFunction0(15, _c2));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"pageNumber\", ctx_r12.paginationCustomer.page)(\"pageSize\", ctx_r12.paginationCustomer.size)(\"columns\", ctx_r12.columnInfoCustomer)(\"dataSet\", ctx_r12.dataSetCustomer)(\"options\", ctx_r12.optionTableCustomer)(\"loadData\", ctx_r12.searchCustomer.bind(ctx_r12))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(16, _c3))(\"scrollHeight\", \"400px\")(\"sort\", ctx_r12.paginationCustomer.sortBy)(\"params\", ctx_r12.paramQuickSearchCustomer);\n  }\n}\nfunction AppProfileEditComponent_p_card_7_p_tabPanel_72_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 10)(1, \"div\", 36)(2, \"input\", 37);\n    i0.ɵɵlistener(\"keydown.enter\", function AppProfileEditComponent_p_card_7_p_tabPanel_72_Template_input_keydown_enter_2_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.onSearchContract(true));\n    })(\"ngModelChange\", function AppProfileEditComponent_p_card_7_p_tabPanel_72_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.paramQuickSearchContract.keyword = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-button\", 38);\n    i0.ɵɵlistener(\"click\", function AppProfileEditComponent_p_card_7_p_tabPanel_72_Template_p_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.onSearchContract(true));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(4, \"table-vnpt\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r13.tranService.translate(\"global.menu.listbill\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"placeholder\", ctx_r13.tranService.translate(\"sim.label.quickSearch\"))(\"ngModel\", ctx_r13.paramQuickSearchContract.keyword)(\"ngModelOptions\", i0.ɵɵpureFunction0(15, _c2));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"pageNumber\", ctx_r13.paginationContract.page)(\"pageSize\", ctx_r13.paginationContract.size)(\"columns\", ctx_r13.columnInfoContract)(\"dataSet\", ctx_r13.dataSetContract)(\"options\", ctx_r13.optionTableContract)(\"loadData\", ctx_r13.searchContract.bind(ctx_r13))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(16, _c3))(\"scrollHeight\", \"400px\")(\"sort\", ctx_r13.paginationContract.sortBy)(\"params\", ctx_r13.paramQuickSearchContract);\n  }\n}\nfunction AppProfileEditComponent_p_card_7_p_tabPanel_73_label_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 61);\n    i0.ɵɵlistener(\"click\", function AppProfileEditComponent_p_card_7_p_tabPanel_73_label_18_Template_label_click_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r25.isShowSecretKey = true);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppProfileEditComponent_p_card_7_p_tabPanel_73_label_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 62);\n    i0.ɵɵlistener(\"click\", function AppProfileEditComponent_p_card_7_p_tabPanel_73_label_19_Template_label_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r27.isShowSecretKey = false);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppProfileEditComponent_p_card_7_p_tabPanel_73_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 40)(1, \"div\", 41)(2, \"p-panel\", 42)(3, \"div\", 43)(4, \"p-radioButton\", 44);\n    i0.ɵɵlistener(\"ngModelChange\", function AppProfileEditComponent_p_card_7_p_tabPanel_73_Template_p_radioButton_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.statusGrantApi = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p-radioButton\", 45);\n    i0.ɵɵlistener(\"ngModelChange\", function AppProfileEditComponent_p_card_7_p_tabPanel_73_Template_p_radioButton_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.statusGrantApi = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 46)(7, \"div\", 47)(8, \"div\", 48)(9, \"label\", 49);\n    i0.ɵɵtext(10, \"Client ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 50);\n    i0.ɵɵlistener(\"ngModelChange\", function AppProfileEditComponent_p_card_7_p_tabPanel_73_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.genGrantApi.clientId = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 47)(13, \"div\", 48)(14, \"label\", 49);\n    i0.ɵɵtext(15, \"Secret Key\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 51)(17, \"input\", 52);\n    i0.ɵɵlistener(\"ngModelChange\", function AppProfileEditComponent_p_card_7_p_tabPanel_73_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.genGrantApi.secretKey = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, AppProfileEditComponent_p_card_7_p_tabPanel_73_label_18_Template, 1, 0, \"label\", 53);\n    i0.ɵɵtemplate(19, AppProfileEditComponent_p_card_7_p_tabPanel_73_label_19_Template, 1, 0, \"label\", 54);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 55)(21, \"p-button\", 56);\n    i0.ɵɵlistener(\"click\", function AppProfileEditComponent_p_card_7_p_tabPanel_73_Template_p_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r34 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r34.genToken());\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(22, \"div\")(23, \"p-panel\", 57)(24, \"div\", 46)(25, \"div\", 58)(26, \"p-dropdown\", 59);\n    i0.ɵɵlistener(\"ngModelChange\", function AppProfileEditComponent_p_card_7_p_tabPanel_73_Template_p_dropdown_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.paramsSearchGrantApi.module = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 58)(28, \"input\", 60);\n    i0.ɵɵlistener(\"ngModelChange\", function AppProfileEditComponent_p_card_7_p_tabPanel_73_Template_input_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.paramsSearchGrantApi.api = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"p-button\", 38);\n    i0.ɵɵlistener(\"click\", function AppProfileEditComponent_p_card_7_p_tabPanel_73_Template_p_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.onSearchGrantApi(true));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(30, \"table-vnpt\", 39);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r14.tranService.translate(\"account.text.grantApi\"));\n    i0.ɵɵproperty(\"pt\", \"ProfileTab\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"showHeader\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r14.tranService.translate(\"account.text.working\"))(\"ngModel\", ctx_r14.statusGrantApi)(\"ngModelOptions\", i0.ɵɵpureFunction0(39, _c2));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", ctx_r14.tranService.translate(\"account.text.notWorking\"))(\"ngModel\", ctx_r14.statusGrantApi)(\"ngModelOptions\", i0.ɵɵpureFunction0(40, _c2));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r14.genGrantApi.clientId)(\"disabled\", true)(\"ngModelOptions\", i0.ɵɵpureFunction0(41, _c2));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r14.genGrantApi.secretKey)(\"ngModelOptions\", i0.ɵɵpureFunction0(42, _c2))(\"type\", ctx_r14.isShowSecretKey ? \"text\" : \"password\")(\"disabled\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.isShowSecretKey == false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.isShowSecretKey == true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r14.tranService.translate(\"account.text.gen\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"showHeader\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"showClear\", true)(\"ngModel\", ctx_r14.paramsSearchGrantApi.module)(\"ngModelOptions\", i0.ɵɵpureFunction0(43, _c2))(\"options\", ctx_r14.listModule)(\"emptyFilterMessage\", ctx_r14.tranService.translate(\"global.text.nodata\"))(\"placeholder\", ctx_r14.tranService.translate(\"account.text.module\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r14.paramsSearchGrantApi.api)(\"ngModelOptions\", i0.ɵɵpureFunction0(44, _c2));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"pageNumber\", ctx_r14.paginationGrantApi.page)(\"pageSize\", ctx_r14.paginationGrantApi.size)(\"columns\", ctx_r14.columnInfoGrantApi)(\"dataSet\", ctx_r14.dataSetGrantApi)(\"options\", ctx_r14.optionTableGrantApi)(\"loadData\", ctx_r14.searchGrantApi.bind(ctx_r14))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(45, _c3))(\"scrollHeight\", \"400px\")(\"sort\", ctx_r14.paginationGrantApi.sortBy)(\"params\", ctx_r14.paramsSearchGrantApi);\n  }\n}\nfunction AppProfileEditComponent_p_card_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-card\", 8)(1, \"div\")(2, \"p-tabView\", 9);\n    i0.ɵɵlistener(\"onChange\", function AppProfileEditComponent_p_card_7_Template_p_tabView_onChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.onTabChange($event));\n    });\n    i0.ɵɵelementStart(3, \"p-tabPanel\", 10)(4, \"div\", 11)(5, \"div\", 12)(6, \"div\", 13)(7, \"label\", 14);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 15);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 13)(12, \"label\", 16);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementStart(14, \"span\", 17);\n    i0.ɵɵtext(15, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 18)(17, \"input\", 19);\n    i0.ɵɵlistener(\"ngModelChange\", function AppProfileEditComponent_p_card_7_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.accountInfo.fullName = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 20);\n    i0.ɵɵelement(19, \"label\", 16);\n    i0.ɵɵelementStart(20, \"div\", 21);\n    i0.ɵɵtemplate(21, AppProfileEditComponent_p_card_7_small_21_Template, 2, 1, \"small\", 22);\n    i0.ɵɵtemplate(22, AppProfileEditComponent_p_card_7_small_22_Template, 2, 2, \"small\", 22);\n    i0.ɵɵtemplate(23, AppProfileEditComponent_p_card_7_small_23_Template, 2, 1, \"small\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 13)(25, \"label\", 23);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementStart(27, \"span\", 17);\n    i0.ɵɵtext(28, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 21)(30, \"input\", 24);\n    i0.ɵɵlistener(\"ngModelChange\", function AppProfileEditComponent_p_card_7_Template_input_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.accountInfo.email = $event);\n    })(\"ngModelChange\", function AppProfileEditComponent_p_card_7_Template_input_ngModelChange_30_listener() {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r42 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r42.checkExistAccount(\"email\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 20);\n    i0.ɵɵelement(32, \"label\", 23);\n    i0.ɵɵelementStart(33, \"div\", 21);\n    i0.ɵɵtemplate(34, AppProfileEditComponent_p_card_7_small_34_Template, 2, 1, \"small\", 22);\n    i0.ɵɵtemplate(35, AppProfileEditComponent_p_card_7_small_35_Template, 2, 2, \"small\", 22);\n    i0.ɵɵtemplate(36, AppProfileEditComponent_p_card_7_small_36_Template, 2, 1, \"small\", 22);\n    i0.ɵɵtemplate(37, AppProfileEditComponent_p_card_7_small_37_Template, 2, 3, \"small\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 13)(39, \"label\", 25);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 21)(42, \"input\", 26);\n    i0.ɵɵlistener(\"ngModelChange\", function AppProfileEditComponent_p_card_7_Template_input_ngModelChange_42_listener($event) {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r43 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r43.accountInfo.phone = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(43, \"div\", 20);\n    i0.ɵɵelement(44, \"label\", 25);\n    i0.ɵɵelementStart(45, \"div\", 21);\n    i0.ɵɵtemplate(46, AppProfileEditComponent_p_card_7_small_46_Template, 2, 1, \"small\", 22);\n    i0.ɵɵtemplate(47, AppProfileEditComponent_p_card_7_small_47_Template, 2, 3, \"small\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 13)(49, \"label\", 27);\n    i0.ɵɵtext(50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"div\", 21)(52, \"textarea\", 28);\n    i0.ɵɵlistener(\"ngModelChange\", function AppProfileEditComponent_p_card_7_Template_textarea_ngModelChange_52_listener($event) {\n      i0.ɵɵrestoreView(_r39);\n      const ctx_r44 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r44.accountInfo.description = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"div\", 20);\n    i0.ɵɵelement(54, \"label\", 27);\n    i0.ɵɵelementStart(55, \"div\", 21);\n    i0.ɵɵtemplate(56, AppProfileEditComponent_p_card_7_small_56_Template, 2, 2, \"small\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(57, \"div\", 12)(58, \"div\", 13)(59, \"label\", 29);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"div\", 15)(62, \"span\");\n    i0.ɵɵtext(63);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(64, AppProfileEditComponent_p_card_7_div_64_Template, 6, 3, \"div\", 30);\n    i0.ɵɵelementStart(65, \"div\", 13)(66, \"label\", 31);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"div\", 32)(69, \"div\");\n    i0.ɵɵtext(70);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵtemplate(71, AppProfileEditComponent_p_card_7_p_tabPanel_71_Template, 5, 17, \"p-tabPanel\", 33);\n    i0.ɵɵtemplate(72, AppProfileEditComponent_p_card_7_p_tabPanel_72_Template, 5, 17, \"p-tabPanel\", 33);\n    i0.ɵɵtemplate(73, AppProfileEditComponent_p_card_7_p_tabPanel_73_Template, 31, 46, \"p-tabPanel\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r0.tranService.translate(\"account.label.generalInfo\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.username\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.accountResponse.username, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.fullname\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.accountInfo.fullName)(\"required\", true)(\"maxLength\", 255)(\"placeholder\", ctx_r0.tranService.translate(\"account.text.inputFullname\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.fullName.dirty && (ctx_r0.formAccount.controls.fullName.errors == null ? null : ctx_r0.formAccount.controls.fullName.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.fullName.errors == null ? null : ctx_r0.formAccount.controls.fullName.errors.maxLength);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.fullName.errors == null ? null : ctx_r0.formAccount.controls.fullName.errors.pattern);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.email\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.accountInfo.email)(\"required\", true)(\"maxLength\", 255)(\"placeholder\", ctx_r0.tranService.translate(\"account.text.inputEmail\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.email.dirty && (ctx_r0.formAccount.controls.email.errors == null ? null : ctx_r0.formAccount.controls.email.errors.required));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.email.errors == null ? null : ctx_r0.formAccount.controls.email.errors.maxLength);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.email.errors == null ? null : ctx_r0.formAccount.controls.email.errors.pattern);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isEmailExisted);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.phone\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.accountInfo.phone)(\"placeholder\", ctx_r0.tranService.translate(\"account.text.inputPhone\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.phone.errors == null ? null : ctx_r0.formAccount.controls.phone.errors.pattern);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPhoneExisted);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.description\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"autoResize\", false)(\"ngModel\", ctx_r0.accountInfo.description)(\"maxlength\", 255)(\"placeholder\", ctx_r0.tranService.translate(\"sim.text.inputDescription\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.formAccount.controls.description.errors == null ? null : ctx_r0.formAccount.controls.description.errors.maxLength);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.userType\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.getStringUserType(ctx_r0.accountResponse.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.accountInfo.userType != ctx_r0.optionUserType.ADMIN && ctx_r0.accountInfo.userType != ctx_r0.optionUserType.AGENCY);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.role\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.getStringRoles());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.accountInfo.userType == ctx_r0.CONSTANTS.USER_TYPE.CUSTOMER);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.accountInfo.userType == ctx_r0.CONSTANTS.USER_TYPE.CUSTOMER);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.userType == ctx_r0.optionUserType.CUSTOMER && ctx_r0.statusGrantApi != null);\n  }\n}\nexport class AppProfileEditComponent {\n  constructor(route, router, utilService, accountService, customerService, contractService, tranService, messageCommonService, formBuilder, debounceService, sessionService) {\n    this.route = route;\n    this.router = router;\n    this.utilService = utilService;\n    this.accountService = accountService;\n    this.customerService = customerService;\n    this.contractService = contractService;\n    this.tranService = tranService;\n    this.messageCommonService = messageCommonService;\n    this.formBuilder = formBuilder;\n    this.debounceService = debounceService;\n    this.sessionService = sessionService;\n    this.isUsernameExisted = false;\n    this.isEmailExisted = false;\n    this.isPhoneExisted = false;\n    this.oldUserType = null;\n    this.isShowSecretKey = true;\n    this.listModule = [];\n    //sẽ lưu lại list api sau khi đã chọn\n    this.selectItemGrantApi = [];\n    this.paramsSearchGrantApi = {\n      api: null,\n      module: null\n    };\n    this.genGrantApi = {\n      clientId: '',\n      secretKey: ''\n    };\n    this.isChangeSecretKey = false;\n    this.CONSTANTS = CONSTANTS;\n  }\n  ngOnInit() {\n    this.userInfo = this.sessionService.userInfo;\n    this.userType = this.userInfo.type;\n    this.accountId = this.userInfo.id;\n    this.optionUserType = CONSTANTS.USER_TYPE;\n    this.items = [{\n      label: this.tranService.translate(\"global.menu.account\"),\n      routerLink: \"/profile\"\n    }, {\n      label: this.tranService.translate(\"global.menu.editAccount\")\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    let fullTypeAccount = [{\n      name: this.tranService.translate(\"account.usertype.admin\"),\n      value: CONSTANTS.USER_TYPE.ADMIN\n    }, {\n      name: this.tranService.translate(\"account.usertype.customer\"),\n      value: CONSTANTS.USER_TYPE.CUSTOMER\n    }, {\n      name: this.tranService.translate(\"account.usertype.province\"),\n      value: CONSTANTS.USER_TYPE.PROVINCE\n    }, {\n      name: this.tranService.translate(\"account.usertype.district\"),\n      value: CONSTANTS.USER_TYPE.DISTRICT\n    }, {\n      name: this.tranService.translate(\"account.usertype.agency\"),\n      value: CONSTANTS.USER_TYPE.AGENCY\n    }];\n    this.statusAccounts = fullTypeAccount;\n    this.accountInfo = {\n      accountName: null,\n      fullName: null,\n      email: null,\n      phone: null,\n      userType: this.statusAccounts[0].value,\n      province: null,\n      roles: null,\n      description: null,\n      manager: null,\n      customers: null\n    };\n    this.paginationGrantApi = {\n      page: 0,\n      size: 10,\n      sortBy: \"id,desc\"\n    };\n    this.columnInfoGrantApi = [{\n      name: \"API\",\n      key: \"name\",\n      size: \"30%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: \"Module\",\n      key: \"module\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }];\n    this.dataSetGrantApi = {\n      content: [],\n      total: 0\n    };\n    this.optionTableGrantApi = {\n      hasClearSelected: false,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false\n    };\n    this.getDetail();\n    this.paramQuickSearchCustomer = {\n      keyword: null,\n      accountRootId: Number(this.accountId)\n    };\n    this.columnInfoCustomer = [{\n      name: this.tranService.translate(\"customer.label.customerCode\"),\n      key: \"code\",\n      size: \"30%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"customer.label.customerName\"),\n      key: \"name\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }];\n    this.dataSetCustomer = {\n      content: [],\n      total: 0\n    };\n    this.paginationCustomer = {\n      page: 0,\n      size: 10,\n      sortBy: \"name,asc;id,asc\"\n    };\n    this.optionTableCustomer = {\n      hasClearSelected: false,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false\n    };\n    this.paramQuickSearchContract = {\n      keyword: null,\n      accountRootId: Number(this.accountId),\n      customerIds: []\n    };\n    this.columnInfoContract = [{\n      name: this.tranService.translate(\"customer.label.customerCode\"),\n      key: \"customerCode\",\n      size: \"30%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"customer.label.customerName\"),\n      key: \"customerName\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"contract.label.contractCode\"),\n      key: \"contractCode\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }];\n    this.dataSetContract = {\n      content: [],\n      total: 0\n    };\n    this.paginationContract = {\n      page: 0,\n      size: 10,\n      sortBy: \"customerName,asc;id,asc\"\n    };\n    this.optionTableContract = {\n      hasClearSelected: false,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false\n    };\n  }\n  ngAfterContentChecked() {\n    if (this.accountInfo.userType != this.oldUserType && this.formAccount) {\n      this.oldUserType = this.accountInfo.userType;\n      this.formAccount.get(\"province\").reset();\n      this.formAccount.get(\"customers\").reset();\n    }\n  }\n  checkExistAccount(type) {\n    let email = null;\n    let username = null;\n    if (type == \"accountName\") {\n      this.isUsernameExisted = false;\n      username = this.accountInfo.accountName;\n      if (username == this.accountResponse.username) return;\n    } else if (type == \"email\") {\n      this.isEmailExisted = false;\n      email = this.accountInfo.email;\n      if (email == this.accountResponse.email) return;\n    }\n    let me = this;\n    this.debounceService.set(type, this.accountService.checkAccount.bind(this.accountService), email, username, response => {\n      if (response >= 1) {\n        if (type == \"accountName\") {\n          me.isUsernameExisted = true;\n        } else {\n          me.isEmailExisted = true;\n        }\n      }\n    });\n  }\n  onSubmitCreate() {\n    let dataBody = {\n      username: this.accountInfo.accountName,\n      fullName: this.accountInfo.fullName,\n      description: this.accountInfo.description,\n      email: this.accountInfo.email,\n      phone: this.accountInfo.phone,\n      type: this.accountInfo.userType,\n      provinceCode: this.accountInfo.province,\n      roleLst: (this.accountInfo.roles || []).map(el => el.id),\n      customerIdLst: (this.accountInfo.customers || []).map(el => el.id),\n      statusApi: this.statusGrantApi,\n      secretId: this.genGrantApi.secretKey,\n      isChangeSecretKey: this.isChangeSecretKey\n    };\n    if (dataBody.phone != null) {\n      if (dataBody.phone.startsWith('0')) {\n        dataBody.phone = \"84\" + dataBody.phone.substring(1, dataBody.phone.length);\n      } else if (dataBody.phone.length == 9 || dataBody.phone.length == 10) {\n        dataBody.phone = \"84\" + dataBody.phone;\n      }\n    }\n    this.messageCommonService.onload();\n    let me = this;\n    this.accountService.updateProfile(dataBody, response => {\n      me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n      me.router.navigate(['/profile']);\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  closeForm() {\n    this.router.navigate(['/profile']);\n  }\n  getDetail() {\n    let me = this;\n    let accountid = this.userInfo.id;\n    this.accountService.viewProfile(response => {\n      me.accountResponse = response;\n      me.accountInfo.accountName = response.username;\n      me.accountInfo.fullName = response.fullName;\n      me.accountInfo.email = response.email;\n      me.accountInfo.description = response.description;\n      me.accountInfo.phone = response.phone;\n      me.accountInfo.province = response.provinceCode;\n      me.accountInfo.userType = response.type;\n      me.formAccount = me.formBuilder.group(me.accountInfo);\n      me.formAccount.controls.accountName.disable();\n      me.formAccount.controls.userType.disable();\n      // me.formAccount.controls.provinceCode.disable();\n      if (me.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\n        me.resetPaginationCustomerAndContract();\n        me.paramQuickSearchCustomer.accountRootId = Number(me.accountId);\n        me.paramQuickSearchContract.accountRootId = Number(me.accountId);\n        me.paramQuickSearchContract.customerIds = (me.accountResponse.customers || []).map(customer => customer.customerId);\n      }\n      me.statusGrantApi = response.statusApi;\n      me.selectItemGrantApi = response.listApiId ? response.listApiId.map(el => ({\n        id: el\n      })) : [{\n        id: -99\n      }];\n      me.genGrantApi.secretKey = response.secretId;\n      me.genGrantApi.clientId = response.username;\n      setTimeout(function () {\n        me.getListRole(false);\n      }, 100);\n    });\n  }\n  getListRole(isClear) {\n    let me = this;\n    this.accountService.getListRole(this.accountInfo.userType, response => {\n      me.listRole = response.map(el => {\n        return {\n          id: el.id,\n          name: el.name\n        };\n      });\n      me.formAccount.controls.roles.disable();\n      if (isClear) {\n        me.accountInfo.roles = null;\n      } else {\n        let roleIds = (me.accountResponse.roles || []).map(el => el.roleId);\n        me.accountInfo.roles = me.listRole.filter(el => roleIds.includes(el.id));\n      }\n    });\n  }\n  getStringCustomers() {\n    return (this.accountResponse.customers || []).map(el => el.customerName + ' - ' + el.customerCode).toLocaleString();\n  }\n  getStringUserType(value) {\n    if (value == CONSTANTS.USER_TYPE.ADMIN) {\n      return this.tranService.translate(\"account.usertype.admin\");\n    } else if (value == CONSTANTS.USER_TYPE.CUSTOMER) {\n      return this.tranService.translate(\"account.usertype.customer\");\n    } else if (value == CONSTANTS.USER_TYPE.PROVINCE) {\n      return this.tranService.translate(\"account.usertype.province\");\n    } else if (value == CONSTANTS.USER_TYPE.DISTRICT) {\n      return this.tranService.translate(\"account.usertype.district\");\n    } else if (value == CONSTANTS.USER_TYPE.AGENCY) {\n      return this.tranService.translate(\"account.usertype.agency\");\n    } else {\n      return \"\";\n    }\n  }\n  getStringRoles() {\n    return (this.accountResponse.roles || []).map(el => el.roleName).toLocaleString();\n  }\n  onTabChange(event) {\n    const tabName = event.originalEvent.target.innerText;\n    let me = this;\n    if (event && tabName.includes(this.tranService.translate('account.text.grantApi'))) {\n      me.onSearchGrantApi();\n    } else if (event && tabName.includes(this.tranService.translate('global.menu.listbill'))) {\n      me.onSearchContract();\n    } else if (event && tabName.includes(this.tranService.translate('global.menu.listcustomer'))) {\n      me.onSearchCustomer();\n    }\n  }\n  onSearchCustomer(back) {\n    let me = this;\n    if (back) {\n      me.paginationCustomer.page = 0;\n    }\n    me.searchCustomer(me.paginationCustomer.page, me.paginationCustomer.size, me.paginationCustomer.sortBy, me.paramQuickSearchCustomer);\n  }\n  onSearchContract(back) {\n    let me = this;\n    if (back) {\n      me.paginationContract.page = 0;\n    }\n    me.searchContract(me.paginationContract.page, me.paginationContract.size, me.paginationContract.sortBy, me.paramQuickSearchContract);\n  }\n  searchCustomer(page, limit, sort, params) {\n    let me = this;\n    this.paginationCustomer.page = page;\n    this.paginationCustomer.size = limit;\n    this.paginationCustomer.sortBy = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    Object.keys(this.paramQuickSearchCustomer).forEach(key => {\n      if (this.paramQuickSearchCustomer[key] != null) {\n        dataParams[key] = this.paramQuickSearchCustomer[key];\n      }\n    });\n    me.messageCommonService.onload();\n    this.customerService.quickSearchCustomer(dataParams, this.paramQuickSearchCustomer, response => {\n      me.dataSetCustomer = {\n        content: response.content,\n        total: response.totalElements\n      };\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n    // console.log(this.selectItemCustomer)\n  }\n\n  searchContract(page, limit, sort, params) {\n    let me = this;\n    this.paginationContract.page = page;\n    this.paginationContract.size = limit;\n    this.paginationContract.sortBy = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    // Object.keys(this.paramQuickSearchContract).forEach(key => {\n    //     if(this.paramQuickSearchContract[key] != null){\n    //         dataParams[key] = this.paramQuickSearchContract[key];\n    //     }\n    // })\n    me.messageCommonService.onload();\n    this.contractService.quickSearchContract(dataParams, this.paramQuickSearchContract, response => {\n      me.dataSetContract = {\n        content: response.content,\n        total: response.totalElements\n      };\n      // console.log(response)\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  resetPaginationCustomerAndContract() {\n    this.paginationCustomer = {\n      page: 0,\n      size: 10,\n      sortBy: \"name,asc;id,asc\"\n    };\n    this.paginationContract = {\n      page: 0,\n      size: 10,\n      sortBy: \"customerName,asc;id,asc\"\n    };\n  }\n  searchGrantApi(page, limit, sort, params) {\n    let me = this;\n    this.paginationGrantApi.page = page;\n    this.paginationGrantApi.size = limit;\n    this.paginationGrantApi.sortBy = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort,\n      selectedApiIds: this.selectItemGrantApi.map(el => el.id).join(',')\n    };\n    Object.keys(this.paramsSearchGrantApi).forEach(key => {\n      if (this.paramsSearchGrantApi[key] != null) {\n        dataParams[key] = this.paramsSearchGrantApi[key];\n      }\n    });\n    console.log(dataParams);\n    me.messageCommonService.onload();\n    this.accountService.searchGrantApi(dataParams, response => {\n      me.dataSetGrantApi = {\n        content: response.content,\n        total: response.totalElements\n      };\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n    let copyParam = {\n      ...dataParams\n    };\n    copyParam.size = *********;\n    this.accountService.searchGrantApi(copyParam, response => {\n      me.listModule = [...new Set(response.content.map(el => el.module))];\n      me.listModule = me.listModule.map(el => ({\n        name: el,\n        value: el\n      }));\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  generateToken(n) {\n    var chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    var token = '';\n    for (var i = 0; i < n; i++) {\n      token += chars[Math.floor(Math.random() * chars.length)];\n    }\n    return token;\n  }\n  genToken() {\n    this.genGrantApi.secretKey = this.generateToken(20);\n  }\n  onSearchGrantApi(back) {\n    let me = this;\n    console.log(me.paramsSearchGrantApi);\n    if (back) {\n      me.paginationGrantApi.page = 0;\n    }\n    me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);\n  }\n  static {\n    this.ɵfac = function AppProfileEditComponent_Factory(t) {\n      return new (t || AppProfileEditComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.UtilService), i0.ɵɵdirectiveInject(i3.AccountService), i0.ɵɵdirectiveInject(i4.CustomerService), i0.ɵɵdirectiveInject(i5.ContractService), i0.ɵɵdirectiveInject(i6.TranslateService), i0.ɵɵdirectiveInject(i7.MessageCommonService), i0.ɵɵdirectiveInject(i8.FormBuilder), i0.ɵɵdirectiveInject(i9.DebounceInputService), i0.ɵɵdirectiveInject(i10.SessionService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppProfileEditComponent,\n      selectors: [[\"app-account-edit\"]],\n      decls: 8,\n      vars: 7,\n      consts: [[3, \"formGroup\", \"ngSubmit\"], [1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"align-items-center\", \"mr-2\"], [\"styleClass\", \"p-button-secondary p-button-outlined mr-2\", 3, \"label\", \"click\"], [\"styleClass\", \"p-button-info\", \"type\", \"submit\", 3, \"label\", \"disabled\"], [\"styleClass\", \"mt-3 responsive-form\", 4, \"ngIf\"], [\"styleClass\", \"mt-3 responsive-form\"], [3, \"onChange\"], [3, \"header\"], [1, \"flex\", \"flex-row\", \"justify-content-between\", \"profile-create\"], [2, \"width\", \"49%\"], [1, \"w-full\", \"field\", \"grid\"], [\"htmlFor\", \"accountName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"col\"], [\"htmlFor\", \"fullName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"text-red-500\"], [1, \"col\", \"wrap-div\", \"wrap-div-1\"], [\"pInputText\", \"\", \"id\", \"fullName\", \"formControlName\", \"fullName\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [1, \"w-full\", \"field\", \"grid\", \"text-error-field\"], [1, \"col\", \"wrap-div\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [\"htmlFor\", \"email\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"email\", \"formControlName\", \"email\", \"pattern\", \"^[a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+)+(\\\\.[a-z]{2,})$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"phone\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"phone\", \"formControlName\", \"phone\", \"pattern\", \"^((\\\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\", 1, \"w-full\", 3, \"ngModel\", \"placeholder\", \"ngModelChange\"], [\"htmlFor\", \"description\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"description\", \"formControlName\", \"description\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"autoResize\", \"ngModel\", \"maxlength\", \"placeholder\", \"ngModelChange\"], [\"for\", \"userType\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"class\", \"w-full field grid\", 4, \"ngIf\"], [\"htmlFor\", \"roles\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"col\", 2, \"max-width\", \"calc(100% - 180px) !important\"], [3, \"header\", 4, \"ngIf\"], [3, \"header\", \"pt\", 4, \"ngIf\"], [\"htmlFor\", \"province\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"gap-3\", \"mt-4\"], [\"type\", \"text\", \"pInputText\", \"\", 2, \"min-width\", \"35vw\", 3, \"placeholder\", \"ngModel\", \"ngModelOptions\", \"keydown.enter\", \"ngModelChange\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"ml-3 p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"button\", 3, \"click\"], [3, \"fieldId\", \"pageNumber\", \"pageSize\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"rowsPerPageOptions\", \"scrollHeight\", \"sort\", \"params\"], [3, \"header\", \"pt\"], [1, \"mb-3\"], [3, \"showHeader\"], [1, \"flex\", \"gap-2\"], [\"value\", \"1\", 1, \"p-3\", 3, \"label\", \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [\"value\", \"0\", 1, \"p-3\", 3, \"label\", \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [1, \"flex\", \"gap-3\", \"align-items-center\"], [1, \"col-5\"], [1, \"flex\", \"align-items-center\"], [1, \"mr-3\", 2, \"min-width\", \"100px\"], [\"type\", \"text\", \"pInputText\", \"\", 1, \"w-full\", 3, \"ngModel\", \"disabled\", \"ngModelOptions\", \"ngModelChange\"], [1, \"w-full\", \"flex\", \"align-items-center\"], [\"pInputText\", \"\", 1, \"w-full\", \"mr-2\", 2, \"padding-right\", \"30px\", 3, \"ngModel\", \"ngModelOptions\", \"type\", \"disabled\", \"ngModelChange\"], [\"style\", \"margin-left: -30px;z-index: 1;\", \"class\", \"pi pi-eye toggle-password\", 3, \"click\", 4, \"ngIf\"], [\"style\", \"margin-left: -30px;z-index: 1;\", \"class\", \"pi pi-eye-slash toggle-password\", 3, \"click\", 4, \"ngIf\"], [1, \"col-2\"], [\"styleClass\", \"p-button-primary mr-2\", 3, \"label\", \"click\"], [1, \"\", 3, \"showHeader\"], [1, \"col-3\"], [\"optionLabel\", \"name\", \"optionValue\", \"value\", \"filter\", \"true\", 1, \"w-full\", 3, \"showClear\", \"ngModel\", \"ngModelOptions\", \"options\", \"emptyFilterMessage\", \"placeholder\", \"ngModelChange\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"API\", 1, \"w-full\", \"mr-2\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [1, \"pi\", \"pi-eye\", \"toggle-password\", 2, \"margin-left\", \"-30px\", \"z-index\", \"1\", 3, \"click\"], [1, \"pi\", \"pi-eye-slash\", \"toggle-password\", 2, \"margin-left\", \"-30px\", \"z-index\", \"1\", 3, \"click\"]],\n      template: function AppProfileEditComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0);\n          i0.ɵɵlistener(\"ngSubmit\", function AppProfileEditComponent_Template_form_ngSubmit_0_listener() {\n            return ctx.onSubmitCreate();\n          });\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵelement(2, \"p-breadcrumb\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"p-button\", 5);\n          i0.ɵɵlistener(\"click\", function AppProfileEditComponent_Template_p_button_click_5_listener() {\n            return ctx.closeForm();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"p-button\", 6);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(7, AppProfileEditComponent_p_card_7_Template, 74, 39, \"p-card\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.formAccount);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.cancel\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.save\"))(\"disabled\", ctx.formAccount.invalid || ctx.isPhoneExisted || ctx.isUsernameExisted || ctx.isEmailExisted);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccount);\n        }\n      },\n      dependencies: [i11.NgIf, i12.Breadcrumb, i8.ɵNgNoValidate, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgControlStatusGroup, i8.RequiredValidator, i8.MaxLengthValidator, i8.PatternValidator, i8.NgModel, i8.FormGroupDirective, i8.FormControlName, i13.InputText, i14.Button, i15.TableVnptComponent, i16.Dropdown, i17.Card, i18.InputTextarea, i19.TabView, i19.TabPanel, i20.RadioButton],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CONSTANTS", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "tranService", "translate", "ctx_r2", "ɵɵpureFunction0", "_c0", "ctx_r3", "ctx_r4", "ctx_r5", "ctx_r6", "ctx_r7", "ɵɵpureFunction1", "_c1", "toLowerCase", "ctx_r8", "ctx_r9", "ctx_r10", "ctx_r11", "ɵɵtextInterpolate2", "accountResponse", "provinceName", "provinceCode", "ɵɵlistener", "AppProfileEditComponent_p_card_7_p_tabPanel_71_Template_input_keydown_enter_2_listener", "ɵɵrestoreView", "_r16", "ctx_r15", "ɵɵnextContext", "ɵɵresetView", "onSearchCustomer", "AppProfileEditComponent_p_card_7_p_tabPanel_71_Template_input_ngModelChange_2_listener", "$event", "ctx_r17", "paramQuickSearchCustomer", "keyword", "AppProfileEditComponent_p_card_7_p_tabPanel_71_Template_p_button_click_3_listener", "ctx_r18", "ɵɵelement", "ɵɵpropertyInterpolate", "ctx_r12", "ɵɵproperty", "_c2", "paginationCustomer", "page", "size", "columnInfoCustomer", "dataSetCustomer", "optionTableCustomer", "searchCustomer", "bind", "_c3", "sortBy", "AppProfileEditComponent_p_card_7_p_tabPanel_72_Template_input_keydown_enter_2_listener", "_r20", "ctx_r19", "onSearchContract", "AppProfileEditComponent_p_card_7_p_tabPanel_72_Template_input_ngModelChange_2_listener", "ctx_r21", "paramQuickSearchContract", "AppProfileEditComponent_p_card_7_p_tabPanel_72_Template_p_button_click_3_listener", "ctx_r22", "ctx_r13", "paginationContract", "columnInfoContract", "dataSetContract", "optionTableContract", "searchContract", "AppProfileEditComponent_p_card_7_p_tabPanel_73_label_18_Template_label_click_0_listener", "_r26", "ctx_r25", "isShowSecretKey", "AppProfileEditComponent_p_card_7_p_tabPanel_73_label_19_Template_label_click_0_listener", "_r28", "ctx_r27", "AppProfileEditComponent_p_card_7_p_tabPanel_73_Template_p_radioButton_ngModelChange_4_listener", "_r30", "ctx_r29", "statusGrantApi", "AppProfileEditComponent_p_card_7_p_tabPanel_73_Template_p_radioButton_ngModelChange_5_listener", "ctx_r31", "AppProfileEditComponent_p_card_7_p_tabPanel_73_Template_input_ngModelChange_11_listener", "ctx_r32", "genGrant<PERSON>pi", "clientId", "AppProfileEditComponent_p_card_7_p_tabPanel_73_Template_input_ngModelChange_17_listener", "ctx_r33", "secret<PERSON>ey", "ɵɵtemplate", "AppProfileEditComponent_p_card_7_p_tabPanel_73_label_18_Template", "AppProfileEditComponent_p_card_7_p_tabPanel_73_label_19_Template", "AppProfileEditComponent_p_card_7_p_tabPanel_73_Template_p_button_click_21_listener", "ctx_r34", "genToken", "AppProfileEditComponent_p_card_7_p_tabPanel_73_Template_p_dropdown_ngModelChange_26_listener", "ctx_r35", "paramsSearchGrantApi", "module", "AppProfileEditComponent_p_card_7_p_tabPanel_73_Template_input_ngModelChange_28_listener", "ctx_r36", "api", "AppProfileEditComponent_p_card_7_p_tabPanel_73_Template_p_button_click_29_listener", "ctx_r37", "onSearchGrantApi", "ctx_r14", "listModule", "paginationGrantApi", "columnInfoGrantApi", "dataSetGrantApi", "optionTableGrantApi", "searchGrantApi", "AppProfileEditComponent_p_card_7_Template_p_tabView_onChange_2_listener", "_r39", "ctx_r38", "onTabChange", "AppProfileEditComponent_p_card_7_Template_input_ngModelChange_17_listener", "ctx_r40", "accountInfo", "fullName", "AppProfileEditComponent_p_card_7_small_21_Template", "AppProfileEditComponent_p_card_7_small_22_Template", "AppProfileEditComponent_p_card_7_small_23_Template", "AppProfileEditComponent_p_card_7_Template_input_ngModelChange_30_listener", "ctx_r41", "email", "ctx_r42", "checkExistAccount", "AppProfileEditComponent_p_card_7_small_34_Template", "AppProfileEditComponent_p_card_7_small_35_Template", "AppProfileEditComponent_p_card_7_small_36_Template", "AppProfileEditComponent_p_card_7_small_37_Template", "AppProfileEditComponent_p_card_7_Template_input_ngModelChange_42_listener", "ctx_r43", "phone", "AppProfileEditComponent_p_card_7_small_46_Template", "AppProfileEditComponent_p_card_7_small_47_Template", "AppProfileEditComponent_p_card_7_Template_textarea_ngModelChange_52_listener", "ctx_r44", "description", "AppProfileEditComponent_p_card_7_small_56_Template", "AppProfileEditComponent_p_card_7_div_64_Template", "AppProfileEditComponent_p_card_7_p_tabPanel_71_Template", "AppProfileEditComponent_p_card_7_p_tabPanel_72_Template", "AppProfileEditComponent_p_card_7_p_tabPanel_73_Template", "ctx_r0", "ɵɵtextInterpolate1", "username", "formAccount", "controls", "dirty", "errors", "required", "max<PERSON><PERSON><PERSON>", "pattern", "isEmailExisted", "isPhoneExisted", "getStringUserType", "type", "userType", "optionUserType", "ADMIN", "AGENCY", "getStringRoles", "USER_TYPE", "CUSTOMER", "AppProfileEditComponent", "constructor", "route", "router", "utilService", "accountService", "customerService", "contractService", "messageCommonService", "formBuilder", "debounceService", "sessionService", "isUsernameExisted", "oldUserType", "selectItemGrantApi", "isChangeSecretKey", "ngOnInit", "userInfo", "accountId", "id", "items", "label", "routerLink", "home", "icon", "fullTypeAccount", "name", "value", "PROVINCE", "DISTRICT", "statusAccounts", "accountName", "province", "roles", "manager", "customers", "key", "align", "isShow", "isSort", "content", "total", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "getDetail", "accountRootId", "Number", "customerIds", "ngAfterContentChecked", "get", "reset", "me", "set", "checkAccount", "response", "onSubmitCreate", "dataBody", "roleLst", "map", "el", "customerIdLst", "statusApi", "secretId", "startsWith", "substring", "length", "onload", "updateProfile", "success", "navigate", "offload", "closeForm", "accountid", "viewProfile", "group", "disable", "resetPaginationCustomerAndContract", "customer", "customerId", "listApiId", "setTimeout", "getListRole", "isClear", "listRole", "roleIds", "roleId", "filter", "includes", "getStringCustomers", "customerName", "customerCode", "toLocaleString", "<PERSON><PERSON><PERSON>", "event", "tabName", "originalEvent", "target", "innerText", "back", "limit", "sort", "params", "dataParams", "Object", "keys", "for<PERSON>ach", "quickSearchCustomer", "totalElements", "quickSearchContract", "selectedApiIds", "join", "console", "log", "copyParam", "Set", "generateToken", "n", "chars", "token", "i", "Math", "floor", "random", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "UtilService", "i3", "AccountService", "i4", "CustomerService", "i5", "ContractService", "i6", "TranslateService", "i7", "MessageCommonService", "i8", "FormBuilder", "i9", "DebounceInputService", "i10", "SessionService", "selectors", "decls", "vars", "consts", "template", "AppProfileEditComponent_Template", "rf", "ctx", "AppProfileEditComponent_Template_form_ngSubmit_0_listener", "AppProfileEditComponent_Template_p_button_click_5_listener", "AppProfileEditComponent_p_card_7_Template", "invalid"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\profile\\edit\\app.profile.edit.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\profile\\edit\\app.profile.edit.component.html"], "sourcesContent": ["import { AfterContentChecked, Component, OnInit } from \"@angular/core\";\r\nimport { FormBuilder } from \"@angular/forms\";\r\nimport { ActivatedRoute, Router } from \"@angular/router\";\r\nimport { MenuItem } from \"primeng/api\";\r\nimport { AutoCompleteCompleteEvent } from \"primeng/autocomplete\";\r\nimport { AccountService } from \"src/app/service/account/AccountService\";\r\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\r\nimport { DebounceInputService } from \"src/app/service/comon/debounce.input.service\";\r\nimport { MessageCommonService } from \"src/app/service/comon/message-common.service\";\r\nimport { TranslateService } from \"src/app/service/comon/translate.service\";\r\nimport { UtilService } from \"src/app/service/comon/util.service\";\r\nimport {SessionService} from \"../../../service/session/SessionService\";\r\nimport { CustomerService } from \"src/app/service/customer/CustomerService\";\r\nimport {ColumnInfo, OptionTable} from \"../../common-module/table/table.component\";\r\nimport {ContractService} from \"../../../service/contract/ContractService\";\r\n\r\n@Component({\r\n    selector: \"app-account-edit\",\r\n    templateUrl: './app.profile.edit.component.html'\r\n})\r\nexport class AppProfileEditComponent implements OnInit, AfterContentChecked{\r\n    constructor(private route: ActivatedRoute,\r\n                private router: Router,\r\n                public utilService: UtilService,\r\n                public accountService: AccountService,\r\n                private customerService: CustomerService,\r\n                private contractService: ContractService,\r\n                public tranService: TranslateService,\r\n                public messageCommonService: MessageCommonService,\r\n                private formBuilder: FormBuilder,\r\n                private debounceService: DebounceInputService,\r\n                private sessionService: SessionService) {\r\n\r\n    }\r\n    userInfo : any\r\n    items: Array<MenuItem>;\r\n    home: MenuItem;\r\n    accountInfo: {\r\n        accountName: string| null,\r\n        fullName: string|null,\r\n        email: string|null,\r\n        phone: string|null,\r\n        userType: number| null,\r\n        province: any,\r\n        roles: Array<any>,\r\n        description: string|null,\r\n        manager: any,\r\n        customers: Array<any>\r\n    };\r\n    formAccount: any;\r\n    statusAccounts: Array<any>;\r\n    listRole: Array<any>;\r\n    listProvince: Array<any>;\r\n    listCustomer: Array<any>;\r\n    userType: number;\r\n    optionUserType: any;\r\n    isUsernameExisted: boolean = false;\r\n    isEmailExisted: boolean = false;\r\n    isPhoneExisted: boolean = false;\r\n    oldUserType: number | null = null;\r\n    accountResponse: any;\r\n    paginationCustomer: {\r\n        page: number|null,\r\n        size: number|null,\r\n        sortBy: string|null,\r\n    }\r\n    paramQuickSearchCustomer: {\r\n        keyword: string|null,\r\n        accountRootId: number| null,\r\n    }\r\n    dataSetCustomer: {\r\n        content: Array<any>,\r\n        total: number,\r\n    }\r\n    paginationContract: {\r\n        page: number|null,\r\n        size: number|null,\r\n        sortBy: string|null,\r\n    }\r\n    paramQuickSearchContract: {\r\n        keyword: string|null,\r\n        accountRootId: number| null,\r\n        customerIds: Array<{ id: number }>|null,\r\n    }\r\n    dataSetContract: {\r\n        content: Array<any>,\r\n        total: number,\r\n    }\r\n    columnInfoCustomer: Array<ColumnInfo>;\r\n    optionTableCustomer: OptionTable;\r\n    columnInfoContract: Array<ColumnInfo>;\r\n    optionTableContract: OptionTable;\r\n    accountId: number | string;\r\n\r\n    isShowSecretKey = true\r\n    listModule = []\r\n    //sẽ lưu lại list api sau khi đã chọn\r\n    selectItemGrantApi: Array<any> = []\r\n    paginationGrantApi: {\r\n        page: number|null,\r\n        size: number|null,\r\n        sortBy: string|null,\r\n    }\r\n    columnInfoGrantApi: Array<ColumnInfo>;\r\n\r\n    dataSetGrantApi: {\r\n        content: Array<any>,\r\n        total: number,\r\n    }\r\n    optionTableGrantApi: OptionTable;\r\n\r\n    paramsSearchGrantApi = {api : null, module : null}\r\n\r\n    genGrantApi = {clientId: '', secretKey: ''}\r\n\r\n    statusGrantApi : any;\r\n\r\n    isChangeSecretKey : boolean = false;\r\n\r\n    ngOnInit(): void {\r\n        this.userInfo = this.sessionService.userInfo;\r\n        this.userType = this.userInfo.type;\r\n        this.accountId = this.userInfo.id;\r\n        this.optionUserType = CONSTANTS.USER_TYPE;\r\n        this.items = [\r\n            { label: this.tranService.translate(\"global.menu.account\"), routerLink:\"/profile\"  },\r\n            { label: this.tranService.translate(\"global.menu.editAccount\") }\r\n        ];\r\n        this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n\r\n        let fullTypeAccount = [\r\n            {name: this.tranService.translate(\"account.usertype.admin\"),value:CONSTANTS.USER_TYPE.ADMIN},\r\n            {name: this.tranService.translate(\"account.usertype.customer\"),value:CONSTANTS.USER_TYPE.CUSTOMER},\r\n            {name: this.tranService.translate(\"account.usertype.province\"),value:CONSTANTS.USER_TYPE.PROVINCE},\r\n            {name: this.tranService.translate(\"account.usertype.district\"),value:CONSTANTS.USER_TYPE.DISTRICT},\r\n            {name: this.tranService.translate(\"account.usertype.agency\"),value:CONSTANTS.USER_TYPE.AGENCY},\r\n        ]\r\n        this.statusAccounts = fullTypeAccount;\r\n        this.accountInfo = {\r\n            accountName: null,\r\n            fullName: null,\r\n            email: null,\r\n            phone: null,\r\n            userType: this.statusAccounts[0].value,\r\n            province: null,\r\n            roles: null,\r\n            description: null,\r\n            manager: null,\r\n            customers: null\r\n        }\r\n        this.paginationGrantApi = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"id,desc\",\r\n        }\r\n        this.columnInfoGrantApi = [\r\n            {\r\n                name: \"API\",\r\n                key: \"name\",\r\n                size: \"30%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n            },\r\n            {\r\n                name: \"Module\",\r\n                key: \"module\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n            }\r\n        ]\r\n\r\n        this.dataSetGrantApi = {\r\n            content: [],\r\n            total: 0,\r\n        }\r\n\r\n        this.optionTableGrantApi = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n        }\r\n        this.getDetail();\r\n        this.paramQuickSearchCustomer = {\r\n            keyword: null,\r\n            accountRootId: Number(this.accountId),\r\n        }\r\n        this.columnInfoCustomer = [\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerCode\"),\r\n                key: \"code\",\r\n                size: \"30%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerName\"),\r\n                key: \"name\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n        ]\r\n        this.dataSetCustomer = {\r\n            content: [],\r\n            total: 0,\r\n        }\r\n        this.paginationCustomer = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"name,asc;id,asc\",\r\n        }\r\n        this.optionTableCustomer = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n        }\r\n        this.paramQuickSearchContract = {\r\n            keyword: null,\r\n            accountRootId: Number(this.accountId),\r\n            customerIds: [],\r\n        }\r\n        this.columnInfoContract = [\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerCode\"),\r\n                key: \"customerCode\",\r\n                size: \"30%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerName\"),\r\n                key: \"customerName\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"contract.label.contractCode\"),\r\n                key: \"contractCode\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n        ]\r\n        this.dataSetContract = {\r\n            content: [],\r\n            total: 0,\r\n        }\r\n        this.paginationContract = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"customerName,asc;id,asc\",\r\n        }\r\n        this.optionTableContract = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n        }\r\n    }\r\n\r\n    ngAfterContentChecked(): void {\r\n        if(this.accountInfo.userType != this.oldUserType && this.formAccount){\r\n            this.oldUserType = this.accountInfo.userType;\r\n            this.formAccount.get(\"province\").reset();\r\n            this.formAccount.get(\"customers\").reset();\r\n        }\r\n    }\r\n\r\n    checkExistAccount(type){\r\n        let email = null;\r\n        let username = null;\r\n        if(type == \"accountName\"){\r\n            this.isUsernameExisted = false;\r\n            username = this.accountInfo.accountName;\r\n            if(username == this.accountResponse.username) return;\r\n        }else if(type == \"email\"){\r\n            this.isEmailExisted = false;\r\n            email = this.accountInfo.email;\r\n            if(email == this.accountResponse.email) return;\r\n        }\r\n\r\n        let me = this;\r\n\r\n        this.debounceService.set(type, this.accountService.checkAccount.bind(this.accountService), email, username,(response)=>{\r\n            if(response >= 1){\r\n                if(type == \"accountName\"){\r\n                    me.isUsernameExisted = true;\r\n                }else{\r\n                    me.isEmailExisted = true;\r\n                }\r\n            }\r\n        })\r\n    }\r\n\r\n    onSubmitCreate(){\r\n        let dataBody = {\r\n            username: this.accountInfo.accountName,\r\n            fullName: this.accountInfo.fullName,\r\n            description: this.accountInfo.description,\r\n            email: this.accountInfo.email,\r\n            phone: this.accountInfo.phone,\r\n            type: this.accountInfo.userType,\r\n            provinceCode: this.accountInfo.province,\r\n            roleLst: (this.accountInfo.roles|| []).map(el => el.id),\r\n            customerIdLst: (this.accountInfo.customers || []).map(el => el.id),\r\n            statusApi : this.statusGrantApi,\r\n            secretId : this.genGrantApi.secretKey,\r\n            isChangeSecretKey: this.isChangeSecretKey\r\n        }\r\n        if(dataBody.phone != null){\r\n            if(dataBody.phone.startsWith('0')){\r\n                dataBody.phone = \"84\"+dataBody.phone.substring(1, dataBody.phone.length);\r\n            }else if(dataBody.phone.length == 9 || dataBody.phone.length == 10){\r\n                dataBody.phone = \"84\"+dataBody.phone;\r\n            }\r\n        }\r\n        this.messageCommonService.onload();\r\n        let me = this;\r\n        this.accountService.updateProfile(dataBody, (response)=>{\r\n            me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n            me.router.navigate(['/profile']);\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    closeForm(){\r\n        this.router.navigate(['/profile'])\r\n    }\r\n\r\n    getDetail(){\r\n        let me = this;\r\n        let accountid = this.userInfo.id;\r\n        this.accountService.viewProfile( (response)=>{\r\n            me.accountResponse = response;\r\n            me.accountInfo.accountName = response.username;\r\n            me.accountInfo.fullName = response.fullName;\r\n            me.accountInfo.email = response.email;\r\n            me.accountInfo.description = response.description;\r\n            me.accountInfo.phone = response.phone;\r\n            me.accountInfo.province = response.provinceCode;\r\n            me.accountInfo.userType = response.type;\r\n            me.formAccount = me.formBuilder.group(me.accountInfo);\r\n            me.formAccount.controls.accountName.disable();\r\n            me.formAccount.controls.userType.disable();\r\n            // me.formAccount.controls.provinceCode.disable();\r\n            if (me.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\r\n                me.resetPaginationCustomerAndContract()\r\n                me.paramQuickSearchCustomer.accountRootId = Number(me.accountId)\r\n                me.paramQuickSearchContract.accountRootId = Number(me.accountId)\r\n                me.paramQuickSearchContract.customerIds = (me.accountResponse.customers|| []).map(customer => customer.customerId)\r\n            }\r\n            me.statusGrantApi = response.statusApi\r\n            me.selectItemGrantApi = response.listApiId? response.listApiId.map(el=> ({id: el})) : [{id:-99}]\r\n            me.genGrantApi.secretKey = response.secretId\r\n            me.genGrantApi.clientId = response.username\r\n            setTimeout(function(){\r\n                me.getListRole(false);\r\n            },100)\r\n        })\r\n    }\r\n\r\n    getListRole(isClear){\r\n        let me = this;\r\n        this.accountService.getListRole(this.accountInfo.userType, (response)=>{\r\n            me.listRole = response.map(el => {\r\n                return {\r\n                    id: el.id,\r\n                    name: el.name\r\n                }\r\n            });\r\n            me.formAccount.controls.roles.disable();\r\n            if(isClear){\r\n                me.accountInfo.roles = null;\r\n            }else{\r\n                let roleIds = (me.accountResponse.roles || []).map(el => el.roleId);\r\n                me.accountInfo.roles = me.listRole.filter(el => roleIds.includes(el.id));\r\n            }\r\n        })\r\n    }\r\n\r\n    getStringCustomers(){\r\n        return (this.accountResponse.customers || []).map(el => el.customerName + ' - ' + el.customerCode).toLocaleString();\r\n    }\r\n\r\n    getStringUserType(value) {\r\n        if(value == CONSTANTS.USER_TYPE.ADMIN){\r\n            return this.tranService.translate(\"account.usertype.admin\");\r\n        }else if(value == CONSTANTS.USER_TYPE.CUSTOMER){\r\n            return this.tranService.translate(\"account.usertype.customer\");\r\n        }else if(value == CONSTANTS.USER_TYPE.PROVINCE){\r\n            return this.tranService.translate(\"account.usertype.province\");\r\n        }else if(value == CONSTANTS.USER_TYPE.DISTRICT){\r\n            return this.tranService.translate(\"account.usertype.district\");\r\n        }else if(value == CONSTANTS.USER_TYPE.AGENCY){\r\n            return this.tranService.translate(\"account.usertype.agency\");\r\n        }else{\r\n            return \"\";\r\n        }\r\n    }\r\n\r\n    getStringRoles(){\r\n        return (this.accountResponse.roles || []).map(el => el.roleName).toLocaleString()\r\n    }\r\n    onTabChange(event) {\r\n        const tabName = event.originalEvent.target.innerText;\r\n        let me = this;\r\n        if (event && tabName.includes(this.tranService.translate('account.text.grantApi'))) {\r\n            me.onSearchGrantApi()\r\n        } else if (event && tabName.includes(this.tranService.translate('global.menu.listbill'))) {\r\n            me.onSearchContract()\r\n        } else if (event && tabName.includes(this.tranService.translate('global.menu.listcustomer'))) {\r\n            me.onSearchCustomer()\r\n        }\r\n    }\r\n    onSearchCustomer(back?) {\r\n        let me = this;\r\n        if (back) {\r\n            me.paginationCustomer.page = 0;\r\n        }\r\n        me.searchCustomer(me.paginationCustomer.page, me.paginationCustomer.size, me.paginationCustomer.sortBy, me.paramQuickSearchCustomer);\r\n    }\r\n    onSearchContract(back?) {\r\n        let me = this;\r\n        if (back) {\r\n            me.paginationContract.page = 0;\r\n        }\r\n        me.searchContract(me.paginationContract.page, me.paginationContract.size, me.paginationContract.sortBy, me.paramQuickSearchContract);\r\n    }\r\n    searchCustomer(page, limit, sort, params){\r\n        let me = this;\r\n        this.paginationCustomer.page = page;\r\n        this.paginationCustomer.size = limit;\r\n        this.paginationCustomer.sortBy = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        Object.keys(this.paramQuickSearchCustomer).forEach(key => {\r\n            if(this.paramQuickSearchCustomer[key] != null){\r\n                dataParams[key] = this.paramQuickSearchCustomer[key];\r\n            }\r\n        })\r\n        me.messageCommonService.onload();\r\n        this.customerService.quickSearchCustomer(dataParams, this.paramQuickSearchCustomer,(response)=>{\r\n            me.dataSetCustomer = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n        // console.log(this.selectItemCustomer)\r\n    }\r\n    searchContract(page, limit, sort, params){\r\n        let me = this;\r\n        this.paginationContract.page = page;\r\n        this.paginationContract.size = limit;\r\n        this.paginationContract.sortBy = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        // Object.keys(this.paramQuickSearchContract).forEach(key => {\r\n        //     if(this.paramQuickSearchContract[key] != null){\r\n        //         dataParams[key] = this.paramQuickSearchContract[key];\r\n        //     }\r\n        // })\r\n        me.messageCommonService.onload();\r\n        this.contractService.quickSearchContract(dataParams, this.paramQuickSearchContract,(response)=>{\r\n            me.dataSetContract = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n            // console.log(response)\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n    resetPaginationCustomerAndContract() {\r\n        this.paginationCustomer = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"name,asc;id,asc\",\r\n        }\r\n        this.paginationContract = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"customerName,asc;id,asc\",\r\n        }\r\n    }\r\n\r\n    searchGrantApi(page, limit, sort, params){\r\n        let me = this;\r\n        this.paginationGrantApi.page = page;\r\n        this.paginationGrantApi.size = limit;\r\n        this.paginationGrantApi.sortBy = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort,\r\n            selectedApiIds: this.selectItemGrantApi.map(el=>el.id).join(',')\r\n        }\r\n        Object.keys(this.paramsSearchGrantApi).forEach(key => {\r\n            if(this.paramsSearchGrantApi[key] != null){\r\n                dataParams[key] = this.paramsSearchGrantApi[key];\r\n            }\r\n        })\r\n        console.log(dataParams)\r\n        me.messageCommonService.onload();\r\n        this.accountService.searchGrantApi(dataParams,(response)=>{\r\n            me.dataSetGrantApi = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n        let copyParam = {...dataParams};\r\n        copyParam.size = *********;\r\n        this.accountService.searchGrantApi(copyParam,(response)=>{\r\n            me.listModule = [...new Set(response.content.map(el=>el.module))]\r\n            me.listModule = me.listModule.map(el=>({\r\n                name : el,\r\n                value : el\r\n            }))\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    generateToken(n) {\r\n        var chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r\n        var token = '';\r\n        for(var i = 0; i < n; i++) {\r\n            token += chars[Math.floor(Math.random() * chars.length)];\r\n        }\r\n        return token;\r\n    }\r\n\r\n    genToken(){\r\n        this.genGrantApi.secretKey = this.generateToken(20);\r\n    }\r\n\r\n    onSearchGrantApi(back?) {\r\n        let me = this;\r\n        console.log(me.paramsSearchGrantApi)\r\n        if(back) {\r\n            me.paginationGrantApi.page = 0;\r\n        }\r\n        me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);\r\n    }\r\n\r\n    protected readonly CONSTANTS = CONSTANTS;\r\n}\r\n", "<form [formGroup]=\"formAccount\" (ngSubmit)=\"onSubmitCreate()\">\r\n<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    <div class=\"col-5 flex flex-row justify-content-end align-items-center\">\r\n        <div class=\"flex flex-row justify-content-center align-items-center mr-2\">\r\n            <p-button [label]=\"tranService.translate('global.button.cancel')\" styleClass=\"p-button-secondary p-button-outlined mr-2\" (click)=\"closeForm()\"></p-button>\r\n            <p-button [label]=\"tranService.translate('global.button.save')\" styleClass=\"p-button-info\" type=\"submit\" [disabled]=\"formAccount.invalid || isPhoneExisted || isUsernameExisted || isEmailExisted\"></p-button>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n<p-card styleClass=\"mt-3 responsive-form\" *ngIf=\"formAccount\">\r\n    <div>\r\n        <p-tabView (onChange)=\"onTabChange($event)\">\r\n            <p-tabPanel header=\"{{tranService.translate('account.label.generalInfo')}}\">\r\n                    <div class=\"flex flex-row justify-content-between profile-create\">\r\n                        <div style=\"width: 49%;\">\r\n                            <!-- username -->\r\n                            <div class=\"w-full field grid\">\r\n                                <label htmlFor=\"accountName\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.username\")}}</label>\r\n                                <div class=\"col\">\r\n                                    {{accountResponse.username}}\r\n                                </div>\r\n                            </div>\r\n                            <!-- fullname -->\r\n                            <div class=\"w-full field grid\">\r\n                                <label htmlFor=\"fullName\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.fullname\")}}<span class=\"text-red-500\">*</span></label>\r\n                                <div class=\"col wrap-div wrap-div-1\">\r\n                                    <input class=\"w-full\"\r\n                                           pInputText id=\"fullName\"\r\n                                           [(ngModel)]=\"accountInfo.fullName\"\r\n                                           formControlName=\"fullName\"\r\n                                           [required]=\"true\"\r\n                                           [maxLength]=\"255\"\r\n                                           pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$\"\r\n                                           [placeholder]=\"tranService.translate('account.text.inputFullname')\"\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n                            <!-- error fullname -->\r\n                            <div class=\"w-full field grid text-error-field\">\r\n                                <label htmlFor=\"fullName\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                                <div class=\"col wrap-div\">\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.fullName.dirty && formAccount.controls.fullName.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.fullName.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.fullName.errors?.pattern\">{{tranService.translate(\"global.message.formatContainVN\")}}</small>\r\n                                </div>\r\n                            </div>\r\n                            <!-- email -->\r\n                            <div class=\"w-full field grid\">\r\n                                <label htmlFor=\"email\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.email\")}}<span class=\"text-red-500\">*</span></label>\r\n                                <div class=\"col wrap-div\">\r\n                                    <input class=\"w-full\"\r\n                                           pInputText id=\"email\"\r\n                                           [(ngModel)]=\"accountInfo.email\"\r\n                                           formControlName=\"email\"\r\n                                           [required]=\"true\"\r\n                                           [maxLength]=\"255\"\r\n                                           pattern=\"^[a-z0-9]+[a-z0-9\\-\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\-\\._]*[a-z0-9]+)+(\\.[a-z]{2,})$\"\r\n                                           [placeholder]=\"tranService.translate('account.text.inputEmail')\"\r\n                                           (ngModelChange)=\"checkExistAccount('email')\"\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n                            <!-- error email -->\r\n                            <div class=\"w-full field grid text-error-field\">\r\n                                <label htmlFor=\"email\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                                <div class=\"col wrap-div\">\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.email.dirty && formAccount.controls.email.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.email.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.email.errors?.pattern\">{{tranService.translate(\"global.message.invalidEmail\")}}</small>\r\n                                    <small class=\"text-red-500\" *ngIf=\"isEmailExisted\">{{tranService.translate(\"global.message.exists\",{type: tranService.translate(\"account.label.email\").toLowerCase()})}}</small>\r\n                                </div>\r\n                            </div>\r\n                            <!-- phone -->\r\n                            <div class=\"w-full field grid\">\r\n                                <label htmlFor=\"phone\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.phone\")}}</label>\r\n                                <div class=\"col wrap-div\">\r\n                                    <input class=\"w-full\"\r\n                                           pInputText id=\"phone\"\r\n                                           [(ngModel)]=\"accountInfo.phone\"\r\n                                           formControlName=\"phone\"\r\n                                           pattern=\"^((\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\"\r\n                                           [placeholder]=\"tranService.translate('account.text.inputPhone')\"\r\n                                    />\r\n                                </div>\r\n                            </div>\r\n                            <!-- error phone -->\r\n                            <div class=\"w-full field grid text-error-field\">\r\n                                <label htmlFor=\"phone\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                                <div class=\"col wrap-div\">\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.phone.errors?.pattern\">{{tranService.translate(\"global.message.invalidPhone\")}}</small>\r\n                                    <small class=\"text-red-500\" *ngIf=\"isPhoneExisted\">{{tranService.translate(\"global.message.exists\",{type: tranService.translate(\"account.label.phone\").toLowerCase()})}}</small>\r\n                                </div>\r\n                            </div>\r\n                            <!-- description -->\r\n                            <div class=\"w-full field grid\">\r\n                                <label htmlFor=\"description\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.description\")}}</label>\r\n                                <div class=\"col wrap-div\">\r\n                            <textarea  class=\"w-full\" style=\"resize: none;\"\r\n                                       rows=\"5\"\r\n                                       [autoResize]=\"false\"\r\n                                       pInputTextarea id=\"description\"\r\n                                       [(ngModel)]=\"accountInfo.description\"\r\n                                       formControlName=\"description\"\r\n                                       [maxlength]=\"255\"\r\n                                       [placeholder]=\"tranService.translate('sim.text.inputDescription')\"\r\n                            ></textarea>\r\n                                </div>\r\n                            </div>\r\n                            <!-- error description -->\r\n                            <div class=\"w-full field grid text-error-field\">\r\n                                <label htmlFor=\"description\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                                <div class=\"col wrap-div\">\r\n                                    <small class=\"text-red-500\" *ngIf=\"formAccount.controls.description.errors?.maxLength\">{{tranService.translate(\"global.message.maxLength\",{len:255})}}</small>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div style=\"width: 49%;\">\r\n\r\n                            <!-- loai tai khoan -->\r\n                            <div class=\"w-full field grid\">\r\n                                <label for=\"userType\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.userType\")}}</label>\r\n                                <div class=\"col\">\r\n                                    <span>{{getStringUserType(accountResponse.type)}}</span>\r\n                                </div>\r\n                            </div>\r\n                            <!-- Tinh thanh pho -->\r\n                            <div class=\"w-full field grid\" *ngIf=\"accountInfo.userType != optionUserType.ADMIN && accountInfo.userType != optionUserType.AGENCY\">\r\n                                <label htmlFor=\"province\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.province\")}}</label>\r\n                                <div class=\"col\">\r\n                                    <span>{{accountResponse.provinceName}} ({{accountResponse.provinceCode}})</span>\r\n                                </div>\r\n                            </div>\r\n                            <!-- nhom quyen -->\r\n                            <div class=\"w-full field grid\">\r\n                                <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.role\")}}</label>\r\n                                <div class=\"col\" style=\"max-width: calc(100% - 180px) !important;\">\r\n                                    <div>{{getStringRoles()}}</div>\r\n                                </div>\r\n                            </div>\r\n                            <!-- ten khach hang -->\r\n<!--                            <div class=\"w-full field grid\" *ngIf=\"accountInfo.userType == optionUserType.CUSTOMER\">-->\r\n<!--                                <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.customerName\")}}</label>-->\r\n<!--                                <div class=\"col\" style=\"max-width: calc(100% - 180px) !important;\">-->\r\n<!--                                    <div>{{getStringCustomers()}}</div>-->\r\n<!--                                </div>-->\r\n<!--                            </div>-->\r\n                        </div>\r\n                    </div>\r\n            </p-tabPanel>\r\n            <p-tabPanel header=\"{{tranService.translate('global.menu.listcustomer')}}\" *ngIf=\"accountInfo.userType ==  CONSTANTS.USER_TYPE.CUSTOMER\">\r\n                <div class=\"flex flex-row justify-content-center gap-3 mt-4\">\r\n                    <input style=\"min-width: 35vw\"  type=\"text\" pInputText [placeholder]=\"tranService.translate('sim.label.quickSearch')\" (keydown.enter)=\"onSearchCustomer(true)\" [(ngModel)]=\"paramQuickSearchCustomer.keyword\" [ngModelOptions]=\"{standalone: true}\">\r\n                    <p-button icon=\"pi pi-search\"\r\n                              styleClass=\"ml-3 p-button-rounded p-button-secondary p-button-text button-search\"\r\n                              type=\"button\"\r\n                              (click)=\"onSearchCustomer(true)\"\r\n                    ></p-button>\r\n                </div>\r\n                <table-vnpt\r\n                    [fieldId]=\"'id'\"\r\n                    [pageNumber]=\"paginationCustomer.page\"\r\n                    [pageSize]=\"paginationCustomer.size\"\r\n                    [columns]=\"columnInfoCustomer\"\r\n                    [dataSet]=\"dataSetCustomer\"\r\n                    [options]=\"optionTableCustomer\"\r\n                    [loadData]=\"searchCustomer.bind(this)\"\r\n                    [rowsPerPageOptions]=\"[5,10,20,25,50]\"\r\n                    [scrollHeight]=\"'400px'\"\r\n                    [sort]=\"paginationCustomer.sortBy\"\r\n                    [params]=\"paramQuickSearchCustomer\"\r\n                ></table-vnpt>\r\n            </p-tabPanel>\r\n            <p-tabPanel header=\"{{tranService.translate('global.menu.listbill')}}\" *ngIf=\"accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER\">\r\n                <div class=\"flex flex-row justify-content-center gap-3 mt-4\">\r\n                    <input style=\"min-width: 35vw\"  type=\"text\" pInputText [placeholder]=\"tranService.translate('sim.label.quickSearch')\" (keydown.enter)=\"onSearchContract(true)\" [(ngModel)]=\"paramQuickSearchContract.keyword\" [ngModelOptions]=\"{standalone: true}\">\r\n                    <p-button icon=\"pi pi-search\"\r\n                              styleClass=\"ml-3 p-button-rounded p-button-secondary p-button-text button-search\"\r\n                              type=\"button\"\r\n                              (click)=\"onSearchContract(true)\"\r\n                    ></p-button>\r\n                </div>\r\n                <table-vnpt\r\n                    [fieldId]=\"'id'\"\r\n                    [pageNumber]=\"paginationContract.page\"\r\n                    [pageSize]=\"paginationContract.size\"\r\n                    [columns]=\"columnInfoContract\"\r\n                    [dataSet]=\"dataSetContract\"\r\n                    [options]=\"optionTableContract\"\r\n                    [loadData]=\"searchContract.bind(this)\"\r\n                    [rowsPerPageOptions]=\"[5,10,20,25,50]\"\r\n                    [scrollHeight]=\"'400px'\"\r\n                    [sort]=\"paginationContract.sortBy\"\r\n                    [params]=\"paramQuickSearchContract\"\r\n                ></table-vnpt>\r\n            </p-tabPanel>\r\n            <p-tabPanel header=\"{{tranService.translate('account.text.grantApi')}}\" *ngIf=\"userType == optionUserType.CUSTOMER && statusGrantApi != null\" [pt]=\"'ProfileTab'\">\r\n                <div class=\"mb-3\">\r\n                    <p-panel [showHeader]=\"false\">\r\n                        <div class=\"flex gap-2\">\r\n                            <p-radioButton\r\n                                    [label]=\"tranService.translate('account.text.working')\"\r\n                                    value=\"1\"\r\n                                    class=\"p-3\"\r\n                                    [(ngModel)]=\"statusGrantApi\"\r\n                                    [ngModelOptions]=\"{standalone: true}\"\r\n                            ></p-radioButton>\r\n\r\n                            <p-radioButton\r\n                                    [label]=\"tranService.translate('account.text.notWorking')\"\r\n                                    value=\"0\"\r\n                                    class=\"p-3\"\r\n                                    [(ngModel)]=\"statusGrantApi\"\r\n                                    [ngModelOptions]=\"{standalone: true}\"\r\n                            ></p-radioButton>\r\n                        </div>\r\n                        <div class=\"flex gap-3 align-items-center\">\r\n                            <div class=\"col-5\">\r\n                                <div class=\"flex align-items-center\">\r\n                                    <label style=\"min-width: 100px\" class=\"mr-3\">Client ID</label>\r\n                                    <input [(ngModel)]=\"genGrantApi.clientId\"  [disabled]=\"true\" [ngModelOptions]=\"{standalone: true}\" class=\"w-full\" type=\"text\" pInputText>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-5\">\r\n                                <div class=\"flex align-items-center\">\r\n                                    <label style=\"min-width: 100px\" class=\"mr-3\">Secret Key</label>\r\n                                    <div class=\"w-full flex align-items-center\">\r\n                                        <input class=\"w-full mr-2\" style=\"padding-right: 30px;\"\r\n                                               [(ngModel)]=\"genGrantApi.secretKey\"\r\n                                               [ngModelOptions]=\"{standalone: true}\"\r\n                                               [type]=\"isShowSecretKey ? 'text': 'password'\"\r\n                                               pInputText\r\n                                               [disabled]=\"true\"\r\n                                        />\r\n                                        <label style=\"margin-left: -30px;z-index: 1;\" *ngIf=\"isShowSecretKey == false\" class=\"pi pi-eye toggle-password\" (click)=\"isShowSecretKey = true\"></label>\r\n                                        <label style=\"margin-left: -30px;z-index: 1;\" *ngIf=\"isShowSecretKey == true\" class=\"pi pi-eye-slash toggle-password\" (click)=\"isShowSecretKey = false\"></label>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-2\">\r\n                                <p-button (click)=\"genToken()\" [label]=\"tranService.translate('account.text.gen')\" styleClass=\"p-button-primary mr-2\"></p-button>\r\n                            </div>\r\n                        </div>\r\n                    </p-panel>\r\n                </div>\r\n                <div>\r\n                    <p-panel [showHeader]=\"false\" class=\"  \">\r\n                        <div class=\"flex gap-3 align-items-center\">\r\n                            <div class=\"col-3\">\r\n                                <p-dropdown class=\"w-full\"\r\n                                            [showClear]=\"true\"\r\n                                            [(ngModel)]=\"paramsSearchGrantApi.module\"\r\n                                            [ngModelOptions]=\"{standalone: true}\"\r\n                                            [options]=\"listModule\"\r\n                                            optionLabel=\"name\"\r\n                                            optionValue=\"value\"\r\n                                            [emptyFilterMessage]=\"tranService.translate('global.text.nodata')\"\r\n                                            filter=\"true\"\r\n                                            [placeholder]=\"tranService.translate('account.text.module')\"\r\n                                ></p-dropdown>\r\n                            </div>\r\n                            <div class=\"col-3\">\r\n                                <input [(ngModel)]=\"paramsSearchGrantApi.api\" [ngModelOptions]=\"{standalone: true}\" class=\"w-full mr-2\" type=\"text\" pInputText placeholder=\"API\"/>\r\n                            </div>\r\n                            <p-button icon=\"pi pi-search\"\r\n                                      styleClass=\"ml-3 p-button-rounded p-button-secondary p-button-text button-search\"\r\n                                      type=\"button\"\r\n                                      (click)=\"onSearchGrantApi(true)\"\r\n                            ></p-button>\r\n                        </div>\r\n\r\n                        <table-vnpt\r\n                                [fieldId]=\"'id'\"\r\n                                [pageNumber]=\"paginationGrantApi.page\"\r\n                                [pageSize]=\"paginationGrantApi.size\"\r\n                                [columns]=\"columnInfoGrantApi\"\r\n                                [dataSet]=\"dataSetGrantApi\"\r\n                                [options]=\"optionTableGrantApi\"\r\n                                [loadData]=\"searchGrantApi.bind(this)\"\r\n                                [rowsPerPageOptions]=\"[5,10,20,25,50]\"\r\n                                [scrollHeight]=\"'400px'\"\r\n                                [sort]=\"paginationGrantApi.sortBy\"\r\n                                [params]=\"paramsSearchGrantApi\"\r\n                        ></table-vnpt>\r\n                    </p-panel>\r\n                </div>\r\n            </p-tabPanel>\r\n        </p-tabView>\r\n    </div>\r\n</p-card>\r\n</form>\r\n\r\n"], "mappings": "AAMA,SAASA,SAAS,QAAQ,iCAAiC;;;;;;;;;;;;;;;;;;;;;;;;ICqCvBC,EAAA,CAAAC,cAAA,gBAA0H;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,4BAAoD;;;;;;;;;;IAC9KR,EAAA,CAAAC,cAAA,gBAAoF;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAvEH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,iBAAA,CAAAI,MAAA,CAAAF,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAC,GAAA,GAA+D;;;;;IACnJX,EAAA,CAAAC,cAAA,gBAAkF;IAAAD,EAAA,CAAAE,MAAA,GAA2D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAnEH,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAK,iBAAA,CAAAO,MAAA,CAAAL,WAAA,CAAAC,SAAA,mCAA2D;;;;;IAuB7IR,EAAA,CAAAC,cAAA,gBAAoH;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA5DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAQ,MAAA,CAAAN,WAAA,CAAAC,SAAA,4BAAoD;;;;;IACxKR,EAAA,CAAAC,cAAA,gBAAiF;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAvEH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,iBAAA,CAAAS,MAAA,CAAAP,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAC,GAAA,GAA+D;;;;;IAChJX,EAAA,CAAAC,cAAA,gBAA+E;IAAAD,EAAA,CAAAE,MAAA,GAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAhEH,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAK,iBAAA,CAAAU,MAAA,CAAAR,WAAA,CAAAC,SAAA,gCAAwD;;;;;;;;;;IACvIR,EAAA,CAAAC,cAAA,gBAAmD;IAAAD,EAAA,CAAAE,MAAA,GAAqH;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA7HH,EAAA,CAAAI,SAAA,GAAqH;IAArHJ,EAAA,CAAAK,iBAAA,CAAAW,MAAA,CAAAT,WAAA,CAAAC,SAAA,0BAAAR,EAAA,CAAAiB,eAAA,IAAAC,GAAA,EAAAF,MAAA,CAAAT,WAAA,CAAAC,SAAA,wBAAAW,WAAA,KAAqH;;;;;IAoBxKnB,EAAA,CAAAC,cAAA,gBAA+E;IAAAD,EAAA,CAAAE,MAAA,GAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAhEH,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAK,iBAAA,CAAAe,MAAA,CAAAb,WAAA,CAAAC,SAAA,gCAAwD;;;;;IACvIR,EAAA,CAAAC,cAAA,gBAAmD;IAAAD,EAAA,CAAAE,MAAA,GAAqH;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAA7HH,EAAA,CAAAI,SAAA,GAAqH;IAArHJ,EAAA,CAAAK,iBAAA,CAAAgB,MAAA,CAAAd,WAAA,CAAAC,SAAA,0BAAAR,EAAA,CAAAiB,eAAA,IAAAC,GAAA,EAAAG,MAAA,CAAAd,WAAA,CAAAC,SAAA,wBAAAW,WAAA,KAAqH;;;;;IAsBxKnB,EAAA,CAAAC,cAAA,gBAAuF;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IAAvEH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,iBAAA,CAAAiB,OAAA,CAAAf,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAU,eAAA,IAAAC,GAAA,GAA+D;;;;;IAc9JX,EAAA,CAAAC,cAAA,cAAqI;IACjED,EAAA,CAAAE,MAAA,GAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3HH,EAAA,CAAAC,cAAA,cAAiB;IACPD,EAAA,CAAAE,MAAA,GAAmE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAFpBH,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAAkB,OAAA,CAAAhB,WAAA,CAAAC,SAAA,2BAAmD;IAEzGR,EAAA,CAAAI,SAAA,GAAmE;IAAnEJ,EAAA,CAAAwB,kBAAA,KAAAD,OAAA,CAAAE,eAAA,CAAAC,YAAA,QAAAH,OAAA,CAAAE,eAAA,CAAAE,YAAA,MAAmE;;;;;;;;;;;;;;IAoBjG3B,EAAA,CAAAC,cAAA,qBAAyI;IAEXD,EAAA,CAAA4B,UAAA,2BAAAC,uFAAA;MAAA7B,EAAA,CAAA8B,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAhC,EAAA,CAAAiC,aAAA;MAAA,OAAiBjC,EAAA,CAAAkC,WAAA,CAAAF,OAAA,CAAAG,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC,2BAAAC,uFAAAC,MAAA;MAAArC,EAAA,CAAA8B,aAAA,CAAAC,IAAA;MAAA,MAAAO,OAAA,GAAAtC,EAAA,CAAAiC,aAAA;MAAA,OAAcjC,EAAA,CAAAkC,WAAA,CAAAI,OAAA,CAAAC,wBAAA,CAAAC,OAAA,GAAAH,MAAA,CAAwC;IAAA,EAAtD;IAA9JrC,EAAA,CAAAG,YAAA,EAAoP;IACpPH,EAAA,CAAAC,cAAA,mBAIC;IADSD,EAAA,CAAA4B,UAAA,mBAAAa,kFAAA;MAAAzC,EAAA,CAAA8B,aAAA,CAAAC,IAAA;MAAA,MAAAW,OAAA,GAAA1C,EAAA,CAAAiC,aAAA;MAAA,OAASjC,EAAA,CAAAkC,WAAA,CAAAQ,OAAA,CAAAP,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC;IACzCnC,EAAA,CAAAG,YAAA,EAAW;IAEhBH,EAAA,CAAA2C,SAAA,qBAYc;IAClB3C,EAAA,CAAAG,YAAA,EAAa;;;;IAtBDH,EAAA,CAAA4C,qBAAA,WAAAC,OAAA,CAAAtC,WAAA,CAAAC,SAAA,6BAA8D;IAEXR,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAA8C,UAAA,gBAAAD,OAAA,CAAAtC,WAAA,CAAAC,SAAA,0BAA8D,YAAAqC,OAAA,CAAAN,wBAAA,CAAAC,OAAA,oBAAAxC,EAAA,CAAAU,eAAA,KAAAqC,GAAA;IAQrH/C,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAA8C,UAAA,iBAAgB,eAAAD,OAAA,CAAAG,kBAAA,CAAAC,IAAA,cAAAJ,OAAA,CAAAG,kBAAA,CAAAE,IAAA,aAAAL,OAAA,CAAAM,kBAAA,aAAAN,OAAA,CAAAO,eAAA,aAAAP,OAAA,CAAAQ,mBAAA,cAAAR,OAAA,CAAAS,cAAA,CAAAC,IAAA,CAAAV,OAAA,yBAAA7C,EAAA,CAAAU,eAAA,KAAA8C,GAAA,oCAAAX,OAAA,CAAAG,kBAAA,CAAAS,MAAA,YAAAZ,OAAA,CAAAN,wBAAA;;;;;;IAaxBvC,EAAA,CAAAC,cAAA,qBAAoI;IAEND,EAAA,CAAA4B,UAAA,2BAAA8B,uFAAA;MAAA1D,EAAA,CAAA8B,aAAA,CAAA6B,IAAA;MAAA,MAAAC,OAAA,GAAA5D,EAAA,CAAAiC,aAAA;MAAA,OAAiBjC,EAAA,CAAAkC,WAAA,CAAA0B,OAAA,CAAAC,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC,2BAAAC,uFAAAzB,MAAA;MAAArC,EAAA,CAAA8B,aAAA,CAAA6B,IAAA;MAAA,MAAAI,OAAA,GAAA/D,EAAA,CAAAiC,aAAA;MAAA,OAAcjC,EAAA,CAAAkC,WAAA,CAAA6B,OAAA,CAAAC,wBAAA,CAAAxB,OAAA,GAAAH,MAAA,CAAwC;IAAA,EAAtD;IAA9JrC,EAAA,CAAAG,YAAA,EAAoP;IACpPH,EAAA,CAAAC,cAAA,mBAIC;IADSD,EAAA,CAAA4B,UAAA,mBAAAqC,kFAAA;MAAAjE,EAAA,CAAA8B,aAAA,CAAA6B,IAAA;MAAA,MAAAO,OAAA,GAAAlE,EAAA,CAAAiC,aAAA;MAAA,OAASjC,EAAA,CAAAkC,WAAA,CAAAgC,OAAA,CAAAL,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC;IACzC7D,EAAA,CAAAG,YAAA,EAAW;IAEhBH,EAAA,CAAA2C,SAAA,qBAYc;IAClB3C,EAAA,CAAAG,YAAA,EAAa;;;;IAtBDH,EAAA,CAAA4C,qBAAA,WAAAuB,OAAA,CAAA5D,WAAA,CAAAC,SAAA,yBAA0D;IAEPR,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAA8C,UAAA,gBAAAqB,OAAA,CAAA5D,WAAA,CAAAC,SAAA,0BAA8D,YAAA2D,OAAA,CAAAH,wBAAA,CAAAxB,OAAA,oBAAAxC,EAAA,CAAAU,eAAA,KAAAqC,GAAA;IAQrH/C,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAA8C,UAAA,iBAAgB,eAAAqB,OAAA,CAAAC,kBAAA,CAAAnB,IAAA,cAAAkB,OAAA,CAAAC,kBAAA,CAAAlB,IAAA,aAAAiB,OAAA,CAAAE,kBAAA,aAAAF,OAAA,CAAAG,eAAA,aAAAH,OAAA,CAAAI,mBAAA,cAAAJ,OAAA,CAAAK,cAAA,CAAAjB,IAAA,CAAAY,OAAA,yBAAAnE,EAAA,CAAAU,eAAA,KAAA8C,GAAA,oCAAAW,OAAA,CAAAC,kBAAA,CAAAX,MAAA,YAAAU,OAAA,CAAAH,wBAAA;;;;;;IAmDIhE,EAAA,CAAAC,cAAA,gBAAkJ;IAAjCD,EAAA,CAAA4B,UAAA,mBAAA6C,wFAAA;MAAAzE,EAAA,CAAA8B,aAAA,CAAA4C,IAAA;MAAA,MAAAC,OAAA,GAAA3E,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,CAAAyC,OAAA,CAAAC,eAAA,GAA2B,IAAI;IAAA,EAAC;IAAC5E,EAAA,CAAAG,YAAA,EAAQ;;;;;;IAC1JH,EAAA,CAAAC,cAAA,gBAAwJ;IAAlCD,EAAA,CAAA4B,UAAA,mBAAAiD,wFAAA;MAAA7E,EAAA,CAAA8B,aAAA,CAAAgD,IAAA;MAAA,MAAAC,OAAA,GAAA/E,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,CAAA6C,OAAA,CAAAH,eAAA,GAA2B,KAAK;IAAA,EAAC;IAAC5E,EAAA,CAAAG,YAAA,EAAQ;;;;;;IAvC5LH,EAAA,CAAAC,cAAA,qBAAkK;IAQ1ID,EAAA,CAAA4B,UAAA,2BAAAoD,+FAAA3C,MAAA;MAAArC,EAAA,CAAA8B,aAAA,CAAAmD,IAAA;MAAA,MAAAC,OAAA,GAAAlF,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,CAAAgD,OAAA,CAAAC,cAAA,GAAA9C,MAAA;IAAA,EAA4B;IAEnCrC,EAAA,CAAAG,YAAA,EAAgB;IAEjBH,EAAA,CAAAC,cAAA,wBAMC;IAFOD,EAAA,CAAA4B,UAAA,2BAAAwD,+FAAA/C,MAAA;MAAArC,EAAA,CAAA8B,aAAA,CAAAmD,IAAA;MAAA,MAAAI,OAAA,GAAArF,EAAA,CAAAiC,aAAA;MAAA,OAAAjC,EAAA,CAAAkC,WAAA,CAAAmD,OAAA,CAAAF,cAAA,GAAA9C,MAAA;IAAA,EAA4B;IAEnCrC,EAAA,CAAAG,YAAA,EAAgB;IAErBH,EAAA,CAAAC,cAAA,cAA2C;IAGcD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAC,cAAA,iBAAyI;IAAlID,EAAA,CAAA4B,UAAA,2BAAA0D,wFAAAjD,MAAA;MAAArC,EAAA,CAAA8B,aAAA,CAAAmD,IAAA;MAAA,MAAAM,OAAA,GAAAvF,EAAA,CAAAiC,aAAA;MAAA,OAAajC,EAAA,CAAAkC,WAAA,CAAAqD,OAAA,CAAAC,WAAA,CAAAC,QAAA,GAAApD,MAAA,CAA4B;IAAA,EAAP;IAAzCrC,EAAA,CAAAG,YAAA,EAAyI;IAGjJH,EAAA,CAAAC,cAAA,eAAmB;IAEkCD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/DH,EAAA,CAAAC,cAAA,eAA4C;IAEjCD,EAAA,CAAA4B,UAAA,2BAAA8D,wFAAArD,MAAA;MAAArC,EAAA,CAAA8B,aAAA,CAAAmD,IAAA;MAAA,MAAAU,OAAA,GAAA3F,EAAA,CAAAiC,aAAA;MAAA,OAAajC,EAAA,CAAAkC,WAAA,CAAAyD,OAAA,CAAAH,WAAA,CAAAI,SAAA,GAAAvD,MAAA,CACvD;IAAA,EAD6E;IAD1CrC,EAAA,CAAAG,YAAA,EAME;IACFH,EAAA,CAAA6F,UAAA,KAAAC,gEAAA,oBAA0J;IAC1J9F,EAAA,CAAA6F,UAAA,KAAAE,gEAAA,oBAAgK;IACpK/F,EAAA,CAAAG,YAAA,EAAM;IAGdH,EAAA,CAAAC,cAAA,eAAmB;IACLD,EAAA,CAAA4B,UAAA,mBAAAoE,mFAAA;MAAAhG,EAAA,CAAA8B,aAAA,CAAAmD,IAAA;MAAA,MAAAgB,OAAA,GAAAjG,EAAA,CAAAiC,aAAA;MAAA,OAASjC,EAAA,CAAAkC,WAAA,CAAA+D,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAAwFlG,EAAA,CAAAG,YAAA,EAAW;IAKjJH,EAAA,CAAAC,cAAA,WAAK;IAMuBD,EAAA,CAAA4B,UAAA,2BAAAuE,6FAAA9D,MAAA;MAAArC,EAAA,CAAA8B,aAAA,CAAAmD,IAAA;MAAA,MAAAmB,OAAA,GAAApG,EAAA,CAAAiC,aAAA;MAAA,OAAajC,EAAA,CAAAkC,WAAA,CAAAkE,OAAA,CAAAC,oBAAA,CAAAC,MAAA,GAAAjE,MAAA,CACpD;IAAA,EADgF;IAQpDrC,EAAA,CAAAG,YAAA,EAAa;IAElBH,EAAA,CAAAC,cAAA,eAAmB;IACRD,EAAA,CAAA4B,UAAA,2BAAA2E,wFAAAlE,MAAA;MAAArC,EAAA,CAAA8B,aAAA,CAAAmD,IAAA;MAAA,MAAAuB,OAAA,GAAAxG,EAAA,CAAAiC,aAAA;MAAA,OAAajC,EAAA,CAAAkC,WAAA,CAAAsE,OAAA,CAAAH,oBAAA,CAAAI,GAAA,GAAApE,MAAA,CAAgC;IAAA,EAAP;IAA7CrC,EAAA,CAAAG,YAAA,EAAkJ;IAEtJH,EAAA,CAAAC,cAAA,oBAIC;IADSD,EAAA,CAAA4B,UAAA,mBAAA8E,mFAAA;MAAA1G,EAAA,CAAA8B,aAAA,CAAAmD,IAAA;MAAA,MAAA0B,OAAA,GAAA3G,EAAA,CAAAiC,aAAA;MAAA,OAASjC,EAAA,CAAAkC,WAAA,CAAAyE,OAAA,CAAAC,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC;IACzC5G,EAAA,CAAAG,YAAA,EAAW;IAGhBH,EAAA,CAAA2C,SAAA,sBAYc;IAClB3C,EAAA,CAAAG,YAAA,EAAU;;;;IAxFNH,EAAA,CAAA4C,qBAAA,WAAAiE,OAAA,CAAAtG,WAAA,CAAAC,SAAA,0BAA2D;IAAuER,EAAA,CAAA8C,UAAA,oBAAmB;IAEhJ9C,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAA8C,UAAA,qBAAoB;IAGb9C,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAA8C,UAAA,UAAA+D,OAAA,CAAAtG,WAAA,CAAAC,SAAA,yBAAuD,YAAAqG,OAAA,CAAA1B,cAAA,oBAAAnF,EAAA,CAAAU,eAAA,KAAAqC,GAAA;IAQvD/C,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAA8C,UAAA,UAAA+D,OAAA,CAAAtG,WAAA,CAAAC,SAAA,4BAA0D,YAAAqG,OAAA,CAAA1B,cAAA,oBAAAnF,EAAA,CAAAU,eAAA,KAAAqC,GAAA;IAWnD/C,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAA8C,UAAA,YAAA+D,OAAA,CAAArB,WAAA,CAAAC,QAAA,CAAkC,qCAAAzF,EAAA,CAAAU,eAAA,KAAAqC,GAAA;IAQ9B/C,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAA8C,UAAA,YAAA+D,OAAA,CAAArB,WAAA,CAAAI,SAAA,CAAmC,mBAAA5F,EAAA,CAAAU,eAAA,KAAAqC,GAAA,WAAA8D,OAAA,CAAAjC,eAAA;IAMK5E,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAA8C,UAAA,SAAA+D,OAAA,CAAAjC,eAAA,UAA8B;IAC9B5E,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAA8C,UAAA,SAAA+D,OAAA,CAAAjC,eAAA,SAA6B;IAKrD5E,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAA8C,UAAA,UAAA+D,OAAA,CAAAtG,WAAA,CAAAC,SAAA,qBAAmD;IAMrFR,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAA8C,UAAA,qBAAoB;IAIL9C,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAA8C,UAAA,mBAAkB,YAAA+D,OAAA,CAAAR,oBAAA,CAAAC,MAAA,oBAAAtG,EAAA,CAAAU,eAAA,KAAAqC,GAAA,cAAA8D,OAAA,CAAAC,UAAA,wBAAAD,OAAA,CAAAtG,WAAA,CAAAC,SAAA,uCAAAqG,OAAA,CAAAtG,WAAA,CAAAC,SAAA;IAYvBR,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAA8C,UAAA,YAAA+D,OAAA,CAAAR,oBAAA,CAAAI,GAAA,CAAsC,mBAAAzG,EAAA,CAAAU,eAAA,KAAAqC,GAAA;IAU7C/C,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAA8C,UAAA,iBAAgB,eAAA+D,OAAA,CAAAE,kBAAA,CAAA9D,IAAA,cAAA4D,OAAA,CAAAE,kBAAA,CAAA7D,IAAA,aAAA2D,OAAA,CAAAG,kBAAA,aAAAH,OAAA,CAAAI,eAAA,aAAAJ,OAAA,CAAAK,mBAAA,cAAAL,OAAA,CAAAM,cAAA,CAAA5D,IAAA,CAAAsD,OAAA,yBAAA7G,EAAA,CAAAU,eAAA,KAAA8C,GAAA,oCAAAqD,OAAA,CAAAE,kBAAA,CAAAtD,MAAA,YAAAoD,OAAA,CAAAR,oBAAA;;;;;;IAtQhDrG,EAAA,CAAAC,cAAA,gBAA8D;IAE3CD,EAAA,CAAA4B,UAAA,sBAAAwF,wEAAA/E,MAAA;MAAArC,EAAA,CAAA8B,aAAA,CAAAuF,IAAA;MAAA,MAAAC,OAAA,GAAAtH,EAAA,CAAAiC,aAAA;MAAA,OAAYjC,EAAA,CAAAkC,WAAA,CAAAoF,OAAA,CAAAC,WAAA,CAAAlF,MAAA,CAAmB;IAAA,EAAC;IACvCrC,EAAA,CAAAC,cAAA,qBAA4E;IAKWD,EAAA,CAAAE,MAAA,GAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9HH,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA+B;IACqCD,EAAA,CAAAE,MAAA,IAAmD;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtJH,EAAA,CAAAC,cAAA,eAAqC;IAG1BD,EAAA,CAAA4B,UAAA,2BAAA4F,0EAAAnF,MAAA;MAAArC,EAAA,CAAA8B,aAAA,CAAAuF,IAAA;MAAA,MAAAI,OAAA,GAAAzH,EAAA,CAAAiC,aAAA;MAAA,OAAajC,EAAA,CAAAkC,WAAA,CAAAuF,OAAA,CAAAC,WAAA,CAAAC,QAAA,GAAAtF,MAAA,CACnD;IAAA,EADwE;IAFzCrC,EAAA,CAAAG,YAAA,EAQE;IAIVH,EAAA,CAAAC,cAAA,eAAgD;IAC5CD,EAAA,CAAA2C,SAAA,iBAAwE;IACxE3C,EAAA,CAAAC,cAAA,eAA0B;IACtBD,EAAA,CAAA6F,UAAA,KAAA+B,kDAAA,oBAAsL;IACtL5H,EAAA,CAAA6F,UAAA,KAAAgC,kDAAA,oBAA2J;IAC3J7H,EAAA,CAAA6F,UAAA,KAAAiC,kDAAA,oBAAqJ;IACzJ9H,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA+B;IACkCD,EAAA,CAAAE,MAAA,IAAgD;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChJH,EAAA,CAAAC,cAAA,eAA0B;IAGfD,EAAA,CAAA4B,UAAA,2BAAAmG,0EAAA1F,MAAA;MAAArC,EAAA,CAAA8B,aAAA,CAAAuF,IAAA;MAAA,MAAAW,OAAA,GAAAhI,EAAA,CAAAiC,aAAA;MAAA,OAAajC,EAAA,CAAAkC,WAAA,CAAA8F,OAAA,CAAAN,WAAA,CAAAO,KAAA,GAAA5F,MAAA,CACnD;IAAA,EADqE,2BAAA0F,0EAAA;MAAA/H,EAAA,CAAA8B,aAAA,CAAAuF,IAAA;MAAA,MAAAa,OAAA,GAAAlI,EAAA,CAAAiC,aAAA;MAAA,OAMdjC,EAAA,CAAAkC,WAAA,CAAAgG,OAAA,CAAAC,iBAAA,CAAkB,OAAO,CAAC;IAAA,EANZ;IAFtCnI,EAAA,CAAAG,YAAA,EASE;IAIVH,EAAA,CAAAC,cAAA,eAAgD;IAC5CD,EAAA,CAAA2C,SAAA,iBAAqE;IACrE3C,EAAA,CAAAC,cAAA,eAA0B;IACtBD,EAAA,CAAA6F,UAAA,KAAAuC,kDAAA,oBAAgL;IAChLpI,EAAA,CAAA6F,UAAA,KAAAwC,kDAAA,oBAAwJ;IACxJrI,EAAA,CAAA6F,UAAA,KAAAyC,kDAAA,oBAA+I;IAC/ItI,EAAA,CAAA6F,UAAA,KAAA0C,kDAAA,oBAAgL;IACpLvI,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA+B;IACkCD,EAAA,CAAAE,MAAA,IAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrHH,EAAA,CAAAC,cAAA,eAA0B;IAGfD,EAAA,CAAA4B,UAAA,2BAAA4G,0EAAAnG,MAAA;MAAArC,EAAA,CAAA8B,aAAA,CAAAuF,IAAA;MAAA,MAAAoB,OAAA,GAAAzI,EAAA,CAAAiC,aAAA;MAAA,OAAajC,EAAA,CAAAkC,WAAA,CAAAuG,OAAA,CAAAf,WAAA,CAAAgB,KAAA,GAAArG,MAAA,CACnD;IAAA,EADqE;IAFtCrC,EAAA,CAAAG,YAAA,EAME;IAIVH,EAAA,CAAAC,cAAA,eAAgD;IAC5CD,EAAA,CAAA2C,SAAA,iBAAqE;IACrE3C,EAAA,CAAAC,cAAA,eAA0B;IACtBD,EAAA,CAAA6F,UAAA,KAAA8C,kDAAA,oBAA+I;IAC/I3I,EAAA,CAAA6F,UAAA,KAAA+C,kDAAA,oBAAgL;IACpL5I,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA+B;IACwCD,EAAA,CAAAE,MAAA,IAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjIH,EAAA,CAAAC,cAAA,eAA0B;IAKnBD,EAAA,CAAA4B,UAAA,2BAAAiH,6EAAAxG,MAAA;MAAArC,EAAA,CAAA8B,aAAA,CAAAuF,IAAA;MAAA,MAAAyB,OAAA,GAAA9I,EAAA,CAAAiC,aAAA;MAAA,OAAajC,EAAA,CAAAkC,WAAA,CAAA4G,OAAA,CAAApB,WAAA,CAAAqB,WAAA,GAAA1G,MAAA,CAC/C;IAAA,EADuE;IAI/CrC,EAAA,CAAAG,YAAA,EAAW;IAIZH,EAAA,CAAAC,cAAA,eAAgD;IAC5CD,EAAA,CAAA2C,SAAA,iBAA2E;IAC3E3C,EAAA,CAAAC,cAAA,eAA0B;IACtBD,EAAA,CAAA6F,UAAA,KAAAmD,kDAAA,oBAA8J;IAClKhJ,EAAA,CAAAG,YAAA,EAAM;IAGdH,EAAA,CAAAC,cAAA,eAAyB;IAI2CD,EAAA,CAAAE,MAAA,IAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvHH,EAAA,CAAAC,cAAA,eAAiB;IACPD,EAAA,CAAAE,MAAA,IAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIhEH,EAAA,CAAA6F,UAAA,KAAAoD,gDAAA,kBAKM;IAENjJ,EAAA,CAAAC,cAAA,eAA+B;IACkCD,EAAA,CAAAE,MAAA,IAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpHH,EAAA,CAAAC,cAAA,eAAmE;IAC1DD,EAAA,CAAAE,MAAA,IAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAavDH,EAAA,CAAA6F,UAAA,KAAAqD,uDAAA,0BAsBa;IACblJ,EAAA,CAAA6F,UAAA,KAAAsD,uDAAA,0BAsBa;IACbnJ,EAAA,CAAA6F,UAAA,KAAAuD,uDAAA,2BA0Fa;IACjBpJ,EAAA,CAAAG,YAAA,EAAY;;;;IAlRIH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAA4C,qBAAA,WAAAyG,MAAA,CAAA9I,WAAA,CAAAC,SAAA,8BAA+D;IAKYR,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAAgJ,MAAA,CAAA9I,WAAA,CAAAC,SAAA,2BAAmD;IAElHR,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAsJ,kBAAA,MAAAD,MAAA,CAAA5H,eAAA,CAAA8H,QAAA,MACJ;IAIgEvJ,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAAgJ,MAAA,CAAA9I,WAAA,CAAAC,SAAA,2BAAmD;IAIxGR,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAA8C,UAAA,YAAAuG,MAAA,CAAA3B,WAAA,CAAAC,QAAA,CAAkC,oDAAA0B,MAAA,CAAA9I,WAAA,CAAAC,SAAA;IAaZR,EAAA,CAAAI,SAAA,GAA2F;IAA3FJ,EAAA,CAAA8C,UAAA,SAAAuG,MAAA,CAAAG,WAAA,CAAAC,QAAA,CAAA9B,QAAA,CAAA+B,KAAA,KAAAL,MAAA,CAAAG,WAAA,CAAAC,QAAA,CAAA9B,QAAA,CAAAgC,MAAA,kBAAAN,MAAA,CAAAG,WAAA,CAAAC,QAAA,CAAA9B,QAAA,CAAAgC,MAAA,CAAAC,QAAA,EAA2F;IAC3F5J,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAA8C,UAAA,SAAAuG,MAAA,CAAAG,WAAA,CAAAC,QAAA,CAAA9B,QAAA,CAAAgC,MAAA,kBAAAN,MAAA,CAAAG,WAAA,CAAAC,QAAA,CAAA9B,QAAA,CAAAgC,MAAA,CAAAE,SAAA,CAAqD;IACrD7J,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAA8C,UAAA,SAAAuG,MAAA,CAAAG,WAAA,CAAAC,QAAA,CAAA9B,QAAA,CAAAgC,MAAA,kBAAAN,MAAA,CAAAG,WAAA,CAAAC,QAAA,CAAA9B,QAAA,CAAAgC,MAAA,CAAAG,OAAA,CAAmD;IAKvB9J,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,iBAAA,CAAAgJ,MAAA,CAAA9I,WAAA,CAAAC,SAAA,wBAAgD;IAIlGR,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAA8C,UAAA,YAAAuG,MAAA,CAAA3B,WAAA,CAAAO,KAAA,CAA+B,oDAAAoB,MAAA,CAAA9I,WAAA,CAAAC,SAAA;IAcTR,EAAA,CAAAI,SAAA,GAAqF;IAArFJ,EAAA,CAAA8C,UAAA,SAAAuG,MAAA,CAAAG,WAAA,CAAAC,QAAA,CAAAxB,KAAA,CAAAyB,KAAA,KAAAL,MAAA,CAAAG,WAAA,CAAAC,QAAA,CAAAxB,KAAA,CAAA0B,MAAA,kBAAAN,MAAA,CAAAG,WAAA,CAAAC,QAAA,CAAAxB,KAAA,CAAA0B,MAAA,CAAAC,QAAA,EAAqF;IACrF5J,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAA8C,UAAA,SAAAuG,MAAA,CAAAG,WAAA,CAAAC,QAAA,CAAAxB,KAAA,CAAA0B,MAAA,kBAAAN,MAAA,CAAAG,WAAA,CAAAC,QAAA,CAAAxB,KAAA,CAAA0B,MAAA,CAAAE,SAAA,CAAkD;IAClD7J,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAA8C,UAAA,SAAAuG,MAAA,CAAAG,WAAA,CAAAC,QAAA,CAAAxB,KAAA,CAAA0B,MAAA,kBAAAN,MAAA,CAAAG,WAAA,CAAAC,QAAA,CAAAxB,KAAA,CAAA0B,MAAA,CAAAG,OAAA,CAAgD;IAChD9J,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAA8C,UAAA,SAAAuG,MAAA,CAAAU,cAAA,CAAoB;IAKQ/J,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,iBAAA,CAAAgJ,MAAA,CAAA9I,WAAA,CAAAC,SAAA,wBAAgD;IAIlGR,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAA8C,UAAA,YAAAuG,MAAA,CAAA3B,WAAA,CAAAgB,KAAA,CAA+B,gBAAAW,MAAA,CAAA9I,WAAA,CAAAC,SAAA;IAWTR,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAA8C,UAAA,SAAAuG,MAAA,CAAAG,WAAA,CAAAC,QAAA,CAAAf,KAAA,CAAAiB,MAAA,kBAAAN,MAAA,CAAAG,WAAA,CAAAC,QAAA,CAAAf,KAAA,CAAAiB,MAAA,CAAAG,OAAA,CAAgD;IAChD9J,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAA8C,UAAA,SAAAuG,MAAA,CAAAW,cAAA,CAAoB;IAKchK,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAAgJ,MAAA,CAAA9I,WAAA,CAAAC,SAAA,8BAAsD;IAIlHR,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAA8C,UAAA,qBAAoB,YAAAuG,MAAA,CAAA3B,WAAA,CAAAqB,WAAA,mCAAAM,MAAA,CAAA9I,WAAA,CAAAC,SAAA;IAaMR,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAA8C,UAAA,SAAAuG,MAAA,CAAAG,WAAA,CAAAC,QAAA,CAAAV,WAAA,CAAAY,MAAA,kBAAAN,MAAA,CAAAG,WAAA,CAAAC,QAAA,CAAAV,WAAA,CAAAY,MAAA,CAAAE,SAAA,CAAwD;IAQ7B7J,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAAgJ,MAAA,CAAA9I,WAAA,CAAAC,SAAA,2BAAmD;IAErGR,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAK,iBAAA,CAAAgJ,MAAA,CAAAY,iBAAA,CAAAZ,MAAA,CAAA5H,eAAA,CAAAyI,IAAA,EAA2C;IAIzBlK,EAAA,CAAAI,SAAA,GAAmG;IAAnGJ,EAAA,CAAA8C,UAAA,SAAAuG,MAAA,CAAA3B,WAAA,CAAAyC,QAAA,IAAAd,MAAA,CAAAe,cAAA,CAAAC,KAAA,IAAAhB,MAAA,CAAA3B,WAAA,CAAAyC,QAAA,IAAAd,MAAA,CAAAe,cAAA,CAAAE,MAAA,CAAmG;IAQlEtK,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAK,iBAAA,CAAAgJ,MAAA,CAAA9I,WAAA,CAAAC,SAAA,uBAA+C;IAEnGR,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAAgJ,MAAA,CAAAkB,cAAA,GAAoB;IAa2BvK,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAA8C,UAAA,SAAAuG,MAAA,CAAA3B,WAAA,CAAAyC,QAAA,IAAAd,MAAA,CAAAtJ,SAAA,CAAAyK,SAAA,CAAAC,QAAA,CAA2D;IAuB/DzK,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAA8C,UAAA,SAAAuG,MAAA,CAAA3B,WAAA,CAAAyC,QAAA,IAAAd,MAAA,CAAAtJ,SAAA,CAAAyK,SAAA,CAAAC,QAAA,CAA0D;IAuBzDzK,EAAA,CAAAI,SAAA,GAAmE;IAAnEJ,EAAA,CAAA8C,UAAA,SAAAuG,MAAA,CAAAc,QAAA,IAAAd,MAAA,CAAAe,cAAA,CAAAK,QAAA,IAAApB,MAAA,CAAAlE,cAAA,SAAmE;;;ADjLxJ,OAAM,MAAOuF,uBAAuB;EAChCC,YAAoBC,KAAqB,EACrBC,MAAc,EACfC,WAAwB,EACxBC,cAA8B,EAC7BC,eAAgC,EAChCC,eAAgC,EACjC1K,WAA6B,EAC7B2K,oBAA0C,EACzCC,WAAwB,EACxBC,eAAqC,EACrCC,cAA8B;IAV9B,KAAAT,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACP,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IAChB,KAAA1K,WAAW,GAAXA,WAAW;IACX,KAAA2K,oBAAoB,GAApBA,oBAAoB;IACnB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IAyBlC,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAvB,cAAc,GAAY,KAAK;IAC/B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAuB,WAAW,GAAkB,IAAI;IAmCjC,KAAA3G,eAAe,GAAG,IAAI;IACtB,KAAAkC,UAAU,GAAG,EAAE;IACf;IACA,KAAA0E,kBAAkB,GAAe,EAAE;IAcnC,KAAAnF,oBAAoB,GAAG;MAACI,GAAG,EAAG,IAAI;MAAEH,MAAM,EAAG;IAAI,CAAC;IAElD,KAAAd,WAAW,GAAG;MAACC,QAAQ,EAAE,EAAE;MAAEG,SAAS,EAAE;IAAE,CAAC;IAI3C,KAAA6F,iBAAiB,GAAa,KAAK;IAichB,KAAA1L,SAAS,GAAGA,SAAS;EArhBxC;EAsFA2L,QAAQA,CAAA;IACJ,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACN,cAAc,CAACM,QAAQ;IAC5C,IAAI,CAACxB,QAAQ,GAAG,IAAI,CAACwB,QAAQ,CAACzB,IAAI;IAClC,IAAI,CAAC0B,SAAS,GAAG,IAAI,CAACD,QAAQ,CAACE,EAAE;IACjC,IAAI,CAACzB,cAAc,GAAGrK,SAAS,CAACyK,SAAS;IACzC,IAAI,CAACsB,KAAK,GAAG,CACT;MAAEC,KAAK,EAAE,IAAI,CAACxL,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MAAEwL,UAAU,EAAC;IAAU,CAAG,EACpF;MAAED,KAAK,EAAE,IAAI,CAACxL,WAAW,CAACC,SAAS,CAAC,yBAAyB;IAAC,CAAE,CACnE;IACD,IAAI,CAACyL,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEF,UAAU,EAAE;IAAG,CAAE;IAEnD,IAAIG,eAAe,GAAG,CAClB;MAACC,IAAI,EAAE,IAAI,CAAC7L,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAAC6L,KAAK,EAACtM,SAAS,CAACyK,SAAS,CAACH;IAAK,CAAC,EAC5F;MAAC+B,IAAI,EAAE,IAAI,CAAC7L,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAAC6L,KAAK,EAACtM,SAAS,CAACyK,SAAS,CAACC;IAAQ,CAAC,EAClG;MAAC2B,IAAI,EAAE,IAAI,CAAC7L,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAAC6L,KAAK,EAACtM,SAAS,CAACyK,SAAS,CAAC8B;IAAQ,CAAC,EAClG;MAACF,IAAI,EAAE,IAAI,CAAC7L,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAAC6L,KAAK,EAACtM,SAAS,CAACyK,SAAS,CAAC+B;IAAQ,CAAC,EAClG;MAACH,IAAI,EAAE,IAAI,CAAC7L,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;MAAC6L,KAAK,EAACtM,SAAS,CAACyK,SAAS,CAACF;IAAM,CAAC,CACjG;IACD,IAAI,CAACkC,cAAc,GAAGL,eAAe;IACrC,IAAI,CAACzE,WAAW,GAAG;MACf+E,WAAW,EAAE,IAAI;MACjB9E,QAAQ,EAAE,IAAI;MACdM,KAAK,EAAE,IAAI;MACXS,KAAK,EAAE,IAAI;MACXyB,QAAQ,EAAE,IAAI,CAACqC,cAAc,CAAC,CAAC,CAAC,CAACH,KAAK;MACtCK,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,IAAI;MACX5D,WAAW,EAAE,IAAI;MACjB6D,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE;KACd;IACD,IAAI,CAAC9F,kBAAkB,GAAG;MACtB9D,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRO,MAAM,EAAE;KACX;IACD,IAAI,CAACuD,kBAAkB,GAAG,CACtB;MACIoF,IAAI,EAAE,KAAK;MACXU,GAAG,EAAE,MAAM;MACX5J,IAAI,EAAE,KAAK;MACX6J,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIb,IAAI,EAAE,QAAQ;MACdU,GAAG,EAAE,QAAQ;MACb5J,IAAI,EAAE,KAAK;MACX6J,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IAED,IAAI,CAAChG,eAAe,GAAG;MACnBiG,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IAED,IAAI,CAACjG,mBAAmB,GAAG;MACvBkG,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;IACD,IAAI,CAACC,SAAS,EAAE;IAChB,IAAI,CAACjL,wBAAwB,GAAG;MAC5BC,OAAO,EAAE,IAAI;MACbiL,aAAa,EAAEC,MAAM,CAAC,IAAI,CAAC9B,SAAS;KACvC;IACD,IAAI,CAACzI,kBAAkB,GAAG,CACtB;MACIiJ,IAAI,EAAE,IAAI,CAAC7L,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/DsM,GAAG,EAAE,MAAM;MACX5J,IAAI,EAAE,KAAK;MACX6J,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIb,IAAI,EAAE,IAAI,CAAC7L,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/DsM,GAAG,EAAE,MAAM;MACX5J,IAAI,EAAE,KAAK;MACX6J,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IACD,IAAI,CAAC7J,eAAe,GAAG;MACnB8J,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD,IAAI,CAACnK,kBAAkB,GAAG;MACtBC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRO,MAAM,EAAE;KACX;IACD,IAAI,CAACJ,mBAAmB,GAAG;MACvB+J,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;IACD,IAAI,CAACvJ,wBAAwB,GAAG;MAC5BxB,OAAO,EAAE,IAAI;MACbiL,aAAa,EAAEC,MAAM,CAAC,IAAI,CAAC9B,SAAS,CAAC;MACrC+B,WAAW,EAAE;KAChB;IACD,IAAI,CAACtJ,kBAAkB,GAAG,CACtB;MACI+H,IAAI,EAAE,IAAI,CAAC7L,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/DsM,GAAG,EAAE,cAAc;MACnB5J,IAAI,EAAE,KAAK;MACX6J,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIb,IAAI,EAAE,IAAI,CAAC7L,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/DsM,GAAG,EAAE,cAAc;MACnB5J,IAAI,EAAE,KAAK;MACX6J,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIb,IAAI,EAAE,IAAI,CAAC7L,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/DsM,GAAG,EAAE,cAAc;MACnB5J,IAAI,EAAE,KAAK;MACX6J,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IACD,IAAI,CAAC3I,eAAe,GAAG;MACnB4I,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD,IAAI,CAAC/I,kBAAkB,GAAG;MACtBnB,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRO,MAAM,EAAE;KACX;IACD,IAAI,CAACc,mBAAmB,GAAG;MACvB6I,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;EACL;EAEAK,qBAAqBA,CAAA;IACjB,IAAG,IAAI,CAAClG,WAAW,CAACyC,QAAQ,IAAI,IAAI,CAACoB,WAAW,IAAI,IAAI,CAAC/B,WAAW,EAAC;MACjE,IAAI,CAAC+B,WAAW,GAAG,IAAI,CAAC7D,WAAW,CAACyC,QAAQ;MAC5C,IAAI,CAACX,WAAW,CAACqE,GAAG,CAAC,UAAU,CAAC,CAACC,KAAK,EAAE;MACxC,IAAI,CAACtE,WAAW,CAACqE,GAAG,CAAC,WAAW,CAAC,CAACC,KAAK,EAAE;;EAEjD;EAEA3F,iBAAiBA,CAAC+B,IAAI;IAClB,IAAIjC,KAAK,GAAG,IAAI;IAChB,IAAIsB,QAAQ,GAAG,IAAI;IACnB,IAAGW,IAAI,IAAI,aAAa,EAAC;MACrB,IAAI,CAACoB,iBAAiB,GAAG,KAAK;MAC9B/B,QAAQ,GAAG,IAAI,CAAC7B,WAAW,CAAC+E,WAAW;MACvC,IAAGlD,QAAQ,IAAI,IAAI,CAAC9H,eAAe,CAAC8H,QAAQ,EAAE;KACjD,MAAK,IAAGW,IAAI,IAAI,OAAO,EAAC;MACrB,IAAI,CAACH,cAAc,GAAG,KAAK;MAC3B9B,KAAK,GAAG,IAAI,CAACP,WAAW,CAACO,KAAK;MAC9B,IAAGA,KAAK,IAAI,IAAI,CAACxG,eAAe,CAACwG,KAAK,EAAE;;IAG5C,IAAI8F,EAAE,GAAG,IAAI;IAEb,IAAI,CAAC3C,eAAe,CAAC4C,GAAG,CAAC9D,IAAI,EAAE,IAAI,CAACa,cAAc,CAACkD,YAAY,CAAC1K,IAAI,CAAC,IAAI,CAACwH,cAAc,CAAC,EAAE9C,KAAK,EAAEsB,QAAQ,EAAE2E,QAAQ,IAAG;MACnH,IAAGA,QAAQ,IAAI,CAAC,EAAC;QACb,IAAGhE,IAAI,IAAI,aAAa,EAAC;UACrB6D,EAAE,CAACzC,iBAAiB,GAAG,IAAI;SAC9B,MAAI;UACDyC,EAAE,CAAChE,cAAc,GAAG,IAAI;;;IAGpC,CAAC,CAAC;EACN;EAEAoE,cAAcA,CAAA;IACV,IAAIC,QAAQ,GAAG;MACX7E,QAAQ,EAAE,IAAI,CAAC7B,WAAW,CAAC+E,WAAW;MACtC9E,QAAQ,EAAE,IAAI,CAACD,WAAW,CAACC,QAAQ;MACnCoB,WAAW,EAAE,IAAI,CAACrB,WAAW,CAACqB,WAAW;MACzCd,KAAK,EAAE,IAAI,CAACP,WAAW,CAACO,KAAK;MAC7BS,KAAK,EAAE,IAAI,CAAChB,WAAW,CAACgB,KAAK;MAC7BwB,IAAI,EAAE,IAAI,CAACxC,WAAW,CAACyC,QAAQ;MAC/BxI,YAAY,EAAE,IAAI,CAAC+F,WAAW,CAACgF,QAAQ;MACvC2B,OAAO,EAAE,CAAC,IAAI,CAAC3G,WAAW,CAACiF,KAAK,IAAG,EAAE,EAAE2B,GAAG,CAACC,EAAE,IAAIA,EAAE,CAAC1C,EAAE,CAAC;MACvD2C,aAAa,EAAE,CAAC,IAAI,CAAC9G,WAAW,CAACmF,SAAS,IAAI,EAAE,EAAEyB,GAAG,CAACC,EAAE,IAAIA,EAAE,CAAC1C,EAAE,CAAC;MAClE4C,SAAS,EAAG,IAAI,CAACtJ,cAAc;MAC/BuJ,QAAQ,EAAG,IAAI,CAAClJ,WAAW,CAACI,SAAS;MACrC6F,iBAAiB,EAAE,IAAI,CAACA;KAC3B;IACD,IAAG2C,QAAQ,CAAC1F,KAAK,IAAI,IAAI,EAAC;MACtB,IAAG0F,QAAQ,CAAC1F,KAAK,CAACiG,UAAU,CAAC,GAAG,CAAC,EAAC;QAC9BP,QAAQ,CAAC1F,KAAK,GAAG,IAAI,GAAC0F,QAAQ,CAAC1F,KAAK,CAACkG,SAAS,CAAC,CAAC,EAAER,QAAQ,CAAC1F,KAAK,CAACmG,MAAM,CAAC;OAC3E,MAAK,IAAGT,QAAQ,CAAC1F,KAAK,CAACmG,MAAM,IAAI,CAAC,IAAIT,QAAQ,CAAC1F,KAAK,CAACmG,MAAM,IAAI,EAAE,EAAC;QAC/DT,QAAQ,CAAC1F,KAAK,GAAG,IAAI,GAAC0F,QAAQ,CAAC1F,KAAK;;;IAG5C,IAAI,CAACwC,oBAAoB,CAAC4D,MAAM,EAAE;IAClC,IAAIf,EAAE,GAAG,IAAI;IACb,IAAI,CAAChD,cAAc,CAACgE,aAAa,CAACX,QAAQ,EAAGF,QAAQ,IAAG;MACpDH,EAAE,CAAC7C,oBAAoB,CAAC8D,OAAO,CAACjB,EAAE,CAACxN,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;MACvFuN,EAAE,CAAClD,MAAM,CAACoE,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;IACpC,CAAC,EAAE,IAAI,EAAE,MAAI;MACTlB,EAAE,CAAC7C,oBAAoB,CAACgE,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAC,SAASA,CAAA;IACL,IAAI,CAACtE,MAAM,CAACoE,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACtC;EAEAzB,SAASA,CAAA;IACL,IAAIO,EAAE,GAAG,IAAI;IACb,IAAIqB,SAAS,GAAG,IAAI,CAACzD,QAAQ,CAACE,EAAE;IAChC,IAAI,CAACd,cAAc,CAACsE,WAAW,CAAGnB,QAAQ,IAAG;MACzCH,EAAE,CAACtM,eAAe,GAAGyM,QAAQ;MAC7BH,EAAE,CAACrG,WAAW,CAAC+E,WAAW,GAAGyB,QAAQ,CAAC3E,QAAQ;MAC9CwE,EAAE,CAACrG,WAAW,CAACC,QAAQ,GAAGuG,QAAQ,CAACvG,QAAQ;MAC3CoG,EAAE,CAACrG,WAAW,CAACO,KAAK,GAAGiG,QAAQ,CAACjG,KAAK;MACrC8F,EAAE,CAACrG,WAAW,CAACqB,WAAW,GAAGmF,QAAQ,CAACnF,WAAW;MACjDgF,EAAE,CAACrG,WAAW,CAACgB,KAAK,GAAGwF,QAAQ,CAACxF,KAAK;MACrCqF,EAAE,CAACrG,WAAW,CAACgF,QAAQ,GAAGwB,QAAQ,CAACvM,YAAY;MAC/CoM,EAAE,CAACrG,WAAW,CAACyC,QAAQ,GAAG+D,QAAQ,CAAChE,IAAI;MACvC6D,EAAE,CAACvE,WAAW,GAAGuE,EAAE,CAAC5C,WAAW,CAACmE,KAAK,CAACvB,EAAE,CAACrG,WAAW,CAAC;MACrDqG,EAAE,CAACvE,WAAW,CAACC,QAAQ,CAACgD,WAAW,CAAC8C,OAAO,EAAE;MAC7CxB,EAAE,CAACvE,WAAW,CAACC,QAAQ,CAACU,QAAQ,CAACoF,OAAO,EAAE;MAC1C;MACA,IAAIxB,EAAE,CAACrG,WAAW,CAACyC,QAAQ,IAAIpK,SAAS,CAACyK,SAAS,CAACC,QAAQ,EAAE;QACzDsD,EAAE,CAACyB,kCAAkC,EAAE;QACvCzB,EAAE,CAACxL,wBAAwB,CAACkL,aAAa,GAAGC,MAAM,CAACK,EAAE,CAACnC,SAAS,CAAC;QAChEmC,EAAE,CAAC/J,wBAAwB,CAACyJ,aAAa,GAAGC,MAAM,CAACK,EAAE,CAACnC,SAAS,CAAC;QAChEmC,EAAE,CAAC/J,wBAAwB,CAAC2J,WAAW,GAAG,CAACI,EAAE,CAACtM,eAAe,CAACoL,SAAS,IAAG,EAAE,EAAEyB,GAAG,CAACmB,QAAQ,IAAIA,QAAQ,CAACC,UAAU,CAAC;;MAEtH3B,EAAE,CAAC5I,cAAc,GAAG+I,QAAQ,CAACO,SAAS;MACtCV,EAAE,CAACvC,kBAAkB,GAAG0C,QAAQ,CAACyB,SAAS,GAAEzB,QAAQ,CAACyB,SAAS,CAACrB,GAAG,CAACC,EAAE,KAAI;QAAC1C,EAAE,EAAE0C;MAAE,CAAC,CAAC,CAAC,GAAG,CAAC;QAAC1C,EAAE,EAAC,CAAC;MAAE,CAAC,CAAC;MAChGkC,EAAE,CAACvI,WAAW,CAACI,SAAS,GAAGsI,QAAQ,CAACQ,QAAQ;MAC5CX,EAAE,CAACvI,WAAW,CAACC,QAAQ,GAAGyI,QAAQ,CAAC3E,QAAQ;MAC3CqG,UAAU,CAAC;QACP7B,EAAE,CAAC8B,WAAW,CAAC,KAAK,CAAC;MACzB,CAAC,EAAC,GAAG,CAAC;IACV,CAAC,CAAC;EACN;EAEAA,WAAWA,CAACC,OAAO;IACf,IAAI/B,EAAE,GAAG,IAAI;IACb,IAAI,CAAChD,cAAc,CAAC8E,WAAW,CAAC,IAAI,CAACnI,WAAW,CAACyC,QAAQ,EAAG+D,QAAQ,IAAG;MACnEH,EAAE,CAACgC,QAAQ,GAAG7B,QAAQ,CAACI,GAAG,CAACC,EAAE,IAAG;QAC5B,OAAO;UACH1C,EAAE,EAAE0C,EAAE,CAAC1C,EAAE;UACTO,IAAI,EAAEmC,EAAE,CAACnC;SACZ;MACL,CAAC,CAAC;MACF2B,EAAE,CAACvE,WAAW,CAACC,QAAQ,CAACkD,KAAK,CAAC4C,OAAO,EAAE;MACvC,IAAGO,OAAO,EAAC;QACP/B,EAAE,CAACrG,WAAW,CAACiF,KAAK,GAAG,IAAI;OAC9B,MAAI;QACD,IAAIqD,OAAO,GAAG,CAACjC,EAAE,CAACtM,eAAe,CAACkL,KAAK,IAAI,EAAE,EAAE2B,GAAG,CAACC,EAAE,IAAIA,EAAE,CAAC0B,MAAM,CAAC;QACnElC,EAAE,CAACrG,WAAW,CAACiF,KAAK,GAAGoB,EAAE,CAACgC,QAAQ,CAACG,MAAM,CAAC3B,EAAE,IAAIyB,OAAO,CAACG,QAAQ,CAAC5B,EAAE,CAAC1C,EAAE,CAAC,CAAC;;IAEhF,CAAC,CAAC;EACN;EAEAuE,kBAAkBA,CAAA;IACd,OAAO,CAAC,IAAI,CAAC3O,eAAe,CAACoL,SAAS,IAAI,EAAE,EAAEyB,GAAG,CAACC,EAAE,IAAIA,EAAE,CAAC8B,YAAY,GAAG,KAAK,GAAG9B,EAAE,CAAC+B,YAAY,CAAC,CAACC,cAAc,EAAE;EACvH;EAEAtG,iBAAiBA,CAACoC,KAAK;IACnB,IAAGA,KAAK,IAAItM,SAAS,CAACyK,SAAS,CAACH,KAAK,EAAC;MAClC,OAAO,IAAI,CAAC9J,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;KAC9D,MAAK,IAAG6L,KAAK,IAAItM,SAAS,CAACyK,SAAS,CAACC,QAAQ,EAAC;MAC3C,OAAO,IAAI,CAAClK,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;KACjE,MAAK,IAAG6L,KAAK,IAAItM,SAAS,CAACyK,SAAS,CAAC8B,QAAQ,EAAC;MAC3C,OAAO,IAAI,CAAC/L,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;KACjE,MAAK,IAAG6L,KAAK,IAAItM,SAAS,CAACyK,SAAS,CAAC+B,QAAQ,EAAC;MAC3C,OAAO,IAAI,CAAChM,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;KACjE,MAAK,IAAG6L,KAAK,IAAItM,SAAS,CAACyK,SAAS,CAACF,MAAM,EAAC;MACzC,OAAO,IAAI,CAAC/J,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;KAC/D,MAAI;MACD,OAAO,EAAE;;EAEjB;EAEA+J,cAAcA,CAAA;IACV,OAAO,CAAC,IAAI,CAAC9I,eAAe,CAACkL,KAAK,IAAI,EAAE,EAAE2B,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACiC,QAAQ,CAAC,CAACD,cAAc,EAAE;EACrF;EACAhJ,WAAWA,CAACkJ,KAAK;IACb,MAAMC,OAAO,GAAGD,KAAK,CAACE,aAAa,CAACC,MAAM,CAACC,SAAS;IACpD,IAAI9C,EAAE,GAAG,IAAI;IACb,IAAI0C,KAAK,IAAIC,OAAO,CAACP,QAAQ,CAAC,IAAI,CAAC5P,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC,CAAC,EAAE;MAChFuN,EAAE,CAACnH,gBAAgB,EAAE;KACxB,MAAM,IAAI6J,KAAK,IAAIC,OAAO,CAACP,QAAQ,CAAC,IAAI,CAAC5P,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC,CAAC,EAAE;MACtFuN,EAAE,CAAClK,gBAAgB,EAAE;KACxB,MAAM,IAAI4M,KAAK,IAAIC,OAAO,CAACP,QAAQ,CAAC,IAAI,CAAC5P,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC,CAAC,EAAE;MAC1FuN,EAAE,CAAC5L,gBAAgB,EAAE;;EAE7B;EACAA,gBAAgBA,CAAC2O,IAAK;IAClB,IAAI/C,EAAE,GAAG,IAAI;IACb,IAAI+C,IAAI,EAAE;MACN/C,EAAE,CAAC/K,kBAAkB,CAACC,IAAI,GAAG,CAAC;;IAElC8K,EAAE,CAACzK,cAAc,CAACyK,EAAE,CAAC/K,kBAAkB,CAACC,IAAI,EAAE8K,EAAE,CAAC/K,kBAAkB,CAACE,IAAI,EAAE6K,EAAE,CAAC/K,kBAAkB,CAACS,MAAM,EAAEsK,EAAE,CAACxL,wBAAwB,CAAC;EACxI;EACAsB,gBAAgBA,CAACiN,IAAK;IAClB,IAAI/C,EAAE,GAAG,IAAI;IACb,IAAI+C,IAAI,EAAE;MACN/C,EAAE,CAAC3J,kBAAkB,CAACnB,IAAI,GAAG,CAAC;;IAElC8K,EAAE,CAACvJ,cAAc,CAACuJ,EAAE,CAAC3J,kBAAkB,CAACnB,IAAI,EAAE8K,EAAE,CAAC3J,kBAAkB,CAAClB,IAAI,EAAE6K,EAAE,CAAC3J,kBAAkB,CAACX,MAAM,EAAEsK,EAAE,CAAC/J,wBAAwB,CAAC;EACxI;EACAV,cAAcA,CAACL,IAAI,EAAE8N,KAAK,EAAEC,IAAI,EAAEC,MAAM;IACpC,IAAIlD,EAAE,GAAG,IAAI;IACb,IAAI,CAAC/K,kBAAkB,CAACC,IAAI,GAAGA,IAAI;IACnC,IAAI,CAACD,kBAAkB,CAACE,IAAI,GAAG6N,KAAK;IACpC,IAAI,CAAC/N,kBAAkB,CAACS,MAAM,GAAGuN,IAAI;IACrC,IAAIE,UAAU,GAAG;MACbjO,IAAI;MACJC,IAAI,EAAE6N,KAAK;MACXC;KACH;IACDG,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7O,wBAAwB,CAAC,CAAC8O,OAAO,CAACvE,GAAG,IAAG;MACrD,IAAG,IAAI,CAACvK,wBAAwB,CAACuK,GAAG,CAAC,IAAI,IAAI,EAAC;QAC1CoE,UAAU,CAACpE,GAAG,CAAC,GAAG,IAAI,CAACvK,wBAAwB,CAACuK,GAAG,CAAC;;IAE5D,CAAC,CAAC;IACFiB,EAAE,CAAC7C,oBAAoB,CAAC4D,MAAM,EAAE;IAChC,IAAI,CAAC9D,eAAe,CAACsG,mBAAmB,CAACJ,UAAU,EAAE,IAAI,CAAC3O,wBAAwB,EAAE2L,QAAQ,IAAG;MAC3FH,EAAE,CAAC3K,eAAe,GAAG;QACjB8J,OAAO,EAAEgB,QAAQ,CAAChB,OAAO;QACzBC,KAAK,EAAEe,QAAQ,CAACqD;OACnB;IACL,CAAC,EAAE,IAAI,EAAE,MAAI;MACTxD,EAAE,CAAC7C,oBAAoB,CAACgE,OAAO,EAAE;IACrC,CAAC,CAAC;IACF;EACJ;;EACA1K,cAAcA,CAACvB,IAAI,EAAE8N,KAAK,EAAEC,IAAI,EAAEC,MAAM;IACpC,IAAIlD,EAAE,GAAG,IAAI;IACb,IAAI,CAAC3J,kBAAkB,CAACnB,IAAI,GAAGA,IAAI;IACnC,IAAI,CAACmB,kBAAkB,CAAClB,IAAI,GAAG6N,KAAK;IACpC,IAAI,CAAC3M,kBAAkB,CAACX,MAAM,GAAGuN,IAAI;IACrC,IAAIE,UAAU,GAAG;MACbjO,IAAI;MACJC,IAAI,EAAE6N,KAAK;MACXC;KACH;IACD;IACA;IACA;IACA;IACA;IACAjD,EAAE,CAAC7C,oBAAoB,CAAC4D,MAAM,EAAE;IAChC,IAAI,CAAC7D,eAAe,CAACuG,mBAAmB,CAACN,UAAU,EAAE,IAAI,CAAClN,wBAAwB,EAAEkK,QAAQ,IAAG;MAC3FH,EAAE,CAACzJ,eAAe,GAAG;QACjB4I,OAAO,EAAEgB,QAAQ,CAAChB,OAAO;QACzBC,KAAK,EAAEe,QAAQ,CAACqD;OACnB;MACD;IACJ,CAAC,EAAE,IAAI,EAAE,MAAI;MACTxD,EAAE,CAAC7C,oBAAoB,CAACgE,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EACAM,kCAAkCA,CAAA;IAC9B,IAAI,CAACxM,kBAAkB,GAAG;MACtBC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRO,MAAM,EAAE;KACX;IACD,IAAI,CAACW,kBAAkB,GAAG;MACtBnB,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRO,MAAM,EAAE;KACX;EACL;EAEA0D,cAAcA,CAAClE,IAAI,EAAE8N,KAAK,EAAEC,IAAI,EAAEC,MAAM;IACpC,IAAIlD,EAAE,GAAG,IAAI;IACb,IAAI,CAAChH,kBAAkB,CAAC9D,IAAI,GAAGA,IAAI;IACnC,IAAI,CAAC8D,kBAAkB,CAAC7D,IAAI,GAAG6N,KAAK;IACpC,IAAI,CAAChK,kBAAkB,CAACtD,MAAM,GAAGuN,IAAI;IACrC,IAAIE,UAAU,GAAG;MACbjO,IAAI;MACJC,IAAI,EAAE6N,KAAK;MACXC,IAAI;MACJS,cAAc,EAAE,IAAI,CAACjG,kBAAkB,CAAC8C,GAAG,CAACC,EAAE,IAAEA,EAAE,CAAC1C,EAAE,CAAC,CAAC6F,IAAI,CAAC,GAAG;KAClE;IACDP,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC/K,oBAAoB,CAAC,CAACgL,OAAO,CAACvE,GAAG,IAAG;MACjD,IAAG,IAAI,CAACzG,oBAAoB,CAACyG,GAAG,CAAC,IAAI,IAAI,EAAC;QACtCoE,UAAU,CAACpE,GAAG,CAAC,GAAG,IAAI,CAACzG,oBAAoB,CAACyG,GAAG,CAAC;;IAExD,CAAC,CAAC;IACF6E,OAAO,CAACC,GAAG,CAACV,UAAU,CAAC;IACvBnD,EAAE,CAAC7C,oBAAoB,CAAC4D,MAAM,EAAE;IAChC,IAAI,CAAC/D,cAAc,CAAC5D,cAAc,CAAC+J,UAAU,EAAEhD,QAAQ,IAAG;MACtDH,EAAE,CAAC9G,eAAe,GAAG;QACjBiG,OAAO,EAAEgB,QAAQ,CAAChB,OAAO;QACzBC,KAAK,EAAEe,QAAQ,CAACqD;OACnB;IACL,CAAC,EAAE,IAAI,EAAE,MAAI;MACTxD,EAAE,CAAC7C,oBAAoB,CAACgE,OAAO,EAAE;IACrC,CAAC,CAAC;IACF,IAAI2C,SAAS,GAAG;MAAC,GAAGX;IAAU,CAAC;IAC/BW,SAAS,CAAC3O,IAAI,GAAG,SAAS;IAC1B,IAAI,CAAC6H,cAAc,CAAC5D,cAAc,CAAC0K,SAAS,EAAE3D,QAAQ,IAAG;MACrDH,EAAE,CAACjH,UAAU,GAAG,CAAC,GAAG,IAAIgL,GAAG,CAAC5D,QAAQ,CAAChB,OAAO,CAACoB,GAAG,CAACC,EAAE,IAAEA,EAAE,CAACjI,MAAM,CAAC,CAAC,CAAC;MACjEyH,EAAE,CAACjH,UAAU,GAAGiH,EAAE,CAACjH,UAAU,CAACwH,GAAG,CAACC,EAAE,KAAG;QACnCnC,IAAI,EAAGmC,EAAE;QACTlC,KAAK,EAAGkC;OACX,CAAC,CAAC;IACP,CAAC,EAAE,IAAI,EAAE,MAAI;MACTR,EAAE,CAAC7C,oBAAoB,CAACgE,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEA6C,aAAaA,CAACC,CAAC;IACX,IAAIC,KAAK,GAAG,gEAAgE;IAC5E,IAAIC,KAAK,GAAG,EAAE;IACd,KAAI,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MACvBD,KAAK,IAAID,KAAK,CAACG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGL,KAAK,CAACpD,MAAM,CAAC,CAAC;;IAE5D,OAAOqD,KAAK;EAChB;EAEAhM,QAAQA,CAAA;IACJ,IAAI,CAACV,WAAW,CAACI,SAAS,GAAG,IAAI,CAACmM,aAAa,CAAC,EAAE,CAAC;EACvD;EAEAnL,gBAAgBA,CAACkK,IAAK;IAClB,IAAI/C,EAAE,GAAG,IAAI;IACb4D,OAAO,CAACC,GAAG,CAAC7D,EAAE,CAAC1H,oBAAoB,CAAC;IACpC,IAAGyK,IAAI,EAAE;MACL/C,EAAE,CAAChH,kBAAkB,CAAC9D,IAAI,GAAG,CAAC;;IAElC8K,EAAE,CAAC5G,cAAc,CAAC4G,EAAE,CAAChH,kBAAkB,CAAC9D,IAAI,EAAE8K,EAAE,CAAChH,kBAAkB,CAAC7D,IAAI,EAAE6K,EAAE,CAAChH,kBAAkB,CAACtD,MAAM,EAAEsK,EAAE,CAAC1H,oBAAoB,CAAC;EACpI;;;uBAhiBSqE,uBAAuB,EAAA1K,EAAA,CAAAuS,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzS,EAAA,CAAAuS,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA1S,EAAA,CAAAuS,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAA5S,EAAA,CAAAuS,iBAAA,CAAAM,EAAA,CAAAC,cAAA,GAAA9S,EAAA,CAAAuS,iBAAA,CAAAQ,EAAA,CAAAC,eAAA,GAAAhT,EAAA,CAAAuS,iBAAA,CAAAU,EAAA,CAAAC,eAAA,GAAAlT,EAAA,CAAAuS,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAApT,EAAA,CAAAuS,iBAAA,CAAAc,EAAA,CAAAC,oBAAA,GAAAtT,EAAA,CAAAuS,iBAAA,CAAAgB,EAAA,CAAAC,WAAA,GAAAxT,EAAA,CAAAuS,iBAAA,CAAAkB,EAAA,CAAAC,oBAAA,GAAA1T,EAAA,CAAAuS,iBAAA,CAAAoB,GAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAvBlJ,uBAAuB;MAAAmJ,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpBpCnU,EAAA,CAAAC,cAAA,cAA8D;UAA9BD,EAAA,CAAA4B,UAAA,sBAAAyS,0DAAA;YAAA,OAAYD,GAAA,CAAAjG,cAAA,EAAgB;UAAA,EAAC;UAC7DnO,EAAA,CAAAC,cAAA,aAAqG;UACjGD,EAAA,CAAA2C,SAAA,sBAAoF;UACpF3C,EAAA,CAAAC,cAAA,aAAwE;UAEyDD,EAAA,CAAA4B,UAAA,mBAAA0S,2DAAA;YAAA,OAASF,GAAA,CAAAjF,SAAA,EAAW;UAAA,EAAC;UAACnP,EAAA,CAAAG,YAAA,EAAW;UAC1JH,EAAA,CAAA2C,SAAA,kBAA8M;UAClN3C,EAAA,CAAAG,YAAA,EAAM;UAIdH,EAAA,CAAA6F,UAAA,IAAA0O,yCAAA,sBAuRS;UACTvU,EAAA,CAAAG,YAAA,EAAO;;;UAnSDH,EAAA,CAAA8C,UAAA,cAAAsR,GAAA,CAAA5K,WAAA,CAAyB;UAEYxJ,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAA8C,UAAA,UAAAsR,GAAA,CAAAtI,KAAA,CAAe,SAAAsI,GAAA,CAAAnI,IAAA;UAGpCjM,EAAA,CAAAI,SAAA,GAAuD;UAAvDJ,EAAA,CAAA8C,UAAA,UAAAsR,GAAA,CAAA7T,WAAA,CAAAC,SAAA,yBAAuD;UACvDR,EAAA,CAAAI,SAAA,GAAqD;UAArDJ,EAAA,CAAA8C,UAAA,UAAAsR,GAAA,CAAA7T,WAAA,CAAAC,SAAA,uBAAqD,aAAA4T,GAAA,CAAA5K,WAAA,CAAAgL,OAAA,IAAAJ,GAAA,CAAApK,cAAA,IAAAoK,GAAA,CAAA9I,iBAAA,IAAA8I,GAAA,CAAArK,cAAA;UAKhC/J,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAA8C,UAAA,SAAAsR,GAAA,CAAA5K,WAAA,CAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}