{"ast": null, "code": "import { CONSTANTS } from \"src/app/service/comon/constants\";\nimport { ComponentBase } from \"src/app/component.base\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/service/rating-plan/RatingPlanService\";\nimport * as i2 from \"../../../service/account/AccountService\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"primeng/breadcrumb\";\nimport * as i7 from \"primeng/inputtext\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"../../common-module/table/table.component\";\nimport * as i10 from \"primeng/dropdown\";\nimport * as i11 from \"primeng/card\";\nimport * as i12 from \"primeng/dialog\";\nimport * as i13 from \"primeng/inputswitch\";\nimport * as i14 from \"primeng/radiobutton\";\nimport * as i15 from \"primeng/panel\";\nconst _c0 = function () {\n  return [\"create\"];\n};\nfunction AppRatingPlanListComponent_p_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-button\", 43);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"global.button.create\"))(\"routerLink\", i0.ɵɵpureFunction0(2, _c0));\n  }\n}\nfunction AppRatingPlanListComponent_span_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 10)(1, \"p-dropdown\", 44);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRatingPlanListComponent_span_58_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.searchInfoUser.provinceCode = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 45);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx_r1.searchInfoUser.provinceCode)(\"options\", ctx_r1.listProvince)(\"filter\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.tranService.translate(\"ratingPlan.label.province\"));\n  }\n}\nfunction AppRatingPlanListComponent_span_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.tranService.translate(\"account.label.province\"), \": \", ctx_r2.provinceInfo, \"\");\n  }\n}\nfunction AppRatingPlanListComponent_p_dialog_67_p_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 68);\n    i0.ɵɵlistener(\"click\", function AppRatingPlanListComponent_p_dialog_67_p_button_2_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.active());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"label\", ctx_r6.tranService.translate(\"global.button.active\"));\n  }\n}\nfunction AppRatingPlanListComponent_p_dialog_67_p_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 68);\n    i0.ɵɵlistener(\"click\", function AppRatingPlanListComponent_p_dialog_67_p_button_3_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.approve());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"label\", ctx_r7.tranService.translate(\"global.button.approve\"));\n  }\n}\nfunction AppRatingPlanListComponent_p_dialog_67_p_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 68);\n    i0.ɵɵlistener(\"click\", function AppRatingPlanListComponent_p_dialog_67_p_button_4_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.suspend());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"label\", ctx_r8.tranService.translate(\"global.button.suspend\"));\n  }\n}\nfunction AppRatingPlanListComponent_p_dialog_67_div_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"span\", 51);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 69);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r9.tranService.translate(\"ratingPlan.label.province\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r9.myProvices);\n  }\n}\nfunction AppRatingPlanListComponent_p_dialog_67_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"span\", 70);\n    i0.ɵɵlistener(\"click\", function AppRatingPlanListComponent_p_dialog_67_div_79_Template_span_click_1_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.openDialogAddCustomerAccount());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r10.tranService.translate(\"account.label.showCustomerAccount\"));\n  }\n}\nconst _c1 = function () {\n  return {\n    width: \"980px\"\n  };\n};\nconst _c2 = function (a0) {\n  return [a0];\n};\nconst _c3 = function () {\n  return {\n    \"width\": \"calc(100% + 16px)\"\n  };\n};\nfunction AppRatingPlanListComponent_p_dialog_67_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dialog\", 28);\n    i0.ɵɵlistener(\"visibleChange\", function AppRatingPlanListComponent_p_dialog_67_Template_p_dialog_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.isShowModalDetail = $event);\n    });\n    i0.ɵɵelementStart(1, \"div\", 46);\n    i0.ɵɵtemplate(2, AppRatingPlanListComponent_p_dialog_67_p_button_2_Template, 1, 1, \"p-button\", 47);\n    i0.ɵɵtemplate(3, AppRatingPlanListComponent_p_dialog_67_p_button_3_Template, 1, 1, \"p-button\", 47);\n    i0.ɵɵtemplate(4, AppRatingPlanListComponent_p_dialog_67_p_button_4_Template, 1, 1, \"p-button\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p-card\", 48)(6, \"div\", 49)(7, \"div\", 50)(8, \"div\", 29)(9, \"span\", 51);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 52);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 29)(14, \"span\", 51);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 53);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 29)(19, \"span\", 51);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 54)(22, \"span\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 29)(25, \"span\", 51);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 52);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 29)(30, \"span\", 51);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 52);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 29)(35, \"span\", 51);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\", 53);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 50)(40, \"div\", 29)(41, \"span\", 55);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"span\", 52);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 29)(46, \"span\", 52)(47, \"span\")(48, \"p-radioButton\", 56);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRatingPlanListComponent_p_dialog_67_Template_p_radioButton_ngModelChange_48_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.ratingPlanInfo.subscriptionType = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(49, \" \\u00A0 \");\n    i0.ɵɵelementStart(50, \"span\");\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(52, \"span\", 57)(53, \"span\")(54, \"p-radioButton\", 58);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRatingPlanListComponent_p_dialog_67_Template_p_radioButton_ngModelChange_54_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.ratingPlanInfo.subscriptionType = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(55, \" \\u00A0 \");\n    i0.ɵɵelementStart(56, \"span\");\n    i0.ɵɵtext(57);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(58, \"div\", 29)(59, \"span\", 55);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"span\", 52);\n    i0.ɵɵtext(62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 29)(64, \"span\", 55);\n    i0.ɵɵtext(65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"span\", 52);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 29)(69, \"span\", 55);\n    i0.ɵɵtext(70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"div\", 59)(72, \"p-inputSwitch\", 60);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRatingPlanListComponent_p_dialog_67_Template_p_inputSwitch_ngModelChange_72_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.checkedReload = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(73, \"div\", 29)(74, \"span\", 55);\n    i0.ɵɵtext(75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"span\", 52);\n    i0.ɵɵtext(77);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(78, AppRatingPlanListComponent_p_dialog_67_div_78_Template, 5, 2, \"div\", 61);\n    i0.ɵɵtemplate(79, AppRatingPlanListComponent_p_dialog_67_div_79_Template, 3, 1, \"div\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(80, \"div\", 62)(81, \"span\", 63);\n    i0.ɵɵtext(82);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(83, \"div\", 64)(84, \"div\", 50)(85, \"div\", 29)(86, \"span\", 51);\n    i0.ɵɵtext(87);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"span\", 52);\n    i0.ɵɵtext(89);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(90, \"div\", 62)(91, \"span\", 51);\n    i0.ɵɵtext(92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(93, \"span\", 52);\n    i0.ɵɵtext(94);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(95, \"div\", 50)(96, \"div\", 29)(97, \"span\", 51);\n    i0.ɵɵtext(98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(99, \"span\", 52);\n    i0.ɵɵtext(100);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(101, \"div\", 62)(102, \"span\", 51);\n    i0.ɵɵtext(103);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(104, \"span\", 52);\n    i0.ɵɵtext(105);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(106, \"div\", 65)(107, \"span\", 63);\n    i0.ɵɵtext(108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(109, \"div\", 66)(110, \"p-inputSwitch\", 60);\n    i0.ɵɵlistener(\"ngModelChange\", function AppRatingPlanListComponent_p_dialog_67_Template_p_inputSwitch_ngModelChange_110_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.checkedFlexible = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(111, \"div\", 67)(112, \"div\", 50)(113, \"div\", 29)(114, \"span\", 51);\n    i0.ɵɵtext(115);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(116, \"span\", 52);\n    i0.ɵɵtext(117);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(118, \"div\", 29)(119, \"span\", 51);\n    i0.ɵɵtext(120);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(121, \"span\", 52);\n    i0.ɵɵtext(122);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(123, \"div\", 29)(124, \"span\", 51);\n    i0.ɵɵtext(125);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(126, \"span\", 52);\n    i0.ɵɵtext(127);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(128, \"div\", 29)(129, \"span\", 51);\n    i0.ɵɵtext(130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(131, \"span\", 52);\n    i0.ɵɵtext(132);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(133, \"div\", 29)(134, \"span\", 55);\n    i0.ɵɵtext(135);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(136, \"span\", 52);\n    i0.ɵɵtext(137);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(138, \"div\", 50)(139, \"div\", 29)(140, \"span\", 51);\n    i0.ɵɵtext(141);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(142, \"div\", 29)(143, \"span\", 51);\n    i0.ɵɵtext(144);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(73, _c1));\n    i0.ɵɵproperty(\"header\", ctx_r3.tranService.translate(\"global.menu.detailplan\"))(\"visible\", ctx_r3.isShowModalDetail)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r3.ratingPlanInfo.status == ctx_r3.planStatuses.CREATE_NEW || ctx_r3.ratingPlanInfo.status == ctx_r3.planStatuses.DEACTIVATED) && ctx_r3.checkAuthen(i0.ɵɵpureFunction1(74, _c2, ctx_r3.CONSTANTS.PERMISSIONS.RATING_PLAN.ACTIVE)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.ratingPlanInfo.status == ctx_r3.planStatuses.PENDING && ctx_r3.checkAuthen(i0.ɵɵpureFunction1(76, _c2, ctx_r3.CONSTANTS.PERMISSIONS.RATING_PLAN.APPROVE)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.ratingPlanInfo.status == ctx_r3.planStatuses.ACTIVATED && ctx_r3.checkAuthen(i0.ɵɵpureFunction1(78, _c2, ctx_r3.CONSTANTS.PERMISSIONS.RATING_PLAN.SUPPEND)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(80, _c3));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.planCode\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.code);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.planName\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.status\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r3.getClassStatus(ctx_r3.ratingPlanInfo.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.getNameStatus(ctx_r3.ratingPlanInfo.status));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.dispatchCode\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.dispatchCode);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.customerType\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getNameCustomerType(ctx_r3.ratingPlanInfo.customerType));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.description\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.description);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.subscriptionFee\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate3(\"\", ctx_r3.ratingPlanInfo.subscriptionFee, \"\\u00A0 \\u00A0 \\u00A0 \\u00A0 \", ctx_r3.tranService.translate(\"ratingPlan.text.textDong\"), \"\\u00A0\", ctx_r3.tranService.translate(\"ratingPlan.text.vat\"), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", true)(\"value\", ctx_r3.subscriptionTypes[0].ip)(\"ngModel\", ctx_r3.ratingPlanInfo.subscriptionType);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.subscriptionType.post\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", true)(\"value\", ctx_r3.subscriptionTypes[1].ip)(\"ngModel\", ctx_r3.ratingPlanInfo.subscriptionType);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.subscriptionType.pre\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.cycle\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getCycleTimeUnit(ctx_r3.ratingPlanInfo.cycleTimeUnit));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.cycleInterval\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.cycleInterval);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.reload\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.checkedReload)(\"disabled\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.ratingScope\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getRatingScope(ctx_r3.ratingPlanInfo.ratingScope));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.ratingPlanInfo.ratingScope == ctx_r3.planScopes.PROVINCE || ctx_r3.ratingPlanInfo.ratingScope == ctx_r3.planScopes.CUSTOMER);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.ratingPlanInfo.ratingScope == ctx_r3.planScopes.CUSTOMER);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.flat\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.limitDataUsage\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.limitDataUsage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.dataMax\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.dataMax);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.limitSmsInside\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.limitSmsInside);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.limitSmsOutside\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.limitSmsOutside);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.flexible\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.checkedFlexible)(\"disabled\", true);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.feePerDataUnit\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.feePerDataUnit);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.squeezedSpeed\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.downSpeed);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.feeSmsInside\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.feeSmsInside);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.feeSmsOutside\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.feeSmsOutside);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"ratingPlan.label.maximumFee\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.ratingPlanInfo.maximumFee);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"/\\u00A0 \\u00A0 \\u00A0 \", ctx_r3.ratingPlanInfo.dataRoundUnit, \" \\u00A0 \\u00A0 \\u00A0 \\u00A0 KB\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"/\\u00A0 \\u00A0 \\u00A0 \", ctx_r3.ratingPlanInfo.squeezedSpeed, \"\");\n  }\n}\nconst _c4 = function () {\n  return {\n    width: \"850px\"\n  };\n};\nconst _c5 = function () {\n  return [5, 10, 20, 25, 50];\n};\nconst _c6 = function () {\n  return {\n    \"margin-right\": \"20px\"\n  };\n};\nconst _c7 = function () {\n  return {\n    width: \"1000px\"\n  };\n};\nexport class AppRatingPlanListComponent extends ComponentBase {\n  constructor(ratingPlanService, accountService, formBuilder, injector) {\n    super(injector);\n    this.ratingPlanService = ratingPlanService;\n    this.accountService = accountService;\n    this.formBuilder = formBuilder;\n    this.planSelected = {};\n    this.selectItems = [];\n    this.selectItemsUser = [];\n    this.selectItemsUserOld = [{\n      id: -1,\n      provinceCode: \"\"\n    }];\n    this.isShowDialogAssignPlan = false;\n    this.isShowModalDetail = false;\n    this.response = {};\n    this.isShowDialogShowCustomerAccount = false;\n    this.planStatuses = CONSTANTS.RATING_PLAN_STATUS;\n    this.subscriptionTypes = [];\n    this.provinceInfo = \"\";\n    this.planScopes = CONSTANTS.RATING_PLAN_SCOPE;\n    this.allPermissions = CONSTANTS.PERMISSIONS;\n    this.allUserType = CONSTANTS.USER_TYPE;\n    this.CONSTANTS = CONSTANTS;\n  }\n  ngOnInit() {\n    let me = this;\n    this.optionTableShowCustomerAccount = {\n      hasClearSelected: true,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false,\n      action: null,\n      paginator: false\n    };\n    this.columnsInfoUserForDetail = [{\n      name: this.tranService.translate(\"ratingPlan.label.username\"),\n      key: \"username\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n      // style:{\n      //     cursor: \"pointer\",\n      //     color: \"var(--mainColorText)\"\n      // },\n      // funcGetRouting(item) {\n      //     return [`/plans/detail/${item.id}`]\n      // },\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.fullName\"),\n      key: \"fullName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.email\"),\n      key: \"email\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.province\"),\n      key: \"provinceName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }];\n    this.dataSetAssignPlanForDetail = {\n      content: [],\n      total: 0\n    };\n    this.userType = this.sessionService.userInfo.type;\n    this.items = [{\n      label: this.tranService.translate(\"global.menu.ratingplanmgmt\")\n    }, {\n      label: this.tranService.translate(\"global.menu.listplan\")\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.listStatus = [{\n      name: this.tranService.translate(\"ratingPlan.status.create\"),\n      value: CONSTANTS.RATING_PLAN_STATUS.CREATE_NEW\n    }, {\n      name: this.tranService.translate(\"ratingPlan.status.pending\"),\n      value: CONSTANTS.RATING_PLAN_STATUS.PENDING\n    }, {\n      name: this.tranService.translate(\"ratingPlan.status.activated\"),\n      value: CONSTANTS.RATING_PLAN_STATUS.ACTIVATED\n    }, {\n      name: this.tranService.translate(\"ratingPlan.status.deactivated\"),\n      value: CONSTANTS.RATING_PLAN_STATUS.DEACTIVATED\n    }], this.listCycle = [{\n      name: this.tranService.translate(\"ratingPlan.cycle.day\"),\n      value: CONSTANTS.RATING_PLAN_CYCLE.DAY\n    }, {\n      name: this.tranService.translate(\"ratingPlan.cycle.month\"),\n      value: CONSTANTS.RATING_PLAN_CYCLE.MONTH\n    }];\n    this.listPaidType = [{\n      name: this.tranService.translate(\"ratingPlan.subscriptionType.pre\"),\n      value: CONSTANTS.SUBSCRIPTION_TYPE.PREPAID\n    }, {\n      name: this.tranService.translate(\"ratingPlan.subscriptionType.post\"),\n      value: CONSTANTS.SUBSCRIPTION_TYPE.POSTPAID\n    }];\n    this.listCustomerType = [{\n      name: this.tranService.translate(\"ratingPlan.customerType.personal\"),\n      value: CONSTANTS.CUSTOMER_TYPE.PERSONAL\n    }, {\n      name: this.tranService.translate(\"ratingPlan.customerType.enterprise\"),\n      value: CONSTANTS.CUSTOMER_TYPE.INTERPRISE\n    }\n    // ,\n    // {name: this.tranService.translate(\"ratingPlan.customerType.agency\"), value: CONSTANTS.CUSTOMER_TYPE.AGENCY},\n    ];\n\n    this.listRatingScope = [{\n      name: this.tranService.translate(\"ratingPlan.ratingScope.nativeWide\"),\n      value: CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE\n    }, {\n      name: this.tranService.translate(\"ratingPlan.ratingScope.province\"),\n      value: CONSTANTS.RATING_PLAN_SCOPE.PROVINCE\n    }, {\n      name: this.tranService.translate(\"ratingPlan.ratingScope.customer\"),\n      value: CONSTANTS.RATING_PLAN_SCOPE.CUSTOMER\n    }];\n    this.ratingPlanInfo = {\n      id: null,\n      code: null,\n      name: null,\n      status: null,\n      dispatchCode: null,\n      customerType: null,\n      subscriptionFee: null,\n      subscriptionType: null,\n      ratingScope: null,\n      cycleTimeUnit: null,\n      cycleInterval: null,\n      reload: null,\n      flat: null,\n      limitDataUsage: null,\n      limitSmsOutside: null,\n      limitSmsInside: null,\n      flexible: null,\n      feePerDataUnit: null,\n      squeezedSpeed: null,\n      feeSmsInside: null,\n      feeSmsOutside: null,\n      maximumFee: null,\n      dataRoundUnit: null,\n      downSpeed: null,\n      provinceCode: null,\n      description: null,\n      dataMax: null,\n      userIds: null\n    };\n    this.searchInfo = {\n      id: null,\n      name: null,\n      status: null,\n      cycleTimeUnit: null,\n      paidType: null,\n      customerType: null,\n      ratingScope: null\n    };\n    this.createSearchUserInfo();\n    this.formSearchUser = this.formBuilder.group(this.searchInfoUser);\n    this.formSearch = this.formBuilder.group(this.searchInfo);\n    this.selectItems = [];\n    this.pageNumber = 0;\n    this.pageSize = 10;\n    this.sort = \"name,asc\";\n    this.selectItemsUser = [];\n    this.optionTable = {\n      hasClearSelected: true,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false,\n      action: [{\n        icon: \"pi pi-pencil\",\n        tooltip: this.tranService.translate(\"global.button.edit\"),\n        func: function (id, item) {\n          me.router.navigate([`/plans/update/${id}`]);\n        },\n        funcAppear: function (id, item) {\n          return me.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.UPDATE]);\n        }\n      }, {\n        icon: \"pi pi-trash\",\n        tooltip: this.tranService.translate(\"global.button.delete\"),\n        func: function (id, item) {\n          me.messageCommonService.confirm(me.tranService.translate(\"global.message.titleConfirmDeletePlan\"), me.tranService.translate(\"global.message.confirmDeletePlan\"), {\n            ok: () => {\n              me.ratingPlanService.deleteById(id, response => {\n                me.messageCommonService.success(me.tranService.translate(\"global.message.deleteSuccess\"));\n                me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n              });\n            },\n            cancel: () => {\n              // me.messageCommonService.error(me.tranService.translate(\"global.message.deleteFail\"));\n            }\n          });\n        },\n        funcAppear: function (id, item) {\n          return item.status != CONSTANTS.RATING_PLAN_STATUS.ACTIVATED && me.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.DELETE]);\n        }\n      }, {\n        icon: \"pi pi-file\",\n        tooltip: this.tranService.translate(\"global.button.assignPlan\"),\n        func: function (id, item) {\n          me.selectItemsUser = [];\n          me.pageNumberAssign = 0;\n          me.pageSizeAssign = 10;\n          me.sortAssign = \"createdDate,asc\";\n          const userInfo = JSON.parse(localStorage.getItem('userInfo'));\n          me.ratingPlanService.getById(id, response => {\n            me.planSelected = response;\n            if (me.planSelected.ratingScope == CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE) {\n              me.listProvince = [...me.listProvincOrigins];\n            } else {\n              if (userInfo.provinceCode) {\n                me.listProvince = userInfo.provinceCode;\n              } else if (userInfo.type == 1) {\n                me.listProvince = me.listProvincOrigins.filter(el => response.provinceCode.includes(el.code));\n              } else {\n                me.listProvince = [];\n              }\n            }\n            me.ratingPlanService.getAllAccountPlanAssign(me.planSelected.id, response => {\n              me.selectItemsUser = (response || []).map(el => {\n                return {\n                  provinceCode: el.provinceCode,\n                  userId: el.userId,\n                  id: el.userId\n                };\n              });\n              me.selectItemsUserOld = [...me.selectItemsUser];\n            });\n            me.searchUser(me.pageNumberAssign, me.pageSizeAssign, me.sortAssign, me.searchInfoUser);\n            me.isShowDialogAssignPlan = true;\n          });\n        },\n        funcAppear: function (id, item) {\n          return item.status == CONSTANTS.RATING_PLAN_STATUS.ACTIVATED && me.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.ISSUE]) && item.ratingScope == CONSTANTS.RATING_PLAN_SCOPE.CUSTOMER;\n        }\n      }]\n    };\n    this.optionTableAssignPLan = {\n      hasClearSelected: false,\n      hasShowIndex: true,\n      hasShowChoose: true,\n      hasShowToggleColumn: false\n    };\n    this.columns = [{\n      name: this.tranService.translate(\"ratingPlan.label.planCode\"),\n      key: \"code\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.planName\"),\n      key: \"name\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      style: {\n        cursor: \"pointer\",\n        color: \"var(--mainColorText)\",\n        display: 'inline-block',\n        maxWidth: '400px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      },\n      isShowTooltip: true,\n      funcClick(id, item) {\n        me.planId = id;\n        me.getDetailPLan();\n        me.checkSubscriptionType();\n        me.isShowModalDetail = true;\n      }\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.status\"),\n      key: \"status\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        if (value == CONSTANTS.RATING_PLAN_STATUS.CREATE_NEW) {\n          return me.tranService.translate(\"ratingPlan.status.create\");\n        } else if (value == CONSTANTS.RATING_PLAN_STATUS.PENDING) {\n          return me.tranService.translate(\"ratingPlan.status.pending\");\n        } else if (value == CONSTANTS.RATING_PLAN_STATUS.ACTIVATED) {\n          return me.tranService.translate(\"ratingPlan.status.activated\");\n        } else if (value == CONSTANTS.RATING_PLAN_STATUS.DEACTIVATED) {\n          return me.tranService.translate(\"ratingPlan.status.deactivated\");\n        } else {\n          return \"\";\n        }\n      },\n      funcGetClassname(value) {\n        if (value == CONSTANTS.RATING_PLAN_STATUS.CREATE_NEW) {\n          return ['p-2', 'text-white', \"bg-cyan-300\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.RATING_PLAN_STATUS.PENDING) {\n          return ['p-2', 'text-white', \"bg-red-500\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.RATING_PLAN_STATUS.ACTIVATED) {\n          return ['p-2', 'text-white', \"bg-green-500\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.RATING_PLAN_STATUS.DEACTIVATED) {\n          return ['p-2', 'text-white', \"bg-orange-400\", \"border-round\", \"inline-block\"];\n        } else {\n          return [];\n        }\n      }\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.customerType\"),\n      key: \"customerType\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        if (value == CONSTANTS.CUSTOMER_TYPE.INTERPRISE) {\n          return me.tranService.translate(\"ratingPlan.customerType.enterprise\");\n        } else if (value == CONSTANTS.CUSTOMER_TYPE.PERSONAL) {\n          return me.tranService.translate(\"ratingPlan.customerType.personal\");\n        } else if (value == CONSTANTS.CUSTOMER_TYPE.AGENCY) {\n          return me.tranService.translate(\"ratingPlan.customerType.agency\");\n        } else {\n          return \"\";\n        }\n      }\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.paidType\"),\n      key: \"paidType\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        if (value == CONSTANTS.SUBSCRIPTION_TYPE.POSTPAID) {\n          return me.tranService.translate(\"ratingPlan.subscriptionType.post\");\n        } else if (value == CONSTANTS.SUBSCRIPTION_TYPE.PREPAID) {\n          return me.tranService.translate(\"ratingPlan.subscriptionType.pre\");\n        } else {\n          return \"\";\n        }\n      }\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.ratingScope\"),\n      key: \"ratingScope\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        if (value == CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE) {\n          return me.tranService.translate(\"ratingPlan.ratingScope.nativeWide\");\n        } else if (value == CONSTANTS.RATING_PLAN_SCOPE.CUSTOMER) {\n          return me.tranService.translate(\"ratingPlan.ratingScope.customer\");\n        } else if (value == CONSTANTS.RATING_PLAN_SCOPE.PROVINCE) {\n          return me.tranService.translate(\"ratingPlan.ratingScope.province\");\n        } else {\n          return \"\";\n        }\n      }\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.cycle\"),\n      key: \"cycleTimeUnit\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        if (value == CONSTANTS.RATING_PLAN_CYCLE.DAY) {\n          return me.tranService.translate(\"ratingPlan.cycle.day\");\n        } else if (value == CONSTANTS.RATING_PLAN_CYCLE.MONTH) {\n          return me.tranService.translate(\"ratingPlan.cycle.month\");\n        } else {\n          return \"\";\n        }\n      }\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.subscriptionFee\"),\n      key: \"subscriptionFee\",\n      size: \"150px\",\n      align: \"right\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        return me.utilService.convertNumberToString(value);\n      }\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.limitDataUsage\"),\n      key: \"limitDataUsage\",\n      size: \"150px\",\n      align: \"right\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        return me.utilService.convertNumberToString(value);\n      }\n    }];\n    this.dataSetAssignPlan = {\n      content: [],\n      total: 0\n    };\n    this.getListProvince();\n    this.columnsInfoUser = [{\n      name: this.tranService.translate(\"ratingPlan.label.username\"),\n      key: \"username\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      style: {\n        cursor: \"pointer\",\n        color: \"var(--mainColorText)\"\n      },\n      funcGetRouting(item) {\n        return [`/plans/detail/${item.id}`];\n      }\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.fullName\"),\n      key: \"fullName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.email\"),\n      key: \"email\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"ratingPlan.label.province\"),\n      key: \"provinceCode\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        for (let i = 0; i < me.listProvincOrigins.length; i++) {\n          if (value == me.listProvincOrigins[i].code) {\n            return `${me.listProvincOrigins[i].name} (${me.listProvincOrigins[i].code})`;\n          }\n        }\n        return null;\n      }\n    }];\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n  }\n  ngAfterContentChecked() {\n    if (this.isShowDialogAssignPlan == false) {\n      this.formSearchUser.reset();\n    }\n  }\n  onSubmitSearch() {\n    this.pageNumber = 0;\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n  }\n  onSubmitSearchUser() {\n    this.pageNumberAssign = 0;\n    this.searchUser(this.pageNumberAssign, this.pageSizeAssign, this.sortAssign, this.searchInfoUser);\n  }\n  createSearchUserInfo() {\n    this.searchInfoUser = {\n      username: null,\n      fullName: null,\n      email: null,\n      provinceCode: null\n    };\n  }\n  checkInValidAssignPlan() {\n    if (this.selectItemsUser.length == 0 && this.selectItemsUserOld.length == 0) {\n      return true;\n    }\n    let flag = false;\n    for (let i = 0; i < this.selectItemsUser.length; i++) {\n      if ((this.selectItemsUser[i].provinceCode || \"\") == \"\") {\n        flag = true;\n        break;\n      }\n    }\n    return flag;\n  }\n  assignPlan() {\n    let me = this;\n    let oldIds = this.selectItemsUserOld.map(el => el.id);\n    let dataAdd = this.selectItemsUser.filter(el => !oldIds.includes(el.id)).map(el => {\n      return {\n        userId: el.id,\n        provinceCode: el.provinceCode,\n        type: 1\n      };\n    });\n    let currentSelected = this.selectItemsUser.map(el => el.id);\n    let dataRemove = this.selectItemsUserOld.filter(el => !currentSelected.includes(el.id)).map(el => {\n      return {\n        userId: el.id,\n        provinceCode: el.provinceCode,\n        type: -1\n      };\n    });\n    let data = {\n      planId: this.planSelected.id,\n      data: [...dataAdd, ...dataRemove]\n    };\n    if (data.data.length <= 0) {\n      return;\n    }\n    me.ratingPlanService.assignPlan(data.planId, data.data, response => {\n      me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n    });\n    me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n    this.isShowDialogAssignPlan = false;\n  }\n  searchUser(page, limit, sort, params) {\n    let me = this;\n    this.pageNumberAssign = page;\n    this.pageSizeAssign = limit;\n    this.sortAssign = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort,\n      type: CONSTANTS.USER_TYPE.CUSTOMER,\n      ratingPlanId: this.planSelected.id\n    };\n    Object.keys(this.searchInfoUser).forEach(key => {\n      if (this.searchInfoUser[key] != null) {\n        dataParams[key] = this.searchInfoUser[key];\n      }\n    });\n    if ((dataParams[\"provinceCode\"] || \"\").trim().length == 0) {\n      if (this.userType == CONSTANTS.USER_TYPE.ADMIN) {\n        if (this.planSelected.ratingScope == CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE) {\n          dataParams[\"provinceCode\"] = \"\";\n        } else {\n          if (this.planSelected.provinceCode) {\n            dataParams[\"provinceCode\"] = this.planSelected.provinceCode.toLocaleString();\n          } else {\n            dataParams[\"provinceCode\"] = [];\n          }\n        }\n      } else {\n        dataParams[\"provinceCode\"] = this.sessionService.userInfo.provinceCode;\n      }\n    }\n    // me.messageCommonService.onload();//checkfix\n    this.ratingPlanService.searchUser(dataParams, response => {\n      me.dataSetAssignPlan = {\n        content: response.content,\n        total: response.totalElements\n      };\n    });\n  }\n  search(page, limit, sort, params) {\n    let me = this;\n    this.pageNumber = page;\n    this.pageSize = limit;\n    this.sort = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    Object.keys(this.searchInfo).forEach(key => {\n      if (this.searchInfo[key] != null) {\n        dataParams[key] = this.searchInfo[key];\n      }\n    });\n    this.dataSet = {\n      content: [],\n      total: 0\n    };\n    me.messageCommonService.onload();\n    this.ratingPlanService.search(dataParams, response => {\n      me.dataSet = {\n        content: response.content,\n        total: response.totalElements\n      };\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  getListProvince() {\n    let me = this;\n    this.ratingPlanService.getListProvince(response => {\n      this.listProvince = response.map(el => {\n        if (el.code == me.sessionService.userInfo.provinceCode) {\n          me.provinceInfo = `${el.name} - ${el.code}`;\n        }\n        return {\n          ...el,\n          display: `${el.name} - ${el.code}`\n        };\n      });\n      this.listProvincOrigins = [...this.listProvince];\n    });\n  }\n  getDetailPLan() {\n    let me = this;\n    this.messageCommonService.onload();\n    me.ratingPlanService.getById(Number(me.planId), response => {\n      me.response = response;\n      me.ratingPlanInfo.id = response.id;\n      me.ratingPlanInfo.code = response.code;\n      me.ratingPlanInfo.name = response.name;\n      me.ratingPlanInfo.status = response.status;\n      me.ratingPlanInfo.dispatchCode = response.dispatchCode;\n      me.ratingPlanInfo.customerType = response.customerType;\n      me.ratingPlanInfo.subscriptionFee = response.subscriptionFee;\n      me.ratingPlanInfo.subscriptionType = response.paidType;\n      me.ratingPlanInfo.ratingScope = response.ratingScope;\n      me.ratingPlanInfo.cycleTimeUnit = response.cycleTimeUnit;\n      me.ratingPlanInfo.cycleInterval = response.cycleInterval;\n      me.ratingPlanInfo.reload = response.reload;\n      me.ratingPlanInfo.flat = response.flat;\n      me.ratingPlanInfo.limitDataUsage = response.limitDataUsage;\n      me.ratingPlanInfo.limitSmsOutside = response.limitSmsOutside;\n      me.ratingPlanInfo.limitSmsInside = response.limitSmsInside;\n      me.ratingPlanInfo.flexible = response.flexible;\n      me.ratingPlanInfo.feePerDataUnit = response.feePerDataUnit;\n      me.ratingPlanInfo.squeezedSpeed = response.squeezedSpeed;\n      me.ratingPlanInfo.feeSmsInside = response.feeSmsInside;\n      me.ratingPlanInfo.feeSmsOutside = response.feeSmsOutside;\n      me.ratingPlanInfo.maximumFee = response.maximumFee;\n      me.ratingPlanInfo.dataRoundUnit = response.dataRoundUnit;\n      me.ratingPlanInfo.downSpeed = response.downSpeed;\n      me.ratingPlanInfo.provinceCode = response.provinceCode;\n      me.ratingPlanInfo.description = response.description;\n      me.ratingPlanInfo.dataMax = response.dataMax;\n      me.ratingPlanInfo.userIds = response.userIds;\n      me.getReload(me.ratingPlanInfo.reload);\n      me.getFlexible(me.ratingPlanInfo.flexible);\n      me.myProvices = \"\";\n      if (response.provinceCode != null) {\n        me.accountService.getListProvinceByCode(response.provinceCode, data => {\n          me.provinces = data.map(el => {\n            return {\n              code: el.code,\n              name: `${el.name}`\n            };\n          });\n          me.provinces.forEach(el => {\n            if (me.ratingPlanInfo.provinceCode.includes(el.code)) {\n              me.myProvices += `${el.name}, `;\n            }\n          });\n          if (me.myProvices.length > 0) {\n            me.myProvices = me.myProvices.substring(0, me.myProvices.length - 2);\n          }\n        });\n      }\n      me.accountService.getUserAssignedOnRatingPlan({\n        ratingPlanId: response.id\n      }, resp => {\n        this.dataSetAssignPlanForDetail = {\n          content: resp,\n          total: resp ? resp.length : 0\n        };\n      });\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  getReload(value) {\n    if (value == CONSTANTS.RELOAD.YES) {\n      return this.checkedReload = true;\n    } else if (value == CONSTANTS.RELOAD.NO) {\n      return this.checkedReload = false;\n    }\n    return \"\";\n  }\n  getFlexible(value) {\n    if (value == CONSTANTS.FLEXIBLE.YES) {\n      return this.checkedFlexible = true;\n    } else if (value == CONSTANTS.FLEXIBLE.NO) {\n      return this.checkedFlexible = false;\n    }\n    return \"\";\n  }\n  getClassStatus(value) {\n    if (value == CONSTANTS.RATING_PLAN_STATUS.ACTIVATED) {\n      return ['p-2', \"text-teal-800\", \"bg-teal-100\", \"border-round\", \"inline-block\"];\n    } else if (value == CONSTANTS.RATING_PLAN_STATUS.CREATE_NEW) {\n      return ['p-2', \"text-primary-600\", \"bg-primary-100\", \"border-round\", \"inline-block\"];\n    } else if (value == CONSTANTS.RATING_PLAN_STATUS.PENDING) {\n      return ['p-2', \"text-red-700\", \"bg-red-100\", \"border-round\", \"inline-block\"];\n    } else if (value == CONSTANTS.RATING_PLAN_STATUS.DEACTIVATED) {\n      return ['p-2', \"text-orange-700\", \"bg-orange-100\", \"border-round\", \"inline-block\"];\n    }\n    return [];\n  }\n  getNameCustomerType(value) {\n    if (value == CONSTANTS.CUSTOMER_TYPE.PERSONAL) {\n      return this.tranService.translate(\"ratingPlan.customerType.personal\");\n    } else if (value == CONSTANTS.CUSTOMER_TYPE.INTERPRISE) {\n      return this.tranService.translate(\"ratingPlan.customerType.enterprise\");\n    } else if (value == CONSTANTS.CUSTOMER_TYPE.AGENCY) {\n      return this.tranService.translate(\"ratingPlan.customerType.agency\");\n    }\n    return \"\";\n  }\n  checkSubscriptionType() {\n    this.subscriptionTypes = [{\n      type: this.tranService.translate(\"ratingPlan.subscriptionType.post\"),\n      ip: CONSTANTS.SUBSCRIPTION_TYPE.POSTPAID\n    }, {\n      type: this.tranService.translate(\"ratingPlan.subscriptionType.pre\"),\n      ip: CONSTANTS.SUBSCRIPTION_TYPE.PREPAID\n    }];\n  }\n  getCycleTimeUnit(value) {\n    if (value == CONSTANTS.CYCLE_TIME_UNITS.DAY) {\n      return this.tranService.translate(\"ratingPlan.cycle.day\");\n    } else if (value == CONSTANTS.CYCLE_TIME_UNITS.MONTH) {\n      return this.tranService.translate(\"ratingPlan.cycle.month\");\n    }\n    return \"\";\n  }\n  getRatingScope(value) {\n    if (value == CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE) {\n      return this.tranService.translate(\"ratingPlan.ratingScope.nativeWide\");\n    } else if (value == CONSTANTS.RATING_PLAN_SCOPE.CUSTOMER) {\n      return this.tranService.translate(\"ratingPlan.ratingScope.customer\");\n    } else if (value == CONSTANTS.RATING_PLAN_SCOPE.PROVINCE) {\n      return this.tranService.translate(\"ratingPlan.ratingScope.province\");\n    }\n    return \"\";\n  }\n  getNameStatus(value) {\n    if (value == CONSTANTS.RATING_PLAN_STATUS.ACTIVATED) {\n      return this.tranService.translate(\"ratingPlan.status.activated\");\n    } else if (value == CONSTANTS.RATING_PLAN_STATUS.CREATE_NEW) {\n      return this.tranService.translate(\"ratingPlan.status.create\");\n    } else if (value == CONSTANTS.RATING_PLAN_STATUS.PENDING) {\n      return this.tranService.translate(\"ratingPlan.status.pending\");\n    } else if (value == CONSTANTS.RATING_PLAN_STATUS.DEACTIVATED) {\n      return this.tranService.translate(\"ratingPlan.status.deactivated\");\n    }\n    return \"\";\n  }\n  active() {\n    let me = this;\n    me.messageCommonService.confirm(me.tranService.translate(\"global.message.confirmActivePlan\"), me.tranService.translate(\"global.message.titleConfirmActivePlan\"), {\n      ok: () => {\n        me.ratingPlanService.activePlan(me.planId, response => {});\n        me.messageCommonService.success(me.tranService.translate(\"global.message.activeSuccess\"));\n        window.location.reload();\n      },\n      cancel: () => {\n        // me.messageCommonService.error(me.tranService.translate(\"global.message.deleteFail\"));\n      }\n    });\n  }\n  approve() {\n    let me = this;\n    me.messageCommonService.confirm(me.tranService.translate(\"global.message.confirmApprovePlan\"), me.tranService.translate(\"global.message.titleConfirmApprovePlan\"), {\n      ok: () => {\n        me.ratingPlanService.activePlan(me.planId, response => {\n          // me.router.navigate(['/plans']);\n        });\n        me.messageCommonService.success(me.tranService.translate(\"global.message.approveSuccess\"));\n        window.location.reload();\n      },\n      cancel: () => {\n        // me.messageCommonService.error(me.tranService.translate(\"global.message.deleteFail\"));\n      }\n    });\n  }\n  suspend() {\n    let me = this;\n    me.messageCommonService.confirm(me.tranService.translate(\"global.message.confirmSuspendPlan\"), me.tranService.translate(\"global.message.titleConfirmSuspendPlan\"), {\n      ok: () => {\n        me.ratingPlanService.suspendPlan(me.planId, response => {\n          // me.router.navigate(['/plans']);\n        });\n        me.messageCommonService.success(me.tranService.translate(\"global.message.suspendSuuccess\"));\n        window.location.reload();\n      },\n      cancel: () => {\n        // me.messageCommonService.error(me.tranService.translate(\"global.message.deleteFail\"));\n      }\n    });\n  }\n  changeManageLevel() {}\n  openDialogAddCustomerAccount() {\n    this.isShowDialogShowCustomerAccount = true;\n  }\n  static {\n    this.ɵfac = function AppRatingPlanListComponent_Factory(t) {\n      return new (t || AppRatingPlanListComponent)(i0.ɵɵdirectiveInject(i1.RatingPlanService), i0.ɵɵdirectiveInject(i2.AccountService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppRatingPlanListComponent,\n      selectors: [[\"app-rating-plan-list\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 71,\n      vars: 96,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [\"styleClass\", \"p-button-info\", \"icon\", \"\", \"routerLinkActive\", \"router-link-active\", 3, \"label\", \"routerLink\", 4, \"ngIf\"], [1, \"pb-2\", \"pt-3\", \"vnpt-field-set\", 3, \"formGroup\", \"ngSubmit\"], [3, \"toggleable\", \"header\"], [1, \"grid\", \"search-grid-3\"], [1, \"col-3\"], [1, \"p-float-label\"], [\"pInputText\", \"\", \"pInputText\", \"\", \"id\", \"name\", \"formControlName\", \"name\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"name\"], [\"styleClass\", \"w-full\", \"id\", \"status\", \"formControlName\", \"status\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"ngModelChange\"], [\"for\", \"status\", 1, \"label-dropdown\"], [\"styleClass\", \"w-full\", \"id\", \"cycleTimeUnit\", \"formControlName\", \"cycleTimeUnit\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"ngModelChange\"], [\"for\", \"cycleTimeUnit\", 1, \"label-dropdown\"], [\"styleClass\", \"w-full\", \"id\", \"paidType\", \"formControlName\", \"paidType\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"ngModelChange\"], [\"for\", \"paidType\", 1, \"label-dropdown\"], [\"styleClass\", \"w-full\", \"id\", \"customerType\", \"formControlName\", \"customerType\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"ngModelChange\"], [\"for\", \"customerType\", 1, \"label-dropdown\"], [\"styleClass\", \"w-full\", \"id\", \"ratingScope\", \"formControlName\", \"ratingScope\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"ngModelChange\"], [\"for\", \"ratingScope\", 1, \"label-dropdown\"], [1, \"col-3\", \"pb-0\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"submit\"], [3, \"fieldId\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"pageNumber\", \"pageSize\", \"sort\", \"params\", \"labelTable\", \"selectItemsChange\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"flex\", \"justify-content-center\", \"dialog-push-group\"], [3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"grid\"], [\"pInputText\", \"\", \"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"username\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"username\"], [\"pInputText\", \"\", \"pInputText\", \"\", \"id\", \"fullName\", \"formControlName\", \"fullName\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"fullName\"], [\"class\", \"p-float-label\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"fieldId\", \"pageNumber\", \"pageSize\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"rowsPerPageOptions\", \"scrollHeight\", \"sort\", \"params\", \"selectItemsChange\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"align-items-center\", 2, \"padding-top\", \"30px\"], [\"styleClass\", \"p-button-secondary\", 3, \"label\", \"click\"], [\"styleClass\", \"p-button-info\", 3, \"label\", \"disabled\", \"click\"], [1, \"flex\", \"justify-content-center\", \"dialog-vnpt\"], [3, \"header\", \"visible\", \"modal\", \"style\", \"draggable\", \"resizable\", \"visibleChange\", 4, \"ngIf\"], [\"scrollHeight\", \"300px\", 3, \"fieldId\", \"columns\", \"dataSet\", \"options\"], [\"styleClass\", \"p-button-info\", \"icon\", \"\", \"routerLinkActive\", \"router-link-active\", 3, \"label\", \"routerLink\"], [\"styleClass\", \"w-full\", \"id\", \"provinceCode\", \"optionLabel\", \"display\", \"optionValue\", \"code\", \"formControlName\", \"provinceCode\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"filter\", \"ngModelChange\"], [\"for\", \"provinceCode\"], [1, \"flex\", \"flex-row\", \"justify-content-start\", \"align-items-start\"], [\"styleClass\", \"mr-2 p-button-secondary\", \"icon\", \"\", 3, \"label\", \"click\", 4, \"ngIf\"], [\"styleClass\", \"h-full\"], [1, \"col\", \"ratingPlan-detail\", \"custom-rating-detail\", \"pr-0\", \"flex\", 2, \"border\", \"1px solid black\", \"margin-bottom\", \"20px\"], [1, \"flex-1\"], [1, \"inline-block\", \"col-fixed\", 2, \"min-width\", \"200px\", \"max-width\", \"200px\", \"padding-bottom\", \"5px\"], [1, \"inline-block\", \"col-fixed\"], [1, \"inline-block\", \"col-fixed\", 2, \"min-width\", \"200px\", \"max-width\", \"400px\", \"word-wrap\", \"break-word\", \"white-space\", \"normal\"], [1, \"text-white\", \"w-auto\", \"col\"], [1, \"inline-block\", \"col-fixed\", 2, \"min-width\", \"200px\", \"max-width\", \"200px\"], [\"inputId\", \"typeIp1\", 3, \"disabled\", \"value\", \"ngModel\", \"ngModelChange\"], [1, \"inline-block\", \"col-fixed\", \"radioButton2\", 2, \"padding-left\", \"108px\"], [\"inputId\", \"typeIp2\", 3, \"disabled\", \"value\", \"ngModel\", \"ngModelChange\"], [2, \"padding-top\", \"10px\", \"padding-left\", \"13px\"], [3, \"ngModel\", \"disabled\", \"ngModelChange\"], [\"class\", \"grid\", 4, \"ngIf\"], [1, \"mt-1\", \"grid\"], [1, \"inline-block\", \"col-fixed\", 2, \"font-size\", \"20px\", \"margin-left\", \"10px\"], [\"id\", \"name\", 1, \"col\", \"ratingPlan-detail\", \"custom-rating-detail-limit\", \"pr-0\", \"flex\", 2, \"border\", \"1px solid black\", \"margin-bottom\", \"20px\"], [1, \"mt-1\", \"grid\", \"header-grid\"], [2, \"padding-top\", \"18px\"], [\"id\", \"flexible\", 1, \"col\", \"ratingPlan-detail\", \"pr-0\", \"flex\", 2, \"border\", \"1px solid black\"], [\"styleClass\", \"mr-2 p-button-secondary\", \"icon\", \"\", 3, \"label\", \"click\"], [1, \"inline-block\", \"col-fixed\", 2, \"width\", \"fit-content !important\"], [1, \"inline-block\", \"col-fixed\", 2, \"min-width\", \"200px\", \"max-width\", \"200px\", \"padding-bottom\", \"5px\", \"cursor\", \"pointer\", \"text-decoration\", \"underline\", \"color\", \"blue\", \"transition\", \"color 0.3s\", 3, \"click\"]],\n      template: function AppRatingPlanListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵtemplate(6, AppRatingPlanListComponent_p_button_6_Template, 1, 3, \"p-button\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"form\", 6);\n          i0.ɵɵlistener(\"ngSubmit\", function AppRatingPlanListComponent_Template_form_ngSubmit_7_listener() {\n            return ctx.onSubmitSearch();\n          });\n          i0.ɵɵelementStart(8, \"p-panel\", 7)(9, \"div\", 8)(10, \"div\", 9)(11, \"span\", 10)(12, \"input\", 11);\n          i0.ɵɵlistener(\"ngModelChange\", function AppRatingPlanListComponent_Template_input_ngModelChange_12_listener($event) {\n            return ctx.searchInfo.name = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"label\", 12);\n          i0.ɵɵtext(14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 9)(16, \"span\", 10)(17, \"p-dropdown\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function AppRatingPlanListComponent_Template_p_dropdown_ngModelChange_17_listener($event) {\n            return ctx.searchInfo.status = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"label\", 14);\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 9)(21, \"span\", 10)(22, \"p-dropdown\", 15);\n          i0.ɵɵlistener(\"ngModelChange\", function AppRatingPlanListComponent_Template_p_dropdown_ngModelChange_22_listener($event) {\n            return ctx.searchInfo.cycleTimeUnit = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"label\", 16);\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 9)(26, \"span\", 10)(27, \"p-dropdown\", 17);\n          i0.ɵɵlistener(\"ngModelChange\", function AppRatingPlanListComponent_Template_p_dropdown_ngModelChange_27_listener($event) {\n            return ctx.searchInfo.paidType = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"label\", 18);\n          i0.ɵɵtext(29);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"div\", 9)(31, \"span\", 10)(32, \"p-dropdown\", 19);\n          i0.ɵɵlistener(\"ngModelChange\", function AppRatingPlanListComponent_Template_p_dropdown_ngModelChange_32_listener($event) {\n            return ctx.searchInfo.customerType = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"label\", 20);\n          i0.ɵɵtext(34);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 9)(36, \"span\", 10)(37, \"p-dropdown\", 21);\n          i0.ɵɵlistener(\"ngModelChange\", function AppRatingPlanListComponent_Template_p_dropdown_ngModelChange_37_listener($event) {\n            return ctx.searchInfo.ratingScope = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"label\", 22);\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(40, \"div\", 23);\n          i0.ɵɵelement(41, \"p-button\", 24);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(42, \"table-vnpt\", 25);\n          i0.ɵɵlistener(\"selectItemsChange\", function AppRatingPlanListComponent_Template_table_vnpt_selectItemsChange_42_listener($event) {\n            return ctx.selectItems = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"form\", 26);\n          i0.ɵɵlistener(\"ngSubmit\", function AppRatingPlanListComponent_Template_form_ngSubmit_43_listener() {\n            return ctx.onSubmitSearchUser();\n          });\n          i0.ɵɵelementStart(44, \"div\", 27)(45, \"p-dialog\", 28);\n          i0.ɵɵlistener(\"visibleChange\", function AppRatingPlanListComponent_Template_p_dialog_visibleChange_45_listener($event) {\n            return ctx.isShowDialogAssignPlan = $event;\n          });\n          i0.ɵɵelementStart(46, \"div\", 29)(47, \"div\", 9)(48, \"span\", 10)(49, \"input\", 30);\n          i0.ɵɵlistener(\"ngModelChange\", function AppRatingPlanListComponent_Template_input_ngModelChange_49_listener($event) {\n            return ctx.searchInfoUser.username = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"label\", 31);\n          i0.ɵɵtext(51);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(52, \"div\", 9)(53, \"span\", 10)(54, \"input\", 32);\n          i0.ɵɵlistener(\"ngModelChange\", function AppRatingPlanListComponent_Template_input_ngModelChange_54_listener($event) {\n            return ctx.searchInfoUser.fullName = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"label\", 33);\n          i0.ɵɵtext(56);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(57, \"div\", 9);\n          i0.ɵɵtemplate(58, AppRatingPlanListComponent_span_58_Template, 4, 6, \"span\", 34);\n          i0.ɵɵtemplate(59, AppRatingPlanListComponent_span_59_Template, 2, 2, \"span\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 23);\n          i0.ɵɵelement(61, \"p-button\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(62, \"table-vnpt\", 36);\n          i0.ɵɵlistener(\"selectItemsChange\", function AppRatingPlanListComponent_Template_table_vnpt_selectItemsChange_62_listener($event) {\n            return ctx.selectItemsUser = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"div\", 37)(64, \"p-button\", 38);\n          i0.ɵɵlistener(\"click\", function AppRatingPlanListComponent_Template_p_button_click_64_listener() {\n            return ctx.isShowDialogAssignPlan = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"p-button\", 39);\n          i0.ɵɵlistener(\"click\", function AppRatingPlanListComponent_Template_p_button_click_65_listener() {\n            return ctx.assignPlan();\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(66, \"div\", 40);\n          i0.ɵɵtemplate(67, AppRatingPlanListComponent_p_dialog_67_Template, 145, 81, \"p-dialog\", 41);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"div\", 27)(69, \"p-dialog\", 28);\n          i0.ɵɵlistener(\"visibleChange\", function AppRatingPlanListComponent_Template_p_dialog_visibleChange_69_listener($event) {\n            return ctx.isShowDialogShowCustomerAccount = $event;\n          });\n          i0.ɵɵelement(70, \"table-vnpt\", 42);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.menu.listplan\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkAuthen(i0.ɵɵpureFunction1(90, _c2, ctx.allPermissions.RATING_PLAN.CREATE)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formSearch);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"toggleable\", true)(\"header\", ctx.tranService.translate(\"global.text.filter\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.name);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.planName\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.searchInfo.status)(\"options\", ctx.listStatus);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.status\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.searchInfo.cycleTimeUnit)(\"options\", ctx.listCycle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.cycle\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.searchInfo.paidType)(\"options\", ctx.listPaidType);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.paidType\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.searchInfo.customerType)(\"options\", ctx.listCustomerType);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.customerType\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.searchInfo.ratingScope)(\"options\", ctx.listRatingScope);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.ratingScope\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"fieldId\", \"id\")(\"selectItems\", ctx.selectItems)(\"columns\", ctx.columns)(\"dataSet\", ctx.dataSet)(\"options\", ctx.optionTable)(\"loadData\", ctx.search.bind(ctx))(\"pageNumber\", ctx.pageNumber)(\"pageSize\", ctx.pageSize)(\"sort\", ctx.sort)(\"params\", ctx.searchInfo)(\"labelTable\", ctx.tranService.translate(\"global.menu.listplan\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formSearchUser);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(92, _c4));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"ratingPlan.label.assignPlan\"))(\"visible\", ctx.isShowDialogAssignPlan)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfoUser.username);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.username\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfoUser.fullName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ratingPlan.label.fullName\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassMap(ctx.userType == ctx.allUserType.ADMIN ? \"\" : \"flex flex-row justify-content-start align-items-center\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.userType == ctx.allUserType.ADMIN);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.userType != ctx.allUserType.ADMIN);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"fieldId\", \"id\")(\"pageNumber\", ctx.pageNumberAssign)(\"pageSize\", ctx.pageSizeAssign)(\"selectItems\", ctx.selectItemsUser)(\"columns\", ctx.columnsInfoUser)(\"dataSet\", ctx.dataSetAssignPlan)(\"options\", ctx.optionTableAssignPLan)(\"loadData\", ctx.searchUser.bind(ctx))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(93, _c5))(\"scrollHeight\", \"400px\")(\"sort\", ctx.sort)(\"params\", ctx.searchInfoUser);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(94, _c6));\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.cancel\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.save\"))(\"disabled\", ctx.checkInValidAssignPlan());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowModalDetail);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(95, _c7));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"account.label.showCustomerAccount\"))(\"visible\", ctx.isShowDialogShowCustomerAccount)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"fieldId\", \"id1\")(\"columns\", ctx.columnsInfoUserForDetail)(\"dataSet\", ctx.dataSetAssignPlanForDetail)(\"options\", ctx.optionTableShowCustomerAccount);\n        }\n      },\n      dependencies: [i4.NgIf, i5.RouterLink, i5.RouterLinkActive, i6.Breadcrumb, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.NgModel, i3.FormGroupDirective, i3.FormControlName, i7.InputText, i8.Button, i9.TableVnptComponent, i10.Dropdown, i11.Card, i12.Dialog, i13.InputSwitch, i14.RadioButton, i15.Panel],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CONSTANTS", "ComponentBase", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "tranService", "translate", "ɵɵpureFunction0", "_c0", "ɵɵelementStart", "ɵɵlistener", "AppRatingPlanListComponent_span_58_Template_p_dropdown_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "searchInfoUser", "provinceCode", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ctx_r1", "listProvince", "ɵɵtextInterpolate", "ɵɵtextInterpolate2", "ctx_r2", "provinceInfo", "AppRatingPlanListComponent_p_dialog_67_p_button_2_Template_p_button_click_0_listener", "_r12", "ctx_r11", "active", "ctx_r6", "AppRatingPlanListComponent_p_dialog_67_p_button_3_Template_p_button_click_0_listener", "_r14", "ctx_r13", "approve", "ctx_r7", "AppRatingPlanListComponent_p_dialog_67_p_button_4_Template_p_button_click_0_listener", "_r16", "ctx_r15", "suspend", "ctx_r8", "ctx_r9", "myProvices", "AppRatingPlanListComponent_p_dialog_67_div_79_Template_span_click_1_listener", "_r18", "ctx_r17", "openDialogAddCustomerAccount", "ctx_r10", "AppRatingPlanListComponent_p_dialog_67_Template_p_dialog_visibleChange_0_listener", "_r20", "ctx_r19", "isShowModalDetail", "ɵɵtemplate", "AppRatingPlanListComponent_p_dialog_67_p_button_2_Template", "AppRatingPlanListComponent_p_dialog_67_p_button_3_Template", "AppRatingPlanListComponent_p_dialog_67_p_button_4_Template", "AppRatingPlanListComponent_p_dialog_67_Template_p_radioButton_ngModelChange_48_listener", "ctx_r21", "ratingPlanInfo", "subscriptionType", "AppRatingPlanListComponent_p_dialog_67_Template_p_radioButton_ngModelChange_54_listener", "ctx_r22", "AppRatingPlanListComponent_p_dialog_67_Template_p_inputSwitch_ngModelChange_72_listener", "ctx_r23", "checkedReload", "AppRatingPlanListComponent_p_dialog_67_div_78_Template", "AppRatingPlanListComponent_p_dialog_67_div_79_Template", "AppRatingPlanListComponent_p_dialog_67_Template_p_inputSwitch_ngModelChange_110_listener", "ctx_r24", "checkedFlexible", "ɵɵstyleMap", "_c1", "ctx_r3", "status", "planStatuses", "CREATE_NEW", "DEACTIVATED", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵpureFunction1", "_c2", "PERMISSIONS", "RATING_PLAN", "ACTIVE", "PENDING", "APPROVE", "ACTIVATED", "SUPPEND", "_c3", "code", "name", "ɵɵclassMap", "getClassStatus", "getNameStatus", "dispatchCode", "getNameCustomerType", "customerType", "description", "ɵɵtextInterpolate3", "subscriptionFee", "subscriptionTypes", "ip", "getCycleTimeUnit", "cycleTimeUnit", "cycleInterval", "getRatingScope", "ratingScope", "planScopes", "PROVINCE", "CUSTOMER", "limitDataUsage", "dataMax", "limitSmsInside", "limitSmsOutside", "feePerDataUnit", "downSpeed", "feeSmsInside", "feeSmsOutside", "maximumFee", "ɵɵtextInterpolate1", "dataRoundUnit", "squeezedSpeed", "AppRatingPlanListComponent", "constructor", "ratingPlanService", "accountService", "formBuilder", "injector", "planSelected", "selectItems", "selectItemsUser", "selectItemsUserOld", "id", "isShowDialogAssignPlan", "response", "isShowDialogShowCustomerAccount", "RATING_PLAN_STATUS", "RATING_PLAN_SCOPE", "allPermissions", "allUserType", "USER_TYPE", "ngOnInit", "me", "optionTableShowCustomerAccount", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "action", "paginator", "columnsInfoUserForDetail", "key", "size", "align", "isShow", "isSort", "dataSetAssignPlanForDetail", "content", "total", "userType", "sessionService", "userInfo", "type", "items", "label", "home", "icon", "routerLink", "listStatus", "value", "listCycle", "RATING_PLAN_CYCLE", "DAY", "MONTH", "listPaidType", "SUBSCRIPTION_TYPE", "PREPAID", "POSTPAID", "listCustomerType", "CUSTOMER_TYPE", "PERSONAL", "INTERPRISE", "listRatingScope", "NATION_WIDE", "reload", "flat", "flexible", "userIds", "searchInfo", "paidType", "createSearchUserInfo", "formSearchUser", "group", "formSearch", "pageNumber", "pageSize", "sort", "optionTable", "tooltip", "func", "item", "router", "navigate", "funcAppear", "UPDATE", "messageCommonService", "confirm", "ok", "deleteById", "success", "search", "cancel", "DELETE", "pageNumberAssign", "pageSizeAssign", "sortAssign", "JSON", "parse", "localStorage", "getItem", "getById", "listProvinc<PERSON><PERSON>ins", "filter", "el", "includes", "getAllAccountPlanAssign", "map", "userId", "searchUser", "ISSUE", "optionTableAssignPLan", "columns", "style", "cursor", "color", "display", "max<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "isShowTooltip", "funcClick", "planId", "getDetailPLan", "checkSubscriptionType", "funcConvertText", "funcGetClassname", "AGENCY", "utilService", "convertNumberToString", "dataSetAssignPlan", "getListProvince", "columnsInfoUser", "funcGetRouting", "i", "length", "ngAfterContentChecked", "reset", "onSubmitSearch", "onSubmitSearchUser", "username", "fullName", "email", "checkInValidAssignPlan", "flag", "assignPlan", "oldIds", "dataAdd", "currentSelected", "dataRemove", "data", "page", "limit", "params", "dataParams", "ratingPlanId", "Object", "keys", "for<PERSON>ach", "trim", "ADMIN", "toLocaleString", "totalElements", "dataSet", "onload", "offload", "Number", "getReload", "getFlexible", "getListProvinceByCode", "provinces", "substring", "getUserAssignedOnRatingPlan", "resp", "RELOAD", "YES", "NO", "FLEXIBLE", "CYCLE_TIME_UNITS", "activePlan", "window", "location", "suspendPlan", "changeManageLevel", "ɵɵdirectiveInject", "i1", "RatingPlanService", "i2", "AccountService", "i3", "FormBuilder", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "AppRatingPlanListComponent_Template", "rf", "ctx", "AppRatingPlanListComponent_p_button_6_Template", "AppRatingPlanListComponent_Template_form_ngSubmit_7_listener", "AppRatingPlanListComponent_Template_input_ngModelChange_12_listener", "AppRatingPlanListComponent_Template_p_dropdown_ngModelChange_17_listener", "AppRatingPlanListComponent_Template_p_dropdown_ngModelChange_22_listener", "AppRatingPlanListComponent_Template_p_dropdown_ngModelChange_27_listener", "AppRatingPlanListComponent_Template_p_dropdown_ngModelChange_32_listener", "AppRatingPlanListComponent_Template_p_dropdown_ngModelChange_37_listener", "AppRatingPlanListComponent_Template_table_vnpt_selectItemsChange_42_listener", "AppRatingPlanListComponent_Template_form_ngSubmit_43_listener", "AppRatingPlanListComponent_Template_p_dialog_visibleChange_45_listener", "AppRatingPlanListComponent_Template_input_ngModelChange_49_listener", "AppRatingPlanListComponent_Template_input_ngModelChange_54_listener", "AppRatingPlanListComponent_span_58_Template", "AppRatingPlanListComponent_span_59_Template", "AppRatingPlanListComponent_Template_table_vnpt_selectItemsChange_62_listener", "AppRatingPlanListComponent_Template_p_button_click_64_listener", "AppRatingPlanListComponent_Template_p_button_click_65_listener", "AppRatingPlanListComponent_p_dialog_67_Template", "AppRatingPlanListComponent_Template_p_dialog_visibleChange_69_listener", "CREATE", "bind", "_c4", "_c5", "_c6", "_c7"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\rating-plan-management\\list-plan\\app.ratingplan.list.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\rating-plan-management\\list-plan\\app.ratingplan.list.component.html"], "sourcesContent": ["import {Component, Injector, OnInit} from \"@angular/core\";\r\nimport {FormBuilder} from \"@angular/forms\";\r\nimport {MenuItem} from \"primeng/api\";\r\nimport {CONSTANTS} from \"src/app/service/comon/constants\";\r\nimport {RatingPlanService} from \"src/app/service/rating-plan/RatingPlanService\";\r\nimport {ColumnInfo, OptionTable} from \"../../common-module/table/table.component\";\r\nimport {ComponentBase} from \"src/app/component.base\";\r\nimport {AccountService} from \"../../../service/account/AccountService\";\r\n\r\n@Component({\r\n    selector: \"app-rating-plan-list\",\r\n    templateUrl: \"./app.ratingplan.list.component.html\",\r\n})\r\nexport class AppRatingPlanListComponent extends ComponentBase implements OnInit{\r\n    searchInfo: {\r\n        id: number | null,\r\n        name: string | null,\r\n        status: number | null,\r\n        cycleTimeUnit: number | null,\r\n        paidType: number | null,\r\n        customerType: string | null,\r\n        ratingScope: number | null\r\n    }\r\n    searchInfoUser: {\r\n        username: string | null,\r\n        fullName: string | null,\r\n        email: string | null,\r\n        provinceCode: string | null,\r\n    }\r\n    planSelected: any = {};\r\n    listStatus: Array<any>;\r\n    listCycle: Array<any>;\r\n    listPaidType: Array<any>;\r\n    listCustomerType: Array<any>;\r\n    listRatingScope: Array<any>;\r\n    items: MenuItem[];\r\n    home: MenuItem;\r\n    selectItems: Array<any> = [];\r\n    columns: Array<ColumnInfo>;\r\n\r\n    dataSet: {\r\n        content: Array<any>,\r\n        total: number\r\n    };\r\n    optionTable: OptionTable;\r\n    pageNumber: number;\r\n    pageSize: number;\r\n    sort: string;\r\n    formSearch: any;\r\n\r\n    selectItemsUser: Array<{id: number, provinceCode: string, [key:string]:any}> = [];\r\n    selectItemsUserOld: Array<{id: number, provinceCode: string, [key:string]:any}> = [{id: -1, provinceCode: \"\"}];\r\n    columnsInfoUser: Array<ColumnInfo>;\r\n    dataSetAssignPlan: {\r\n        content: Array<any>,\r\n        total: number\r\n    };\r\n    optionTableAssignPLan: OptionTable;\r\n    pageNumberAssign: number;\r\n    pageSizeAssign: number;\r\n    sortAssign: string;\r\n    formSearchUser: any;\r\n    isShowDialogAssignPlan: boolean = false;\r\n    listProvince: Array<any>;\r\n    listProvincOrigins: Array<any>;\r\n    userType: number;\r\n    isShowModalDetail: boolean = false;\r\n    planId: number;\r\n    checkedReload: boolean;\r\n    checkedFlexible: boolean;\r\n    response: any={};\r\n    ratingPlanInfo: {\r\n        id: number|null;\r\n        code: string| null,\r\n        name: string| null,\r\n        status: number| null,\r\n        dispatchCode: string| null,\r\n        customerType: string| null,\r\n        subscriptionFee: string| null,\r\n        subscriptionType: string| null,\r\n        ratingScope: number| null,\r\n        cycleTimeUnit: string| null,\r\n        cycleInterval: string| null,\r\n        reload: string| null,\r\n        flat: string| null,\r\n        limitDataUsage: string| null,\r\n        limitSmsOutside: string| null,\r\n        limitSmsInside: string| null,\r\n        flexible: string| null,\r\n        feePerDataUnit: string| null,\r\n        squeezedSpeed: string| null,\r\n        feeSmsInside: string| null,\r\n        feeSmsOutside: string| null,\r\n        maximumFee: string| null,\r\n        dataRoundUnit: string| null,\r\n        downSpeed: string| null,\r\n        provinceCode: Array<string>| null,\r\n        description: string | null\r\n        dataMax: string | null,\r\n        userIds: number | null,\r\n    };\r\n    isShowDialogShowCustomerAccount: boolean = false;\r\n    optionTableShowCustomerAccount: OptionTable;\r\n    columnsInfoUserForDetail: Array<ColumnInfo>;\r\n    dataSetAssignPlanForDetail: {\r\n        content: Array<any>,\r\n        total: number\r\n    };\r\n    planStatuses: any = CONSTANTS.RATING_PLAN_STATUS;\r\n    provinces: any[] | undefined;\r\n    myProvices: string| null;\r\n    subscriptionTypes: any=[];\r\n    provinceInfo: string = \"\";\r\n    planScopes = CONSTANTS.RATING_PLAN_SCOPE;\r\n    allPermissions = CONSTANTS.PERMISSIONS;\r\n    allUserType = CONSTANTS.USER_TYPE;\r\n    constructor(public ratingPlanService: RatingPlanService,\r\n                private accountService: AccountService,\r\n        private formBuilder: FormBuilder,\r\n        injector: Injector) {\r\n            super(injector);\r\n    }\r\n    ngOnInit(): void {\r\n        let me = this;\r\n        this.optionTableShowCustomerAccount = {\r\n            hasClearSelected: true,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n            action: null,\r\n            paginator: false\r\n        };\r\n\r\n        this.columnsInfoUserForDetail = [\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.username\"),\r\n                key: \"username\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n                // style:{\r\n                //     cursor: \"pointer\",\r\n                //     color: \"var(--mainColorText)\"\r\n                // },\r\n                // funcGetRouting(item) {\r\n                //     return [`/plans/detail/${item.id}`]\r\n                // },\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.fullName\"),\r\n                key: \"fullName\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.email\"),\r\n                key: \"email\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.province\"),\r\n                key: \"provinceName\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n        ];\r\n        this.dataSetAssignPlanForDetail = {\r\n            content: [],\r\n            total: 0,\r\n        };\r\n        this.userType = this.sessionService.userInfo.type;\r\n        this.items = [{ label: this.tranService.translate(\"global.menu.ratingplanmgmt\") }, { label: this.tranService.translate(\"global.menu.listplan\") }];\r\n        this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n\r\n        this.listStatus = [\r\n            {name: this.tranService.translate(\"ratingPlan.status.create\"), value: CONSTANTS.RATING_PLAN_STATUS.CREATE_NEW},\r\n            {name: this.tranService.translate(\"ratingPlan.status.pending\"), value: CONSTANTS.RATING_PLAN_STATUS.PENDING},\r\n            {name: this.tranService.translate(\"ratingPlan.status.activated\"), value: CONSTANTS.RATING_PLAN_STATUS.ACTIVATED},\r\n            {name: this.tranService.translate(\"ratingPlan.status.deactivated\"), value: CONSTANTS.RATING_PLAN_STATUS.DEACTIVATED},\r\n        ],\r\n\r\n        this.listCycle = [\r\n            {name: this.tranService.translate(\"ratingPlan.cycle.day\"), value: CONSTANTS.RATING_PLAN_CYCLE.DAY},\r\n            {name: this.tranService.translate(\"ratingPlan.cycle.month\"), value: CONSTANTS.RATING_PLAN_CYCLE.MONTH},\r\n        ]\r\n\r\n        this.listPaidType = [\r\n            {name: this.tranService.translate(\"ratingPlan.subscriptionType.pre\"), value: CONSTANTS.SUBSCRIPTION_TYPE.PREPAID},\r\n            {name: this.tranService.translate(\"ratingPlan.subscriptionType.post\"), value: CONSTANTS.SUBSCRIPTION_TYPE.POSTPAID},\r\n        ]\r\n\r\n        this.listCustomerType = [\r\n            {name: this.tranService.translate(\"ratingPlan.customerType.personal\"), value: CONSTANTS.CUSTOMER_TYPE.PERSONAL},\r\n            {name: this.tranService.translate(\"ratingPlan.customerType.enterprise\"), value: CONSTANTS.CUSTOMER_TYPE.INTERPRISE}\r\n            // ,\r\n            // {name: this.tranService.translate(\"ratingPlan.customerType.agency\"), value: CONSTANTS.CUSTOMER_TYPE.AGENCY},\r\n        ]\r\n\r\n        this.listRatingScope = [\r\n            {name: this.tranService.translate(\"ratingPlan.ratingScope.nativeWide\"), value: CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE},\r\n            {name: this.tranService.translate(\"ratingPlan.ratingScope.province\"), value: CONSTANTS.RATING_PLAN_SCOPE.PROVINCE},\r\n            {name: this.tranService.translate(\"ratingPlan.ratingScope.customer\"), value: CONSTANTS.RATING_PLAN_SCOPE.CUSTOMER},\r\n        ];\r\n\r\n        this.ratingPlanInfo = {\r\n            id:  null,\r\n            code:  null,\r\n            name:  null,\r\n            status:  null,\r\n            dispatchCode:  null,\r\n            customerType:  null,\r\n            subscriptionFee:  null,\r\n            subscriptionType:  null,\r\n            ratingScope:  null,\r\n            cycleTimeUnit:  null,\r\n            cycleInterval:  null,\r\n            reload:  null,\r\n            flat:  null,\r\n            limitDataUsage: null,\r\n            limitSmsOutside: null,\r\n            limitSmsInside: null,\r\n            flexible: null,\r\n            feePerDataUnit: null,\r\n            squeezedSpeed: null,\r\n            feeSmsInside: null,\r\n            feeSmsOutside: null,\r\n            maximumFee: null,\r\n            dataRoundUnit: null,\r\n            downSpeed: null,\r\n            provinceCode: null,\r\n            description: null,\r\n            dataMax: null,\r\n            userIds: null,\r\n        }\r\n\r\n        this.searchInfo = {\r\n            id: null,\r\n            name: null,\r\n            status: null,\r\n            cycleTimeUnit: null,\r\n            paidType: null,\r\n            customerType: null,\r\n            ratingScope: null\r\n        }\r\n\r\n        this.createSearchUserInfo();\r\n        this.formSearchUser = this.formBuilder.group(this.searchInfoUser);\r\n        this.formSearch = this.formBuilder.group(this.searchInfo);\r\n        this.selectItems = [];\r\n        this.pageNumber = 0;\r\n        this.pageSize = 10;\r\n        this.sort = \"name,asc\";\r\n\r\n        this.selectItemsUser = [];\r\n\r\n        this.optionTable = {\r\n            hasClearSelected: true,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n            action: [\r\n                {\r\n                    icon: \"pi pi-pencil\",\r\n                    tooltip: this.tranService.translate(\"global.button.edit\"),\r\n                    func: function(id, item){\r\n                        me.router.navigate([`/plans/update/${id}`]);\r\n                    },\r\n                    funcAppear: function(id, item) {\r\n                        return me.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.UPDATE]);\r\n                    }\r\n                },\r\n                {\r\n                    icon: \"pi pi-trash\",\r\n                    tooltip: this.tranService.translate(\"global.button.delete\"),\r\n                    func: function(id, item){\r\n                        me.messageCommonService.confirm(\r\n                            me.tranService.translate(\"global.message.titleConfirmDeletePlan\"),\r\n                            me.tranService.translate(\"global.message.confirmDeletePlan\"),\r\n                            {\r\n                                ok:()=>{\r\n                                    me.ratingPlanService.deleteById(id,(response)=>{\r\n                                        me.messageCommonService.success(me.tranService.translate(\"global.message.deleteSuccess\"));\r\n                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\r\n                                    })\r\n                                },\r\n                                cancel: ()=>{\r\n                                    // me.messageCommonService.error(me.tranService.translate(\"global.message.deleteFail\"));\r\n                                }\r\n                            }\r\n                        )\r\n                    },\r\n                    funcAppear: function(id, item) {\r\n                        return item.status != CONSTANTS.RATING_PLAN_STATUS.ACTIVATED && me.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.DELETE]);\r\n                    }\r\n                },\r\n                {\r\n                    icon: \"pi pi-file\",\r\n                    tooltip: this.tranService.translate(\"global.button.assignPlan\"),\r\n                    func: function(id, item){\r\n                        me.selectItemsUser = [];\r\n                        me.pageNumberAssign = 0;\r\n                        me.pageSizeAssign = 10;\r\n                        me.sortAssign = \"createdDate,asc\";\r\n                        const userInfo = JSON.parse(localStorage.getItem('userInfo'))\r\n                        me.ratingPlanService.getById(id, (response)=>{\r\n                            me.planSelected = response;\r\n                            if(me.planSelected.ratingScope == CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE){\r\n                                me.listProvince = [...me.listProvincOrigins];\r\n                            }else{\r\n                                if(userInfo.provinceCode){\r\n                                    me.listProvince = userInfo.provinceCode\r\n                                }else if(userInfo.type == 1){\r\n                                    me.listProvince = me.listProvincOrigins.filter(el => response.provinceCode.includes(el.code));\r\n                                }else{\r\n                                    me.listProvince = []\r\n                                }\r\n                            }\r\n                            me.ratingPlanService.getAllAccountPlanAssign(me.planSelected.id, (response)=>{\r\n                                me.selectItemsUser = (response || []).map(el => {\r\n                                    return {\r\n                                        provinceCode: el.provinceCode,\r\n                                        userId: el.userId,\r\n                                        id: el.userId\r\n                                    }\r\n                                });\r\n                                me.selectItemsUserOld = [...me.selectItemsUser]\r\n                            })\r\n                            me.searchUser(me.pageNumberAssign, me.pageSizeAssign, me.sortAssign, me.searchInfoUser)\r\n                            me.isShowDialogAssignPlan = true;\r\n                        })\r\n                    },\r\n                    funcAppear: function(id, item) {\r\n                        return item.status == CONSTANTS.RATING_PLAN_STATUS.ACTIVATED && me.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.ISSUE])\r\n                            && item.ratingScope ==  CONSTANTS.RATING_PLAN_SCOPE.CUSTOMER;\r\n                    }\r\n                }\r\n            ]\r\n        }\r\n        this.optionTableAssignPLan = {\r\n            hasClearSelected: false,\r\n            hasShowIndex: true,\r\n            hasShowChoose: true,\r\n            hasShowToggleColumn: false,\r\n        }\r\n        this.columns = [\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.planCode\"),\r\n                key: \"code\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.planName\"),\r\n                key: \"name\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                style:{\r\n                    cursor: \"pointer\",\r\n                    color: \"var(--mainColorText)\",\r\n                    display: 'inline-block',\r\n                    maxWidth: '400px',\r\n                    overflow: 'hidden',\r\n                    textOverflow: 'ellipsis'\r\n                },\r\n                isShowTooltip: true,\r\n                funcClick(id, item) {\r\n                    me.planId = id;\r\n                    me.getDetailPLan();\r\n                    me.checkSubscriptionType();\r\n                    me.isShowModalDetail = true;\r\n                },\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.status\"),\r\n                key: \"status\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcConvertText(value) {\r\n                    if(value == CONSTANTS.RATING_PLAN_STATUS.CREATE_NEW){\r\n                        return me.tranService.translate(\"ratingPlan.status.create\");\r\n                    }else if(value == CONSTANTS.RATING_PLAN_STATUS.PENDING){\r\n                        return me.tranService.translate(\"ratingPlan.status.pending\");\r\n                    }else if(value == CONSTANTS.RATING_PLAN_STATUS.ACTIVATED){\r\n                        return me.tranService.translate(\"ratingPlan.status.activated\");\r\n                    }else if(value == CONSTANTS.RATING_PLAN_STATUS.DEACTIVATED){\r\n                        return me.tranService.translate(\"ratingPlan.status.deactivated\");\r\n                    }else{\r\n                        return \"\";\r\n                    }\r\n                },\r\n                funcGetClassname(value) {\r\n                    if(value == CONSTANTS.RATING_PLAN_STATUS.CREATE_NEW){\r\n                        return ['p-2', 'text-white', \"bg-cyan-300\", \"border-round\",\"inline-block\"];\r\n                    }else if(value == CONSTANTS.RATING_PLAN_STATUS.PENDING){\r\n                        return ['p-2', 'text-white', \"bg-red-500\", \"border-round\",\"inline-block\"];\r\n                    }else if(value == CONSTANTS.RATING_PLAN_STATUS.ACTIVATED){\r\n                        return ['p-2', 'text-white', \"bg-green-500\", \"border-round\",\"inline-block\"];\r\n                    }else if(value == CONSTANTS.RATING_PLAN_STATUS.DEACTIVATED){\r\n                        return ['p-2', 'text-white' , \"bg-orange-400\", \"border-round\",\"inline-block\"];\r\n                    }else{\r\n                        return [];\r\n                    }\r\n                },\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.customerType\"),\r\n                key: \"customerType\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcConvertText(value) {\r\n                    if(value == CONSTANTS.CUSTOMER_TYPE.INTERPRISE){\r\n                        return me.tranService.translate(\"ratingPlan.customerType.enterprise\");\r\n                    }else if(value == CONSTANTS.CUSTOMER_TYPE.PERSONAL){\r\n                        return me.tranService.translate(\"ratingPlan.customerType.personal\");\r\n                    }else if(value == CONSTANTS.CUSTOMER_TYPE.AGENCY){\r\n                        return me.tranService.translate(\"ratingPlan.customerType.agency\");\r\n                    }else{\r\n                        return \"\";\r\n                    }\r\n                },\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.paidType\"),\r\n                key: \"paidType\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcConvertText(value) {\r\n                    if(value == CONSTANTS.SUBSCRIPTION_TYPE.POSTPAID){\r\n                        return me.tranService.translate(\"ratingPlan.subscriptionType.post\");\r\n                    }else if(value == CONSTANTS.SUBSCRIPTION_TYPE.PREPAID){\r\n                        return me.tranService.translate(\"ratingPlan.subscriptionType.pre\");\r\n                    }else{\r\n                        return \"\";\r\n                    }\r\n                },\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.ratingScope\"),\r\n                key: \"ratingScope\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcConvertText(value) {\r\n                    if(value == CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE){\r\n                        return me.tranService.translate(\"ratingPlan.ratingScope.nativeWide\");\r\n                    }else if(value == CONSTANTS.RATING_PLAN_SCOPE.CUSTOMER){\r\n                        return me.tranService.translate(\"ratingPlan.ratingScope.customer\");\r\n                    }else if(value == CONSTANTS.RATING_PLAN_SCOPE.PROVINCE){\r\n                        return me.tranService.translate(\"ratingPlan.ratingScope.province\");\r\n                    }else{\r\n                        return \"\";\r\n                    }\r\n                },\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.cycle\"),\r\n                key: \"cycleTimeUnit\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcConvertText(value) {\r\n                    if(value == CONSTANTS.RATING_PLAN_CYCLE.DAY){\r\n                        return me.tranService.translate(\"ratingPlan.cycle.day\");\r\n                    }else if(value == CONSTANTS.RATING_PLAN_CYCLE.MONTH){\r\n                        return me.tranService.translate(\"ratingPlan.cycle.month\");\r\n                    }else{\r\n                        return \"\";\r\n                    }\r\n                },\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.subscriptionFee\"),\r\n                key: \"subscriptionFee\",\r\n                size: \"150px\",\r\n                align: \"right\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcConvertText(value){\r\n                    return me.utilService.convertNumberToString(value);\r\n                }\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.limitDataUsage\"),\r\n                key: \"limitDataUsage\",\r\n                size: \"150px\",\r\n                align: \"right\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcConvertText(value){\r\n                    return me.utilService.convertNumberToString(value);\r\n                }\r\n            },\r\n        ]\r\n\r\n        this.dataSetAssignPlan = {\r\n            content: [],\r\n            total: 0\r\n        }\r\n        this.getListProvince()\r\n        this.columnsInfoUser = [\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.username\"),\r\n                key: \"username\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                style:{\r\n                    cursor: \"pointer\",\r\n             color: \"var(--mainColorText)\"\r\n                },\r\n                funcGetRouting(item) {\r\n                    return [`/plans/detail/${item.id}`]\r\n                },\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.fullName\"),\r\n                key: \"fullName\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.email\"),\r\n                key: \"email\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ratingPlan.label.province\"),\r\n                key: \"provinceCode\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcConvertText(value) {\r\n                    for (let i = 0; i < me.listProvincOrigins.length; i++) {\r\n                        if (value == me.listProvincOrigins[i].code){\r\n                            return `${me.listProvincOrigins[i].name} (${me.listProvincOrigins[i].code})`;\r\n                        }\r\n                    }\r\n                    return null\r\n                }\r\n            },\r\n        ]\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n    }\r\n\r\n    ngAfterContentChecked(): void {\r\n        if (this.isShowDialogAssignPlan == false){\r\n            this.formSearchUser.reset();\r\n        }\r\n    }\r\n\r\n    onSubmitSearch(){\r\n        this.pageNumber = 0;\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n    }\r\n\r\n    onSubmitSearchUser(){\r\n        this.pageNumberAssign = 0;\r\n        this.searchUser(this.pageNumberAssign, this.pageSizeAssign, this.sortAssign, this.searchInfoUser);\r\n    }\r\n\r\n    createSearchUserInfo(){\r\n        this.searchInfoUser = {\r\n            username: null,\r\n            fullName: null,\r\n            email: null,\r\n            provinceCode: null\r\n        }\r\n    }\r\n    checkInValidAssignPlan(){\r\n        if(this.selectItemsUser.length == 0 && this.selectItemsUserOld.length == 0){\r\n            return true;\r\n        }\r\n        let flag = false;\r\n        for(let i = 0;i<this.selectItemsUser.length;i++){\r\n            if((this.selectItemsUser[i].provinceCode || \"\") == \"\"){\r\n                flag = true;\r\n                break;\r\n            }\r\n        }\r\n        return flag;\r\n    }\r\n\r\n    assignPlan(){\r\n        let me = this;\r\n        let oldIds = this.selectItemsUserOld.map(el => el.id);\r\n        let dataAdd = this.selectItemsUser.filter(el => !oldIds.includes(el.id)).map(el => {\r\n            return {\r\n                userId: el.id,\r\n                provinceCode: el.provinceCode,\r\n                type: 1\r\n            }\r\n        })\r\n        let currentSelected = this.selectItemsUser.map(el => el.id);\r\n        let dataRemove = this.selectItemsUserOld.filter(el => !currentSelected.includes(el.id)).map(el => {\r\n            return {\r\n                userId: el.id,\r\n                provinceCode: el.provinceCode,\r\n                type: -1\r\n            }\r\n        })\r\n        let data = {\r\n            planId: this.planSelected.id,\r\n            data: [...dataAdd, ...dataRemove]\r\n        }\r\n        if(data.data.length <= 0){\r\n            return;\r\n        }\r\n        me.ratingPlanService.assignPlan(data.planId, data.data, (response)=>{\r\n            me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\r\n        })\r\n        me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n        this.isShowDialogAssignPlan = false;\r\n    }\r\n\r\n    searchUser(page, limit, sort, params){\r\n        let me = this;\r\n        this.pageNumberAssign = page;\r\n        this.pageSizeAssign = limit;\r\n        this.sortAssign = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort,\r\n            type: CONSTANTS.USER_TYPE.CUSTOMER,\r\n            ratingPlanId: this.planSelected.id,\r\n        }\r\n\r\n        Object.keys(this.searchInfoUser).forEach(key => {\r\n            if(this.searchInfoUser[key] != null){\r\n                dataParams[key] = this.searchInfoUser[key];\r\n            }\r\n        })\r\n        if((dataParams[\"provinceCode\"] || \"\").trim().length == 0){\r\n            if(this.userType == CONSTANTS.USER_TYPE.ADMIN){\r\n                if(this.planSelected.ratingScope == CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE){\r\n                    dataParams[\"provinceCode\"] = \"\";\r\n                }else{\r\n                    if(this.planSelected.provinceCode) {\r\n                        dataParams[\"provinceCode\"] = this.planSelected.provinceCode.toLocaleString();\r\n                    }else{\r\n                        dataParams[\"provinceCode\"] = [];\r\n                    }\r\n                }\r\n            }else{\r\n                dataParams[\"provinceCode\"] = this.sessionService.userInfo.provinceCode;\r\n            }\r\n        }\r\n\r\n\r\n        // me.messageCommonService.onload();//checkfix\r\n        this.ratingPlanService.searchUser(dataParams, (response)=>{\r\n            me.dataSetAssignPlan = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n        })\r\n    }\r\n\r\n    search(page, limit, sort, params){\r\n        let me = this;\r\n        this.pageNumber = page;\r\n        this.pageSize = limit;\r\n        this.sort = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        Object.keys(this.searchInfo).forEach(key => {\r\n            if(this.searchInfo[key] != null){\r\n                dataParams[key] = this.searchInfo[key];\r\n            }\r\n        })\r\n        this.dataSet = {\r\n            content: [],\r\n            total: 0\r\n        }\r\n        me.messageCommonService.onload();\r\n        this.ratingPlanService.search(dataParams, (response)=>{\r\n            me.dataSet = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    getListProvince(){\r\n        let me = this;\r\n        this.ratingPlanService.getListProvince((response)=>{\r\n            this.listProvince = response.map(el => {\r\n                if(el.code == me.sessionService.userInfo.provinceCode){\r\n                    me.provinceInfo = `${el.name} - ${el.code}`\r\n                }\r\n                return {\r\n                    ...el,\r\n                    display: `${el.name} - ${el.code}`\r\n                }\r\n            })\r\n            this.listProvincOrigins = [...this.listProvince];\r\n        })\r\n    };\r\n\r\n    getDetailPLan(){\r\n        let me = this;\r\n        this.messageCommonService.onload();\r\n        me.ratingPlanService.getById(Number(me.planId), (response)=>{\r\n            me.response = response\r\n\r\n            me.ratingPlanInfo.id = response.id\r\n            me.ratingPlanInfo.code = response.code\r\n            me.ratingPlanInfo.name = response.name\r\n            me.ratingPlanInfo.status = response.status\r\n            me.ratingPlanInfo.dispatchCode = response.dispatchCode\r\n            me.ratingPlanInfo.customerType = response.customerType\r\n            me.ratingPlanInfo.subscriptionFee = response.subscriptionFee\r\n            me.ratingPlanInfo.subscriptionType = response.paidType\r\n            me.ratingPlanInfo.ratingScope = response.ratingScope\r\n            me.ratingPlanInfo.cycleTimeUnit = response.cycleTimeUnit\r\n            me.ratingPlanInfo.cycleInterval = response.cycleInterval\r\n            me.ratingPlanInfo.reload = response.reload\r\n            me.ratingPlanInfo.flat = response.flat\r\n            me.ratingPlanInfo.limitDataUsage = response.limitDataUsage\r\n            me.ratingPlanInfo.limitSmsOutside = response.limitSmsOutside\r\n            me.ratingPlanInfo.limitSmsInside = response.limitSmsInside\r\n            me.ratingPlanInfo.flexible = response.flexible\r\n            me.ratingPlanInfo.feePerDataUnit = response.feePerDataUnit\r\n            me.ratingPlanInfo.squeezedSpeed = response.squeezedSpeed\r\n            me.ratingPlanInfo.feeSmsInside = response.feeSmsInside\r\n            me.ratingPlanInfo.feeSmsOutside = response.feeSmsOutside\r\n            me.ratingPlanInfo.maximumFee = response.maximumFee\r\n            me.ratingPlanInfo.dataRoundUnit = response.dataRoundUnit\r\n            me.ratingPlanInfo.downSpeed = response.downSpeed\r\n            me.ratingPlanInfo.provinceCode = response.provinceCode\r\n            me.ratingPlanInfo.description = response.description;\r\n            me.ratingPlanInfo.dataMax = response.dataMax;\r\n            me.ratingPlanInfo.userIds = response.userIds;\r\n\r\n            me.getReload(me.ratingPlanInfo.reload)\r\n            me.getFlexible(me.ratingPlanInfo.flexible)\r\n            me.myProvices = \"\"\r\n            if (response.provinceCode != null){\r\n\r\n                me.accountService.getListProvinceByCode(response.provinceCode,(data)=>{\r\n                    me.provinces = data.map(el => {\r\n                        return {\r\n                            code: el.code,\r\n                            name: `${el.name}`\r\n                        }\r\n                    })\r\n                    me.provinces.forEach(el => {\r\n                        if(me.ratingPlanInfo.provinceCode.includes(el.code)){\r\n                            me.myProvices += `${el.name}, `;\r\n\r\n                        }\r\n                    })\r\n                    if(me.myProvices.length > 0){\r\n                        me.myProvices = me.myProvices.substring(0, me.myProvices.length - 2);\r\n                    }\r\n                })\r\n            }\r\n            me.accountService.getUserAssignedOnRatingPlan({ratingPlanId: response.id}, (resp) => {\r\n                this.dataSetAssignPlanForDetail = {\r\n                    content: resp,\r\n                    total: resp ? resp.length : 0\r\n                }\r\n            })\r\n\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    };\r\n\r\n    getReload(value){\r\n        if(value == CONSTANTS.RELOAD.YES){\r\n            return this.checkedReload = true;\r\n        }else if(value == CONSTANTS.RELOAD.NO){\r\n            return this.checkedReload = false\r\n        }\r\n        return \"\";\r\n    }\r\n\r\n    getFlexible(value){\r\n        if(value == CONSTANTS.FLEXIBLE.YES){\r\n            return this.checkedFlexible = true;\r\n        }else if(value == CONSTANTS.FLEXIBLE.NO){\r\n            return this.checkedFlexible = false\r\n        }\r\n        return \"\";\r\n    };\r\n\r\n    getClassStatus(value){\r\n        if(value == CONSTANTS.RATING_PLAN_STATUS.ACTIVATED){\r\n            return ['p-2', \"text-teal-800\", \"bg-teal-100\",\"border-round\",\"inline-block\"];\r\n        }else if(value == CONSTANTS.RATING_PLAN_STATUS.CREATE_NEW){\r\n            return ['p-2', \"text-primary-600\", \"bg-primary-100\", \"border-round\",\"inline-block\"];\r\n        }else if(value == CONSTANTS.RATING_PLAN_STATUS.PENDING){\r\n            return ['p-2', \"text-red-700\", \"bg-red-100\",\"border-round\",\"inline-block\"];\r\n        }else if(value == CONSTANTS.RATING_PLAN_STATUS.DEACTIVATED){\r\n            return ['p-2', \"text-orange-700\", \"bg-orange-100\", \"border-round\",\"inline-block\"];\r\n        }\r\n        return [];\r\n    };\r\n\r\n    getNameCustomerType(value){\r\n        if(value == CONSTANTS.CUSTOMER_TYPE.PERSONAL){\r\n            return this.tranService.translate(\"ratingPlan.customerType.personal\");\r\n        }else if(value == CONSTANTS.CUSTOMER_TYPE.INTERPRISE){\r\n            return this.tranService.translate(\"ratingPlan.customerType.enterprise\");\r\n        }else if(value == CONSTANTS.CUSTOMER_TYPE.AGENCY){\r\n            return this.tranService.translate(\"ratingPlan.customerType.agency\");\r\n        }\r\n        return \"\";\r\n    };\r\n\r\n    checkSubscriptionType(){\r\n        this.subscriptionTypes = [{\r\n            type: this.tranService.translate(\"ratingPlan.subscriptionType.post\"),\r\n            ip: CONSTANTS.SUBSCRIPTION_TYPE.POSTPAID,\r\n        },\r\n            {\r\n                type: this.tranService.translate(\"ratingPlan.subscriptionType.pre\"),\r\n                ip: CONSTANTS.SUBSCRIPTION_TYPE.PREPAID,\r\n            }]\r\n    };\r\n\r\n    getCycleTimeUnit(value){\r\n        if(value == CONSTANTS.CYCLE_TIME_UNITS.DAY){\r\n            return this.tranService.translate(\"ratingPlan.cycle.day\");\r\n        }else if(value == CONSTANTS.CYCLE_TIME_UNITS.MONTH){\r\n            return this.tranService.translate(\"ratingPlan.cycle.month\");\r\n        }\r\n        return \"\";\r\n    };\r\n\r\n    getRatingScope(value){\r\n        if(value == CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE){\r\n            return this.tranService.translate(\"ratingPlan.ratingScope.nativeWide\");\r\n        }else if(value == CONSTANTS.RATING_PLAN_SCOPE.CUSTOMER){\r\n            return this.tranService.translate(\"ratingPlan.ratingScope.customer\");\r\n        }else if(value == CONSTANTS.RATING_PLAN_SCOPE.PROVINCE){\r\n            return this.tranService.translate(\"ratingPlan.ratingScope.province\");\r\n        }\r\n        return \"\";\r\n    };\r\n\r\n    getNameStatus(value){\r\n        if(value == CONSTANTS.RATING_PLAN_STATUS.ACTIVATED){\r\n            return this.tranService.translate(\"ratingPlan.status.activated\");\r\n        }else if(value == CONSTANTS.RATING_PLAN_STATUS.CREATE_NEW){\r\n            return this.tranService.translate(\"ratingPlan.status.create\");\r\n        }else if(value == CONSTANTS.RATING_PLAN_STATUS.PENDING){\r\n            return this.tranService.translate(\"ratingPlan.status.pending\");\r\n        }else if(value == CONSTANTS.RATING_PLAN_STATUS.DEACTIVATED){\r\n            return this.tranService.translate(\"ratingPlan.status.deactivated\");\r\n        }\r\n        return \"\";\r\n    };\r\n\r\n    active(){\r\n        let me = this\r\n        me.messageCommonService.confirm(\r\n            me.tranService.translate(\"global.message.confirmActivePlan\"),\r\n            me.tranService.translate(\"global.message.titleConfirmActivePlan\"),\r\n            {\r\n                ok:()=>{\r\n                    me.ratingPlanService.activePlan(me.planId,(response)=>{\r\n                    })\r\n                    me.messageCommonService.success(me.tranService.translate(\"global.message.activeSuccess\"));\r\n                    window.location.reload();\r\n                },\r\n                cancel: ()=>{\r\n                    // me.messageCommonService.error(me.tranService.translate(\"global.message.deleteFail\"));\r\n                }\r\n            }\r\n        )\r\n    }\r\n    approve(){\r\n        let me = this\r\n        me.messageCommonService.confirm(\r\n            me.tranService.translate(\"global.message.confirmApprovePlan\"),\r\n            me.tranService.translate(\"global.message.titleConfirmApprovePlan\"),\r\n            {\r\n                ok:()=>{\r\n                    me.ratingPlanService.activePlan(me.planId,(response)=>{\r\n                        // me.router.navigate(['/plans']);\r\n                    })\r\n                    me.messageCommonService.success(me.tranService.translate(\"global.message.approveSuccess\"));\r\n                    window.location.reload();\r\n                },\r\n                cancel: ()=>{\r\n                    // me.messageCommonService.error(me.tranService.translate(\"global.message.deleteFail\"));\r\n                }\r\n            }\r\n        )\r\n    }\r\n    suspend(){\r\n        let me = this\r\n        me.messageCommonService.confirm(\r\n            me.tranService.translate(\"global.message.confirmSuspendPlan\"),\r\n            me.tranService.translate(\"global.message.titleConfirmSuspendPlan\"),\r\n            {\r\n                ok:()=>{\r\n                    me.ratingPlanService.suspendPlan(me.planId,(response)=>{\r\n                        // me.router.navigate(['/plans']);\r\n                    })\r\n                    me.messageCommonService.success(me.tranService.translate(\"global.message.suspendSuuccess\"));\r\n                    window.location.reload();\r\n                },\r\n                cancel: ()=>{\r\n                    // me.messageCommonService.error(me.tranService.translate(\"global.message.deleteFail\"));\r\n                }\r\n            }\r\n        )\r\n    }\r\n\r\n    changeManageLevel(){\r\n\r\n    }\r\n\r\n    protected readonly CONSTANTS = CONSTANTS;\r\n\r\n    openDialogAddCustomerAccount() {\r\n        this.isShowDialogShowCustomerAccount = true\r\n    }\r\n}\r\n", "<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{tranService.translate(\"global.menu.listplan\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"col-5 flex flex-row justify-content-end align-items-center\">\r\n        <p-button *ngIf=\"checkAuthen([allPermissions.RATING_PLAN.CREATE])\" styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.create')\" icon=\"\" [routerLink]=\"['create']\" routerLinkActive=\"router-link-active\" ></p-button>\r\n    </div>\r\n</div>\r\n\r\n<form [formGroup]=\"formSearch\" (ngSubmit)=\"onSubmitSearch()\" class=\"pb-2 pt-3 vnpt-field-set\"   >\r\n    <p-panel [toggleable]=\"true\" [header]=\"tranService.translate('global.text.filter')\">\r\n        <div class=\"grid search-grid-3\">\r\n        <!-- Ten goi cuoc -->\r\n        <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input pInputText\r\n                           class=\"w-full\"\r\n                           pInputText id=\"name\"\r\n                           [(ngModel)]=\"searchInfo.name\"\r\n                           formControlName=\"name\"\r\n                    />\r\n                    <label htmlFor=\"name\">{{tranService.translate(\"ratingPlan.label.planName\")}}</label>\r\n                </span>\r\n        </div>\r\n        <!-- Trang thai -->\r\n        <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\" [showClear]=\"true\"\r\n                                id=\"status\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"searchInfo.status\"\r\n                                formControlName=\"status\"\r\n                                [options]=\"listStatus\"\r\n                                optionLabel=\"name\"\r\n                                optionValue=\"value\"\r\n                    ></p-dropdown>\r\n                    <label class=\"label-dropdown\" for=\"status\">{{tranService.translate(\"ratingPlan.label.status\")}}</label>\r\n                </span>\r\n        </div>\r\n        <!-- chu ky -->\r\n        <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\" [showClear]=\"true\"\r\n                                id=\"cycleTimeUnit\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"searchInfo.cycleTimeUnit\"\r\n                                formControlName=\"cycleTimeUnit\"\r\n                                [options]=\"listCycle\"\r\n                                optionLabel=\"name\"\r\n                                optionValue=\"value\"\r\n                    ></p-dropdown>\r\n                    <label class=\"label-dropdown\" for=\"cycleTimeUnit\">{{tranService.translate(\"ratingPlan.label.cycle\")}}</label>\r\n                </span>\r\n        </div>\r\n        <!-- hinh thuc thanh toan -->\r\n        <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\" [showClear]=\"true\"\r\n                                id=\"paidType\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"searchInfo.paidType\"\r\n                                formControlName=\"paidType\"\r\n                                [options]=\"listPaidType\"\r\n                                optionLabel=\"name\"\r\n                                optionValue=\"value\"\r\n                    ></p-dropdown>\r\n                    <label class=\"label-dropdown\" for=\"paidType\">{{tranService.translate(\"ratingPlan.label.paidType\")}}</label>\r\n                </span>\r\n        </div>\r\n        <!-- loai khach hang -->\r\n        <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\" [showClear]=\"true\"\r\n                                id=\"customerType\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"searchInfo.customerType\"\r\n                                formControlName=\"customerType\"\r\n                                [options]=\"listCustomerType\"\r\n                                optionLabel=\"name\"\r\n                                optionValue=\"value\"\r\n                    ></p-dropdown>\r\n                    <label class=\"label-dropdown\" for=\"customerType\">{{tranService.translate(\"ratingPlan.label.customerType\")}}</label>\r\n                </span>\r\n        </div>\r\n        <!-- pham vi goi cuoc -->\r\n        <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\" [showClear]=\"true\"\r\n                                id=\"ratingScope\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"searchInfo.ratingScope\"\r\n                                formControlName=\"ratingScope\"\r\n                                [options]=\"listRatingScope\"\r\n                                optionLabel=\"name\"\r\n                                optionValue=\"value\"\r\n                    ></p-dropdown>\r\n                    <label class=\"label-dropdown\" for=\"ratingScope\">{{tranService.translate(\"ratingPlan.label.ratingScope\")}}</label>\r\n                </span>\r\n        </div>\r\n        <div class=\"col-3 pb-0\">\r\n            <p-button icon=\"pi pi-search\"\r\n                      styleClass=\"p-button-rounded p-button-secondary p-button-text button-search\"\r\n                      type=\"submit\"\r\n            ></p-button>\r\n        </div>\r\n    </div>\r\n    </p-panel>\r\n</form>\r\n\r\n<table-vnpt\r\n    [fieldId]=\"'id'\"\r\n    [(selectItems)]=\"selectItems\"\r\n    [columns]=\"columns\"\r\n    [dataSet]=\"dataSet\"\r\n    [options]=\"optionTable\"\r\n    [loadData]=\"search.bind(this)\"\r\n    [pageNumber]=\"pageNumber\"\r\n    [pageSize]=\"pageSize\"\r\n    [sort]=\"sort\"\r\n    [params]=\"searchInfo\"\r\n    [labelTable]=\"this.tranService.translate('global.menu.listplan')\"\r\n></table-vnpt>\r\n\r\n\r\n<!-- dialog assign plan -->\r\n<form [formGroup]=\"formSearchUser\" (ngSubmit)=\"onSubmitSearchUser()\">\r\n    <div class=\"flex justify-content-center dialog-push-group\">\r\n        <p-dialog [header]=\"tranService.translate('ratingPlan.label.assignPlan')\" [(visible)]=\"isShowDialogAssignPlan\" [modal]=\"true\" [style]=\"{ width: '850px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n            <div class=\"grid\">\r\n                <!-- Ten dang nhap -->\r\n                <div class=\"col-3\">\r\n                    <span class=\"p-float-label\">\r\n                        <input pInputText\r\n                               class=\"w-full\"\r\n                               pInputText id=\"username\"\r\n                               [(ngModel)]=\"searchInfoUser.username\"\r\n                               formControlName=\"username\"\r\n                        />\r\n                        <label htmlFor=\"username\">{{tranService.translate(\"ratingPlan.label.username\")}}</label>\r\n                    </span>\r\n                </div>\r\n                <!-- Ho ten -->\r\n                <div class=\"col-3\">\r\n                    <span class=\"p-float-label\">\r\n                        <input pInputText\r\n                               class=\"w-full\"\r\n                               pInputText id=\"fullName\"\r\n                               [(ngModel)]=\"searchInfoUser.fullName\"\r\n                               formControlName=\"fullName\"\r\n                        />\r\n                        <label htmlFor=\"fullName\">{{tranService.translate(\"ratingPlan.label.fullName\")}}</label>\r\n                    </span>\r\n                </div>\r\n                <!-- Thanh pho -->\r\n                <div class=\"col-3\" [class]=\"userType == allUserType.ADMIN ? '' : 'flex flex-row justify-content-start align-items-center'\">\r\n                    <span class=\"p-float-label\" *ngIf=\"userType == allUserType.ADMIN\">\r\n                        <p-dropdown styleClass=\"w-full\" [showClear]=\"true\"\r\n                                id=\"provinceCode\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"searchInfoUser.provinceCode\"\r\n                                [options]=\"listProvince\"\r\n                                optionLabel=\"display\"\r\n                                optionValue=\"code\"\r\n                                formControlName=\"provinceCode\" [filter] = true\r\n                        ></p-dropdown>\r\n                        <label for=\"provinceCode\">{{tranService.translate(\"ratingPlan.label.province\")}}</label>\r\n                    </span>\r\n                    <span *ngIf=\"userType != allUserType.ADMIN\">{{tranService.translate(\"account.label.province\")}}: {{provinceInfo}}</span>\r\n                </div>\r\n                <div class=\"col-3 pb-0\">\r\n                    <p-button icon=\"pi pi-search\"\r\n                              styleClass=\"p-button-rounded p-button-secondary p-button-text button-search\"\r\n                              type=\"submit\"\r\n                    ></p-button>\r\n                </div>\r\n            </div>\r\n            <table-vnpt\r\n                    [fieldId]=\"'id'\"\r\n                    [pageNumber]=\"pageNumberAssign\"\r\n                    [pageSize]=\"pageSizeAssign\"\r\n                    [(selectItems)]=\"selectItemsUser\"\r\n                    [columns]=\"columnsInfoUser\"\r\n                    [dataSet]=\"dataSetAssignPlan\"\r\n                    [options]=\"optionTableAssignPLan\"\r\n                    [loadData]=\"searchUser.bind(this)\"\r\n                    [rowsPerPageOptions]=\"[5,10,20,25,50]\"\r\n                    [scrollHeight]=\"'400px'\"\r\n                    [sort]=\"sort\"\r\n                    [params]=\"searchInfoUser\"\r\n            ></table-vnpt>\r\n            <div class=\"flex flex-row justify-content-center align-items-center\" style=\"padding-top: 30px\">\r\n                <p-button styleClass=\"p-button-secondary\" [label]=\"tranService.translate('global.button.cancel')\" (click)=\"isShowDialogAssignPlan = false\" [style]=\"{'margin-right': '20px'}\"></p-button>\r\n                <p-button styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.save')\" [disabled]=\"checkInValidAssignPlan()\" (click)=\"assignPlan()\" ></p-button>\r\n            </div>\r\n        </p-dialog>\r\n    </div>\r\n</form>\r\n\r\n<div class=\"flex justify-content-center dialog-vnpt \">\r\n    <p-dialog [header]=\"tranService.translate('global.menu.detailplan')\" [(visible)]=\"isShowModalDetail\" [modal]=\"true\" [style]=\"{ width: '980px' }\" [draggable]=\"false\" [resizable]=\"false\" *ngIf=\"isShowModalDetail\">\r\n        <div class=\"flex flex-row justify-content-start align-items-start\">\r\n            <p-button styleClass=\"mr-2 p-button-secondary\" *ngIf=\"(ratingPlanInfo.status == planStatuses.CREATE_NEW || ratingPlanInfo.status == planStatuses.DEACTIVATED) && checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.ACTIVE])\" [label]=\"tranService.translate('global.button.active')\" icon=\"\" (click)=\"active()\" ></p-button>\r\n            <p-button styleClass=\"mr-2 p-button-secondary\" *ngIf=\"ratingPlanInfo.status == planStatuses.PENDING && checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.APPROVE])\" [label]=\"tranService.translate('global.button.approve')\" icon=\"\" (click)=\"approve()\" ></p-button>\r\n            <p-button styleClass=\"mr-2 p-button-secondary\" *ngIf=\"ratingPlanInfo.status == planStatuses.ACTIVATED && checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.SUPPEND])\" [label]=\"tranService.translate('global.button.suspend')\" icon=\"\" (click)=\"suspend()\" ></p-button>\r\n        </div>\r\n        <p-card  styleClass=\"h-full\" [style]=\"{'width': 'calc(100% + 16px)'}\">\r\n            <div class=\"col ratingPlan-detail custom-rating-detail pr-0 flex\" style=\"border:1px solid black; margin-bottom: 20px\">\r\n                <div class=\"flex-1 \">\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.planCode\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.code}}</span>\r\n                    </div>\r\n                    <div class=\" grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.planName\")}}</span>\r\n                        <span class=\"inline-block col-fixed\" style=\"min-width: 200px; max-width: 400px; word-wrap: break-word; white-space: normal;\">{{ratingPlanInfo.name}}</span>\r\n                    </div>\r\n                    <div class=\" grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.status\")}}</span>\r\n                        <div  class=\"text-white w-auto col\">\r\n                            <span [class]=\"getClassStatus(ratingPlanInfo.status)\">{{getNameStatus(ratingPlanInfo.status)}}</span>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\" grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.dispatchCode\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.dispatchCode}}</span>\r\n                    </div>\r\n                    <div class=\" grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.customerType\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{getNameCustomerType(ratingPlanInfo.customerType)}}</span>\r\n                    </div>\r\n                    <div class=\" grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.description\")}}</span>\r\n                        <span class=\"inline-block col-fixed\" style=\"min-width: 200px; max-width: 400px; word-wrap: break-word; white-space: normal;\">{{ratingPlanInfo.description}}</span>\r\n                    </div>\r\n                    <!--                    <div class=\"mt-1 grid\">-->\r\n                    <!--                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.motachuaco\")}}</span>-->\r\n                    <!--                    </div>-->\r\n                </div>\r\n                <div class=\"flex-1\">\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.subscriptionFee\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.subscriptionFee}}&nbsp; &nbsp; &nbsp; &nbsp; {{tranService.translate(\"ratingPlan.text.textDong\")}}&nbsp;{{tranService.translate(\"ratingPlan.text.vat\")}}</span>\r\n                    </div>\r\n                    <div class=\"grid\">\r\n                        <span class=\"inline-block col-fixed\">\r\n                            <span>\r\n                                <p-radioButton [disabled]=\"true\" [value]=\"subscriptionTypes[0].ip\" [(ngModel)]=\"ratingPlanInfo.subscriptionType\" inputId=\"typeIp1\"></p-radioButton>\r\n                                &nbsp;\r\n                                <span>{{tranService.translate(\"ratingPlan.subscriptionType.post\")}}</span>\r\n                            </span>\r\n                        </span>\r\n                        <span class=\"inline-block col-fixed radioButton2\" style=\"padding-left: 108px\">\r\n                            <span>\r\n                                <p-radioButton [disabled]=\"true\" [value]=\"subscriptionTypes[1].ip\" [(ngModel)]=\"ratingPlanInfo.subscriptionType\" inputId=\"typeIp2\"></p-radioButton>\r\n                                &nbsp;\r\n                                <span>{{tranService.translate(\"ratingPlan.subscriptionType.pre\")}}</span>\r\n                            </span>\r\n                        </span>\r\n                    </div>\r\n\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.cycle\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{getCycleTimeUnit(ratingPlanInfo.cycleTimeUnit)}}</span>\r\n                    </div>\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.cycleInterval\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.cycleInterval}}</span>\r\n                    </div>\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.reload\")}}</span>\r\n                        <div style=\"padding-top: 10px; padding-left: 13px\">\r\n                            <p-inputSwitch [(ngModel)]=\"checkedReload\" [disabled]=\"true\"></p-inputSwitch>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.ratingScope\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{getRatingScope(ratingPlanInfo.ratingScope)}}</span>\r\n                    </div>\r\n                    <div class=\"grid\" *ngIf=\"ratingPlanInfo.ratingScope == planScopes.PROVINCE || ratingPlanInfo.ratingScope == planScopes.CUSTOMER\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.province\")}}</span>\r\n                        <span class=\"inline-block col-fixed\" style=\"width: fit-content !important;\">{{myProvices}}</span>\r\n                    </div>\r\n                    <div class=\"grid\" *ngIf=\"ratingPlanInfo.ratingScope == planScopes.CUSTOMER\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px; cursor: pointer; text-decoration: underline; color: blue; transition: color 0.3s;\" class=\"inline-block col-fixed\" (click)=\"openDialogAddCustomerAccount()\">{{tranService.translate(\"account.label.showCustomerAccount\")}}</span>\r\n<!--                        <span class=\"inline-block col-fixed\" style=\"width: fit-content !important;\">{{myProvices}}</span>-->\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"mt-1 grid\">\r\n                <span style=\"font-size: 20px;margin-left: 10px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.flat\")}}</span>\r\n            </div>\r\n            <div class=\"col ratingPlan-detail custom-rating-detail-limit pr-0 flex\"  style=\"border:1px solid black; margin-bottom: 20px\" id = \"name\">\r\n                <div class=\"flex-1\">\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.limitDataUsage\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.limitDataUsage}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.dataMax\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.dataMax}}</span>\r\n                    </div>\r\n                </div>\r\n                <div class=\"flex-1\">\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.limitSmsInside\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.limitSmsInside}}</span>\r\n                    </div>\r\n                    <div class=\"mt-1 grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.limitSmsOutside\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.limitSmsOutside}}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"mt-1 grid header-grid\">\r\n                <span style=\"font-size: 20px;margin-left: 10px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.flexible\")}}</span>\r\n                <div style=\"padding-top: 18px\">\r\n                    <p-inputSwitch [(ngModel)]=\"checkedFlexible\" [disabled]=\"true\"></p-inputSwitch>\r\n                </div>\r\n            </div>\r\n            <div class=\"col ratingPlan-detail pr-0 flex\"  style=\"border:1px solid black;\" id = \"flexible\">\r\n                <div class=\"flex-1\">\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.feePerDataUnit\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.feePerDataUnit}}</span>\r\n                    </div>\r\n                    <div class=\" grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.squeezedSpeed\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.downSpeed}}</span>\r\n                    </div>\r\n                    <div class=\" grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.feeSmsInside\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.feeSmsInside}}</span>\r\n                    </div>\r\n                    <div class=\" grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.feeSmsOutside\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.feeSmsOutside}}</span>\r\n                    </div>\r\n                    <div class=\" grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"ratingPlan.label.maximumFee\")}}</span>\r\n                        <span class=\"inline-block col-fixed\">{{ratingPlanInfo.maximumFee}}</span>\r\n                    </div>\r\n                </div>\r\n                <div class=\"flex-1\">\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">/&nbsp; &nbsp; &nbsp; {{ratingPlanInfo.dataRoundUnit}} &nbsp; &nbsp; &nbsp; &nbsp;   KB</span>\r\n                    </div>\r\n                    <div class=\"grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;padding-bottom: 5px\" class=\"inline-block col-fixed\">/&nbsp; &nbsp; &nbsp; {{ratingPlanInfo.squeezedSpeed}}</span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </p-card>\r\n    </p-dialog>\r\n</div>\r\n<div class=\"flex justify-content-center dialog-push-group\">\r\n    <p-dialog [header]=\"tranService.translate('account.label.showCustomerAccount')\" [(visible)]=\"isShowDialogShowCustomerAccount\" [modal]=\"true\" [style]=\"{ width: '1000px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n\r\n        <table-vnpt\r\n            [fieldId]=\"'id1'\"\r\n            [columns]=\"columnsInfoUserForDetail\"\r\n            [dataSet]=\"dataSetAssignPlanForDetail\"\r\n            [options]=\"optionTableShowCustomerAccount\"\r\n            scrollHeight=\"300px\"\r\n        ></table-vnpt>\r\n    </p-dialog>\r\n</div>\r\n\r\n"], "mappings": "AAGA,SAAQA,SAAS,QAAO,iCAAiC;AAGzD,SAAQC,aAAa,QAAO,wBAAwB;;;;;;;;;;;;;;;;;;;;;;ICA5CC,EAAA,CAAAC,SAAA,mBAA0O;;;;IAA5ID,EAAA,CAAAE,UAAA,UAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,yBAAuD,eAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA;;;;;;IAiJzIP,EAAA,CAAAQ,cAAA,eAAkE;IAGtDR,EAAA,CAAAS,UAAA,2BAAAC,gFAAAC,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAAaf,EAAA,CAAAgB,WAAA,CAAAF,MAAA,CAAAG,cAAA,CAAAC,YAAA,GAAAP,MAAA,CACxC;IAAA,EADoE;IAKhDX,EAAA,CAAAmB,YAAA,EAAa;IACdnB,EAAA,CAAAQ,cAAA,gBAA0B;IAAAR,EAAA,CAAAoB,MAAA,GAAsD;IAAApB,EAAA,CAAAmB,YAAA,EAAQ;;;;IARxDnB,EAAA,CAAAqB,SAAA,GAAkB;IAAlBrB,EAAA,CAAAE,UAAA,mBAAkB,uCAAAoB,MAAA,CAAAL,cAAA,CAAAC,YAAA,aAAAI,MAAA,CAAAC,YAAA;IAQxBvB,EAAA,CAAAqB,SAAA,GAAsD;IAAtDrB,EAAA,CAAAwB,iBAAA,CAAAF,MAAA,CAAAlB,WAAA,CAAAC,SAAA,8BAAsD;;;;;IAEpFL,EAAA,CAAAQ,cAAA,WAA4C;IAAAR,EAAA,CAAAoB,MAAA,GAAqE;IAAApB,EAAA,CAAAmB,YAAA,EAAO;;;;IAA5EnB,EAAA,CAAAqB,SAAA,GAAqE;IAArErB,EAAA,CAAAyB,kBAAA,KAAAC,MAAA,CAAAtB,WAAA,CAAAC,SAAA,kCAAAqB,MAAA,CAAAC,YAAA,KAAqE;;;;;;IAkCzH3B,EAAA,CAAAQ,cAAA,mBAA8S;IAApBR,EAAA,CAAAS,UAAA,mBAAAmB,qFAAA;MAAA5B,EAAA,CAAAY,aAAA,CAAAiB,IAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAc,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAAE/B,EAAA,CAAAmB,YAAA,EAAW;;;;IAA/FnB,EAAA,CAAAE,UAAA,UAAA8B,MAAA,CAAA5B,WAAA,CAAAC,SAAA,yBAAuD;;;;;;IACjRL,EAAA,CAAAQ,cAAA,mBAAuP;IAArBR,EAAA,CAAAS,UAAA,mBAAAwB,qFAAA;MAAAjC,EAAA,CAAAY,aAAA,CAAAsB,IAAA;MAAA,MAAAC,OAAA,GAAAnC,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAmB,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAAEpC,EAAA,CAAAmB,YAAA,EAAW;;;;IAAjGnB,EAAA,CAAAE,UAAA,UAAAmC,MAAA,CAAAjC,WAAA,CAAAC,SAAA,0BAAwD;;;;;;IACzNL,EAAA,CAAAQ,cAAA,mBAAyP;IAArBR,EAAA,CAAAS,UAAA,mBAAA6B,qFAAA;MAAAtC,EAAA,CAAAY,aAAA,CAAA2B,IAAA;MAAA,MAAAC,OAAA,GAAAxC,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAwB,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAAEzC,EAAA,CAAAmB,YAAA,EAAW;;;;IAAjGnB,EAAA,CAAAE,UAAA,UAAAwC,MAAA,CAAAtC,WAAA,CAAAC,SAAA,0BAAwD;;;;;IA2EnNL,EAAA,CAAAQ,cAAA,cAAiI;IAC1BR,EAAA,CAAAoB,MAAA,GAAsD;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAChKnB,EAAA,CAAAQ,cAAA,eAA4E;IAAAR,EAAA,CAAAoB,MAAA,GAAc;IAAApB,EAAA,CAAAmB,YAAA,EAAO;;;;IADEnB,EAAA,CAAAqB,SAAA,GAAsD;IAAtDrB,EAAA,CAAAwB,iBAAA,CAAAmB,MAAA,CAAAvC,WAAA,CAAAC,SAAA,8BAAsD;IAC7EL,EAAA,CAAAqB,SAAA,GAAc;IAAdrB,EAAA,CAAAwB,iBAAA,CAAAmB,MAAA,CAAAC,UAAA,CAAc;;;;;;IAE9F5C,EAAA,CAAAQ,cAAA,cAA4E;IAC8GR,EAAA,CAAAS,UAAA,mBAAAoC,6EAAA;MAAA7C,EAAA,CAAAY,aAAA,CAAAkC,IAAA;MAAA,MAAAC,OAAA,GAAA/C,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAA+B,OAAA,CAAAC,4BAAA,EAA8B;IAAA,EAAC;IAAChD,EAAA,CAAAoB,MAAA,GAA8D;IAAApB,EAAA,CAAAmB,YAAA,EAAO;;;;IAArEnB,EAAA,CAAAqB,SAAA,GAA8D;IAA9DrB,EAAA,CAAAwB,iBAAA,CAAAyB,OAAA,CAAA7C,WAAA,CAAAC,SAAA,sCAA8D;;;;;;;;;;;;;;;;;;;IApFjTL,EAAA,CAAAQ,cAAA,mBAAmN;IAA9IR,EAAA,CAAAS,UAAA,2BAAAyC,kFAAAvC,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAuC,IAAA;MAAA,MAAAC,OAAA,GAAApD,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAAoC,OAAA,CAAAC,iBAAA,GAAA1C,MAAA;IAAA,EAA+B;IAChGX,EAAA,CAAAQ,cAAA,cAAmE;IAC/DR,EAAA,CAAAsD,UAAA,IAAAC,0DAAA,uBAAyT;IACzTvD,EAAA,CAAAsD,UAAA,IAAAE,0DAAA,uBAAkQ;IAClQxD,EAAA,CAAAsD,UAAA,IAAAG,0DAAA,uBAAoQ;IACxQzD,EAAA,CAAAmB,YAAA,EAAM;IACNnB,EAAA,CAAAQ,cAAA,iBAAsE;IAI6CR,EAAA,CAAAoB,MAAA,IAAsD;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAChKnB,EAAA,CAAAQ,cAAA,gBAAqC;IAAAR,EAAA,CAAAoB,MAAA,IAAuB;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAEvEnB,EAAA,CAAAQ,cAAA,eAAmB;IACoFR,EAAA,CAAAoB,MAAA,IAAsD;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAChKnB,EAAA,CAAAQ,cAAA,gBAA6H;IAAAR,EAAA,CAAAoB,MAAA,IAAuB;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAE/JnB,EAAA,CAAAQ,cAAA,eAAmB;IACoFR,EAAA,CAAAoB,MAAA,IAAoD;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAC9JnB,EAAA,CAAAQ,cAAA,eAAoC;IACsBR,EAAA,CAAAoB,MAAA,IAAwC;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAG7GnB,EAAA,CAAAQ,cAAA,eAAmB;IACoFR,EAAA,CAAAoB,MAAA,IAA0D;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IACpKnB,EAAA,CAAAQ,cAAA,gBAAqC;IAAAR,EAAA,CAAAoB,MAAA,IAA+B;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAE/EnB,EAAA,CAAAQ,cAAA,eAAmB;IACoFR,EAAA,CAAAoB,MAAA,IAA0D;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IACpKnB,EAAA,CAAAQ,cAAA,gBAAqC;IAAAR,EAAA,CAAAoB,MAAA,IAAoD;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAEpGnB,EAAA,CAAAQ,cAAA,eAAmB;IACoFR,EAAA,CAAAoB,MAAA,IAAyD;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IACnKnB,EAAA,CAAAQ,cAAA,gBAA6H;IAAAR,EAAA,CAAAoB,MAAA,IAA8B;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAM1KnB,EAAA,CAAAQ,cAAA,eAAoB;IAEmER,EAAA,CAAAoB,MAAA,IAA6D;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IACnJnB,EAAA,CAAAQ,cAAA,gBAAqC;IAAAR,EAAA,CAAAoB,MAAA,IAAyK;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAEzNnB,EAAA,CAAAQ,cAAA,eAAkB;IAG6DR,EAAA,CAAAS,UAAA,2BAAAiD,wFAAA/C,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAuC,IAAA;MAAA,MAAAQ,OAAA,GAAA3D,EAAA,CAAAe,aAAA;MAAA,OAAaf,EAAA,CAAAgB,WAAA,CAAA2C,OAAA,CAAAC,cAAA,CAAAC,gBAAA,GAAAlD,MAAA,CAAuC;IAAA,EAAP;IAAmBX,EAAA,CAAAmB,YAAA,EAAgB;IACnJnB,EAAA,CAAAoB,MAAA,gBACA;IAAApB,EAAA,CAAAQ,cAAA,YAAM;IAAAR,EAAA,CAAAoB,MAAA,IAA6D;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAGlFnB,EAAA,CAAAQ,cAAA,gBAA8E;IAEHR,EAAA,CAAAS,UAAA,2BAAAqD,wFAAAnD,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAuC,IAAA;MAAA,MAAAY,OAAA,GAAA/D,EAAA,CAAAe,aAAA;MAAA,OAAaf,EAAA,CAAAgB,WAAA,CAAA+C,OAAA,CAAAH,cAAA,CAAAC,gBAAA,GAAAlD,MAAA,CAAuC;IAAA,EAAP;IAAmBX,EAAA,CAAAmB,YAAA,EAAgB;IACnJnB,EAAA,CAAAoB,MAAA,gBACA;IAAApB,EAAA,CAAAQ,cAAA,YAAM;IAAAR,EAAA,CAAAoB,MAAA,IAA4D;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAKrFnB,EAAA,CAAAQ,cAAA,eAAkB;IACiER,EAAA,CAAAoB,MAAA,IAAmD;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IACzInB,EAAA,CAAAQ,cAAA,gBAAqC;IAAAR,EAAA,CAAAoB,MAAA,IAAkD;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAElGnB,EAAA,CAAAQ,cAAA,eAAkB;IACiER,EAAA,CAAAoB,MAAA,IAA2D;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IACjJnB,EAAA,CAAAQ,cAAA,gBAAqC;IAAAR,EAAA,CAAAoB,MAAA,IAAgC;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAEhFnB,EAAA,CAAAQ,cAAA,eAAkB;IACkER,EAAA,CAAAoB,MAAA,IAAoD;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAC3InB,EAAA,CAAAQ,cAAA,eAAmD;IAChCR,EAAA,CAAAS,UAAA,2BAAAuD,wFAAArD,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAuC,IAAA;MAAA,MAAAc,OAAA,GAAAjE,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAAiD,OAAA,CAAAC,aAAA,GAAAvD,MAAA;IAAA,EAA2B;IAAmBX,EAAA,CAAAmB,YAAA,EAAgB;IAGrFnB,EAAA,CAAAQ,cAAA,eAAkB;IACiER,EAAA,CAAAoB,MAAA,IAAyD;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAC/InB,EAAA,CAAAQ,cAAA,gBAAqC;IAAAR,EAAA,CAAAoB,MAAA,IAA8C;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAE9FnB,EAAA,CAAAsD,UAAA,KAAAa,sDAAA,kBAGM;IACNnE,EAAA,CAAAsD,UAAA,KAAAc,sDAAA,kBAGM;IACVpE,EAAA,CAAAmB,YAAA,EAAM;IAGVnB,EAAA,CAAAQ,cAAA,eAAuB;IAC4DR,EAAA,CAAAoB,MAAA,IAAkD;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAE5InB,EAAA,CAAAQ,cAAA,eAAyI;IAG1BR,EAAA,CAAAoB,MAAA,IAA4D;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IACtKnB,EAAA,CAAAQ,cAAA,gBAAqC;IAAAR,EAAA,CAAAoB,MAAA,IAAiC;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAEjFnB,EAAA,CAAAQ,cAAA,eAAuB;IACgFR,EAAA,CAAAoB,MAAA,IAAqD;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAC/JnB,EAAA,CAAAQ,cAAA,gBAAqC;IAAAR,EAAA,CAAAoB,MAAA,IAA0B;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAG9EnB,EAAA,CAAAQ,cAAA,eAAoB;IAEuFR,EAAA,CAAAoB,MAAA,IAA4D;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IACtKnB,EAAA,CAAAQ,cAAA,gBAAqC;IAAAR,EAAA,CAAAoB,MAAA,KAAiC;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAEjFnB,EAAA,CAAAQ,cAAA,gBAAuB;IACgFR,EAAA,CAAAoB,MAAA,KAA6D;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IACvKnB,EAAA,CAAAQ,cAAA,iBAAqC;IAAAR,EAAA,CAAAoB,MAAA,KAAkC;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAK1FnB,EAAA,CAAAQ,cAAA,gBAAmC;IACgDR,EAAA,CAAAoB,MAAA,KAAsD;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAC5InB,EAAA,CAAAQ,cAAA,gBAA+B;IACZR,EAAA,CAAAS,UAAA,2BAAA4D,yFAAA1D,MAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAuC,IAAA;MAAA,MAAAmB,OAAA,GAAAtE,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAAAsD,OAAA,CAAAC,eAAA,GAAA5D,MAAA;IAAA,EAA6B;IAAmBX,EAAA,CAAAmB,YAAA,EAAgB;IAGvFnB,EAAA,CAAAQ,cAAA,gBAA8F;IAGiBR,EAAA,CAAAoB,MAAA,KAA4D;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IACtKnB,EAAA,CAAAQ,cAAA,iBAAqC;IAAAR,EAAA,CAAAoB,MAAA,KAAiC;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAEjFnB,EAAA,CAAAQ,cAAA,gBAAmB;IACoFR,EAAA,CAAAoB,MAAA,KAA2D;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IACrKnB,EAAA,CAAAQ,cAAA,iBAAqC;IAAAR,EAAA,CAAAoB,MAAA,KAA4B;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAE5EnB,EAAA,CAAAQ,cAAA,gBAAmB;IACoFR,EAAA,CAAAoB,MAAA,KAA0D;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IACpKnB,EAAA,CAAAQ,cAAA,iBAAqC;IAAAR,EAAA,CAAAoB,MAAA,KAA+B;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAE/EnB,EAAA,CAAAQ,cAAA,gBAAmB;IACoFR,EAAA,CAAAoB,MAAA,KAA2D;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IACrKnB,EAAA,CAAAQ,cAAA,iBAAqC;IAAAR,EAAA,CAAAoB,MAAA,KAAgC;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAEhFnB,EAAA,CAAAQ,cAAA,gBAAmB;IACiER,EAAA,CAAAoB,MAAA,KAAwD;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAC/InB,EAAA,CAAAQ,cAAA,iBAAqC;IAAAR,EAAA,CAAAoB,MAAA,KAA6B;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAGjFnB,EAAA,CAAAQ,cAAA,gBAAoB;IAEuFR,EAAA,CAAAoB,MAAA,KAAuF;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAErMnB,EAAA,CAAAQ,cAAA,gBAAkB;IACqFR,EAAA,CAAAoB,MAAA,KAAsD;IAAApB,EAAA,CAAAmB,YAAA,EAAO;;;;IAtJhEnB,EAAA,CAAAwE,UAAA,CAAAxE,EAAA,CAAAM,eAAA,KAAAmE,GAAA,EAA4B;IAAtIzE,EAAA,CAAAE,UAAA,WAAAwE,MAAA,CAAAtE,WAAA,CAAAC,SAAA,2BAA0D,YAAAqE,MAAA,CAAArB,iBAAA;IAEZrD,EAAA,CAAAqB,SAAA,GAAwK;IAAxKrB,EAAA,CAAAE,UAAA,UAAAwE,MAAA,CAAAd,cAAA,CAAAe,MAAA,IAAAD,MAAA,CAAAE,YAAA,CAAAC,UAAA,IAAAH,MAAA,CAAAd,cAAA,CAAAe,MAAA,IAAAD,MAAA,CAAAE,YAAA,CAAAE,WAAA,KAAAJ,MAAA,CAAAK,WAAA,CAAA/E,EAAA,CAAAgF,eAAA,KAAAC,GAAA,EAAAP,MAAA,CAAA5E,SAAA,CAAAoF,WAAA,CAAAC,WAAA,CAAAC,MAAA,GAAwK;IACxKpF,EAAA,CAAAqB,SAAA,GAA+G;IAA/GrB,EAAA,CAAAE,UAAA,SAAAwE,MAAA,CAAAd,cAAA,CAAAe,MAAA,IAAAD,MAAA,CAAAE,YAAA,CAAAS,OAAA,IAAAX,MAAA,CAAAK,WAAA,CAAA/E,EAAA,CAAAgF,eAAA,KAAAC,GAAA,EAAAP,MAAA,CAAA5E,SAAA,CAAAoF,WAAA,CAAAC,WAAA,CAAAG,OAAA,GAA+G;IAC/GtF,EAAA,CAAAqB,SAAA,GAAiH;IAAjHrB,EAAA,CAAAE,UAAA,SAAAwE,MAAA,CAAAd,cAAA,CAAAe,MAAA,IAAAD,MAAA,CAAAE,YAAA,CAAAW,SAAA,IAAAb,MAAA,CAAAK,WAAA,CAAA/E,EAAA,CAAAgF,eAAA,KAAAC,GAAA,EAAAP,MAAA,CAAA5E,SAAA,CAAAoF,WAAA,CAAAC,WAAA,CAAAK,OAAA,GAAiH;IAExIxF,EAAA,CAAAqB,SAAA,GAAwC;IAAxCrB,EAAA,CAAAwE,UAAA,CAAAxE,EAAA,CAAAM,eAAA,KAAAmF,GAAA,EAAwC;IAI8CzF,EAAA,CAAAqB,SAAA,GAAsD;IAAtDrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,8BAAsD;IACpHL,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAd,cAAA,CAAA8B,IAAA,CAAuB;IAGuC1F,EAAA,CAAAqB,SAAA,GAAsD;IAAtDrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,8BAAsD;IAC5BL,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAd,cAAA,CAAA+B,IAAA,CAAuB;IAGjD3F,EAAA,CAAAqB,SAAA,GAAoD;IAApDrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,4BAAoD;IAE7IL,EAAA,CAAAqB,SAAA,GAA+C;IAA/CrB,EAAA,CAAA4F,UAAA,CAAAlB,MAAA,CAAAmB,cAAA,CAAAnB,MAAA,CAAAd,cAAA,CAAAe,MAAA,EAA+C;IAAC3E,EAAA,CAAAqB,SAAA,GAAwC;IAAxCrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAoB,aAAA,CAAApB,MAAA,CAAAd,cAAA,CAAAe,MAAA,EAAwC;IAIC3E,EAAA,CAAAqB,SAAA,GAA0D;IAA1DrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,kCAA0D;IACxHL,EAAA,CAAAqB,SAAA,GAA+B;IAA/BrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAd,cAAA,CAAAmC,YAAA,CAA+B;IAG+B/F,EAAA,CAAAqB,SAAA,GAA0D;IAA1DrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,kCAA0D;IACxHL,EAAA,CAAAqB,SAAA,GAAoD;IAApDrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAsB,mBAAA,CAAAtB,MAAA,CAAAd,cAAA,CAAAqC,YAAA,EAAoD;IAGUjG,EAAA,CAAAqB,SAAA,GAAyD;IAAzDrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,iCAAyD;IAC/BL,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAd,cAAA,CAAAsC,WAAA,CAA8B;IAQ5ElG,EAAA,CAAAqB,SAAA,GAA6D;IAA7DrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,qCAA6D;IACvGL,EAAA,CAAAqB,SAAA,GAAyK;IAAzKrB,EAAA,CAAAmG,kBAAA,KAAAzB,MAAA,CAAAd,cAAA,CAAAwC,eAAA,kCAAA1B,MAAA,CAAAtE,WAAA,CAAAC,SAAA,wCAAAqE,MAAA,CAAAtE,WAAA,CAAAC,SAAA,4BAAyK;IAKvLL,EAAA,CAAAqB,SAAA,GAAiB;IAAjBrB,EAAA,CAAAE,UAAA,kBAAiB,UAAAwE,MAAA,CAAA2B,iBAAA,IAAAC,EAAA,aAAA5B,MAAA,CAAAd,cAAA,CAAAC,gBAAA;IAE1B7D,EAAA,CAAAqB,SAAA,GAA6D;IAA7DrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,qCAA6D;IAKpDL,EAAA,CAAAqB,SAAA,GAAiB;IAAjBrB,EAAA,CAAAE,UAAA,kBAAiB,UAAAwE,MAAA,CAAA2B,iBAAA,IAAAC,EAAA,aAAA5B,MAAA,CAAAd,cAAA,CAAAC,gBAAA;IAE1B7D,EAAA,CAAAqB,SAAA,GAA4D;IAA5DrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,oCAA4D;IAMKL,EAAA,CAAAqB,SAAA,GAAmD;IAAnDrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,2BAAmD;IAC7FL,EAAA,CAAAqB,SAAA,GAAkD;IAAlDrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAA6B,gBAAA,CAAA7B,MAAA,CAAAd,cAAA,CAAA4C,aAAA,EAAkD;IAGRxG,EAAA,CAAAqB,SAAA,GAA2D;IAA3DrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,mCAA2D;IACrGL,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAd,cAAA,CAAA6C,aAAA,CAAgC;IAGWzG,EAAA,CAAAqB,SAAA,GAAoD;IAApDrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,4BAAoD;IAEjHL,EAAA,CAAAqB,SAAA,GAA2B;IAA3BrB,EAAA,CAAAE,UAAA,YAAAwE,MAAA,CAAAR,aAAA,CAA2B;IAIiClE,EAAA,CAAAqB,SAAA,GAAyD;IAAzDrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,iCAAyD;IACnGL,EAAA,CAAAqB,SAAA,GAA8C;IAA9CrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAgC,cAAA,CAAAhC,MAAA,CAAAd,cAAA,CAAA+C,WAAA,EAA8C;IAEpE3G,EAAA,CAAAqB,SAAA,GAA4G;IAA5GrB,EAAA,CAAAE,UAAA,SAAAwE,MAAA,CAAAd,cAAA,CAAA+C,WAAA,IAAAjC,MAAA,CAAAkC,UAAA,CAAAC,QAAA,IAAAnC,MAAA,CAAAd,cAAA,CAAA+C,WAAA,IAAAjC,MAAA,CAAAkC,UAAA,CAAAE,QAAA,CAA4G;IAI5G9G,EAAA,CAAAqB,SAAA,GAAuD;IAAvDrB,EAAA,CAAAE,UAAA,SAAAwE,MAAA,CAAAd,cAAA,CAAA+C,WAAA,IAAAjC,MAAA,CAAAkC,UAAA,CAAAE,QAAA,CAAuD;IAQC9G,EAAA,CAAAqB,SAAA,GAAkD;IAAlDrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,0BAAkD;IAKtBL,EAAA,CAAAqB,SAAA,GAA4D;IAA5DrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,oCAA4D;IAC1HL,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAd,cAAA,CAAAmD,cAAA,CAAiC;IAG6B/G,EAAA,CAAAqB,SAAA,GAAqD;IAArDrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,6BAAqD;IACnHL,EAAA,CAAAqB,SAAA,GAA0B;IAA1BrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAd,cAAA,CAAAoD,OAAA,CAA0B;IAKoChH,EAAA,CAAAqB,SAAA,GAA4D;IAA5DrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,oCAA4D;IAC1HL,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAd,cAAA,CAAAqD,cAAA,CAAiC;IAG6BjH,EAAA,CAAAqB,SAAA,GAA6D;IAA7DrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,qCAA6D;IAC3HL,EAAA,CAAAqB,SAAA,GAAkC;IAAlCrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAd,cAAA,CAAAsD,eAAA,CAAkC;IAMAlH,EAAA,CAAAqB,SAAA,GAAsD;IAAtDrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,8BAAsD;IAElHL,EAAA,CAAAqB,SAAA,GAA6B;IAA7BrB,EAAA,CAAAE,UAAA,YAAAwE,MAAA,CAAAH,eAAA,CAA6B;IAM2DvE,EAAA,CAAAqB,SAAA,GAA4D;IAA5DrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,oCAA4D;IAC1HL,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAd,cAAA,CAAAuD,cAAA,CAAiC;IAG6BnH,EAAA,CAAAqB,SAAA,GAA2D;IAA3DrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,mCAA2D;IACzHL,EAAA,CAAAqB,SAAA,GAA4B;IAA5BrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAd,cAAA,CAAAwD,SAAA,CAA4B;IAGkCpH,EAAA,CAAAqB,SAAA,GAA0D;IAA1DrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,kCAA0D;IACxHL,EAAA,CAAAqB,SAAA,GAA+B;IAA/BrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAd,cAAA,CAAAyD,YAAA,CAA+B;IAG+BrH,EAAA,CAAAqB,SAAA,GAA2D;IAA3DrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,mCAA2D;IACzHL,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAd,cAAA,CAAA0D,aAAA,CAAgC;IAGWtH,EAAA,CAAAqB,SAAA,GAAwD;IAAxDrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAtE,WAAA,CAAAC,SAAA,gCAAwD;IACnGL,EAAA,CAAAqB,SAAA,GAA6B;IAA7BrB,EAAA,CAAAwB,iBAAA,CAAAkD,MAAA,CAAAd,cAAA,CAAA2D,UAAA,CAA6B;IAKiCvH,EAAA,CAAAqB,SAAA,GAAuF;IAAvFrB,EAAA,CAAAwH,kBAAA,2BAAA9C,MAAA,CAAAd,cAAA,CAAA6D,aAAA,oCAAuF;IAGvFzH,EAAA,CAAAqB,SAAA,GAAsD;IAAtDrB,EAAA,CAAAwH,kBAAA,2BAAA9C,MAAA,CAAAd,cAAA,CAAA8D,aAAA,KAAsD;;;;;;;;;;;;;;;;;;;;;AD3UjL,OAAM,MAAOC,0BAA2B,SAAQ5H,aAAa;EAuGzD6H,YAAmBC,iBAAoC,EACnCC,cAA8B,EACtCC,WAAwB,EAChCC,QAAkB;IACd,KAAK,CAACA,QAAQ,CAAC;IAJJ,KAAAH,iBAAiB,GAAjBA,iBAAiB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACtB,KAAAC,WAAW,GAAXA,WAAW;IAzFvB,KAAAE,YAAY,GAAQ,EAAE;IAQtB,KAAAC,WAAW,GAAe,EAAE;IAa5B,KAAAC,eAAe,GAAgE,EAAE;IACjF,KAAAC,kBAAkB,GAAgE,CAAC;MAACC,EAAE,EAAE,CAAC,CAAC;MAAEnH,YAAY,EAAE;IAAE,CAAC,CAAC;IAW9G,KAAAoH,sBAAsB,GAAY,KAAK;IAIvC,KAAAjF,iBAAiB,GAAY,KAAK;IAIlC,KAAAkF,QAAQ,GAAM,EAAE;IA+BhB,KAAAC,+BAA+B,GAAY,KAAK;IAOhD,KAAA5D,YAAY,GAAQ9E,SAAS,CAAC2I,kBAAkB;IAGhD,KAAApC,iBAAiB,GAAM,EAAE;IACzB,KAAA1E,YAAY,GAAW,EAAE;IACzB,KAAAiF,UAAU,GAAG9G,SAAS,CAAC4I,iBAAiB;IACxC,KAAAC,cAAc,GAAG7I,SAAS,CAACoF,WAAW;IACtC,KAAA0D,WAAW,GAAG9I,SAAS,CAAC+I,SAAS;IAi0Bd,KAAA/I,SAAS,GAAGA,SAAS;EA3zBxC;EACAgJ,QAAQA,CAAA;IACJ,IAAIC,EAAE,GAAG,IAAI;IACb,IAAI,CAACC,8BAA8B,GAAG;MAClCC,gBAAgB,EAAE,IAAI;MACtBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE,KAAK;MAC1BC,MAAM,EAAE,IAAI;MACZC,SAAS,EAAE;KACd;IAED,IAAI,CAACC,wBAAwB,GAAG,CAC5B;MACI5D,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7DmJ,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;MACR;MACA;MACA;MACA;MACA;MACA;MACA;KACH,EACD;MACIjE,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7DmJ,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIjE,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1DmJ,GAAG,EAAE,OAAO;MACZC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIjE,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7DmJ,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IACD,IAAI,CAACC,0BAA0B,GAAG;MAC9BC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACC,QAAQ,CAACC,IAAI;IACjD,IAAI,CAACC,KAAK,GAAG,CAAC;MAAEC,KAAK,EAAE,IAAI,CAACjK,WAAW,CAACC,SAAS,CAAC,4BAA4B;IAAC,CAAE,EAAE;MAAEgK,KAAK,EAAE,IAAI,CAACjK,WAAW,CAACC,SAAS,CAAC,sBAAsB;IAAC,CAAE,CAAC;IACjJ,IAAI,CAACiK,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;IAEnD,IAAI,CAACC,UAAU,GAAG,CACd;MAAC9E,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAAEqK,KAAK,EAAE5K,SAAS,CAAC2I,kBAAkB,CAAC5D;IAAU,CAAC,EAC9G;MAACc,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAAEqK,KAAK,EAAE5K,SAAS,CAAC2I,kBAAkB,CAACpD;IAAO,CAAC,EAC5G;MAACM,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAAEqK,KAAK,EAAE5K,SAAS,CAAC2I,kBAAkB,CAAClD;IAAS,CAAC,EAChH;MAACI,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;MAAEqK,KAAK,EAAE5K,SAAS,CAAC2I,kBAAkB,CAAC3D;IAAW,CAAC,CACvH,EAED,IAAI,CAAC6F,SAAS,GAAG,CACb;MAAChF,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MAAEqK,KAAK,EAAE5K,SAAS,CAAC8K,iBAAiB,CAACC;IAAG,CAAC,EAClG;MAAClF,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAAEqK,KAAK,EAAE5K,SAAS,CAAC8K,iBAAiB,CAACE;IAAK,CAAC,CACzG;IAED,IAAI,CAACC,YAAY,GAAG,CAChB;MAACpF,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;MAAEqK,KAAK,EAAE5K,SAAS,CAACkL,iBAAiB,CAACC;IAAO,CAAC,EACjH;MAACtF,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;MAAEqK,KAAK,EAAE5K,SAAS,CAACkL,iBAAiB,CAACE;IAAQ,CAAC,CACtH;IAED,IAAI,CAACC,gBAAgB,GAAG,CACpB;MAACxF,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;MAAEqK,KAAK,EAAE5K,SAAS,CAACsL,aAAa,CAACC;IAAQ,CAAC,EAC/G;MAAC1F,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC;MAAEqK,KAAK,EAAE5K,SAAS,CAACsL,aAAa,CAACE;IAAU;IAClH;IACA;IAAA,CACH;;IAED,IAAI,CAACC,eAAe,GAAG,CACnB;MAAC5F,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC;MAAEqK,KAAK,EAAE5K,SAAS,CAAC4I,iBAAiB,CAAC8C;IAAW,CAAC,EACvH;MAAC7F,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;MAAEqK,KAAK,EAAE5K,SAAS,CAAC4I,iBAAiB,CAAC7B;IAAQ,CAAC,EAClH;MAAClB,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;MAAEqK,KAAK,EAAE5K,SAAS,CAAC4I,iBAAiB,CAAC5B;IAAQ,CAAC,CACrH;IAED,IAAI,CAAClD,cAAc,GAAG;MAClByE,EAAE,EAAG,IAAI;MACT3C,IAAI,EAAG,IAAI;MACXC,IAAI,EAAG,IAAI;MACXhB,MAAM,EAAG,IAAI;MACboB,YAAY,EAAG,IAAI;MACnBE,YAAY,EAAG,IAAI;MACnBG,eAAe,EAAG,IAAI;MACtBvC,gBAAgB,EAAG,IAAI;MACvB8C,WAAW,EAAG,IAAI;MAClBH,aAAa,EAAG,IAAI;MACpBC,aAAa,EAAG,IAAI;MACpBgF,MAAM,EAAG,IAAI;MACbC,IAAI,EAAG,IAAI;MACX3E,cAAc,EAAE,IAAI;MACpBG,eAAe,EAAE,IAAI;MACrBD,cAAc,EAAE,IAAI;MACpB0E,QAAQ,EAAE,IAAI;MACdxE,cAAc,EAAE,IAAI;MACpBO,aAAa,EAAE,IAAI;MACnBL,YAAY,EAAE,IAAI;MAClBC,aAAa,EAAE,IAAI;MACnBC,UAAU,EAAE,IAAI;MAChBE,aAAa,EAAE,IAAI;MACnBL,SAAS,EAAE,IAAI;MACflG,YAAY,EAAE,IAAI;MAClBgF,WAAW,EAAE,IAAI;MACjBc,OAAO,EAAE,IAAI;MACb4E,OAAO,EAAE;KACZ;IAED,IAAI,CAACC,UAAU,GAAG;MACdxD,EAAE,EAAE,IAAI;MACR1C,IAAI,EAAE,IAAI;MACVhB,MAAM,EAAE,IAAI;MACZ6B,aAAa,EAAE,IAAI;MACnBsF,QAAQ,EAAE,IAAI;MACd7F,YAAY,EAAE,IAAI;MAClBU,WAAW,EAAE;KAChB;IAED,IAAI,CAACoF,oBAAoB,EAAE;IAC3B,IAAI,CAACC,cAAc,GAAG,IAAI,CAACjE,WAAW,CAACkE,KAAK,CAAC,IAAI,CAAChL,cAAc,CAAC;IACjE,IAAI,CAACiL,UAAU,GAAG,IAAI,CAACnE,WAAW,CAACkE,KAAK,CAAC,IAAI,CAACJ,UAAU,CAAC;IACzD,IAAI,CAAC3D,WAAW,GAAG,EAAE;IACrB,IAAI,CAACiE,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,IAAI,GAAG,UAAU;IAEtB,IAAI,CAAClE,eAAe,GAAG,EAAE;IAEzB,IAAI,CAACmE,WAAW,GAAG;MACfrD,gBAAgB,EAAE,IAAI;MACtBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE,KAAK;MAC1BC,MAAM,EAAE,CACJ;QACIkB,IAAI,EAAE,cAAc;QACpBgC,OAAO,EAAE,IAAI,CAACnM,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACzDmM,IAAI,EAAE,SAAAA,CAASnE,EAAE,EAAEoE,IAAI;UACnB1D,EAAE,CAAC2D,MAAM,CAACC,QAAQ,CAAC,CAAC,iBAAiBtE,EAAE,EAAE,CAAC,CAAC;QAC/C,CAAC;QACDuE,UAAU,EAAE,SAAAA,CAASvE,EAAE,EAAEoE,IAAI;UACzB,OAAO1D,EAAE,CAAChE,WAAW,CAAC,CAACjF,SAAS,CAACoF,WAAW,CAACC,WAAW,CAAC0H,MAAM,CAAC,CAAC;QACrE;OACH,EACD;QACItC,IAAI,EAAE,aAAa;QACnBgC,OAAO,EAAE,IAAI,CAACnM,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QAC3DmM,IAAI,EAAE,SAAAA,CAASnE,EAAE,EAAEoE,IAAI;UACnB1D,EAAE,CAAC+D,oBAAoB,CAACC,OAAO,CAC3BhE,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,uCAAuC,CAAC,EACjE0I,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC,EAC5D;YACI2M,EAAE,EAACA,CAAA,KAAI;cACHjE,EAAE,CAAClB,iBAAiB,CAACoF,UAAU,CAAC5E,EAAE,EAAEE,QAAQ,IAAG;gBAC3CQ,EAAE,CAAC+D,oBAAoB,CAACI,OAAO,CAACnE,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC,CAAC;gBACzF0I,EAAE,CAACoE,MAAM,CAACpE,EAAE,CAACoD,UAAU,EAAEpD,EAAE,CAACqD,QAAQ,EAAErD,EAAE,CAACsD,IAAI,EAAEtD,EAAE,CAAC8C,UAAU,CAAC;cACjE,CAAC,CAAC;YACN,CAAC;YACDuB,MAAM,EAAEA,CAAA,KAAI;cACR;YAAA;WAEP,CACJ;QACL,CAAC;QACDR,UAAU,EAAE,SAAAA,CAASvE,EAAE,EAAEoE,IAAI;UACzB,OAAOA,IAAI,CAAC9H,MAAM,IAAI7E,SAAS,CAAC2I,kBAAkB,CAAClD,SAAS,IAAIwD,EAAE,CAAChE,WAAW,CAAC,CAACjF,SAAS,CAACoF,WAAW,CAACC,WAAW,CAACkI,MAAM,CAAC,CAAC;QAC9H;OACH,EACD;QACI9C,IAAI,EAAE,YAAY;QAClBgC,OAAO,EAAE,IAAI,CAACnM,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;QAC/DmM,IAAI,EAAE,SAAAA,CAASnE,EAAE,EAAEoE,IAAI;UACnB1D,EAAE,CAACZ,eAAe,GAAG,EAAE;UACvBY,EAAE,CAACuE,gBAAgB,GAAG,CAAC;UACvBvE,EAAE,CAACwE,cAAc,GAAG,EAAE;UACtBxE,EAAE,CAACyE,UAAU,GAAG,iBAAiB;UACjC,MAAMtD,QAAQ,GAAGuD,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC;UAC7D7E,EAAE,CAAClB,iBAAiB,CAACgG,OAAO,CAACxF,EAAE,EAAGE,QAAQ,IAAG;YACzCQ,EAAE,CAACd,YAAY,GAAGM,QAAQ;YAC1B,IAAGQ,EAAE,CAACd,YAAY,CAACtB,WAAW,IAAI7G,SAAS,CAAC4I,iBAAiB,CAAC8C,WAAW,EAAC;cACtEzC,EAAE,CAACxH,YAAY,GAAG,CAAC,GAAGwH,EAAE,CAAC+E,kBAAkB,CAAC;aAC/C,MAAI;cACD,IAAG5D,QAAQ,CAAChJ,YAAY,EAAC;gBACrB6H,EAAE,CAACxH,YAAY,GAAG2I,QAAQ,CAAChJ,YAAY;eAC1C,MAAK,IAAGgJ,QAAQ,CAACC,IAAI,IAAI,CAAC,EAAC;gBACxBpB,EAAE,CAACxH,YAAY,GAAGwH,EAAE,CAAC+E,kBAAkB,CAACC,MAAM,CAACC,EAAE,IAAIzF,QAAQ,CAACrH,YAAY,CAAC+M,QAAQ,CAACD,EAAE,CAACtI,IAAI,CAAC,CAAC;eAChG,MAAI;gBACDqD,EAAE,CAACxH,YAAY,GAAG,EAAE;;;YAG5BwH,EAAE,CAAClB,iBAAiB,CAACqG,uBAAuB,CAACnF,EAAE,CAACd,YAAY,CAACI,EAAE,EAAGE,QAAQ,IAAG;cACzEQ,EAAE,CAACZ,eAAe,GAAG,CAACI,QAAQ,IAAI,EAAE,EAAE4F,GAAG,CAACH,EAAE,IAAG;gBAC3C,OAAO;kBACH9M,YAAY,EAAE8M,EAAE,CAAC9M,YAAY;kBAC7BkN,MAAM,EAAEJ,EAAE,CAACI,MAAM;kBACjB/F,EAAE,EAAE2F,EAAE,CAACI;iBACV;cACL,CAAC,CAAC;cACFrF,EAAE,CAACX,kBAAkB,GAAG,CAAC,GAAGW,EAAE,CAACZ,eAAe,CAAC;YACnD,CAAC,CAAC;YACFY,EAAE,CAACsF,UAAU,CAACtF,EAAE,CAACuE,gBAAgB,EAAEvE,EAAE,CAACwE,cAAc,EAAExE,EAAE,CAACyE,UAAU,EAAEzE,EAAE,CAAC9H,cAAc,CAAC;YACvF8H,EAAE,CAACT,sBAAsB,GAAG,IAAI;UACpC,CAAC,CAAC;QACN,CAAC;QACDsE,UAAU,EAAE,SAAAA,CAASvE,EAAE,EAAEoE,IAAI;UACzB,OAAOA,IAAI,CAAC9H,MAAM,IAAI7E,SAAS,CAAC2I,kBAAkB,CAAClD,SAAS,IAAIwD,EAAE,CAAChE,WAAW,CAAC,CAACjF,SAAS,CAACoF,WAAW,CAACC,WAAW,CAACmJ,KAAK,CAAC,CAAC,IAClH7B,IAAI,CAAC9F,WAAW,IAAK7G,SAAS,CAAC4I,iBAAiB,CAAC5B,QAAQ;QACpE;OACH;KAER;IACD,IAAI,CAACyH,qBAAqB,GAAG;MACzBtF,gBAAgB,EAAE,KAAK;MACvBE,YAAY,EAAE,IAAI;MAClBD,aAAa,EAAE,IAAI;MACnBE,mBAAmB,EAAE;KACxB;IACD,IAAI,CAACoF,OAAO,GAAG,CACX;MACI7I,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7DmJ,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIjE,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7DmJ,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZ6E,KAAK,EAAC;QACFC,MAAM,EAAE,SAAS;QACjBC,KAAK,EAAE,sBAAsB;QAC7BC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;OACjB;MACDC,aAAa,EAAE,IAAI;MACnBC,SAASA,CAAC5G,EAAE,EAAEoE,IAAI;QACd1D,EAAE,CAACmG,MAAM,GAAG7G,EAAE;QACdU,EAAE,CAACoG,aAAa,EAAE;QAClBpG,EAAE,CAACqG,qBAAqB,EAAE;QAC1BrG,EAAE,CAAC1F,iBAAiB,GAAG,IAAI;MAC/B;KACH,EACD;MACIsC,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;MAC3DmJ,GAAG,EAAE,QAAQ;MACbC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZyF,eAAeA,CAAC3E,KAAK;QACjB,IAAGA,KAAK,IAAI5K,SAAS,CAAC2I,kBAAkB,CAAC5D,UAAU,EAAC;UAChD,OAAOkE,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;SAC9D,MAAK,IAAGqK,KAAK,IAAI5K,SAAS,CAAC2I,kBAAkB,CAACpD,OAAO,EAAC;UACnD,OAAO0D,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;SAC/D,MAAK,IAAGqK,KAAK,IAAI5K,SAAS,CAAC2I,kBAAkB,CAAClD,SAAS,EAAC;UACrD,OAAOwD,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;SACjE,MAAK,IAAGqK,KAAK,IAAI5K,SAAS,CAAC2I,kBAAkB,CAAC3D,WAAW,EAAC;UACvD,OAAOiE,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;SACnE,MAAI;UACD,OAAO,EAAE;;MAEjB,CAAC;MACDiP,gBAAgBA,CAAC5E,KAAK;QAClB,IAAGA,KAAK,IAAI5K,SAAS,CAAC2I,kBAAkB,CAAC5D,UAAU,EAAC;UAChD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAC,cAAc,CAAC;SAC7E,MAAK,IAAG6F,KAAK,IAAI5K,SAAS,CAAC2I,kBAAkB,CAACpD,OAAO,EAAC;UACnD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAC,cAAc,CAAC;SAC5E,MAAK,IAAGqF,KAAK,IAAI5K,SAAS,CAAC2I,kBAAkB,CAAClD,SAAS,EAAC;UACrD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAC,cAAc,CAAC;SAC9E,MAAK,IAAGmF,KAAK,IAAI5K,SAAS,CAAC2I,kBAAkB,CAAC3D,WAAW,EAAC;UACvD,OAAO,CAAC,KAAK,EAAE,YAAY,EAAG,eAAe,EAAE,cAAc,EAAC,cAAc,CAAC;SAChF,MAAI;UACD,OAAO,EAAE;;MAEjB;KACH,EACD;MACIa,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;MACjEmJ,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZyF,eAAeA,CAAC3E,KAAK;QACjB,IAAGA,KAAK,IAAI5K,SAAS,CAACsL,aAAa,CAACE,UAAU,EAAC;UAC3C,OAAOvC,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC;SACxE,MAAK,IAAGqK,KAAK,IAAI5K,SAAS,CAACsL,aAAa,CAACC,QAAQ,EAAC;UAC/C,OAAOtC,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;SACtE,MAAK,IAAGqK,KAAK,IAAI5K,SAAS,CAACsL,aAAa,CAACmE,MAAM,EAAC;UAC7C,OAAOxG,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC;SACpE,MAAI;UACD,OAAO,EAAE;;MAEjB;KACH,EACD;MACIsF,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7DmJ,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZyF,eAAeA,CAAC3E,KAAK;QACjB,IAAGA,KAAK,IAAI5K,SAAS,CAACkL,iBAAiB,CAACE,QAAQ,EAAC;UAC7C,OAAOnC,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;SACtE,MAAK,IAAGqK,KAAK,IAAI5K,SAAS,CAACkL,iBAAiB,CAACC,OAAO,EAAC;UAClD,OAAOlC,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;SACrE,MAAI;UACD,OAAO,EAAE;;MAEjB;KACH,EACD;MACIsF,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC;MAChEmJ,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZyF,eAAeA,CAAC3E,KAAK;QACjB,IAAGA,KAAK,IAAI5K,SAAS,CAAC4I,iBAAiB,CAAC8C,WAAW,EAAC;UAChD,OAAOzC,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC;SACvE,MAAK,IAAGqK,KAAK,IAAI5K,SAAS,CAAC4I,iBAAiB,CAAC5B,QAAQ,EAAC;UACnD,OAAOiC,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;SACrE,MAAK,IAAGqK,KAAK,IAAI5K,SAAS,CAAC4I,iBAAiB,CAAC7B,QAAQ,EAAC;UACnD,OAAOkC,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;SACrE,MAAI;UACD,OAAO,EAAE;;MAEjB;KACH,EACD;MACIsF,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1DmJ,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZyF,eAAeA,CAAC3E,KAAK;QACjB,IAAGA,KAAK,IAAI5K,SAAS,CAAC8K,iBAAiB,CAACC,GAAG,EAAC;UACxC,OAAO9B,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;SAC1D,MAAK,IAAGqK,KAAK,IAAI5K,SAAS,CAAC8K,iBAAiB,CAACE,KAAK,EAAC;UAChD,OAAO/B,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;SAC5D,MAAI;UACD,OAAO,EAAE;;MAEjB;KACH,EACD;MACIsF,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;MACpEmJ,GAAG,EAAE,iBAAiB;MACtBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,OAAO;MACdC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZyF,eAAeA,CAAC3E,KAAK;QACjB,OAAO3B,EAAE,CAACyG,WAAW,CAACC,qBAAqB,CAAC/E,KAAK,CAAC;MACtD;KACH,EACD;MACI/E,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;MACnEmJ,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,OAAO;MACdC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZyF,eAAeA,CAAC3E,KAAK;QACjB,OAAO3B,EAAE,CAACyG,WAAW,CAACC,qBAAqB,CAAC/E,KAAK,CAAC;MACtD;KACH,CACJ;IAED,IAAI,CAACgF,iBAAiB,GAAG;MACrB5F,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD,IAAI,CAAC4F,eAAe,EAAE;IACtB,IAAI,CAACC,eAAe,GAAG,CACnB;MACIjK,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7DmJ,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZ6E,KAAK,EAAC;QACFC,MAAM,EAAE,SAAS;QACxBC,KAAK,EAAE;OACH;MACDkB,cAAcA,CAACpD,IAAI;QACf,OAAO,CAAC,iBAAiBA,IAAI,CAACpE,EAAE,EAAE,CAAC;MACvC;KACH,EACD;MACI1C,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7DmJ,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIjE,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1DmJ,GAAG,EAAE,OAAO;MACZC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIjE,IAAI,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7DmJ,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZyF,eAAeA,CAAC3E,KAAK;QACjB,KAAK,IAAIoF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/G,EAAE,CAAC+E,kBAAkB,CAACiC,MAAM,EAAED,CAAC,EAAE,EAAE;UACnD,IAAIpF,KAAK,IAAI3B,EAAE,CAAC+E,kBAAkB,CAACgC,CAAC,CAAC,CAACpK,IAAI,EAAC;YACvC,OAAO,GAAGqD,EAAE,CAAC+E,kBAAkB,CAACgC,CAAC,CAAC,CAACnK,IAAI,KAAKoD,EAAE,CAAC+E,kBAAkB,CAACgC,CAAC,CAAC,CAACpK,IAAI,GAAG;;;QAGpF,OAAO,IAAI;MACf;KACH,CACJ;IACD,IAAI,CAACyH,MAAM,CAAC,IAAI,CAAChB,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACR,UAAU,CAAC;EAC3E;EAEAmE,qBAAqBA,CAAA;IACjB,IAAI,IAAI,CAAC1H,sBAAsB,IAAI,KAAK,EAAC;MACrC,IAAI,CAAC0D,cAAc,CAACiE,KAAK,EAAE;;EAEnC;EAEAC,cAAcA,CAAA;IACV,IAAI,CAAC/D,UAAU,GAAG,CAAC;IACnB,IAAI,CAACgB,MAAM,CAAC,IAAI,CAAChB,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACR,UAAU,CAAC;EAC3E;EAEAsE,kBAAkBA,CAAA;IACd,IAAI,CAAC7C,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACe,UAAU,CAAC,IAAI,CAACf,gBAAgB,EAAE,IAAI,CAACC,cAAc,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACvM,cAAc,CAAC;EACrG;EAEA8K,oBAAoBA,CAAA;IAChB,IAAI,CAAC9K,cAAc,GAAG;MAClBmP,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,IAAI;MACXpP,YAAY,EAAE;KACjB;EACL;EACAqP,sBAAsBA,CAAA;IAClB,IAAG,IAAI,CAACpI,eAAe,CAAC4H,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC3H,kBAAkB,CAAC2H,MAAM,IAAI,CAAC,EAAC;MACvE,OAAO,IAAI;;IAEf,IAAIS,IAAI,GAAG,KAAK;IAChB,KAAI,IAAIV,CAAC,GAAG,CAAC,EAACA,CAAC,GAAC,IAAI,CAAC3H,eAAe,CAAC4H,MAAM,EAACD,CAAC,EAAE,EAAC;MAC5C,IAAG,CAAC,IAAI,CAAC3H,eAAe,CAAC2H,CAAC,CAAC,CAAC5O,YAAY,IAAI,EAAE,KAAK,EAAE,EAAC;QAClDsP,IAAI,GAAG,IAAI;QACX;;;IAGR,OAAOA,IAAI;EACf;EAEAC,UAAUA,CAAA;IACN,IAAI1H,EAAE,GAAG,IAAI;IACb,IAAI2H,MAAM,GAAG,IAAI,CAACtI,kBAAkB,CAAC+F,GAAG,CAACH,EAAE,IAAIA,EAAE,CAAC3F,EAAE,CAAC;IACrD,IAAIsI,OAAO,GAAG,IAAI,CAACxI,eAAe,CAAC4F,MAAM,CAACC,EAAE,IAAI,CAAC0C,MAAM,CAACzC,QAAQ,CAACD,EAAE,CAAC3F,EAAE,CAAC,CAAC,CAAC8F,GAAG,CAACH,EAAE,IAAG;MAC9E,OAAO;QACHI,MAAM,EAAEJ,EAAE,CAAC3F,EAAE;QACbnH,YAAY,EAAE8M,EAAE,CAAC9M,YAAY;QAC7BiJ,IAAI,EAAE;OACT;IACL,CAAC,CAAC;IACF,IAAIyG,eAAe,GAAG,IAAI,CAACzI,eAAe,CAACgG,GAAG,CAACH,EAAE,IAAIA,EAAE,CAAC3F,EAAE,CAAC;IAC3D,IAAIwI,UAAU,GAAG,IAAI,CAACzI,kBAAkB,CAAC2F,MAAM,CAACC,EAAE,IAAI,CAAC4C,eAAe,CAAC3C,QAAQ,CAACD,EAAE,CAAC3F,EAAE,CAAC,CAAC,CAAC8F,GAAG,CAACH,EAAE,IAAG;MAC7F,OAAO;QACHI,MAAM,EAAEJ,EAAE,CAAC3F,EAAE;QACbnH,YAAY,EAAE8M,EAAE,CAAC9M,YAAY;QAC7BiJ,IAAI,EAAE,CAAC;OACV;IACL,CAAC,CAAC;IACF,IAAI2G,IAAI,GAAG;MACP5B,MAAM,EAAE,IAAI,CAACjH,YAAY,CAACI,EAAE;MAC5ByI,IAAI,EAAE,CAAC,GAAGH,OAAO,EAAE,GAAGE,UAAU;KACnC;IACD,IAAGC,IAAI,CAACA,IAAI,CAACf,MAAM,IAAI,CAAC,EAAC;MACrB;;IAEJhH,EAAE,CAAClB,iBAAiB,CAAC4I,UAAU,CAACK,IAAI,CAAC5B,MAAM,EAAE4B,IAAI,CAACA,IAAI,EAAGvI,QAAQ,IAAG;MAChEQ,EAAE,CAACoE,MAAM,CAACpE,EAAE,CAACoD,UAAU,EAAEpD,EAAE,CAACqD,QAAQ,EAAErD,EAAE,CAACsD,IAAI,EAAEtD,EAAE,CAAC8C,UAAU,CAAC;IACjE,CAAC,CAAC;IACF9C,EAAE,CAAC+D,oBAAoB,CAACI,OAAO,CAACnE,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;IACvF,IAAI,CAACiI,sBAAsB,GAAG,KAAK;EACvC;EAEA+F,UAAUA,CAAC0C,IAAI,EAAEC,KAAK,EAAE3E,IAAI,EAAE4E,MAAM;IAChC,IAAIlI,EAAE,GAAG,IAAI;IACb,IAAI,CAACuE,gBAAgB,GAAGyD,IAAI;IAC5B,IAAI,CAACxD,cAAc,GAAGyD,KAAK;IAC3B,IAAI,CAACxD,UAAU,GAAGnB,IAAI;IACtB,IAAI6E,UAAU,GAAG;MACbH,IAAI;MACJtH,IAAI,EAAEuH,KAAK;MACX3E,IAAI;MACJlC,IAAI,EAAErK,SAAS,CAAC+I,SAAS,CAAC/B,QAAQ;MAClCqK,YAAY,EAAE,IAAI,CAAClJ,YAAY,CAACI;KACnC;IAED+I,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpQ,cAAc,CAAC,CAACqQ,OAAO,CAAC9H,GAAG,IAAG;MAC3C,IAAG,IAAI,CAACvI,cAAc,CAACuI,GAAG,CAAC,IAAI,IAAI,EAAC;QAChC0H,UAAU,CAAC1H,GAAG,CAAC,GAAG,IAAI,CAACvI,cAAc,CAACuI,GAAG,CAAC;;IAElD,CAAC,CAAC;IACF,IAAG,CAAC0H,UAAU,CAAC,cAAc,CAAC,IAAI,EAAE,EAAEK,IAAI,EAAE,CAACxB,MAAM,IAAI,CAAC,EAAC;MACrD,IAAG,IAAI,CAAC/F,QAAQ,IAAIlK,SAAS,CAAC+I,SAAS,CAAC2I,KAAK,EAAC;QAC1C,IAAG,IAAI,CAACvJ,YAAY,CAACtB,WAAW,IAAI7G,SAAS,CAAC4I,iBAAiB,CAAC8C,WAAW,EAAC;UACxE0F,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;SAClC,MAAI;UACD,IAAG,IAAI,CAACjJ,YAAY,CAAC/G,YAAY,EAAE;YAC/BgQ,UAAU,CAAC,cAAc,CAAC,GAAG,IAAI,CAACjJ,YAAY,CAAC/G,YAAY,CAACuQ,cAAc,EAAE;WAC/E,MAAI;YACDP,UAAU,CAAC,cAAc,CAAC,GAAG,EAAE;;;OAG1C,MAAI;QACDA,UAAU,CAAC,cAAc,CAAC,GAAG,IAAI,CAACjH,cAAc,CAACC,QAAQ,CAAChJ,YAAY;;;IAK9E;IACA,IAAI,CAAC2G,iBAAiB,CAACwG,UAAU,CAAC6C,UAAU,EAAG3I,QAAQ,IAAG;MACtDQ,EAAE,CAAC2G,iBAAiB,GAAG;QACnB5F,OAAO,EAAEvB,QAAQ,CAACuB,OAAO;QACzBC,KAAK,EAAExB,QAAQ,CAACmJ;OACnB;IACL,CAAC,CAAC;EACN;EAEAvE,MAAMA,CAAC4D,IAAI,EAAEC,KAAK,EAAE3E,IAAI,EAAE4E,MAAM;IAC5B,IAAIlI,EAAE,GAAG,IAAI;IACb,IAAI,CAACoD,UAAU,GAAG4E,IAAI;IACtB,IAAI,CAAC3E,QAAQ,GAAG4E,KAAK;IACrB,IAAI,CAAC3E,IAAI,GAAGA,IAAI;IAChB,IAAI6E,UAAU,GAAG;MACbH,IAAI;MACJtH,IAAI,EAAEuH,KAAK;MACX3E;KACH;IACD+E,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxF,UAAU,CAAC,CAACyF,OAAO,CAAC9H,GAAG,IAAG;MACvC,IAAG,IAAI,CAACqC,UAAU,CAACrC,GAAG,CAAC,IAAI,IAAI,EAAC;QAC5B0H,UAAU,CAAC1H,GAAG,CAAC,GAAG,IAAI,CAACqC,UAAU,CAACrC,GAAG,CAAC;;IAE9C,CAAC,CAAC;IACF,IAAI,CAACmI,OAAO,GAAG;MACX7H,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACDhB,EAAE,CAAC+D,oBAAoB,CAAC8E,MAAM,EAAE;IAChC,IAAI,CAAC/J,iBAAiB,CAACsF,MAAM,CAAC+D,UAAU,EAAG3I,QAAQ,IAAG;MAClDQ,EAAE,CAAC4I,OAAO,GAAG;QACT7H,OAAO,EAAEvB,QAAQ,CAACuB,OAAO;QACzBC,KAAK,EAAExB,QAAQ,CAACmJ;OACnB;IACL,CAAC,EAAE,IAAI,EAAE,MAAI;MACT3I,EAAE,CAAC+D,oBAAoB,CAAC+E,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAlC,eAAeA,CAAA;IACX,IAAI5G,EAAE,GAAG,IAAI;IACb,IAAI,CAAClB,iBAAiB,CAAC8H,eAAe,CAAEpH,QAAQ,IAAG;MAC/C,IAAI,CAAChH,YAAY,GAAGgH,QAAQ,CAAC4F,GAAG,CAACH,EAAE,IAAG;QAClC,IAAGA,EAAE,CAACtI,IAAI,IAAIqD,EAAE,CAACkB,cAAc,CAACC,QAAQ,CAAChJ,YAAY,EAAC;UAClD6H,EAAE,CAACpH,YAAY,GAAG,GAAGqM,EAAE,CAACrI,IAAI,MAAMqI,EAAE,CAACtI,IAAI,EAAE;;QAE/C,OAAO;UACH,GAAGsI,EAAE;UACLY,OAAO,EAAE,GAAGZ,EAAE,CAACrI,IAAI,MAAMqI,EAAE,CAACtI,IAAI;SACnC;MACL,CAAC,CAAC;MACF,IAAI,CAACoI,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAACvM,YAAY,CAAC;IACpD,CAAC,CAAC;EACN;EAEA4N,aAAaA,CAAA;IACT,IAAIpG,EAAE,GAAG,IAAI;IACb,IAAI,CAAC+D,oBAAoB,CAAC8E,MAAM,EAAE;IAClC7I,EAAE,CAAClB,iBAAiB,CAACgG,OAAO,CAACiE,MAAM,CAAC/I,EAAE,CAACmG,MAAM,CAAC,EAAG3G,QAAQ,IAAG;MACxDQ,EAAE,CAACR,QAAQ,GAAGA,QAAQ;MAEtBQ,EAAE,CAACnF,cAAc,CAACyE,EAAE,GAAGE,QAAQ,CAACF,EAAE;MAClCU,EAAE,CAACnF,cAAc,CAAC8B,IAAI,GAAG6C,QAAQ,CAAC7C,IAAI;MACtCqD,EAAE,CAACnF,cAAc,CAAC+B,IAAI,GAAG4C,QAAQ,CAAC5C,IAAI;MACtCoD,EAAE,CAACnF,cAAc,CAACe,MAAM,GAAG4D,QAAQ,CAAC5D,MAAM;MAC1CoE,EAAE,CAACnF,cAAc,CAACmC,YAAY,GAAGwC,QAAQ,CAACxC,YAAY;MACtDgD,EAAE,CAACnF,cAAc,CAACqC,YAAY,GAAGsC,QAAQ,CAACtC,YAAY;MACtD8C,EAAE,CAACnF,cAAc,CAACwC,eAAe,GAAGmC,QAAQ,CAACnC,eAAe;MAC5D2C,EAAE,CAACnF,cAAc,CAACC,gBAAgB,GAAG0E,QAAQ,CAACuD,QAAQ;MACtD/C,EAAE,CAACnF,cAAc,CAAC+C,WAAW,GAAG4B,QAAQ,CAAC5B,WAAW;MACpDoC,EAAE,CAACnF,cAAc,CAAC4C,aAAa,GAAG+B,QAAQ,CAAC/B,aAAa;MACxDuC,EAAE,CAACnF,cAAc,CAAC6C,aAAa,GAAG8B,QAAQ,CAAC9B,aAAa;MACxDsC,EAAE,CAACnF,cAAc,CAAC6H,MAAM,GAAGlD,QAAQ,CAACkD,MAAM;MAC1C1C,EAAE,CAACnF,cAAc,CAAC8H,IAAI,GAAGnD,QAAQ,CAACmD,IAAI;MACtC3C,EAAE,CAACnF,cAAc,CAACmD,cAAc,GAAGwB,QAAQ,CAACxB,cAAc;MAC1DgC,EAAE,CAACnF,cAAc,CAACsD,eAAe,GAAGqB,QAAQ,CAACrB,eAAe;MAC5D6B,EAAE,CAACnF,cAAc,CAACqD,cAAc,GAAGsB,QAAQ,CAACtB,cAAc;MAC1D8B,EAAE,CAACnF,cAAc,CAAC+H,QAAQ,GAAGpD,QAAQ,CAACoD,QAAQ;MAC9C5C,EAAE,CAACnF,cAAc,CAACuD,cAAc,GAAGoB,QAAQ,CAACpB,cAAc;MAC1D4B,EAAE,CAACnF,cAAc,CAAC8D,aAAa,GAAGa,QAAQ,CAACb,aAAa;MACxDqB,EAAE,CAACnF,cAAc,CAACyD,YAAY,GAAGkB,QAAQ,CAAClB,YAAY;MACtD0B,EAAE,CAACnF,cAAc,CAAC0D,aAAa,GAAGiB,QAAQ,CAACjB,aAAa;MACxDyB,EAAE,CAACnF,cAAc,CAAC2D,UAAU,GAAGgB,QAAQ,CAAChB,UAAU;MAClDwB,EAAE,CAACnF,cAAc,CAAC6D,aAAa,GAAGc,QAAQ,CAACd,aAAa;MACxDsB,EAAE,CAACnF,cAAc,CAACwD,SAAS,GAAGmB,QAAQ,CAACnB,SAAS;MAChD2B,EAAE,CAACnF,cAAc,CAAC1C,YAAY,GAAGqH,QAAQ,CAACrH,YAAY;MACtD6H,EAAE,CAACnF,cAAc,CAACsC,WAAW,GAAGqC,QAAQ,CAACrC,WAAW;MACpD6C,EAAE,CAACnF,cAAc,CAACoD,OAAO,GAAGuB,QAAQ,CAACvB,OAAO;MAC5C+B,EAAE,CAACnF,cAAc,CAACgI,OAAO,GAAGrD,QAAQ,CAACqD,OAAO;MAE5C7C,EAAE,CAACgJ,SAAS,CAAChJ,EAAE,CAACnF,cAAc,CAAC6H,MAAM,CAAC;MACtC1C,EAAE,CAACiJ,WAAW,CAACjJ,EAAE,CAACnF,cAAc,CAAC+H,QAAQ,CAAC;MAC1C5C,EAAE,CAACnG,UAAU,GAAG,EAAE;MAClB,IAAI2F,QAAQ,CAACrH,YAAY,IAAI,IAAI,EAAC;QAE9B6H,EAAE,CAACjB,cAAc,CAACmK,qBAAqB,CAAC1J,QAAQ,CAACrH,YAAY,EAAE4P,IAAI,IAAG;UAClE/H,EAAE,CAACmJ,SAAS,GAAGpB,IAAI,CAAC3C,GAAG,CAACH,EAAE,IAAG;YACzB,OAAO;cACHtI,IAAI,EAAEsI,EAAE,CAACtI,IAAI;cACbC,IAAI,EAAE,GAAGqI,EAAE,CAACrI,IAAI;aACnB;UACL,CAAC,CAAC;UACFoD,EAAE,CAACmJ,SAAS,CAACZ,OAAO,CAACtD,EAAE,IAAG;YACtB,IAAGjF,EAAE,CAACnF,cAAc,CAAC1C,YAAY,CAAC+M,QAAQ,CAACD,EAAE,CAACtI,IAAI,CAAC,EAAC;cAChDqD,EAAE,CAACnG,UAAU,IAAI,GAAGoL,EAAE,CAACrI,IAAI,IAAI;;UAGvC,CAAC,CAAC;UACF,IAAGoD,EAAE,CAACnG,UAAU,CAACmN,MAAM,GAAG,CAAC,EAAC;YACxBhH,EAAE,CAACnG,UAAU,GAAGmG,EAAE,CAACnG,UAAU,CAACuP,SAAS,CAAC,CAAC,EAAEpJ,EAAE,CAACnG,UAAU,CAACmN,MAAM,GAAG,CAAC,CAAC;;QAE5E,CAAC,CAAC;;MAENhH,EAAE,CAACjB,cAAc,CAACsK,2BAA2B,CAAC;QAACjB,YAAY,EAAE5I,QAAQ,CAACF;MAAE,CAAC,EAAGgK,IAAI,IAAI;QAChF,IAAI,CAACxI,0BAA0B,GAAG;UAC9BC,OAAO,EAAEuI,IAAI;UACbtI,KAAK,EAAEsI,IAAI,GAAGA,IAAI,CAACtC,MAAM,GAAG;SAC/B;MACL,CAAC,CAAC;IAEN,CAAC,EAAE,IAAI,EAAE,MAAI;MACThH,EAAE,CAAC+D,oBAAoB,CAAC+E,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAE,SAASA,CAACrH,KAAK;IACX,IAAGA,KAAK,IAAI5K,SAAS,CAACwS,MAAM,CAACC,GAAG,EAAC;MAC7B,OAAO,IAAI,CAACrO,aAAa,GAAG,IAAI;KACnC,MAAK,IAAGwG,KAAK,IAAI5K,SAAS,CAACwS,MAAM,CAACE,EAAE,EAAC;MAClC,OAAO,IAAI,CAACtO,aAAa,GAAG,KAAK;;IAErC,OAAO,EAAE;EACb;EAEA8N,WAAWA,CAACtH,KAAK;IACb,IAAGA,KAAK,IAAI5K,SAAS,CAAC2S,QAAQ,CAACF,GAAG,EAAC;MAC/B,OAAO,IAAI,CAAChO,eAAe,GAAG,IAAI;KACrC,MAAK,IAAGmG,KAAK,IAAI5K,SAAS,CAAC2S,QAAQ,CAACD,EAAE,EAAC;MACpC,OAAO,IAAI,CAACjO,eAAe,GAAG,KAAK;;IAEvC,OAAO,EAAE;EACb;EAEAsB,cAAcA,CAAC6E,KAAK;IAChB,IAAGA,KAAK,IAAI5K,SAAS,CAAC2I,kBAAkB,CAAClD,SAAS,EAAC;MAC/C,OAAO,CAAC,KAAK,EAAE,eAAe,EAAE,aAAa,EAAC,cAAc,EAAC,cAAc,CAAC;KAC/E,MAAK,IAAGmF,KAAK,IAAI5K,SAAS,CAAC2I,kBAAkB,CAAC5D,UAAU,EAAC;MACtD,OAAO,CAAC,KAAK,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,cAAc,EAAC,cAAc,CAAC;KACtF,MAAK,IAAG6F,KAAK,IAAI5K,SAAS,CAAC2I,kBAAkB,CAACpD,OAAO,EAAC;MACnD,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,YAAY,EAAC,cAAc,EAAC,cAAc,CAAC;KAC7E,MAAK,IAAGqF,KAAK,IAAI5K,SAAS,CAAC2I,kBAAkB,CAAC3D,WAAW,EAAC;MACvD,OAAO,CAAC,KAAK,EAAE,iBAAiB,EAAE,eAAe,EAAE,cAAc,EAAC,cAAc,CAAC;;IAErF,OAAO,EAAE;EACb;EAEAkB,mBAAmBA,CAAC0E,KAAK;IACrB,IAAGA,KAAK,IAAI5K,SAAS,CAACsL,aAAa,CAACC,QAAQ,EAAC;MACzC,OAAO,IAAI,CAACjL,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;KACxE,MAAK,IAAGqK,KAAK,IAAI5K,SAAS,CAACsL,aAAa,CAACE,UAAU,EAAC;MACjD,OAAO,IAAI,CAAClL,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC;KAC1E,MAAK,IAAGqK,KAAK,IAAI5K,SAAS,CAACsL,aAAa,CAACmE,MAAM,EAAC;MAC7C,OAAO,IAAI,CAACnP,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC;;IAEvE,OAAO,EAAE;EACb;EAEA+O,qBAAqBA,CAAA;IACjB,IAAI,CAAC/I,iBAAiB,GAAG,CAAC;MACtB8D,IAAI,EAAE,IAAI,CAAC/J,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;MACpEiG,EAAE,EAAExG,SAAS,CAACkL,iBAAiB,CAACE;KACnC,EACG;MACIf,IAAI,EAAE,IAAI,CAAC/J,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;MACnEiG,EAAE,EAAExG,SAAS,CAACkL,iBAAiB,CAACC;KACnC,CAAC;EACV;EAEA1E,gBAAgBA,CAACmE,KAAK;IAClB,IAAGA,KAAK,IAAI5K,SAAS,CAAC4S,gBAAgB,CAAC7H,GAAG,EAAC;MACvC,OAAO,IAAI,CAACzK,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;KAC5D,MAAK,IAAGqK,KAAK,IAAI5K,SAAS,CAAC4S,gBAAgB,CAAC5H,KAAK,EAAC;MAC/C,OAAO,IAAI,CAAC1K,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;;IAE/D,OAAO,EAAE;EACb;EAEAqG,cAAcA,CAACgE,KAAK;IAChB,IAAGA,KAAK,IAAI5K,SAAS,CAAC4I,iBAAiB,CAAC8C,WAAW,EAAC;MAChD,OAAO,IAAI,CAACpL,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC;KACzE,MAAK,IAAGqK,KAAK,IAAI5K,SAAS,CAAC4I,iBAAiB,CAAC5B,QAAQ,EAAC;MACnD,OAAO,IAAI,CAAC1G,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;KACvE,MAAK,IAAGqK,KAAK,IAAI5K,SAAS,CAAC4I,iBAAiB,CAAC7B,QAAQ,EAAC;MACnD,OAAO,IAAI,CAACzG,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;;IAExE,OAAO,EAAE;EACb;EAEAyF,aAAaA,CAAC4E,KAAK;IACf,IAAGA,KAAK,IAAI5K,SAAS,CAAC2I,kBAAkB,CAAClD,SAAS,EAAC;MAC/C,OAAO,IAAI,CAACnF,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;KACnE,MAAK,IAAGqK,KAAK,IAAI5K,SAAS,CAAC2I,kBAAkB,CAAC5D,UAAU,EAAC;MACtD,OAAO,IAAI,CAACzE,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;KAChE,MAAK,IAAGqK,KAAK,IAAI5K,SAAS,CAAC2I,kBAAkB,CAACpD,OAAO,EAAC;MACnD,OAAO,IAAI,CAACjF,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;KACjE,MAAK,IAAGqK,KAAK,IAAI5K,SAAS,CAAC2I,kBAAkB,CAAC3D,WAAW,EAAC;MACvD,OAAO,IAAI,CAAC1E,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;;IAEtE,OAAO,EAAE;EACb;EAEA0B,MAAMA,CAAA;IACF,IAAIgH,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC+D,oBAAoB,CAACC,OAAO,CAC3BhE,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC,EAC5D0I,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,uCAAuC,CAAC,EACjE;MACI2M,EAAE,EAACA,CAAA,KAAI;QACHjE,EAAE,CAAClB,iBAAiB,CAAC8K,UAAU,CAAC5J,EAAE,CAACmG,MAAM,EAAE3G,QAAQ,IAAG,CACtD,CAAC,CAAC;QACFQ,EAAE,CAAC+D,oBAAoB,CAACI,OAAO,CAACnE,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC,CAAC;QACzFuS,MAAM,CAACC,QAAQ,CAACpH,MAAM,EAAE;MAC5B,CAAC;MACD2B,MAAM,EAAEA,CAAA,KAAI;QACR;MAAA;KAEP,CACJ;EACL;EACAhL,OAAOA,CAAA;IACH,IAAI2G,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC+D,oBAAoB,CAACC,OAAO,CAC3BhE,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC,EAC7D0I,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,wCAAwC,CAAC,EAClE;MACI2M,EAAE,EAACA,CAAA,KAAI;QACHjE,EAAE,CAAClB,iBAAiB,CAAC8K,UAAU,CAAC5J,EAAE,CAACmG,MAAM,EAAE3G,QAAQ,IAAG;UAClD;QAAA,CACH,CAAC;QACFQ,EAAE,CAAC+D,oBAAoB,CAACI,OAAO,CAACnE,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC,CAAC;QAC1FuS,MAAM,CAACC,QAAQ,CAACpH,MAAM,EAAE;MAC5B,CAAC;MACD2B,MAAM,EAAEA,CAAA,KAAI;QACR;MAAA;KAEP,CACJ;EACL;EACA3K,OAAOA,CAAA;IACH,IAAIsG,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC+D,oBAAoB,CAACC,OAAO,CAC3BhE,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC,EAC7D0I,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,wCAAwC,CAAC,EAClE;MACI2M,EAAE,EAACA,CAAA,KAAI;QACHjE,EAAE,CAAClB,iBAAiB,CAACiL,WAAW,CAAC/J,EAAE,CAACmG,MAAM,EAAE3G,QAAQ,IAAG;UACnD;QAAA,CACH,CAAC;QACFQ,EAAE,CAAC+D,oBAAoB,CAACI,OAAO,CAACnE,EAAE,CAAC3I,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,CAAC;QAC3FuS,MAAM,CAACC,QAAQ,CAACpH,MAAM,EAAE;MAC5B,CAAC;MACD2B,MAAM,EAAEA,CAAA,KAAI;QACR;MAAA;KAEP,CACJ;EACL;EAEA2F,iBAAiBA,CAAA,GAEjB;EAIA/P,4BAA4BA,CAAA;IACxB,IAAI,CAACwF,+BAA+B,GAAG,IAAI;EAC/C;;;uBA36BSb,0BAA0B,EAAA3H,EAAA,CAAAgT,iBAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAlT,EAAA,CAAAgT,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAApT,EAAA,CAAAgT,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAtT,EAAA,CAAAgT,iBAAA,CAAAhT,EAAA,CAAAuT,QAAA;IAAA;EAAA;;;YAA1B5L,0BAA0B;MAAA6L,SAAA;MAAAC,QAAA,GAAAzT,EAAA,CAAA0T,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbvChU,EAAA,CAAAQ,cAAA,aAAqG;UAEzDR,EAAA,CAAAoB,MAAA,GAAiD;UAAApB,EAAA,CAAAmB,YAAA,EAAM;UAC3FnB,EAAA,CAAAC,SAAA,sBAAoF;UACxFD,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAQ,cAAA,aAAwE;UACpER,EAAA,CAAAsD,UAAA,IAAA4Q,8CAAA,sBAA0O;UAC9OlU,EAAA,CAAAmB,YAAA,EAAM;UAGVnB,EAAA,CAAAQ,cAAA,cAAiG;UAAlER,EAAA,CAAAS,UAAA,sBAAA0T,6DAAA;YAAA,OAAYF,GAAA,CAAA/D,cAAA,EAAgB;UAAA,EAAC;UACxDlQ,EAAA,CAAAQ,cAAA,iBAAoF;UAQ7DR,EAAA,CAAAS,UAAA,2BAAA2T,oEAAAzT,MAAA;YAAA,OAAAsT,GAAA,CAAApI,UAAA,CAAAlG,IAAA,GAAAhF,MAAA;UAAA,EAA6B;UAHpCX,EAAA,CAAAmB,YAAA,EAKE;UACFnB,EAAA,CAAAQ,cAAA,iBAAsB;UAAAR,EAAA,CAAAoB,MAAA,IAAsD;UAAApB,EAAA,CAAAmB,YAAA,EAAQ;UAIhGnB,EAAA,CAAAQ,cAAA,cAAmB;UAIKR,EAAA,CAAAS,UAAA,2BAAA4T,yEAAA1T,MAAA;YAAA,OAAAsT,GAAA,CAAApI,UAAA,CAAAlH,MAAA,GAAAhE,MAAA;UAAA,EAA+B;UAK1CX,EAAA,CAAAmB,YAAA,EAAa;UACdnB,EAAA,CAAAQ,cAAA,iBAA2C;UAAAR,EAAA,CAAAoB,MAAA,IAAoD;UAAApB,EAAA,CAAAmB,YAAA,EAAQ;UAInHnB,EAAA,CAAAQ,cAAA,cAAmB;UAIKR,EAAA,CAAAS,UAAA,2BAAA6T,yEAAA3T,MAAA;YAAA,OAAAsT,GAAA,CAAApI,UAAA,CAAArF,aAAA,GAAA7F,MAAA;UAAA,EAAsC;UAKjDX,EAAA,CAAAmB,YAAA,EAAa;UACdnB,EAAA,CAAAQ,cAAA,iBAAkD;UAAAR,EAAA,CAAAoB,MAAA,IAAmD;UAAApB,EAAA,CAAAmB,YAAA,EAAQ;UAIzHnB,EAAA,CAAAQ,cAAA,cAAmB;UAIKR,EAAA,CAAAS,UAAA,2BAAA8T,yEAAA5T,MAAA;YAAA,OAAAsT,GAAA,CAAApI,UAAA,CAAAC,QAAA,GAAAnL,MAAA;UAAA,EAAiC;UAK5CX,EAAA,CAAAmB,YAAA,EAAa;UACdnB,EAAA,CAAAQ,cAAA,iBAA6C;UAAAR,EAAA,CAAAoB,MAAA,IAAsD;UAAApB,EAAA,CAAAmB,YAAA,EAAQ;UAIvHnB,EAAA,CAAAQ,cAAA,cAAmB;UAIKR,EAAA,CAAAS,UAAA,2BAAA+T,yEAAA7T,MAAA;YAAA,OAAAsT,GAAA,CAAApI,UAAA,CAAA5F,YAAA,GAAAtF,MAAA;UAAA,EAAqC;UAKhDX,EAAA,CAAAmB,YAAA,EAAa;UACdnB,EAAA,CAAAQ,cAAA,iBAAiD;UAAAR,EAAA,CAAAoB,MAAA,IAA0D;UAAApB,EAAA,CAAAmB,YAAA,EAAQ;UAI/HnB,EAAA,CAAAQ,cAAA,cAAmB;UAIKR,EAAA,CAAAS,UAAA,2BAAAgU,yEAAA9T,MAAA;YAAA,OAAAsT,GAAA,CAAApI,UAAA,CAAAlF,WAAA,GAAAhG,MAAA;UAAA,EAAoC;UAK/CX,EAAA,CAAAmB,YAAA,EAAa;UACdnB,EAAA,CAAAQ,cAAA,iBAAgD;UAAAR,EAAA,CAAAoB,MAAA,IAAyD;UAAApB,EAAA,CAAAmB,YAAA,EAAQ;UAG7HnB,EAAA,CAAAQ,cAAA,eAAwB;UACpBR,EAAA,CAAAC,SAAA,oBAGY;UAChBD,EAAA,CAAAmB,YAAA,EAAM;UAKdnB,EAAA,CAAAQ,cAAA,sBAYC;UAVGR,EAAA,CAAAS,UAAA,+BAAAiU,6EAAA/T,MAAA;YAAA,OAAAsT,GAAA,CAAA/L,WAAA,GAAAvH,MAAA;UAAA,EAA6B;UAUhCX,EAAA,CAAAmB,YAAA,EAAa;UAIdnB,EAAA,CAAAQ,cAAA,gBAAqE;UAAlCR,EAAA,CAAAS,UAAA,sBAAAkU,8DAAA;YAAA,OAAYV,GAAA,CAAA9D,kBAAA,EAAoB;UAAA,EAAC;UAChEnQ,EAAA,CAAAQ,cAAA,eAA2D;UACmBR,EAAA,CAAAS,UAAA,2BAAAmU,uEAAAjU,MAAA;YAAA,OAAAsT,GAAA,CAAA3L,sBAAA,GAAA3H,MAAA;UAAA,EAAoC;UAC1GX,EAAA,CAAAQ,cAAA,eAAkB;UAOCR,EAAA,CAAAS,UAAA,2BAAAoU,oEAAAlU,MAAA;YAAA,OAAAsT,GAAA,CAAAhT,cAAA,CAAAmP,QAAA,GAAAzP,MAAA;UAAA,EAAqC;UAH5CX,EAAA,CAAAmB,YAAA,EAKE;UACFnB,EAAA,CAAAQ,cAAA,iBAA0B;UAAAR,EAAA,CAAAoB,MAAA,IAAsD;UAAApB,EAAA,CAAAmB,YAAA,EAAQ;UAIhGnB,EAAA,CAAAQ,cAAA,cAAmB;UAKJR,EAAA,CAAAS,UAAA,2BAAAqU,oEAAAnU,MAAA;YAAA,OAAAsT,GAAA,CAAAhT,cAAA,CAAAoP,QAAA,GAAA1P,MAAA;UAAA,EAAqC;UAH5CX,EAAA,CAAAmB,YAAA,EAKE;UACFnB,EAAA,CAAAQ,cAAA,iBAA0B;UAAAR,EAAA,CAAAoB,MAAA,IAAsD;UAAApB,EAAA,CAAAmB,YAAA,EAAQ;UAIhGnB,EAAA,CAAAQ,cAAA,cAA2H;UACvHR,EAAA,CAAAsD,UAAA,KAAAyR,2CAAA,mBAUO;UACP/U,EAAA,CAAAsD,UAAA,KAAA0R,2CAAA,mBAAwH;UAC5HhV,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAQ,cAAA,eAAwB;UACpBR,EAAA,CAAAC,SAAA,oBAGY;UAChBD,EAAA,CAAAmB,YAAA,EAAM;UAEVnB,EAAA,CAAAQ,cAAA,sBAaC;UATOR,EAAA,CAAAS,UAAA,+BAAAwU,6EAAAtU,MAAA;YAAA,OAAAsT,GAAA,CAAA9L,eAAA,GAAAxH,MAAA;UAAA,EAAiC;UASxCX,EAAA,CAAAmB,YAAA,EAAa;UACdnB,EAAA,CAAAQ,cAAA,eAA+F;UACOR,EAAA,CAAAS,UAAA,mBAAAyU,+DAAA;YAAA,OAAAjB,GAAA,CAAA3L,sBAAA,GAAkC,KAAK;UAAA,EAAC;UAAoCtI,EAAA,CAAAmB,YAAA,EAAW;UACzLnB,EAAA,CAAAQ,cAAA,oBAAyJ;UAAxBR,EAAA,CAAAS,UAAA,mBAAA0U,+DAAA;YAAA,OAASlB,GAAA,CAAAxD,UAAA,EAAY;UAAA,EAAC;UAAEzQ,EAAA,CAAAmB,YAAA,EAAW;UAMpLnB,EAAA,CAAAQ,cAAA,eAAsD;UAClDR,EAAA,CAAAsD,UAAA,KAAA8R,+CAAA,0BA2JW;UACfpV,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAQ,cAAA,eAA2D;UACyBR,EAAA,CAAAS,UAAA,2BAAA4U,uEAAA1U,MAAA;YAAA,OAAAsT,GAAA,CAAAzL,+BAAA,GAAA7H,MAAA;UAAA,EAA6C;UAEzHX,EAAA,CAAAC,SAAA,sBAMc;UAClBD,EAAA,CAAAmB,YAAA,EAAW;;;UAvW6BnB,EAAA,CAAAqB,SAAA,GAAiD;UAAjDrB,EAAA,CAAAwB,iBAAA,CAAAyS,GAAA,CAAA7T,WAAA,CAAAC,SAAA,yBAAiD;UAC9CL,EAAA,CAAAqB,SAAA,GAAe;UAAfrB,EAAA,CAAAE,UAAA,UAAA+T,GAAA,CAAA7J,KAAA,CAAe,SAAA6J,GAAA,CAAA3J,IAAA;UAG3CtK,EAAA,CAAAqB,SAAA,GAAsD;UAAtDrB,EAAA,CAAAE,UAAA,SAAA+T,GAAA,CAAAlP,WAAA,CAAA/E,EAAA,CAAAgF,eAAA,KAAAC,GAAA,EAAAgP,GAAA,CAAAtL,cAAA,CAAAxD,WAAA,CAAAmQ,MAAA,GAAsD;UAInEtV,EAAA,CAAAqB,SAAA,GAAwB;UAAxBrB,EAAA,CAAAE,UAAA,cAAA+T,GAAA,CAAA/H,UAAA,CAAwB;UACjBlM,EAAA,CAAAqB,SAAA,GAAmB;UAAnBrB,EAAA,CAAAE,UAAA,oBAAmB,WAAA+T,GAAA,CAAA7T,WAAA,CAAAC,SAAA;UAQLL,EAAA,CAAAqB,SAAA,GAA6B;UAA7BrB,EAAA,CAAAE,UAAA,YAAA+T,GAAA,CAAApI,UAAA,CAAAlG,IAAA,CAA6B;UAGd3F,EAAA,CAAAqB,SAAA,GAAsD;UAAtDrB,EAAA,CAAAwB,iBAAA,CAAAyS,GAAA,CAAA7T,WAAA,CAAAC,SAAA,8BAAsD;UAM5CL,EAAA,CAAAqB,SAAA,GAAkB;UAAlBrB,EAAA,CAAAE,UAAA,mBAAkB,uCAAA+T,GAAA,CAAApI,UAAA,CAAAlH,MAAA,aAAAsP,GAAA,CAAAxJ,UAAA;UAQPzK,EAAA,CAAAqB,SAAA,GAAoD;UAApDrB,EAAA,CAAAwB,iBAAA,CAAAyS,GAAA,CAAA7T,WAAA,CAAAC,SAAA,4BAAoD;UAM/DL,EAAA,CAAAqB,SAAA,GAAkB;UAAlBrB,EAAA,CAAAE,UAAA,mBAAkB,uCAAA+T,GAAA,CAAApI,UAAA,CAAArF,aAAA,aAAAyN,GAAA,CAAAtJ,SAAA;UAQA3K,EAAA,CAAAqB,SAAA,GAAmD;UAAnDrB,EAAA,CAAAwB,iBAAA,CAAAyS,GAAA,CAAA7T,WAAA,CAAAC,SAAA,2BAAmD;UAMrEL,EAAA,CAAAqB,SAAA,GAAkB;UAAlBrB,EAAA,CAAAE,UAAA,mBAAkB,uCAAA+T,GAAA,CAAApI,UAAA,CAAAC,QAAA,aAAAmI,GAAA,CAAAlJ,YAAA;UAQL/K,EAAA,CAAAqB,SAAA,GAAsD;UAAtDrB,EAAA,CAAAwB,iBAAA,CAAAyS,GAAA,CAAA7T,WAAA,CAAAC,SAAA,8BAAsD;UAMnEL,EAAA,CAAAqB,SAAA,GAAkB;UAAlBrB,EAAA,CAAAE,UAAA,mBAAkB,uCAAA+T,GAAA,CAAApI,UAAA,CAAA5F,YAAA,aAAAgO,GAAA,CAAA9I,gBAAA;UAQDnL,EAAA,CAAAqB,SAAA,GAA0D;UAA1DrB,EAAA,CAAAwB,iBAAA,CAAAyS,GAAA,CAAA7T,WAAA,CAAAC,SAAA,kCAA0D;UAM3EL,EAAA,CAAAqB,SAAA,GAAkB;UAAlBrB,EAAA,CAAAE,UAAA,mBAAkB,uCAAA+T,GAAA,CAAApI,UAAA,CAAAlF,WAAA,aAAAsN,GAAA,CAAA1I,eAAA;UAQFvL,EAAA,CAAAqB,SAAA,GAAyD;UAAzDrB,EAAA,CAAAwB,iBAAA,CAAAyS,GAAA,CAAA7T,WAAA,CAAAC,SAAA,iCAAyD;UAczHL,EAAA,CAAAqB,SAAA,GAAgB;UAAhBrB,EAAA,CAAAE,UAAA,iBAAgB,gBAAA+T,GAAA,CAAA/L,WAAA,aAAA+L,GAAA,CAAAzF,OAAA,aAAAyF,GAAA,CAAAtC,OAAA,aAAAsC,GAAA,CAAA3H,WAAA,cAAA2H,GAAA,CAAA9G,MAAA,CAAAoI,IAAA,CAAAtB,GAAA,iBAAAA,GAAA,CAAA9H,UAAA,cAAA8H,GAAA,CAAA7H,QAAA,UAAA6H,GAAA,CAAA5H,IAAA,YAAA4H,GAAA,CAAApI,UAAA,gBAAAoI,GAAA,CAAA7T,WAAA,CAAAC,SAAA;UAedL,EAAA,CAAAqB,SAAA,GAA4B;UAA5BrB,EAAA,CAAAE,UAAA,cAAA+T,GAAA,CAAAjI,cAAA,CAA4B;UAEoGhM,EAAA,CAAAqB,SAAA,GAA4B;UAA5BrB,EAAA,CAAAwE,UAAA,CAAAxE,EAAA,CAAAM,eAAA,KAAAkV,GAAA,EAA4B;UAAhJxV,EAAA,CAAAE,UAAA,WAAA+T,GAAA,CAAA7T,WAAA,CAAAC,SAAA,gCAA+D,YAAA4T,GAAA,CAAA3L,sBAAA;UAQlDtI,EAAA,CAAAqB,SAAA,GAAqC;UAArCrB,EAAA,CAAAE,UAAA,YAAA+T,GAAA,CAAAhT,cAAA,CAAAmP,QAAA,CAAqC;UAGlBpQ,EAAA,CAAAqB,SAAA,GAAsD;UAAtDrB,EAAA,CAAAwB,iBAAA,CAAAyS,GAAA,CAAA7T,WAAA,CAAAC,SAAA,8BAAsD;UASzEL,EAAA,CAAAqB,SAAA,GAAqC;UAArCrB,EAAA,CAAAE,UAAA,YAAA+T,GAAA,CAAAhT,cAAA,CAAAoP,QAAA,CAAqC;UAGlBrQ,EAAA,CAAAqB,SAAA,GAAsD;UAAtDrB,EAAA,CAAAwB,iBAAA,CAAAyS,GAAA,CAAA7T,WAAA,CAAAC,SAAA,8BAAsD;UAIrEL,EAAA,CAAAqB,SAAA,GAAuG;UAAvGrB,EAAA,CAAA4F,UAAA,CAAAqO,GAAA,CAAAjK,QAAA,IAAAiK,GAAA,CAAArL,WAAA,CAAA4I,KAAA,iEAAuG;UACzFxR,EAAA,CAAAqB,SAAA,GAAmC;UAAnCrB,EAAA,CAAAE,UAAA,SAAA+T,GAAA,CAAAjK,QAAA,IAAAiK,GAAA,CAAArL,WAAA,CAAA4I,KAAA,CAAmC;UAWzDxR,EAAA,CAAAqB,SAAA,GAAmC;UAAnCrB,EAAA,CAAAE,UAAA,SAAA+T,GAAA,CAAAjK,QAAA,IAAAiK,GAAA,CAAArL,WAAA,CAAA4I,KAAA,CAAmC;UAU1CxR,EAAA,CAAAqB,SAAA,GAAgB;UAAhBrB,EAAA,CAAAE,UAAA,iBAAgB,eAAA+T,GAAA,CAAA3G,gBAAA,cAAA2G,GAAA,CAAA1G,cAAA,iBAAA0G,GAAA,CAAA9L,eAAA,aAAA8L,GAAA,CAAArE,eAAA,aAAAqE,GAAA,CAAAvE,iBAAA,aAAAuE,GAAA,CAAA1F,qBAAA,cAAA0F,GAAA,CAAA5F,UAAA,CAAAkH,IAAA,CAAAtB,GAAA,yBAAAjU,EAAA,CAAAM,eAAA,KAAAmV,GAAA,oCAAAxB,GAAA,CAAA5H,IAAA,YAAA4H,GAAA,CAAAhT,cAAA;UAcuHjB,EAAA,CAAAqB,SAAA,GAAkC;UAAlCrB,EAAA,CAAAwE,UAAA,CAAAxE,EAAA,CAAAM,eAAA,KAAAoV,GAAA,EAAkC;UAAnI1V,EAAA,CAAAE,UAAA,UAAA+T,GAAA,CAAA7T,WAAA,CAAAC,SAAA,yBAAuD;UAC5DL,EAAA,CAAAqB,SAAA,GAAqD;UAArDrB,EAAA,CAAAE,UAAA,UAAA+T,GAAA,CAAA7T,WAAA,CAAAC,SAAA,uBAAqD,aAAA4T,GAAA,CAAA1D,sBAAA;UAOoFvQ,EAAA,CAAAqB,SAAA,GAAuB;UAAvBrB,EAAA,CAAAE,UAAA,SAAA+T,GAAA,CAAA5Q,iBAAA,CAAuB;UA8JpErD,EAAA,CAAAqB,SAAA,GAA6B;UAA7BrB,EAAA,CAAAwE,UAAA,CAAAxE,EAAA,CAAAM,eAAA,KAAAqV,GAAA,EAA6B;UAAhK3V,EAAA,CAAAE,UAAA,WAAA+T,GAAA,CAAA7T,WAAA,CAAAC,SAAA,sCAAqE,YAAA4T,GAAA,CAAAzL,+BAAA;UAGvExI,EAAA,CAAAqB,SAAA,GAAiB;UAAjBrB,EAAA,CAAAE,UAAA,kBAAiB,YAAA+T,GAAA,CAAA1K,wBAAA,aAAA0K,GAAA,CAAApK,0BAAA,aAAAoK,GAAA,CAAAjL,8BAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}