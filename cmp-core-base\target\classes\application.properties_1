#mongodb
spring.data.mongodb.host=************
spring.data.mongodb.port=27017
spring.data.mongodb.database=resourcedb

mongodb.host=************
mongodb.port=27017
mongodb.database=resourcedb

##rabbit
#spring.rabbitmq.host=************
spring.rabbitmq.host=************
spring.rabbitmq.port=5672
spring.rabbitmq.username=admin
spring.rabbitmq.password=onegate@2020

#logging
logging.level.org.springframework=INFO
logging.level.vn.vnpt.onegate=DEBUG
logging.level.=INFO

#redis
spring.redis.hostName=************
spring.redis.port=6379
spring.redis.password=ump@2018
spring.redis.jedis.pool.max-active=1000
spring.redis.jedis.pool.min-idle=0
spring.redis.jedis.pool.max-wait=-1
spring.redis.jedis.pool.max-idle=10

#mysql
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=******************************************************************************************
spring.datasource.username=onegate
spring.datasource.password=onegate@123

#hibernate
spring.jpa.show-sql=true
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL5Dialect
