{"ast": null, "code": "import { TicketService } from \"src/app/service/ticket/TicketService\";\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\nimport { ComponentBase } from \"src/app/component.base\";\nimport { AccountService } from \"../../../../service/account/AccountService\";\nimport { InputFileVnptComponent } from \"../../../common-module/input-file/input.file.component\";\nimport * as XLSX from 'xlsx';\nimport { SimTicketService } from \"../../../../service/ticket/SimTicketService\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/breadcrumb\";\nimport * as i4 from \"primeng/tooltip\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/inputtext\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"../../../common-module/table/table.component\";\nimport * as i9 from \"../../../common-module/input-file/input.file.component\";\nimport * as i10 from \"../../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i11 from \"primeng/calendar\";\nimport * as i12 from \"primeng/dropdown\";\nimport * as i13 from \"primeng/card\";\nimport * as i14 from \"primeng/dialog\";\nimport * as i15 from \"primeng/panel\";\nimport * as i16 from \"primeng/table\";\nimport * as i17 from \"src/app/service/ticket/TicketService\";\nimport * as i18 from \"../../../../service/account/AccountService\";\nimport * as i19 from \"../../../../service/ticket/SimTicketService\";\nfunction ListActiveSimTicketComponent_p_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 69);\n    i0.ɵɵlistener(\"click\", function ListActiveSimTicketComponent_p_button_6_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.showModalCreate());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"global.button.create\"));\n  }\n}\nfunction ListActiveSimTicketComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"span\", 11)(2, \"p-dropdown\", 70);\n    i0.ɵɵlistener(\"ngModelChange\", function ListActiveSimTicketComponent_div_10_Template_p_dropdown_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.searchInfo.provinceCode = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 71);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"showClear\", true)(\"filter\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx_r1.searchInfo.provinceCode)(\"required\", false)(\"options\", ctx_r1.listProvince);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.tranService.translate(\"account.label.province\"));\n  }\n}\nfunction ListActiveSimTicketComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"label\", 32);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 33);\n    i0.ɵɵtext(4, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 34)(6, \"p-dropdown\", 72);\n    i0.ɵɵlistener(\"ngModelChange\", function ListActiveSimTicketComponent_div_46_Template_p_dropdown_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.ticket.provinceCode = $event);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"account.label.province\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"showClear\", false)(\"filter\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx_r2.ticket.provinceCode)(\"options\", ctx_r2.listProvince)(\"disabled\", true)(\"readonly\", true);\n  }\n}\nconst _c0 = function () {\n  return {\n    len: 255\n  };\n};\nfunction ListActiveSimTicketComponent_small_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListActiveSimTicketComponent_small_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.tranService.translate(\"global.message.formatCode\"));\n  }\n}\nfunction ListActiveSimTicketComponent_small_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction ListActiveSimTicketComponent_small_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 33);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r6.tranService.translate(\"global.message.formatCode\"));\n  }\n}\nfunction ListActiveSimTicketComponent_div_103_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\", 79);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r15.tranService.translate(\"global.text.stt\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r15.tranService.translate(\"ticket.label.imsi\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"hidden\", ctx_r15.typeRequest == \"view\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r15.tranService.translate(\"global.text.action\"));\n  }\n}\nfunction ListActiveSimTicketComponent_div_103_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 79)(6, \"p-button\", 80);\n    i0.ɵɵlistener(\"onClick\", function ListActiveSimTicketComponent_div_103_ng_template_11_Template_p_button_onClick_6_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const i_r18 = restoredCtx.rowIndex;\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.removeImsi(i_r18));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r17 = ctx.$implicit;\n    const i_r18 = ctx.rowIndex;\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r18 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r17);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"hidden\", ctx_r16.typeRequest == \"view\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r16.isShowUpload);\n  }\n}\nfunction ListActiveSimTicketComponent_div_103_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"p-card\", 74)(2, \"div\", 29)(3, \"label\", 30)(4, \"b\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \":\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 75)(9, \"p-table\", 76);\n    i0.ɵɵtemplate(10, ListActiveSimTicketComponent_div_103_ng_template_10_Template, 7, 4, \"ng-template\", 77);\n    i0.ɵɵtemplate(11, ListActiveSimTicketComponent_div_103_ng_template_11_Template, 7, 4, \"ng-template\", 78);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r7.typeRequest == \"create\" ? \"mt-4\" : \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r7.tranService.translate(\"ticket.label.listactiveImsis\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", ctx_r7.listImsisSelected);\n  }\n}\nfunction ListActiveSimTicketComponent_p_button_106_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 81);\n    i0.ɵɵlistener(\"onClick\", function ListActiveSimTicketComponent_p_button_106_Template_p_button_onClick_0_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.isShowCreateRequest = false);\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r8.formActiveSim.invalid || ctx_r8.listImsisSelected.length == 0)(\"hidden\", ctx_r8.typeRequest == \"view\")(\"label\", ctx_r8.tranService.translate(\"global.button.save\"));\n  }\n}\nconst _c1 = function (a0) {\n  return [a0];\n};\nconst _c2 = function () {\n  return {\n    width: \"1000px\"\n  };\n};\nconst _c3 = function () {\n  return {\n    status: 0,\n    ticketType: 2\n  };\n};\nconst _c4 = function () {\n  return {\n    width: \"500px\"\n  };\n};\nexport class ListActiveSimTicketComponent extends ComponentBase {\n  constructor(ticketService, accountService, simTicketService, cdr, formBuilder, injector) {\n    super(injector);\n    this.ticketService = ticketService;\n    this.accountService = accountService;\n    this.simTicketService = simTicketService;\n    this.cdr = cdr;\n    this.formBuilder = formBuilder;\n    this.injector = injector;\n    this.maxDateFrom = new Date();\n    this.minDateTo = null;\n    this.maxDateTo = new Date();\n    this.listImsis = [];\n    this.listImsisSelected = [];\n    this.errorRecords = [];\n    this.CONSTANTS = CONSTANTS;\n  }\n  ngOnInit() {\n    let me = this;\n    this.userInfo = this.sessionService.userInfo;\n    this.isShowCreateRequest = false;\n    this.isShowDownload = false;\n    this.isShowUpload = false;\n    this.typeRequest = 'create';\n    this.userType = CONSTANTS.USER_TYPE;\n    this.isValidActiveSim = true;\n    this.ticket = {\n      id: null,\n      contactName: null,\n      contactEmail: null,\n      contactPhone: null,\n      content: null,\n      note: null,\n      cause: null,\n      type: CONSTANTS.REQUEST_TYPE.ACTIVE_SIM,\n      status: null,\n      statusOld: null,\n      assigneeId: null,\n      provinceCode: null\n    };\n    this.fileName = \"\";\n    this.listTicketType = [{\n      label: this.tranService.translate('ticket.type.activeSim'),\n      value: 1\n    }];\n    this.mapTicketStatus = {\n      0: [{\n        label: me.tranService.translate('ticket.status.received'),\n        value: 1\n      }],\n      1: [{\n        label: me.tranService.translate('ticket.status.inProgress'),\n        value: 2\n      }, {\n        label: me.tranService.translate('ticket.status.reject'),\n        value: 3\n      }],\n      2: [{\n        label: me.tranService.translate('ticket.status.done'),\n        value: 4\n      }]\n    };\n    this.listTicketStatus = [{\n      label: me.tranService.translate('ticket.status.new'),\n      value: 0\n    }, {\n      label: me.tranService.translate('ticket.status.received'),\n      value: 1\n    }, {\n      label: me.tranService.translate('ticket.status.inProgress'),\n      value: 2\n    }, {\n      label: me.tranService.translate('ticket.status.reject'),\n      value: 3\n    }, {\n      label: me.tranService.translate('ticket.status.done'),\n      value: 4\n    }];\n    this.searchInfo = {\n      provinceCode: null,\n      email: null,\n      contactPhone: null,\n      contactEmail: null,\n      type: CONSTANTS.REQUEST_TYPE.ACTIVE_SIM,\n      status: null,\n      dateFrom: null,\n      dateTo: null,\n      contactName: null\n    };\n    this.columns = [{\n      name: this.tranService.translate(\"ticket.label.province\"),\n      key: \"provinceName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: this.userInfo.type == CONSTANTS.USER_TYPE.ADMIN,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"ticket.label.customerName\"),\n      key: \"contactName\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      isShowTooltip: true,\n      style: {\n        display: 'inline-block',\n        maxWidth: '350px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.email\"),\n      key: \"contactEmail\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      isShowTooltip: true,\n      style: {\n        display: 'inline-block',\n        maxWidth: '350px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.phone\"),\n      key: \"contactPhone\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"ticket.label.content\"),\n      key: \"content\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      isShowTooltip: true,\n      style: {\n        display: 'inline-block',\n        maxWidth: '350px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      }\n    }, {\n      name: this.tranService.translate(\"ticket.label.createdDate\"),\n      key: \"createdDate\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        if (value == null) return null;\n        return me.utilService.convertDateToString(new Date(value));\n      }\n    }\n    // {\n    //     name: this.tranService.translate(\"ticket.label.updatedDate\"),\n    //     key: \"updatedDate\",\n    //     size: \"fit-content\",\n    //     align: \"left\",\n    //     isShow: true,\n    //     isSort: true,\n    //     funcConvertText(value) {\n    //         if (value == null) return null;\n    //         return me.utilService.convertDateToString(new Date(value))\n    //     },\n    // },\n    // {\n    //     name: this.tranService.translate(\"ticket.label.updateBy\"),\n    //     key: \"updatedByName\",\n    //     size: \"fit-content\",\n    //     align: \"left\",\n    //     isShow: true,\n    //     isSort: true\n    // },\n    // {\n    //     name: this.tranService.translate(\"ticket.label.status\"),\n    //     key: \"status\",\n    //     size: \"fit-content\",\n    //     align: \"left\",\n    //     isShow: true,\n    //     isSort: true,\n    //     funcGetClassname: (value) => {\n    //         if (value == CONSTANTS.REQUEST_STATUS.NEW) {\n    //             return ['p-2', 'text-white', \"bg-cyan-300\", \"border-round\", \"inline-block\"];\n    //         } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\n    //             return ['p-2', 'text-white', \"bg-bluegray-500\", \"border-round\", \"inline-block\"];\n    //         } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\n    //             return ['p-2', 'text-white', \"bg-orange-400\", \"border-round\", \"inline-block\"];\n    //         } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\n    //             return ['p-2', 'text-white', \"bg-red-500\", \"border-round\", \"inline-block\"];\n    //         } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\n    //             return ['p-2', 'text-white', \"bg-green-500\", \"border-round\", \"inline-block\"];\n    //         }\n    //         return '';\n    //     },\n    //     funcConvertText: function (value) {\n    //         if (value == CONSTANTS.REQUEST_STATUS.NEW) {\n    //             return me.tranService.translate(\"ticket.status.new\");\n    //         } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\n    //             return me.tranService.translate(\"ticket.status.received\");\n    //         } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\n    //             return me.tranService.translate(\"ticket.status.inProgress\");\n    //         } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\n    //             return me.tranService.translate(\"ticket.status.reject\");\n    //         } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\n    //             return me.tranService.translate(\"ticket.status.done\");\n    //         }\n    //         return \"\";\n    //     }\n    // }\n    ];\n\n    this.optionTable = {\n      hasClearSelected: false,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false,\n      action: [{\n        icon: \"pi pi-info-circle\",\n        tooltip: this.tranService.translate(\"global.button.view\"),\n        func: function (id, item) {\n          me.handleRequest(id, item, 'view');\n        }\n      }]\n    };\n    this.pageNumber = 0;\n    this.pageSize = 10;\n    this.sort = \"createdDate,desc\";\n    this.dataSet = {\n      content: [],\n      total: 0\n    };\n    this.formSearchTicket = this.formBuilder.group(this.searchInfo);\n    this.formActiveSim = this.formBuilder.group(this.ticket);\n    this.optionInputFile = {\n      type: ['xls', 'xlsx'],\n      messageErrorType: this.tranService.translate(\"global.message.wrongFileExcel\"),\n      maxSize: 100,\n      unit: \"MB\",\n      required: false,\n      isShowButtonUpload: true,\n      actionUpload: this.uploadFile.bind(this),\n      disabled: false\n    };\n    this.getListProvince();\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n  }\n  search(page, limit, sort, params) {\n    let me = this;\n    this.pageNumber = page;\n    this.pageSize = limit;\n    this.sort = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    Object.keys(this.searchInfo).forEach(key => {\n      if (this.searchInfo[key] != null) {\n        if (key == \"dateFrom\") {\n          dataParams[\"dateFrom\"] = this.searchInfo.dateFrom.getTime();\n        } else if (key == \"dateTo\") {\n          dataParams[\"dateTo\"] = this.searchInfo.dateTo.getTime();\n        } else {\n          dataParams[key] = this.searchInfo[key];\n        }\n      }\n    });\n    this.dataSet = {\n      content: [],\n      total: 0\n    };\n    // me.messageCommonService.onload();\n    this.ticketService.searchTicket(dataParams, response => {\n      me.dataSet = {\n        content: response.content,\n        total: response.totalElements\n      };\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  resetTicket() {\n    this.ticket = {\n      id: null,\n      contactName: null,\n      contactEmail: null,\n      contactPhone: null,\n      content: null,\n      note: null,\n      cause: null,\n      type: CONSTANTS.REQUEST_TYPE.ACTIVE_SIM,\n      status: null,\n      statusOld: null,\n      assigneeId: null,\n      provinceCode: null\n    };\n    this.listImsisSelected = [];\n    this.errorRecords = [];\n    this.isShowDownload = false;\n  }\n  onSubmitSearch() {\n    this.pageNumber = 0;\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n  }\n  getListProvince() {\n    this.accountService.getListProvince(response => {\n      this.listProvince = response.map(el => {\n        return {\n          ...el,\n          display: `${el.code} - ${el.name}`\n        };\n      });\n    });\n  }\n  // tạo sửa yêu cầu\n  createRequest() {\n    let me = this;\n    console.log(\"create\");\n    me.messageCommonService.onload();\n    let bodySend = {\n      contactName: this.ticket.contactName,\n      contactEmail: this.ticket.contactEmail,\n      contactPhone: this.ticket.contactPhone,\n      content: this.ticket.content,\n      note: this.ticket.note,\n      type: this.ticket.type\n    };\n    this.ticketService.createTicket(bodySend, resp => {\n      // console.log(resp)\n      let createSimTicketBody = {\n        ticketId: resp.id,\n        userCustomerId: resp.createdBy,\n        userHandleId: resp.assigneeId,\n        imsis: me.listImsisSelected\n      };\n      me.simTicketService.create(createSimTicketBody, res => {\n        console.log(res);\n        me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n        me.isShowCreateRequest = false;\n        me.messageCommonService.offload();\n        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n      });\n      // get mail admin tinh\n      // me.ticketService.getListAssignee({email : '', provinceCode : this.userInfo.provinceCode, page : 0, size: 99999999}, (respAssignee)=>{\n      //     let listProvinceConfig = respAssignee.content;\n      //     let array = []\n      //     for (let user of listProvinceConfig) {\n      //         array.push({\n      //             userId: user.id,\n      //             ticketId: resp.id\n      //         })\n      //     }\n      //     me.ticketService.sendMailNotify(array);\n      // })\n      me.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, resp1 => {\n        let array = [];\n        for (let info of resp1.emailInfos) {\n          array.push({\n            userId: info.userId,\n            ticketId: resp.id\n          });\n        }\n        if (resp?.assigneeId) {\n          array.push({\n            userId: resp.assigneeId,\n            ticketId: resp.id\n          });\n        }\n        me.ticketService.sendMailNotify(array);\n      });\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  showModalCreate() {\n    this.isShowCreateRequest = true;\n    this.typeRequest = 'create';\n    this.resetTicket();\n    // auto fill thong tin khi tao\n    if (this.userInfo.type === CONSTANTS.USER_TYPE.CUSTOMER) {\n      this.ticket.contactName = this.userInfo.fullName;\n      this.ticket.contactPhone = this.userInfo.phone;\n      this.ticket.contactEmail = this.userInfo.email;\n    }\n    this.formActiveSim = this.formBuilder.group(this.ticket);\n  }\n  handleRequest(id, item, typeRequest) {\n    if (typeRequest == 'view') {\n      this.typeRequest = typeRequest;\n      this.isShowCreateRequest = true;\n    }\n    let me = this;\n    this.resetTicket();\n    this.formActiveSim.reset();\n    this.ticketService.getDetailTicket(item.id, resp => {\n      this.ticket = {\n        id: resp.id,\n        contactName: resp.contactName,\n        contactEmail: resp.contactEmail,\n        contactPhone: resp.contactPhone,\n        content: resp.content,\n        note: resp.note,\n        cause: resp.cause,\n        type: resp.type,\n        status: null,\n        statusOld: resp.status,\n        assigneeId: resp.assigneeId,\n        provinceCode: resp.provinceCode\n      };\n      this.simTicketService.search({\n        ticketId: this.ticket.id,\n        size: 1000\n      }, res => {\n        let imsis = [];\n        res.content.forEach(item => {\n          imsis.push(item.imsi);\n        });\n        this.listImsisSelected = imsis;\n      });\n    });\n    this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, resp => {\n      this.listEmail = resp.emailInfos;\n    });\n  }\n  preventCharacter(event) {\n    if (event.ctrlKey) {\n      return;\n    }\n    if (event.keyCode == 8 || event.keyCode == 13 || event.keyCode == 37 || event.keyCode == 39) {\n      return;\n    }\n    if (event.keyCode < 48 || event.keyCode > 57) {\n      event.preventDefault();\n    }\n    // Chặn ký tự 'e', 'E' và dấu '+'\n    if (event.keyCode == 69 || event.keyCode == 101 || event.keyCode == 107 || event.keyCode == 187) {\n      event.preventDefault();\n    }\n  }\n  downloadTemplate() {\n    this.ticketService.downloadTemplate();\n  }\n  uploadFile(file) {\n    let me = this;\n    me.isShowUpload = false;\n    this.fileName = this.removeFileExtension(file.name);\n    me.messageCommonService.onload();\n    if (file.size > 1024 * 1024) {\n      // 1MB\n      me.messageCommonService.offload();\n      me.messageCommonService.warning(me.tranService.translate(\"ticket.message.largeFile\"));\n      return;\n    }\n    if (file) {\n      // me.listImsisSelected = [];\n      me.errorRecords = [];\n      me.listImsis = [];\n      const reader = new FileReader();\n      reader.onload = e => {\n        const binaryStr = e.target.result;\n        const workbook = XLSX.read(binaryStr, {\n          type: 'binary'\n        });\n        const sheetName = workbook.SheetNames[0];\n        const worksheet = workbook.Sheets[sheetName];\n        const jsonData = XLSX.utils.sheet_to_json(worksheet, {\n          header: 1\n        });\n        // Bỏ qua dòng trống và chỉ lấy dòng có dữ liệu\n        let headers = jsonData[0];\n        let data = jsonData.slice(1).filter(row => row.some(cell => cell !== null && cell !== undefined && cell !== ''));\n        this.validateFile(headers, data);\n      };\n      reader.readAsBinaryString(file);\n    }\n  }\n  validateFile(headers, data) {\n    let me = this;\n    me.errorRecords = [];\n    me.inputFileComponent.resetFile();\n    const requiredHeaders = ['STT', 'IMSI (Bắt buộc)'];\n    const extraColumns = headers.length > requiredHeaders.length;\n    const missingColumns = headers.length < requiredHeaders.length;\n    for (let i = 0; i < requiredHeaders.length; i++) {\n      if (headers[i] !== requiredHeaders[i]) {\n        me.messageCommonService.offload();\n        me.messageCommonService.error(me.tranService.translate('ticket.message.wrongSample'));\n        return;\n      }\n    }\n    if (extraColumns || missingColumns) {\n      if (extraColumns) {\n        me.messageCommonService.error(me.tranService.translate('ticket.message.redundantColumns'));\n      }\n      if (missingColumns) {\n        me.messageCommonService.error(me.tranService.translate('ticket.message.missingColumns'));\n      }\n      me.messageCommonService.offload();\n      return;\n    }\n    if (data.length === 0) {\n      me.messageCommonService.offload();\n      me.messageCommonService.error(me.tranService.translate('ticket.message.emptyFile'));\n      return;\n    }\n    const filteredRecords = data.filter(record => record.length === requiredHeaders.length && (record[0] !== undefined && record[0].toString().trim() !== '' || record[1].trim() !== ''));\n    if (filteredRecords.length === 0) {\n      me.messageCommonService.offload();\n      me.messageCommonService.error(me.tranService.translate('ticket.message.emptyFile'));\n      return;\n    }\n    data.forEach((record, index) => {\n      let errors = [];\n      if (!record[1] || record[1].trim() === '') {\n        errors.push(me.tranService.translate('ticket.message.missingImsiInfo'));\n      } else if (typeof record[1] !== 'string' || !/^\\d+$/.test(record[1]) || record[1].length > 18) {\n        errors.push(me.tranService.translate('ticket.message.wrongImsiFormat'));\n      } else {\n        this.listImsis.push(record[1]);\n      }\n      if (errors.length > 0) {\n        this.errorRecords.push([...record, errors.join(', ')]);\n      }\n    });\n    this.listImsis = this.listImsis.map(imsi => Number(imsi.trim()));\n    if (this.listImsis.length > 0) {\n      me.simTicketService.search({\n        listImsi: me.listImsis.join(','),\n        ticketType: CONSTANTS.REQUEST_TYPE.ORDER_SIM\n      }, res => {\n        let index = 0;\n        for (const imsi of me.listImsis) {\n          let found = false;\n          for (const item of res.content) {\n            if (imsi === item.imsi) {\n              if (item.status == CONSTANTS.SIM_TICKET_STATUS.ACTIVATED) {\n                me.errorRecords.push(['0', imsi + '', me.tranService.translate('ticket.message.imsiIsActivated')]);\n              } else {\n                if (me.listImsisSelected.includes(imsi)) {\n                  me.errorRecords.push(['0', imsi + '', me.tranService.translate(\"ticket.message.imsiIsExist\")]);\n                } else {\n                  me.listImsisSelected.push(imsi);\n                }\n              }\n              found = true;\n              break;\n            }\n          }\n          // Nếu không tìm thấy IMSI trong\n          if (!found) {\n            me.errorRecords.push(['0', imsi + '', me.tranService.translate(\"ticket.message.imsiNotExist\")]);\n          }\n        }\n        me.messageCommonService.offload();\n        if (this.errorRecords.length > 0) {\n          me.isShowDownload = true;\n        } else {\n          me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n        }\n      });\n    } else {\n      me.messageCommonService.offload();\n      if (this.errorRecords.length > 0) {\n        me.isShowDownload = true;\n      } else {\n        me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n      }\n    }\n  }\n  downloadErrorFile() {\n    let me = this;\n    for (let i = 0; i < me.errorRecords.length; i++) {\n      me.errorRecords[i][0] = (i + 1).toString();\n    }\n    console.log(this.errorRecords);\n    const ws = XLSX.utils.aoa_to_sheet([['STT', 'IMSI', 'Nội dung lỗi'], ...this.errorRecords]);\n    const wb = XLSX.utils.book_new();\n    XLSX.utils.book_append_sheet(wb, ws, 'Errors');\n    const wbout = XLSX.write(wb, {\n      bookType: 'xlsx',\n      type: 'binary'\n    });\n    const buf = new ArrayBuffer(wbout.length);\n    const view = new Uint8Array(buf);\n    for (let i = 0; i < wbout.length; ++i) {\n      view[i] = wbout.charCodeAt(i) & 0xFF;\n    }\n    const blob = new Blob([buf], {\n      type: 'application/octet-stream'\n    });\n    const url = window.URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `${this.fileName}_Danh_sách_lỗi_${new Date().toISOString().replace(/[-:T.]/g, '').substring(0, 14)}.xlsx`;\n    document.body.appendChild(a);\n    a.click();\n    window.URL.revokeObjectURL(url);\n    document.body.removeChild(a);\n    this.isShowDownload = false;\n  }\n  onChangeDateFrom(value) {\n    if (value) {\n      this.minDateTo = value;\n    } else {\n      this.minDateTo = null;\n    }\n  }\n  onChangeDateTo(value) {\n    if (value) {\n      this.maxDateFrom = value;\n    } else {\n      this.maxDateFrom = new Date();\n    }\n  }\n  removeImsi(i) {\n    this.listImsisSelected.splice(i, 1);\n  }\n  isHideUpload() {\n    this.isShowUpload = false;\n  }\n  removeFileExtension(fileName) {\n    const lastDotIndex = fileName.lastIndexOf('.');\n    if (lastDotIndex === -1) return fileName;\n    return fileName.substring(0, lastDotIndex);\n  }\n  onKeyDownNote(event) {\n    if (event.key === ' ' && (this.ticket.note == null || this.ticket.note != null && this.ticket.note.trim() === '')) {\n      event.preventDefault();\n    }\n    if (this.ticket.note != null && this.ticket.note.trim() != '') {\n      this.ticket.note = this.ticket.note.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  onKeyDownContent(event) {\n    if (event.key === ' ' && (this.ticket.content == null || this.ticket.content != null && this.ticket.content.trim() === '')) {\n      event.preventDefault();\n    }\n    if (this.ticket.content != null && this.ticket.content.trim() != '') {\n      this.ticket.content = this.ticket.content.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  onKeyDownNoteContent(event, note) {\n    if (event.key === ' ' && (!note.content || note.content.trim() === '')) {\n      event.preventDefault();\n    }\n    if (note.content && note.content.trim() !== '') {\n      note.content = note.content.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  onKeyDownCause(event) {\n    if (event.key === ' ' && (this.ticket.cause == null || this.ticket.cause != null && this.ticket.cause.trim() === '')) {\n      event.preventDefault();\n    }\n    if (this.ticket.cause != null && this.ticket.cause.trim() != '') {\n      this.ticket.cause = this.ticket.cause.trimStart().replace(/\\s{2,}/g, ' ');\n      return;\n    }\n  }\n  validateInput(event) {\n    const invalidChars = ['e', 'E', '+', '-'];\n    const inputField = event.target;\n    let value = inputField.value;\n    // Remove invalid characters from the value\n    invalidChars.forEach(char => {\n      value = value.split(char).join('');\n    });\n    // Update the input field value if it contains invalid characters\n    if (inputField.value !== value) {\n      inputField.value = value;\n    }\n  }\n  static {\n    this.ɵfac = function ListActiveSimTicketComponent_Factory(t) {\n      return new (t || ListActiveSimTicketComponent)(i0.ɵɵdirectiveInject(TicketService), i0.ɵɵdirectiveInject(AccountService), i0.ɵɵdirectiveInject(SimTicketService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ListActiveSimTicketComponent,\n      selectors: [[\"list-active-sim-ticket\"]],\n      viewQuery: function ListActiveSimTicketComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(InputFileVnptComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputFileComponent = _t.first);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 123,\n      vars: 112,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [\"styleClass\", \"p-button-info\", \"icon\", \"\", 3, \"label\", \"click\", 4, \"ngIf\"], [1, \"pt-3\", \"pb-2\", \"vnpt-field-set\", 3, \"formGroup\", \"ngSubmit\"], [3, \"toggleable\", \"header\"], [1, \"grid\", \"search-grid-4\"], [\"class\", \"col-2\", 4, \"ngIf\"], [1, \"col-2\"], [1, \"p-float-label\"], [\"pInputText\", \"\", \"id\", \"contactName\", \"formControlName\", \"contactName\", \"type\", \"text\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"contactName\"], [\"pInputText\", \"\", \"id\", \"phone\", \"formControlName\", \"contactPhone\", \"type\", \"number\", \"min\", \"0\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\", \"keydown\"], [\"htmlFor\", \"phone\"], [\"styleClass\", \"w-full\", \"id\", \"dateFrom\", \"formControlName\", \"dateFrom\", \"dateFormat\", \"dd/mm/yy\", 3, \"ngModel\", \"showIcon\", \"showClear\", \"maxDate\", \"ngModelChange\", \"onSelect\", \"onInput\"], [\"htmlFor\", \"dateFrom\", 1, \"label-calendar\"], [\"styleClass\", \"w-full\", \"id\", \"dateTo\", \"formControlName\", \"dateTo\", \"dateFormat\", \"dd/mm/yy\", 3, \"ngModel\", \"showIcon\", \"showClear\", \"minDate\", \"maxDate\", \"ngModelChange\", \"onSelect\", \"onInput\"], [\"htmlFor\", \"dateTo\", 1, \"label-calendar\"], [1, \"col-2\", \"pb-0\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"submit\"], [3, \"tableId\", \"fieldId\", \"columns\", \"dataSet\", \"options\", \"pageNumber\", \"loadData\", \"pageSize\", \"sort\", \"params\", \"labelTable\"], [1, \"flex\", \"justify-content-center\"], [3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\", \"onHide\"], [1, \"mt-3\", 3, \"formGroup\", \"ngSubmit\"], [1, \"flex\", \"dialog-ticket-sim-1\"], [1, \"flex-1\", \"flex\", \"col-6\"], [1, \"flex-wrap\", \"w-full\", \"ticket-sim-card\"], [1, \"w-full\", \"field\", \"grid\"], [1, \"col-fixed\", 2, \"width\", \"180px\"], [\"class\", \"w-full field grid\", 4, \"ngIf\"], [\"htmlFor\", \"contactName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"text-red-500\"], [1, \"col\"], [\"pInputText\", \"\", \"id\", \"contactName\", \"formControlName\", \"contactName\", \"pattern\", \"^[^~`!@#\\\\$%\\\\^&*\\\\(\\\\)=\\\\+\\\\[\\\\]\\\\{\\\\}\\\\|\\\\\\\\,<>\\\\/?]*$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"readonly\", \"ngModelChange\"], [\"htmlFor\", \"email\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"contactEmail\", \"formControlName\", \"contactEmail\", \"pattern\", \"^[a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\\\-\\\\._]*[a-z0-9]+)+(\\\\.[a-z]{2,})$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"maxLength\", \"placeholder\", \"readonly\", \"ngModelChange\"], [\"htmlFor\", \"phone\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"contactPhone\", \"formControlName\", \"contactPhone\", \"pattern\", \"^((\\\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\", 1, \"w-full\", 3, \"ngModel\", \"required\", \"placeholder\", \"readonly\", \"ngModelChange\", \"keydown\"], [\"htmlFor\", \"content\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"pInputText\", \"\", \"id\", \"content\", \"formControlName\", \"content\", 1, \"w-full\", 3, \"ngModel\", \"readonly\", \"placeholder\", \"pTooltip\", \"maxlength\", \"ngModelChange\", \"keydown\"], [1, \"w-full\", \"field\", \"grid\", \"text-error-field\"], [\"htmlFor\", \"content\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [\"htmlFor\", \"note\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"pInputText\", \"\", \"id\", \"note\", \"formControlName\", \"note\", 1, \"w-full\", 3, \"ngModel\", \"readonly\", \"placeholder\", \"pTooltip\", \"maxlength\", \"ngModelChange\", \"keydown\"], [\"htmlFor\", \"note\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"block\", \"flex-wrap\", \"w-full\"], [1, \"w-full\", \"mb-3\"], [1, \"flex\", \"items-center\"], [\"objectKey\", \"activeImsi\", \"paramKey\", \"imsi\", \"keyReturn\", \"imsi\", \"displayPattern\", \"${imsi}\", 2, \"width\", \"70%\", 3, \"value\", \"placeholder\", \"isMultiChoice\", \"paramDefault\", \"disabled\", \"valueChange\"], [\"icon\", \"pi pi-upload\", 3, \"hidden\", \"label\", \"onClick\"], [\"class\", \"block ticket-sim-card-1\", 3, \"class\", 4, \"ngIf\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"align-items-center\", \"mt-3\"], [\"styleClass\", \"mr-2 p-button-secondary\", 3, \"label\", \"hidden\", \"click\"], [\"type\", \"submit\", \"styleClass\", \"p-button-info\", 3, \"disabled\", \"hidden\", \"label\", \"onClick\", 4, \"ngIf\"], [3, \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"grid\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"col-10\", \"align-items-center\", \"justify-content-center\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"pi\", \"pi-times-circle\", 2, \"font-size\", \"2em\", \"color\", \"red\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", 3, \"label\", \"onClick\"], [3, \"visible\", \"header\", \"visibleChange\"], [1, \"w-full\", \"field\", \"grid\", \"mt-4\"], [1, \"col-10\", \"flex\", \"flex-row\", \"justify-content-start\", \"align-items-center\"], [1, \"w-full\", 3, \"fileObject\", \"options\", \"fileObjectChange\"], [1, \"col-2\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [\"icon\", \"pi pi-download\", \"styleClass\", \"p-button-outlined p-button-secondary\", 3, \"pTooltip\", \"click\"], [\"styleClass\", \"p-button-info\", \"icon\", \"\", 3, \"label\", \"click\"], [\"styleClass\", \"w-full\", \"filterBy\", \"display\", \"id\", \"provinceCode\", \"formControlName\", \"provinceCode\", \"optionLabel\", \"display\", \"optionValue\", \"code\", 3, \"showClear\", \"filter\", \"autoDisplayFirst\", \"ngModel\", \"required\", \"options\", \"ngModelChange\"], [\"htmlFor\", \"provinceCode\", 1, \"label-dropdown\"], [\"styleClass\", \"w-full\", \"filterBy\", \"display\", \"id\", \"provinceCode\", \"formControlName\", \"provinceCode\", \"optionLabel\", \"display\", \"optionValue\", \"code\", 3, \"showClear\", \"filter\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"disabled\", \"readonly\", \"ngModelChange\"], [1, \"block\", \"ticket-sim-card-1\"], [1, \"p-grid\", \"p-justify-center\", \"mt-5\"], [1, \"w-full\"], [3, \"value\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [3, \"hidden\"], [\"icon\", \"pi pi-trash\", 3, \"disabled\", \"onClick\"], [\"type\", \"submit\", \"styleClass\", \"p-button-info\", 3, \"disabled\", \"hidden\", \"label\", \"onClick\"]],\n      template: function ListActiveSimTicketComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵtemplate(6, ListActiveSimTicketComponent_p_button_6_Template, 1, 1, \"p-button\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"form\", 6);\n          i0.ɵɵlistener(\"ngSubmit\", function ListActiveSimTicketComponent_Template_form_ngSubmit_7_listener() {\n            return ctx.onSubmitSearch();\n          });\n          i0.ɵɵelementStart(8, \"p-panel\", 7)(9, \"div\", 8);\n          i0.ɵɵtemplate(10, ListActiveSimTicketComponent_div_10_Template, 5, 7, \"div\", 9);\n          i0.ɵɵelementStart(11, \"div\", 10)(12, \"span\", 11)(13, \"input\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function ListActiveSimTicketComponent_Template_input_ngModelChange_13_listener($event) {\n            return ctx.searchInfo.contactName = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"label\", 13);\n          i0.ɵɵtext(15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 10)(17, \"span\", 11)(18, \"input\", 14);\n          i0.ɵɵlistener(\"ngModelChange\", function ListActiveSimTicketComponent_Template_input_ngModelChange_18_listener($event) {\n            return ctx.searchInfo.contactPhone = $event;\n          })(\"keydown\", function ListActiveSimTicketComponent_Template_input_keydown_18_listener($event) {\n            return ctx.preventCharacter($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"label\", 15);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"div\", 10)(22, \"span\", 11)(23, \"p-calendar\", 16);\n          i0.ɵɵlistener(\"ngModelChange\", function ListActiveSimTicketComponent_Template_p_calendar_ngModelChange_23_listener($event) {\n            return ctx.searchInfo.dateFrom = $event;\n          })(\"onSelect\", function ListActiveSimTicketComponent_Template_p_calendar_onSelect_23_listener() {\n            return ctx.onChangeDateFrom(ctx.searchInfo.dateFrom);\n          })(\"onInput\", function ListActiveSimTicketComponent_Template_p_calendar_onInput_23_listener() {\n            return ctx.onChangeDateFrom(ctx.searchInfo.dateFrom);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"label\", 17);\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"div\", 10)(27, \"span\", 11)(28, \"p-calendar\", 18);\n          i0.ɵɵlistener(\"ngModelChange\", function ListActiveSimTicketComponent_Template_p_calendar_ngModelChange_28_listener($event) {\n            return ctx.searchInfo.dateTo = $event;\n          })(\"onSelect\", function ListActiveSimTicketComponent_Template_p_calendar_onSelect_28_listener() {\n            return ctx.onChangeDateTo(ctx.searchInfo.dateTo);\n          })(\"onInput\", function ListActiveSimTicketComponent_Template_p_calendar_onInput_28_listener() {\n            return ctx.onChangeDateTo(ctx.searchInfo.dateTo);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"label\", 19);\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 20);\n          i0.ɵɵelement(32, \"p-button\", 21);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(33, \"table-vnpt\", 22);\n          i0.ɵɵelementStart(34, \"div\", 23)(35, \"p-dialog\", 24);\n          i0.ɵɵlistener(\"visibleChange\", function ListActiveSimTicketComponent_Template_p_dialog_visibleChange_35_listener($event) {\n            return ctx.isShowCreateRequest = $event;\n          })(\"onHide\", function ListActiveSimTicketComponent_Template_p_dialog_onHide_35_listener() {\n            return ctx.isHideUpload();\n          });\n          i0.ɵɵelementStart(36, \"form\", 25);\n          i0.ɵɵlistener(\"ngSubmit\", function ListActiveSimTicketComponent_Template_form_ngSubmit_36_listener() {\n            return ctx.createRequest();\n          });\n          i0.ɵɵelementStart(37, \"div\", 26)(38, \"div\", 27)(39, \"p-card\", 28)(40, \"div\", 29)(41, \"label\", 30)(42, \"b\");\n          i0.ɵɵtext(43);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\");\n          i0.ɵɵtext(45, \":\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(46, ListActiveSimTicketComponent_div_46_Template, 7, 8, \"div\", 31);\n          i0.ɵɵelementStart(47, \"div\", 29)(48, \"label\", 32);\n          i0.ɵɵtext(49);\n          i0.ɵɵelementStart(50, \"span\", 33);\n          i0.ɵɵtext(51, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"div\", 34)(53, \"input\", 35);\n          i0.ɵɵlistener(\"ngModelChange\", function ListActiveSimTicketComponent_Template_input_ngModelChange_53_listener($event) {\n            return ctx.ticket.contactName = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"div\", 29)(55, \"label\", 36);\n          i0.ɵɵtext(56);\n          i0.ɵɵelementStart(57, \"span\", 33);\n          i0.ɵɵtext(58, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(59, \"div\", 34)(60, \"input\", 37);\n          i0.ɵɵlistener(\"ngModelChange\", function ListActiveSimTicketComponent_Template_input_ngModelChange_60_listener($event) {\n            return ctx.ticket.contactEmail = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(61, \"div\", 29)(62, \"label\", 38);\n          i0.ɵɵtext(63);\n          i0.ɵɵelementStart(64, \"span\", 33);\n          i0.ɵɵtext(65, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"div\", 34)(67, \"input\", 39);\n          i0.ɵɵlistener(\"ngModelChange\", function ListActiveSimTicketComponent_Template_input_ngModelChange_67_listener($event) {\n            return ctx.ticket.contactPhone = $event;\n          })(\"keydown\", function ListActiveSimTicketComponent_Template_input_keydown_67_listener($event) {\n            return ctx.preventCharacter($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(68, \"div\", 29)(69, \"label\", 40);\n          i0.ɵɵtext(70);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"div\", 34)(72, \"input\", 41);\n          i0.ɵɵlistener(\"ngModelChange\", function ListActiveSimTicketComponent_Template_input_ngModelChange_72_listener($event) {\n            return ctx.ticket.content = $event;\n          })(\"keydown\", function ListActiveSimTicketComponent_Template_input_keydown_72_listener($event) {\n            return ctx.onKeyDownContent($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(73, \"div\", 42);\n          i0.ɵɵelement(74, \"label\", 43);\n          i0.ɵɵelementStart(75, \"div\", 34);\n          i0.ɵɵtemplate(76, ListActiveSimTicketComponent_small_76_Template, 2, 2, \"small\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(77, ListActiveSimTicketComponent_small_77_Template, 2, 1, \"small\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"div\", 29)(79, \"label\", 45);\n          i0.ɵɵtext(80);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"div\", 34)(82, \"input\", 46);\n          i0.ɵɵlistener(\"ngModelChange\", function ListActiveSimTicketComponent_Template_input_ngModelChange_82_listener($event) {\n            return ctx.ticket.note = $event;\n          })(\"keydown\", function ListActiveSimTicketComponent_Template_input_keydown_82_listener($event) {\n            return ctx.onKeyDownNote($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(83, \"div\", 42);\n          i0.ɵɵelement(84, \"label\", 47);\n          i0.ɵɵelementStart(85, \"div\", 34);\n          i0.ɵɵtemplate(86, ListActiveSimTicketComponent_small_86_Template, 2, 2, \"small\", 44);\n          i0.ɵɵtemplate(87, ListActiveSimTicketComponent_small_87_Template, 2, 1, \"small\", 44);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(88, \"div\", 27)(89, \"div\", 48)(90, \"p-card\", 49)(91, \"div\", 29)(92, \"label\", 30)(93, \"b\");\n          i0.ɵɵtext(94);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"span\", 33);\n          i0.ɵɵtext(96, \"*\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"span\");\n          i0.ɵɵtext(98, \":\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(99, \"div\", 50)(100, \"vnpt-select\", 51);\n          i0.ɵɵlistener(\"valueChange\", function ListActiveSimTicketComponent_Template_vnpt_select_valueChange_100_listener($event) {\n            return ctx.listImsisSelected = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"div\")(102, \"p-button\", 52);\n          i0.ɵɵlistener(\"onClick\", function ListActiveSimTicketComponent_Template_p_button_onClick_102_listener() {\n            return ctx.isShowUpload = true;\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(103, ListActiveSimTicketComponent_div_103_Template, 12, 4, \"div\", 53);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(104, \"div\", 54)(105, \"p-button\", 55);\n          i0.ɵɵlistener(\"click\", function ListActiveSimTicketComponent_Template_p_button_click_105_listener() {\n            ctx.isShowCreateRequest = false;\n            return ctx.isShowUpload = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(106, ListActiveSimTicketComponent_p_button_106_Template, 1, 3, \"p-button\", 56);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(107, \"p-dialog\", 57);\n          i0.ɵɵlistener(\"visibleChange\", function ListActiveSimTicketComponent_Template_p_dialog_visibleChange_107_listener($event) {\n            return ctx.isShowDownload = $event;\n          });\n          i0.ɵɵelementStart(108, \"div\", 58)(109, \"div\", 59);\n          i0.ɵɵelement(110, \"i\", 60)(111, \"br\");\n          i0.ɵɵelementStart(112, \"b\", 61);\n          i0.ɵɵtext(113);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(114, \"p\", 61);\n          i0.ɵɵtext(115);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(116, \"p-button\", 62);\n          i0.ɵɵlistener(\"onClick\", function ListActiveSimTicketComponent_Template_p_button_onClick_116_listener() {\n            return ctx.downloadErrorFile();\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(117, \"p-dialog\", 63);\n          i0.ɵɵlistener(\"visibleChange\", function ListActiveSimTicketComponent_Template_p_dialog_visibleChange_117_listener($event) {\n            return ctx.isShowUpload = $event;\n          });\n          i0.ɵɵelementStart(118, \"div\", 64)(119, \"div\", 65)(120, \"input-file-vnpt\", 66);\n          i0.ɵɵlistener(\"fileObjectChange\", function ListActiveSimTicketComponent_Template_input_file_vnpt_fileObjectChange_120_listener($event) {\n            return ctx.fileObject = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(121, \"div\", 67)(122, \"p-button\", 68);\n          i0.ɵɵlistener(\"click\", function ListActiveSimTicketComponent_Template_p_button_click_122_listener() {\n            return ctx.downloadTemplate();\n          });\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.menu.activeSim\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo.type == ctx.userType.CUSTOMER && ctx.checkAuthen(i0.ɵɵpureFunction1(105, _c1, ctx.CONSTANTS.PERMISSIONS.TICKET.CREATE)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formSearchTicket);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"toggleable\", true)(\"header\", ctx.tranService.translate(\"global.text.filter\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo.type == ctx.userType.ADMIN);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.contactName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.customerName\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.contactPhone);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.phone\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.dateFrom)(\"showIcon\", true)(\"showClear\", true)(\"maxDate\", ctx.maxDateFrom);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.dateFrom\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.dateTo)(\"showIcon\", true)(\"showClear\", true)(\"minDate\", ctx.minDateTo)(\"maxDate\", ctx.maxDateTo);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.dateTo\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"tableId\", \"tableTicketConfigList\")(\"fieldId\", \"provinceCode\")(\"columns\", ctx.columns)(\"dataSet\", ctx.dataSet)(\"options\", ctx.optionTable)(\"pageNumber\", ctx.pageNumber)(\"loadData\", ctx.search.bind(ctx))(\"pageSize\", ctx.pageSize)(\"sort\", ctx.sort)(\"params\", ctx.searchInfo)(\"labelTable\", ctx.tranService.translate(\"ticket.menu.requestList\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(107, _c2));\n          i0.ɵɵproperty(\"header\", ctx.typeRequest == \"view\" ? ctx.tranService.translate(\"ticket.label.viewActiveSim\") : ctx.tranService.translate(\"ticket.label.requestActiveSim\"))(\"visible\", ctx.isShowCreateRequest)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formActiveSim);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.generalInfo\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo.type == ctx.userType.ADMIN && ctx.typeRequest == \"view\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.customerName\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.ticket.contactName)(\"required\", true)(\"maxLength\", 50)(\"placeholder\", ctx.tranService.translate(\"account.text.inputFullname\"))(\"readonly\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.email\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.ticket.contactEmail)(\"required\", true)(\"maxLength\", 50)(\"placeholder\", ctx.tranService.translate(\"account.text.inputEmail\"))(\"readonly\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.phone\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.ticket.contactPhone)(\"required\", true)(\"placeholder\", ctx.tranService.translate(\"account.text.inputPhone\"))(\"readonly\", true);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.content\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.ticket.content)(\"readonly\", ctx.typeRequest == \"view\" || ctx.isShowUpload)(\"placeholder\", ctx.tranService.translate(\"ticket.label.content\"))(\"pTooltip\", ctx.ticket.content)(\"maxlength\", 255);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formActiveSim.controls.content.errors == null ? null : ctx.formActiveSim.controls.content.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formActiveSim.controls.content.errors == null ? null : ctx.formActiveSim.controls.content.errors.pattern);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.note\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.ticket.note)(\"readonly\", ctx.typeRequest == \"view\" || ctx.isShowUpload)(\"placeholder\", ctx.tranService.translate(\"ticket.label.note\"))(\"pTooltip\", ctx.ticket.note)(\"maxlength\", 255);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formActiveSim.controls.note.errors == null ? null : ctx.formActiveSim.controls.note.errors.maxLength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formActiveSim.controls.note.errors == null ? null : ctx.formActiveSim.controls.note.errors.pattern);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassMap(ctx.typeRequest == \"view\" ? \"hidden\" : \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.label.enterImsi\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"value\", ctx.listImsisSelected)(\"placeholder\", ctx.tranService.translate(\"ticket.label.listImsi\"))(\"isMultiChoice\", true)(\"paramDefault\", i0.ɵɵpureFunction0(108, _c3))(\"disabled\", ctx.isShowUpload);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"hidden\", ctx.typeRequest == \"view\" ? \"hidden\" : \"\")(\"label\", ctx.tranService.translate(\"ticket.message.uploadFile\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.listImsisSelected.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.cancel\"))(\"hidden\", ctx.typeRequest == \"view\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkAuthen(i0.ɵɵpureFunction1(109, _c1, ctx.CONSTANTS.PERMISSIONS.TICKET.CREATE)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(111, _c4));\n          i0.ɵɵproperty(\"visible\", ctx.isShowDownload)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\"\", ctx.tranService.translate(\"ticket.message.isError\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"ticket.message.isDownloadMessage\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"ticket.message.downloadFile\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"visible\", ctx.isShowUpload)(\"header\", ctx.tranService.translate(\"ticket.label.imsiByFile\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"fileObject\", ctx.fileObject)(\"options\", ctx.optionInputFile);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"pTooltip\", ctx.tranService.translate(\"global.button.downloadTemp\"));\n        }\n      },\n      dependencies: [i2.NgIf, i3.Breadcrumb, i4.Tooltip, i5.PrimeTemplate, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.MaxLengthValidator, i1.PatternValidator, i1.MinValidator, i1.FormGroupDirective, i1.FormControlName, i6.InputText, i7.Button, i8.TableVnptComponent, i9.InputFileVnptComponent, i10.VnptCombobox, i11.Calendar, i12.Dropdown, i13.Card, i14.Dialog, i15.Panel, i16.Table],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["TicketService", "CONSTANTS", "ComponentBase", "AccountService", "InputFileVnptComponent", "XLSX", "SimTicketService", "i0", "ɵɵelementStart", "ɵɵlistener", "ListActiveSimTicketComponent_p_button_6_Template_p_button_click_0_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "showModalCreate", "ɵɵelementEnd", "ɵɵproperty", "ctx_r0", "tranService", "translate", "ListActiveSimTicketComponent_div_10_Template_p_dropdown_ngModelChange_2_listener", "$event", "_r12", "ctx_r11", "searchInfo", "provinceCode", "ɵɵtext", "ɵɵadvance", "ctx_r1", "listProvince", "ɵɵtextInterpolate", "ListActiveSimTicketComponent_div_46_Template_p_dropdown_ngModelChange_6_listener", "_r14", "ctx_r13", "ticket", "ctx_r2", "ctx_r3", "ɵɵpureFunction0", "_c0", "ctx_r4", "ctx_r5", "ctx_r6", "ctx_r15", "typeRequest", "ListActiveSimTicketComponent_div_103_ng_template_11_Template_p_button_onClick_6_listener", "restoredCtx", "_r20", "i_r18", "rowIndex", "ctx_r19", "removeImsi", "item_r17", "ctx_r16", "isShowUpload", "ɵɵtemplate", "ListActiveSimTicketComponent_div_103_ng_template_10_Template", "ListActiveSimTicketComponent_div_103_ng_template_11_Template", "ɵɵclassMap", "ctx_r7", "listImsisSelected", "ListActiveSimTicketComponent_p_button_106_Template_p_button_onClick_0_listener", "_r22", "ctx_r21", "isShowCreateRequest", "ctx_r8", "formActiveSim", "invalid", "length", "ListActiveSimTicketComponent", "constructor", "ticketService", "accountService", "simTicketService", "cdr", "formBuilder", "injector", "maxDateFrom", "Date", "minDateTo", "maxDateTo", "listImsis", "errorRecords", "ngOnInit", "me", "userInfo", "sessionService", "isShowDownload", "userType", "USER_TYPE", "isValidActiveSim", "id", "contactName", "contactEmail", "contactPhone", "content", "note", "cause", "type", "REQUEST_TYPE", "ACTIVE_SIM", "status", "statusOld", "assigneeId", "fileName", "listTicketType", "label", "value", "mapTicketStatus", "listTicketStatus", "email", "dateFrom", "dateTo", "columns", "name", "key", "size", "align", "isShow", "ADMIN", "isSort", "isShowTooltip", "style", "display", "max<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "funcConvertText", "utilService", "convertDateToString", "optionTable", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "action", "icon", "tooltip", "func", "item", "handleRequest", "pageNumber", "pageSize", "sort", "dataSet", "total", "formSearchTicket", "group", "optionInputFile", "messageErrorType", "maxSize", "unit", "required", "isShowButtonUpload", "actionUpload", "uploadFile", "bind", "disabled", "getListProvince", "search", "page", "limit", "params", "dataParams", "Object", "keys", "for<PERSON>ach", "getTime", "searchTicket", "response", "totalElements", "messageCommonService", "offload", "resetTicket", "onSubmitSearch", "map", "el", "code", "createRequest", "console", "log", "onload", "bodySend", "createTicket", "resp", "createSimTicketBody", "ticketId", "userCustomerId", "created<PERSON>y", "userHandleId", "imsis", "create", "res", "success", "getDetailTicketConfig", "resp1", "array", "info", "emailInfos", "push", "userId", "sendMailNotify", "CUSTOMER", "fullName", "phone", "reset", "getDetailTicket", "imsi", "listEmail", "preventCharacter", "event", "ctrl<PERSON>ey", "keyCode", "preventDefault", "downloadTemplate", "file", "removeFileExtension", "warning", "reader", "FileReader", "e", "binaryStr", "target", "result", "workbook", "read", "sheetName", "SheetNames", "worksheet", "Sheets", "jsonData", "utils", "sheet_to_json", "header", "headers", "data", "slice", "filter", "row", "some", "cell", "undefined", "validateFile", "readAsBinaryString", "inputFileComponent", "resetFile", "requiredHeaders", "extraColumns", "missingColumns", "i", "error", "filteredRecords", "record", "toString", "trim", "index", "errors", "test", "join", "Number", "listImsi", "ticketType", "ORDER_SIM", "found", "SIM_TICKET_STATUS", "ACTIVATED", "includes", "downloadErrorFile", "ws", "aoa_to_sheet", "wb", "book_new", "book_append_sheet", "wbout", "write", "bookType", "buf", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "view", "Uint8Array", "charCodeAt", "blob", "Blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "toISOString", "replace", "substring", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "onChangeDateFrom", "onChangeDateTo", "splice", "isHideUpload", "lastDotIndex", "lastIndexOf", "onKeyDownNote", "trimStart", "onKeyDownContent", "onKeyDownNoteContent", "onKeyDownCause", "validateInput", "invalid<PERSON>hars", "inputField", "char", "split", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "FormBuilder", "Injector", "selectors", "viewQuery", "ListActiveSimTicketComponent_Query", "rf", "ctx", "ɵɵelement", "ListActiveSimTicketComponent_p_button_6_Template", "ListActiveSimTicketComponent_Template_form_ngSubmit_7_listener", "ListActiveSimTicketComponent_div_10_Template", "ListActiveSimTicketComponent_Template_input_ngModelChange_13_listener", "ListActiveSimTicketComponent_Template_input_ngModelChange_18_listener", "ListActiveSimTicketComponent_Template_input_keydown_18_listener", "ListActiveSimTicketComponent_Template_p_calendar_ngModelChange_23_listener", "ListActiveSimTicketComponent_Template_p_calendar_onSelect_23_listener", "ListActiveSimTicketComponent_Template_p_calendar_onInput_23_listener", "ListActiveSimTicketComponent_Template_p_calendar_ngModelChange_28_listener", "ListActiveSimTicketComponent_Template_p_calendar_onSelect_28_listener", "ListActiveSimTicketComponent_Template_p_calendar_onInput_28_listener", "ListActiveSimTicketComponent_Template_p_dialog_visibleChange_35_listener", "ListActiveSimTicketComponent_Template_p_dialog_onHide_35_listener", "ListActiveSimTicketComponent_Template_form_ngSubmit_36_listener", "ListActiveSimTicketComponent_div_46_Template", "ListActiveSimTicketComponent_Template_input_ngModelChange_53_listener", "ListActiveSimTicketComponent_Template_input_ngModelChange_60_listener", "ListActiveSimTicketComponent_Template_input_ngModelChange_67_listener", "ListActiveSimTicketComponent_Template_input_keydown_67_listener", "ListActiveSimTicketComponent_Template_input_ngModelChange_72_listener", "ListActiveSimTicketComponent_Template_input_keydown_72_listener", "ListActiveSimTicketComponent_small_76_Template", "ListActiveSimTicketComponent_small_77_Template", "ListActiveSimTicketComponent_Template_input_ngModelChange_82_listener", "ListActiveSimTicketComponent_Template_input_keydown_82_listener", "ListActiveSimTicketComponent_small_86_Template", "ListActiveSimTicketComponent_small_87_Template", "ListActiveSimTicketComponent_Template_vnpt_select_valueChange_100_listener", "ListActiveSimTicketComponent_Template_p_button_onClick_102_listener", "ListActiveSimTicketComponent_div_103_Template", "ListActiveSimTicketComponent_Template_p_button_click_105_listener", "ListActiveSimTicketComponent_p_button_106_Template", "ListActiveSimTicketComponent_Template_p_dialog_visibleChange_107_listener", "ListActiveSimTicketComponent_Template_p_button_onClick_116_listener", "ListActiveSimTicketComponent_Template_p_dialog_visibleChange_117_listener", "ListActiveSimTicketComponent_Template_input_file_vnpt_fileObjectChange_120_listener", "fileObject", "ListActiveSimTicketComponent_Template_p_button_click_122_listener", "items", "home", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵpureFunction1", "_c1", "PERMISSIONS", "TICKET", "CREATE", "ɵɵstyleMap", "_c2", "controls", "max<PERSON><PERSON><PERSON>", "pattern", "_c3", "_c4", "ɵɵtextInterpolate1"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\ticket\\list\\active-sim\\app.list.active-sim.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\ticket\\list\\active-sim\\app.list.active-sim.component.html"], "sourcesContent": ["import {ChangeDetectorRef, Component, Inject, Injector, OnInit, ViewChild} from \"@angular/core\";\r\nimport {MenuItem} from \"primeng/api\";\r\nimport {TicketService} from \"src/app/service/ticket/TicketService\";\r\nimport {ColumnInfo, OptionTable} from \"../../../common-module/table/table.component\";\r\nimport {CONSTANTS} from \"src/app/service/comon/constants\";\r\nimport {ComponentBase} from \"src/app/component.base\";\r\nimport {AccountService} from \"../../../../service/account/AccountService\";\r\nimport {FormBuilder} from \"@angular/forms\";\r\nimport {InputFileVnptComponent, OptionInputFile} from \"../../../common-module/input-file/input.file.component\";\r\nimport * as XLSX from 'xlsx';\r\nimport {elementAt} from \"rxjs\";\r\nimport {SimTicketService} from \"../../../../service/ticket/SimTicketService\";\r\nimport {da} from \"suneditor/src/lang\";\r\n\r\n@Component({\r\n    selector: \"list-active-sim-ticket\",\r\n    templateUrl: './app.list.active-sim.component.html'\r\n})\r\nexport class ListActiveSimTicketComponent extends ComponentBase implements OnInit {\r\n    @ViewChild(InputFileVnptComponent) inputFileComponent: InputFileVnptComponent;\r\n\r\n    items: MenuItem[];\r\n    home: MenuItem\r\n    searchInfo: {\r\n        provinceCode: string | null,\r\n        email: string | null,\r\n        contactPhone: string | null,\r\n        contactEmail: string | null,\r\n        type: number | null,\r\n        status: number | null,\r\n        dateFrom: Date | null,\r\n        dateTo: Date | null,\r\n        contactName: string | null,\r\n    };\r\n    maxDateFrom: Date | number | string | null = new Date();\r\n    minDateTo: Date | number | string | null = null;\r\n    maxDateTo: Date | number | string | null = new Date();\r\n    columns: Array<ColumnInfo>;\r\n    listImsis = [];\r\n    listImsisSelected = []\r\n    dataSet: {\r\n        content: Array<any>,\r\n        total: number\r\n    };\r\n    selectItems: Array<any>;\r\n    optionTable: OptionTable;\r\n    pageNumber: number;\r\n    pageSize: number;\r\n    sort: string;\r\n    formSearchTicket: any;\r\n    listProvince: Array<any>;\r\n    listTicketType: Array<any>;\r\n    listTicketStatus: Array<any>;\r\n    mapTicketStatus: any;\r\n    listEmail: Array<any>;\r\n    isShowCreateRequest: boolean;\r\n    isShowDownload: boolean;\r\n    isShowUpload: boolean\r\n    formActiveSim: any;\r\n    ticket: {\r\n        id: number\r\n        contactName: string | null,\r\n        contactEmail: string | null,\r\n        contactPhone: string | null,\r\n        content: string | null,\r\n        note: string | null,\r\n        cause: string | null,\r\n        type: number | null, // 0: thay thế sim, 1: test sim\r\n        status: number | null,\r\n        statusOld?: number | null,\r\n        assigneeId: number | null,\r\n        provinceCode: string | null,\r\n    };\r\n    typeRequest: string\r\n    userInfo: any\r\n    userType: any\r\n    isValidActiveSim: boolean\r\n    fileObject: any;\r\n    fileName: string | null;\r\n    errorRecords: any[] = [];\r\n    optionInputFile: OptionInputFile;\r\n\r\n    constructor(\r\n        @Inject(TicketService) private ticketService: TicketService,\r\n        @Inject(AccountService) private accountService: AccountService,\r\n        @Inject(SimTicketService) private simTicketService: SimTicketService,\r\n        private cdr: ChangeDetectorRef,\r\n        private formBuilder: FormBuilder,\r\n        private injector: Injector) {\r\n        super(injector);\r\n    }\r\n\r\n    ngOnInit() {\r\n        let me = this;\r\n        this.userInfo = this.sessionService.userInfo;\r\n        this.isShowCreateRequest = false;\r\n        this.isShowDownload = false;\r\n        this.isShowUpload = false;\r\n        this.typeRequest = 'create'\r\n        this.userType = CONSTANTS.USER_TYPE;\r\n        this.isValidActiveSim = true;\r\n        this.ticket = {\r\n            id: null,\r\n            contactName: null,\r\n            contactEmail: null,\r\n            contactPhone: null,\r\n            content: null,\r\n            note: null,\r\n            cause: null,\r\n            type: CONSTANTS.REQUEST_TYPE.ACTIVE_SIM, // 0: thay thế sim, 1: test sim\r\n            status: null,\r\n            statusOld: null,\r\n            assigneeId: null,\r\n            provinceCode: null,\r\n        };\r\n        this.fileName = \"\";\r\n        this.listTicketType = [\r\n            {\r\n                label: this.tranService.translate('ticket.type.activeSim'),\r\n                value: 1\r\n            }\r\n        ]\r\n        this.mapTicketStatus = {\r\n            0: [{\r\n                label: me.tranService.translate('ticket.status.received'),\r\n                value: 1\r\n            }],\r\n            1: [\r\n                {\r\n                    label: me.tranService.translate('ticket.status.inProgress'),\r\n                    value: 2\r\n                },\r\n                {\r\n                    label: me.tranService.translate('ticket.status.reject'),\r\n                    value: 3\r\n                }\r\n            ],\r\n            2: [\r\n                {\r\n                    label: me.tranService.translate('ticket.status.done'),\r\n                    value: 4\r\n                }\r\n            ]\r\n        }\r\n        this.listTicketStatus = [\r\n            {\r\n                label: me.tranService.translate('ticket.status.new'),\r\n                value: 0\r\n            },\r\n            {\r\n                label: me.tranService.translate('ticket.status.received'),\r\n                value: 1\r\n            },\r\n            {\r\n                label: me.tranService.translate('ticket.status.inProgress'),\r\n                value: 2\r\n            },\r\n            {\r\n                label: me.tranService.translate('ticket.status.reject'),\r\n                value: 3\r\n            },\r\n            {\r\n                label: me.tranService.translate('ticket.status.done'),\r\n                value: 4\r\n            }\r\n        ]\r\n        this.searchInfo = {\r\n            provinceCode: null,\r\n            email: null,\r\n            contactPhone: null,\r\n            contactEmail: null,\r\n            type: CONSTANTS.REQUEST_TYPE.ACTIVE_SIM,\r\n            status: null,\r\n            dateFrom: null,\r\n            dateTo: null,\r\n            contactName: null\r\n        }\r\n        this.columns = [\r\n            {\r\n                name: this.tranService.translate(\"ticket.label.province\"),\r\n                key: \"provinceName\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: this.userInfo.type == CONSTANTS.USER_TYPE.ADMIN,\r\n                isSort: true\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ticket.label.customerName\"),\r\n                key: \"contactName\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                isShowTooltip: true,\r\n                style: {\r\n                    display: 'inline-block',\r\n                    maxWidth: '350px',\r\n                    overflow: 'hidden',\r\n                    textOverflow: 'ellipsis'\r\n                }\r\n            }, {\r\n                name: this.tranService.translate(\"ticket.label.email\"),\r\n                key: \"contactEmail\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                isShowTooltip: true,\r\n                style: {\r\n                    display: 'inline-block',\r\n                    maxWidth: '350px',\r\n                    overflow: 'hidden',\r\n                    textOverflow: 'ellipsis'\r\n                }\r\n            }, {\r\n                name: this.tranService.translate(\"ticket.label.phone\"),\r\n                key: \"contactPhone\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true\r\n            }, {\r\n                name: this.tranService.translate(\"ticket.label.content\"),\r\n                key: \"content\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                isShowTooltip: true,\r\n                style: {\r\n                    display: 'inline-block',\r\n                    maxWidth: '350px',\r\n                    overflow: 'hidden',\r\n                    textOverflow: 'ellipsis'\r\n                }\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"ticket.label.createdDate\"),\r\n                key: \"createdDate\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcConvertText(value) {\r\n                    if (value == null) return null;\r\n                    return me.utilService.convertDateToString(new Date(value))\r\n                },\r\n            },\r\n            // {\r\n            //     name: this.tranService.translate(\"ticket.label.updatedDate\"),\r\n            //     key: \"updatedDate\",\r\n            //     size: \"fit-content\",\r\n            //     align: \"left\",\r\n            //     isShow: true,\r\n            //     isSort: true,\r\n            //     funcConvertText(value) {\r\n            //         if (value == null) return null;\r\n            //         return me.utilService.convertDateToString(new Date(value))\r\n            //     },\r\n            // },\r\n            // {\r\n            //     name: this.tranService.translate(\"ticket.label.updateBy\"),\r\n            //     key: \"updatedByName\",\r\n            //     size: \"fit-content\",\r\n            //     align: \"left\",\r\n            //     isShow: true,\r\n            //     isSort: true\r\n            // },\r\n            // {\r\n            //     name: this.tranService.translate(\"ticket.label.status\"),\r\n            //     key: \"status\",\r\n            //     size: \"fit-content\",\r\n            //     align: \"left\",\r\n            //     isShow: true,\r\n            //     isSort: true,\r\n            //     funcGetClassname: (value) => {\r\n            //         if (value == CONSTANTS.REQUEST_STATUS.NEW) {\r\n            //             return ['p-2', 'text-white', \"bg-cyan-300\", \"border-round\", \"inline-block\"];\r\n            //         } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\r\n            //             return ['p-2', 'text-white', \"bg-bluegray-500\", \"border-round\", \"inline-block\"];\r\n            //         } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\r\n            //             return ['p-2', 'text-white', \"bg-orange-400\", \"border-round\", \"inline-block\"];\r\n            //         } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\r\n            //             return ['p-2', 'text-white', \"bg-red-500\", \"border-round\", \"inline-block\"];\r\n            //         } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\r\n            //             return ['p-2', 'text-white', \"bg-green-500\", \"border-round\", \"inline-block\"];\r\n            //         }\r\n            //         return '';\r\n            //     },\r\n            //     funcConvertText: function (value) {\r\n            //         if (value == CONSTANTS.REQUEST_STATUS.NEW) {\r\n            //             return me.tranService.translate(\"ticket.status.new\");\r\n            //         } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {\r\n            //             return me.tranService.translate(\"ticket.status.received\");\r\n            //         } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {\r\n            //             return me.tranService.translate(\"ticket.status.inProgress\");\r\n            //         } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {\r\n            //             return me.tranService.translate(\"ticket.status.reject\");\r\n            //         } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {\r\n            //             return me.tranService.translate(\"ticket.status.done\");\r\n            //         }\r\n            //         return \"\";\r\n            //     }\r\n            // }\r\n        ];\r\n\r\n        this.optionTable = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n            action: [\r\n                {\r\n                    icon: \"pi pi-info-circle\",\r\n                    tooltip: this.tranService.translate(\"global.button.view\"),\r\n                    func: function (id, item) {\r\n                        me.handleRequest(id, item, 'view')\r\n                    },\r\n                },]\r\n        }\r\n        this.pageNumber = 0;\r\n        this.pageSize = 10;\r\n        this.sort = \"createdDate,desc\"\r\n        this.dataSet = {\r\n            content: [],\r\n            total: 0\r\n        }\r\n        this.formSearchTicket = this.formBuilder.group(this.searchInfo);\r\n        this.formActiveSim = this.formBuilder.group(this.ticket);\r\n        this.optionInputFile = {\r\n            type: ['xls', 'xlsx'],\r\n            messageErrorType: this.tranService.translate(\"global.message.wrongFileExcel\"),\r\n            maxSize: 100,\r\n            unit: \"MB\",\r\n            required: false,\r\n            isShowButtonUpload: true,\r\n            actionUpload: this.uploadFile.bind(this),\r\n            disabled: false\r\n        }\r\n        this.getListProvince();\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n    }\r\n\r\n    search(page, limit, sort, params) {\r\n        let me = this;\r\n        this.pageNumber = page;\r\n        this.pageSize = limit;\r\n        this.sort = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        Object.keys(this.searchInfo).forEach(key => {\r\n            if (this.searchInfo[key] != null) {\r\n                if (key == \"dateFrom\") {\r\n                    dataParams[\"dateFrom\"] = this.searchInfo.dateFrom.getTime();\r\n                } else if (key == \"dateTo\") {\r\n                    dataParams[\"dateTo\"] = this.searchInfo.dateTo.getTime();\r\n                } else {\r\n                    dataParams[key] = this.searchInfo[key];\r\n                }\r\n            }\r\n        })\r\n        this.dataSet = {\r\n            content: [],\r\n            total: 0\r\n        }\r\n        // me.messageCommonService.onload();\r\n        this.ticketService.searchTicket(dataParams, (response) => {\r\n            me.dataSet = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n        }, null, () => {\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    resetTicket() {\r\n        this.ticket = {\r\n            id: null,\r\n            contactName: null,\r\n            contactEmail: null,\r\n            contactPhone: null,\r\n            content: null,\r\n            note: null,\r\n            cause: null,\r\n            type: CONSTANTS.REQUEST_TYPE.ACTIVE_SIM, // 0: thay thế sim, 1: test sim\r\n            status: null,\r\n            statusOld: null,\r\n            assigneeId: null,\r\n            provinceCode: null,\r\n        };\r\n        this.listImsisSelected = [];\r\n        this.errorRecords = [];\r\n        this.isShowDownload = false;\r\n    }\r\n\r\n    onSubmitSearch() {\r\n        this.pageNumber = 0;\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n    }\r\n\r\n    getListProvince() {\r\n        this.accountService.getListProvince((response) => {\r\n            this.listProvince = response.map(el => {\r\n                return {\r\n                    ...el,\r\n                    display: `${el.code} - ${el.name}`\r\n                }\r\n            })\r\n        })\r\n    }\r\n\r\n    // tạo sửa yêu cầu\r\n    createRequest() {\r\n        let me = this;\r\n        console.log(\"create\")\r\n        me.messageCommonService.onload()\r\n        let bodySend = {\r\n            contactName: this.ticket.contactName,\r\n            contactEmail: this.ticket.contactEmail,\r\n            contactPhone: this.ticket.contactPhone,\r\n            content: this.ticket.content,\r\n            note: this.ticket.note,\r\n            type: this.ticket.type,\r\n        }\r\n        this.ticketService.createTicket(bodySend, (resp) => {\r\n            // console.log(resp)\r\n            let createSimTicketBody = {\r\n                ticketId: resp.id,\r\n                userCustomerId: resp.createdBy,\r\n                userHandleId: resp.assigneeId,\r\n                imsis: me.listImsisSelected,\r\n            }\r\n            me.simTicketService.create(createSimTicketBody, (res) => {\r\n                console.log(res)\r\n                me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n                me.isShowCreateRequest = false\r\n                me.messageCommonService.offload();\r\n                me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)\r\n            })\r\n            // get mail admin tinh\r\n            // me.ticketService.getListAssignee({email : '', provinceCode : this.userInfo.provinceCode, page : 0, size: 99999999}, (respAssignee)=>{\r\n            //     let listProvinceConfig = respAssignee.content;\r\n            //     let array = []\r\n            //     for (let user of listProvinceConfig) {\r\n            //         array.push({\r\n            //             userId: user.id,\r\n            //             ticketId: resp.id\r\n            //         })\r\n            //     }\r\n            //     me.ticketService.sendMailNotify(array);\r\n            // })\r\n            me.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp1) => {\r\n                let array = []\r\n                for (let info of resp1.emailInfos) {\r\n                    array.push({\r\n                        userId: info.userId,\r\n                        ticketId: resp.id\r\n                    })\r\n                }\r\n                if (resp?.assigneeId) {\r\n                    array.push({\r\n                        userId: resp.assigneeId,\r\n                        ticketId: resp.id\r\n                    })\r\n                }\r\n                me.ticketService.sendMailNotify(array);\r\n            })\r\n        }, null, () => {\r\n            me.messageCommonService.offload()\r\n        })\r\n    }\r\n\r\n    showModalCreate() {\r\n        this.isShowCreateRequest = true\r\n        this.typeRequest = 'create'\r\n        this.resetTicket()\r\n        // auto fill thong tin khi tao\r\n        if (this.userInfo.type === CONSTANTS.USER_TYPE.CUSTOMER) {\r\n            this.ticket.contactName = this.userInfo.fullName;\r\n            this.ticket.contactPhone = this.userInfo.phone;\r\n            this.ticket.contactEmail = this.userInfo.email;\r\n        }\r\n        this.formActiveSim = this.formBuilder.group(this.ticket)\r\n    }\r\n\r\n    handleRequest(id, item, typeRequest: string) {\r\n        if (typeRequest == 'view') {\r\n            this.typeRequest = typeRequest\r\n            this.isShowCreateRequest = true;\r\n        }\r\n        let me = this\r\n        this.resetTicket()\r\n        this.formActiveSim.reset()\r\n        this.ticketService.getDetailTicket(item.id, (resp) => {\r\n            this.ticket = {\r\n                id: resp.id,\r\n                contactName: resp.contactName,\r\n                contactEmail: resp.contactEmail,\r\n                contactPhone: resp.contactPhone,\r\n                content: resp.content,\r\n                note: resp.note,\r\n                cause: resp.cause,\r\n                type: resp.type, // 0: thay thế sim, 1: test sim, 2: order sim, 3: active sim\r\n                status: null,\r\n                statusOld: resp.status,\r\n                assigneeId: resp.assigneeId,\r\n                provinceCode: resp.provinceCode,\r\n            }\r\n            this.simTicketService.search({ticketId: this.ticket.id, size: 1000}, (res) => {\r\n                    let imsis: number[] = [];\r\n                    res.content.forEach(item => {\r\n                        imsis.push(item.imsi);\r\n                    })\r\n                    this.listImsisSelected = imsis\r\n\r\n                }\r\n            )\r\n        })\r\n\r\n        this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp) => {\r\n            this.listEmail = resp.emailInfos;\r\n        })\r\n    }\r\n\r\n    preventCharacter(event) {\r\n        if (event.ctrlKey) {\r\n            return;\r\n        }\r\n        if (event.keyCode == 8 || event.keyCode == 13 || event.keyCode == 37 || event.keyCode == 39) {\r\n            return;\r\n        }\r\n        if (event.keyCode < 48 || event.keyCode > 57) {\r\n            event.preventDefault();\r\n        }\r\n        // Chặn ký tự 'e', 'E' và dấu '+'\r\n        if (event.keyCode == 69 || event.keyCode == 101 || event.keyCode == 107 || event.keyCode == 187) {\r\n            event.preventDefault();\r\n        }\r\n    }\r\n\r\n    downloadTemplate() {\r\n        this.ticketService.downloadTemplate();\r\n    }\r\n\r\n    uploadFile(file: any) {\r\n        let me = this;\r\n        me.isShowUpload = false;\r\n        this.fileName = this.removeFileExtension(file.name);\r\n        me.messageCommonService.onload();\r\n        if (file.size > 1024 * 1024) { // 1MB\r\n            me.messageCommonService.offload();\r\n            me.messageCommonService.warning(me.tranService.translate(\"ticket.message.largeFile\"));\r\n            return;\r\n        }\r\n        if (file) {\r\n            // me.listImsisSelected = [];\r\n            me.errorRecords = [];\r\n            me.listImsis = [];\r\n            const reader = new FileReader();\r\n            reader.onload = (e: any) => {\r\n                const binaryStr = e.target.result;\r\n                const workbook = XLSX.read(binaryStr, {type: 'binary'});\r\n                const sheetName = workbook.SheetNames[0];\r\n                const worksheet = workbook.Sheets[sheetName];\r\n                const jsonData = XLSX.utils.sheet_to_json(worksheet, {header: 1});\r\n\r\n                // Bỏ qua dòng trống và chỉ lấy dòng có dữ liệu\r\n                let headers = jsonData[0];\r\n                let data = jsonData.slice(1).filter((row: any) => row.some((cell: any) => cell !== null && cell !== undefined && cell !== ''));\r\n                this.validateFile(headers, data);\r\n            };\r\n            reader.readAsBinaryString(file);\r\n        }\r\n    }\r\n\r\n    validateFile(headers, data: any[]) {\r\n        let me = this;\r\n        me.errorRecords = [];\r\n        me.inputFileComponent.resetFile();\r\n        const requiredHeaders = ['STT', 'IMSI (Bắt buộc)'];\r\n        const extraColumns = headers.length > requiredHeaders.length;\r\n        const missingColumns = headers.length < requiredHeaders.length;\r\n\r\n        for (let i = 0; i < requiredHeaders.length; i++) {\r\n            if (headers[i] !== requiredHeaders[i]) {\r\n                me.messageCommonService.offload()\r\n                me.messageCommonService.error(me.tranService.translate('ticket.message.wrongSample'));\r\n                return;\r\n            }\r\n        }\r\n\r\n        if (extraColumns || missingColumns) {\r\n            if (extraColumns) {\r\n                me.messageCommonService.error(me.tranService.translate('ticket.message.redundantColumns'))\r\n            }\r\n            if (missingColumns) {\r\n                me.messageCommonService.error(me.tranService.translate('ticket.message.missingColumns'))\r\n            }\r\n            me.messageCommonService.offload()\r\n            return;\r\n        }\r\n\r\n        if (data.length === 0) {\r\n            me.messageCommonService.offload()\r\n            me.messageCommonService.error(me.tranService.translate('ticket.message.emptyFile'));\r\n            return;\r\n        }\r\n\r\n        const filteredRecords = data.filter(record =>\r\n            record.length === requiredHeaders.length &&\r\n            (record[0] !== undefined && record[0].toString().trim() !== '' || record[1].trim() !== '')\r\n        );\r\n\r\n        if (filteredRecords.length === 0) {\r\n            me.messageCommonService.offload()\r\n            me.messageCommonService.error(me.tranService.translate('ticket.message.emptyFile'));\r\n            return;\r\n        }\r\n\r\n        data.forEach((record, index) => {\r\n            let errors = [];\r\n            if (!record[1] || record[1].trim() === '') {\r\n                errors.push(me.tranService.translate('ticket.message.missingImsiInfo'));\r\n            } else if (typeof record[1] !== 'string' || !/^\\d+$/.test(record[1]) || record[1].length > 18) {\r\n                errors.push(me.tranService.translate('ticket.message.wrongImsiFormat'));\r\n            } else {\r\n                this.listImsis.push(record[1])\r\n            }\r\n            if (errors.length > 0) {\r\n                this.errorRecords.push([...record, errors.join(', ')]);\r\n            }\r\n        });\r\n\r\n        this.listImsis = this.listImsis.map(imsi => Number(imsi.trim()))\r\n\r\n        if (this.listImsis.length > 0) {\r\n            me.simTicketService.search({\r\n                listImsi: me.listImsis.join(','),\r\n                ticketType: CONSTANTS.REQUEST_TYPE.ORDER_SIM\r\n            }, (res) => {\r\n                let index = 0;\r\n                for (const imsi of me.listImsis) {\r\n                    let found = false;\r\n                    for (const item of res.content) {\r\n                        if (imsi === item.imsi) {\r\n                            if (item.status == CONSTANTS.SIM_TICKET_STATUS.ACTIVATED) {\r\n                                me.errorRecords.push(['0', imsi + '', me.tranService.translate('ticket.message.imsiIsActivated')])\r\n                            } else {\r\n                                if (me.listImsisSelected.includes(imsi)) {\r\n                                    me.errorRecords.push(['0', imsi + '', me.tranService.translate(\"ticket.message.imsiIsExist\")])\r\n                                } else {\r\n                                    me.listImsisSelected.push(imsi);\r\n                                }\r\n                            }\r\n                            found = true;\r\n                            break;\r\n                        }\r\n                    }\r\n                    // Nếu không tìm thấy IMSI trong\r\n                    if (!found) {\r\n                        me.errorRecords.push(['0', imsi + '', me.tranService.translate(\"ticket.message.imsiNotExist\")])\r\n                    }\r\n                }\r\n                me.messageCommonService.offload();\r\n                if (this.errorRecords.length > 0) {\r\n                    me.isShowDownload = true;\r\n                } else {\r\n                    me.messageCommonService.success((me.tranService.translate(\"global.message.saveSuccess\")))\r\n                }\r\n            })\r\n        } else {\r\n            me.messageCommonService.offload();\r\n            if (this.errorRecords.length > 0) {\r\n                me.isShowDownload = true;\r\n            } else {\r\n                me.messageCommonService.success((me.tranService.translate(\"global.message.saveSuccess\")))\r\n            }\r\n        }\r\n\r\n    }\r\n\r\n    downloadErrorFile() {\r\n        let me = this;\r\n        for (let i = 0; i < me.errorRecords.length; i++) {\r\n            me.errorRecords[i][0] = (i + 1).toString();\r\n        }\r\n        console.log(this.errorRecords)\r\n        const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet([['STT', 'IMSI', 'Nội dung lỗi'], ...this.errorRecords]);\r\n        const wb: XLSX.WorkBook = XLSX.utils.book_new();\r\n        XLSX.utils.book_append_sheet(wb, ws, 'Errors');\r\n\r\n        const wbout = XLSX.write(wb, {bookType: 'xlsx', type: 'binary'});\r\n        const buf = new ArrayBuffer(wbout.length);\r\n        const view = new Uint8Array(buf);\r\n\r\n        for (let i = 0; i < wbout.length; ++i) {\r\n            view[i] = wbout.charCodeAt(i) & 0xFF;\r\n        }\r\n\r\n        const blob = new Blob([buf], {type: 'application/octet-stream'});\r\n        const url = window.URL.createObjectURL(blob);\r\n        const a = document.createElement('a');\r\n        a.href = url;\r\n        a.download = `${this.fileName}_Danh_sách_lỗi_${new Date().toISOString().replace(/[-:T.]/g, '').substring(0, 14)}.xlsx`;\r\n        document.body.appendChild(a);\r\n        a.click();\r\n        window.URL.revokeObjectURL(url);\r\n        document.body.removeChild(a);\r\n        this.isShowDownload = false;\r\n    }\r\n\r\n    onChangeDateFrom(value) {\r\n        if (value) {\r\n            this.minDateTo = value;\r\n        } else {\r\n            this.minDateTo = null\r\n        }\r\n    }\r\n\r\n    onChangeDateTo(value) {\r\n        if (value) {\r\n            this.maxDateFrom = value;\r\n        } else {\r\n            this.maxDateFrom = new Date();\r\n        }\r\n    }\r\n\r\n    removeImsi(i: number) {\r\n        this.listImsisSelected.splice(i, 1);\r\n    }\r\n\r\n    isHideUpload() {\r\n        this.isShowUpload = false;\r\n    }\r\n    removeFileExtension(fileName: string): string {\r\n        const lastDotIndex = fileName.lastIndexOf('.');\r\n        if (lastDotIndex === -1) return fileName;\r\n        return fileName.substring(0, lastDotIndex);\r\n    }\r\n    onKeyDownNote(event): void {\r\n        if (event.key === ' ' && (this.ticket.note == null || this.ticket.note != null && this.ticket.note.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (this.ticket.note != null && this.ticket.note.trim() != '') {\r\n            this.ticket.note = this.ticket.note.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n    onKeyDownContent(event) {\r\n        if (event.key === ' ' && (this.ticket.content == null || this.ticket.content != null && this.ticket.content.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (this.ticket.content != null && this.ticket.content.trim() != '') {\r\n            this.ticket.content = this.ticket.content.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n    onKeyDownNoteContent(event: KeyboardEvent, note: any): void {\r\n        if (event.key === ' ' && (!note.content || note.content.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (note.content && note.content.trim() !== '') {\r\n            note.content = note.content.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n    onKeyDownCause(event) {\r\n        if (event.key === ' ' && (this.ticket.cause == null || this.ticket.cause != null && this.ticket.cause.trim() === '')) {\r\n            event.preventDefault();\r\n        }\r\n\r\n        if (this.ticket.cause != null && this.ticket.cause.trim() != '') {\r\n            this.ticket.cause = this.ticket.cause.trimStart().replace(/\\s{2,}/g, ' ');\r\n            return;\r\n        }\r\n    }\r\n    validateInput(event) {\r\n        const invalidChars = ['e', 'E', '+', '-'];\r\n        const inputField = event.target;\r\n        let value = inputField.value;\r\n\r\n        // Remove invalid characters from the value\r\n        invalidChars.forEach(char => {\r\n            value = value.split(char).join('');\r\n        });\r\n\r\n        // Update the input field value if it contains invalid characters\r\n        if (inputField.value !== value) {\r\n            inputField.value = value;\r\n        }\r\n    }\r\n\r\n    protected readonly CONSTANTS = CONSTANTS;\r\n}\r\n", "    <div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{ tranService.translate(\"ticket.menu.activeSim\") }}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"col-5 flex flex-row justify-content-end align-items-center\">\r\n        <p-button styleClass=\"p-button-info\"\r\n                  *ngIf=\"userInfo.type == userType.CUSTOMER && checkAuthen([CONSTANTS.PERMISSIONS.TICKET.CREATE])\"\r\n                  [label]=\"tranService.translate('global.button.create')\"\r\n                  (click)=\"showModalCreate()\" icon=\"\">\r\n        </p-button>\r\n    </div>\r\n</div>\r\n\r\n<form [formGroup]=\"formSearchTicket\" (ngSubmit)=\"onSubmitSearch()\" class=\"pt-3 pb-2 vnpt-field-set\">\r\n    <p-panel [toggleable]=\"true\" [header]=\"tranService.translate('global.text.filter')\">\r\n        <div class=\"grid search-grid-4\">\r\n            <!-- ma tinh -->\r\n            <div *ngIf=\"this.userInfo.type == this.userType.ADMIN\" class=\"col-2\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\"\r\n                                [showClear]=\"true\" [filter]=\"true\" filterBy=\"display\"\r\n                                id=\"provinceCode\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"searchInfo.provinceCode\"\r\n                                [required]=\"false\"\r\n                                formControlName=\"provinceCode\"\r\n                                [options]=\"listProvince\"\r\n                                optionLabel=\"display\"\r\n                                optionValue=\"code\"\r\n                    ></p-dropdown>\r\n                    <label class=\"label-dropdown\" htmlFor=\"provinceCode\">{{ tranService.translate(\"account.label.province\") }}</label>\r\n                </span>\r\n            </div>\r\n            <!-- trạng thái ticket -->\r\n            <!--            <div class=\"col-2\">-->\r\n            <!--                <span class=\"p-float-label\">-->\r\n            <!--                    <p-dropdown styleClass=\"w-full\"-->\r\n            <!--                                [showClear]=\"true\" [filter]=\"true\" filterBy=\"display\"-->\r\n            <!--                                id=\"status\" [autoDisplayFirst]=\"false\"-->\r\n            <!--                                [(ngModel)]=\"searchInfo.status\"-->\r\n            <!--                                [required]=\"false\"-->\r\n            <!--                                formControlName=\"status\"-->\r\n            <!--                                [options]=\"listTicketStatus\"-->\r\n            <!--                                optionLabel=\"label\"-->\r\n            <!--                                optionValue=\"value\"-->\r\n            <!--                    ></p-dropdown>-->\r\n            <!--                    <label htmlFor=\"status\">{{tranService.translate(\"ticket.label.status\")}}</label>-->\r\n            <!--                </span>-->\r\n            <!--            </div>-->\r\n            <div class=\"col-2\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                           pInputText id=\"contactName\"\r\n                           [(ngModel)]=\"searchInfo.contactName\"\r\n                           formControlName=\"contactName\"\r\n                           type=\"text\"\r\n                    />\r\n                    <label htmlFor=\"contactName\">{{ tranService.translate(\"ticket.label.customerName\") }}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-2\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                           pInputText id=\"phone\"\r\n                           [(ngModel)]=\"searchInfo.contactPhone\"\r\n                           formControlName=\"contactPhone\"\r\n                           type=\"number\"\r\n                           (keydown)=\"preventCharacter($event)\"\r\n                           min = 0\r\n                    />\r\n                    <label htmlFor=\"phone\">{{ tranService.translate(\"ticket.label.phone\") }}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-2\">\r\n                <span class=\"p-float-label\">\r\n                    <p-calendar styleClass=\"w-full\"\r\n                                id=\"dateFrom\"\r\n                                [(ngModel)]=\"searchInfo.dateFrom\"\r\n                                formControlName=\"dateFrom\"\r\n                                [showIcon]=\"true\"\r\n                                [showClear]=\"true\"\r\n                                dateFormat=\"dd/mm/yy\"\r\n                                [maxDate]=\"maxDateFrom\"\r\n                                (onSelect)=\"onChangeDateFrom(searchInfo.dateFrom)\"\r\n                                (onInput)=\"onChangeDateFrom(searchInfo.dateFrom)\"\r\n                    ></p-calendar>\r\n                    <label class=\"label-calendar\" htmlFor=\"dateFrom\">{{ tranService.translate(\"ticket.label.dateFrom\") }}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-2\">\r\n                <span class=\"p-float-label\">\r\n                    <p-calendar styleClass=\"w-full\"\r\n                                id=\"dateTo\"\r\n                                [(ngModel)]=\"searchInfo.dateTo\"\r\n                                formControlName=\"dateTo\"\r\n                                [showIcon]=\"true\"\r\n                                [showClear]=\"true\"\r\n                                dateFormat=\"dd/mm/yy\"\r\n                                [minDate]=\"minDateTo\"\r\n                                [maxDate]=\"maxDateTo\"\r\n                                (onSelect)=\"onChangeDateTo(searchInfo.dateTo)\"\r\n                                (onInput)=\"onChangeDateTo(searchInfo.dateTo)\"\r\n                    />\r\n                    <label class=\"label-calendar\" htmlFor=\"dateTo\">{{ tranService.translate(\"ticket.label.dateTo\") }}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-2 pb-0\">\r\n                <p-button icon=\"pi pi-search\"\r\n                          styleClass=\"p-button-rounded p-button-secondary p-button-text button-search\"\r\n                          type=\"submit\"\r\n                ></p-button>\r\n            </div>\r\n        </div>\r\n    </p-panel>\r\n</form>\r\n\r\n<table-vnpt\r\n    [tableId]=\"'tableTicketConfigList'\"\r\n    [fieldId]=\"'provinceCode'\"\r\n    [columns]=\"columns\"\r\n    [dataSet]=\"dataSet\"\r\n    [options]=\"optionTable\"\r\n    [pageNumber]=\"pageNumber\"\r\n    [loadData]=\"search.bind(this)\"\r\n    [pageSize]=\"pageSize\"\r\n    [sort]=\"sort\"\r\n    [params]=\"searchInfo\"\r\n    [labelTable]=\"tranService.translate('ticket.menu.requestList')\"\r\n></table-vnpt>\r\n<!--    dialog tạo yêu cầu-->\r\n<div class=\"flex justify-content-center\">\r\n    <p-dialog\r\n        [header]=\"typeRequest == 'view' ? tranService.translate('ticket.label.viewActiveSim'): tranService.translate('ticket.label.requestActiveSim')\"\r\n        [(visible)]=\"isShowCreateRequest\"\r\n        [modal]=\"true\" [style]=\"{ width: '1000px' }\" [draggable]=\"false\" [resizable]=\"false\" (onHide)=\"isHideUpload()\">\r\n        <form class=\"mt-3\" [formGroup]=\"formActiveSim\" (ngSubmit)=\"createRequest()\">\r\n            <div class=\"flex dialog-ticket-sim-1\">\r\n                <div class=\"flex-1 flex col-6\">\r\n                    <p-card class=\"flex-wrap w-full ticket-sim-card\">\r\n                        <div class=\"w-full field grid\">\r\n                            <label class=\"col-fixed\"\r\n                                   style=\"width:180px\"><b>{{ tranService.translate(\"ticket.label.generalInfo\") }}</b><span\r\n                            >:</span></label>\r\n                        </div>\r\n                        <div class=\"w-full field grid\" *ngIf=\"this.userInfo.type == this.userType.ADMIN && typeRequest == 'view'\">\r\n                            <label htmlFor=\"contactName\" class=\"col-fixed\"\r\n                                   style=\"width:180px\">{{ tranService.translate(\"account.label.province\") }}<span\r\n                                class=\"text-red-500\">*</span></label>\r\n                            <div class=\"col\">\r\n                                <p-dropdown styleClass=\"w-full\"\r\n                                            [showClear]=\"false\" [filter]=\"true\" filterBy=\"display\"\r\n                                            id=\"provinceCode\" [autoDisplayFirst]=\"false\"\r\n                                            [(ngModel)]=\"ticket.provinceCode\"\r\n                                            formControlName=\"provinceCode\"\r\n                                            [options]=\"listProvince\"\r\n                                            optionLabel=\"display\"\r\n                                            optionValue=\"code\"\r\n                                            [disabled]=\"true\"\r\n                                            [readonly]=\"true\"\r\n                                ></p-dropdown>\r\n                            </div>\r\n                        </div>\r\n                        <!-- contactName -->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"contactName\" class=\"col-fixed\"\r\n                                   style=\"width:180px\">{{ tranService.translate(\"ticket.label.customerName\") }}<span\r\n                                class=\"text-red-500\">*</span></label>\r\n                            <div class=\"col\">\r\n                                <input class=\"w-full\"\r\n                                       pInputText id=\"contactName\"\r\n                                       [(ngModel)]=\"ticket.contactName\"\r\n                                       formControlName=\"contactName\"\r\n                                       [required]=\"true\"\r\n                                       [maxLength]=\"50\"\r\n                                       pattern=\"^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$\"\r\n                                       [placeholder]=\"tranService.translate('account.text.inputFullname')\"\r\n                                       [readonly]=\"true\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"email\" class=\"col-fixed\"\r\n                                   style=\"width:180px\">{{ tranService.translate(\"ticket.label.email\") }}<span\r\n                                class=\"text-red-500\">*</span></label>\r\n                            <div class=\"col\">\r\n                                <input class=\"w-full\"\r\n                                       pInputText id=\"contactEmail\"\r\n                                       [(ngModel)]=\"ticket.contactEmail\"\r\n                                       formControlName=\"contactEmail\"\r\n                                       [required]=\"true\"\r\n                                       [maxLength]=\"50\"\r\n                                       pattern=\"^[a-z0-9]+[a-z0-9\\-\\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\\-\\._]*[a-z0-9]+)+(\\.[a-z]{2,})$\"\r\n                                       [placeholder]=\"tranService.translate('account.text.inputEmail')\"\r\n                                       [readonly]=\"true\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n                        <!-- phone-->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"phone\" class=\"col-fixed\"\r\n                                   style=\"width:180px\">{{ tranService.translate(\"ticket.label.phone\") }}<span\r\n                                class=\"text-red-500\">*</span></label>\r\n                            <div class=\"col\">\r\n                                <input class=\"w-full\"\r\n                                       pInputText id=\"contactPhone\"\r\n                                       [(ngModel)]=\"ticket.contactPhone\"\r\n                                       formControlName=\"contactPhone\"\r\n                                       [required]=\"true\"\r\n                                       pattern=\"^((\\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$\"\r\n                                       (keydown)=\"preventCharacter($event)\"\r\n                                       [placeholder]=\"tranService.translate('account.text.inputPhone')\"\r\n                                       [readonly]=\"true\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n                        <!-- content-->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"content\" class=\"col-fixed\"\r\n                                   style=\"width:180px;height: fit-content;\">{{ tranService.translate(\"ticket.label.content\") }}</label>\r\n                            <div class=\"col\">\r\n                                <input class=\"w-full\"\r\n                                       pInputText id=\"content\"\r\n                                       [(ngModel)]=\"ticket.content\"\r\n                                       formControlName=\"content\"\r\n                                       [readonly]=\"typeRequest == 'view' || isShowUpload\"\r\n                                       [placeholder]=\"tranService.translate('ticket.label.content')\"\r\n                                       [pTooltip]=\"ticket.content\"\r\n                                       [maxlength]=\"255\"\r\n                                       (keydown)=\"onKeyDownContent($event)\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"w-full field grid text-error-field\">\r\n                            <label htmlFor=\"content\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                            <div class=\"col\">\r\n                                <small class=\"text-red-500\"\r\n                                       *ngIf=\"formActiveSim.controls.content.errors?.maxLength\">{{ tranService.translate(\"global.message.maxLength\", {len: 255}) }}</small>\r\n                            </div>\r\n                            <small class=\"text-red-500\"\r\n                                   *ngIf=\"formActiveSim.controls.content.errors?.pattern\">{{ tranService.translate(\"global.message.formatCode\") }}</small>\r\n                        </div>\r\n                        <!-- note-->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"note\" class=\"col-fixed\"\r\n                                   style=\"width:180px;height: fit-content;\">{{ tranService.translate(\"ticket.label.note\") }}</label>\r\n                            <div class=\"col\">\r\n                                <input class=\"w-full\"\r\n                                       pInputText id=\"note\"\r\n                                       [(ngModel)]=\"ticket.note\"\r\n                                       formControlName=\"note\"\r\n                                       [readonly]=\"typeRequest == 'view' || isShowUpload\"\r\n                                       [placeholder]=\"tranService.translate('ticket.label.note')\"\r\n                                       [pTooltip]=\"ticket.note\"\r\n                                       [maxlength]=\"255\"\r\n                                       (keydown)=\"onKeyDownNote($event)\"\r\n                                />\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"w-full field grid text-error-field\">\r\n                            <label htmlFor=\"note\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                            <div class=\"col\">\r\n                                <small class=\"text-red-500\"\r\n                                       *ngIf=\"formActiveSim.controls.note.errors?.maxLength\">{{ tranService.translate(\"global.message.maxLength\", {len: 255}) }}</small>\r\n                                <small class=\"text-red-500\"\r\n                                       *ngIf=\"formActiveSim.controls.note.errors?.pattern\">{{ tranService.translate(\"global.message.formatCode\") }}</small>\r\n                            </div>\r\n                        </div>\r\n                    </p-card>\r\n                </div>\r\n                <div class=\"flex-1 flex col-6\">\r\n                    <div class=\"block flex-wrap w-full\">\r\n                        <p-card class=\"w-full mb-3\" [class]=\"typeRequest == 'view' ? 'hidden' : ''\">\r\n                            <div class=\"w-full field grid\">\r\n                                <label class=\"col-fixed\"\r\n                                       style=\"width:180px\"><b>{{ tranService.translate(\"ticket.label.enterImsi\") }}</b><span\r\n                                    class=\"text-red-500\">*</span><span\r\n                                >:</span></label>\r\n                            </div>\r\n                            <div class=\"flex items-center\">\r\n                                <vnpt-select\r\n                                    [(value)]=\"listImsisSelected\" style=\"width:70%\"\r\n                                    [placeholder]=\"tranService.translate('ticket.label.listImsi')\"\r\n                                    objectKey=\"activeImsi\"\r\n                                    paramKey=\"imsi\"\r\n                                    keyReturn=\"imsi\"\r\n                                    displayPattern=\"${imsi}\"\r\n                                    [isMultiChoice]=\"true\"\r\n                                    [paramDefault]=\"{status: 0, ticketType: 2}\"\r\n                                    [disabled] = \"isShowUpload\"\r\n                                ></vnpt-select>\r\n                                <div>\r\n                                    <p-button (onClick)=\"isShowUpload = true\" icon=\"pi pi-upload\"\r\n                                              [hidden]=\"typeRequest == 'view' ? 'hidden' : ''\"\r\n                                              [label]=\"this.tranService.translate('ticket.message.uploadFile')\"></p-button>\r\n                                </div>\r\n                            </div>\r\n                        </p-card>\r\n\r\n                        <div class=\"block ticket-sim-card-1\" *ngIf=\"this.listImsisSelected.length > 0\" [class]=\"typeRequest == 'create' ? 'mt-4' : ''\">\r\n                            <p-card class=\"p-grid p-justify-center mt-5\">\r\n                                <div class=\"w-full field grid\">\r\n                                    <label class=\"col-fixed\"\r\n                                           style=\"width:180px\"><b>{{ tranService.translate(\"ticket.label.listactiveImsis\") }}</b><span\r\n                                    >:</span></label>\r\n                                </div>\r\n                                <div class=\"w-full\">\r\n                                    <p-table [value]=\"this.listImsisSelected\">\r\n                                        <ng-template pTemplate=\"header\">\r\n                                            <tr>\r\n                                                <th>{{ tranService.translate('global.text.stt') }}</th>\r\n                                                <th>{{ tranService.translate('ticket.label.imsi') }}</th>\r\n                                                <th [hidden]=\"typeRequest == 'view'\">{{ tranService.translate('global.text.action') }}</th>\r\n                                            </tr>\r\n                                        </ng-template>\r\n                                        <ng-template pTemplate=\"body\" let-item let-i=\"rowIndex\">\r\n                                            <tr>\r\n                                                <td>{{ i + 1 }}</td>\r\n                                                <td>{{ item }}</td>\r\n                                                <td [hidden]=\"typeRequest == 'view'\">\r\n                                                    <p-button icon=\"pi pi-trash\" (onClick)=\"removeImsi(i)\" [disabled] = \"isShowUpload\" ></p-button>\r\n                                                </td>\r\n                                            </tr>\r\n                                        </ng-template>\r\n                                    </p-table>\r\n                                </div>\r\n                            </p-card>\r\n                        </div>\r\n\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex flex-row justify-content-center align-items-center mt-3\">\r\n                <p-button styleClass=\"mr-2 p-button-secondary\" [label]=\"tranService.translate('global.button.cancel')\"\r\n                          (click)=\"isShowCreateRequest = false; isShowUpload = false\" [hidden]=\"typeRequest == 'view'\"></p-button>\r\n                <p-button type=\"submit\" styleClass=\"p-button-info\" *ngIf=\"checkAuthen([CONSTANTS.PERMISSIONS.TICKET.CREATE])\"\r\n                          [disabled]=\"formActiveSim.invalid || listImsisSelected.length == 0\"\r\n                          [hidden]=\"typeRequest == 'view'\" (onClick)=\"isShowCreateRequest = false\"\r\n                          [label]=\"tranService.translate('global.button.save')\"></p-button>\r\n            </div>\r\n        </form>\r\n    </p-dialog>\r\n    <p-dialog [(visible)]=\"isShowDownload\"\r\n              [modal]=\"true\" [style]=\"{ width: '500px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n        <div class=\"grid flex align-items-center justify-content-center\">\r\n            <div class=\"col-10 align-items-center justify-content-center\">\r\n                <i class=\"flex align-items-center justify-content-center pi pi-times-circle\"\r\n                   style=\"font-size: 2em; color: red;\"></i>\r\n                <br>\r\n                <b class=\"flex align-items-center justify-content-center\">{{ this.tranService.translate('ticket.message.isError') }} </b>\r\n                <p class=\"flex align-items-center justify-content-center\">{{ this.tranService.translate('ticket.message.isDownloadMessage') }}</p>\r\n                <p-button (onClick)=\"downloadErrorFile()\" class=\"flex align-items-center justify-content-center\"\r\n                          [label]=\"this.tranService.translate('ticket.message.downloadFile')\"></p-button>\r\n            </div>\r\n        </div>\r\n    </p-dialog>\r\n    <p-dialog [(visible)]=\"isShowUpload\" [header]=\"this.tranService.translate('ticket.label.imsiByFile')\">\r\n        <div class=\"w-full field grid mt-4\">\r\n            <div class=\"col-10 flex flex-row justify-content-start align-items-center\">\r\n                <input-file-vnpt class=\"w-full\" [(fileObject)]=\"fileObject\"\r\n                                 [options]=\"optionInputFile\"\r\n                ></input-file-vnpt>\r\n            </div>\r\n            <div class=\"col-2 flex flex-row justify-content-end align-items-center\">\r\n                <p-button icon=\"pi pi-download\" [pTooltip]=\"tranService.translate('global.button.downloadTemp')\"\r\n                          styleClass=\"p-button-outlined p-button-secondary\" (click)=\"downloadTemplate()\"></p-button>\r\n            </div>\r\n        </div>\r\n    </p-dialog>\r\n</div>\r\n"], "mappings": "AAEA,SAAQA,aAAa,QAAO,sCAAsC;AAElE,SAAQC,SAAS,QAAO,iCAAiC;AACzD,SAAQC,aAAa,QAAO,wBAAwB;AACpD,SAAQC,cAAc,QAAO,4CAA4C;AAEzE,SAAQC,sBAAsB,QAAwB,wDAAwD;AAC9G,OAAO,KAAKC,IAAI,MAAM,MAAM;AAE5B,SAAQC,gBAAgB,QAAO,6CAA6C;;;;;;;;;;;;;;;;;;;;;;;;ICLpEC,EAAA,CAAAC,cAAA,mBAG8C;IAApCD,EAAA,CAAAE,UAAA,mBAAAC,2EAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IACrCT,EAAA,CAAAU,YAAA,EAAW;;;;IAFDV,EAAA,CAAAW,UAAA,UAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,yBAAuD;;;;;;IAU7Dd,EAAA,CAAAC,cAAA,cAAqE;IAKjDD,EAAA,CAAAE,UAAA,2BAAAa,iFAAAC,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAa,IAAA;MAAA,MAAAC,OAAA,GAAAlB,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAU,OAAA,CAAAC,UAAA,CAAAC,YAAA,GAAAJ,MAAA,CACxC;IAAA,EADgE;IAMhDhB,EAAA,CAAAU,YAAA,EAAa;IACdV,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAqB,MAAA,GAAqD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IATtGV,EAAA,CAAAsB,SAAA,GAAkB;IAAlBtB,EAAA,CAAAW,UAAA,mBAAkB,uDAAAY,MAAA,CAAAJ,UAAA,CAAAC,YAAA,gCAAAG,MAAA,CAAAC,YAAA;IASuBxB,EAAA,CAAAsB,SAAA,GAAqD;IAArDtB,EAAA,CAAAyB,iBAAA,CAAAF,MAAA,CAAAV,WAAA,CAAAC,SAAA,2BAAqD;;;;;;IAkHtGd,EAAA,CAAAC,cAAA,cAA0G;IAE3ED,EAAA,CAAAqB,MAAA,GAAqD;IAAArB,EAAA,CAAAC,cAAA,eACvD;IAAAD,EAAA,CAAAqB,MAAA,QAAC;IAAArB,EAAA,CAAAU,YAAA,EAAO;IACjCV,EAAA,CAAAC,cAAA,cAAiB;IAIDD,EAAA,CAAAE,UAAA,2BAAAwB,iFAAAV,MAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAuB,IAAA;MAAA,MAAAC,OAAA,GAAA5B,EAAA,CAAAO,aAAA;MAAA,OAAaP,EAAA,CAAAQ,WAAA,CAAAoB,OAAA,CAAAC,MAAA,CAAAT,YAAA,GAAAJ,MAAA,CACpD;IAAA,EADwE;IAO5ChB,EAAA,CAAAU,YAAA,EAAa;;;;IAbSV,EAAA,CAAAsB,SAAA,GAAqD;IAArDtB,EAAA,CAAAyB,iBAAA,CAAAK,MAAA,CAAAjB,WAAA,CAAAC,SAAA,2BAAqD;IAIhEd,EAAA,CAAAsB,SAAA,GAAmB;IAAnBtB,EAAA,CAAAW,UAAA,oBAAmB,uDAAAmB,MAAA,CAAAD,MAAA,CAAAT,YAAA,aAAAU,MAAA,CAAAN,YAAA;;;;;;;;;;IAqF/BxB,EAAA,CAAAC,cAAA,gBACgE;IAAAD,EAAA,CAAAqB,MAAA,GAAmE;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA3EV,EAAA,CAAAsB,SAAA,GAAmE;IAAnEtB,EAAA,CAAAyB,iBAAA,CAAAM,MAAA,CAAAlB,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAgC,eAAA,IAAAC,GAAA,GAAmE;;;;;IAEvIjC,EAAA,CAAAC,cAAA,gBAC8D;IAAAD,EAAA,CAAAqB,MAAA,GAAwD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAhEV,EAAA,CAAAsB,SAAA,GAAwD;IAAxDtB,EAAA,CAAAyB,iBAAA,CAAAS,MAAA,CAAArB,WAAA,CAAAC,SAAA,8BAAwD;;;;;IAsBlHd,EAAA,CAAAC,cAAA,gBAC6D;IAAAD,EAAA,CAAAqB,MAAA,GAAmE;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA3EV,EAAA,CAAAsB,SAAA,GAAmE;IAAnEtB,EAAA,CAAAyB,iBAAA,CAAAU,MAAA,CAAAtB,WAAA,CAAAC,SAAA,6BAAAd,EAAA,CAAAgC,eAAA,IAAAC,GAAA,GAAmE;;;;;IAChIjC,EAAA,CAAAC,cAAA,gBAC2D;IAAAD,EAAA,CAAAqB,MAAA,GAAwD;IAAArB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAhEV,EAAA,CAAAsB,SAAA,GAAwD;IAAxDtB,EAAA,CAAAyB,iBAAA,CAAAW,MAAA,CAAAvB,WAAA,CAAAC,SAAA,8BAAwD;;;;;IA4CvGd,EAAA,CAAAC,cAAA,SAAI;IACID,EAAA,CAAAqB,MAAA,GAA8C;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACvDV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,GAAgD;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACzDV,EAAA,CAAAC,cAAA,aAAqC;IAAAD,EAAA,CAAAqB,MAAA,GAAiD;IAAArB,EAAA,CAAAU,YAAA,EAAK;;;;IAFvFV,EAAA,CAAAsB,SAAA,GAA8C;IAA9CtB,EAAA,CAAAyB,iBAAA,CAAAY,OAAA,CAAAxB,WAAA,CAAAC,SAAA,oBAA8C;IAC9Cd,EAAA,CAAAsB,SAAA,GAAgD;IAAhDtB,EAAA,CAAAyB,iBAAA,CAAAY,OAAA,CAAAxB,WAAA,CAAAC,SAAA,sBAAgD;IAChDd,EAAA,CAAAsB,SAAA,GAAgC;IAAhCtB,EAAA,CAAAW,UAAA,WAAA0B,OAAA,CAAAC,WAAA,WAAgC;IAACtC,EAAA,CAAAsB,SAAA,GAAiD;IAAjDtB,EAAA,CAAAyB,iBAAA,CAAAY,OAAA,CAAAxB,WAAA,CAAAC,SAAA,uBAAiD;;;;;;IAI1Fd,EAAA,CAAAC,cAAA,SAAI;IACID,EAAA,CAAAqB,MAAA,GAAW;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACpBV,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAqB,MAAA,GAAU;IAAArB,EAAA,CAAAU,YAAA,EAAK;IACnBV,EAAA,CAAAC,cAAA,aAAqC;IACJD,EAAA,CAAAE,UAAA,qBAAAqC,yFAAA;MAAA,MAAAC,WAAA,GAAAxC,EAAA,CAAAI,aAAA,CAAAqC,IAAA;MAAA,MAAAC,KAAA,GAAAF,WAAA,CAAAG,QAAA;MAAA,MAAAC,OAAA,GAAA5C,EAAA,CAAAO,aAAA;MAAA,OAAWP,EAAA,CAAAQ,WAAA,CAAAoC,OAAA,CAAAC,UAAA,CAAAH,KAAA,CAAa;IAAA,EAAC;IAA8B1C,EAAA,CAAAU,YAAA,EAAW;;;;;;IAH/FV,EAAA,CAAAsB,SAAA,GAAW;IAAXtB,EAAA,CAAAyB,iBAAA,CAAAiB,KAAA,KAAW;IACX1C,EAAA,CAAAsB,SAAA,GAAU;IAAVtB,EAAA,CAAAyB,iBAAA,CAAAqB,QAAA,CAAU;IACV9C,EAAA,CAAAsB,SAAA,GAAgC;IAAhCtB,EAAA,CAAAW,UAAA,WAAAoC,OAAA,CAAAT,WAAA,WAAgC;IACuBtC,EAAA,CAAAsB,SAAA,GAA2B;IAA3BtB,EAAA,CAAAW,UAAA,aAAAoC,OAAA,CAAAC,YAAA,CAA2B;;;;;IArB9GhD,EAAA,CAAAC,cAAA,cAA+H;IAIrFD,EAAA,CAAAqB,MAAA,GAA2D;IAAArB,EAAA,CAAAU,YAAA,EAAI;IAAAV,EAAA,CAAAC,cAAA,WAC5F;IAAAD,EAAA,CAAAqB,MAAA,QAAC;IAAArB,EAAA,CAAAU,YAAA,EAAO;IAEbV,EAAA,CAAAC,cAAA,cAAoB;IAEZD,EAAA,CAAAiD,UAAA,KAAAC,4DAAA,0BAMc;IACdlD,EAAA,CAAAiD,UAAA,KAAAE,4DAAA,0BAQc;IAClBnD,EAAA,CAAAU,YAAA,EAAU;;;;IAzByDV,EAAA,CAAAoD,UAAA,CAAAC,MAAA,CAAAf,WAAA,2BAA+C;IAIpFtC,EAAA,CAAAsB,SAAA,GAA2D;IAA3DtB,EAAA,CAAAyB,iBAAA,CAAA4B,MAAA,CAAAxC,WAAA,CAAAC,SAAA,iCAA2D;IAIhFd,EAAA,CAAAsB,SAAA,GAAgC;IAAhCtB,EAAA,CAAAW,UAAA,UAAA0C,MAAA,CAAAC,iBAAA,CAAgC;;;;;;IA4B7DtD,EAAA,CAAAC,cAAA,mBAGgE;IADrBD,EAAA,CAAAE,UAAA,qBAAAqD,+EAAA;MAAAvD,EAAA,CAAAI,aAAA,CAAAoD,IAAA;MAAA,MAAAC,OAAA,GAAAzD,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAiD,OAAA,CAAAC,mBAAA,GAAiC,KAAK;IAAA,EAAC;IAClB1D,EAAA,CAAAU,YAAA,EAAW;;;;IAFjEV,EAAA,CAAAW,UAAA,aAAAgD,MAAA,CAAAC,aAAA,CAAAC,OAAA,IAAAF,MAAA,CAAAL,iBAAA,CAAAQ,MAAA,MAAmE,WAAAH,MAAA,CAAArB,WAAA,qBAAAqB,MAAA,CAAA9C,WAAA,CAAAC,SAAA;;;;;;;;;;;;;;;;;;;;;;AD7T7F,OAAM,MAAOiD,4BAA6B,SAAQpE,aAAa;EAgE3DqE,YACmCC,aAA4B,EAC3BC,cAA8B,EAC5BC,gBAAkC,EAC5DC,GAAsB,EACtBC,WAAwB,EACxBC,QAAkB;IAC1B,KAAK,CAACA,QAAQ,CAAC;IANgB,KAAAL,aAAa,GAAbA,aAAa;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACZ,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAC1C,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAtDpB,KAAAC,WAAW,GAAkC,IAAIC,IAAI,EAAE;IACvD,KAAAC,SAAS,GAAkC,IAAI;IAC/C,KAAAC,SAAS,GAAkC,IAAIF,IAAI,EAAE;IAErD,KAAAG,SAAS,GAAG,EAAE;IACd,KAAArB,iBAAiB,GAAG,EAAE;IAwCtB,KAAAsB,YAAY,GAAU,EAAE;IAgtBL,KAAAlF,SAAS,GAAGA,SAAS;EArsBxC;EAEAmF,QAAQA,CAAA;IACJ,IAAIC,EAAE,GAAG,IAAI;IACb,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACD,QAAQ;IAC5C,IAAI,CAACrB,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACuB,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACjC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACV,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAAC4C,QAAQ,GAAGxF,SAAS,CAACyF,SAAS;IACnC,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACvD,MAAM,GAAG;MACVwD,EAAE,EAAE,IAAI;MACRC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE,IAAI;MAClBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAElG,SAAS,CAACmG,YAAY,CAACC,UAAU;MACvCC,MAAM,EAAE,IAAI;MACZC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,IAAI;MAChB7E,YAAY,EAAE;KACjB;IACD,IAAI,CAAC8E,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,cAAc,GAAG,CAClB;MACIC,KAAK,EAAE,IAAI,CAACvF,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;MAC1DuF,KAAK,EAAE;KACV,CACJ;IACD,IAAI,CAACC,eAAe,GAAG;MACnB,CAAC,EAAE,CAAC;QACAF,KAAK,EAAEtB,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;QACzDuF,KAAK,EAAE;OACV,CAAC;MACF,CAAC,EAAE,CACC;QACID,KAAK,EAAEtB,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;QAC3DuF,KAAK,EAAE;OACV,EACD;QACID,KAAK,EAAEtB,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QACvDuF,KAAK,EAAE;OACV,CACJ;MACD,CAAC,EAAE,CACC;QACID,KAAK,EAAEtB,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACrDuF,KAAK,EAAE;OACV;KAER;IACD,IAAI,CAACE,gBAAgB,GAAG,CACpB;MACIH,KAAK,EAAEtB,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;MACpDuF,KAAK,EAAE;KACV,EACD;MACID,KAAK,EAAEtB,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MACzDuF,KAAK,EAAE;KACV,EACD;MACID,KAAK,EAAEtB,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC3DuF,KAAK,EAAE;KACV,EACD;MACID,KAAK,EAAEtB,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACvDuF,KAAK,EAAE;KACV,EACD;MACID,KAAK,EAAEtB,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;MACrDuF,KAAK,EAAE;KACV,CACJ;IACD,IAAI,CAAClF,UAAU,GAAG;MACdC,YAAY,EAAE,IAAI;MAClBoF,KAAK,EAAE,IAAI;MACXhB,YAAY,EAAE,IAAI;MAClBD,YAAY,EAAE,IAAI;MAClBK,IAAI,EAAElG,SAAS,CAACmG,YAAY,CAACC,UAAU;MACvCC,MAAM,EAAE,IAAI;MACZU,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,IAAI;MACZpB,WAAW,EAAE;KAChB;IACD,IAAI,CAACqB,OAAO,GAAG,CACX;MACIC,IAAI,EAAE,IAAI,CAAC/F,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;MACzD+F,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI,CAACjC,QAAQ,CAACa,IAAI,IAAIlG,SAAS,CAACyF,SAAS,CAAC8B,KAAK;MACvDC,MAAM,EAAE;KACX,EACD;MACIN,IAAI,EAAE,IAAI,CAAC/F,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7D+F,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZC,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAE;QACHC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;;KAErB,EAAE;MACCZ,IAAI,EAAE,IAAI,CAAC/F,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;MACtD+F,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZC,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAE;QACHC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;;KAErB,EAAE;MACCZ,IAAI,EAAE,IAAI,CAAC/F,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;MACtD+F,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE;KACX,EAAE;MACCN,IAAI,EAAE,IAAI,CAAC/F,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACxD+F,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZC,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAE;QACHC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;;KAErB,EACD;MACIZ,IAAI,EAAE,IAAI,CAAC/F,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;MAC5D+F,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZE,MAAM,EAAE,IAAI;MACZO,eAAeA,CAACpB,KAAK;QACjB,IAAIA,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI;QAC9B,OAAOvB,EAAE,CAAC4C,WAAW,CAACC,mBAAmB,CAAC,IAAInD,IAAI,CAAC6B,KAAK,CAAC,CAAC;MAC9D;;IAEJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAAA,CACH;;IAED,IAAI,CAACuB,WAAW,GAAG;MACfC,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE,KAAK;MAC1BC,MAAM,EAAE,CACJ;QACIC,IAAI,EAAE,mBAAmB;QACzBC,OAAO,EAAE,IAAI,CAACtH,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACzDsH,IAAI,EAAE,SAAAA,CAAU/C,EAAE,EAAEgD,IAAI;UACpBvD,EAAE,CAACwD,aAAa,CAACjD,EAAE,EAAEgD,IAAI,EAAE,MAAM,CAAC;QACtC;OACH;KACR;IACD,IAAI,CAACE,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,IAAI,GAAG,kBAAkB;IAC9B,IAAI,CAACC,OAAO,GAAG;MACXjD,OAAO,EAAE,EAAE;MACXkD,KAAK,EAAE;KACV;IACD,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACvE,WAAW,CAACwE,KAAK,CAAC,IAAI,CAAC1H,UAAU,CAAC;IAC/D,IAAI,CAACyC,aAAa,GAAG,IAAI,CAACS,WAAW,CAACwE,KAAK,CAAC,IAAI,CAAChH,MAAM,CAAC;IACxD,IAAI,CAACiH,eAAe,GAAG;MACnBlD,IAAI,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;MACrBmD,gBAAgB,EAAE,IAAI,CAAClI,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;MAC7EkI,OAAO,EAAE,GAAG;MACZC,IAAI,EAAE,IAAI;MACVC,QAAQ,EAAE,KAAK;MACfC,kBAAkB,EAAE,IAAI;MACxBC,YAAY,EAAE,IAAI,CAACC,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;MACxCC,QAAQ,EAAE;KACb;IACD,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,MAAM,CAAC,IAAI,CAAClB,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACtH,UAAU,CAAC;EAC3E;EAEAsI,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAElB,IAAI,EAAEmB,MAAM;IAC5B,IAAI9E,EAAE,GAAG,IAAI;IACb,IAAI,CAACyD,UAAU,GAAGmB,IAAI;IACtB,IAAI,CAAClB,QAAQ,GAAGmB,KAAK;IACrB,IAAI,CAAClB,IAAI,GAAGA,IAAI;IAChB,IAAIoB,UAAU,GAAG;MACbH,IAAI;MACJ5C,IAAI,EAAE6C,KAAK;MACXlB;KACH;IACDqB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5I,UAAU,CAAC,CAAC6I,OAAO,CAACnD,GAAG,IAAG;MACvC,IAAI,IAAI,CAAC1F,UAAU,CAAC0F,GAAG,CAAC,IAAI,IAAI,EAAE;QAC9B,IAAIA,GAAG,IAAI,UAAU,EAAE;UACnBgD,UAAU,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC1I,UAAU,CAACsF,QAAQ,CAACwD,OAAO,EAAE;SAC9D,MAAM,IAAIpD,GAAG,IAAI,QAAQ,EAAE;UACxBgD,UAAU,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC1I,UAAU,CAACuF,MAAM,CAACuD,OAAO,EAAE;SAC1D,MAAM;UACHJ,UAAU,CAAChD,GAAG,CAAC,GAAG,IAAI,CAAC1F,UAAU,CAAC0F,GAAG,CAAC;;;IAGlD,CAAC,CAAC;IACF,IAAI,CAAC6B,OAAO,GAAG;MACXjD,OAAO,EAAE,EAAE;MACXkD,KAAK,EAAE;KACV;IACD;IACA,IAAI,CAAC1E,aAAa,CAACiG,YAAY,CAACL,UAAU,EAAGM,QAAQ,IAAI;MACrDrF,EAAE,CAAC4D,OAAO,GAAG;QACTjD,OAAO,EAAE0E,QAAQ,CAAC1E,OAAO;QACzBkD,KAAK,EAAEwB,QAAQ,CAACC;OACnB;IACL,CAAC,EAAE,IAAI,EAAE,MAAK;MACVtF,EAAE,CAACuF,oBAAoB,CAACC,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAC,WAAWA,CAAA;IACP,IAAI,CAAC1I,MAAM,GAAG;MACVwD,EAAE,EAAE,IAAI;MACRC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE,IAAI;MAClBC,OAAO,EAAE,IAAI;MACbC,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAElG,SAAS,CAACmG,YAAY,CAACC,UAAU;MACvCC,MAAM,EAAE,IAAI;MACZC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,IAAI;MAChB7E,YAAY,EAAE;KACjB;IACD,IAAI,CAACkC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACsB,YAAY,GAAG,EAAE;IACtB,IAAI,CAACK,cAAc,GAAG,KAAK;EAC/B;EAEAuF,cAAcA,CAAA;IACV,IAAI,CAACjC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACkB,MAAM,CAAC,IAAI,CAAClB,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACtH,UAAU,CAAC;EAC3E;EAEAqI,eAAeA,CAAA;IACX,IAAI,CAACtF,cAAc,CAACsF,eAAe,CAAEW,QAAQ,IAAI;MAC7C,IAAI,CAAC3I,YAAY,GAAG2I,QAAQ,CAACM,GAAG,CAACC,EAAE,IAAG;QAClC,OAAO;UACH,GAAGA,EAAE;UACLrD,OAAO,EAAE,GAAGqD,EAAE,CAACC,IAAI,MAAMD,EAAE,CAAC9D,IAAI;SACnC;MACL,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEA;EACAgE,aAAaA,CAAA;IACT,IAAI9F,EAAE,GAAG,IAAI;IACb+F,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;IACrBhG,EAAE,CAACuF,oBAAoB,CAACU,MAAM,EAAE;IAChC,IAAIC,QAAQ,GAAG;MACX1F,WAAW,EAAE,IAAI,CAACzD,MAAM,CAACyD,WAAW;MACpCC,YAAY,EAAE,IAAI,CAAC1D,MAAM,CAAC0D,YAAY;MACtCC,YAAY,EAAE,IAAI,CAAC3D,MAAM,CAAC2D,YAAY;MACtCC,OAAO,EAAE,IAAI,CAAC5D,MAAM,CAAC4D,OAAO;MAC5BC,IAAI,EAAE,IAAI,CAAC7D,MAAM,CAAC6D,IAAI;MACtBE,IAAI,EAAE,IAAI,CAAC/D,MAAM,CAAC+D;KACrB;IACD,IAAI,CAAC3B,aAAa,CAACgH,YAAY,CAACD,QAAQ,EAAGE,IAAI,IAAI;MAC/C;MACA,IAAIC,mBAAmB,GAAG;QACtBC,QAAQ,EAAEF,IAAI,CAAC7F,EAAE;QACjBgG,cAAc,EAAEH,IAAI,CAACI,SAAS;QAC9BC,YAAY,EAAEL,IAAI,CAACjF,UAAU;QAC7BuF,KAAK,EAAE1G,EAAE,CAACxB;OACb;MACDwB,EAAE,CAACX,gBAAgB,CAACsH,MAAM,CAACN,mBAAmB,EAAGO,GAAG,IAAI;QACpDb,OAAO,CAACC,GAAG,CAACY,GAAG,CAAC;QAChB5G,EAAE,CAACuF,oBAAoB,CAACsB,OAAO,CAAC7G,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QACvFgE,EAAE,CAACpB,mBAAmB,GAAG,KAAK;QAC9BoB,EAAE,CAACuF,oBAAoB,CAACC,OAAO,EAAE;QACjCxF,EAAE,CAAC2E,MAAM,CAAC3E,EAAE,CAACyD,UAAU,EAAEzD,EAAE,CAAC0D,QAAQ,EAAE1D,EAAE,CAAC2D,IAAI,EAAE3D,EAAE,CAAC3D,UAAU,CAAC;MACjE,CAAC,CAAC;MACF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA2D,EAAE,CAACb,aAAa,CAAC2H,qBAAqB,CAAC9G,EAAE,CAACC,QAAQ,CAAC3D,YAAY,EAAGyK,KAAK,IAAI;QACvE,IAAIC,KAAK,GAAG,EAAE;QACd,KAAK,IAAIC,IAAI,IAAIF,KAAK,CAACG,UAAU,EAAE;UAC/BF,KAAK,CAACG,IAAI,CAAC;YACPC,MAAM,EAAEH,IAAI,CAACG,MAAM;YACnBd,QAAQ,EAAEF,IAAI,CAAC7F;WAClB,CAAC;;QAEN,IAAI6F,IAAI,EAAEjF,UAAU,EAAE;UAClB6F,KAAK,CAACG,IAAI,CAAC;YACPC,MAAM,EAAEhB,IAAI,CAACjF,UAAU;YACvBmF,QAAQ,EAAEF,IAAI,CAAC7F;WAClB,CAAC;;QAENP,EAAE,CAACb,aAAa,CAACkI,cAAc,CAACL,KAAK,CAAC;MAC1C,CAAC,CAAC;IACN,CAAC,EAAE,IAAI,EAAE,MAAK;MACVhH,EAAE,CAACuF,oBAAoB,CAACC,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEA7J,eAAeA,CAAA;IACX,IAAI,CAACiD,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACpB,WAAW,GAAG,QAAQ;IAC3B,IAAI,CAACiI,WAAW,EAAE;IAClB;IACA,IAAI,IAAI,CAACxF,QAAQ,CAACa,IAAI,KAAKlG,SAAS,CAACyF,SAAS,CAACiH,QAAQ,EAAE;MACrD,IAAI,CAACvK,MAAM,CAACyD,WAAW,GAAG,IAAI,CAACP,QAAQ,CAACsH,QAAQ;MAChD,IAAI,CAACxK,MAAM,CAAC2D,YAAY,GAAG,IAAI,CAACT,QAAQ,CAACuH,KAAK;MAC9C,IAAI,CAACzK,MAAM,CAAC0D,YAAY,GAAG,IAAI,CAACR,QAAQ,CAACyB,KAAK;;IAElD,IAAI,CAAC5C,aAAa,GAAG,IAAI,CAACS,WAAW,CAACwE,KAAK,CAAC,IAAI,CAAChH,MAAM,CAAC;EAC5D;EAEAyG,aAAaA,CAACjD,EAAE,EAAEgD,IAAI,EAAE/F,WAAmB;IACvC,IAAIA,WAAW,IAAI,MAAM,EAAE;MACvB,IAAI,CAACA,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACoB,mBAAmB,GAAG,IAAI;;IAEnC,IAAIoB,EAAE,GAAG,IAAI;IACb,IAAI,CAACyF,WAAW,EAAE;IAClB,IAAI,CAAC3G,aAAa,CAAC2I,KAAK,EAAE;IAC1B,IAAI,CAACtI,aAAa,CAACuI,eAAe,CAACnE,IAAI,CAAChD,EAAE,EAAG6F,IAAI,IAAI;MACjD,IAAI,CAACrJ,MAAM,GAAG;QACVwD,EAAE,EAAE6F,IAAI,CAAC7F,EAAE;QACXC,WAAW,EAAE4F,IAAI,CAAC5F,WAAW;QAC7BC,YAAY,EAAE2F,IAAI,CAAC3F,YAAY;QAC/BC,YAAY,EAAE0F,IAAI,CAAC1F,YAAY;QAC/BC,OAAO,EAAEyF,IAAI,CAACzF,OAAO;QACrBC,IAAI,EAAEwF,IAAI,CAACxF,IAAI;QACfC,KAAK,EAAEuF,IAAI,CAACvF,KAAK;QACjBC,IAAI,EAAEsF,IAAI,CAACtF,IAAI;QACfG,MAAM,EAAE,IAAI;QACZC,SAAS,EAAEkF,IAAI,CAACnF,MAAM;QACtBE,UAAU,EAAEiF,IAAI,CAACjF,UAAU;QAC3B7E,YAAY,EAAE8J,IAAI,CAAC9J;OACtB;MACD,IAAI,CAAC+C,gBAAgB,CAACsF,MAAM,CAAC;QAAC2B,QAAQ,EAAE,IAAI,CAACvJ,MAAM,CAACwD,EAAE;QAAEyB,IAAI,EAAE;MAAI,CAAC,EAAG4E,GAAG,IAAI;QACrE,IAAIF,KAAK,GAAa,EAAE;QACxBE,GAAG,CAACjG,OAAO,CAACuE,OAAO,CAAC3B,IAAI,IAAG;UACvBmD,KAAK,CAACS,IAAI,CAAC5D,IAAI,CAACoE,IAAI,CAAC;QACzB,CAAC,CAAC;QACF,IAAI,CAACnJ,iBAAiB,GAAGkI,KAAK;MAElC,CAAC,CACJ;IACL,CAAC,CAAC;IAEF,IAAI,CAACvH,aAAa,CAAC2H,qBAAqB,CAAC9G,EAAE,CAACC,QAAQ,CAAC3D,YAAY,EAAG8J,IAAI,IAAI;MACxE,IAAI,CAACwB,SAAS,GAAGxB,IAAI,CAACc,UAAU;IACpC,CAAC,CAAC;EACN;EAEAW,gBAAgBA,CAACC,KAAK;IAClB,IAAIA,KAAK,CAACC,OAAO,EAAE;MACf;;IAEJ,IAAID,KAAK,CAACE,OAAO,IAAI,CAAC,IAAIF,KAAK,CAACE,OAAO,IAAI,EAAE,IAAIF,KAAK,CAACE,OAAO,IAAI,EAAE,IAAIF,KAAK,CAACE,OAAO,IAAI,EAAE,EAAE;MACzF;;IAEJ,IAAIF,KAAK,CAACE,OAAO,GAAG,EAAE,IAAIF,KAAK,CAACE,OAAO,GAAG,EAAE,EAAE;MAC1CF,KAAK,CAACG,cAAc,EAAE;;IAE1B;IACA,IAAIH,KAAK,CAACE,OAAO,IAAI,EAAE,IAAIF,KAAK,CAACE,OAAO,IAAI,GAAG,IAAIF,KAAK,CAACE,OAAO,IAAI,GAAG,IAAIF,KAAK,CAACE,OAAO,IAAI,GAAG,EAAE;MAC7FF,KAAK,CAACG,cAAc,EAAE;;EAE9B;EAEAC,gBAAgBA,CAAA;IACZ,IAAI,CAAC/I,aAAa,CAAC+I,gBAAgB,EAAE;EACzC;EAEA3D,UAAUA,CAAC4D,IAAS;IAChB,IAAInI,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC9B,YAAY,GAAG,KAAK;IACvB,IAAI,CAACkD,QAAQ,GAAG,IAAI,CAACgH,mBAAmB,CAACD,IAAI,CAACrG,IAAI,CAAC;IACnD9B,EAAE,CAACuF,oBAAoB,CAACU,MAAM,EAAE;IAChC,IAAIkC,IAAI,CAACnG,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE;MAAE;MAC3BhC,EAAE,CAACuF,oBAAoB,CAACC,OAAO,EAAE;MACjCxF,EAAE,CAACuF,oBAAoB,CAAC8C,OAAO,CAACrI,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC,CAAC;MACrF;;IAEJ,IAAImM,IAAI,EAAE;MACN;MACAnI,EAAE,CAACF,YAAY,GAAG,EAAE;MACpBE,EAAE,CAACH,SAAS,GAAG,EAAE;MACjB,MAAMyI,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACrC,MAAM,GAAIuC,CAAM,IAAI;QACvB,MAAMC,SAAS,GAAGD,CAAC,CAACE,MAAM,CAACC,MAAM;QACjC,MAAMC,QAAQ,GAAG5N,IAAI,CAAC6N,IAAI,CAACJ,SAAS,EAAE;UAAC3H,IAAI,EAAE;QAAQ,CAAC,CAAC;QACvD,MAAMgI,SAAS,GAAGF,QAAQ,CAACG,UAAU,CAAC,CAAC,CAAC;QACxC,MAAMC,SAAS,GAAGJ,QAAQ,CAACK,MAAM,CAACH,SAAS,CAAC;QAC5C,MAAMI,QAAQ,GAAGlO,IAAI,CAACmO,KAAK,CAACC,aAAa,CAACJ,SAAS,EAAE;UAACK,MAAM,EAAE;QAAC,CAAC,CAAC;QAEjE;QACA,IAAIC,OAAO,GAAGJ,QAAQ,CAAC,CAAC,CAAC;QACzB,IAAIK,IAAI,GAAGL,QAAQ,CAACM,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CAAEC,GAAQ,IAAKA,GAAG,CAACC,IAAI,CAAEC,IAAS,IAAKA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKC,SAAS,IAAID,IAAI,KAAK,EAAE,CAAC,CAAC;QAC9H,IAAI,CAACE,YAAY,CAACR,OAAO,EAAEC,IAAI,CAAC;MACpC,CAAC;MACDjB,MAAM,CAACyB,kBAAkB,CAAC5B,IAAI,CAAC;;EAEvC;EAEA2B,YAAYA,CAACR,OAAO,EAAEC,IAAW;IAC7B,IAAIvJ,EAAE,GAAG,IAAI;IACbA,EAAE,CAACF,YAAY,GAAG,EAAE;IACpBE,EAAE,CAACgK,kBAAkB,CAACC,SAAS,EAAE;IACjC,MAAMC,eAAe,GAAG,CAAC,KAAK,EAAE,iBAAiB,CAAC;IAClD,MAAMC,YAAY,GAAGb,OAAO,CAACtK,MAAM,GAAGkL,eAAe,CAAClL,MAAM;IAC5D,MAAMoL,cAAc,GAAGd,OAAO,CAACtK,MAAM,GAAGkL,eAAe,CAAClL,MAAM;IAE9D,KAAK,IAAIqL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,eAAe,CAAClL,MAAM,EAAEqL,CAAC,EAAE,EAAE;MAC7C,IAAIf,OAAO,CAACe,CAAC,CAAC,KAAKH,eAAe,CAACG,CAAC,CAAC,EAAE;QACnCrK,EAAE,CAACuF,oBAAoB,CAACC,OAAO,EAAE;QACjCxF,EAAE,CAACuF,oBAAoB,CAAC+E,KAAK,CAACtK,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QACrF;;;IAIR,IAAImO,YAAY,IAAIC,cAAc,EAAE;MAChC,IAAID,YAAY,EAAE;QACdnK,EAAE,CAACuF,oBAAoB,CAAC+E,KAAK,CAACtK,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC,CAAC;;MAE9F,IAAIoO,cAAc,EAAE;QAChBpK,EAAE,CAACuF,oBAAoB,CAAC+E,KAAK,CAACtK,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC,CAAC;;MAE5FgE,EAAE,CAACuF,oBAAoB,CAACC,OAAO,EAAE;MACjC;;IAGJ,IAAI+D,IAAI,CAACvK,MAAM,KAAK,CAAC,EAAE;MACnBgB,EAAE,CAACuF,oBAAoB,CAACC,OAAO,EAAE;MACjCxF,EAAE,CAACuF,oBAAoB,CAAC+E,KAAK,CAACtK,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC,CAAC;MACnF;;IAGJ,MAAMuO,eAAe,GAAGhB,IAAI,CAACE,MAAM,CAACe,MAAM,IACtCA,MAAM,CAACxL,MAAM,KAAKkL,eAAe,CAAClL,MAAM,KACvCwL,MAAM,CAAC,CAAC,CAAC,KAAKX,SAAS,IAAIW,MAAM,CAAC,CAAC,CAAC,CAACC,QAAQ,EAAE,CAACC,IAAI,EAAE,KAAK,EAAE,IAAIF,MAAM,CAAC,CAAC,CAAC,CAACE,IAAI,EAAE,KAAK,EAAE,CAAC,CAC7F;IAED,IAAIH,eAAe,CAACvL,MAAM,KAAK,CAAC,EAAE;MAC9BgB,EAAE,CAACuF,oBAAoB,CAACC,OAAO,EAAE;MACjCxF,EAAE,CAACuF,oBAAoB,CAAC+E,KAAK,CAACtK,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC,CAAC;MACnF;;IAGJuN,IAAI,CAACrE,OAAO,CAAC,CAACsF,MAAM,EAAEG,KAAK,KAAI;MAC3B,IAAIC,MAAM,GAAG,EAAE;MACf,IAAI,CAACJ,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,CAACE,IAAI,EAAE,KAAK,EAAE,EAAE;QACvCE,MAAM,CAACzD,IAAI,CAACnH,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,CAAC;OAC1E,MAAM,IAAI,OAAOwO,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,CAAC,OAAO,CAACK,IAAI,CAACL,MAAM,CAAC,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,CAACxL,MAAM,GAAG,EAAE,EAAE;QAC3F4L,MAAM,CAACzD,IAAI,CAACnH,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,CAAC;OAC1E,MAAM;QACH,IAAI,CAAC6D,SAAS,CAACsH,IAAI,CAACqD,MAAM,CAAC,CAAC,CAAC,CAAC;;MAElC,IAAII,MAAM,CAAC5L,MAAM,GAAG,CAAC,EAAE;QACnB,IAAI,CAACc,YAAY,CAACqH,IAAI,CAAC,CAAC,GAAGqD,MAAM,EAAEI,MAAM,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;;IAE9D,CAAC,CAAC;IAEF,IAAI,CAACjL,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC8F,GAAG,CAACgC,IAAI,IAAIoD,MAAM,CAACpD,IAAI,CAAC+C,IAAI,EAAE,CAAC,CAAC;IAEhE,IAAI,IAAI,CAAC7K,SAAS,CAACb,MAAM,GAAG,CAAC,EAAE;MAC3BgB,EAAE,CAACX,gBAAgB,CAACsF,MAAM,CAAC;QACvBqG,QAAQ,EAAEhL,EAAE,CAACH,SAAS,CAACiL,IAAI,CAAC,GAAG,CAAC;QAChCG,UAAU,EAAErQ,SAAS,CAACmG,YAAY,CAACmK;OACtC,EAAGtE,GAAG,IAAI;QACP,IAAI+D,KAAK,GAAG,CAAC;QACb,KAAK,MAAMhD,IAAI,IAAI3H,EAAE,CAACH,SAAS,EAAE;UAC7B,IAAIsL,KAAK,GAAG,KAAK;UACjB,KAAK,MAAM5H,IAAI,IAAIqD,GAAG,CAACjG,OAAO,EAAE;YAC5B,IAAIgH,IAAI,KAAKpE,IAAI,CAACoE,IAAI,EAAE;cACpB,IAAIpE,IAAI,CAACtC,MAAM,IAAIrG,SAAS,CAACwQ,iBAAiB,CAACC,SAAS,EAAE;gBACtDrL,EAAE,CAACF,YAAY,CAACqH,IAAI,CAAC,CAAC,GAAG,EAAEQ,IAAI,GAAG,EAAE,EAAE3H,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,CAAC,CAAC;eACrG,MAAM;gBACH,IAAIgE,EAAE,CAACxB,iBAAiB,CAAC8M,QAAQ,CAAC3D,IAAI,CAAC,EAAE;kBACrC3H,EAAE,CAACF,YAAY,CAACqH,IAAI,CAAC,CAAC,GAAG,EAAEQ,IAAI,GAAG,EAAE,EAAE3H,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC,CAAC;iBACjG,MAAM;kBACHgE,EAAE,CAACxB,iBAAiB,CAAC2I,IAAI,CAACQ,IAAI,CAAC;;;cAGvCwD,KAAK,GAAG,IAAI;cACZ;;;UAGR;UACA,IAAI,CAACA,KAAK,EAAE;YACRnL,EAAE,CAACF,YAAY,CAACqH,IAAI,CAAC,CAAC,GAAG,EAAEQ,IAAI,GAAG,EAAE,EAAE3H,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC,CAAC,CAAC;;;QAGvGgE,EAAE,CAACuF,oBAAoB,CAACC,OAAO,EAAE;QACjC,IAAI,IAAI,CAAC1F,YAAY,CAACd,MAAM,GAAG,CAAC,EAAE;UAC9BgB,EAAE,CAACG,cAAc,GAAG,IAAI;SAC3B,MAAM;UACHH,EAAE,CAACuF,oBAAoB,CAACsB,OAAO,CAAE7G,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAE,CAAC;;MAEjG,CAAC,CAAC;KACL,MAAM;MACHgE,EAAE,CAACuF,oBAAoB,CAACC,OAAO,EAAE;MACjC,IAAI,IAAI,CAAC1F,YAAY,CAACd,MAAM,GAAG,CAAC,EAAE;QAC9BgB,EAAE,CAACG,cAAc,GAAG,IAAI;OAC3B,MAAM;QACHH,EAAE,CAACuF,oBAAoB,CAACsB,OAAO,CAAE7G,EAAE,CAACjE,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAE,CAAC;;;EAIrG;EAEAuP,iBAAiBA,CAAA;IACb,IAAIvL,EAAE,GAAG,IAAI;IACb,KAAK,IAAIqK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrK,EAAE,CAACF,YAAY,CAACd,MAAM,EAAEqL,CAAC,EAAE,EAAE;MAC7CrK,EAAE,CAACF,YAAY,CAACuK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACA,CAAC,GAAG,CAAC,EAAEI,QAAQ,EAAE;;IAE9C1E,OAAO,CAACC,GAAG,CAAC,IAAI,CAAClG,YAAY,CAAC;IAC9B,MAAM0L,EAAE,GAAmBxQ,IAAI,CAACmO,KAAK,CAACsC,YAAY,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,cAAc,CAAC,EAAE,GAAG,IAAI,CAAC3L,YAAY,CAAC,CAAC;IAC3G,MAAM4L,EAAE,GAAkB1Q,IAAI,CAACmO,KAAK,CAACwC,QAAQ,EAAE;IAC/C3Q,IAAI,CAACmO,KAAK,CAACyC,iBAAiB,CAACF,EAAE,EAAEF,EAAE,EAAE,QAAQ,CAAC;IAE9C,MAAMK,KAAK,GAAG7Q,IAAI,CAAC8Q,KAAK,CAACJ,EAAE,EAAE;MAACK,QAAQ,EAAE,MAAM;MAAEjL,IAAI,EAAE;IAAQ,CAAC,CAAC;IAChE,MAAMkL,GAAG,GAAG,IAAIC,WAAW,CAACJ,KAAK,CAAC7M,MAAM,CAAC;IACzC,MAAMkN,IAAI,GAAG,IAAIC,UAAU,CAACH,GAAG,CAAC;IAEhC,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,KAAK,CAAC7M,MAAM,EAAE,EAAEqL,CAAC,EAAE;MACnC6B,IAAI,CAAC7B,CAAC,CAAC,GAAGwB,KAAK,CAACO,UAAU,CAAC/B,CAAC,CAAC,GAAG,IAAI;;IAGxC,MAAMgC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACN,GAAG,CAAC,EAAE;MAAClL,IAAI,EAAE;IAA0B,CAAC,CAAC;IAChE,MAAMyL,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IAC5C,MAAMM,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACrCF,CAAC,CAACG,IAAI,GAAGP,GAAG;IACZI,CAAC,CAACI,QAAQ,GAAG,GAAG,IAAI,CAAC3L,QAAQ,kBAAkB,IAAI1B,IAAI,EAAE,CAACsN,WAAW,EAAE,CAACC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO;IACtHN,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,CAAC,CAAC;IAC5BA,CAAC,CAACU,KAAK,EAAE;IACTb,MAAM,CAACC,GAAG,CAACa,eAAe,CAACf,GAAG,CAAC;IAC/BK,QAAQ,CAACO,IAAI,CAACI,WAAW,CAACZ,CAAC,CAAC;IAC5B,IAAI,CAACxM,cAAc,GAAG,KAAK;EAC/B;EAEAqN,gBAAgBA,CAACjM,KAAK;IAClB,IAAIA,KAAK,EAAE;MACP,IAAI,CAAC5B,SAAS,GAAG4B,KAAK;KACzB,MAAM;MACH,IAAI,CAAC5B,SAAS,GAAG,IAAI;;EAE7B;EAEA8N,cAAcA,CAAClM,KAAK;IAChB,IAAIA,KAAK,EAAE;MACP,IAAI,CAAC9B,WAAW,GAAG8B,KAAK;KAC3B,MAAM;MACH,IAAI,CAAC9B,WAAW,GAAG,IAAIC,IAAI,EAAE;;EAErC;EAEA3B,UAAUA,CAACsM,CAAS;IAChB,IAAI,CAAC7L,iBAAiB,CAACkP,MAAM,CAACrD,CAAC,EAAE,CAAC,CAAC;EACvC;EAEAsD,YAAYA,CAAA;IACR,IAAI,CAACzP,YAAY,GAAG,KAAK;EAC7B;EACAkK,mBAAmBA,CAAChH,QAAgB;IAChC,MAAMwM,YAAY,GAAGxM,QAAQ,CAACyM,WAAW,CAAC,GAAG,CAAC;IAC9C,IAAID,YAAY,KAAK,CAAC,CAAC,EAAE,OAAOxM,QAAQ;IACxC,OAAOA,QAAQ,CAAC8L,SAAS,CAAC,CAAC,EAAEU,YAAY,CAAC;EAC9C;EACAE,aAAaA,CAAChG,KAAK;IACf,IAAIA,KAAK,CAAC/F,GAAG,KAAK,GAAG,KAAK,IAAI,CAAChF,MAAM,CAAC6D,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC7D,MAAM,CAAC6D,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC7D,MAAM,CAAC6D,IAAI,CAAC8J,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MAC/G5C,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAI,IAAI,CAAClL,MAAM,CAAC6D,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC7D,MAAM,CAAC6D,IAAI,CAAC8J,IAAI,EAAE,IAAI,EAAE,EAAE;MAC3D,IAAI,CAAC3N,MAAM,CAAC6D,IAAI,GAAG,IAAI,CAAC7D,MAAM,CAAC6D,IAAI,CAACmN,SAAS,EAAE,CAACd,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MACvE;;EAER;EACAe,gBAAgBA,CAAClG,KAAK;IAClB,IAAIA,KAAK,CAAC/F,GAAG,KAAK,GAAG,KAAK,IAAI,CAAChF,MAAM,CAAC4D,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC5D,MAAM,CAAC4D,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC5D,MAAM,CAAC4D,OAAO,CAAC+J,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MACxH5C,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAI,IAAI,CAAClL,MAAM,CAAC4D,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC5D,MAAM,CAAC4D,OAAO,CAAC+J,IAAI,EAAE,IAAI,EAAE,EAAE;MACjE,IAAI,CAAC3N,MAAM,CAAC4D,OAAO,GAAG,IAAI,CAAC5D,MAAM,CAAC4D,OAAO,CAACoN,SAAS,EAAE,CAACd,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MAC7E;;EAER;EACAgB,oBAAoBA,CAACnG,KAAoB,EAAElH,IAAS;IAChD,IAAIkH,KAAK,CAAC/F,GAAG,KAAK,GAAG,KAAK,CAACnB,IAAI,CAACD,OAAO,IAAIC,IAAI,CAACD,OAAO,CAAC+J,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MACpE5C,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAIrH,IAAI,CAACD,OAAO,IAAIC,IAAI,CAACD,OAAO,CAAC+J,IAAI,EAAE,KAAK,EAAE,EAAE;MAC5C9J,IAAI,CAACD,OAAO,GAAGC,IAAI,CAACD,OAAO,CAACoN,SAAS,EAAE,CAACd,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MAC/D;;EAER;EACAiB,cAAcA,CAACpG,KAAK;IAChB,IAAIA,KAAK,CAAC/F,GAAG,KAAK,GAAG,KAAK,IAAI,CAAChF,MAAM,CAAC8D,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC9D,MAAM,CAAC8D,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC9D,MAAM,CAAC8D,KAAK,CAAC6J,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;MAClH5C,KAAK,CAACG,cAAc,EAAE;;IAG1B,IAAI,IAAI,CAAClL,MAAM,CAAC8D,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC9D,MAAM,CAAC8D,KAAK,CAAC6J,IAAI,EAAE,IAAI,EAAE,EAAE;MAC7D,IAAI,CAAC3N,MAAM,CAAC8D,KAAK,GAAG,IAAI,CAAC9D,MAAM,CAAC8D,KAAK,CAACkN,SAAS,EAAE,CAACd,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;MACzE;;EAER;EACAkB,aAAaA,CAACrG,KAAK;IACf,MAAMsG,YAAY,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACzC,MAAMC,UAAU,GAAGvG,KAAK,CAACY,MAAM;IAC/B,IAAInH,KAAK,GAAG8M,UAAU,CAAC9M,KAAK;IAE5B;IACA6M,YAAY,CAAClJ,OAAO,CAACoJ,IAAI,IAAG;MACxB/M,KAAK,GAAGA,KAAK,CAACgN,KAAK,CAACD,IAAI,CAAC,CAACxD,IAAI,CAAC,EAAE,CAAC;IACtC,CAAC,CAAC;IAEF;IACA,IAAIuD,UAAU,CAAC9M,KAAK,KAAKA,KAAK,EAAE;MAC5B8M,UAAU,CAAC9M,KAAK,GAAGA,KAAK;;EAEhC;;;uBA3wBStC,4BAA4B,EAAA/D,EAAA,CAAAsT,iBAAA,CAiEzB7T,aAAa,GAAAO,EAAA,CAAAsT,iBAAA,CACb1T,cAAc,GAAAI,EAAA,CAAAsT,iBAAA,CACdvT,gBAAgB,GAAAC,EAAA,CAAAsT,iBAAA,CAAAtT,EAAA,CAAAuT,iBAAA,GAAAvT,EAAA,CAAAsT,iBAAA,CAAAE,EAAA,CAAAC,WAAA,GAAAzT,EAAA,CAAAsT,iBAAA,CAAAtT,EAAA,CAAA0T,QAAA;IAAA;EAAA;;;YAnEnB3P,4BAA4B;MAAA4P,SAAA;MAAAC,SAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAC1BjU,sBAAsB;;;;;;;;;;;;;UCnBjCG,EAAA,CAAAC,cAAA,aAAqG;UAE7DD,EAAA,CAAAqB,MAAA,GAAoD;UAAArB,EAAA,CAAAU,YAAA,EAAM;UAC9FV,EAAA,CAAAgU,SAAA,sBAAoF;UACxFhU,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,aAAwE;UACpED,EAAA,CAAAiD,UAAA,IAAAgR,gDAAA,sBAIW;UACfjU,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,cAAoG;UAA/DD,EAAA,CAAAE,UAAA,sBAAAgU,+DAAA;YAAA,OAAYH,GAAA,CAAAvJ,cAAA,EAAgB;UAAA,EAAC;UAC9DxK,EAAA,CAAAC,cAAA,iBAAoF;UAG5ED,EAAA,CAAAiD,UAAA,KAAAkR,4CAAA,iBAcM;UAiBNnU,EAAA,CAAAC,cAAA,eAAmB;UAIJD,EAAA,CAAAE,UAAA,2BAAAkU,sEAAApT,MAAA;YAAA,OAAA+S,GAAA,CAAA5S,UAAA,CAAAmE,WAAA,GAAAtE,MAAA;UAAA,EAAoC;UAF3ChB,EAAA,CAAAU,YAAA,EAKE;UACFV,EAAA,CAAAC,cAAA,iBAA6B;UAAAD,EAAA,CAAAqB,MAAA,IAAwD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAGrGV,EAAA,CAAAC,cAAA,eAAmB;UAIJD,EAAA,CAAAE,UAAA,2BAAAmU,sEAAArT,MAAA;YAAA,OAAA+S,GAAA,CAAA5S,UAAA,CAAAqE,YAAA,GAAAxE,MAAA;UAAA,EAAqC,qBAAAsT,gEAAAtT,MAAA;YAAA,OAG1B+S,GAAA,CAAApH,gBAAA,CAAA3L,MAAA,CAAwB;UAAA,EAHE;UAF5ChB,EAAA,CAAAU,YAAA,EAOE;UACFV,EAAA,CAAAC,cAAA,iBAAuB;UAAAD,EAAA,CAAAqB,MAAA,IAAiD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAGxFV,EAAA,CAAAC,cAAA,eAAmB;UAICD,EAAA,CAAAE,UAAA,2BAAAqU,2EAAAvT,MAAA;YAAA,OAAA+S,GAAA,CAAA5S,UAAA,CAAAsF,QAAA,GAAAzF,MAAA;UAAA,EAAiC,sBAAAwT,sEAAA;YAAA,OAMrBT,GAAA,CAAAzB,gBAAA,CAAAyB,GAAA,CAAA5S,UAAA,CAAAsF,QAAA,CAAqC;UAAA,EANhB,qBAAAgO,qEAAA;YAAA,OAOtBV,GAAA,CAAAzB,gBAAA,CAAAyB,GAAA,CAAA5S,UAAA,CAAAsF,QAAA,CAAqC;UAAA,EAPf;UAQ5CzG,EAAA,CAAAU,YAAA,EAAa;UACdV,EAAA,CAAAC,cAAA,iBAAiD;UAAAD,EAAA,CAAAqB,MAAA,IAAoD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAGrHV,EAAA,CAAAC,cAAA,eAAmB;UAICD,EAAA,CAAAE,UAAA,2BAAAwU,2EAAA1T,MAAA;YAAA,OAAA+S,GAAA,CAAA5S,UAAA,CAAAuF,MAAA,GAAA1F,MAAA;UAAA,EAA+B,sBAAA2T,sEAAA;YAAA,OAOnBZ,GAAA,CAAAxB,cAAA,CAAAwB,GAAA,CAAA5S,UAAA,CAAAuF,MAAA,CAAiC;UAAA,EAPd,qBAAAkO,qEAAA;YAAA,OAQpBb,GAAA,CAAAxB,cAAA,CAAAwB,GAAA,CAAA5S,UAAA,CAAAuF,MAAA,CAAiC;UAAA,EARb;UAF3C1G,EAAA,CAAAU,YAAA,EAWE;UACFV,EAAA,CAAAC,cAAA,iBAA+C;UAAAD,EAAA,CAAAqB,MAAA,IAAkD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAGjHV,EAAA,CAAAC,cAAA,eAAwB;UACpBD,EAAA,CAAAgU,SAAA,oBAGY;UAChBhU,EAAA,CAAAU,YAAA,EAAM;UAKlBV,EAAA,CAAAgU,SAAA,sBAYc;UAEdhU,EAAA,CAAAC,cAAA,eAAyC;UAGjCD,EAAA,CAAAE,UAAA,2BAAA2U,yEAAA7T,MAAA;YAAA,OAAA+S,GAAA,CAAArQ,mBAAA,GAAA1C,MAAA;UAAA,EAAiC,oBAAA8T,kEAAA;YAAA,OAC8Df,GAAA,CAAAtB,YAAA,EAAc;UAAA,EAD5E;UAEjCzS,EAAA,CAAAC,cAAA,gBAA4E;UAA7BD,EAAA,CAAAE,UAAA,sBAAA6U,gEAAA;YAAA,OAAYhB,GAAA,CAAAnJ,aAAA,EAAe;UAAA,EAAC;UACvE5K,EAAA,CAAAC,cAAA,eAAsC;UAKQD,EAAA,CAAAqB,MAAA,IAAuD;UAAArB,EAAA,CAAAU,YAAA,EAAI;UAAAV,EAAA,CAAAC,cAAA,YACxF;UAAAD,EAAA,CAAAqB,MAAA,SAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UAEbV,EAAA,CAAAiD,UAAA,KAAA+R,4CAAA,kBAiBM;UAENhV,EAAA,CAAAC,cAAA,eAA+B;UAEAD,EAAA,CAAAqB,MAAA,IAAwD;UAAArB,EAAA,CAAAC,cAAA,gBAC1D;UAAAD,EAAA,CAAAqB,MAAA,SAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UACjCV,EAAA,CAAAC,cAAA,eAAiB;UAGND,EAAA,CAAAE,UAAA,2BAAA+U,sEAAAjU,MAAA;YAAA,OAAA+S,GAAA,CAAAlS,MAAA,CAAAyD,WAAA,GAAAtE,MAAA;UAAA,EAAgC;UAFvChB,EAAA,CAAAU,YAAA,EASE;UAGVV,EAAA,CAAAC,cAAA,eAA+B;UAEAD,EAAA,CAAAqB,MAAA,IAAiD;UAAArB,EAAA,CAAAC,cAAA,gBACnD;UAAAD,EAAA,CAAAqB,MAAA,SAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UACjCV,EAAA,CAAAC,cAAA,eAAiB;UAGND,EAAA,CAAAE,UAAA,2BAAAgV,sEAAAlU,MAAA;YAAA,OAAA+S,GAAA,CAAAlS,MAAA,CAAA0D,YAAA,GAAAvE,MAAA;UAAA,EAAiC;UAFxChB,EAAA,CAAAU,YAAA,EASE;UAIVV,EAAA,CAAAC,cAAA,eAA+B;UAEAD,EAAA,CAAAqB,MAAA,IAAiD;UAAArB,EAAA,CAAAC,cAAA,gBACnD;UAAAD,EAAA,CAAAqB,MAAA,SAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UACjCV,EAAA,CAAAC,cAAA,eAAiB;UAGND,EAAA,CAAAE,UAAA,2BAAAiV,sEAAAnU,MAAA;YAAA,OAAA+S,GAAA,CAAAlS,MAAA,CAAA2D,YAAA,GAAAxE,MAAA;UAAA,EAAiC,qBAAAoU,gEAAApU,MAAA;YAAA,OAItB+S,GAAA,CAAApH,gBAAA,CAAA3L,MAAA,CAAwB;UAAA,EAJF;UAFxChB,EAAA,CAAAU,YAAA,EASE;UAIVV,EAAA,CAAAC,cAAA,eAA+B;UAEqBD,EAAA,CAAAqB,MAAA,IAAmD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UAC3GV,EAAA,CAAAC,cAAA,eAAiB;UAGND,EAAA,CAAAE,UAAA,2BAAAmV,sEAAArU,MAAA;YAAA,OAAA+S,GAAA,CAAAlS,MAAA,CAAA4D,OAAA,GAAAzE,MAAA;UAAA,EAA4B,qBAAAsU,gEAAAtU,MAAA;YAAA,OAMjB+S,GAAA,CAAAjB,gBAAA,CAAA9R,MAAA,CAAwB;UAAA,EANP;UAFnChB,EAAA,CAAAU,YAAA,EASE;UAGVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAAgU,SAAA,iBAAuE;UACvEhU,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAiD,UAAA,KAAAsS,8CAAA,oBAC2I;UAC/IvV,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAiD,UAAA,KAAAuS,8CAAA,oBAC8H;UAClIxV,EAAA,CAAAU,YAAA,EAAM;UAENV,EAAA,CAAAC,cAAA,eAA+B;UAEqBD,EAAA,CAAAqB,MAAA,IAAgD;UAAArB,EAAA,CAAAU,YAAA,EAAQ;UACxGV,EAAA,CAAAC,cAAA,eAAiB;UAGND,EAAA,CAAAE,UAAA,2BAAAuV,sEAAAzU,MAAA;YAAA,OAAA+S,GAAA,CAAAlS,MAAA,CAAA6D,IAAA,GAAA1E,MAAA;UAAA,EAAyB,qBAAA0U,gEAAA1U,MAAA;YAAA,OAMd+S,GAAA,CAAAnB,aAAA,CAAA5R,MAAA,CAAqB;UAAA,EANP;UAFhChB,EAAA,CAAAU,YAAA,EASE;UAGVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAAgU,SAAA,iBAAoE;UACpEhU,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAiD,UAAA,KAAA0S,8CAAA,oBACwI;UACxI3V,EAAA,CAAAiD,UAAA,KAAA2S,8CAAA,oBAC2H;UAC/H5V,EAAA,CAAAU,YAAA,EAAM;UAIlBV,EAAA,CAAAC,cAAA,eAA+B;UAKeD,EAAA,CAAAqB,MAAA,IAAqD;UAAArB,EAAA,CAAAU,YAAA,EAAI;UAAAV,EAAA,CAAAC,cAAA,gBAC9D;UAAAD,EAAA,CAAAqB,MAAA,SAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UAAAV,EAAA,CAAAC,cAAA,YAChC;UAAAD,EAAA,CAAAqB,MAAA,SAAC;UAAArB,EAAA,CAAAU,YAAA,EAAO;UAEbV,EAAA,CAAAC,cAAA,eAA+B;UAEvBD,EAAA,CAAAE,UAAA,yBAAA2V,2EAAA7U,MAAA;YAAA,OAAA+S,GAAA,CAAAzQ,iBAAA,GAAAtC,MAAA;UAAA,EAA6B;UAShChB,EAAA,CAAAU,YAAA,EAAc;UACfV,EAAA,CAAAC,cAAA,YAAK;UACSD,EAAA,CAAAE,UAAA,qBAAA4V,oEAAA;YAAA,OAAA/B,GAAA,CAAA/Q,YAAA,GAA0B,IAAI;UAAA,EAAC;UAEmChD,EAAA,CAAAU,YAAA,EAAW;UAKnGV,EAAA,CAAAiD,UAAA,MAAA8S,6CAAA,mBA4BM;UAEV/V,EAAA,CAAAU,YAAA,EAAM;UAGdV,EAAA,CAAAC,cAAA,gBAA0E;UAE5DD,EAAA,CAAAE,UAAA,mBAAA8V,kEAAA;YAAAjC,GAAA,CAAArQ,mBAAA,GAA+B,KAAK;YAAA,OAAAqQ,GAAA,CAAA/Q,YAAA,GAAiB,KAAK;UAAA,EAAC;UAAkChD,EAAA,CAAAU,YAAA,EAAW;UAClHV,EAAA,CAAAiD,UAAA,MAAAgT,kDAAA,uBAG2E;UAC/EjW,EAAA,CAAAU,YAAA,EAAM;UAGdV,EAAA,CAAAC,cAAA,qBAC8F;UADpFD,EAAA,CAAAE,UAAA,2BAAAgW,0EAAAlV,MAAA;YAAA,OAAA+S,GAAA,CAAA9O,cAAA,GAAAjE,MAAA;UAAA,EAA4B;UAElChB,EAAA,CAAAC,cAAA,gBAAiE;UAEzDD,EAAA,CAAAgU,SAAA,cAC2C;UAE3ChU,EAAA,CAAAC,cAAA,cAA0D;UAAAD,EAAA,CAAAqB,MAAA,KAA2D;UAAArB,EAAA,CAAAU,YAAA,EAAI;UACzHV,EAAA,CAAAC,cAAA,cAA0D;UAAAD,EAAA,CAAAqB,MAAA,KAAoE;UAAArB,EAAA,CAAAU,YAAA,EAAI;UAClIV,EAAA,CAAAC,cAAA,qBAC8E;UADpED,EAAA,CAAAE,UAAA,qBAAAiW,oEAAA;YAAA,OAAWpC,GAAA,CAAA1D,iBAAA,EAAmB;UAAA,EAAC;UACqCrQ,EAAA,CAAAU,YAAA,EAAW;UAIrGV,EAAA,CAAAC,cAAA,qBAAsG;UAA5FD,EAAA,CAAAE,UAAA,2BAAAkW,0EAAApV,MAAA;YAAA,OAAA+S,GAAA,CAAA/Q,YAAA,GAAAhC,MAAA;UAAA,EAA0B;UAChChB,EAAA,CAAAC,cAAA,gBAAoC;UAEID,EAAA,CAAAE,UAAA,8BAAAmW,oFAAArV,MAAA;YAAA,OAAA+S,GAAA,CAAAuC,UAAA,GAAAtV,MAAA;UAAA,EAA2B;UAE1DhB,EAAA,CAAAU,YAAA,EAAkB;UAEvBV,EAAA,CAAAC,cAAA,gBAAwE;UAERD,EAAA,CAAAE,UAAA,mBAAAqW,kEAAA;YAAA,OAASxC,GAAA,CAAA/G,gBAAA,EAAkB;UAAA,EAAC;UAAChN,EAAA,CAAAU,YAAA,EAAW;;;UA1WxEV,EAAA,CAAAsB,SAAA,GAAoD;UAApDtB,EAAA,CAAAyB,iBAAA,CAAAsS,GAAA,CAAAlT,WAAA,CAAAC,SAAA,0BAAoD;UACjDd,EAAA,CAAAsB,SAAA,GAAe;UAAftB,EAAA,CAAAW,UAAA,UAAAoT,GAAA,CAAAyC,KAAA,CAAe,SAAAzC,GAAA,CAAA0C,IAAA;UAI3CzW,EAAA,CAAAsB,SAAA,GAA8F;UAA9FtB,EAAA,CAAAW,UAAA,SAAAoT,GAAA,CAAAhP,QAAA,CAAAa,IAAA,IAAAmO,GAAA,CAAA7O,QAAA,CAAAkH,QAAA,IAAA2H,GAAA,CAAA2C,WAAA,CAAA1W,EAAA,CAAA2W,eAAA,MAAAC,GAAA,EAAA7C,GAAA,CAAArU,SAAA,CAAAmX,WAAA,CAAAC,MAAA,CAAAC,MAAA,GAA8F;UAO3G/W,EAAA,CAAAsB,SAAA,GAA8B;UAA9BtB,EAAA,CAAAW,UAAA,cAAAoT,GAAA,CAAAnL,gBAAA,CAA8B;UACvB5I,EAAA,CAAAsB,SAAA,GAAmB;UAAnBtB,EAAA,CAAAW,UAAA,oBAAmB,WAAAoT,GAAA,CAAAlT,WAAA,CAAAC,SAAA;UAGdd,EAAA,CAAAsB,SAAA,GAA+C;UAA/CtB,EAAA,CAAAW,UAAA,SAAAoT,GAAA,CAAAhP,QAAA,CAAAa,IAAA,IAAAmO,GAAA,CAAA7O,QAAA,CAAA+B,KAAA,CAA+C;UAmCtCjH,EAAA,CAAAsB,SAAA,GAAoC;UAApCtB,EAAA,CAAAW,UAAA,YAAAoT,GAAA,CAAA5S,UAAA,CAAAmE,WAAA,CAAoC;UAIdtF,EAAA,CAAAsB,SAAA,GAAwD;UAAxDtB,EAAA,CAAAyB,iBAAA,CAAAsS,GAAA,CAAAlT,WAAA,CAAAC,SAAA,8BAAwD;UAO9Ed,EAAA,CAAAsB,SAAA,GAAqC;UAArCtB,EAAA,CAAAW,UAAA,YAAAoT,GAAA,CAAA5S,UAAA,CAAAqE,YAAA,CAAqC;UAMrBxF,EAAA,CAAAsB,SAAA,GAAiD;UAAjDtB,EAAA,CAAAyB,iBAAA,CAAAsS,GAAA,CAAAlT,WAAA,CAAAC,SAAA,uBAAiD;UAO5Dd,EAAA,CAAAsB,SAAA,GAAiC;UAAjCtB,EAAA,CAAAW,UAAA,YAAAoT,GAAA,CAAA5S,UAAA,CAAAsF,QAAA,CAAiC,iDAAAsN,GAAA,CAAAxP,WAAA;UASIvE,EAAA,CAAAsB,SAAA,GAAoD;UAApDtB,EAAA,CAAAyB,iBAAA,CAAAsS,GAAA,CAAAlT,WAAA,CAAAC,SAAA,0BAAoD;UAOzFd,EAAA,CAAAsB,SAAA,GAA+B;UAA/BtB,EAAA,CAAAW,UAAA,YAAAoT,GAAA,CAAA5S,UAAA,CAAAuF,MAAA,CAA+B,iDAAAqN,GAAA,CAAAtP,SAAA,aAAAsP,GAAA,CAAArP,SAAA;UAUI1E,EAAA,CAAAsB,SAAA,GAAkD;UAAlDtB,EAAA,CAAAyB,iBAAA,CAAAsS,GAAA,CAAAlT,WAAA,CAAAC,SAAA,wBAAkD;UAcjHd,EAAA,CAAAsB,SAAA,GAAmC;UAAnCtB,EAAA,CAAAW,UAAA,oCAAmC,uCAAAoT,GAAA,CAAApN,OAAA,aAAAoN,GAAA,CAAArL,OAAA,aAAAqL,GAAA,CAAAnM,WAAA,gBAAAmM,GAAA,CAAAxL,UAAA,cAAAwL,GAAA,CAAAtK,MAAA,CAAAH,IAAA,CAAAyK,GAAA,eAAAA,GAAA,CAAAvL,QAAA,UAAAuL,GAAA,CAAAtL,IAAA,YAAAsL,GAAA,CAAA5S,UAAA,gBAAA4S,GAAA,CAAAlT,WAAA,CAAAC,SAAA;UAiBhBd,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAgX,UAAA,CAAAhX,EAAA,CAAAgC,eAAA,MAAAiV,GAAA,EAA6B;UAF5CjX,EAAA,CAAAW,UAAA,WAAAoT,GAAA,CAAAzR,WAAA,aAAAyR,GAAA,CAAAlT,WAAA,CAAAC,SAAA,iCAAAiT,GAAA,CAAAlT,WAAA,CAAAC,SAAA,kCAA8I,YAAAiT,GAAA,CAAArQ,mBAAA;UAG3H1D,EAAA,CAAAsB,SAAA,GAA2B;UAA3BtB,EAAA,CAAAW,UAAA,cAAAoT,GAAA,CAAAnQ,aAAA,CAA2B;UAMI5D,EAAA,CAAAsB,SAAA,GAAuD;UAAvDtB,EAAA,CAAAyB,iBAAA,CAAAsS,GAAA,CAAAlT,WAAA,CAAAC,SAAA,6BAAuD;UAGzDd,EAAA,CAAAsB,SAAA,GAAwE;UAAxEtB,EAAA,CAAAW,UAAA,SAAAoT,GAAA,CAAAhP,QAAA,CAAAa,IAAA,IAAAmO,GAAA,CAAA7O,QAAA,CAAA+B,KAAA,IAAA8M,GAAA,CAAAzR,WAAA,WAAwE;UAqBzEtC,EAAA,CAAAsB,SAAA,GAAwD;UAAxDtB,EAAA,CAAAyB,iBAAA,CAAAsS,GAAA,CAAAlT,WAAA,CAAAC,SAAA,8BAAwD;UAKxEd,EAAA,CAAAsB,SAAA,GAAgC;UAAhCtB,EAAA,CAAAW,UAAA,YAAAoT,GAAA,CAAAlS,MAAA,CAAAyD,WAAA,CAAgC,mDAAAyO,GAAA,CAAAlT,WAAA,CAAAC,SAAA;UAYhBd,EAAA,CAAAsB,SAAA,GAAiD;UAAjDtB,EAAA,CAAAyB,iBAAA,CAAAsS,GAAA,CAAAlT,WAAA,CAAAC,SAAA,uBAAiD;UAKjEd,EAAA,CAAAsB,SAAA,GAAiC;UAAjCtB,EAAA,CAAAW,UAAA,YAAAoT,GAAA,CAAAlS,MAAA,CAAA0D,YAAA,CAAiC,mDAAAwO,GAAA,CAAAlT,WAAA,CAAAC,SAAA;UAajBd,EAAA,CAAAsB,SAAA,GAAiD;UAAjDtB,EAAA,CAAAyB,iBAAA,CAAAsS,GAAA,CAAAlT,WAAA,CAAAC,SAAA,uBAAiD;UAKjEd,EAAA,CAAAsB,SAAA,GAAiC;UAAjCtB,EAAA,CAAAW,UAAA,YAAAoT,GAAA,CAAAlS,MAAA,CAAA2D,YAAA,CAAiC,kCAAAuO,GAAA,CAAAlT,WAAA,CAAAC,SAAA;UAaId,EAAA,CAAAsB,SAAA,GAAmD;UAAnDtB,EAAA,CAAAyB,iBAAA,CAAAsS,GAAA,CAAAlT,WAAA,CAAAC,SAAA,yBAAmD;UAIxFd,EAAA,CAAAsB,SAAA,GAA4B;UAA5BtB,EAAA,CAAAW,UAAA,YAAAoT,GAAA,CAAAlS,MAAA,CAAA4D,OAAA,CAA4B,aAAAsO,GAAA,CAAAzR,WAAA,cAAAyR,GAAA,CAAA/Q,YAAA,iBAAA+Q,GAAA,CAAAlT,WAAA,CAAAC,SAAA,sCAAAiT,GAAA,CAAAlS,MAAA,CAAA4D,OAAA;UAc3BzF,EAAA,CAAAsB,SAAA,GAAsD;UAAtDtB,EAAA,CAAAW,UAAA,SAAAoT,GAAA,CAAAnQ,aAAA,CAAAsT,QAAA,CAAAzR,OAAA,CAAAiK,MAAA,kBAAAqE,GAAA,CAAAnQ,aAAA,CAAAsT,QAAA,CAAAzR,OAAA,CAAAiK,MAAA,CAAAyH,SAAA,CAAsD;UAG1DnX,EAAA,CAAAsB,SAAA,GAAoD;UAApDtB,EAAA,CAAAW,UAAA,SAAAoT,GAAA,CAAAnQ,aAAA,CAAAsT,QAAA,CAAAzR,OAAA,CAAAiK,MAAA,kBAAAqE,GAAA,CAAAnQ,aAAA,CAAAsT,QAAA,CAAAzR,OAAA,CAAAiK,MAAA,CAAA0H,OAAA,CAAoD;UAKZpX,EAAA,CAAAsB,SAAA,GAAgD;UAAhDtB,EAAA,CAAAyB,iBAAA,CAAAsS,GAAA,CAAAlT,WAAA,CAAAC,SAAA,sBAAgD;UAIrFd,EAAA,CAAAsB,SAAA,GAAyB;UAAzBtB,EAAA,CAAAW,UAAA,YAAAoT,GAAA,CAAAlS,MAAA,CAAA6D,IAAA,CAAyB,aAAAqO,GAAA,CAAAzR,WAAA,cAAAyR,GAAA,CAAA/Q,YAAA,iBAAA+Q,GAAA,CAAAlT,WAAA,CAAAC,SAAA,mCAAAiT,GAAA,CAAAlS,MAAA,CAAA6D,IAAA;UAcxB1F,EAAA,CAAAsB,SAAA,GAAmD;UAAnDtB,EAAA,CAAAW,UAAA,SAAAoT,GAAA,CAAAnQ,aAAA,CAAAsT,QAAA,CAAAxR,IAAA,CAAAgK,MAAA,kBAAAqE,GAAA,CAAAnQ,aAAA,CAAAsT,QAAA,CAAAxR,IAAA,CAAAgK,MAAA,CAAAyH,SAAA,CAAmD;UAEnDnX,EAAA,CAAAsB,SAAA,GAAiD;UAAjDtB,EAAA,CAAAW,UAAA,SAAAoT,GAAA,CAAAnQ,aAAA,CAAAsT,QAAA,CAAAxR,IAAA,CAAAgK,MAAA,kBAAAqE,GAAA,CAAAnQ,aAAA,CAAAsT,QAAA,CAAAxR,IAAA,CAAAgK,MAAA,CAAA0H,OAAA,CAAiD;UAOrCpX,EAAA,CAAAsB,SAAA,GAA+C;UAA/CtB,EAAA,CAAAoD,UAAA,CAAA2Q,GAAA,CAAAzR,WAAA,2BAA+C;UAGrCtC,EAAA,CAAAsB,SAAA,GAAqD;UAArDtB,EAAA,CAAAyB,iBAAA,CAAAsS,GAAA,CAAAlT,WAAA,CAAAC,SAAA,2BAAqD;UAM/Ed,EAAA,CAAAsB,SAAA,GAA6B;UAA7BtB,EAAA,CAAAW,UAAA,UAAAoT,GAAA,CAAAzQ,iBAAA,CAA6B,gBAAAyQ,GAAA,CAAAlT,WAAA,CAAAC,SAAA,kEAAAd,EAAA,CAAAgC,eAAA,MAAAqV,GAAA,eAAAtD,GAAA,CAAA/Q,YAAA;UAYnBhD,EAAA,CAAAsB,SAAA,GAAgD;UAAhDtB,EAAA,CAAAW,UAAA,WAAAoT,GAAA,CAAAzR,WAAA,2BAAgD,UAAAyR,GAAA,CAAAlT,WAAA,CAAAC,SAAA;UAMhCd,EAAA,CAAAsB,SAAA,GAAuC;UAAvCtB,EAAA,CAAAW,UAAA,SAAAoT,GAAA,CAAAzQ,iBAAA,CAAAQ,MAAA,KAAuC;UAkCtC9D,EAAA,CAAAsB,SAAA,GAAuD;UAAvDtB,EAAA,CAAAW,UAAA,UAAAoT,GAAA,CAAAlT,WAAA,CAAAC,SAAA,yBAAuD,WAAAiT,GAAA,CAAAzR,WAAA;UAElDtC,EAAA,CAAAsB,SAAA,GAAwD;UAAxDtB,EAAA,CAAAW,UAAA,SAAAoT,GAAA,CAAA2C,WAAA,CAAA1W,EAAA,CAAA2W,eAAA,MAAAC,GAAA,EAAA7C,GAAA,CAAArU,SAAA,CAAAmX,WAAA,CAAAC,MAAA,CAAAC,MAAA,GAAwD;UAQ/F/W,EAAA,CAAAsB,SAAA,GAA4B;UAA5BtB,EAAA,CAAAgX,UAAA,CAAAhX,EAAA,CAAAgC,eAAA,MAAAsV,GAAA,EAA4B;UAD3CtX,EAAA,CAAAW,UAAA,YAAAoT,GAAA,CAAA9O,cAAA,CAA4B;UAOgCjF,EAAA,CAAAsB,SAAA,GAA2D;UAA3DtB,EAAA,CAAAuX,kBAAA,KAAAxD,GAAA,CAAAlT,WAAA,CAAAC,SAAA,gCAA2D;UAC3Dd,EAAA,CAAAsB,SAAA,GAAoE;UAApEtB,EAAA,CAAAyB,iBAAA,CAAAsS,GAAA,CAAAlT,WAAA,CAAAC,SAAA,qCAAoE;UAEpHd,EAAA,CAAAsB,SAAA,GAAmE;UAAnEtB,EAAA,CAAAW,UAAA,UAAAoT,GAAA,CAAAlT,WAAA,CAAAC,SAAA,gCAAmE;UAI/Ed,EAAA,CAAAsB,SAAA,GAA0B;UAA1BtB,EAAA,CAAAW,UAAA,YAAAoT,GAAA,CAAA/Q,YAAA,CAA0B,WAAA+Q,GAAA,CAAAlT,WAAA,CAAAC,SAAA;UAGQd,EAAA,CAAAsB,SAAA,GAA2B;UAA3BtB,EAAA,CAAAW,UAAA,eAAAoT,GAAA,CAAAuC,UAAA,CAA2B,YAAAvC,GAAA,CAAAjL,eAAA;UAK3B9I,EAAA,CAAAsB,SAAA,GAAgE;UAAhEtB,EAAA,CAAAW,UAAA,aAAAoT,GAAA,CAAAlT,WAAA,CAAAC,SAAA,+BAAgE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}