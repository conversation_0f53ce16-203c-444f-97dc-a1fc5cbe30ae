<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round mb-2">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.listGroupSub")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button [label]="tranService.translate('global.button.add')"
                  (click)="createGroupSub()"
                  *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.SHARE_GROUP.CREATE])"
                  styleClass="p-button-info mr-2"></p-button>
    </div>
</div>


<p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
    <div class="grid search-grid-4">

        <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="groupCode"
                           [(ngModel)]="searchInfo.groupCode"
                           (keyup.enter)="onSubmitSearch()"
                    />
                    <label htmlFor="imei">{{tranService.translate("datapool.label.groupCode")}}</label>
                </span>
        </div>

        <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="groupName"
                           [(ngModel)]="searchInfo.groupName"
                           (keyup.enter)="onSubmitSearch()"
                    />
                    <label htmlFor="groupName">{{tranService.translate("datapool.label.groupName")}}</label>
                </span>
        </div>

        <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="description"
                           [(ngModel)]="searchInfo.description"
                           (keyup.enter)="onSubmitSearch()"
                    />
                    <label htmlFor="description">{{tranService.translate("datapool.label.description")}}</label>
                </span>
        </div>

        <div class="col-3 pb-0">
            <p-button icon="pi pi-search"
                      styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                      (click)="onSubmitSearch()"
            ></p-button>
        </div>
    </div>
</p-panel>
<table-vnpt
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.listGroupSub')"
></table-vnpt>

<div class="flex justify-content-center dialog-vnpt">
    <p-dialog
        [header]="tranService.translate('datapool.label.detailGroup')"
        [(visible)]="isShowPopupDetail"
        [modal]="true"
        [style]="{ width: '980px' }"
        [draggable]="false"
        [resizable]="false"
    >
        <div class="mt-3">
            <p-card>
                <div class="text-black-alpha-90 font-medium">{{tranService.translate("datapool.label.generalInfo")}}</div>
                <div class="gap-4 mt-3 px-2 mb-0">
                    <div class="flex flex-column gap-2 flex-1">
                        <label htmlFor="groupCode" class="my-auto" style="min-width: 110px;">{{tranService.translate("groupSim.label.groupKey")}}<span class="text-red-500">*</span></label>
                        <input
                            pInputText
                            id="groupKey"
                            type="text"
                            [placeholder]="tranService.translate('groupSim.placeHolder.groupKey')"
                            [(ngModel)]="groupInfo.groupCode"
                            disabled
                        />
                    </div>
                    <div class="flex flex-column gap-2 flex-1 mt-3">
                        <label htmlFor="groupName" class="my-auto" style="min-width: 110px;">{{tranService.translate("groupSim.label.groupName")}}<span class="text-red-500">*</span></label>
                        <input
                            pInputText
                            id="groupName"
                            type="text"
                            class="w-full"
                            [placeholder]="tranService.translate('groupSim.placeHolder.groupName')"
                            [(ngModel)]="groupInfo.groupName"
                            disabled
                        />
                    </div>
                </div>
                <div class="w-full mt-3 px-2">
                    <div class="flex flex-column gap-2 flex-1">
                        <label htmlFor="description">{{tranService.translate("datapool.label.description")}}</label>
                        <textarea
                            class="w-full" style="resize: none;"
                            rows="5"
                            [autoResize]="false"
                            pInputTextarea id="description"
                            [placeholder]="tranService.translate('sim.text.inputDescription')"
                            [(ngModel)]="groupInfo.description"
                            disabled
                        ></textarea>
                    </div>
                </div>
            </p-card>
        </div>

        <div class="mt-3">
            <div class="flex flex-row justify-content-start gap-3 mb-3">
                <input style="min-width: 20vw"  type="text" pInputText [placeholder]="tranService.translate('sim.label.quickSearch')" (keydown.enter)="onQuickSearch()" [(ngModel)]="valueSearch" [ngModelOptions]="{standalone: true}">
                <p-button icon="pi pi-search"
                          styleClass="ml-3 p-button-rounded p-button-secondary p-button-text button-search"
                          type="button"
                          (click)="onQuickSearch()"
                ></p-button>
            </div>
            <p-card>
                <div class="text-black-alpha-90 font-medium">{{tranService.translate("datapool.label.listSubOfGroup")}}</div>
<!--                <div class="mt-5 flex flex-row gap-3 justify-content-between">-->
<!--                    <div class="flex flex-row gap-3">-->
<!--                        <div class="relative flex">-->
<!--                            <vnpt-select-->
<!--                                [(value)]="phoneReceiptSelect"-->
<!--                                (onchange)="checkValidAdd()"-->
<!--                                (onSelectItem)="addPhone(phoneReceiptSelect)"-->
<!--                                [isAutoComplete]="true"-->
<!--                                [isMultiChoice]="false"-->
<!--                                paramKey="phoneReceipt"-->
<!--                                keyReturn="phoneReceipt"-->
<!--                                [lazyLoad]="false"-->
<!--                                [placeholder]="tranService.translate('datapool.label.receiverPhone')"-->
<!--                                displayPattern="${phoneReceipt}"-->
<!--                                [isFilterLocal]="true"-->
<!--                                [loadData]="loadSimNotInGroup.bind(this)"-->
<!--                            ></vnpt-select>-->
<!--                        </div>-->
<!--                        <button [disabled]="isClickAdd || !isValidPhone" type="button" pButton [label]="tranService.translate('datapool.button.add')" (click)="addPhone(phoneReceiptSelect)"></button>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div class="mb-5 flex flex-row gap-3 justify-content-between text-red-500 px-1">-->
<!--                    <div *ngIf="!isValidPhone">-->
<!--                        {{tranService.translate("datapool.message.digitError")}}-->
<!--                    </div>-->
<!--                </div>-->

                <table-vnpt
                    [tableId]="'tableSubInGroupDetail'"
                    [fieldId]="'idGroup'"
                    [(selectItems)]="selectItemsSub"
                    [columns]="columnsSub"
                    [dataSet]="dataSetSub"
                    [loadData]="searchSub.bind(this)"
                    [pageNumber]="pageNumberSub"
                    [pageSize]="pageSizeSub"
                    [sort]="sortSub"
                    [params]="searchInfoSub"
                    [options]="optionTableSub"
                ></table-vnpt>
            </p-card>
        </div>
    </p-dialog>
</div>
