<!-- <ul class="layout-menu">
    <ng-container *ngFor="let item of model; let i = index;">
        <li app-menuitem *ngIf="!item.separator" [item]="item" [index]="i" [root]="true"></li>
        <li *ngIf="item.separator" class="menu-separator"></li>
    </ng-container>
</ul> -->

<!-- <div *ngIf="layoutService.typeMenu == 'big'" (click)="layoutService.changeSize('small')" class="button-change-type-menu" [class]="layoutService.isShowMenu == false ? 'hidden': ''"><div class="caret-left"></div></div> -->
<!-- <div *ngIf="layoutService.typeMenu == 'small'" (click)="layoutService.changeSize('big')" class="button-change-type-menu" [class]="layoutService.isShowMenu == false ? 'hidden': ''"><div class="caret-right"></div></div> -->

<div *ngIf="layoutService.typeMenu == 'big'" class="relative">
    <div style=
    "color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 9999;
    outline: 0 none;
    cursor: pointer;
    padding: 0.75rem 2rem;
    background-color: #021c34;
    transition: background-color 0.2s, box-shadow 0.2s;
    box-shadow: -5px 12px 19px 0px rgba(0,0,0,0.51);
    -webkit-box-shadow: -5px 12px 19px 0px rgba(0,0,0,0.51);
    -moz-box-shadow: -5px 12px 19px 0px rgba(0,0,0,0.51);
    min-height: 60px;" class="flex flex-row text-lg menuItemExpanded" id="menuToggleBlock">
        <div class="text-lg font-semibold">Menu</div>
        <div *ngIf="layoutService.typeMenu == 'big'"
        (click)="layoutService.changeSize('small')"
        style=
        "background-color: #1c3349;
        padding: 5px;
        width: 30px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;"><i class="pi pi-angle-left"></i></div>
        <!-- <div *ngIf="layoutService.typeMenu == 'big'" (click)="layoutService.changeSize('small')" class="button-change-type-menu" [class]="layoutService.isShowMenu == false ? 'hidden': ''"><div class="caret-left"></div></div> -->
    </div>
    <div class="menu-big-list">
        <ul class="layout-menu flex-grow-1" >
            <ng-container *ngFor="let item of model; let i = index;">
                <li class="text-lg menuItemExpanded" app-menuitem *ngIf="!item.separator" [item]="item" [index]="i" [root]="true"></li>
                <li class="text-lg menuItemExpanded" *ngIf="item.separator" class="menu-separator"></li>
            </ng-container>
        </ul>
<!--        <div style="height: 90px;"></div>-->
    </div>
<!--    Chuyển hotline sang bên phải màn-->
<!--    <div class="align-items-end" style=" position: sticky;-->
<!--        bottom: 0;-->
<!--        left: 0;-->
<!--        right: 0;-->
<!--        background-color: #021c34;-->
<!--        color: white;-->
<!--        text-align: center;-->
<!--        padding: 10px;-->
<!--        font-weight: bold;-->
<!--        box-shadow: 0 -5px 10px rgba(0,0,0,0.3);-->
<!--        -webkit-box-shadow: -5px -12px 19px 0px rgba(0,0,0,0.51);-->
<!--        -moz-box-shadow: -5px -12px 19px 0px rgba(0,0,0,0.51);">-->
<!--        <div class="flex flex-row justify-content-center align-content-center align-items-center w-full">-->
<!--            <i class="fas fa-phone-volume" style="font-size: 20px; margin-right: 8px;"></i>-->
<!--            <div style="font-size: 15px; font-weight: bold;">Hotline: 1800 1091</div>-->
<!--        </div>-->
<!--    </div>-->
<!--    <div class="font-footer-sbar" style="height: 200px; padding: 15px; color:#959EAD; box-shadow: -5px -12px 19px 0px rgba(0,0,0,0.51);-->
<!--    -webkit-box-shadow: -5px -12px 19px 0px rgba(0,0,0,0.51);-->
<!--    -moz-box-shadow: -5px -12px 19px 0px rgba(0,0,0,0.51);">-->
<!--        <div class="flex flex-row justify-content-start gap-2">-->
<!--            <i class="pi pi-phone"></i>-->
<!--            <div class="font-footer-sbar">(+84) 38362094</div>-->
<!--        </div>-->
<!--        <div class="flex flex-row justify-content-start gap-2 mt-2">-->
<!--            <i class="pi pi-map-marker"></i>-->
<!--            <div class="font-footer-sbar">124 Hoang Quoc Viet, Cau Giay, Ha Noi</div>-->
<!--        </div>-->
        <!-- <div class="flex flex-row justify-content-between">
            <i class="pi pi-user"></i>
            <div>1.</div>
        </div> -->
<!--        <div style="width: 100%; height: 1; border-bottom: 1px solid #2a2f35;" class="pt-2"></div>-->
<!--        <div class="pt-2 font-footer-sbar">CMP by VNPT-Technology</div>-->
<!--    </div>-->
</div>
<div class="menuItemCollaped" *ngIf="layoutService.typeMenu == 'small'">
    <div style="display: flex; padding: 5px;
    box-shadow: -5px 12px 19px 0px rgba(0,0,0,0.51);
    -webkit-box-shadow: -5px 12px 19px 0px rgba(0,0,0,0.51);
    -moz-box-shadow: -5px 12px 19px 0px rgba(0,0,0,0.51);"class="justify-content-center align-items-center">
        <div
            (click)="handleOpenBigSidebar()"
            style=
            "background-color: #1c3349;
            color: white;
            padding: 5px;
            width: 30px;
            height: 30px;
            cursor: pointer;
            display: flex;
            justify-content: center;
            margin: 10px 0;
            align-items: center;
            border-radius: 50%;"><i class="pi pi-angle-right"></i>
        </div>
    </div>
    <!-- <p-tieredMenu [model]="model[0].itemsNoLabel" appendTo="body"></p-tieredMenu> -->
    <div (scroll)="handleScroll($event)" class="menu-item-small flex flex-column align-items-center mt-3"
    style="overflow-y: auto;
    width: 60px ;">
        <div *ngFor="let category of model">
          <div class="small-menu-category">
              <div *ngFor="let item of category.items" appSmallSidebarElement
              [itemInfo]="item">
                <div [ngClass]="{'active-route-small': isActiveRoute(item)}">
                    <i *ngIf="item.visible != false" class="{{item.icon}} icon-small-menu mb-3" styleClass="icon-small-menu">
                    </i>
                </div>
              </div>
          </div>
        </div>
      </div>
    <div style="height: 200px; box-shadow: -5px -12px 19px 0px rgba(0,0,0,0.51);
    -webkit-box-shadow: -5px -12px 19px 0px rgba(0,0,0,0.51);
    -moz-box-shadow: -5px -12px 19px 0px rgba(0,0,0,0.51);"></div>
</div>
<!-- <p-tieredMenu *ngIf="layoutService.typeMenu == 'small'" [model]="model[0].itemsNoLabel" appendTo="body"></p-tieredMenu> -->
<!-- <p-tooltip for="dashboard" [value]="transService.translate('global.menu.dashboard')"/> -->
