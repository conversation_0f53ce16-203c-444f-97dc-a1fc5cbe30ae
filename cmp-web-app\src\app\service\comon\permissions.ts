export default {
    ACCOUNT: {
        VIEW_LIST: "searchUser",
        VIEW_DETAIL: "getUser",
        CREATE: "createUser",
        UPDATE: "updateUser",
        DELETE: "deleteUser",
        CHANGE_STATUS: "updateUser",
        CHANGE_MANAGER_DATA: "changeManageData",
        SEARCH_LOG_API:"searchLogApi"
    },
    PROFILE: {
        VIEW: "getProfileUser",
        UPDATE: "updateProfile",
    },
    ROLE: {
        VIEW_LIST: "searchRole",
        VIEW_DETAIL: "getRole",
        CREATE: "createRole",
        UPDATE: "updateRole",
        DELETE: "",
        CHANGE_STATUS: ""
    },
    PERMISSION: {
        VIEW_LIST: "searchPermission",
        VIEW_DETAIL: "getPermission",
    },
    SIM: {
        VIEW_LIST: "searchSim",
        VIEW_DETAIL: "getSim",
        UPDATE: "updateSim",
        CHANGE_STATUS: "updateSim",
        CREATE: "createSim",
        DELETE : "deleteSim",
    },
    GROUP_SIM: {
        CREATE: "createSimGroup",
        UPDATE: "updateSimGroup",
        VIEW_LIST:"searchSimGroup",
        VIEW_DETAIL:"getSimGroup",
        DELETE:"deleteSimGroup"
    },
    CONTRACT: {
        VIEW_LIST: "searchContract",
        VIEW_DETAIL: "getContract"
    },
    RATING_PLAN: {
        ACTIVE: "changeStatusRatingPlan",
        SUPPEND: "changeStatusRatingPlan",
        APPROVE: "approveRatingPlan",
        CREATE: "createRatingPlan",
        DELETE: "deleteRatingPlan",
        ISSUE: "issueRatingPlan",
        VIEW_LIST: "searchRatingPlan",
        VIEW_DETAIL: "getRatingPlan",
        UPDATE: "updateRatingPlan"
    },
    RATING_PLAN_SIM: {
        REGISTER_PLAN: "setRateSim",
        CANCEL_PLAN: "cancelRateSim",
        REGISTER_BY_FILE: "registerByFile",
        REGISTER_BY_LIST: "registerByList",
        REGISTER_HISTORY: "ratingPlanRegistrationHistory"
    },
    REPORT_DYNAMIC: {
        VIEW_LIST: "searchRptCfg",
        VIEW_DETAIL: "getRptCfg",
        CREATE: "createRptCfg",
        UPDATE:"updateRptCfg",
        DELETE: "deleteRptCfg",
    },
    REPORT_SEND: {
        UPDATE: "updateRptSend"
    },
    GROUP_REPORT_DYNAMIC: {
        VIEW_LIST: "searchRptRecvGrp",
        VIEW_DETAIL: "getRptRecvGrp",
        CREATE: "createRptRecvGrp",
        UPDATE:"updateRptRecvGrp",
        DELETE: "deleteRptRecvGrp",
    },
    CUSTOMER: {
        VIEW_LIST:"searchCustomer",
        VIEW_DETAIL:"getCustomer",
        UPDATE:"updateCustomer",
        CREATE:"createCustomer",
        DELETE:"deleteCustomer"
    },
    ALERT: {
        VIEW_LIST: "searchAlert",
        VIEW_DETAIL: "getAlert",
        CREATE: "createAlert",
        UPDATE: "updateAlert",
        DELETE: "deleteAlert",
        CREATE_WALLET_THRESHOLD: "createAlertWalletThreshold",
        CREATE_WALLET_EXPIRY: "createAlertWalletExpiry",
        UPDATE_WALLET_THRESHOLD: "updateAlertWalletThreshold",
        UPDATE_WALLET_EXPIRY: "updateAlertWalletExpiry",
        CHANGE_STATUS: "changeStatusAlert",
    },
    APN_SIM: {
        VIEW_LIST: "searchApnSim",
        VIEW_DETAIL: "getApnSim",
    },
    DEVICE: {
        VIEW_LIST: "searchDevice",
        VIEW_DETAIL: "getDevice",
        CREATE: "createDevice",
        UPDATE:"updateDevice",
        DELETE: "deleteDevice",
    },
    ALERT_RECEIVING_GROUP: {
        VIEW_LIST: "searchAlertRecvGrp",
        VIEW_DETAIL: "getAlertRecvGrp",
        CREATE: "createAlertRecvGrp",
        UPDATE: "updateAlertRecvGrp",
        DELETE: "deleteAlertRecvGrp",
    },
    ALERT_HISTORY: {
        VIEW_LIST: "searchAlertLog",
    },
    CONFIG_DYNAMIC_CHART: {
        VIEW_LIST: "searchCnfDynamicChart",
        VIEW_DETAIL: "getCnfDynamicChart",
        CREATE: "createCnfDynamicChart",
        UPDATE: "updateCnfDynamicChart",
        DELETE: "deleteCnfDynamicChart"
    },
    LOG : {
        VIEW_LIST : "searchLog"
    },
    TICKET: {
        VIEW_LIST: "getTicket",
        CREATE: "createTicket",
        UPDATE: "updateTicket"
    },
    POLICY:{
        PERSONAL: "getPersonalDataPolicy",
    },
    DYNAMICCHART: {
        VIEW_LIST: "getDashBoardContent",
    },
    DATAPOOL:{
        VIEW_WALLET: "searchWallet",
        CREATE_WALLET: "accuracyWallet",
        SHARE_WALLET: "shareWallet",
        CREATE_SHARE: "createShareInfo",
        VIEW_SHARE: "searchShareInfo",
        VIEW_HISTORY_WALLET: "walletHistory",
        // ALERT_WALLET_THRESHOLD: "alertWalletThreshold",
        // ALERT_WALLET_EXPIRY: "alertWalletExpiry",
    },
    SHARE_GROUP: {
        CREATE: "createShareGroup",
        EDIT: "editShareGroup",
        VIEW_LIST:"searchShareGroup",
        VIEW_DETAIL:"detailShareGroup",
        DELETE:"deleteShareGroup"
    },
    DIAGNOSE: {
        VIEW_LIST: "searchDiagnose",
    },
    AUTO_SHARE_GROUP: {
        CREATE: "createShareGroup",
        EDIT: "editShareGroup",
        VIEW_LIST:"searchShareGroup",
        VIEW_DETAIL:"detailShareGroup",
        DELETE:"deleteShareGroup"
    },
    THIRD_PARTY_API: {
        GRANT_PERMISSION_3RD_API: "grantPermission3rdApi",
        // GET_SIM_INFO_TO_ASSIGN: "getSimInfoToAssign",
        // GRANT_PERMISSION_3RD_API: "getSimInfo",
        // GRANT_PERMISSION_3RD_API: "getListSimByAccount",
        // GRANT_PERMISSION_3RD_API: "getSimInfoToImport",
        // GRANT_PERMISSION_3RD_API: "count-by-provinces",

    },
    GUIDE: {
        INTEGRATION: "guideIntegration",
    }
}
