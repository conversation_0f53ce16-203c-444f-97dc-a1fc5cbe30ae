<?xml version="1.0" encoding="UTF-8"?>
<Configuration>

    <Properties>
        <Property name="basePath">logs</Property>
<!--        <Property name="fileName">i-sim-trans</Property>-->
        <Property name="serviceName">fe-api</Property>
        <Property name="hostName">api140</Property>
    </Properties>

    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout
                    pattern="[%d{MM:dd:yyyy HH:mm:ss.SSS}] [%level] [%X{trace-id}] [%logger{36}] - %msg%n"/>
        </Console>


        <RollingFile name="i_sim_trans" fileName="${basePath}/i-sim-trans.log"
                     filePattern="${basePath}/i_sim_trans__${serviceName}-${hostName}__%d{yyyy-MM-dd-HH-mm}__%i.log">
            <CronTriggeringPolicy schedule="0 0/1 * * * ?"/>
            <TimeBasedTriggeringPolicy
                    interval="1"/><!-- como el filePattern tiene como unidad minima el minuto, se hara cada 1 minutos -->
        </RollingFile>

        <RollingFile name="i_trans" fileName="${basePath}/${serviceName}_${hostName}_i_trans.log"
                     filePattern="${basePath}/i_trans__${serviceName}-${hostName}__%d{yyyy-MM-dd-HH-mm}__%i.log">
<!--            <PatternLayout-->
<!--                    pattern="%d{MM:dd:yyyy HH:mm:ss.SSS} [%level] [%X{trace-id}] [%logger{36}] : %msg%n"/>-->
            <CronTriggeringPolicy schedule="0 0/1 * * * ?"/>
            <TimeBasedTriggeringPolicy interval="1"/>
        </RollingFile>

    </Appenders>

    <Loggers>
        <Logger name="vn.vnpt.cmp" level="info" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AlarmLog"/>
        </Logger>


        <Logger name="ISimTransLog" level="info" additivity="false">
            <AppenderRef ref="i_sim_trans"/>
        </Logger>

        <Logger name="ITransLog" level="info" additivity="false">
            <AppenderRef ref="i_trans"/>
        </Logger>

        <Root level="info">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="AlarmLog"/>
        </Root>

    </Loggers>
</Configuration>
