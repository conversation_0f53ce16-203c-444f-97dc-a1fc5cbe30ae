<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{ tranService.translate("ticket.menu.listIssuedSim") }}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button styleClass="p-button-info"
                  [label]="tranService.translate('global.button.export')"
                  icon="pi pi-download"
                  (onClick)="onExport()">
        </p-button>
    </div>
</div>

<form [formGroup]="formSearchSimTicket" (ngSubmit)="onSubmitSearch()" class="pt-3 pb-2 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid search-grid-4">
            <!-- imsi -->
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="imsi"
                           [(ngModel)]="searchInfo.imsi"
                           formControlName="imsi"
                           type="number"
                           (keydown)="preventCharacter($event)"
                           min = 0
                    />
                    <label htmlFor="imsi">{{ tranService.translate("ticket.label.imsi") }}</label>
                </span>
            </div>
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full"
                                [showClear]="true" [filter]="true" filterBy="display"
                                id="status" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.status"
                                [required]="false"
                                formControlName="status"
                                [options]="listSimIssuedStatus"
                                optionLabel="label"
                                optionValue="value"
                                filter = "true"
                                filterBy = "label"
                    ></p-dropdown>
                    <label class="label-dropdown" htmlFor="status">{{ tranService.translate("ticket.label.status") }}</label>
                </span>
            </div>

            <div class="col-3 pb-0">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="allocationDate"
                                [(ngModel)]="searchInfo.allocationDate"
                                formControlName="allocationDate"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                    ></p-calendar>
                    <label class="label-calendar" htmlFor="allocationDate">{{ tranService.translate("ticket.label.allocationDate") }}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="activedDate"
                                [(ngModel)]="searchInfo.activedDate"
                                formControlName="activedDate"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                    />
                    <label class="label-calendar" htmlFor="activedDate">{{ tranService.translate("ticket.label.activedDate") }}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>

<table-vnpt
    [tableId]="'tableSimTicket'"
    [fieldId]="'id'"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [pageNumber]="pageNumber"
    [loadData]="search.bind(this)"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="tranService.translate('ticket.menu.requestList')"
></table-vnpt>

