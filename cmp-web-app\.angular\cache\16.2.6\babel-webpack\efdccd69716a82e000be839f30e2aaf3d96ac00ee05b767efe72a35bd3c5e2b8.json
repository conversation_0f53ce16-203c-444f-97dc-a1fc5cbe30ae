{"ast": null, "code": "export default {\n  label: {\n    email: \"Email\",\n    password: \"Password\",\n    signIn: \"Sign In\",\n    forgotPass: \"Forgot Password\",\n    m2mTitle: \"M2M Subscription Management System\",\n    resetPass: \"Reset Password\",\n    editProfile: \"Edit Profile\",\n    changePassword: \"Change Password\",\n    logout: \"Logout\"\n  },\n  text: {\n    newUI: \"We would like to announce that the new interface is being tested from July 9, 2024. Please experience and give us feedback so we can improve. Thank you!\"\n  }\n};", "map": {"version": 3, "names": ["label", "email", "password", "signIn", "forgot<PERSON>ass", "m2mTitle", "resetPass", "editProfile", "changePassword", "logout", "text", "newUI"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\en\\login.ts"], "sourcesContent": ["export default {\r\n    label: {\r\n        email: \"Email\",\r\n        password: \"Password\",\r\n        signIn : \"Sign In\",\r\n        forgotPass : \"Forgot Password\",\r\n        m2mTitle : \"M2M Subscription Management System\",\r\n        resetPass : \"Reset Password\",\r\n        editProfile : \"Edit Profile\",\r\n        changePassword : \"Change Password\",\r\n        logout : \"Logout\",\r\n    },\r\n    text: {\r\n        newUI: \"We would like to announce that the new interface is being tested from July 9, 2024. Please experience and give us feedback so we can improve. Thank you!\"\r\n    }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAE;IACHC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAG,SAAS;IAClBC,UAAU,EAAG,iBAAiB;IAC9BC,QAAQ,EAAG,oCAAoC;IAC/CC,SAAS,EAAG,gBAAgB;IAC5BC,WAAW,EAAG,cAAc;IAC5BC,cAAc,EAAG,iBAAiB;IAClCC,MAAM,EAAG;GACZ;EACDC,IAAI,EAAE;IACFC,KAAK,EAAE;;CAEd"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}