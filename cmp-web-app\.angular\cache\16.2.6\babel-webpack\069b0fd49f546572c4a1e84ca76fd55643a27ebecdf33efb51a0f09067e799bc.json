{"ast": null, "code": "import { FormControl, FormGroup, Validators } from '@angular/forms';\nimport { ComponentBase } from 'src/app/component.base';\nimport { ShareManagementService } from 'src/app/service/datapool/ShareManagementService';\nimport { ComboLazyControl } from 'src/app/template/common-module/combobox-lazyload/combobox.lazyload';\nimport { TrafficWalletService } from \"../../../../service/datapool/TrafficWalletService\";\nimport { digitValidator } from 'src/app/template/common-module/validatorCustoms';\nimport { CONSTANTS } from \"../../../../service/comon/constants\";\nimport { GroupSubWalletService } from \"../../../../service/group-sub-wallet/GroupSubWalletService\";\nimport * as XLSX from 'xlsx-js-style';\nimport * as FileSaver from 'file-saver';\nimport { convert84to0PhoneNumber } from \"../../../common-module/utils/util\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"primeng/calendar\";\nimport * as i3 from \"primeng/button\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"../../../common-module/table/table.component\";\nimport * as i6 from \"../../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i7 from \"../../../common-module/upload-file/upload-file-dialog.component\";\nimport * as i8 from \"primeng/dialog\";\nimport * as i9 from \"primeng/breadcrumb\";\nimport * as i10 from \"primeng/tooltip\";\nimport * as i11 from \"@angular/forms\";\nimport * as i12 from \"primeng/inputtext\";\nimport * as i13 from \"@angular/common\";\nimport * as i14 from \"primeng/dropdown\";\nimport * as i15 from \"primeng/card\";\nimport * as i16 from \"primeng/inputtextarea\";\nimport * as i17 from \"primeng/table\";\nimport * as i18 from \"../../../common-module/ng-prime/inputotp/inputotp\";\nimport * as i19 from \"primeng/radiobutton\";\nimport * as i20 from \"primeng/inputnumber\";\nimport * as i21 from \"src/app/service/datapool/ShareManagementService\";\nimport * as i22 from \"../../../../service/datapool/TrafficWalletService\";\nimport * as i23 from \"../../../../service/group-sub-wallet/GroupSubWalletService\";\nconst _c0 = [\"normalShare\"];\nfunction ShareDataComponent_div_21_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction ShareDataComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, ShareDataComponent_div_21_div_1_Template, 2, 1, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.shareDataForm.get(\"walletValue\").errors.required);\n  }\n}\nconst _c1 = function () {\n  return {\n    len: 255\n  };\n};\nfunction ShareDataComponent_div_31_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r8.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c1)));\n  }\n}\nfunction ShareDataComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, ShareDataComponent_div_31_div_1_Template, 2, 2, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.shareDataForm.get(\"description\").errors.maxlength);\n  }\n}\nfunction ShareDataComponent_div_32_vnpt_select_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"vnpt-select\", 60);\n    i0.ɵɵlistener(\"valueChange\", function ShareDataComponent_div_32_vnpt_select_7_Template_vnpt_select_valueChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.phoneReceiptSelect = $event);\n    })(\"onchange\", function ShareDataComponent_div_32_vnpt_select_7_Template_vnpt_select_onchange_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.checkValidAdd());\n    })(\"onSelectItem\", function ShareDataComponent_div_32_vnpt_select_7_Template_vnpt_select_onSelectItem_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.addPhone(ctx_r20.phoneReceiptSelect, true));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"control\", ctx_r9.phoneReceiptSelectControl)(\"value\", ctx_r9.phoneReceiptSelect)(\"isAutoComplete\", true)(\"isMultiChoice\", false)(\"lazyLoad\", true)(\"placeholder\", ctx_r9.tranService.translate(\"datapool.label.receiverPhone\"))(\"loadData\", ctx_r9.getListShareInfoCbb.bind(ctx_r9));\n  }\n}\nfunction ShareDataComponent_div_32_vnpt_select_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"vnpt-select\", 61);\n    i0.ɵɵlistener(\"valueChange\", function ShareDataComponent_div_32_vnpt_select_8_Template_vnpt_select_valueChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.groupSelected = $event);\n    })(\"onSelectItem\", function ShareDataComponent_div_32_vnpt_select_8_Template_vnpt_select_onSelectItem_0_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r23.onSelectGroup(ctx_r23.groupSelected));\n    })(\"onchange\", function ShareDataComponent_div_32_vnpt_select_8_Template_vnpt_select_onchange_0_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.clearSelectedGroup());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", ctx_r10.groupSelected)(\"placeholder\", ctx_r10.tranService.translate(\"datapool.label.shareGroup\"))(\"isMultiChoice\", false);\n  }\n}\nfunction ShareDataComponent_div_32_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function ShareDataComponent_div_32_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.addPhone(ctx_r25.phoneReceiptSelect));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r11.isClickCreate || !ctx_r11.isValidPhone)(\"label\", ctx_r11.tranService.translate(\"datapool.button.add\"));\n  }\n}\nfunction ShareDataComponent_div_32_p_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 63);\n    i0.ɵɵlistener(\"click\", function ShareDataComponent_div_32_p_button_11_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.importByFile());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"label\", ctx_r12.tranService.translate(\"datapool.button.importFile\"));\n  }\n}\nfunction ShareDataComponent_div_32_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.tranService.translate(\"datapool.message.digitError\"), \" \");\n  }\n}\nfunction ShareDataComponent_div_32_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"div\", 65);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function ShareDataComponent_div_32_div_24_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.selectedTableData = []);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u0110\\u00E3 ch\\u1ECDn \", ctx_r14.selectedTableData.length, \" gi\\u00E1 tr\\u1ECB\");\n  }\n}\nconst _c2 = function () {\n  return {\n    type: \"MB\"\n  };\n};\nfunction ShareDataComponent_div_32_p_table_25_ng_template_2_th_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r35 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r35.tranService.translate(\"datapool.label.sharingData\", i0.ɵɵpureFunction0(1, _c2)));\n  }\n}\nconst _c3 = function () {\n  return {\n    type: \"Ph\\u00FAt\"\n  };\n};\nfunction ShareDataComponent_div_32_p_table_25_ng_template_2_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r36 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r36.tranService.translate(\"datapool.label.sharingData\", i0.ɵɵpureFunction0(1, _c3)));\n  }\n}\nconst _c4 = function () {\n  return {\n    type: \"SMS\"\n  };\n};\nfunction ShareDataComponent_div_32_p_table_25_ng_template_2_th_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r37 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r37.tranService.translate(\"datapool.label.sharingData\", i0.ɵɵpureFunction0(1, _c4)));\n  }\n}\nconst _c5 = function () {\n  return {\n    type: \"\"\n  };\n};\nfunction ShareDataComponent_div_32_p_table_25_ng_template_2_th_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r38 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r38.tranService.translate(\"datapool.label.sharingData\", i0.ɵɵpureFunction0(1, _c5)));\n  }\n}\nfunction ShareDataComponent_div_32_p_table_25_ng_template_2_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 76)(2, \"span\", 77);\n    i0.ɵɵtext(3, \"\\u00A0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"colspan\", 7);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r39.tranService.translate(\"global.text.nodata\"), \" \");\n  }\n}\nfunction ShareDataComponent_div_32_p_table_25_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 72)(2, \"div\", 73)(3, \"p-tableHeaderCheckbox\", 74);\n    i0.ɵɵlistener(\"click\", function ShareDataComponent_div_32_p_table_25_ng_template_2_Template_p_tableHeaderCheckbox_click_3_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r40 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r40.onClickSelection());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 75);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, ShareDataComponent_div_32_p_table_25_ng_template_2_th_14_Template, 2, 2, \"th\", 15);\n    i0.ɵɵtemplate(15, ShareDataComponent_div_32_p_table_25_ng_template_2_th_15_Template, 2, 2, \"th\", 15);\n    i0.ɵɵtemplate(16, ShareDataComponent_div_32_p_table_25_ng_template_2_th_16_Template, 2, 2, \"th\", 15);\n    i0.ɵɵtemplate(17, ShareDataComponent_div_32_p_table_25_ng_template_2_th_17_Template, 2, 2, \"th\", 15);\n    i0.ɵɵelementStart(18, \"th\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"th\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, ShareDataComponent_div_32_p_table_25_ng_template_2_tr_21_Template, 5, 2, \"tr\", 15);\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r32.isAutoWallet);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r32.tranService.translate(\"datapool.label.autoSharing\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r32.tranService.translate(\"global.text.stt\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r32.tranService.translate(\"datapool.label.phone\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r32.tranService.translate(\"datapool.label.fullName\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r32.tranService.translate(\"datapool.label.email\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r32.trafficType == \"G\\u00F3i Data\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r32.trafficType == \"G\\u00F3i tho\\u1EA1i\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r32.trafficType || \"\").toUpperCase().includes(\"G\\u00F3i SMS\".toUpperCase()));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r32.trafficType != \"G\\u00F3i Data\" && ctx_r32.trafficType != \"G\\u00F3i tho\\u1EA1i\" && !(ctx_r32.trafficType || \"\").toUpperCase().includes(\"G\\u00F3i SMS\".toUpperCase()));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r32.tranService.translate(\"datapool.label.percentage\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r32.shareList || ctx_r32.shareList.length === 0);\n  }\n}\nconst _c6 = function () {\n  return {\n    len: 50\n  };\n};\nfunction ShareDataComponent_div_32_p_table_25_ng_template_3_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r44 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r44.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c6)), \" \");\n  }\n}\nconst _c7 = function () {\n  return {\n    len: 150\n  };\n};\nfunction ShareDataComponent_div_32_p_table_25_ng_template_3_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r45 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r45.tranService.translate(\"global.message.wrongFormatName\", i0.ɵɵpureFunction0(1, _c7)), \" \");\n  }\n}\nconst _c8 = function () {\n  return {\n    len: 100\n  };\n};\nfunction ShareDataComponent_div_32_p_table_25_ng_template_3_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r46 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r46.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c8)), \" \");\n  }\n}\nfunction ShareDataComponent_div_32_p_table_25_ng_template_3_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r47 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r47.tranService.translate(\"global.message.formatEmail\"), \" \");\n  }\n}\nfunction ShareDataComponent_div_32_p_table_25_ng_template_3_i_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 89);\n  }\n}\nfunction ShareDataComponent_div_32_p_table_25_ng_template_3_i_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 90);\n  }\n}\nfunction ShareDataComponent_div_32_p_table_25_ng_template_3_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r50 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r50.tranService.translate(\"datapool.message.smsError\"), \" \");\n  }\n}\nfunction ShareDataComponent_div_32_p_table_25_ng_template_3_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r51 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r51.tranService.translate(\"datapool.message.dataError\"), \" \");\n  }\n}\nfunction ShareDataComponent_div_32_p_table_25_ng_template_3_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"%\");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c9 = function (a0) {\n  return {\n    \"surface-200\": a0\n  };\n};\nfunction ShareDataComponent_div_32_p_table_25_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r54 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 78)(2, \"p-tableCheckbox\", 79);\n    i0.ɵɵlistener(\"click\", function ShareDataComponent_div_32_p_table_25_ng_template_3_Template_p_tableCheckbox_click_2_listener() {\n      i0.ɵɵrestoreView(_r54);\n      const ctx_r53 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r53.onClickSelection());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\")(8, \"input\", 80);\n    i0.ɵɵlistener(\"input\", function ShareDataComponent_div_32_p_table_25_ng_template_3_Template_input_input_8_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r54);\n      const index_r43 = restoredCtx.rowIndex;\n      const ctx_r55 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r55.changeDataName($event, index_r43));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, ShareDataComponent_div_32_p_table_25_ng_template_3_div_9_Template, 2, 2, \"div\", 26);\n    i0.ɵɵtemplate(10, ShareDataComponent_div_32_p_table_25_ng_template_3_div_10_Template, 2, 2, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\")(12, \"input\", 80);\n    i0.ɵɵlistener(\"input\", function ShareDataComponent_div_32_p_table_25_ng_template_3_Template_input_input_12_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r54);\n      const index_r43 = restoredCtx.rowIndex;\n      const ctx_r56 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r56.changeDataMail($event, index_r43));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, ShareDataComponent_div_32_p_table_25_ng_template_3_div_13_Template, 2, 2, \"div\", 26);\n    i0.ɵɵtemplate(14, ShareDataComponent_div_32_p_table_25_ng_template_3_div_14_Template, 2, 1, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\")(16, \"div\", 81)(17, \"input\", 82);\n    i0.ɵɵlistener(\"input\", function ShareDataComponent_div_32_p_table_25_ng_template_3_Template_input_input_17_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r54);\n      const index_r43 = restoredCtx.rowIndex;\n      const ctx_r57 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r57.changeData($event, index_r43));\n    })(\"keydown\", function ShareDataComponent_div_32_p_table_25_ng_template_3_Template_input_keydown_17_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r54);\n      const index_r43 = restoredCtx.rowIndex;\n      const ctx_r58 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r58.onKeyDown($event, index_r43));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 83);\n    i0.ɵɵlistener(\"click\", function ShareDataComponent_div_32_p_table_25_ng_template_3_Template_div_click_18_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r54);\n      const list_r42 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(list_r42.locked = !list_r42.locked);\n    });\n    i0.ɵɵtemplate(19, ShareDataComponent_div_32_p_table_25_ng_template_3_i_19_Template, 1, 0, \"i\", 84);\n    i0.ɵɵtemplate(20, ShareDataComponent_div_32_p_table_25_ng_template_3_i_20_Template, 1, 0, \"i\", 85);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, ShareDataComponent_div_32_p_table_25_ng_template_3_div_21_Template, 2, 1, \"div\", 26);\n    i0.ɵɵtemplate(22, ShareDataComponent_div_32_p_table_25_ng_template_3_div_22_Template, 2, 1, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\")(24, \"div\", 86);\n    i0.ɵɵtext(25);\n    i0.ɵɵtemplate(26, ShareDataComponent_div_32_p_table_25_ng_template_3_div_26_Template, 2, 0, \"div\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"td\")(28, \"button\", 87);\n    i0.ɵɵlistener(\"click\", function ShareDataComponent_div_32_p_table_25_ng_template_3_Template_button_click_28_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r54);\n      const index_r43 = restoredCtx.rowIndex;\n      const ctx_r60 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r60.deleteItem(index_r43));\n    });\n    i0.ɵɵelement(29, \"i\", 88);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const list_r42 = ctx.$implicit;\n    const index_r43 = ctx.rowIndex;\n    const ctx_r33 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", list_r42)(\"disabled\", !ctx_r33.isAutoWallet);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(index_r43 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(list_r42.phoneReceipt);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", list_r42.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (list_r42.name == null ? null : list_r42.name.length) >= 50);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r33.utilService.checkValidCharacterVietnamese(list_r42.name));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", list_r42.email);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (list_r42.email == null ? null : list_r42.email.length) >= 100);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r33.isMailInvalid(list_r42.email));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c9, list_r42.locked))(\"disabled\", list_r42.locked)(\"value\", list_r42.data);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", list_r42.locked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !list_r42.locked);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r33.trafficType || \"\").toUpperCase().includes(\"G\\u00F3i SMS\".toUpperCase()) && (list_r42.data < 10 || !ctx_r33.checkDataCondition(list_r42)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r33.trafficType == \"G\\u00F3i Data\" && (list_r42.data < 100 || !ctx_r33.checkDataCondition(list_r42)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", list_r42.percent, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", list_r42.percent || list_r42.percent == 0);\n  }\n}\nfunction ShareDataComponent_div_32_p_table_25_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r62 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 91)(1, \"div\", 92);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-inputNumber\", 93);\n    i0.ɵɵlistener(\"keyup.enter\", function ShareDataComponent_div_32_p_table_25_ng_template_4_Template_p_inputNumber_keyup_enter_3_listener() {\n      i0.ɵɵrestoreView(_r62);\n      const ctx_r61 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r61.jumpPage());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r34.tranService.translate(\"global.text.page\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"min\", 1)(\"max\", ctx_r34.getMaxPage());\n  }\n}\nconst _c10 = function () {\n  return {\n    \"min-width\": \"50rem\",\n    \"padding\": \"21px\"\n  };\n};\nconst _c11 = function () {\n  return [5, 10, 20, 25, 50];\n};\nfunction ShareDataComponent_div_32_p_table_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-table\", 67, 68);\n    i0.ɵɵlistener(\"selectionChange\", function ShareDataComponent_div_32_p_table_25_Template_p_table_selectionChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r63 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r63.selectedTableData = $event);\n    })(\"onPage\", function ShareDataComponent_div_32_p_table_25_Template_p_table_onPage_0_listener($event) {\n      i0.ɵɵrestoreView(_r64);\n      const ctx_r65 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r65.pageChange($event));\n    });\n    i0.ɵɵtemplate(2, ShareDataComponent_div_32_p_table_25_ng_template_2_Template, 22, 12, \"ng-template\", 69);\n    i0.ɵɵtemplate(3, ShareDataComponent_div_32_p_table_25_ng_template_3_Template, 30, 21, \"ng-template\", 70);\n    i0.ɵɵtemplate(4, ShareDataComponent_div_32_p_table_25_ng_template_4_Template, 4, 3, \"ng-template\", 71);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", ctx_r15.shareList)(\"tableStyle\", i0.ɵɵpureFunction0(9, _c10))(\"selection\", ctx_r15.selectedTableData)(\"totalRecords\", ctx_r15.shareList.length)(\"paginator\", ctx_r15.shareList.length > 0)(\"rows\", 10)(\"showCurrentPageReport\", true)(\"currentPageReportTemplate\", ctx_r15.tranService.translate(\"global.text.templateTextPagination\"))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(10, _c11));\n  }\n}\nfunction ShareDataComponent_div_32_table_vnpt_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r67 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"table-vnpt\", 94);\n    i0.ɵɵlistener(\"selectItemsChange\", function ShareDataComponent_div_32_table_vnpt_26_Template_table_vnpt_selectItemsChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r66 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r66.groupSelectedValue = $event);\n    })(\"onChangeSelectAllItems\", function ShareDataComponent_div_32_table_vnpt_26_Template_table_vnpt_onChangeSelectAllItems_0_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r68 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r68.onClickSelection());\n    })(\"onChangeSelectItem\", function ShareDataComponent_div_32_table_vnpt_26_Template_table_vnpt_onChangeSelectItem_0_listener() {\n      i0.ɵɵrestoreView(_r67);\n      const ctx_r69 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r69.onClickSelection());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"tableId\", \"tbSubShareByGroup\")(\"fieldId\", \"id\")(\"columns\", ctx_r16.columns)(\"dataSet\", ctx_r16.dataSet)(\"options\", ctx_r16.optionTable)(\"loadData\", ctx_r16.search.bind(ctx_r16))(\"pageNumber\", ctx_r16.pageNumber)(\"pageSize\", ctx_r16.pageSize)(\"sort\", ctx_r16.sort)(\"params\", ctx_r16.searchInfo)(\"selectItems\", ctx_r16.groupSelectedValue)(\"tableSelectionText\", ctx_r16.tranService.translate(\"datapool.label.autoSharing\"));\n  }\n}\nfunction ShareDataComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"p-card\")(2, \"div\", 7);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 40)(5, \"div\", 41)(6, \"div\", 42);\n    i0.ɵɵtemplate(7, ShareDataComponent_div_32_vnpt_select_7_Template, 1, 7, \"vnpt-select\", 43);\n    i0.ɵɵtemplate(8, ShareDataComponent_div_32_vnpt_select_8_Template, 1, 3, \"vnpt-select\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, ShareDataComponent_div_32_button_9_Template, 1, 2, \"button\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 46);\n    i0.ɵɵtemplate(11, ShareDataComponent_div_32_p_button_11_Template, 1, 1, \"p-button\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 48);\n    i0.ɵɵtemplate(13, ShareDataComponent_div_32_div_13_Template, 2, 1, \"div\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 49)(15, \"div\", 50);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 51)(18, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function ShareDataComponent_div_32_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r70 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r70.shareData());\n    });\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function ShareDataComponent_div_32_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r72 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r72.defineData());\n    });\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function ShareDataComponent_div_32_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r73 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r73.resetData());\n    });\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(24, ShareDataComponent_div_32_div_24_Template, 4, 1, \"div\", 53);\n    i0.ɵɵtemplate(25, ShareDataComponent_div_32_p_table_25_Template, 5, 11, \"p-table\", 54);\n    i0.ɵɵtemplate(26, ShareDataComponent_div_32_table_vnpt_26_Template, 1, 12, \"table-vnpt\", 55);\n    i0.ɵɵelementStart(27, \"div\", 56)(28, \"a\", 57);\n    i0.ɵɵelement(29, \"button\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function ShareDataComponent_div_32_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r74 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r74.handleShareData());\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.walletName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.shareMethod == 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.shareMethod == 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.shareMethod == 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.shareMethod == 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isValidPhone && ctx_r2.shareMethod == 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\"\", ctx_r2.tranService.translate(\"datapool.label.remainData\"), \": \", ctx_r2.shareMethod == 1 ? ctx_r2.formatNumber(ctx_r2.dataShareGroupTotal) : ctx_r2.formatNumber(ctx_r2.remainDataTotal), \"/\", ctx_r2.formatNumber(ctx_r2.totalData), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.shareList.length <= 0 && ctx_r2.dataSet.total <= 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"datapool.button.equalSharing\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.shareList.length <= 0 && ctx_r2.dataSet.total <= 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"datapool.button.fixedAllocation\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.shareList.length <= 0 && ctx_r2.dataSet.total <= 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"datapool.button.revokeSharing\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedTableData.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.shareMethod == 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.shareMethod == 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"label\", ctx_r2.tranService.translate(\"global.button.cancel\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", !(ctx_r2.shareDataForm.controls.walletValue.valid && ctx_r2.shareDataForm.controls.description.valid) || ctx_r2.checkValidData())(\"label\", ctx_r2.tranService.translate(\"global.button.save\"));\n  }\n}\nfunction ShareDataComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.tranService.translate(\"datapool.message.smsError\"), \" \");\n  }\n}\nfunction ShareDataComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.tranService.translate(\"datapool.message.dataError\"), \" \");\n  }\n}\nfunction ShareDataComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.tranService.translate(\"datapool.message.exceededData\"));\n  }\n}\nfunction ShareDataComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", ctx_r6.tranService.translate(\"datapool.message.in\"), \" \", ctx_r6.countdown, \" \", ctx_r6.tranService.translate(\"datapool.message.sec\"), \" \");\n  }\n}\nconst _c12 = function () {\n  return {\n    width: \"50vw\"\n  };\n};\nconst _c13 = function (a0) {\n  return {\n    data: a0\n  };\n};\nconst _c14 = function () {\n  return {\n    width: \"30vw\"\n  };\n};\nconst _c15 = function () {\n  return {\n    width: \"60vw\"\n  };\n};\nexport class ShareDataComponent extends ComponentBase {\n  constructor(shareService, walletService, groupSubService, injector) {\n    super(injector);\n    this.shareService = shareService;\n    this.walletService = walletService;\n    this.groupSubService = groupSubService;\n    this.phoneList = [];\n    this.groupShareList = [];\n    this.showShareList = false;\n    this.isShowDefined = false;\n    this.isSubmit = false;\n    this.isError = false;\n    this.phoneReceiptSelect = \"\";\n    this.phoneReceiptSelectControl = new ComboLazyControl();\n    this.idGroupSelected = \"\";\n    this.walletControl = new ComboLazyControl();\n    this.paramSearchWallet = {};\n    this.isClickCreate = true;\n    this.shareDataForm = new FormGroup({\n      walletValue: new FormControl(null, [Validators.required]),\n      dateShare: new FormControl(new Date()),\n      definedData: new FormControl(),\n      selectedShareMethod: new FormControl(0),\n      description: new FormControl(null, [Validators.maxLength(255)]),\n      otp: new FormControl(null, [Validators.required, digitValidator(6)]),\n      pageCurrent: new FormControl(1)\n    });\n    this.isDefineValueError = false;\n    this.isShowDialogImportByFile = false;\n    this.isShareByGroup = false;\n    this.isShowErrorUpload = false;\n    this.isValidPhone = true;\n    this.isValidDataFixed = false;\n    this.dataForLabel = \"Data\";\n    this.optionTableError = {\n      hasClearSelected: false,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false\n    };\n    this.pageNumberError = 0;\n    this.pageSizeError = 10;\n    this.sortError = \"\";\n    this.dataSetError = {\n      content: [],\n      total: 0\n    };\n    this.searchInfoError = {\n      value: \"\"\n    };\n    this.columnsError = [{\n      name: this.tranService.translate(\"datapool.label.phone\"),\n      key: \"phoneReceipt\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"datapool.label.description\"),\n      key: \"description\",\n      size: \"fit-content\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }];\n    this.dataShareGroup = 0;\n    this.selectedTableData = [];\n    this.isNonUseOTP = true;\n    this.groupSelectedValue = [];\n    this.isAutoWallet = true;\n  }\n  openSubmit() {\n    this.isSubmit = true;\n    this.countdown = 60;\n    this.startCountdown();\n  }\n  isMailInvalid(email) {\n    if (!email) {\n      return false;\n    }\n    const pattern = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$/;\n    return !pattern.test(email);\n  }\n  convertDateToString(value) {\n    if (value == null) return \"\";\n    const pad = num => num.toString().padStart(2, '0');\n    const day = pad(value.getDate());\n    const month = pad(value.getMonth() + 1); // Tháng bắt đầu từ 0 nên cần cộng 1\n    const year = value.getFullYear();\n    const hours = pad(value.getHours());\n    const minutes = pad(value.getMinutes());\n    const seconds = pad(value.getSeconds());\n    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;\n  }\n  submitForm() {\n    let me = this;\n    this.groupSelected = null;\n    if (this.shareMethod == 1) {\n      const dataBodyGroup = {\n        subCode: this.selectedWallet.subCode,\n        otp: this.shareDataForm.get('otp').value,\n        reason: this.shareDataForm.get('description').value,\n        sharingDay: this.convertDateToString(this.shareDataForm.get('dateShare').value),\n        idGroup: Number(this.idGroupSelected),\n        numOfShare: Number(this.dataShareGroup),\n        transId: '',\n        trafficType: \"DATA\",\n        expireDate: '30 ngày',\n        type: 0,\n        autoPhones: this.groupSelectedValue.map(item => item.phoneReceipt)\n      };\n      me.messageCommonService.onload();\n      this.shareService.shareTrafficByGroup(dataBodyGroup, res => {\n        if (res.error_code === 'BSS-00000000') {\n          let ok = 1;\n          let dataErrorTable = [];\n          let countToTal = 0;\n          res.data.forEach(element => {\n            if (element.status == 0 && element.error != \"\") {\n              // me.messageCommonService.error(`Thuê bao ${element.sdt} có lỗi (${element.error})`);\n              dataErrorTable.push({\n                phoneReceipt: convert84to0PhoneNumber(element.sdt),\n                // description: element.error\n                description: \"Lỗi do thuê bao không hợp lệ\"\n              });\n              ok = 0;\n            }\n            countToTal++;\n          });\n          if (ok == 0) {\n            this.isError = true;\n            this.dataSetError = {\n              content: dataErrorTable,\n              total: dataErrorTable.length\n            };\n            this.FulldataSetError = {\n              ...this.dataSetError\n            };\n            me.messageCommonService.error(me.tranService.translate(\"datapool.message.shareNotifyFail\", {\n              success: countToTal - dataErrorTable.length,\n              total: countToTal\n            }), null, 10000);\n          } else {\n            me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n            this.router.navigate(['/data-pool/shareMgmt/listShare']);\n          }\n        } else if (res.error_code === 'Success') {\n          me.messageCommonService.success(me.tranService.translate(\"datapool.message.shareNotifyBackground\"), null, 15000);\n          this.router.navigate(['/data-pool/shareMgmt/listShare']);\n        } else {\n          this.messageCommonService.error(res.message);\n        }\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n    } else {\n      const convertShareInfo = this.shareList.map(e => ({\n        phone: String(e?.phoneReceipt),\n        trafficType: \"DATA\",\n        numOfShare: Number(e?.data),\n        expireDate: '30 ngày',\n        name: e?.name,\n        email: e?.email,\n        type: 0,\n        isAuto: !!this.selectedTableData.find(item => item.phoneReceipt === e.phoneReceipt)\n      }));\n      const dataBody = {\n        subCode: this.selectedWallet.subCode,\n        otp: this.shareDataForm.get('otp').value,\n        reason: this.shareDataForm.get('description').value,\n        sharingDay: this.convertDateToString(this.shareDataForm.get('dateShare').value),\n        shareInfos: convertShareInfo,\n        transId: ''\n      };\n      console.log(convertShareInfo);\n      me.messageCommonService.onload();\n      this.shareService.shareTraffic(dataBody, res => {\n        if (res.error_code === 'BSS-00000000') {\n          let ok = 1;\n          let dataErrorTable = [];\n          res.data.forEach(element => {\n            if (element.status == 0 && element.error != \"\") {\n              // me.messageCommonService.error(`Thuê bao ${element.sdt} có lỗi (${element.error})`);\n              dataErrorTable.push({\n                phoneReceipt: convert84to0PhoneNumber(element.sdt),\n                // description: element.error\n                description: \"Lỗi do thuê bao không hợp lệ\"\n              });\n              ok = 0;\n            }\n          });\n          if (ok == 0) {\n            this.isError = true;\n            this.dataSetError = {\n              content: dataErrorTable,\n              total: dataErrorTable.length\n            };\n            this.FulldataSetError = {\n              ...this.dataSetError\n            };\n            me.messageCommonService.error(me.tranService.translate(\"datapool.message.shareNotifyFail\", {\n              success: this.shareList.length - this.dataSetError.content.length,\n              total: this.shareList.length\n            }), null, 10000);\n          } else {\n            me.messageCommonService.success(me.tranService.translate(\"datapool.message.shareNotifySuccess\", {\n              success: this.shareList.length,\n              total: this.shareList.length\n            }), null, 10000);\n            this.router.navigate(['/data-pool/shareMgmt/listShare']);\n          }\n        } else if (res.error_code === 'Success') {\n          me.messageCommonService.success(me.tranService.translate(\"datapool.message.shareNotifyBackground\"), null, 15000);\n          this.router.navigate(['/data-pool/shareMgmt/listShare']);\n        } else {\n          this.messageCommonService.error(res.message);\n        }\n      }, null, () => {\n        me.messageCommonService.offload();\n      });\n    }\n    this.isNonUseOTP = true;\n  }\n  ngOnInit() {\n    let me = this;\n    me.columns = [{\n      name: this.tranService.translate(\"datapool.label.phone\"),\n      key: \"phoneReceipt\",\n      size: \"10%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"datapool.label.fullName\"),\n      key: \"name\",\n      size: \"25%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"datapool.label.email\"),\n      key: \"email\",\n      size: \"25%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false,\n      isShowTooltip: true,\n      style: {\n        display: 'inline-block',\n        maxWidth: '350px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis'\n      }\n    }, {\n      name: me.trafficType == 'Gói Data' ? me.tranService.translate(\"datapool.label.sharingData\", {\n        type: 'MB'\n      }) : me.trafficType == 'Gói thoại' ? me.tranService.translate(\"datapool.label.sharingData\", {\n        type: 'Phút'\n      }) : (me.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase()) ? me.tranService.translate(\"datapool.label.sharingData\", {\n        type: 'SMS'\n      }) : me.tranService.translate(\"datapool.label.sharingDataNotType\"),\n      key: \"sharingData\",\n      size: \"25%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false,\n      funcConvertText(value) {\n        switch (me.trafficType) {\n          case 'Gói Data':\n            return `${me.dataShareGroup?.toString()} (MB)`;\n          case 'Gói thoại':\n            return `${me.dataShareGroup?.toString()} (Phút)`;\n          case 'Gói SMS':\n          case 'Gói SMS ngoại mạng':\n          case 'Gói SMS VNP':\n            return `${me.dataShareGroup?.toString()} (SMS)`;\n          default:\n            return '';\n        }\n      }\n    }, {\n      name: this.tranService.translate(\"datapool.label.percentage\"),\n      key: \"percentDataGroup\",\n      size: \"25%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false,\n      funcConvertText(value) {\n        return me.percentShareGroup?.toString() ? `${me.percentShareGroup?.toString()} %` : '';\n      }\n    }];\n    this.items = [{\n      label: this.tranService.translate(`global.menu.trafficManagement`)\n    }, {\n      label: this.tranService.translate(\"global.menu.shareList\"),\n      routerLink: [\"/data-pool/shareMgmt/listShare\"]\n    }, {\n      label: this.tranService.translate(\"datapool.label.shareData\")\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.getWalletCbb();\n    this.showShareList = false;\n    this.phoneList = [];\n    // this.changeWallet();\n    this.observableService.subscribe(CONSTANTS.OBSERVABLE.UPDATE_SHARE_INFO, {\n      next: this.loadData.bind(this)\n    });\n    this.shareMethod = 0;\n    me.selectItems = [];\n    me.optionTable = {\n      hasClearSelected: false,\n      hasShowChoose: true,\n      hasShowIndex: true,\n      hasShowToggleColumn: false,\n      disabledCheckBox: false,\n      paginator: true\n    };\n    me.searchInfo = {\n      value: \"\"\n    };\n    me.pageNumber = 0;\n    me.pageSize = 10;\n    me.sort = \"phoneReceipt,desc\";\n    me.dataSet = {\n      content: [],\n      total: 0\n    };\n  }\n  loadData(data) {\n    if (data) {\n      if (data.length > 1000) {\n        this.messageCommonService.error(this.tranService.translate(\"error.invalid.file.form.maxrow\"));\n        return;\n      }\n      this.shareList = data;\n      let oldSelectData = this.selectedTableData;\n      this.selectedTableData = [];\n      //data.isAuto.toLowerCase()===\"có\" || data.isAuto.toLowerCase()==\"co\"\n      data.forEach((item, index) => {\n        if (item.isAuto && (item.isAuto.localeCompare(\"Có\", 'vi', {\n          sensitivity: 'base'\n        }) === 0 || item.isAuto.localeCompare(\"Co\", 'vi', {\n          sensitivity: 'base'\n        }) === 0)) {\n          this.selectedTableData.push(item);\n        }\n      });\n      this.selectedTableData = [...this.selectedTableData, ...oldSelectData];\n    }\n    this.caculateRemain();\n    if (!this.isAutoWallet) {\n      this.selectedTableData = [];\n      this.messageCommonService.warning(\"Ví chưa đăng ký chia sẻ không cần OTP nên các thuê bao đã tích chọn tự động sẽ bị bỏ tích\");\n    }\n  }\n  changeWallet() {\n    let me = this;\n    if (this.shareMethod == 1) {\n      this.dataShareGroup = 0;\n      this.percentShareGroup = 0;\n    }\n    if (this.shareList) {\n      this.shareList.forEach(item => {\n        item.data = null;\n        item.percent = null;\n      });\n    } else {\n      this.shareList = [];\n    }\n    const walletId = this.shareDataForm.get('walletValue').value;\n    const wallet = this.dummyWallet?.find(w => w.subCode === walletId);\n    if (wallet) {\n      this.selectedWallet = wallet;\n      if (wallet.autoType != 2) {\n        this.isNonUseOTP = true;\n        this.isAutoWallet = true;\n        me.optionTable = {\n          hasClearSelected: false,\n          hasShowChoose: true,\n          hasShowIndex: true,\n          hasShowToggleColumn: false,\n          disabledCheckBox: false,\n          paginator: true\n        };\n      } else {\n        this.isNonUseOTP = false;\n        this.isAutoWallet = false;\n        me.optionTable = {\n          hasClearSelected: false,\n          hasShowChoose: true,\n          hasShowIndex: true,\n          hasShowToggleColumn: false,\n          disabledCheckBox: true,\n          paginator: true\n        };\n      }\n      this.groupSelectedValue = [];\n      this.selectedTableData = [];\n      this.walletName = this.selectedWallet.packageName;\n    } else {\n      console.log('Không tìm thấy ví có id =', walletId);\n    }\n    // this.getListShareInfoCbb();\n    this.showShareList = true;\n    // typeOfTraffic\n    this.trafficType = this.dummyWallet?.find(wallet => wallet.subCode === this.shareDataForm.get(\"walletValue\").value)?.trafficType;\n    this.totalData = this.dummyWallet?.find(wallet => wallet.subCode === this.shareDataForm.get(\"walletValue\").value)?.purchasedTraffic;\n    this.remainDataTotal = this.dummyWallet?.find(wallet => wallet.subCode === this.shareDataForm.get('walletValue').value)?.totalRemainingTraffic;\n    this.dataShareGroupTotal = this.dummyWallet?.find(wallet => wallet.subCode === this.shareDataForm.get('walletValue').value)?.totalRemainingTraffic;\n    this.originRemain = this.dummyWallet?.find(wallet => wallet.subCode === this.shareDataForm.get('walletValue').value)?.totalRemainingTraffic;\n    if ((this.trafficType || \"\").toUpperCase().includes(\"Gói SMS\".toUpperCase())) {\n      this.dataForLabel = \"SMS\";\n    } else if (this.trafficType == \"Gói Data\") {\n      this.dataForLabel = \"MB\";\n    }\n  }\n  getListShareInfoCbb(params, callback) {\n    return this.shareService.getListShareInfoCbb(params, response => {\n      this.phoneList = response.content;\n      callback(response);\n    });\n  }\n  getShareGroupInfo(params, callback) {\n    let me = this;\n    this.messageCommonService.onload();\n    this.groupSubService.search(params, response => {\n      // console.log(response)\n      me.groupShareList = response.content;\n      let data = {\n        content: response.content,\n        totalPages: response.totalElements\n      };\n    }, null, () => {\n      this.messageCommonService.offload();\n    });\n  }\n  getWalletCbb() {\n    let me = this;\n    this.messageCommonService.onload();\n    this.walletService.getWalletCbbShare(response => {\n      me.dummyWallet = response;\n    }, null, () => {\n      this.messageCommonService.offload();\n    });\n  }\n  addPhone(data, onSelected) {\n    let me = this;\n    if (data === null || data === undefined || data === \"\") {\n      return;\n    }\n    if (this.shareList.length > 999) {\n      this.messageCommonService.error(this.tranService.translate(\"error.invalid.file.form.maxrow\"));\n      return;\n    }\n    this.isClickCreate = false;\n    this.shareList = this.cleanArray(this.shareList);\n    const value = this.phoneList.find(dta => dta.phoneReceipt === data);\n    if (this.shareList.find(i => i?.phoneReceipt === data)) {\n      this.messageCommonService.error(this.tranService.translate(\"datapool.message.existed\"));\n    } else {\n      /**\n       * UAT 2.4 issue 31\n       * Khi add số thuê bao được chia sẻ, nếu đã có trong danh sách thì bỏ qua check số đó là thuê bao vinaphone hay không, trong các trường hợp sau:\n       * - Chia sẻ thường\n       * - Nhóm chia sẻ tự động > thêm sdt chia sẻ tự động\n       * - Thêm thuê bao vào nhóm\n       * - icon chia sẻ ở Danh sách ví\n       */\n      this.addPhoneTable(value, data);\n      /**\n       * bỏ check số vina\n       */\n      // if (onSelected) {\n      //     this.addPhoneTable(value, data)\n      // } else {\n      //     const phone = String(data)?.replace(/^0/,\"84\");\n      //     this.messageCommonService.onload()\n      //     this.walletService.checkParticipant({phoneNumber: phone},\n      //         (response) => {\n      //             if (response.error_code === \"0\" && (response.result === \"02\" || response.result === \"11\")) {\n      //                 this.addPhoneTable(value, data)\n      //             } else if (response.error_code === \"0\" && response.result === \"0\") {\n      //                 if (isVinaphoneNumber(data)) {\n      //                     this.addPhoneTable(value, data)\n      //                 } else {\n      //                     this.messageCommonService.error(this.tranService.translate(\"datapool.message.notValidPhone\"))\n      //                 }\n      //             } else {\n      //                 this.messageCommonService.error(this.tranService.translate(\"datapool.message.notValidPhone\"))\n      //             }\n      //         },\n      //         null, () => {\n      //             this.messageCommonService.offload();\n      //         })\n      // }\n    }\n\n    this.phoneReceiptSelectControl.reload();\n    this.phoneReceiptSelectControl.clearValue();\n    this.phoneReceiptSelectControl.clearFilter();\n    this.phoneReceiptSelectControl.reloadOption();\n    this.phoneReceiptSelect = \"\";\n  }\n  addPhoneTable(value, data) {\n    let me = this;\n    let dataValue;\n    if ((this.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) {\n      dataValue = 10;\n    } else if (this.trafficType == \"Gói Data\") {\n      dataValue = 100;\n    } else {\n      dataValue = null;\n    }\n    if (this.remainDataTotal < dataValue) {\n      return this.messageCommonService.error(this.tranService.translate(\"datapool.message.exceededData\"));\n    }\n    if (value) {\n      if (value?.percent) {\n        value.percent = null;\n      }\n      value.data = dataValue;\n      this.shareList.push(value);\n      setTimeout(function () {\n        me.phoneReceiptSelect = \"\";\n      }, 100);\n      this.isClickCreate = true;\n      this.shareList = [...this.shareList];\n    } else {\n      let pushData = {\n        phoneReceipt: data,\n        name: \"\",\n        email: \"\",\n        data: dataValue\n      };\n      this.shareList.push(pushData);\n      this.shareList = [...this.shareList];\n    }\n    this.caculateRemain();\n  }\n  checkValidAdd() {\n    this.isClickCreate = true;\n    if (!this.phoneList.find(dta => dta.phoneReceipt === this.phoneReceiptSelect)) {\n      this.isClickCreate = false;\n    } else {\n      this.isClickCreate = true;\n    }\n    if (this.phoneReceiptSelect == \"\" || this.phoneReceiptSelect == null || this.phoneReceiptSelect === undefined) {\n      this.isClickCreate = true;\n    }\n    const regex = /^0[0-9]{9,10}$/;\n    const inputValue = this.phoneReceiptSelect;\n    this.isValidPhone = regex.test(inputValue);\n  }\n  checkValidData() {\n    if (this.shareList.length === 0 && this.shareMethod == 0 || this.dataShareGroupTotal === 0 && this.shareMethod == 1) {\n      return true;\n    }\n    if (this.shareMethod == 0 && this.shareList.length != 0) {\n      for (let item of this.shareList) {\n        if (!item.name) item.name = \"\";\n        if (!item.email) item.email = \"\";\n        if (!this.checkDataCondition(item) || !item.data || this.trafficType === \"Gói Data\" && item.data < 100 || (this.trafficType || \"\").toUpperCase().includes(\"Gói SMS\".toUpperCase()) && item.data < 10 || item.name.length >= 50 || item.email.length >= 100 || this.isMailInvalid(item.email)) {\n          return true;\n        }\n      }\n      return false;\n    }\n    if (this.shareMethod == 1 && this.dataShareGroupTotal && this.dataShareGroupTotal != 0 && this.dataShareGroup > 0) {\n      return false;\n    }\n    return true;\n  }\n  cleanArray(arr) {\n    return arr.filter(item => item !== null && item !== undefined && item !== \"\");\n  }\n  deleteItem(i) {\n    this.messageCommonService.confirm(this.tranService.translate(\"datapool.button.delete\"), this.tranService.translate(\"datapool.message.confirmDelete\"), {\n      ok: () => {\n        const a = this.shareList[i].data;\n        if (a) {\n          this.shareList[i].data = null;\n          this.shareList[i].percent = null;\n        }\n        this.shareList = this.shareList.filter((item, index) => index != i);\n        this.caculateRemain();\n      }\n    });\n    // const a = this.shareList[i].data\n    // if(a){\n    //   this.shareList[i].data = null\n    //   this.shareList[i].percent = null\n    // }\n    // this.shareList = this.shareList.filter((item,index) => index != i);\n    // this.caculateRemain();\n  }\n\n  changeData(event, i) {\n    const shareValue = event.target.value;\n    this.shareList[i].data = shareValue;\n    this.shareList[i].percent = Math.round(shareValue / this.originRemain * 100 * 100) / 100;\n    this.caculateRemain();\n  }\n  onKeyDown(event, i) {\n    const key = event.key;\n    const isNumber = /^[0-9]$/.test(key);\n    if (key === \"-\") {\n      return false;\n    }\n    let total = 0;\n    this.shareList.forEach((item, index) => {\n      let value;\n      if (!item.data || item.data == null || item.data == undefined || i == index) {\n        value = 0;\n      } else {\n        value = item.data;\n      }\n      total = Number(total) + Number(value);\n    });\n    const shareValue = event.target.value;\n    const nextValue = parseInt(shareValue + key, 10);\n    let totalUsedValue = nextValue + total;\n    if (this.originRemain - totalUsedValue < 0) {\n      this.messageCommonService.error(this.tranService.translate(\"datapool.message.exceededData\"));\n      return false;\n    }\n    return true;\n  }\n  changeDataName(event, i) {\n    const shareValue = event.target.value;\n    this.shareList[i].name = shareValue;\n  }\n  changeDataMail(event, i) {\n    const shareValue = event.target.value;\n    this.shareList[i].email = shareValue;\n  }\n  shareData() {\n    let remainInit = this.dummyWallet?.find(wallet => wallet.subCode === this.shareDataForm.get('walletValue').value)?.totalRemainingTraffic;\n    let totalDataLocked = 0;\n    let remainAfterLocked = remainInit - totalDataLocked;\n    let totalDataGroup = this.dataSet.total;\n    if (this.shareMethod == 1) {\n      if (this.trafficType === 'Gói Data') {\n        let trafficShare = Math.floor(remainAfterLocked / totalDataGroup / 100) * 100;\n        if (remainInit - trafficShare * totalDataGroup < 0) {\n          this.messageCommonService.error(\"Vượt quá dung lương chia sẻ\");\n        } else {\n          this.dataShareGroup = Math.floor(remainAfterLocked / totalDataGroup / 100) * 100;\n        }\n      }\n      if ((this.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) {\n        this.dataShareGroup = Math.floor(remainAfterLocked / totalDataGroup / 5) * 5;\n      }\n      this.dataShareGroupTotal = remainAfterLocked - Number(this.dataShareGroup) * Number(totalDataGroup);\n      this.percentShareGroup = Math.round(this.dataShareGroup / remainInit * 100 * 100) / 100;\n    } else {\n      let lengthRemain = this.shareList.length;\n      this.shareList.filter(element => element.locked === true).forEach(element => {\n        totalDataLocked = Number(totalDataLocked) + Number(element.data);\n        lengthRemain = lengthRemain - 1;\n      });\n      let remainAfterLocked = remainInit - totalDataLocked;\n      let intPart = Math.floor(remainAfterLocked / lengthRemain);\n      let remainderPart = remainAfterLocked % lengthRemain;\n      this.shareList.filter(element => element.locked === false || !element.locked).forEach(element => {\n        element.data = intPart;\n        element.percent = Math.round(intPart / remainInit * 100 * 100) / 100;\n      });\n      this.remainDataTotal = remainderPart;\n    }\n  }\n  caculateRemain() {\n    let totalQuantity = 0;\n    if (this.shareMethod == 1) {\n      this.dataShareGroupTotal = this.originRemain - (Number(totalQuantity) + Number(this.dataShareGroup));\n    } else {\n      this.shareList.forEach(item => {\n        let value;\n        if (!item.data || item.data == null || item.data == undefined) {\n          value = 0;\n        } else {\n          value = item.data;\n        }\n        totalQuantity = Number(totalQuantity) + Number(value);\n      });\n      this.remainDataTotal = this.originRemain - totalQuantity;\n    }\n  }\n  defineData() {\n    this.isShowDefined = true;\n  }\n  definedDataChange() {\n    if (this.shareMethod == 1) {\n      this.dataShareGroup = this.shareDataForm.controls['definedData'].value;\n      this.percentShareGroup = Math.round(this.shareDataForm.controls['definedData'].value / this.originRemain * 100 * 100) / 100;\n    } else {\n      this.shareList.filter(e => e.locked == false || !e.locked).forEach(element => {\n        element.data = this.shareDataForm.controls['definedData'].value;\n        element.percent = Math.round(this.shareDataForm.controls['definedData'].value / this.originRemain * 100 * 100) / 100;\n      });\n    }\n    this.caculateRemain();\n    this.isShowDefined = !this.isShowDefined;\n  }\n  resetData() {\n    this.messageCommonService.confirm(this.tranService.translate(\"datapool.button.revokeSharing\"), this.tranService.translate(\"datapool.message.confirmRevoke\"), {\n      ok: () => {\n        this.remainDataTotal = this.originRemain;\n        if (this.shareMethod == 0) {\n          this.shareList.forEach(item => {\n            item.data = null;\n            item.percent = null;\n          });\n        } else {\n          this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n          this.dataShareGroup = 0;\n          this.percentShareGroup = 0;\n        }\n      }\n    });\n  }\n  startCountdown() {\n    this.interval = setInterval(() => {\n      if (this.countdown > 0) {\n        this.countdown--;\n      } else {\n        clearInterval(this.interval);\n      }\n    }, 1000);\n  }\n  resetTimer() {\n    this.countdown = 60;\n    clearInterval(this.interval);\n    this.startCountdown();\n    this.sendOTP();\n  }\n  onHideDefine() {\n    this.shareDataForm.controls['definedData'].reset();\n  }\n  handleShareData() {\n    if (this.isNonUseOTP) {\n      this.submitForm();\n    } else {\n      this.sendOTP();\n    }\n  }\n  // send OTP\n  sendOTP() {\n    let me = this;\n    let body = {\n      phoneNumber: this.selectedWallet?.phoneActive?.replace(/^0/, '84')\n    };\n    me.messageCommonService.onload();\n    this.shareService.sendOTP(body, res => {\n      if (res.errorCode === 'BSS-00000000') {\n        me.openSubmit();\n      }\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  checkValidDefine(event) {\n    let lengthRemain = null;\n    if (this.shareMethod == 0) {\n      lengthRemain = this.shareList.length;\n      this.shareList.filter(element => element.locked === true).forEach(element => {\n        lengthRemain = lengthRemain - 1;\n      });\n    } else {\n      lengthRemain = this.dataSet.total;\n    }\n    if (this.shareDataForm.controls['definedData'].value * lengthRemain > this.originRemain) {\n      this.isDefineValueError = true;\n      return;\n    } else {\n      this.isDefineValueError = false;\n    }\n    if (this.trafficType === 'Gói Data') {\n      if (this.shareDataForm.controls['definedData'].value % 100 !== 0) {\n        this.isValidDataFixed = true;\n      } else {\n        this.isValidDataFixed = false;\n      }\n    }\n    if ((this.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) {\n      if (this.shareDataForm.controls['definedData'].value % 5 !== 0) {\n        this.isValidDataFixed = true;\n      } else {\n        this.isValidDataFixed = false;\n      }\n    }\n  }\n  onHideImport() {\n    this.isShowDialogImportByFile = false;\n  }\n  importByFile() {\n    let me = this;\n    me.isShowDialogImportByFile = true;\n    me.observableService.next(CONSTANTS.OBSERVABLE.KEY_INPUT_FILE_VNPT, {});\n    me.isShowErrorUpload = false;\n  }\n  checkDataCondition(item) {\n    if (this.trafficType === 'Gói Data') {\n      return item.data % 100 === 0;\n    }\n    if ((this.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) {\n      return item.data % 5 === 0;\n    }\n    return true;\n  }\n  clearFileCallback() {\n    this.isShowErrorUpload = false;\n  }\n  ngOnDestroy() {\n    clearInterval(this.interval);\n  }\n  downloadTemplate() {\n    this.shareService.downloadTemplateReceiveInfo();\n  }\n  onSelectGroup(group) {\n    let me = this;\n    me.idGroupSelected = group.id;\n    me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n    me.dataShareGroupTotal = me.dummyWallet?.find(wallet => wallet.subCode === me.shareDataForm.get('walletValue').value)?.totalRemainingTraffic;\n    me.dataShareGroup = 0;\n    me.percentShareGroup = 0;\n  }\n  search(page, limit, sort, params) {\n    this.pageNumber = page;\n    this.pageSize = limit;\n    this.sort = sort;\n    let me = this;\n    let totalQuantity = 0;\n    let dataParams = {\n      ...params,\n      page,\n      size: limit,\n      sort\n    };\n    me.messageCommonService.onload();\n    this.groupSubService.searchInGroup(Number(me.idGroupSelected), dataParams, response => {\n      me.dataSet = {\n        content: response.content,\n        total: response.totalElements\n      };\n      // me.dataShareGroupTotal = response.totalElements;\n      totalQuantity = Number(totalQuantity) + Number(me.dataShareGroup * response.totalElements);\n      this.remainDataTotal = this.originRemain - totalQuantity;\n      this.dataShareGroupTotal = this.originRemain - totalQuantity;\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  clearSelectedGroup() {\n    this.groupSelectedValue = [];\n  }\n  clearSelectedData() {\n    this.selectedTableData = [];\n  }\n  onClickSelection() {\n    if (!this.isAutoWallet) {\n      this.messageCommonService.warning(this.tranService.translate(\"datapool.text.hasntAuto\"));\n    }\n  }\n  onHideError() {\n    window.location.reload();\n  }\n  downloadErrorFile() {\n    this.exportToExcel(this.FulldataSetError.content);\n  }\n  exportToExcel(data) {\n    // Chuẩn bị dữ liệu và tiêu đề cột\n    const header = ['STT', 'SĐT', 'Mô tả'];\n    const excelData = data.map((item, index) => [index + 1, item.phoneReceipt, item.description]);\n    // Tạo sheet và thêm tiêu đề\n    const ws = XLSX.utils.aoa_to_sheet([header, ...excelData]);\n    ws['!cols'] = [{\n      wch: 5\n    }, {\n      wch: 21\n    }, {\n      wch: 30\n    }];\n    // Bôi đậm tiêu đề\n    const headerCells = ['A1', 'B1', 'C1']; // Các ô tiêu đề trong sheet (tương ứng với cột A, B, C)\n    headerCells.forEach(cell => {\n      if (ws[cell]) {\n        ws[cell].s = {\n          font: {\n            bold: true // Bôi đậm chữ\n          },\n\n          alignment: {\n            horizontal: 'center',\n            vertical: 'center' // Căn giữa theo chiều dọc\n          }\n        };\n      }\n    });\n    // Căn giữa cho các ô dữ liệu\n    const rowCount = data.length;\n    for (let row = 2; row <= rowCount + 1; row++) {\n      for (let col = 0; col < header.length; col++) {\n        const cellRef = XLSX.utils.encode_cell({\n          r: row - 1,\n          c: col\n        });\n        if (ws[cellRef]) {\n          ws[cellRef].s = {\n            alignment: {\n              horizontal: 'center',\n              vertical: 'center' // Căn giữa theo chiều dọc\n            }\n          };\n        }\n      }\n    }\n    // Tạo workbook và xuất file\n    const wb = {\n      Sheets: {\n        'Danh sách SĐT chia sẻ lỗi': ws\n      },\n      SheetNames: ['Danh sách SĐT chia sẻ lỗi']\n    };\n    const excelBuffer = XLSX.write(wb, {\n      bookType: 'xlsx',\n      type: 'array'\n    });\n    this.saveAsExcelFile(excelBuffer, 'Danh_sach_loi_');\n  }\n  saveAsExcelFile(buffer, fileName) {\n    const data = new Blob([buffer], {\n      type: EXCEL_TYPE\n    });\n    FileSaver.saveAs(data, fileName + '_' + formatDateToDDMMYYYYHHMMSS(new Date().getTime()) + EXCEL_EXTENSION);\n  }\n  pagingDataError(pageNumber, pageSize) {\n    const startIndex = pageNumber * pageSize;\n    const endIndex = startIndex + pageSize;\n    this.dataSetError.content = this.FulldataSetError.content.slice(startIndex, endIndex);\n    console.log(this.dataSetError);\n    this.dataSetError = {\n      ...this.dataSetError\n    };\n  }\n  pageChange(event) {\n    this.normalShareTable.first = event.first;\n    this.normalShareTable.rows = event.rows;\n    this.shareDataForm.controls['pageCurrent'].setValue(event.first / event.rows + 1);\n  }\n  jumpPage() {\n    let pageCurrent = this.shareDataForm.controls.pageCurrent.value;\n    const totalPages = Math.ceil(this.shareList.length / this.normalShareTable.rows);\n    if (pageCurrent < 1 || pageCurrent > totalPages) {\n      return;\n    }\n    this.normalShareTable.first = (pageCurrent - 1) * this.normalShareTable.rows;\n  }\n  getMaxPage() {\n    if (this.shareList.length % this.normalShareTable.rows == 0) {\n      return this.shareList.length / this.normalShareTable.rows;\n    } else {\n      return Math.ceil(this.shareList.length / this.normalShareTable.rows);\n    }\n  }\n  formatNumber(value) {\n    return new Intl.NumberFormat('vi-VN').format(value);\n  }\n  static {\n    this.ɵfac = function ShareDataComponent_Factory(t) {\n      return new (t || ShareDataComponent)(i0.ɵɵdirectiveInject(ShareManagementService), i0.ɵɵdirectiveInject(TrafficWalletService), i0.ɵɵdirectiveInject(GroupSubWalletService), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ShareDataComponent,\n      selectors: [[\"app-share-data\"]],\n      viewQuery: function ShareDataComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.normalShareTable = _t.first);\n        }\n      },\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 60,\n      vars: 79,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [3, \"shareList\", \"remainDataTotal\", \"isShowDialogImportByFile\", \"isShowErrorUpload\", \"totalRemain\", \"trafficType\", \"onHideImport\"], [\"action\", \"\", 1, \"responsive-form\", 3, \"formGroup\", \"submit\"], [1, \"mt-3\"], [1, \"font-bold\", \"text-xl\"], [1, \"flex\", \"flex-row\", \"gap-4\", \"mt-3\", \"px-2\", \"mb-0\"], [1, \"flex\", \"flex-column\", \"gap-2\", \"flex-1\"], [\"htmlFor\", \"walletName\"], [\"formControlName\", \"walletValue\", \"optionLabel\", \"subCode\", \"optionValue\", \"subCode\", \"id\", \"walletName\", \"filter\", \"true\", 3, \"options\", \"placeholder\", \"emptyFilterMessage\", \"onChange\"], [\"htmlFor\", \"datePicker\"], [\"styleClass\", \"w-full\", \"id\", \"datePicker\", \"formControlName\", \"dateShare\", \"dateFormat\", \"dd/mm/yy\", 3, \"showTime\", \"showSeconds\"], [1, \"flex\", \"flex-row\", \"gap-4\", \"px-2\", \"py-0\", \"m-0\"], [4, \"ngIf\"], [\"htmlFor\", \"description\"], [\"rows\", \"5\", \"pInputTextarea\", \"\", \"id\", \"description\", \"formControlName\", \"description\", 1, \"w-full\", 2, \"resize\", \"none\", 3, \"autoResize\", \"placeholder\"], [1, \"flex\", \"flex-column\", \"gap-2\", \"flex-1\", \"field\"], [\"name\", \"selectedShareMethod\", \"value\", \"0\", \"formControlName\", \"selectedShareMethod\", \"inputId\", \"1\", 1, \"p-3\", 3, \"label\", \"ngModel\", \"ngModelChange\", \"onClick\"], [\"name\", \"selectedShareMethod\", \"value\", \"1\", \"inputId\", \"2\", \"formControlName\", \"selectedShareMethod\", 1, \"p-3\", 3, \"label\", \"ngModel\", \"ngModelChange\", \"onClick\"], [1, \"w-full\", \"field\", \"grid\", \"px-2\", \"m-0\", \"py-0\", \"mb-3\"], [\"class\", \"mt-3 table-vnpt\", 4, \"ngIf\"], [3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"onHide\", \"visibleChange\"], [\"htmlFor\", \"\"], [\"type\", \"number\", \"formControlName\", \"definedData\", \"pInputText\", \"\", 3, \"input\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [1, \"m-auto\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"m-auto\", \"mr-2\", 3, \"disabled\", \"click\"], [\"type\", \"button\", \"pButton\", \"\", 1, \"m-auto\", \"ml-2\", \"p-button-outlined\", 3, \"click\"], [3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [\"formControlName\", \"otp\", \"length\", \"6\", 1, \"mx-auto\", \"my-3\", 3, \"integerOnly\"], [\"type\", \"button\", 1, \"border-none\", \"mb-4\", \"cursor-pointer\", \"flex\", \"flex-row\", \"justify-content-center\", \"font-semibold\", 2, \"background-color\", \"transparent\", 3, \"disabled\", \"click\"], [\"pButton\", \"\", 1, \"m-auto\", \"mr-2\", 3, \"disabled\"], [\"pButton\", \"\", 1, \"m-auto\", \"ml-2\", \"p-button-outlined\", 3, \"click\"], [1, \"dataError\", 3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\", \"onHide\"], [\"styleClass\", \"mr-2 p-button-outlined\", \"tooltipPosition\", \"right\", \"icon\", \"pi pi-download\", 2, \"position\", \"absolute\", \"top\", \"30px\", \"right\", \"45px\", \"z-index\", \"5\", \"font-size\", \"10px\", 3, \"pTooltip\", \"onClick\"], [3, \"tableId\", \"fieldId\", \"columns\", \"dataSet\", \"options\", \"pageNumber\", \"pageSize\", \"sort\", \"params\", \"loadData\", \"labelTable\"], [1, \"text-red-500\"], [1, \"mt-3\", \"table-vnpt\"], [1, \"mt-5\", \"flex\", \"flex-row\", \"gap-3\", \"justify-content-between\"], [1, \"flex\", \"flex-row\", \"gap-3\", \"col-7\"], [1, \"col-5\", 2, \"max-width\", \"calc(100% - 1px) !important\"], [\"paramKey\", \"phoneReceipt\", \"keyReturn\", \"phoneReceipt\", \"displayPattern\", \"${phoneReceipt}\", 3, \"control\", \"value\", \"isAutoComplete\", \"isMultiChoice\", \"lazyLoad\", \"placeholder\", \"loadData\", \"valueChange\", \"onchange\", \"onSelectItem\", 4, \"ngIf\"], [\"objectKey\", \"groupSubWallet\", \"paramKey\", \"groupName\", \"keyReturn\", \"id\", \"styleClass\", \"w-full\", \"displayPattern\", \"${groupName}\", \"typeValue\", \"object\", 3, \"value\", \"placeholder\", \"isMultiChoice\", \"valueChange\", \"onSelectItem\", \"onchange\", 4, \"ngIf\"], [\"type\", \"button\", \"pButton\", \"\", 3, \"disabled\", \"label\", \"click\", 4, \"ngIf\"], [1, \"flex\", \"flex-wrap\", \"justify-content-end\", \"gap-4\"], [\"icon\", \"pi pi-file-excel\", \"styleClass\", \"p-button-success\", 3, \"label\", \"click\", 4, \"ngIf\"], [1, \"mb-5\", \"flex\", \"flex-row\", \"gap-3\", \"justify-content-between\", \"text-red-500\", \"px-1\"], [1, \"flex\", \"flex-row\", \"justify-content-between\", \"mb-2\"], [1, \"font-semibold\", \"text-lg\"], [1, \"flex\", \"flex-row\", \"gap-2\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-outlined\", 3, \"disabled\", \"click\"], [\"class\", \"flex flex-row gap-3 mb-2\", 4, \"ngIf\"], [\"class\", \"normalShareTable\", \"dataKey\", \"phoneReceipt\", 3, \"value\", \"tableStyle\", \"selection\", \"totalRecords\", \"paginator\", \"rows\", \"showCurrentPageReport\", \"currentPageReportTemplate\", \"rowsPerPageOptions\", \"selectionChange\", \"onPage\", 4, \"ngIf\"], [\"selectionWidth\", \"8\", 3, \"tableId\", \"fieldId\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"pageNumber\", \"pageSize\", \"sort\", \"params\", \"labelTable\", \"selectItems\", \"tableSelectionText\", \"selectItemsChange\", \"onChangeSelectAllItems\", \"onChangeSelectItem\", 4, \"ngIf\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"gap-3\", \"p-2\", \"mt-5\"], [\"routerLink\", \"/data-pool/shareMgmt/listShare\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-secondary\", \"p-button-outlined\", 3, \"label\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-info\", 3, \"disabled\", \"label\", \"click\"], [\"paramKey\", \"phoneReceipt\", \"keyReturn\", \"phoneReceipt\", \"displayPattern\", \"${phoneReceipt}\", 3, \"control\", \"value\", \"isAutoComplete\", \"isMultiChoice\", \"lazyLoad\", \"placeholder\", \"loadData\", \"valueChange\", \"onchange\", \"onSelectItem\"], [\"objectKey\", \"groupSubWallet\", \"paramKey\", \"groupName\", \"keyReturn\", \"id\", \"styleClass\", \"w-full\", \"displayPattern\", \"${groupName}\", \"typeValue\", \"object\", 3, \"value\", \"placeholder\", \"isMultiChoice\", \"valueChange\", \"onSelectItem\", \"onchange\"], [\"type\", \"button\", \"pButton\", \"\", 3, \"disabled\", \"label\", \"click\"], [\"icon\", \"pi pi-file-excel\", \"styleClass\", \"p-button-success\", 3, \"label\", \"click\"], [1, \"flex\", \"flex-row\", \"gap-3\", \"mb-2\"], [1, \"align-self-auto\", \"my-auto\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-times\", 3, \"click\"], [\"dataKey\", \"phoneReceipt\", 1, \"normalShareTable\", 3, \"value\", \"tableStyle\", \"selection\", \"totalRecords\", \"paginator\", \"rows\", \"showCurrentPageReport\", \"currentPageReportTemplate\", \"rowsPerPageOptions\", \"selectionChange\", \"onPage\"], [\"normalShare\", \"\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"paginatorright\"], [2, \"width\", \"112px\"], [1, \"flex\", \"flex-row\", \"gap-3\", \"ml-2\", \"align-items-center\"], [3, \"disabled\", \"click\"], [1, \"align-self-auto\"], [1, \"box-table-nodata\"], [1, \"pi\", \"pi-inbox\", 2, \"font-size\", \"x-large\"], [2, \"max-width\", \"150px\"], [3, \"value\", \"disabled\", \"click\"], [\"type\", \"text\", \"pInputText\", \"\", 3, \"value\", \"input\"], [1, \"flex\", \"flex-row\", \"align-items-center\"], [\"pInputText\", \"\", \"type\", \"number\", 1, \"border-noround-right\", 3, \"ngClass\", \"disabled\", \"value\", \"input\", \"keydown\"], [\"pInputText\", \"\", \"styleClass\", \"cursor-pointer\", 1, \"border-round-right\", \"border-noround-left\", \"cursor-pointer\", \"surface-300\", 3, \"click\"], [\"class\", \"pi pi-lock\", 4, \"ngIf\"], [\"class\", \"pi pi-lock-open\", 4, \"ngIf\"], [1, \"flex\", \"flex-row\"], [\"type\", \"button\", \"pButton\", \"\", 1, \"p-button-outlined\", 3, \"click\"], [1, \"pi\", \"pi-trash\"], [1, \"pi\", \"pi-lock\"], [1, \"pi\", \"pi-lock-open\"], [1, \"flex\", \"flex-row\", \"justify-content-start\", \"align-items-center\", \"grap-2\", \"ml-3\"], [1, \"mr-2\"], [\"formControlName\", \"pageCurrent\", \"mode\", \"decimal\", 3, \"min\", \"max\", \"keyup.enter\"], [\"selectionWidth\", \"8\", 3, \"tableId\", \"fieldId\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"pageNumber\", \"pageSize\", \"sort\", \"params\", \"labelTable\", \"selectItems\", \"tableSelectionText\", \"selectItemsChange\", \"onChangeSelectAllItems\", \"onChangeSelectItem\"]],\n      template: function ShareDataComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"upload-file-vnpt\", 4);\n          i0.ɵɵlistener(\"onHideImport\", function ShareDataComponent_Template_upload_file_vnpt_onHideImport_5_listener() {\n            return ctx.onHideImport();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"form\", 5);\n          i0.ɵɵlistener(\"submit\", function ShareDataComponent_Template_form_submit_6_listener() {\n            return ctx.submitForm();\n          });\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"p-card\")(9, \"div\", 7);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"div\", 9)(13, \"label\", 10);\n          i0.ɵɵtext(14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"p-dropdown\", 11);\n          i0.ɵɵlistener(\"onChange\", function ShareDataComponent_Template_p_dropdown_onChange_15_listener() {\n            return ctx.changeWallet();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 9)(17, \"label\", 12);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(19, \"p-calendar\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 14);\n          i0.ɵɵtemplate(21, ShareDataComponent_div_21_Template, 2, 1, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 8)(23, \"div\", 9)(24, \"label\", 16);\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(26, \"textarea\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"div\", 18)(28, \"p-radioButton\", 19);\n          i0.ɵɵlistener(\"ngModelChange\", function ShareDataComponent_Template_p_radioButton_ngModelChange_28_listener($event) {\n            return ctx.shareMethod = $event;\n          })(\"onClick\", function ShareDataComponent_Template_p_radioButton_onClick_28_listener() {\n            return ctx.clearSelectedGroup();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"p-radioButton\", 20);\n          i0.ɵɵlistener(\"ngModelChange\", function ShareDataComponent_Template_p_radioButton_ngModelChange_29_listener($event) {\n            return ctx.shareMethod = $event;\n          })(\"onClick\", function ShareDataComponent_Template_p_radioButton_onClick_29_listener() {\n            return ctx.clearSelectedData();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"div\", 21);\n          i0.ɵɵtemplate(31, ShareDataComponent_div_31_Template, 2, 1, \"div\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(32, ShareDataComponent_div_32_Template, 31, 21, \"div\", 22);\n          i0.ɵɵelementStart(33, \"p-dialog\", 23);\n          i0.ɵɵlistener(\"onHide\", function ShareDataComponent_Template_p_dialog_onHide_33_listener() {\n            return ctx.onHideDefine();\n          })(\"visibleChange\", function ShareDataComponent_Template_p_dialog_visibleChange_33_listener($event) {\n            return ctx.isShowDefined = $event;\n          });\n          i0.ɵɵelementStart(34, \"div\", 9)(35, \"label\", 24);\n          i0.ɵɵtext(36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"input\", 25);\n          i0.ɵɵlistener(\"input\", function ShareDataComponent_Template_input_input_37_listener($event) {\n            return ctx.checkValidDefine($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(38, ShareDataComponent_div_38_Template, 2, 1, \"div\", 26);\n          i0.ɵɵtemplate(39, ShareDataComponent_div_39_Template, 2, 1, \"div\", 26);\n          i0.ɵɵtemplate(40, ShareDataComponent_div_40_Template, 2, 1, \"div\", 26);\n          i0.ɵɵelementStart(41, \"div\", 27)(42, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function ShareDataComponent_Template_button_click_42_listener() {\n            return ctx.definedDataChange();\n          });\n          i0.ɵɵtext(43);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function ShareDataComponent_Template_button_click_44_listener() {\n            return ctx.isShowDefined = false;\n          });\n          i0.ɵɵtext(45);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(46, \"p-dialog\", 30);\n          i0.ɵɵlistener(\"visibleChange\", function ShareDataComponent_Template_p_dialog_visibleChange_46_listener($event) {\n            return ctx.isSubmit = $event;\n          });\n          i0.ɵɵelementStart(47, \"div\", 9);\n          i0.ɵɵelement(48, \"p-inputOtp\", 31);\n          i0.ɵɵelementStart(49, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function ShareDataComponent_Template_button_click_49_listener() {\n            return ctx.resetTimer();\n          });\n          i0.ɵɵtext(50);\n          i0.ɵɵtemplate(51, ShareDataComponent_div_51_Template, 2, 3, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"div\", 27)(53, \"button\", 33);\n          i0.ɵɵtext(54);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"button\", 34);\n          i0.ɵɵlistener(\"click\", function ShareDataComponent_Template_button_click_55_listener() {\n            return ctx.isSubmit = false;\n          });\n          i0.ɵɵtext(56);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(57, \"p-dialog\", 35);\n          i0.ɵɵlistener(\"visibleChange\", function ShareDataComponent_Template_p_dialog_visibleChange_57_listener($event) {\n            return ctx.isError = $event;\n          })(\"onHide\", function ShareDataComponent_Template_p_dialog_onHide_57_listener() {\n            return ctx.onHideError();\n          });\n          i0.ɵɵelementStart(58, \"p-button\", 36);\n          i0.ɵɵlistener(\"onClick\", function ShareDataComponent_Template_p_button_onClick_58_listener() {\n            return ctx.downloadErrorFile();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(59, \"table-vnpt\", 37);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"datapool.label.shareData\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"shareList\", ctx.shareList)(\"remainDataTotal\", ctx.remainDataTotal)(\"isShowDialogImportByFile\", ctx.isShowDialogImportByFile)(\"isShowErrorUpload\", ctx.isShowErrorUpload)(\"totalRemain\", ctx.originRemain)(\"trafficType\", ctx.trafficType);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.shareDataForm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"datapool.label.generalInfomation\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"datapool.label.dataWallet\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"options\", ctx.dummyWallet)(\"placeholder\", ctx.tranService.translate(\"datapool.label.selectWallet\"))(\"emptyFilterMessage\", ctx.tranService.translate(\"global.text.nodata\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"datapool.label.shareDate\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"showTime\", true)(\"showSeconds\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.shareDataForm.get(\"walletValue\").invalid && ctx.shareDataForm.get(\"walletValue\").dirty);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"datapool.label.description\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"autoResize\", false)(\"placeholder\", ctx.tranService.translate(\"sim.text.inputDescription\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"datapool.label.shareNormal\"))(\"ngModel\", ctx.shareMethod);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"datapool.label.shareByGroup\"))(\"ngModel\", ctx.shareMethod);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.shareDataForm.get(\"description\").invalid && ctx.shareDataForm.get(\"description\").dirty);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showShareList);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(74, _c12));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"datapool.button.fixedAllocation\"))(\"visible\", ctx.isShowDefined)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"datapool.label.revokeData\", i0.ɵɵpureFunction1(75, _c13, ctx.dataForLabel)));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", (ctx.trafficType || \"\").toUpperCase().includes(\"G\\u00F3i SMS\".toUpperCase()) && (ctx.shareDataForm.controls[\"definedData\"].value < 10 || ctx.isValidDataFixed) && ctx.shareDataForm.controls[\"definedData\"].dirty);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.trafficType == \"G\\u00F3i Data\" && (ctx.shareDataForm.controls[\"definedData\"].value < 100 || ctx.isValidDataFixed) && ctx.shareDataForm.controls[\"definedData\"].dirty);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isDefineValueError);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", (ctx.trafficType || \"\").toUpperCase().includes(\"G\\u00F3i SMS\".toUpperCase()) && ctx.shareDataForm.controls[\"definedData\"].value < 10 || ctx.isValidDataFixed || ctx.trafficType == \"G\\u00F3i Data\" && ctx.shareDataForm.controls[\"definedData\"].value < 100 || ctx.isDefineValueError || ctx.shareDataForm.controls[\"definedData\"].value == \"\" || ctx.shareDataForm.controls[\"definedData\"].value == null || ctx.shareDataForm.controls[\"definedData\"].value == undefined || ctx.isValidDataFixed);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.button.confirm\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.button.cancel\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(77, _c14));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"datapool.label.otpCode\"))(\"visible\", ctx.isSubmit)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"integerOnly\", true);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.countdown > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\"\", ctx.tranService.translate(\"datapool.message.resendOtp\"), \"\\u00A0\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.countdown > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.shareDataForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.button.save\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.button.cancel\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(78, _c15));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"datapool.label.listShareError\"))(\"visible\", ctx.isError)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"pTooltip\", ctx.tranService.translate(\"datapool.label.downloadErrorFile\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"tableId\", \"tbSubShareByGroup\")(\"fieldId\", \"id\")(\"columns\", ctx.columnsError)(\"dataSet\", ctx.dataSetError)(\"options\", ctx.optionTableError)(\"pageNumber\", ctx.pageNumberError)(\"pageSize\", ctx.pageSizeError)(\"sort\", ctx.sortError)(\"params\", ctx.searchInfoError)(\"loadData\", ctx.pagingDataError.bind(ctx));\n        }\n      },\n      dependencies: [i1.RouterLink, i2.Calendar, i3.ButtonDirective, i3.Button, i4.PrimeTemplate, i5.TableVnptComponent, i6.VnptCombobox, i7.UploadFileDialogComponent, i8.Dialog, i9.Breadcrumb, i10.Tooltip, i11.ɵNgNoValidate, i11.DefaultValueAccessor, i11.NumberValueAccessor, i11.NgControlStatus, i11.NgControlStatusGroup, i12.InputText, i13.NgClass, i13.NgIf, i14.Dropdown, i11.FormGroupDirective, i11.FormControlName, i15.Card, i16.InputTextarea, i17.Table, i17.TableCheckbox, i17.TableHeaderCheckbox, i18.InputOtp, i19.RadioButton, i20.InputNumber],\n      styles: [\".dataError .p-dialog .p-dialog-content {\\n  padding: 0 !important;\\n}\\n\\n  .normalShareTable th {\\n  padding: 3px !important;\\n}\\n\\n  .normalShareTable .p-inputnumber-input {\\n  border-top-right-radius: 0 !important;\\n  border-bottom-right-radius: 0 !important;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInNoYXJlLWRhdGEuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxxQkFBQTtBQUNGOztBQUNBO0VBQ0UsdUJBQUE7QUFFRjs7QUFDQTtFQUNFLHFDQUFBO0VBQ0Esd0NBQUE7QUFFRiIsImZpbGUiOiJzaGFyZS1kYXRhLmNvbXBvbmVudC5zY3NzIiwic291cmNlc0NvbnRlbnQiOlsiOjpuZy1kZWVwIC5kYXRhRXJyb3IgLnAtZGlhbG9nIC5wLWRpYWxvZy1jb250ZW50IHtcclxuICBwYWRkaW5nIDogMCFpbXBvcnRhbnQ7XHJcbn1cclxuOjpuZy1kZWVwIC5ub3JtYWxTaGFyZVRhYmxlIHRoIHtcclxuICBwYWRkaW5nOiAzcHghaW1wb3J0YW50O1xyXG59XHJcblxyXG46Om5nLWRlZXAgLm5vcm1hbFNoYXJlVGFibGUgLnAtaW5wdXRudW1iZXItaW5wdXQge1xyXG4gIGJvcmRlci10b3AtcmlnaHQtcmFkaXVzOiAwICFpbXBvcnRhbnQ7XHJcbiAgYm9yZGVyLWJvdHRvbS1yaWdodC1yYWRpdXM6IDAgIWltcG9ydGFudDtcclxufSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdGVtcGxhdGUvZGF0YS1wb29sL3NoYXJlLW1nbXQvc2hhcmUtZGF0YS9zaGFyZS1kYXRhLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UscUJBQUE7QUFDRjs7QUFDQTtFQUNFLHVCQUFBO0FBRUY7O0FBQ0E7RUFDRSxxQ0FBQTtFQUNBLHdDQUFBO0FBRUY7QUFDQSx3dUJBQXd1QiIsInNvdXJjZXNDb250ZW50IjpbIjo6bmctZGVlcCAuZGF0YUVycm9yIC5wLWRpYWxvZyAucC1kaWFsb2ctY29udGVudCB7XHJcbiAgcGFkZGluZyA6IDAhaW1wb3J0YW50O1xyXG59XHJcbjo6bmctZGVlcCAubm9ybWFsU2hhcmVUYWJsZSB0aCB7XHJcbiAgcGFkZGluZzogM3B4IWltcG9ydGFudDtcclxufVxyXG5cclxuOjpuZy1kZWVwIC5ub3JtYWxTaGFyZVRhYmxlIC5wLWlucHV0bnVtYmVyLWlucHV0IHtcclxuICBib3JkZXItdG9wLXJpZ2h0LXJhZGl1czogMCAhaW1wb3J0YW50O1xyXG4gIGJvcmRlci1ib3R0b20tcmlnaHQtcmFkaXVzOiAwICFpbXBvcnRhbnQ7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\", \"input[type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button, input[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button {\\n        \\n\\n        display: none;\\n    }\"]\n    });\n  }\n}\nconst EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';\nconst EXCEL_EXTENSION = '.xlsx';\nfunction formatDateToDDMMYYYYHHMMSS(timestamp) {\n  const date = new Date(timestamp);\n  const dd = String(date.getDate()).padStart(2, '0');\n  const MM = String(date.getMonth() + 1).padStart(2, '0'); // Tháng bắt đầu từ 0\n  const yyyy = date.getFullYear();\n  const HH = String(date.getHours()).padStart(2, '0');\n  const mm = String(date.getMinutes()).padStart(2, '0');\n  const ss = String(date.getSeconds()).padStart(2, '0');\n  return `${dd}${MM}${yyyy}${HH}${mm}${ss}`;\n}", "map": {"version": 3, "names": ["FormControl", "FormGroup", "Validators", "ComponentBase", "ShareManagementService", "ComboLazyControl", "TrafficWalletService", "digitValidator", "CONSTANTS", "GroupSubWalletService", "XLSX", "FileSaver", "convert84to0PhoneNumber", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r7", "tranService", "translate", "ɵɵtemplate", "ShareDataComponent_div_21_div_1_Template", "ɵɵproperty", "ctx_r0", "shareDataForm", "get", "errors", "required", "ctx_r8", "ɵɵpureFunction0", "_c1", "ShareDataComponent_div_31_div_1_Template", "ctx_r1", "maxlength", "ɵɵlistener", "ShareDataComponent_div_32_vnpt_select_7_Template_vnpt_select_valueChange_0_listener", "$event", "ɵɵrestoreView", "_r18", "ctx_r17", "ɵɵnextContext", "ɵɵresetView", "phoneReceiptSelect", "ShareDataComponent_div_32_vnpt_select_7_Template_vnpt_select_onchange_0_listener", "ctx_r19", "checkValidAdd", "ShareDataComponent_div_32_vnpt_select_7_Template_vnpt_select_onSelectItem_0_listener", "ctx_r20", "addPhone", "ctx_r9", "phoneReceiptSelectControl", "getListShareInfoCbb", "bind", "ShareDataComponent_div_32_vnpt_select_8_Template_vnpt_select_valueChange_0_listener", "_r22", "ctx_r21", "groupSelected", "ShareDataComponent_div_32_vnpt_select_8_Template_vnpt_select_onSelectItem_0_listener", "ctx_r23", "onSelectGroup", "ShareDataComponent_div_32_vnpt_select_8_Template_vnpt_select_onchange_0_listener", "ctx_r24", "clearSelectedGroup", "ctx_r10", "ShareDataComponent_div_32_button_9_Template_button_click_0_listener", "_r26", "ctx_r25", "ctx_r11", "isClickCreate", "isValidPhone", "ShareDataComponent_div_32_p_button_11_Template_p_button_click_0_listener", "_r28", "ctx_r27", "importByFile", "ctx_r12", "ɵɵtextInterpolate1", "ctx_r13", "ShareDataComponent_div_32_div_24_Template_button_click_3_listener", "_r30", "ctx_r29", "selectedTableData", "ctx_r14", "length", "ctx_r35", "_c2", "ctx_r36", "_c3", "ctx_r37", "_c4", "ctx_r38", "_c5", "ɵɵattribute", "ctx_r39", "ShareDataComponent_div_32_p_table_25_ng_template_2_Template_p_tableHeaderCheckbox_click_3_listener", "_r41", "ctx_r40", "onClickSelection", "ShareDataComponent_div_32_p_table_25_ng_template_2_th_14_Template", "ShareDataComponent_div_32_p_table_25_ng_template_2_th_15_Template", "ShareDataComponent_div_32_p_table_25_ng_template_2_th_16_Template", "ShareDataComponent_div_32_p_table_25_ng_template_2_th_17_Template", "ɵɵelement", "ShareDataComponent_div_32_p_table_25_ng_template_2_tr_21_Template", "ctx_r32", "isAutoWallet", "trafficType", "toUpperCase", "includes", "shareList", "ctx_r44", "_c6", "ctx_r45", "_c7", "ctx_r46", "_c8", "ctx_r47", "ctx_r50", "ctx_r51", "ShareDataComponent_div_32_p_table_25_ng_template_3_Template_p_tableCheckbox_click_2_listener", "_r54", "ctx_r53", "ShareDataComponent_div_32_p_table_25_ng_template_3_Template_input_input_8_listener", "restoredCtx", "index_r43", "rowIndex", "ctx_r55", "changeDataName", "ShareDataComponent_div_32_p_table_25_ng_template_3_div_9_Template", "ShareDataComponent_div_32_p_table_25_ng_template_3_div_10_Template", "ShareDataComponent_div_32_p_table_25_ng_template_3_Template_input_input_12_listener", "ctx_r56", "changeDataMail", "ShareDataComponent_div_32_p_table_25_ng_template_3_div_13_Template", "ShareDataComponent_div_32_p_table_25_ng_template_3_div_14_Template", "ShareDataComponent_div_32_p_table_25_ng_template_3_Template_input_input_17_listener", "ctx_r57", "changeData", "ShareDataComponent_div_32_p_table_25_ng_template_3_Template_input_keydown_17_listener", "ctx_r58", "onKeyDown", "ShareDataComponent_div_32_p_table_25_ng_template_3_Template_div_click_18_listener", "list_r42", "$implicit", "locked", "ShareDataComponent_div_32_p_table_25_ng_template_3_i_19_Template", "ShareDataComponent_div_32_p_table_25_ng_template_3_i_20_Template", "ShareDataComponent_div_32_p_table_25_ng_template_3_div_21_Template", "ShareDataComponent_div_32_p_table_25_ng_template_3_div_22_Template", "ShareDataComponent_div_32_p_table_25_ng_template_3_div_26_Template", "ShareDataComponent_div_32_p_table_25_ng_template_3_Template_button_click_28_listener", "ctx_r60", "deleteItem", "ctx_r33", "phoneReceipt", "name", "utilService", "checkValidCharacterVietnamese", "email", "isMailInvalid", "ɵɵpureFunction1", "_c9", "data", "checkDataCondition", "percent", "ShareDataComponent_div_32_p_table_25_ng_template_4_Template_p_inputNumber_keyup_enter_3_listener", "_r62", "ctx_r61", "jumpPage", "ctx_r34", "getMaxPage", "ShareDataComponent_div_32_p_table_25_Template_p_table_selectionChange_0_listener", "_r64", "ctx_r63", "ShareDataComponent_div_32_p_table_25_Template_p_table_onPage_0_listener", "ctx_r65", "pageChange", "ShareDataComponent_div_32_p_table_25_ng_template_2_Template", "ShareDataComponent_div_32_p_table_25_ng_template_3_Template", "ShareDataComponent_div_32_p_table_25_ng_template_4_Template", "ctx_r15", "_c10", "_c11", "ShareDataComponent_div_32_table_vnpt_26_Template_table_vnpt_selectItemsChange_0_listener", "_r67", "ctx_r66", "groupSelectedValue", "ShareDataComponent_div_32_table_vnpt_26_Template_table_vnpt_onChangeSelectAllItems_0_listener", "ctx_r68", "ShareDataComponent_div_32_table_vnpt_26_Template_table_vnpt_onChangeSelectItem_0_listener", "ctx_r69", "ctx_r16", "columns", "dataSet", "optionTable", "search", "pageNumber", "pageSize", "sort", "searchInfo", "ShareDataComponent_div_32_vnpt_select_7_Template", "ShareDataComponent_div_32_vnpt_select_8_Template", "ShareDataComponent_div_32_button_9_Template", "ShareDataComponent_div_32_p_button_11_Template", "ShareDataComponent_div_32_div_13_Template", "ShareDataComponent_div_32_Template_button_click_18_listener", "_r71", "ctx_r70", "shareData", "ShareDataComponent_div_32_Template_button_click_20_listener", "ctx_r72", "defineData", "ShareDataComponent_div_32_Template_button_click_22_listener", "ctx_r73", "resetData", "ShareDataComponent_div_32_div_24_Template", "ShareDataComponent_div_32_p_table_25_Template", "ShareDataComponent_div_32_table_vnpt_26_Template", "ShareDataComponent_div_32_Template_button_click_30_listener", "ctx_r74", "handleShareData", "ctx_r2", "walletName", "shareMethod", "ɵɵtextInterpolate3", "formatNumber", "dataShareGroupTotal", "remainDataTotal", "totalData", "total", "controls", "walletValue", "valid", "description", "checkValidData", "ctx_r3", "ctx_r4", "ctx_r5", "ctx_r6", "countdown", "ShareDataComponent", "constructor", "shareService", "walletService", "groupSubService", "injector", "phoneList", "groupShareList", "showShareList", "isShowDefined", "isSubmit", "isError", "idGroupSelected", "walletControl", "paramSearchWallet", "dateShare", "Date", "definedData", "selectedShareMethod", "max<PERSON><PERSON><PERSON>", "otp", "pageCurrent", "isDefineValueError", "isShowDialogImportByFile", "isShareByGroup", "isShowErrorUpload", "isValidDataFixed", "dataForLabel", "optionTableError", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "pageNumberError", "pageSizeError", "sortError", "dataSetError", "content", "searchInfoError", "value", "columnsError", "key", "size", "align", "isShow", "isSort", "dataShareGroup", "isNonUseOTP", "openSubmit", "startCountdown", "pattern", "test", "convertDateToString", "pad", "num", "toString", "padStart", "day", "getDate", "month", "getMonth", "year", "getFullYear", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "submitForm", "me", "dataBodyGroup", "subCode", "<PERSON><PERSON><PERSON><PERSON>", "reason", "sharingDay", "idGroup", "Number", "numOfShare", "transId", "expireDate", "type", "autoPhones", "map", "item", "messageCommonService", "onload", "shareTrafficByGroup", "res", "error_code", "ok", "dataErrorTable", "countToTal", "for<PERSON>ach", "element", "status", "error", "push", "sdt", "FulldataSetError", "success", "router", "navigate", "message", "offload", "convertShareInfo", "e", "phone", "String", "isAuto", "find", "dataBody", "shareInfos", "console", "log", "shareTraffic", "ngOnInit", "isShowTooltip", "style", "display", "max<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "funcConvertText", "percentShareGroup", "items", "label", "routerLink", "home", "icon", "getWalletCbb", "observableService", "subscribe", "OBSERVABLE", "UPDATE_SHARE_INFO", "next", "loadData", "selectItems", "disabledCheckBox", "paginator", "oldSelectData", "index", "localeCompare", "sensitivity", "caculateRemain", "warning", "changeWallet", "walletId", "wallet", "dummy<PERSON><PERSON><PERSON>", "w", "autoType", "packageName", "purchasedTraffic", "totalRemainingTraffic", "originRemain", "params", "callback", "response", "getShareGroupInfo", "totalPages", "totalElements", "getWalletCbbShare", "onSelected", "undefined", "cleanArray", "dta", "i", "addPhoneTable", "reload", "clearValue", "clearFilter", "reloadOption", "dataValue", "setTimeout", "pushData", "regex", "inputValue", "arr", "filter", "confirm", "a", "event", "shareValue", "target", "Math", "round", "isNumber", "nextValue", "parseInt", "totalUsedValue", "remainInit", "totalDataLocked", "remainAfterLocked", "totalDataGroup", "trafficShare", "floor", "lengthRemain", "intPart", "remainderPart", "totalQuantity", "definedDataChange", "interval", "setInterval", "clearInterval", "resetTimer", "sendOTP", "onHideDefine", "reset", "body", "phoneNumber", "phoneActive", "replace", "errorCode", "checkValidDefine", "onHideImport", "KEY_INPUT_FILE_VNPT", "clearFileCallback", "ngOnDestroy", "downloadTemplate", "downloadTemplateReceiveInfo", "group", "id", "page", "limit", "dataParams", "searchInGroup", "clearSelectedData", "onHideError", "window", "location", "downloadErrorFile", "exportToExcel", "header", "excelData", "ws", "utils", "aoa_to_sheet", "wch", "headerCells", "cell", "s", "font", "bold", "alignment", "horizontal", "vertical", "rowCount", "row", "col", "cellRef", "encode_cell", "r", "c", "wb", "Sheets", "SheetNames", "excelBuffer", "write", "bookType", "saveAsExcelFile", "buffer", "fileName", "Blob", "EXCEL_TYPE", "saveAs", "formatDateToDDMMYYYYHHMMSS", "getTime", "EXCEL_EXTENSION", "pagingDataError", "startIndex", "endIndex", "slice", "normalShareTable", "first", "rows", "setValue", "ceil", "Intl", "NumberFormat", "format", "ɵɵdirectiveInject", "Injector", "selectors", "viewQuery", "ShareDataComponent_Query", "rf", "ctx", "ShareDataComponent_Template_upload_file_vnpt_onHideImport_5_listener", "ShareDataComponent_Template_form_submit_6_listener", "ShareDataComponent_Template_p_dropdown_onChange_15_listener", "ShareDataComponent_div_21_Template", "ShareDataComponent_Template_p_radioButton_ngModelChange_28_listener", "ShareDataComponent_Template_p_radioButton_onClick_28_listener", "ShareDataComponent_Template_p_radioButton_ngModelChange_29_listener", "ShareDataComponent_Template_p_radioButton_onClick_29_listener", "ShareDataComponent_div_31_Template", "ShareDataComponent_div_32_Template", "ShareDataComponent_Template_p_dialog_onHide_33_listener", "ShareDataComponent_Template_p_dialog_visibleChange_33_listener", "ShareDataComponent_Template_input_input_37_listener", "ShareDataComponent_div_38_Template", "ShareDataComponent_div_39_Template", "ShareDataComponent_div_40_Template", "ShareDataComponent_Template_button_click_42_listener", "ShareDataComponent_Template_button_click_44_listener", "ShareDataComponent_Template_p_dialog_visibleChange_46_listener", "ShareDataComponent_Template_button_click_49_listener", "ShareDataComponent_div_51_Template", "ShareDataComponent_Template_button_click_55_listener", "ShareDataComponent_Template_p_dialog_visibleChange_57_listener", "ShareDataComponent_Template_p_dialog_onHide_57_listener", "ShareDataComponent_Template_p_button_onClick_58_listener", "invalid", "dirty", "ɵɵstyleMap", "_c12", "_c13", "_c14", "_c15", "timestamp", "date", "dd", "MM", "yyyy", "HH", "mm", "ss"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\data-pool\\share-mgmt\\share-data\\share-data.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\data-pool\\share-mgmt\\share-data\\share-data.component.html"], "sourcesContent": ["import {Component, Inject, Injector, <PERSON><PERSON><PERSON><PERSON>, ViewChild} from '@angular/core';\r\nimport {FormArray, FormControl, FormGroup, Validators} from '@angular/forms';\r\nimport {MenuItem} from 'primeng/api';\r\nimport {ComponentBase} from 'src/app/component.base';\r\nimport {ShareManagementService} from 'src/app/service/datapool/ShareManagementService';\r\nimport {ComboLazyControl} from 'src/app/template/common-module/combobox-lazyload/combobox.lazyload';\r\nimport {GroupInfo, PhoneInfo, ShareDetail, Wallet} from \"../../data-pool.type-data\";\r\nimport {TrafficWalletService} from \"../../../../service/datapool/TrafficWalletService\";\r\nimport {digitValidator} from 'src/app/template/common-module/validatorCustoms';\r\nimport {CONSTANTS, isVinaphoneNumber} from \"../../../../service/comon/constants\";\r\nimport {OptionInputFile} from \"../../../common-module/input-file/input.file.component\";\r\nimport {GroupSubWalletService} from \"../../../../service/group-sub-wallet/GroupSubWalletService\";\r\nimport {ColumnInfo, OptionTable} from \"../../../common-module/table/table.component\";\r\nimport * as XLSX from 'xlsx-js-style';\r\nimport * as FileSaver from 'file-saver';\r\nimport {convert84to0PhoneNumber} from \"../../../common-module/utils/util\";\r\nimport {Table} from \"primeng/table\";\r\nimport {ev} from \"@fullcalendar/core/internal-common\";\r\n\r\n@Component({\r\n    selector: 'app-share-data',\r\n    templateUrl: './share-data.component.html',\r\n    styleUrls: ['./share-data.component.scss']\r\n})\r\nexport class ShareDataComponent extends ComponentBase implements OnDestroy {\r\n  items: MenuItem[];\r\n  home: MenuItem;\r\n  dummyWallet:Wallet[];\r\n  shareList: ShareDetail[];\r\n  phoneList : PhoneInfo[] = [];\r\n  groupShareList : GroupInfo[] = [];\r\n  filteredPhoneList : PhoneInfo[];\r\n  selectedWallet: Wallet;\r\n  walletName: String;\r\n  showShareList: boolean =false;\r\n  isShowDefined:boolean = false;\r\n  isSubmit: boolean = false;\r\n  isError = false\r\n  phoneReceiptSelect: any = \"\";\r\n  phoneReceiptSelectControl = new ComboLazyControl();\r\n  groupSelected:any;\r\n  idGroupSelected: any = \"\";\r\n  shareGroupChoose: any;\r\n  FulldataSetError:any\r\n  constructor( @Inject(ShareManagementService) private shareService: ShareManagementService,\r\n               @Inject(TrafficWalletService) private walletService: TrafficWalletService,\r\n               @Inject(GroupSubWalletService) private groupSubService: GroupSubWalletService,\r\n              injector: Injector,\r\n            ) {super(injector);}\r\n  @ViewChild('normalShare') normalShareTable!: Table;\r\n  walletControl: ComboLazyControl = new ComboLazyControl();\r\n  paramSearchWallet = {};\r\n  countdown:number;\r\n  interval: any;\r\n  trafficType: string;\r\n  isClickCreate: boolean = true\r\n  shareDataForm = new FormGroup({\r\n    walletValue: new FormControl(null, [Validators.required]),\r\n    dateShare: new FormControl(new Date),\r\n    definedData: new FormControl(),\r\n    selectedShareMethod: new FormControl(0),\r\n    description: new FormControl(null, [Validators.maxLength(255)]),\r\n    otp: new FormControl(null, [Validators.required, digitValidator(6)]),\r\n    pageCurrent: new FormControl(1),\r\n  });\r\n  originRemain: number;\r\n  remainDataTotal:number;\r\n  isDefineValueError: boolean = false;\r\n  totalData: number;\r\n  isShowDialogImportByFile: boolean = false;\r\n  isShareByGroup: boolean = false;\r\n  isShowErrorUpload: boolean = false;\r\n  optionInputFile: OptionInputFile;\r\n  fileObject: any;\r\n  isValidPhone: boolean = true;\r\n  isValidDataFixed: boolean = false;\r\n  messageErrorUpload: string | null;\r\n  dataForLabel: string = \"Data\";\r\n  selectItems: Array<{id:number,[key:string]:any}>;\r\n  optionTable: OptionTable;\r\n  pageNumber: number;\r\n  pageSize: number;\r\n  sort: string;\r\n  dataSet: {\r\n    content: Array<any>,\r\n    total: number\r\n  };\r\n  searchInfo: {\r\n    value?: string,\r\n  };\r\n  columns: Array<ColumnInfo>;\r\n    optionTableError: OptionTable = {\r\n        hasClearSelected: false,\r\n        hasShowChoose: false,\r\n        hasShowIndex: true,\r\n        hasShowToggleColumn: false,\r\n    };\r\n    pageNumberError: number = 0;\r\n    pageSizeError: number = 10;\r\n    sortError: string = \"\";\r\n    dataSetError: {\r\n        content: Array<any>,\r\n        total: number\r\n    } = {\r\n        content: [],\r\n        total: 0\r\n    };\r\n    searchInfoError: {\r\n        value?: string,\r\n    } = {\r\n        value: \"\"\r\n    };\r\n    columnsError: Array<ColumnInfo> = [\r\n        {\r\n            name: this.tranService.translate(\"datapool.label.phone\"),\r\n            key: \"phoneReceipt\",\r\n            size: \"fit-content\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: false,\r\n        },\r\n        {\r\n            name: this.tranService.translate(\"datapool.label.description\"),\r\n            key: \"description\",\r\n            size: \"fit-content\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: false\r\n        }];\r\n  dataShareGroup: number = 0;\r\n  dataShareGroupTotal: number;\r\n  shareMethod: number;\r\n  percentShareGroup: number;\r\n\r\n  selectedTableData = [];\r\n  isNonUseOTP = true\r\n  groupSelectedValue: Array<any> = [];\r\n  isAutoWallet = true\r\n\r\n  openSubmit() {\r\n    this.isSubmit = true\r\n    this.countdown = 60\r\n    this.startCountdown()\r\n  }\r\n\r\n  isMailInvalid(email:string){\r\n    if (!email){\r\n      return false\r\n    }\r\n    const pattern:RegExp = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z0-9-.]+$/\r\n    return !pattern.test(email);\r\n  }\r\n\r\n  convertDateToString(value: Date): string {\r\n    if (value == null) return \"\";\r\n\r\n    const pad = (num: number) => num.toString().padStart(2, '0');\r\n\r\n    const day = pad(value.getDate());\r\n    const month = pad(value.getMonth() + 1); // Tháng bắt đầu từ 0 nên cần cộng 1\r\n    const year = value.getFullYear();\r\n\r\n    const hours = pad(value.getHours());\r\n    const minutes = pad(value.getMinutes());\r\n    const seconds = pad(value.getSeconds());\r\n\r\n    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;\r\n  }\r\n\r\n  submitForm() {\r\n    let me = this;\r\n    this.groupSelected = null\r\n    if (this.shareMethod == 1) {\r\n        const dataBodyGroup = {\r\n            subCode: this.selectedWallet.subCode,\r\n            otp: this.shareDataForm.get('otp').value,\r\n            reason: this.shareDataForm.get('description').value,\r\n            sharingDay: this.convertDateToString(this.shareDataForm.get('dateShare').value),\r\n            idGroup: Number(this.idGroupSelected),\r\n            numOfShare: Number(this.dataShareGroup),\r\n            transId: '',\r\n            trafficType: \"DATA\",\r\n            expireDate: '30 ngày',\r\n            type: 0,\r\n            autoPhones: this.groupSelectedValue.map(item => item.phoneReceipt)\r\n        }\r\n\r\n        me.messageCommonService.onload();\r\n\r\n        this.shareService.shareTrafficByGroup(dataBodyGroup, (res) => {\r\n\r\n            if (res.error_code === 'BSS-00000000') {\r\n                let ok = 1\r\n                let dataErrorTable = []\r\n                let countToTal = 0\r\n                res.data.forEach(element => {\r\n                    if(element.status == 0 && element.error != \"\"){\r\n                        // me.messageCommonService.error(`Thuê bao ${element.sdt} có lỗi (${element.error})`);\r\n                        dataErrorTable.push({\r\n                            phoneReceipt: convert84to0PhoneNumber(element.sdt),\r\n                            // description: element.error\r\n                            description: \"Lỗi do thuê bao không hợp lệ\"\r\n                        })\r\n                        ok = 0\r\n                    }\r\n                    countToTal++;\r\n                });\r\n                if(ok == 0){\r\n                    this.isError = true\r\n                    this.dataSetError = {\r\n                        content: dataErrorTable,\r\n                        total: dataErrorTable.length,\r\n                    }\r\n                    this.FulldataSetError = {...this.dataSetError}\r\n                    me.messageCommonService.error(me.tranService.translate(\"datapool.message.shareNotifyFail\", {success: countToTal - dataErrorTable.length, total : countToTal}), null, 10000);\r\n                } else {\r\n                me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n                this.router.navigate(['/data-pool/shareMgmt/listShare']);\r\n                }\r\n            }else if (res.error_code === 'Success') {\r\n                me.messageCommonService.success(me.tranService.translate(\"datapool.message.shareNotifyBackground\"),null, 15000);\r\n                this.router.navigate(['/data-pool/shareMgmt/listShare']);\r\n            } else {\r\n                this.messageCommonService.error(res.message)\r\n            }\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    } else {\r\n        const convertShareInfo = this.shareList.map((e) => ({\r\n            phone: String(e?.phoneReceipt),\r\n            trafficType: \"DATA\",\r\n            numOfShare: Number(e?.data),\r\n            expireDate: '30 ngày',\r\n            name: e?.name,\r\n            email: e?.email,\r\n            type: 0,\r\n            isAuto: !!this.selectedTableData.find(item=> item.phoneReceipt=== e.phoneReceipt),\r\n        }));\r\n\r\n        const dataBody = {\r\n            subCode: this.selectedWallet.subCode,\r\n            otp: this.shareDataForm.get('otp').value,\r\n            reason: this.shareDataForm.get('description').value,\r\n            sharingDay: this.convertDateToString(this.shareDataForm.get('dateShare').value),\r\n            shareInfos: convertShareInfo,\r\n            transId: ''\r\n        };\r\n\r\n        console.log(convertShareInfo)\r\n\r\n        me.messageCommonService.onload();\r\n\r\n        this.shareService.shareTraffic(dataBody, (res) => {\r\n\r\n            if (res.error_code === 'BSS-00000000') {\r\n                let ok = 1\r\n                let dataErrorTable = []\r\n                res.data.forEach(element => {\r\n                    if(element.status == 0 && element.error != \"\"){\r\n                        // me.messageCommonService.error(`Thuê bao ${element.sdt} có lỗi (${element.error})`);\r\n                        dataErrorTable.push({\r\n                            phoneReceipt: convert84to0PhoneNumber(element.sdt),\r\n                            // description: element.error\r\n                            description: \"Lỗi do thuê bao không hợp lệ\"\r\n                        })\r\n                        ok = 0\r\n                    }\r\n                });\r\n                if(ok == 0){\r\n                    this.isError = true\r\n                    this.dataSetError = {\r\n                        content: dataErrorTable,\r\n                        total: dataErrorTable.length,\r\n                    }\r\n                    this.FulldataSetError = {...this.dataSetError}\r\n                    me.messageCommonService.error(me.tranService.translate(\"datapool.message.shareNotifyFail\", {success: this.shareList.length - this.dataSetError.content.length, total : this.shareList.length}), null, 10000);\r\n                }else {\r\n                    me.messageCommonService.success(me.tranService.translate(\"datapool.message.shareNotifySuccess\", {success: this.shareList.length, total : this.shareList.length}), null, 10000);\r\n                    this.router.navigate(['/data-pool/shareMgmt/listShare']);\r\n                }\r\n            }else if (res.error_code === 'Success') {\r\n                me.messageCommonService.success(me.tranService.translate(\"datapool.message.shareNotifyBackground\"),null, 15000);\r\n                this.router.navigate(['/data-pool/shareMgmt/listShare']);\r\n            } else {\r\n                this.messageCommonService.error(res.message)\r\n            }\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        });\r\n    }\r\n    this.isNonUseOTP = true\r\n  }\r\n\r\n  ngOnInit() {\r\n      let me = this;\r\n      me.columns = [\r\n          {\r\n              name: this.tranService.translate(\"datapool.label.phone\"),\r\n              key: \"phoneReceipt\",\r\n              size: \"10%\",\r\n              align: \"left\",\r\n              isShow: true,\r\n              isSort: false,\r\n          },\r\n          {\r\n              name: this.tranService.translate(\"datapool.label.fullName\"),\r\n              key: \"name\",\r\n              size: \"25%\",\r\n              align: \"left\",\r\n              isShow: true,\r\n              isSort: false\r\n          },\r\n          {\r\n              name: this.tranService.translate(\"datapool.label.email\"),\r\n              key: \"email\",\r\n              size: \"25%\",\r\n              align: \"left\",\r\n              isShow: true,\r\n              isSort: false,\r\n              isShowTooltip: true,\r\n              style: {\r\n                  display: 'inline-block',\r\n                  maxWidth: '350px',\r\n                  overflow: 'hidden',\r\n                  textOverflow: 'ellipsis'\r\n              }\r\n          },\r\n          {\r\n              name: me.trafficType == 'Gói Data'\r\n                  ? me.tranService.translate(\"datapool.label.sharingData\", {type: 'MB'})\r\n                  : me.trafficType == 'Gói thoại'\r\n                      ? me.tranService.translate(\"datapool.label.sharingData\", {type: 'Phút'})\r\n                      : (me.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())\r\n                          ? me.tranService.translate(\"datapool.label.sharingData\", {type: 'SMS'})\r\n                          : me.tranService.translate(\"datapool.label.sharingDataNotType\"),\r\n              key: \"sharingData\",\r\n              size: \"25%\",\r\n              align: \"left\",\r\n              isShow: true,\r\n              isSort: false,\r\n              funcConvertText(value) {\r\n                  switch (me.trafficType) {\r\n                      case 'Gói Data':\r\n                          return `${me.dataShareGroup?.toString()} (MB)`;\r\n                      case 'Gói thoại':\r\n                          return `${me.dataShareGroup?.toString()} (Phút)`;\r\n                      case 'Gói SMS':\r\n                      case 'Gói SMS ngoại mạng':\r\n                      case 'Gói SMS VNP':\r\n                          return `${me.dataShareGroup?.toString()} (SMS)`;\r\n                      default:\r\n                          return '';\r\n                  }\r\n              },\r\n          },\r\n          {\r\n              name: this.tranService.translate(\"datapool.label.percentage\"),\r\n              key: \"percentDataGroup\",\r\n              size: \"25%\",\r\n              align: \"left\",\r\n              isShow: true,\r\n              isSort: false,\r\n              funcConvertText(value) {\r\n                  return me.percentShareGroup?.toString() ? `${me.percentShareGroup?.toString()} %` : '';\r\n              },\r\n          },\r\n      ]\r\n      this.items = [{label: this.tranService.translate(`global.menu.trafficManagement`)}, {\r\n          label: this.tranService.translate(\"global.menu.shareList\"),\r\n          routerLink: [\"/data-pool/shareMgmt/listShare\"]\r\n      }, {label: this.tranService.translate(\"datapool.label.shareData\")}];\r\n      this.home = {icon: 'pi pi-home', routerLink: '/'};\r\n      this.getWalletCbb();\r\n      this.showShareList = false;\r\n      this.phoneList = [];\r\n      // this.changeWallet();\r\n      this.observableService.subscribe(CONSTANTS.OBSERVABLE.UPDATE_SHARE_INFO, {\r\n          next: this.loadData.bind(this)\r\n      });\r\n      this.shareMethod = 0;\r\n      me.selectItems = [];\r\n      me.optionTable = {\r\n          hasClearSelected: false,\r\n          hasShowChoose: true,\r\n          hasShowIndex: true,\r\n          hasShowToggleColumn: false,\r\n          disabledCheckBox: false,\r\n          paginator: true\r\n      }\r\n      me.searchInfo = {\r\n          value: \"\"\r\n      };\r\n      me.pageNumber = 0;\r\n      me.pageSize = 10;\r\n      me.sort = \"phoneReceipt,desc\";\r\n      me.dataSet = {\r\n          content: [],\r\n          total: 0\r\n      }\r\n  }\r\n\r\n  loadData(data): void {\r\n      if (data) {\r\n          if(data.length > 1000){\r\n              this.messageCommonService.error(this.tranService.translate(\"error.invalid.file.form.maxrow\"))\r\n              return\r\n          }\r\n          this.shareList = data;\r\n          let oldSelectData = this.selectedTableData\r\n          this.selectedTableData = []\r\n          //data.isAuto.toLowerCase()===\"có\" || data.isAuto.toLowerCase()==\"co\"\r\n          data.forEach((item, index) => {\r\n              if(item.isAuto && (item.isAuto.localeCompare(\"Có\", 'vi', { sensitivity: 'base' }) === 0 || item.isAuto.localeCompare(\"Co\", 'vi', { sensitivity: 'base' }) === 0)){\r\n\r\n                  this.selectedTableData.push(item)\r\n              }\r\n          })\r\n          this.selectedTableData = [\r\n              ...this.selectedTableData,\r\n              ...oldSelectData\r\n          ]\r\n      }\r\n      this.caculateRemain()\r\n      if(!this.isAutoWallet){\r\n          this.selectedTableData = []\r\n          this.messageCommonService.warning(\"Ví chưa đăng ký chia sẻ không cần OTP nên các thuê bao đã tích chọn tự động sẽ bị bỏ tích\");\r\n      }\r\n  }\r\n\r\n  changeWallet() {\r\n      let me = this;\r\n      if (this.shareMethod == 1) {\r\n          this.dataShareGroup = 0;\r\n          this.percentShareGroup = 0;\r\n      }\r\n      if(this.shareList){\r\n          this.shareList.forEach(item => {\r\n              item.data = null;\r\n              item.percent = null;\r\n          });\r\n      }else{\r\n          this.shareList = [];\r\n      }\r\n    const walletId = this.shareDataForm.get('walletValue').value;\r\n    const wallet = this.dummyWallet?.find(w => w.subCode === walletId);\r\n\r\n    if (wallet) {\r\n      this.selectedWallet = wallet;\r\n      if(wallet.autoType != 2){\r\n          this.isNonUseOTP = true\r\n          this.isAutoWallet = true\r\n          me.optionTable = {\r\n              hasClearSelected: false,\r\n              hasShowChoose: true,\r\n              hasShowIndex: true,\r\n              hasShowToggleColumn: false,\r\n              disabledCheckBox: false,\r\n              paginator: true\r\n          }\r\n      }else{\r\n          this.isNonUseOTP = false\r\n          this.isAutoWallet = false\r\n          me.optionTable = {\r\n              hasClearSelected: false,\r\n              hasShowChoose: true,\r\n              hasShowIndex: true,\r\n              hasShowToggleColumn: false,\r\n              disabledCheckBox: true,\r\n              paginator: true\r\n          }\r\n      }\r\n        this.groupSelectedValue = []\r\n        this.selectedTableData = []\r\n      this.walletName = this.selectedWallet.packageName;\r\n    } else {\r\n        console.log('Không tìm thấy ví có id =', walletId);\r\n    }\r\n    // this.getListShareInfoCbb();\r\n    this.showShareList = true\r\n    // typeOfTraffic\r\n    this.trafficType = this.dummyWallet?.find(wallet => wallet.subCode === this.shareDataForm.get(\"walletValue\").value)?.trafficType\r\n    this.totalData = this.dummyWallet?.find(wallet => wallet.subCode === this.shareDataForm.get(\"walletValue\").value)?.purchasedTraffic\r\n    this.remainDataTotal = this.dummyWallet?.find(wallet => wallet.subCode === this.shareDataForm.get('walletValue').value)?.totalRemainingTraffic\r\n    this.dataShareGroupTotal = this.dummyWallet?.find(wallet => wallet.subCode === this.shareDataForm.get('walletValue').value)?.totalRemainingTraffic\r\n    this.originRemain = this.dummyWallet?.find(wallet => wallet.subCode === this.shareDataForm.get('walletValue').value)?.totalRemainingTraffic\r\n\r\n    if((this.trafficType || \"\").toUpperCase().includes(\"Gói SMS\".toUpperCase())){\r\n        this.dataForLabel = \"SMS\"\r\n    }else if (this.trafficType == \"Gói Data\"){\r\n        this.dataForLabel = \"MB\"\r\n    }\r\n\r\n\r\n  }\r\n\r\n    getListShareInfoCbb(params, callback) {\r\n        return this.shareService.getListShareInfoCbb(params, (response)=>{\r\n            this.phoneList = response.content;\r\n            callback(response)\r\n        });\r\n    }\r\n\r\n    getShareGroupInfo(params, callback){\r\n        let me = this;\r\n        this.messageCommonService.onload();\r\n        this.groupSubService.search(params, (response) => {\r\n            // console.log(response)\r\n            me.groupShareList = response.content;\r\n            let data = {\r\n                content: response.content,\r\n                totalPages: response.totalElements\r\n            }\r\n        },null,()=>{\r\n            this.messageCommonService.offload()\r\n        })\r\n    }\r\n\r\n  getWalletCbb() {\r\n      let me = this;\r\n      this.messageCommonService.onload()\r\n      this.walletService.getWalletCbbShare((response) => {\r\n          me.dummyWallet = response;\r\n      }, null, () => {\r\n          this.messageCommonService.offload()\r\n      })\r\n  }\r\n\r\n  addPhone(data, onSelected?){\r\n    let me = this;\r\n      if(data === null || data === undefined || data === \"\"){\r\n          return\r\n      }\r\n      if(this.shareList.length > 999){\r\n          this.messageCommonService.error(this.tranService.translate(\"error.invalid.file.form.maxrow\"))\r\n          return\r\n      }\r\n    this.isClickCreate = false\r\n    this.shareList = this.cleanArray(this.shareList)\r\n    const value = this.phoneList.find(dta => dta.phoneReceipt === data)\r\n    if(this.shareList.find(i => i?.phoneReceipt === data)){\r\n      this.messageCommonService.error(this.tranService.translate(\"datapool.message.existed\"))\r\n    }else {\r\n        /**\r\n         * UAT 2.4 issue 31\r\n         * Khi add số thuê bao được chia sẻ, nếu đã có trong danh sách thì bỏ qua check số đó là thuê bao vinaphone hay không, trong các trường hợp sau:\r\n         * - Chia sẻ thường\r\n         * - Nhóm chia sẻ tự động > thêm sdt chia sẻ tự động\r\n         * - Thêm thuê bao vào nhóm\r\n         * - icon chia sẻ ở Danh sách ví\r\n         */\r\n        this.addPhoneTable(value, data)\r\n        /**\r\n         * bỏ check số vina\r\n         */\r\n        // if (onSelected) {\r\n        //     this.addPhoneTable(value, data)\r\n        // } else {\r\n        //     const phone = String(data)?.replace(/^0/,\"84\");\r\n        //     this.messageCommonService.onload()\r\n        //     this.walletService.checkParticipant({phoneNumber: phone},\r\n        //         (response) => {\r\n        //             if (response.error_code === \"0\" && (response.result === \"02\" || response.result === \"11\")) {\r\n        //                 this.addPhoneTable(value, data)\r\n        //             } else if (response.error_code === \"0\" && response.result === \"0\") {\r\n        //                 if (isVinaphoneNumber(data)) {\r\n        //                     this.addPhoneTable(value, data)\r\n        //                 } else {\r\n        //                     this.messageCommonService.error(this.tranService.translate(\"datapool.message.notValidPhone\"))\r\n        //                 }\r\n        //             } else {\r\n        //                 this.messageCommonService.error(this.tranService.translate(\"datapool.message.notValidPhone\"))\r\n        //             }\r\n        //         },\r\n        //         null, () => {\r\n        //             this.messageCommonService.offload();\r\n        //         })\r\n        // }\r\n\r\n    }\r\n      this.phoneReceiptSelectControl.reload();\r\n      this.phoneReceiptSelectControl.clearValue();\r\n      this.phoneReceiptSelectControl.clearFilter();\r\n      this.phoneReceiptSelectControl.reloadOption();\r\n      this.phoneReceiptSelect = \"\"\r\n  }\r\n\r\n    addPhoneTable(value, data){\r\n        let me = this;\r\n        let dataValue;\r\n        if((this.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())){\r\n            dataValue = 10\r\n        }else if(this.trafficType == \"Gói Data\"){\r\n            dataValue = 100\r\n        }else{\r\n            dataValue = null\r\n        }\r\n        if (this.remainDataTotal < dataValue){\r\n            return this.messageCommonService.error(this.tranService.translate(\"datapool.message.exceededData\"))\r\n        }\r\n        if(value){\r\n            if(value?.percent){\r\n                value.percent=null;\r\n            }\r\n            value.data = dataValue;\r\n            this.shareList.push(value)\r\n            setTimeout(function(){\r\n                me.phoneReceiptSelect = \"\";\r\n            },100);\r\n            this.isClickCreate = true\r\n            this.shareList = [...this.shareList]\r\n        }else{\r\n            let pushData: ShareDetail = {\r\n                phoneReceipt: data,\r\n                name:\"\",\r\n                email:\"\",\r\n                data:dataValue\r\n            }\r\n            this.shareList.push(pushData)\r\n            this.shareList = [...this.shareList]\r\n        }\r\n        this.caculateRemain()\r\n    }\r\n\r\n  checkValidAdd(){\r\n    this.isClickCreate = true\r\n    if(!this.phoneList.find(dta => dta.phoneReceipt === this.phoneReceiptSelect)){\r\n      this.isClickCreate = false\r\n    }else{\r\n      this.isClickCreate = true\r\n    }\r\n    if(this.phoneReceiptSelect == \"\"|| this.phoneReceiptSelect == null || this.phoneReceiptSelect === undefined){\r\n      this.isClickCreate = true\r\n    }\r\n\r\n    const regex = /^0[0-9]{9,10}$/;\r\n    const inputValue = this.phoneReceiptSelect;\r\n    this.isValidPhone = regex.test(inputValue);\r\n  }\r\n\r\n    checkValidData() {\r\n        if ((this.shareList.length === 0 && this.shareMethod == 0) ||\r\n            (this.dataShareGroupTotal === 0 && this.shareMethod == 1)) {\r\n            return true;\r\n        }\r\n\r\n        if (this.shareMethod == 0 && this.shareList.length != 0) {\r\n            for (let item of this.shareList) {\r\n                if (!item.name) item.name = \"\";\r\n                if (!item.email) item.email = \"\";\r\n\r\n                if (!this.checkDataCondition(item) ||\r\n                    !item.data ||\r\n                    (this.trafficType === \"Gói Data\" && item.data < 100) ||\r\n                    ((this.trafficType || \"\").toUpperCase().includes(\"Gói SMS\".toUpperCase()) && item.data < 10) ||\r\n                    item.name.length >= 50 ||\r\n                    item.email.length >= 100 ||\r\n                    this.isMailInvalid(item.email)) {\r\n                    return true;\r\n                }\r\n            }\r\n            return false;\r\n        }\r\n\r\n        if (this.shareMethod == 1 &&\r\n            this.dataShareGroupTotal &&\r\n            this.dataShareGroupTotal != 0 &&\r\n            this.dataShareGroup > 0) {\r\n            return false;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n  cleanArray(arr: any[]): any[] {\r\n    return arr.filter(item => item !== null && item !== undefined && item !== \"\");\r\n  }\r\n\r\n  deleteItem(i){\r\n    this.messageCommonService.confirm(this.tranService.translate(\"datapool.button.delete\"),\r\n    this.tranService.translate(\"datapool.message.confirmDelete\"),{\r\n      ok: ()=>{\r\n        const a = this.shareList[i].data\r\n        if(a){\r\n          this.shareList[i].data = null\r\n          this.shareList[i].percent = null\r\n        }\r\n        this.shareList = this.shareList.filter((item,index) => index != i);\r\n        this.caculateRemain();\r\n      }\r\n    })\r\n    // const a = this.shareList[i].data\r\n    // if(a){\r\n    //   this.shareList[i].data = null\r\n    //   this.shareList[i].percent = null\r\n    // }\r\n    // this.shareList = this.shareList.filter((item,index) => index != i);\r\n    // this.caculateRemain();\r\n  }\r\n\r\n  changeData(event, i) {\r\n      const shareValue = event.target.value;\r\n      this.shareList[i].data = shareValue;\r\n      this.shareList[i].percent = Math.round((shareValue / this.originRemain) * 100 * 100) / 100;\r\n      this.caculateRemain();\r\n  }\r\n\r\n  onKeyDown(event, i){\r\n    const key = event.key;\r\n    const isNumber = /^[0-9]$/.test(key);\r\n\r\n    if (key === \"-\"){\r\n        return false;\r\n    }\r\n\r\n    let total=0;\r\n    this.shareList.forEach((item,index) => {\r\n      let value\r\n      if(!item.data||item.data == null|| item.data == undefined || i == index){\r\n        value = 0\r\n      }else{\r\n        value = item.data\r\n      }\r\n      total = Number(total) + Number(value);\r\n    });\r\n    const shareValue = event.target.value;\r\n    const nextValue = parseInt(shareValue + key, 10);\r\n    let totalUsedValue = nextValue+total\r\n    if(this.originRemain - totalUsedValue < 0){\r\n      this.messageCommonService.error(this.tranService.translate(\"datapool.message.exceededData\"))\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n\r\n  changeDataName(event, i){\r\n    const shareValue = event.target.value\r\n    this.shareList[i].name = shareValue\r\n  }\r\n\r\n  changeDataMail(event, i){\r\n    const shareValue = event.target.value\r\n    this.shareList[i].email = shareValue\r\n  }\r\n\r\n  shareData(){\r\n    let remainInit = this.dummyWallet?.find(wallet => wallet.subCode === this.shareDataForm.get('walletValue').value)?.totalRemainingTraffic;\r\n    let totalDataLocked = 0;\r\n    let remainAfterLocked = remainInit - totalDataLocked;\r\n    let totalDataGroup = this.dataSet.total;\r\n\r\n    if (this.shareMethod == 1) {\r\n        if (this.trafficType === 'Gói Data') {\r\n            let trafficShare = Math.floor(remainAfterLocked / totalDataGroup / 100) * 100;\r\n            if ((remainInit - trafficShare * totalDataGroup) < 0) {\r\n                this.messageCommonService.error(\"Vượt quá dung lương chia sẻ\");\r\n            } else {\r\n                this.dataShareGroup = Math.floor(remainAfterLocked / totalDataGroup / 100) * 100;\r\n            }\r\n        }\r\n        if ((this.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) {\r\n            this.dataShareGroup = Math.floor(remainAfterLocked / totalDataGroup / 5) * 5;\r\n        }\r\n        this.dataShareGroupTotal = remainAfterLocked - (Number(this.dataShareGroup) * Number(totalDataGroup));\r\n        this.percentShareGroup = Math.round((this.dataShareGroup / remainInit) * 100 * 100) / 100;\r\n    } else {\r\n        let lengthRemain = this.shareList.length\r\n        this.shareList.filter(element => element.locked === true).forEach(element => {\r\n            totalDataLocked = Number(totalDataLocked) + Number(element.data);\r\n            lengthRemain = lengthRemain - 1;\r\n        });\r\n        let remainAfterLocked = remainInit - totalDataLocked\r\n        let intPart = Math.floor(remainAfterLocked/lengthRemain);\r\n        let remainderPart = remainAfterLocked % lengthRemain\r\n        this.shareList.filter(element => element.locked === false || !element.locked).forEach(element => {\r\n            element.data = intPart\r\n            element.percent = Math.round((intPart / remainInit) * 100 * 100) / 100;\r\n        });\r\n        this.remainDataTotal = remainderPart\r\n    }\r\n  };\r\n\r\n  caculateRemain() {\r\n    let totalQuantity = 0;\r\n    if (this.shareMethod == 1) {\r\n        this.dataShareGroupTotal = this.originRemain - (Number(totalQuantity) + Number(this.dataShareGroup));\r\n    } else {\r\n        this.shareList.forEach(item => {\r\n            let value\r\n            if(!item.data||item.data == null|| item.data == undefined){\r\n                value = 0\r\n            }else{\r\n                value = item.data\r\n            }\r\n            totalQuantity = Number(totalQuantity) + Number(value);\r\n        });\r\n        this.remainDataTotal = this.originRemain - totalQuantity\r\n    }\r\n  }\r\n\r\n  defineData() {\r\n      this.isShowDefined = true\r\n  };\r\n\r\n  definedDataChange() {\r\n      if (this.shareMethod == 1) {\r\n          this.dataShareGroup = this.shareDataForm.controls['definedData'].value;\r\n          this.percentShareGroup = Math.round((this.shareDataForm.controls['definedData'].value / this.originRemain) * 100 * 100) / 100;\r\n      } else {\r\n          this.shareList.filter(e => e.locked == false || !e.locked).forEach(element => {\r\n              element.data = this.shareDataForm.controls['definedData'].value;\r\n              element.percent = Math.round((this.shareDataForm.controls['definedData'].value / this.originRemain) * 100 * 100) / 100;\r\n          });\r\n      }\r\n      this.caculateRemain();\r\n      this.isShowDefined = !this.isShowDefined\r\n  }\r\n\r\n  resetData(){\r\n    this.messageCommonService.confirm(this.tranService.translate(\"datapool.button.revokeSharing\"),\r\n    this.tranService.translate(\"datapool.message.confirmRevoke\"),{\r\n      ok:() => {\r\n        this.remainDataTotal = this.originRemain\r\n          if (this.shareMethod == 0) {\r\n              this.shareList.forEach(item => {\r\n                  item.data = null;\r\n                  item.percent = null;\r\n              });\r\n          } else {\r\n              this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n              this.dataShareGroup = 0;\r\n              this.percentShareGroup = 0;\r\n          }\r\n      }\r\n    })\r\n  };\r\n\r\n  startCountdown() {\r\n      this.interval = setInterval(() => {\r\n          if (this.countdown > 0) {\r\n              this.countdown--;\r\n          } else {\r\n              clearInterval(this.interval);\r\n          }\r\n      }, 1000);\r\n  }\r\n\r\n  resetTimer() {\r\n      this.countdown = 60;\r\n      clearInterval(this.interval);\r\n      this.startCountdown();\r\n      this.sendOTP();\r\n    }\r\n\r\n  onHideDefine(){\r\n    this.shareDataForm.controls['definedData'].reset()\r\n  }\r\n\r\n  handleShareData(){\r\n      if (this.isNonUseOTP){\r\n          this.submitForm();\r\n      }else{\r\n          this.sendOTP()\r\n      }\r\n  }\r\n\r\n    // send OTP\r\n    sendOTP() {\r\n        let me = this;\r\n        let body = {\r\n            phoneNumber: this.selectedWallet?.phoneActive?.replace(/^0/, '84')\r\n        }\r\n        me.messageCommonService.onload();\r\n        this.shareService.sendOTP(body, (res) => {\r\n          if (res.errorCode === 'BSS-00000000') {\r\n              me.openSubmit();\r\n          }\r\n        }, null, () => {\r\n            me.messageCommonService.offload()\r\n        })\r\n  }\r\n\r\n  checkValidDefine(event){\r\n    let lengthRemain = null;\r\n    if (this.shareMethod == 0) {\r\n        lengthRemain = this.shareList.length;\r\n        this.shareList.filter(element => element.locked === true).forEach(element => {\r\n            lengthRemain = lengthRemain - 1;\r\n        });\r\n    } else {\r\n        lengthRemain = this.dataSet.total;\r\n    }\r\n\r\n\r\n    if((this.shareDataForm.controls['definedData'].value * lengthRemain)>this.originRemain){\r\n      this.isDefineValueError = true\r\n      return\r\n    }else{\r\n      this.isDefineValueError = false\r\n    }\r\n    if (this.trafficType === 'Gói Data') {\r\n      if (this.shareDataForm.controls['definedData'].value % 100 !== 0) {\r\n        this.isValidDataFixed = true;\r\n      } else {\r\n        this.isValidDataFixed = false;\r\n      }\r\n    }\r\n      if((this.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())){\r\n      if (this.shareDataForm.controls['definedData'].value % 5 !== 0) {\r\n        this.isValidDataFixed = true;\r\n      } else {\r\n        this.isValidDataFixed = false;\r\n      }\r\n    }\r\n  }\r\n\r\n    onHideImport(){\r\n      this.isShowDialogImportByFile = false\r\n    }\r\n\r\n    importByFile() {\r\n        let me = this;\r\n        me.isShowDialogImportByFile = true;\r\n        me.observableService.next(CONSTANTS.OBSERVABLE.KEY_INPUT_FILE_VNPT, {});\r\n        me.isShowErrorUpload = false;\r\n    };\r\n\r\n    checkDataCondition(item: any): boolean {\r\n        if (this.trafficType === 'Gói Data') {\r\n            return item.data % 100 === 0;\r\n        }\r\n        if ((this.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) {\r\n            return item.data % 5 === 0;\r\n        }\r\n        return true;\r\n    }\r\n\r\n    clearFileCallback() {\r\n        this.isShowErrorUpload = false;\r\n    }\r\n\r\n    ngOnDestroy() {\r\n        clearInterval(this.interval);\r\n    }\r\n\r\n    downloadTemplate() {\r\n        this.shareService.downloadTemplateReceiveInfo();\r\n    }\r\n\r\n    onSelectGroup(group) {\r\n        let me = this;\r\n        me.idGroupSelected = group.id;\r\n        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\r\n        me.dataShareGroupTotal = me.dummyWallet?.find(wallet => wallet.subCode === me.shareDataForm.get('walletValue').value)?.totalRemainingTraffic;\r\n        me.dataShareGroup = 0;\r\n        me.percentShareGroup = 0;\r\n    }\r\n\r\n    search(page, limit, sort, params){\r\n        this.pageNumber = page;\r\n        this.pageSize = limit;\r\n        this.sort = sort;\r\n        let me = this;\r\n        let totalQuantity = 0;\r\n        let dataParams = {\r\n            ...params,\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        me.messageCommonService.onload();\r\n        this.groupSubService.searchInGroup(Number(me.idGroupSelected), dataParams, (response)=>{\r\n            me.dataSet = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n            // me.dataShareGroupTotal = response.totalElements;\r\n            totalQuantity = Number(totalQuantity) + Number(me.dataShareGroup * response.totalElements);\r\n            this.remainDataTotal = this.originRemain - totalQuantity;\r\n            this.dataShareGroupTotal = this.originRemain - totalQuantity;\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n\r\n    clearSelectedGroup(){\r\n        this.groupSelectedValue = []\r\n    }\r\n\r\n    clearSelectedData(){\r\n        this.selectedTableData = []\r\n    }\r\n\r\n    onClickSelection(){\r\n        if(!this.isAutoWallet){\r\n            this.messageCommonService.warning(this.tranService.translate(\"datapool.text.hasntAuto\"));\r\n        }\r\n    }\r\n\r\n    onHideError(){\r\n        window.location.reload();\r\n    }\r\n\r\n    downloadErrorFile() {\r\n        this.exportToExcel(this.FulldataSetError.content);\r\n    }\r\n\r\n    exportToExcel(data) {\r\n        // Chuẩn bị dữ liệu và tiêu đề cột\r\n        const header = ['STT', 'SĐT', 'Mô tả'];\r\n        const excelData = data.map((item, index) => [index+1, item.phoneReceipt, item.description]);\r\n\r\n        // Tạo sheet và thêm tiêu đề\r\n        const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet([header, ...excelData]);\r\n\r\n        ws['!cols'] = [{wch: 5}, {wch :21}, {wch:30}]\r\n\r\n        // Bôi đậm tiêu đề\r\n        const headerCells = ['A1', 'B1', 'C1']; // Các ô tiêu đề trong sheet (tương ứng với cột A, B, C)\r\n        headerCells.forEach(cell => {\r\n            if (ws[cell]) {\r\n                ws[cell].s = {\r\n                    font: {\r\n                        bold: true, // Bôi đậm chữ\r\n                    },\r\n                    alignment: {\r\n                        horizontal: 'center', // Căn giữa theo chiều ngang\r\n                        vertical: 'center', // Căn giữa theo chiều dọc\r\n                    },\r\n                };\r\n            }\r\n        });\r\n\r\n        // Căn giữa cho các ô dữ liệu\r\n        const rowCount = data.length;\r\n        for (let row = 2; row <= rowCount + 1; row++) {\r\n            for (let col = 0; col < header.length; col++) {\r\n                const cellRef = XLSX.utils.encode_cell({ r: row - 1, c: col });\r\n                if (ws[cellRef]) {\r\n                    ws[cellRef].s = {\r\n                        alignment: {\r\n                            horizontal: 'center', // Căn giữa theo chiều ngang\r\n                            vertical: 'center', // Căn giữa theo chiều dọc\r\n                        },\r\n                    };\r\n                }\r\n            }\r\n        }\r\n\r\n        // Tạo workbook và xuất file\r\n        const wb: XLSX.WorkBook = {\r\n            Sheets: { 'Danh sách SĐT chia sẻ lỗi': ws },\r\n            SheetNames: ['Danh sách SĐT chia sẻ lỗi'],\r\n        };\r\n\r\n        const excelBuffer: any = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });\r\n        this.saveAsExcelFile(excelBuffer, 'Danh_sach_loi_');\r\n    }\r\n\r\n    private saveAsExcelFile(buffer: any, fileName: string): void {\r\n        const data: Blob = new Blob([buffer], { type: EXCEL_TYPE });\r\n        FileSaver.saveAs(data, fileName + '_' + formatDateToDDMMYYYYHHMMSS(new Date().getTime()) + EXCEL_EXTENSION);\r\n    }\r\n\r\n    pagingDataError(pageNumber, pageSize){\r\n        const startIndex = pageNumber * pageSize;\r\n        const endIndex = startIndex + pageSize;\r\n        this.dataSetError.content = this.FulldataSetError.content.slice(startIndex, endIndex);\r\n        console.log(this.dataSetError)\r\n        this.dataSetError = {...this.dataSetError}\r\n    }\r\n\r\n    pageChange(event) {\r\n        this.normalShareTable.first = event.first;\r\n        this.normalShareTable.rows = event.rows;\r\n        this.shareDataForm.controls['pageCurrent'].setValue(event.first/event.rows + 1);\r\n    }\r\n\r\n    jumpPage() {\r\n        let pageCurrent = this.shareDataForm.controls.pageCurrent.value;\r\n        const totalPages = Math.ceil(this.shareList.length / this.normalShareTable.rows);\r\n        if (pageCurrent < 1 || pageCurrent > totalPages) {\r\n            return;\r\n        }\r\n        this.normalShareTable.first = (pageCurrent - 1) * this.normalShareTable.rows;\r\n    }\r\n\r\n    getMaxPage(){\r\n        if(this.shareList.length % this.normalShareTable.rows == 0){\r\n            return this.shareList.length/this.normalShareTable.rows;\r\n        }else{\r\n            return Math.ceil(this.shareList.length/this.normalShareTable.rows);\r\n        }\r\n    }\r\n\r\n    formatNumber(value: number): string {\r\n        return new Intl.NumberFormat('vi-VN').format(value);\r\n    }\r\n}\r\n\r\nconst EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';\r\nconst EXCEL_EXTENSION = '.xlsx';\r\n\r\nfunction formatDateToDDMMYYYYHHMMSS(timestamp: number): string {\r\n    const date = new Date(timestamp);\r\n\r\n    const dd = String(date.getDate()).padStart(2, '0');\r\n    const MM = String(date.getMonth() + 1).padStart(2, '0'); // Tháng bắt đầu từ 0\r\n    const yyyy = date.getFullYear();\r\n    const HH = String(date.getHours()).padStart(2, '0');\r\n    const mm = String(date.getMinutes()).padStart(2, '0');\r\n    const ss = String(date.getSeconds()).padStart(2, '0');\r\n\r\n    return `${dd}${MM}${yyyy}${HH}${mm}${ss}`;\r\n}\r\n", "<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{this.tranService.translate(\"datapool.label.shareData\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n</div>\r\n\r\n<style>\r\n    input[type=number]::-webkit-outer-spin-button,\r\n    input[type=number]::-webkit-inner-spin-button {\r\n        /* Ẩn nút */\r\n        display: none;\r\n    }\r\n</style>\r\n\r\n<upload-file-vnpt\r\n    [shareList]=\"shareList\"\r\n    [remainDataTotal]=\"remainDataTotal\"\r\n    [isShowDialogImportByFile]=\"isShowDialogImportByFile\"\r\n    [isShowErrorUpload]=\"isShowErrorUpload\"\r\n    (onHideImport)=\"onHideImport()\"\r\n    [totalRemain]=\"originRemain\"\r\n    [trafficType]=\"trafficType\"\r\n></upload-file-vnpt>\r\n\r\n<form action=\"\" [formGroup]=\"shareDataForm\" (submit)=\"submitForm()\" class=\"responsive-form\">\r\n    <div class=\"mt-3\">\r\n        <p-card>\r\n            <div class=\"font-bold text-xl\">{{tranService.translate(\"datapool.label.generalInfomation\")}}</div>\r\n            <div class=\"flex flex-row gap-4 mt-3 px-2 mb-0\">\r\n                <div class=\"flex flex-column gap-2 flex-1\">\r\n                    <label htmlFor=\"walletName\">{{tranService.translate(\"datapool.label.dataWallet\")}}</label>\r\n                    <p-dropdown [options]=\"dummyWallet\"\r\n                                formControlName=\"walletValue\" optionLabel=\"subCode\"\r\n                                optionValue=\"subCode\"\r\n                                [placeholder]=\"tranService.translate('datapool.label.selectWallet')\"\r\n                                id=\"walletName\"\r\n                                (onChange)=\"changeWallet()\"\r\n                                [emptyFilterMessage]=\"tranService.translate('global.text.nodata')\"\r\n                                filter=\"true\">\r\n                    </p-dropdown>\r\n                </div>\r\n                <div class=\"flex flex-column gap-2 flex-1\">\r\n                    <label htmlFor=\"datePicker\">{{tranService.translate(\"datapool.label.shareDate\")}}</label>\r\n                    <p-calendar\r\n                        styleClass=\"w-full\"\r\n                        id=\"datePicker\"\r\n                        formControlName=\"dateShare\"\r\n                        [showTime]=\"true\" [showSeconds]=\"true\"\r\n                        dateFormat=\"dd/mm/yy\"\r\n                    ></p-calendar>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex flex-row gap-4 px-2 py-0 m-0\">\r\n                <div *ngIf=\"shareDataForm.get('walletValue').invalid && shareDataForm.get('walletValue').dirty\">\r\n                    <div *ngIf=\"shareDataForm.get('walletValue').errors.required\" class=\"text-red-500\" >{{tranService.translate(\"global.message.required\")}}</div>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex flex-row gap-4 mt-3 px-2 mb-0\">\r\n                <div class=\"flex flex-column gap-2 flex-1\">\r\n                    <label htmlFor=\"description\">{{tranService.translate(\"datapool.label.description\")}}</label>\r\n                    <textarea\r\n                        class=\"w-full\" style=\"resize: none;\"\r\n                        rows=\"5\"\r\n                        [autoResize]=\"false\"\r\n                        pInputTextarea id=\"description\"\r\n                        formControlName=\"description\"\r\n                        [placeholder]=\"tranService.translate('sim.text.inputDescription')\"\r\n                    ></textarea>\r\n                </div>\r\n\r\n                <div class=\"flex flex-column gap-2 flex-1 field\">\r\n                    <p-radioButton\r\n                        [label]=\"tranService.translate('datapool.label.shareNormal')\"\r\n                        name=\"selectedShareMethod\" value=\"0\"\r\n                        formControlName=\"selectedShareMethod\"\r\n                        class=\"p-3\"\r\n                        [(ngModel)]=\"shareMethod\"\r\n                        inputId=\"1\"\r\n                        (onClick)=\"clearSelectedGroup()\">\r\n                    </p-radioButton>\r\n                    <p-radioButton\r\n                        [label]=\"tranService.translate('datapool.label.shareByGroup')\"\r\n                        name=\"selectedShareMethod\"\r\n                        value=\"1\"\r\n                        inputId=\"2\"\r\n                        class=\"p-3\"\r\n                        [(ngModel)]=\"shareMethod\"\r\n                        formControlName=\"selectedShareMethod\"\r\n                        (onClick)=\"clearSelectedData()\">\r\n                    </p-radioButton>\r\n                </div>\r\n            </div>\r\n            <div class=\"w-full field grid px-2 m-0 py-0 mb-3\">\r\n                <div *ngIf=\"shareDataForm.get('description').invalid && shareDataForm.get('description').dirty\">\r\n                    <div *ngIf=\"shareDataForm.get('description').errors.maxlength\" class=\"text-red-500\" >{{tranService.translate(\"global.message.maxLength\",{len:255})}}</div>\r\n                </div>\r\n            </div>\r\n        </p-card>\r\n    </div>\r\n\r\n    <div class=\"mt-3 table-vnpt\" *ngIf=\"showShareList\">\r\n        <p-card>\r\n            <div class=\"font-bold text-xl\">{{walletName}}</div>\r\n            <div class=\"mt-5 flex flex-row gap-3 justify-content-between\">\r\n                <div class=\"flex flex-row gap-3 col-7\">\r\n                    <div class=\"col-5\" style=\"max-width: calc(100% - 1px) !important;\">\r\n                        <vnpt-select\r\n                            [control]=\"phoneReceiptSelectControl\"\r\n                            *ngIf=\"shareMethod == 0\"\r\n                            [(value)]=\"phoneReceiptSelect\"\r\n                            (onchange)=\"checkValidAdd()\"\r\n                            (onSelectItem)=\"addPhone(phoneReceiptSelect, true)\"\r\n                            [isAutoComplete]=\"true\"\r\n                            [isMultiChoice]=\"false\"\r\n                            paramKey=\"phoneReceipt\"\r\n                            keyReturn=\"phoneReceipt\"\r\n                            [lazyLoad]=\"true\"\r\n                            [placeholder]=\"tranService.translate('datapool.label.receiverPhone')\"\r\n                            displayPattern=\"${phoneReceipt}\"\r\n                            [loadData]=\"getListShareInfoCbb.bind(this)\"\r\n                        ></vnpt-select>\r\n\r\n                        <vnpt-select\r\n                            *ngIf=\"shareMethod == 1\"\r\n                            [(value)]=\"groupSelected\"\r\n                            objectKey=\"groupSubWallet\"\r\n                            (onSelectItem)=\"onSelectGroup(groupSelected)\"\r\n                            paramKey=\"groupName\"\r\n                            keyReturn=\"id\"\r\n                            styleClass=\"w-full\"\r\n                            [placeholder]=\"tranService.translate('datapool.label.shareGroup')\"\r\n                            displayPattern=\"${groupName}\"\r\n                            typeValue=\"object\"\r\n                            [isMultiChoice]=\"false\"\r\n                            (onchange)=\"clearSelectedGroup()\"\r\n                        ></vnpt-select>\r\n                    </div>\r\n                    <button *ngIf=\"shareMethod == 0\" [disabled]=\"isClickCreate || !isValidPhone\" type=\"button\" pButton [label]=\"tranService.translate('datapool.button.add')\" (click)=\"addPhone(phoneReceiptSelect)\"></button>\r\n                </div>\r\n                <div class=\"flex flex-wrap justify-content-end gap-4\">\r\n                    <!--                    <p-button icon=\"pi pi-users\" [label]=\"tranService.translate('datapool.button.shareByGroup')\" (click)=\"shareByGroup()\" styleClass=\"p-button-info\"></p-button>-->\r\n                    <p-button *ngIf=\"shareMethod == 0\" icon=\"pi pi-file-excel\" [label]=\"tranService.translate('datapool.button.importFile')\" (click)=\"importByFile()\" styleClass=\"p-button-success\"></p-button>\r\n                </div>\r\n            </div>\r\n            <div class=\"mb-5 flex flex-row gap-3 justify-content-between text-red-500 px-1\">\r\n                <div *ngIf=\"!isValidPhone && shareMethod == 0\">\r\n                    {{tranService.translate(\"datapool.message.digitError\")}}\r\n                </div>\r\n            </div>\r\n            <div class=\"flex flex-row justify-content-between mb-2\">\r\n                <div class=\"font-semibold text-lg\">{{tranService.translate('datapool.label.remainData')}}: {{shareMethod == 1 ? formatNumber(dataShareGroupTotal) : formatNumber(remainDataTotal)}}/{{formatNumber(totalData)}}</div>\r\n                <div class=\"flex flex-row gap-2\">\r\n                    <button [disabled]=\"shareList.length <= 0 && dataSet.total <= 0\" pButton class=\"p-button-outlined\" type=\"button\" (click)=\"shareData()\">{{tranService.translate(\"datapool.button.equalSharing\")}}</button>\r\n                    <button [disabled]=\"shareList.length <= 0 && dataSet.total <= 0\" pButton class=\"p-button-outlined\" type=\"button\" (click)=\"defineData()\">{{tranService.translate(\"datapool.button.fixedAllocation\")}}</button>\r\n                    <button [disabled]=\"shareList.length <= 0 && dataSet.total <= 0\" pButton class=\"p-button-outlined\" type=\"button\" (click)=\"resetData()\">{{tranService.translate(\"datapool.button.revokeSharing\")}}</button>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex flex-row gap-3 mb-2\" *ngIf=\"selectedTableData.length > 0\">\r\n                <div class=\"align-self-auto my-auto\">Đã chọn {{selectedTableData.length}} giá trị</div>\r\n                <button pButton (click)=\"selectedTableData = []\" pRipple type=\"button\" icon=\"pi pi-times\"></button>\r\n            </div>\r\n            <p-table #normalShare class=\"normalShareTable\"\r\n                [value]=\"shareList\" [tableStyle]=\"{ 'min-width': '50rem','padding':'21px' }\"\r\n                [(selection)]=\"selectedTableData\" *ngIf=\"shareMethod == 0\"\r\n                [totalRecords]=\"shareList.length\"\r\n                dataKey=\"phoneReceipt\"\r\n                [paginator]=\"shareList.length > 0\"\r\n                [rows]=\"10\"\r\n                (onPage)=\"pageChange($event)\"\r\n                [showCurrentPageReport]=\"true\"\r\n                [currentPageReportTemplate]=\"tranService.translate('global.text.templateTextPagination')\"\r\n                [rowsPerPageOptions]=\"[5,10,20, 25, 50]\"\r\n            >\r\n                <ng-template pTemplate=\"header\">\r\n                    <tr>\r\n                        <th style=\"width: 112px;!important;\">\r\n                            <div class=\"flex flex-row gap-3 ml-2 align-items-center\">\r\n                                <p-tableHeaderCheckbox [disabled]=\"!isAutoWallet\" (click)=\"onClickSelection()\"></p-tableHeaderCheckbox>\r\n                                <div class=\"align-self-auto\">{{tranService.translate('datapool.label.autoSharing')}}</div>\r\n                            </div>\r\n                        </th>\r\n                        <th >{{tranService.translate(\"global.text.stt\")}}</th>\r\n                        <th>{{tranService.translate(\"datapool.label.phone\")}}</th>\r\n                        <th>{{tranService.translate('datapool.label.fullName')}}</th>\r\n                        <th>{{tranService.translate('datapool.label.email')}}</th>\r\n                        <th *ngIf=\"trafficType == 'Gói Data'\">{{tranService.translate('datapool.label.sharingData', {type: 'MB'})}}</th>\r\n                        <th *ngIf=\"trafficType == 'Gói thoại'\">{{tranService.translate('datapool.label.sharingData', {type: 'Phút'})}}</th>\r\n                        <th *ngIf=\"(trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())\">{{tranService.translate('datapool.label.sharingData', {type: 'SMS'})}}</th>\r\n                        <th *ngIf=\"trafficType != 'Gói Data' && trafficType != 'Gói thoại' && !(trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())\">{{tranService.translate('datapool.label.sharingData', {type: ''})}}</th>\r\n                        <th>{{tranService.translate('datapool.label.percentage')}}</th>\r\n                        <th></th>\r\n                    </tr>\r\n                    <tr *ngIf=\"!shareList || shareList.length === 0\">\r\n                        <td [attr.colspan]=\"7\" class=\"box-table-nodata\">\r\n                            <span class=\"pi pi-inbox\" style=\"font-size: x-large;\">&nbsp;</span>{{tranService.translate(\"global.text.nodata\")}}\r\n                        </td>\r\n                    </tr>\r\n                </ng-template>\r\n                <ng-template pTemplate=\"body\" let-list let-index=\"rowIndex\">\r\n                    <tr>\r\n                        <td style=\"max-width:150px\">\r\n                            <p-tableCheckbox [value]=\"list\" [disabled]=\"!isAutoWallet\" (click)=\"onClickSelection()\"></p-tableCheckbox>\r\n                        </td>\r\n                        <td>{{index+1}}</td>\r\n                        <td>{{ list.phoneReceipt }}</td>\r\n                        <td>\r\n                            <input type=\"text\" (input)=\"changeDataName($event, index)\" pInputText [value]=\"list.name\">\r\n                            <div class=\"text-red-500\" *ngIf=\"list.name?.length >=50\">\r\n                                {{tranService.translate(\"global.message.maxLength\",{len:50})}}\r\n                            </div>\r\n                            <div *ngIf=\"!utilService.checkValidCharacterVietnamese(list.name)\" class=\"text-red-500\">\r\n                                {{tranService.translate(\"global.message.wrongFormatName\",{len:150})}}\r\n                            </div>\r\n                        </td>\r\n                        <td>\r\n                            <input type=\"text\" (input)=\"changeDataMail($event, index)\" pInputText [value]=\"list.email\">\r\n                            <div class=\"text-red-500\" *ngIf=\"list.email?.length >=100\">\r\n                                {{tranService.translate(\"global.message.maxLength\",{len:100})}}\r\n                            </div>\r\n                            <div class=\"text-red-500\" *ngIf=\"isMailInvalid(list.email)\">\r\n                                {{tranService.translate(\"global.message.formatEmail\")}}\r\n                            </div>\r\n                        </td>\r\n                        <td>\r\n                            <div class=\"flex flex-row align-items-center\">\r\n                                <input [ngClass]=\"{'surface-200':list.locked}\" (input)=\"changeData($event, index)\" (keydown)=\"onKeyDown($event, index)\" [disabled]=\"list.locked\" class=\"border-noround-right\" pInputText type=\"number\" [value]=\"list.data\">\r\n                                <div pInputText class=\"border-round-right border-noround-left  cursor-pointer surface-300\" styleClass=\"cursor-pointer\" (click)=\"list.locked=!list.locked\">\r\n                                    <i *ngIf=\"list.locked\" class=\"pi pi-lock\"></i>\r\n                                    <i *ngIf=\"!list.locked\" class=\"pi pi-lock-open\"></i>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"text-red-500\" *ngIf=\"(trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase()) && (list.data < 10 || !checkDataCondition(list))\">\r\n                                {{tranService.translate(\"datapool.message.smsError\")}}\r\n                            </div>\r\n                            <div class=\"text-red-500\" *ngIf=\"trafficType == 'Gói Data' && (list.data < 100 || !checkDataCondition(list))\">\r\n                                {{tranService.translate(\"datapool.message.dataError\")}}\r\n                            </div>\r\n                        </td>\r\n                        <td><div class=\"flex flex-row \">{{ list.percent }} <div *ngIf=\"list.percent||list.percent==0\">%</div> </div></td>\r\n                        <td><button type=\"button\" pButton class=\"p-button-outlined\" (click)=\"deleteItem(index)\"><i class=\"pi pi-trash\"></i></button></td>\r\n                    </tr>\r\n                </ng-template>\r\n                <ng-template pTemplate=\"paginatorright\">\r\n                    <div class=\"flex flex-row justify-content-start align-items-center grap-2 ml-3\">\r\n                        <div class=\"mr-2\">{{tranService.translate('global.text.page')}}</div>\r\n                        <p-inputNumber (keyup.enter)=\"jumpPage()\" formControlName=\"pageCurrent\" mode=\"decimal\" [min]=\"1\" [max]=\"getMaxPage()\"> </p-inputNumber>\r\n                    </div>\r\n                </ng-template>\r\n            </p-table>\r\n\r\n            <table-vnpt\r\n                *ngIf=\"shareMethod == 1\"\r\n                [tableId]=\"'tbSubShareByGroup'\"\r\n                [fieldId]=\"'id'\"\r\n                [columns]=\"columns\"\r\n                [dataSet]=\"dataSet\"\r\n                [options]=\"optionTable\"\r\n                [loadData]=\"search.bind(this)\"\r\n                [pageNumber]=\"pageNumber\"\r\n                [pageSize]=\"pageSize\"\r\n                [sort]=\"sort\"\r\n                [params]=\"searchInfo\"\r\n                [labelTable]=\"\"\r\n                [(selectItems)]=\"groupSelectedValue\"\r\n                (onChangeSelectAllItems)=\"onClickSelection()\"\r\n                (onChangeSelectItem)=\"onClickSelection()\"\r\n                [tableSelectionText]=\"tranService.translate('datapool.label.autoSharing')\"\r\n                selectionWidth=\"8\"\r\n            ></table-vnpt>\r\n\r\n\r\n\r\n            <div class=\"flex flex-row justify-content-center gap-3 p-2 mt-5\">\r\n                <a routerLink=\"/data-pool/shareMgmt/listShare\"><button pButton [label]=\"tranService.translate('global.button.cancel')\" class=\"p-button-secondary p-button-outlined\" type=\"button\"></button></a>\r\n                <button pButton [disabled]=\"!(shareDataForm.controls.walletValue.valid && shareDataForm.controls.description.valid) || checkValidData()\" [label]=\"tranService.translate('global.button.save')\" class=\"p-button-info\" type=\"button\" (click)=\"handleShareData()\"></button>\r\n            </div>\r\n        </p-card>\r\n    </div>\r\n\r\n    <p-dialog (onHide)=\"onHideDefine()\" [header]=\"tranService.translate('datapool.button.fixedAllocation')\" [(visible)]=\"isShowDefined\" [modal]=\"true\" [style]=\"{ width: '50vw' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n        <div class=\"flex flex-column gap-2 flex-1\">\r\n            <label htmlFor=\"\">{{tranService.translate(\"datapool.label.revokeData\", {data: dataForLabel})}}</label>\r\n            <input type=\"number\" (input)=\"checkValidDefine($event)\" formControlName=\"definedData\" pInputText>\r\n            <div class=\"text-red-500\" *ngIf=\"(trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase()) && (shareDataForm.controls['definedData'].value < 10 || isValidDataFixed) && shareDataForm.controls['definedData'].dirty\">\r\n                {{tranService.translate(\"datapool.message.smsError\")}}\r\n            </div>\r\n            <div class=\"text-red-500\" *ngIf=\"trafficType == 'Gói Data' && (shareDataForm.controls['definedData'].value < 100 || isValidDataFixed) && shareDataForm.controls['definedData'].dirty\">\r\n                {{tranService.translate(\"datapool.message.dataError\")}}\r\n            </div>\r\n            <div class=\"text-red-500\" *ngIf=\"isDefineValueError\">{{tranService.translate(\"datapool.message.exceededData\")}}</div>\r\n            <div class=\"m-auto\">\r\n                <button class=\"m-auto mr-2\" [disabled]=\"((trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase()) && shareDataForm.controls['definedData'].value < 10) || isValidDataFixed ||(trafficType == 'Gói Data' && shareDataForm.controls['definedData'].value < 100) || isDefineValueError || shareDataForm.controls['definedData'].value=='' || shareDataForm.controls['definedData'].value == null || shareDataForm.controls['definedData'].value == undefined || isValidDataFixed\" pButton type=\"button\" (click)=\"definedDataChange()\">{{tranService.translate(\"global.button.confirm\")}}</button>\r\n                <button class=\"m-auto ml-2 p-button-outlined\" type=\"button\" pButton (click)=\"isShowDefined = false\">{{tranService.translate(\"global.button.cancel\")}}</button>\r\n            </div>\r\n        </div>\r\n    </p-dialog>\r\n\r\n    <p-dialog [header]=\"tranService.translate('datapool.label.otpCode')\" [(visible)]=\"isSubmit\" [modal]=\"true\" [style]=\"{ width: '30vw' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n        <div class=\"flex flex-column gap-2 flex-1\">\r\n            <p-inputOtp formControlName=\"otp\" class=\"mx-auto my-3\" [integerOnly]=\"true\" length=\"6\"></p-inputOtp>\r\n            <button\r\n                type=\"button\"\r\n                class=\"border-none mb-4 cursor-pointer flex flex-row justify-content-center font-semibold\"\r\n                style=\"background-color: transparent;\" [disabled]=\"countdown>0\"\r\n                (click)=\"resetTimer()\"\r\n            >{{tranService.translate(\"datapool.message.resendOtp\")}}&nbsp;<div *ngIf=\"countdown>0\"> {{tranService.translate(\"datapool.message.in\")}} {{countdown}} {{tranService.translate(\"datapool.message.sec\")}} </div>\r\n            </button>\r\n            <div class=\"m-auto\">\r\n                <button [disabled]=\"shareDataForm.invalid\" class=\"m-auto mr-2\" pButton>{{tranService.translate(\"global.button.save\")}}</button>\r\n                <button class=\"m-auto ml-2 p-button-outlined\" pButton (click)=\"isSubmit = false\">{{tranService.translate(\"global.button.cancel\")}}</button>\r\n            </div>\r\n        </div>\r\n    </p-dialog>\r\n\r\n    <p-dialog class=\"dataError\" [header]=\"tranService.translate('datapool.label.listShareError')\" [(visible)]=\"isError\" [modal]=\"true\" [style]=\"{ width: '60vw' }\" [draggable]=\"false\" [resizable]=\"false\" (onHide)=\"onHideError()\">\r\n        <p-button styleClass=\"mr-2 p-button-outlined\" style=\"position: absolute;top: 30px;right: 45px;z-index: 5;font-size: 10px\"\r\n                  tooltipPosition=\"right\"\r\n                  [pTooltip]=\"tranService.translate('datapool.label.downloadErrorFile')\"\r\n                  icon=\"pi pi-download\" (onClick)=\"downloadErrorFile()\"></p-button>\r\n        <table-vnpt\r\n            [tableId]=\"'tbSubShareByGroup'\"\r\n            [fieldId]=\"'id'\"\r\n            [columns]=\"columnsError\"\r\n            [dataSet]=\"dataSetError\"\r\n            [options]=\"optionTableError\"\r\n            [pageNumber]=\"pageNumberError\"\r\n            [pageSize]=\"pageSizeError\"\r\n            [sort]=\"sortError\"\r\n            [params]=\"searchInfoError\"\r\n            [loadData]=\"pagingDataError.bind(this)\"\r\n            [labelTable]=\"\"\r\n        ></table-vnpt>\r\n    </p-dialog>\r\n</form>\r\n"], "mappings": "AACA,SAAmBA,WAAW,EAAEC,SAAS,EAAEC,UAAU,QAAO,gBAAgB;AAE5E,SAAQC,aAAa,QAAO,wBAAwB;AACpD,SAAQC,sBAAsB,QAAO,iDAAiD;AACtF,SAAQC,gBAAgB,QAAO,oEAAoE;AAEnG,SAAQC,oBAAoB,QAAO,mDAAmD;AACtF,SAAQC,cAAc,QAAO,iDAAiD;AAC9E,SAAQC,SAAS,QAA0B,qCAAqC;AAEhF,SAAQC,qBAAqB,QAAO,4DAA4D;AAEhG,OAAO,KAAKC,IAAI,MAAM,eAAe;AACrC,OAAO,KAAKC,SAAS,MAAM,YAAY;AACvC,SAAQC,uBAAuB,QAAO,mCAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICwCrDC,EAAA,CAAAC,cAAA,cAAoF;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAA1DH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAD5IR,EAAA,CAAAC,cAAA,UAAgG;IAC5FD,EAAA,CAAAS,UAAA,IAAAC,wCAAA,kBAA8I;IAClJV,EAAA,CAAAG,YAAA,EAAM;;;;IADIH,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAW,UAAA,SAAAC,MAAA,CAAAC,aAAA,CAAAC,GAAA,gBAAAC,MAAA,CAAAC,QAAA,CAAsD;;;;;;;;;;IAwC5DhB,EAAA,CAAAC,cAAA,cAAqF;IAAAD,EAAA,CAAAE,MAAA,GAA+D;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAArEH,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,iBAAA,CAAAY,MAAA,CAAAV,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAkB,eAAA,IAAAC,GAAA,GAA+D;;;;;IADxJnB,EAAA,CAAAC,cAAA,UAAgG;IAC5FD,EAAA,CAAAS,UAAA,IAAAW,wCAAA,kBAA0J;IAC9JpB,EAAA,CAAAG,YAAA,EAAM;;;;IADIH,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAW,UAAA,SAAAU,MAAA,CAAAR,aAAA,CAAAC,GAAA,gBAAAC,MAAA,CAAAO,SAAA,CAAuD;;;;;;IAYzDtB,EAAA,CAAAC,cAAA,sBAcC;IAXGD,EAAA,CAAAuB,UAAA,yBAAAC,oFAAAC,MAAA;MAAAzB,EAAA,CAAA0B,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA5B,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAAAF,OAAA,CAAAG,kBAAA,GAAAN,MAAA;IAAA,EAA8B,sBAAAO,iFAAA;MAAAhC,EAAA,CAAA0B,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAAjC,EAAA,CAAA6B,aAAA;MAAA,OAClB7B,EAAA,CAAA8B,WAAA,CAAAG,OAAA,CAAAC,aAAA,EAAe;IAAA,EADG,0BAAAC,qFAAA;MAAAnC,EAAA,CAAA0B,aAAA,CAAAC,IAAA;MAAA,MAAAS,OAAA,GAAApC,EAAA,CAAA6B,aAAA;MAAA,OAEd7B,EAAA,CAAA8B,WAAA,CAAAM,OAAA,CAAAC,QAAA,CAAAD,OAAA,CAAAL,kBAAA,EAA6B,IAAI,CAAC;IAAA,EAFpB;IAWjC/B,EAAA,CAAAG,YAAA,EAAc;;;;IAbXH,EAAA,CAAAW,UAAA,YAAA2B,MAAA,CAAAC,yBAAA,CAAqC,UAAAD,MAAA,CAAAP,kBAAA,mFAAAO,MAAA,CAAA/B,WAAA,CAAAC,SAAA,8CAAA8B,MAAA,CAAAE,mBAAA,CAAAC,IAAA,CAAAH,MAAA;;;;;;IAezCtC,EAAA,CAAAC,cAAA,sBAaC;IAXGD,EAAA,CAAAuB,UAAA,yBAAAmB,oFAAAjB,MAAA;MAAAzB,EAAA,CAAA0B,aAAA,CAAAiB,IAAA;MAAA,MAAAC,OAAA,GAAA5C,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAAAc,OAAA,CAAAC,aAAA,GAAApB,MAAA;IAAA,EAAyB,0BAAAqB,qFAAA;MAAA9C,EAAA,CAAA0B,aAAA,CAAAiB,IAAA;MAAA,MAAAI,OAAA,GAAA/C,EAAA,CAAA6B,aAAA;MAAA,OAET7B,EAAA,CAAA8B,WAAA,CAAAiB,OAAA,CAAAC,aAAA,CAAAD,OAAA,CAAAF,aAAA,CAA4B;IAAA,EAFnB,sBAAAI,iFAAA;MAAAjD,EAAA,CAAA0B,aAAA,CAAAiB,IAAA;MAAA,MAAAO,OAAA,GAAAlD,EAAA,CAAA6B,aAAA;MAAA,OAUb7B,EAAA,CAAA8B,WAAA,CAAAoB,OAAA,CAAAC,kBAAA,EAAoB;IAAA,EAVP;IAW5BnD,EAAA,CAAAG,YAAA,EAAc;;;;IAXXH,EAAA,CAAAW,UAAA,UAAAyC,OAAA,CAAAP,aAAA,CAAyB,gBAAAO,OAAA,CAAA7C,WAAA,CAAAC,SAAA;;;;;;IAajCR,EAAA,CAAAC,cAAA,iBAAiM;IAAvCD,EAAA,CAAAuB,UAAA,mBAAA8B,oEAAA;MAAArD,EAAA,CAAA0B,aAAA,CAAA4B,IAAA;MAAA,MAAAC,OAAA,GAAAvD,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAAyB,OAAA,CAAAlB,QAAA,CAAAkB,OAAA,CAAAxB,kBAAA,CAA4B;IAAA,EAAC;IAAC/B,EAAA,CAAAG,YAAA,EAAS;;;;IAAzKH,EAAA,CAAAW,UAAA,aAAA6C,OAAA,CAAAC,aAAA,KAAAD,OAAA,CAAAE,YAAA,CAA2C,UAAAF,OAAA,CAAAjD,WAAA,CAAAC,SAAA;;;;;;IAI5ER,EAAA,CAAAC,cAAA,mBAAgL;IAAvDD,EAAA,CAAAuB,UAAA,mBAAAoC,yEAAA;MAAA3D,EAAA,CAAA0B,aAAA,CAAAkC,IAAA;MAAA,MAAAC,OAAA,GAAA7D,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAA+B,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAA+B9D,EAAA,CAAAG,YAAA,EAAW;;;;IAAhIH,EAAA,CAAAW,UAAA,UAAAoD,OAAA,CAAAxD,WAAA,CAAAC,SAAA,+BAA6D;;;;;IAI5HR,EAAA,CAAAC,cAAA,UAA+C;IAC3CD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAgE,kBAAA,MAAAC,OAAA,CAAA1D,WAAA,CAAAC,SAAA,qCACJ;;;;;;IAUJR,EAAA,CAAAC,cAAA,cAA2E;IAClCD,EAAA,CAAAE,MAAA,GAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvFH,EAAA,CAAAC,cAAA,iBAA0F;IAA1ED,EAAA,CAAAuB,UAAA,mBAAA2C,kEAAA;MAAAlE,EAAA,CAAA0B,aAAA,CAAAyC,IAAA;MAAA,MAAAC,OAAA,GAAApE,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAAAsC,OAAA,CAAAC,iBAAA;IAAA,EAAgC;IAA0CrE,EAAA,CAAAG,YAAA,EAAS;;;;IAD9DH,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAgE,kBAAA,4BAAAM,OAAA,CAAAD,iBAAA,CAAAE,MAAA,uBAA4C;;;;;;;;;;IA2BzEvE,EAAA,CAAAC,cAAA,SAAsC;IAAAD,EAAA,CAAAE,MAAA,GAAqE;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA1EH,EAAA,CAAAI,SAAA,GAAqE;IAArEJ,EAAA,CAAAK,iBAAA,CAAAmE,OAAA,CAAAjE,WAAA,CAAAC,SAAA,+BAAAR,EAAA,CAAAkB,eAAA,IAAAuD,GAAA,GAAqE;;;;;;;;;;IAC3GzE,EAAA,CAAAC,cAAA,SAAuC;IAAAD,EAAA,CAAAE,MAAA,GAAuE;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA5EH,EAAA,CAAAI,SAAA,GAAuE;IAAvEJ,EAAA,CAAAK,iBAAA,CAAAqE,OAAA,CAAAnE,WAAA,CAAAC,SAAA,+BAAAR,EAAA,CAAAkB,eAAA,IAAAyD,GAAA,GAAuE;;;;;;;;;;IAC9G3E,EAAA,CAAAC,cAAA,SAAgF;IAAAD,EAAA,CAAAE,MAAA,GAAsE;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA3EH,EAAA,CAAAI,SAAA,GAAsE;IAAtEJ,EAAA,CAAAK,iBAAA,CAAAuE,OAAA,CAAArE,WAAA,CAAAC,SAAA,+BAAAR,EAAA,CAAAkB,eAAA,IAAA2D,GAAA,GAAsE;;;;;;;;;;IACtJ7E,EAAA,CAAAC,cAAA,SAA4I;IAAAD,EAAA,CAAAE,MAAA,GAAmE;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAxEH,EAAA,CAAAI,SAAA,GAAmE;IAAnEJ,EAAA,CAAAK,iBAAA,CAAAyE,OAAA,CAAAvE,WAAA,CAAAC,SAAA,+BAAAR,EAAA,CAAAkB,eAAA,IAAA6D,GAAA,GAAmE;;;;;IAInN/E,EAAA,CAAAC,cAAA,SAAiD;IAEaD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAAAH,EAAA,CAAAE,MAAA,GACvE;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAFDH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAgF,WAAA,cAAkB;IACiDhF,EAAA,CAAAI,SAAA,GACvE;IADuEJ,EAAA,CAAAgE,kBAAA,KAAAiB,OAAA,CAAA1E,WAAA,CAAAC,SAAA,4BACvE;;;;;;IArBJR,EAAA,CAAAC,cAAA,SAAI;IAG0DD,EAAA,CAAAuB,UAAA,mBAAA2D,mGAAA;MAAAlF,EAAA,CAAA0B,aAAA,CAAAyD,IAAA;MAAA,MAAAC,OAAA,GAAApF,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAAsD,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAACrF,EAAA,CAAAG,YAAA,EAAwB;IACvGH,EAAA,CAAAC,cAAA,cAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAuD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGlGH,EAAA,CAAAC,cAAA,SAAK;IAAAD,EAAA,CAAAE,MAAA,GAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1DH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7DH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1DH,EAAA,CAAAS,UAAA,KAAA6E,iEAAA,iBAAgH;IAChHtF,EAAA,CAAAS,UAAA,KAAA8E,iEAAA,iBAAmH;IACnHvF,EAAA,CAAAS,UAAA,KAAA+E,iEAAA,iBAA2J;IAC3JxF,EAAA,CAAAS,UAAA,KAAAgF,iEAAA,iBAAoN;IACpNzF,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/DH,EAAA,CAAA0F,SAAA,UAAS;IACb1F,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAS,UAAA,KAAAkF,iEAAA,iBAIK;;;;IAnB8B3F,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAW,UAAA,cAAAiF,OAAA,CAAAC,YAAA,CAA0B;IACpB7F,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAK,iBAAA,CAAAuF,OAAA,CAAArF,WAAA,CAAAC,SAAA,+BAAuD;IAGvFR,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAK,iBAAA,CAAAuF,OAAA,CAAArF,WAAA,CAAAC,SAAA,oBAA4C;IAC7CR,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAK,iBAAA,CAAAuF,OAAA,CAAArF,WAAA,CAAAC,SAAA,yBAAiD;IACjDR,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAuF,OAAA,CAAArF,WAAA,CAAAC,SAAA,4BAAoD;IACpDR,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAK,iBAAA,CAAAuF,OAAA,CAAArF,WAAA,CAAAC,SAAA,yBAAiD;IAChDR,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAW,UAAA,SAAAiF,OAAA,CAAAE,WAAA,oBAA+B;IAC/B9F,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAW,UAAA,SAAAiF,OAAA,CAAAE,WAAA,0BAAgC;IAChC9F,EAAA,CAAAI,SAAA,GAAyE;IAAzEJ,EAAA,CAAAW,UAAA,UAAAiF,OAAA,CAAAE,WAAA,QAAAC,WAAA,GAAAC,QAAA,gBAAAD,WAAA,IAAyE;IACzE/F,EAAA,CAAAI,SAAA,GAAqI;IAArIJ,EAAA,CAAAW,UAAA,SAAAiF,OAAA,CAAAE,WAAA,uBAAAF,OAAA,CAAAE,WAAA,+BAAAF,OAAA,CAAAE,WAAA,QAAAC,WAAA,GAAAC,QAAA,gBAAAD,WAAA,IAAqI;IACtI/F,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAAuF,OAAA,CAAArF,WAAA,CAAAC,SAAA,8BAAsD;IAGzDR,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAW,UAAA,UAAAiF,OAAA,CAAAK,SAAA,IAAAL,OAAA,CAAAK,SAAA,CAAA1B,MAAA,OAA0C;;;;;;;;;;IAevCvE,EAAA,CAAAC,cAAA,cAAyD;IACrDD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAgE,kBAAA,MAAAkC,OAAA,CAAA3F,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAkB,eAAA,IAAAiF,GAAA,QACJ;;;;;;;;;;IACAnG,EAAA,CAAAC,cAAA,cAAwF;IACpFD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAgE,kBAAA,MAAAoC,OAAA,CAAA7F,WAAA,CAAAC,SAAA,mCAAAR,EAAA,CAAAkB,eAAA,IAAAmF,GAAA,QACJ;;;;;;;;;;IAIArG,EAAA,CAAAC,cAAA,cAA2D;IACvDD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAgE,kBAAA,MAAAsC,OAAA,CAAA/F,WAAA,CAAAC,SAAA,6BAAAR,EAAA,CAAAkB,eAAA,IAAAqF,GAAA,QACJ;;;;;IACAvG,EAAA,CAAAC,cAAA,cAA4D;IACxDD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAgE,kBAAA,MAAAwC,OAAA,CAAAjG,WAAA,CAAAC,SAAA,oCACJ;;;;;IAMQR,EAAA,CAAA0F,SAAA,YAA8C;;;;;IAC9C1F,EAAA,CAAA0F,SAAA,YAAoD;;;;;IAG5D1F,EAAA,CAAAC,cAAA,cAAuJ;IACnJD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAgE,kBAAA,MAAAyC,OAAA,CAAAlG,WAAA,CAAAC,SAAA,mCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAA8G;IAC1GD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAgE,kBAAA,MAAA0C,OAAA,CAAAnG,WAAA,CAAAC,SAAA,oCACJ;;;;;IAE+CR,EAAA,CAAAC,cAAA,UAA2C;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;;;IAvCzGH,EAAA,CAAAC,cAAA,SAAI;IAE+DD,EAAA,CAAAuB,UAAA,mBAAAoF,6FAAA;MAAA3G,EAAA,CAAA0B,aAAA,CAAAkF,IAAA;MAAA,MAAAC,OAAA,GAAA7G,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAA+E,OAAA,CAAAxB,gBAAA,EAAkB;IAAA,EAAC;IAACrF,EAAA,CAAAG,YAAA,EAAkB;IAE9GH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,SAAI;IACmBD,EAAA,CAAAuB,UAAA,mBAAAuF,mFAAArF,MAAA;MAAA,MAAAsF,WAAA,GAAA/G,EAAA,CAAA0B,aAAA,CAAAkF,IAAA;MAAA,MAAAI,SAAA,GAAAD,WAAA,CAAAE,QAAA;MAAA,MAAAC,OAAA,GAAAlH,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAAoF,OAAA,CAAAC,cAAA,CAAA1F,MAAA,EAAAuF,SAAA,CAA6B;IAAA,EAAC;IAA1DhH,EAAA,CAAAG,YAAA,EAA0F;IAC1FH,EAAA,CAAAS,UAAA,IAAA2G,iEAAA,kBAEM;IACNpH,EAAA,CAAAS,UAAA,KAAA4G,kEAAA,kBAEM;IACVrH,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IACmBD,EAAA,CAAAuB,UAAA,mBAAA+F,oFAAA7F,MAAA;MAAA,MAAAsF,WAAA,GAAA/G,EAAA,CAAA0B,aAAA,CAAAkF,IAAA;MAAA,MAAAI,SAAA,GAAAD,WAAA,CAAAE,QAAA;MAAA,MAAAM,OAAA,GAAAvH,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAAyF,OAAA,CAAAC,cAAA,CAAA/F,MAAA,EAAAuF,SAAA,CAA6B;IAAA,EAAC;IAA1DhH,EAAA,CAAAG,YAAA,EAA2F;IAC3FH,EAAA,CAAAS,UAAA,KAAAgH,kEAAA,kBAEM;IACNzH,EAAA,CAAAS,UAAA,KAAAiH,kEAAA,kBAEM;IACV1H,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IAEmDD,EAAA,CAAAuB,UAAA,mBAAAoG,oFAAAlG,MAAA;MAAA,MAAAsF,WAAA,GAAA/G,EAAA,CAAA0B,aAAA,CAAAkF,IAAA;MAAA,MAAAI,SAAA,GAAAD,WAAA,CAAAE,QAAA;MAAA,MAAAW,OAAA,GAAA5H,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAA8F,OAAA,CAAAC,UAAA,CAAApG,MAAA,EAAAuF,SAAA,CAAyB;IAAA,EAAC,qBAAAc,sFAAArG,MAAA;MAAA,MAAAsF,WAAA,GAAA/G,EAAA,CAAA0B,aAAA,CAAAkF,IAAA;MAAA,MAAAI,SAAA,GAAAD,WAAA,CAAAE,QAAA;MAAA,MAAAc,OAAA,GAAA/H,EAAA,CAAA6B,aAAA;MAAA,OAAY7B,EAAA,CAAA8B,WAAA,CAAAiG,OAAA,CAAAC,SAAA,CAAAvG,MAAA,EAAAuF,SAAA,CAAwB;IAAA,EAApC;IAAlFhH,EAAA,CAAAG,YAAA,EAA2N;IAC3NH,EAAA,CAAAC,cAAA,eAA0J;IAAnCD,EAAA,CAAAuB,UAAA,mBAAA0G,kFAAA;MAAA,MAAAlB,WAAA,GAAA/G,EAAA,CAAA0B,aAAA,CAAAkF,IAAA;MAAA,MAAAsB,QAAA,GAAAnB,WAAA,CAAAoB,SAAA;MAAA,OAASnI,EAAA,CAAA8B,WAAA,CAAAoG,QAAA,CAAAE,MAAA,IAAAF,QAAA,CAAAE,MAAA,CAAwB;IAAA,EAAC;IACrJpI,EAAA,CAAAS,UAAA,KAAA4H,gEAAA,gBAA8C;IAC9CrI,EAAA,CAAAS,UAAA,KAAA6H,gEAAA,gBAAoD;IACxDtI,EAAA,CAAAG,YAAA,EAAM;IAEVH,EAAA,CAAAS,UAAA,KAAA8H,kEAAA,kBAEM;IACNvI,EAAA,CAAAS,UAAA,KAAA+H,kEAAA,kBAEM;IACVxI,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IAA4BD,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAS,UAAA,KAAAgI,kEAAA,kBAAkD;IAACzI,EAAA,CAAAG,YAAA,EAAM;IAC5GH,EAAA,CAAAC,cAAA,UAAI;IAAwDD,EAAA,CAAAuB,UAAA,mBAAAmH,qFAAA;MAAA,MAAA3B,WAAA,GAAA/G,EAAA,CAAA0B,aAAA,CAAAkF,IAAA;MAAA,MAAAI,SAAA,GAAAD,WAAA,CAAAE,QAAA;MAAA,MAAA0B,OAAA,GAAA3I,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAA6G,OAAA,CAAAC,UAAA,CAAA5B,SAAA,CAAiB;IAAA,EAAC;IAAChH,EAAA,CAAA0F,SAAA,aAA2B;IAAA1F,EAAA,CAAAG,YAAA,EAAS;;;;;;IAtCvGH,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAW,UAAA,UAAAuH,QAAA,CAAc,cAAAW,OAAA,CAAAhD,YAAA;IAE/B7F,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAK,iBAAA,CAAA2G,SAAA,KAAW;IACXhH,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAA6H,QAAA,CAAAY,YAAA,CAAuB;IAE+C9I,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAW,UAAA,UAAAuH,QAAA,CAAAa,IAAA,CAAmB;IAC9D/I,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAW,UAAA,UAAAuH,QAAA,CAAAa,IAAA,kBAAAb,QAAA,CAAAa,IAAA,CAAAxE,MAAA,QAA4B;IAGjDvE,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAW,UAAA,UAAAkI,OAAA,CAAAG,WAAA,CAAAC,6BAAA,CAAAf,QAAA,CAAAa,IAAA,EAA2D;IAKK/I,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAW,UAAA,UAAAuH,QAAA,CAAAgB,KAAA,CAAoB;IAC/DlJ,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAW,UAAA,UAAAuH,QAAA,CAAAgB,KAAA,kBAAAhB,QAAA,CAAAgB,KAAA,CAAA3E,MAAA,SAA8B;IAG9BvE,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAW,UAAA,SAAAkI,OAAA,CAAAM,aAAA,CAAAjB,QAAA,CAAAgB,KAAA,EAA+B;IAM/ClJ,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAAoJ,eAAA,KAAAC,GAAA,EAAAnB,QAAA,CAAAE,MAAA,EAAuC,aAAAF,QAAA,CAAAE,MAAA,WAAAF,QAAA,CAAAoB,IAAA;IAEtCtJ,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAW,UAAA,SAAAuH,QAAA,CAAAE,MAAA,CAAiB;IACjBpI,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAW,UAAA,UAAAuH,QAAA,CAAAE,MAAA,CAAkB;IAGHpI,EAAA,CAAAI,SAAA,GAA0H;IAA1HJ,EAAA,CAAAW,UAAA,UAAAkI,OAAA,CAAA/C,WAAA,QAAAC,WAAA,GAAAC,QAAA,gBAAAD,WAAA,QAAAmC,QAAA,CAAAoB,IAAA,UAAAT,OAAA,CAAAU,kBAAA,CAAArB,QAAA,GAA0H;IAG1HlI,EAAA,CAAAI,SAAA,GAAiF;IAAjFJ,EAAA,CAAAW,UAAA,SAAAkI,OAAA,CAAA/C,WAAA,wBAAAoC,QAAA,CAAAoB,IAAA,WAAAT,OAAA,CAAAU,kBAAA,CAAArB,QAAA,GAAiF;IAIhFlI,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAgE,kBAAA,KAAAkE,QAAA,CAAAsB,OAAA,MAAmB;IAAMxJ,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAW,UAAA,SAAAuH,QAAA,CAAAsB,OAAA,IAAAtB,QAAA,CAAAsB,OAAA,MAAmC;;;;;;IAKhGxJ,EAAA,CAAAC,cAAA,cAAgF;IAC1DD,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACrEH,EAAA,CAAAC,cAAA,wBAAsH;IAAvGD,EAAA,CAAAuB,UAAA,yBAAAkI,iGAAA;MAAAzJ,EAAA,CAAA0B,aAAA,CAAAgI,IAAA;MAAA,MAAAC,OAAA,GAAA3J,EAAA,CAAA6B,aAAA;MAAA,OAAe7B,EAAA,CAAA8B,WAAA,CAAA6H,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAA8E5J,EAAA,CAAAG,YAAA,EAAgB;;;;IADrHH,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAK,iBAAA,CAAAwJ,OAAA,CAAAtJ,WAAA,CAAAC,SAAA,qBAA6C;IACwBR,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAAW,UAAA,UAAS,QAAAkJ,OAAA,CAAAC,UAAA;;;;;;;;;;;;;;;IApF5G9J,EAAA,CAAAC,cAAA,sBAWC;IATGD,EAAA,CAAAuB,UAAA,6BAAAwI,iFAAAtI,MAAA;MAAAzB,EAAA,CAAA0B,aAAA,CAAAsI,IAAA;MAAA,MAAAC,OAAA,GAAAjK,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAAAmI,OAAA,CAAA5F,iBAAA,GAAA5C,MAAA;IAAA,EAAiC,oBAAAyI,wEAAAzI,MAAA;MAAAzB,EAAA,CAAA0B,aAAA,CAAAsI,IAAA;MAAA,MAAAG,OAAA,GAAAnK,EAAA,CAAA6B,aAAA;MAAA,OAKvB7B,EAAA,CAAA8B,WAAA,CAAAqI,OAAA,CAAAC,UAAA,CAAA3I,MAAA,CAAkB;IAAA,EALK;IAUjCzB,EAAA,CAAAS,UAAA,IAAA4J,2DAAA,4BAwBc;IACdrK,EAAA,CAAAS,UAAA,IAAA6J,2DAAA,4BA2Cc;IACdtK,EAAA,CAAAS,UAAA,IAAA8J,2DAAA,0BAKc;IAClBvK,EAAA,CAAAG,YAAA,EAAU;;;;IAtFNH,EAAA,CAAAW,UAAA,UAAA6J,OAAA,CAAAvE,SAAA,CAAmB,eAAAjG,EAAA,CAAAkB,eAAA,IAAAuJ,IAAA,gBAAAD,OAAA,CAAAnG,iBAAA,kBAAAmG,OAAA,CAAAvE,SAAA,CAAA1B,MAAA,eAAAiG,OAAA,CAAAvE,SAAA,CAAA1B,MAAA,8EAAAiG,OAAA,CAAAjK,WAAA,CAAAC,SAAA,8DAAAR,EAAA,CAAAkB,eAAA,KAAAwJ,IAAA;;;;;;IAwFvB1K,EAAA,CAAAC,cAAA,qBAkBC;IALGD,EAAA,CAAAuB,UAAA,+BAAAoJ,yFAAAlJ,MAAA;MAAAzB,EAAA,CAAA0B,aAAA,CAAAkJ,IAAA;MAAA,MAAAC,OAAA,GAAA7K,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAAA+I,OAAA,CAAAC,kBAAA,GAAArJ,MAAA;IAAA,EAAoC,oCAAAsJ,8FAAA;MAAA/K,EAAA,CAAA0B,aAAA,CAAAkJ,IAAA;MAAA,MAAAI,OAAA,GAAAhL,EAAA,CAAA6B,aAAA;MAAA,OACV7B,EAAA,CAAA8B,WAAA,CAAAkJ,OAAA,CAAA3F,gBAAA,EAAkB;IAAA,EADR,gCAAA4F,0FAAA;MAAAjL,EAAA,CAAA0B,aAAA,CAAAkJ,IAAA;MAAA,MAAAM,OAAA,GAAAlL,EAAA,CAAA6B,aAAA;MAAA,OAEd7B,EAAA,CAAA8B,WAAA,CAAAoJ,OAAA,CAAA7F,gBAAA,EAAkB;IAAA,EAFJ;IAKvCrF,EAAA,CAAAG,YAAA,EAAa;;;;IAhBVH,EAAA,CAAAW,UAAA,gCAA+B,6BAAAwK,OAAA,CAAAC,OAAA,aAAAD,OAAA,CAAAE,OAAA,aAAAF,OAAA,CAAAG,WAAA,cAAAH,OAAA,CAAAI,MAAA,CAAA9I,IAAA,CAAA0I,OAAA,iBAAAA,OAAA,CAAAK,UAAA,cAAAL,OAAA,CAAAM,QAAA,UAAAN,OAAA,CAAAO,IAAA,YAAAP,OAAA,CAAAQ,UAAA,iBAAAR,OAAA,CAAAL,kBAAA,wBAAAK,OAAA,CAAA5K,WAAA,CAAAC,SAAA;;;;;;IAxJ3CR,EAAA,CAAAC,cAAA,cAAmD;IAEZD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnDH,EAAA,CAAAC,cAAA,cAA8D;IAGlDD,EAAA,CAAAS,UAAA,IAAAmL,gDAAA,0BAce;IAEf5L,EAAA,CAAAS,UAAA,IAAAoL,gDAAA,0BAae;IACnB7L,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAS,UAAA,IAAAqL,2CAAA,qBAA0M;IAC9M9L,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAsD;IAElDD,EAAA,CAAAS,UAAA,KAAAsL,8CAAA,uBAA2L;IAC/L/L,EAAA,CAAAG,YAAA,EAAM;IAEVH,EAAA,CAAAC,cAAA,eAAgF;IAC5ED,EAAA,CAAAS,UAAA,KAAAuL,yCAAA,kBAEM;IACVhM,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAwD;IACjBD,EAAA,CAAAE,MAAA,IAA4K;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACrNH,EAAA,CAAAC,cAAA,eAAiC;IACoFD,EAAA,CAAAuB,UAAA,mBAAA0K,4DAAA;MAAAjM,EAAA,CAAA0B,aAAA,CAAAwK,IAAA;MAAA,MAAAC,OAAA,GAAAnM,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAAqK,OAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IAACpM,EAAA,CAAAE,MAAA,IAAyD;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzMH,EAAA,CAAAC,cAAA,kBAAwI;IAAvBD,EAAA,CAAAuB,UAAA,mBAAA8K,4DAAA;MAAArM,EAAA,CAAA0B,aAAA,CAAAwK,IAAA;MAAA,MAAAI,OAAA,GAAAtM,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAAwK,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAACvM,EAAA,CAAAE,MAAA,IAA4D;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7MH,EAAA,CAAAC,cAAA,kBAAuI;IAAtBD,EAAA,CAAAuB,UAAA,mBAAAiL,4DAAA;MAAAxM,EAAA,CAAA0B,aAAA,CAAAwK,IAAA;MAAA,MAAAO,OAAA,GAAAzM,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAA2K,OAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IAAC1M,EAAA,CAAAE,MAAA,IAA0D;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAGlNH,EAAA,CAAAS,UAAA,KAAAkM,yCAAA,kBAGM;IACN3M,EAAA,CAAAS,UAAA,KAAAmM,6CAAA,uBAuFU;IAEV5M,EAAA,CAAAS,UAAA,KAAAoM,gDAAA,0BAkBc;IAId7M,EAAA,CAAAC,cAAA,eAAiE;IACdD,EAAA,CAAA0F,SAAA,kBAA4I;IAAA1F,EAAA,CAAAG,YAAA,EAAI;IAC/LH,EAAA,CAAAC,cAAA,kBAA+P;IAA5BD,EAAA,CAAAuB,UAAA,mBAAAuL,4DAAA;MAAA9M,EAAA,CAAA0B,aAAA,CAAAwK,IAAA;MAAA,MAAAa,OAAA,GAAA/M,EAAA,CAAA6B,aAAA;MAAA,OAAS7B,EAAA,CAAA8B,WAAA,CAAAiL,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAAChN,EAAA,CAAAG,YAAA,EAAS;;;;IA5K7OH,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAK,iBAAA,CAAA4M,MAAA,CAAAC,UAAA,CAAc;IAM5BlN,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAW,UAAA,SAAAsM,MAAA,CAAAE,WAAA,MAAsB;IAetBnN,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAW,UAAA,SAAAsM,MAAA,CAAAE,WAAA,MAAsB;IActBnN,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAW,UAAA,SAAAsM,MAAA,CAAAE,WAAA,MAAsB;IAIpBnN,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAW,UAAA,SAAAsM,MAAA,CAAAE,WAAA,MAAsB;IAI/BnN,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAW,UAAA,UAAAsM,MAAA,CAAAvJ,YAAA,IAAAuJ,MAAA,CAAAE,WAAA,MAAuC;IAKVnN,EAAA,CAAAI,SAAA,GAA4K;IAA5KJ,EAAA,CAAAoN,kBAAA,KAAAH,MAAA,CAAA1M,WAAA,CAAAC,SAAA,qCAAAyM,MAAA,CAAAE,WAAA,QAAAF,MAAA,CAAAI,YAAA,CAAAJ,MAAA,CAAAK,mBAAA,IAAAL,MAAA,CAAAI,YAAA,CAAAJ,MAAA,CAAAM,eAAA,QAAAN,MAAA,CAAAI,YAAA,CAAAJ,MAAA,CAAAO,SAAA,MAA4K;IAEnMxN,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAW,UAAA,aAAAsM,MAAA,CAAAhH,SAAA,CAAA1B,MAAA,SAAA0I,MAAA,CAAA5B,OAAA,CAAAoC,KAAA,MAAwD;IAAuEzN,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAK,iBAAA,CAAA4M,MAAA,CAAA1M,WAAA,CAAAC,SAAA,iCAAyD;IACxLR,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAW,UAAA,aAAAsM,MAAA,CAAAhH,SAAA,CAAA1B,MAAA,SAAA0I,MAAA,CAAA5B,OAAA,CAAAoC,KAAA,MAAwD;IAAwEzN,EAAA,CAAAI,SAAA,GAA4D;IAA5DJ,EAAA,CAAAK,iBAAA,CAAA4M,MAAA,CAAA1M,WAAA,CAAAC,SAAA,oCAA4D;IAC5LR,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAW,UAAA,aAAAsM,MAAA,CAAAhH,SAAA,CAAA1B,MAAA,SAAA0I,MAAA,CAAA5B,OAAA,CAAAoC,KAAA,MAAwD;IAAuEzN,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAK,iBAAA,CAAA4M,MAAA,CAAA1M,WAAA,CAAAC,SAAA,kCAA0D;IAGlKR,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAW,UAAA,SAAAsM,MAAA,CAAA5I,iBAAA,CAAAE,MAAA,KAAkC;IAMlCvE,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAW,UAAA,SAAAsM,MAAA,CAAAE,WAAA,MAAsB;IAwFxDnN,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAW,UAAA,SAAAsM,MAAA,CAAAE,WAAA,MAAsB;IAsBwCnN,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAW,UAAA,UAAAsM,MAAA,CAAA1M,WAAA,CAAAC,SAAA,yBAAuD;IACtGR,EAAA,CAAAI,SAAA,GAAwH;IAAxHJ,EAAA,CAAAW,UAAA,eAAAsM,MAAA,CAAApM,aAAA,CAAA6M,QAAA,CAAAC,WAAA,CAAAC,KAAA,IAAAX,MAAA,CAAApM,aAAA,CAAA6M,QAAA,CAAAG,WAAA,CAAAD,KAAA,KAAAX,MAAA,CAAAa,cAAA,GAAwH,UAAAb,MAAA,CAAA1M,WAAA,CAAAC,SAAA;;;;;IAS5IR,EAAA,CAAAC,cAAA,cAA+N;IAC3ND,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAgE,kBAAA,MAAA+J,MAAA,CAAAxN,WAAA,CAAAC,SAAA,mCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAsL;IAClLD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAgE,kBAAA,MAAAgK,MAAA,CAAAzN,WAAA,CAAAC,SAAA,oCACJ;;;;;IACAR,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,GAA0D;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAhEH,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAK,iBAAA,CAAA4N,MAAA,CAAA1N,WAAA,CAAAC,SAAA,kCAA0D;;;;;IAgBjDR,EAAA,CAAAC,cAAA,UAAyB;IAACD,EAAA,CAAAE,MAAA,GAAiH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAvHH,EAAA,CAAAI,SAAA,GAAiH;IAAjHJ,EAAA,CAAAoN,kBAAA,MAAAc,MAAA,CAAA3N,WAAA,CAAAC,SAAA,8BAAA0N,MAAA,CAAAC,SAAA,OAAAD,MAAA,CAAA3N,WAAA,CAAAC,SAAA,8BAAiH;;;;;;;;;;;;;;;;;;;;;;;AD1RrN,OAAM,MAAO4N,kBAAmB,SAAQ9O,aAAa;EAoBnD+O,YAAqDC,YAAoC,EACtCC,aAAmC,EAClCC,eAAsC,EAC9EC,QAAkB;IACjB,KAAK,CAACA,QAAQ,CAAC;IAJyB,KAAAH,YAAY,GAAZA,YAAY;IACd,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,eAAe,GAAfA,eAAe;IAjBnE,KAAAE,SAAS,GAAiB,EAAE;IAC5B,KAAAC,cAAc,GAAiB,EAAE;IAIjC,KAAAC,aAAa,GAAW,KAAK;IAC7B,KAAAC,aAAa,GAAW,KAAK;IAC7B,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAhN,kBAAkB,GAAQ,EAAE;IAC5B,KAAAQ,yBAAyB,GAAG,IAAI/C,gBAAgB,EAAE;IAElD,KAAAwP,eAAe,GAAQ,EAAE;IASzB,KAAAC,aAAa,GAAqB,IAAIzP,gBAAgB,EAAE;IACxD,KAAA0P,iBAAiB,GAAG,EAAE;IAItB,KAAAzL,aAAa,GAAY,IAAI;IAC7B,KAAA5C,aAAa,GAAG,IAAIzB,SAAS,CAAC;MAC5BuO,WAAW,EAAE,IAAIxO,WAAW,CAAC,IAAI,EAAE,CAACE,UAAU,CAAC2B,QAAQ,CAAC,CAAC;MACzDmO,SAAS,EAAE,IAAIhQ,WAAW,CAAC,IAAIiQ,IAAI,CAAJ,CAAI,CAAC;MACpCC,WAAW,EAAE,IAAIlQ,WAAW,EAAE;MAC9BmQ,mBAAmB,EAAE,IAAInQ,WAAW,CAAC,CAAC,CAAC;MACvC0O,WAAW,EAAE,IAAI1O,WAAW,CAAC,IAAI,EAAE,CAACE,UAAU,CAACkQ,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MAC/DC,GAAG,EAAE,IAAIrQ,WAAW,CAAC,IAAI,EAAE,CAACE,UAAU,CAAC2B,QAAQ,EAAEtB,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MACpE+P,WAAW,EAAE,IAAItQ,WAAW,CAAC,CAAC;KAC/B,CAAC;IAGF,KAAAuQ,kBAAkB,GAAY,KAAK;IAEnC,KAAAC,wBAAwB,GAAY,KAAK;IACzC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,iBAAiB,GAAY,KAAK;IAGlC,KAAAnM,YAAY,GAAY,IAAI;IAC5B,KAAAoM,gBAAgB,GAAY,KAAK;IAEjC,KAAAC,YAAY,GAAW,MAAM;IAc3B,KAAAC,gBAAgB,GAAgB;MAC5BC,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;IACD,KAAAC,eAAe,GAAW,CAAC;IAC3B,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,YAAY,GAGR;MACAC,OAAO,EAAE,EAAE;MACXhD,KAAK,EAAE;KACV;IACD,KAAAiD,eAAe,GAEX;MACAC,KAAK,EAAE;KACV;IACD,KAAAC,YAAY,GAAsB,CAC9B;MACI7H,IAAI,EAAE,IAAI,CAACxI,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACxDqQ,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIlI,IAAI,EAAE,IAAI,CAACxI,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;MAC9DqQ,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CAAC;IACR,KAAAC,cAAc,GAAW,CAAC;IAK1B,KAAA7M,iBAAiB,GAAG,EAAE;IACtB,KAAA8M,WAAW,GAAG,IAAI;IAClB,KAAArG,kBAAkB,GAAe,EAAE;IACnC,KAAAjF,YAAY,GAAG,IAAI;EAzFU;EA2F7BuL,UAAUA,CAAA;IACR,IAAI,CAACtC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACX,SAAS,GAAG,EAAE;IACnB,IAAI,CAACkD,cAAc,EAAE;EACvB;EAEAlI,aAAaA,CAACD,KAAY;IACxB,IAAI,CAACA,KAAK,EAAC;MACT,OAAO,KAAK;;IAEd,MAAMoI,OAAO,GAAU,kDAAkD;IACzE,OAAO,CAACA,OAAO,CAACC,IAAI,CAACrI,KAAK,CAAC;EAC7B;EAEAsI,mBAAmBA,CAACb,KAAW;IAC7B,IAAIA,KAAK,IAAI,IAAI,EAAE,OAAO,EAAE;IAE5B,MAAMc,GAAG,GAAIC,GAAW,IAAKA,GAAG,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAE5D,MAAMC,GAAG,GAAGJ,GAAG,CAACd,KAAK,CAACmB,OAAO,EAAE,CAAC;IAChC,MAAMC,KAAK,GAAGN,GAAG,CAACd,KAAK,CAACqB,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IACzC,MAAMC,IAAI,GAAGtB,KAAK,CAACuB,WAAW,EAAE;IAEhC,MAAMC,KAAK,GAAGV,GAAG,CAACd,KAAK,CAACyB,QAAQ,EAAE,CAAC;IACnC,MAAMC,OAAO,GAAGZ,GAAG,CAACd,KAAK,CAAC2B,UAAU,EAAE,CAAC;IACvC,MAAMC,OAAO,GAAGd,GAAG,CAACd,KAAK,CAAC6B,UAAU,EAAE,CAAC;IAEvC,OAAO,GAAGX,GAAG,IAAIE,KAAK,IAAIE,IAAI,IAAIE,KAAK,IAAIE,OAAO,IAAIE,OAAO,EAAE;EACjE;EAEAE,UAAUA,CAAA;IACR,IAAIC,EAAE,GAAG,IAAI;IACb,IAAI,CAAC7P,aAAa,GAAG,IAAI;IACzB,IAAI,IAAI,CAACsK,WAAW,IAAI,CAAC,EAAE;MACvB,MAAMwF,aAAa,GAAG;QAClBC,OAAO,EAAE,IAAI,CAACC,cAAc,CAACD,OAAO;QACpCpD,GAAG,EAAE,IAAI,CAAC3O,aAAa,CAACC,GAAG,CAAC,KAAK,CAAC,CAAC6P,KAAK;QACxCmC,MAAM,EAAE,IAAI,CAACjS,aAAa,CAACC,GAAG,CAAC,aAAa,CAAC,CAAC6P,KAAK;QACnDoC,UAAU,EAAE,IAAI,CAACvB,mBAAmB,CAAC,IAAI,CAAC3Q,aAAa,CAACC,GAAG,CAAC,WAAW,CAAC,CAAC6P,KAAK,CAAC;QAC/EqC,OAAO,EAAEC,MAAM,CAAC,IAAI,CAACjE,eAAe,CAAC;QACrCkE,UAAU,EAAED,MAAM,CAAC,IAAI,CAAC/B,cAAc,CAAC;QACvCiC,OAAO,EAAE,EAAE;QACXrN,WAAW,EAAE,MAAM;QACnBsN,UAAU,EAAE,SAAS;QACrBC,IAAI,EAAE,CAAC;QACPC,UAAU,EAAE,IAAI,CAACxI,kBAAkB,CAACyI,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC1K,YAAY;OACpE;MAED4J,EAAE,CAACe,oBAAoB,CAACC,MAAM,EAAE;MAEhC,IAAI,CAACpF,YAAY,CAACqF,mBAAmB,CAAChB,aAAa,EAAGiB,GAAG,IAAI;QAEzD,IAAIA,GAAG,CAACC,UAAU,KAAK,cAAc,EAAE;UACnC,IAAIC,EAAE,GAAG,CAAC;UACV,IAAIC,cAAc,GAAG,EAAE;UACvB,IAAIC,UAAU,GAAG,CAAC;UAClBJ,GAAG,CAACtK,IAAI,CAAC2K,OAAO,CAACC,OAAO,IAAG;YACvB,IAAGA,OAAO,CAACC,MAAM,IAAI,CAAC,IAAID,OAAO,CAACE,KAAK,IAAI,EAAE,EAAC;cAC1C;cACAL,cAAc,CAACM,IAAI,CAAC;gBAChBvL,YAAY,EAAE/I,uBAAuB,CAACmU,OAAO,CAACI,GAAG,CAAC;gBAClD;gBACAzG,WAAW,EAAE;eAChB,CAAC;cACFiG,EAAE,GAAG,CAAC;;YAEVE,UAAU,EAAE;UAChB,CAAC,CAAC;UACF,IAAGF,EAAE,IAAI,CAAC,EAAC;YACP,IAAI,CAAC/E,OAAO,GAAG,IAAI;YACnB,IAAI,CAACyB,YAAY,GAAG;cAChBC,OAAO,EAAEsD,cAAc;cACvBtG,KAAK,EAAEsG,cAAc,CAACxP;aACzB;YACD,IAAI,CAACgQ,gBAAgB,GAAG;cAAC,GAAG,IAAI,CAAC/D;YAAY,CAAC;YAC9CkC,EAAE,CAACe,oBAAoB,CAACW,KAAK,CAAC1B,EAAE,CAACnS,WAAW,CAACC,SAAS,CAAC,kCAAkC,EAAE;cAACgU,OAAO,EAAER,UAAU,GAAGD,cAAc,CAACxP,MAAM;cAAEkJ,KAAK,EAAGuG;YAAU,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;WAC9K,MAAM;YACPtB,EAAE,CAACe,oBAAoB,CAACe,OAAO,CAAC9B,EAAE,CAACnS,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC,CAAC;YACvF,IAAI,CAACiU,MAAM,CAACC,QAAQ,CAAC,CAAC,gCAAgC,CAAC,CAAC;;SAE3D,MAAK,IAAId,GAAG,CAACC,UAAU,KAAK,SAAS,EAAE;UACpCnB,EAAE,CAACe,oBAAoB,CAACe,OAAO,CAAC9B,EAAE,CAACnS,WAAW,CAACC,SAAS,CAAC,wCAAwC,CAAC,EAAC,IAAI,EAAE,KAAK,CAAC;UAC/G,IAAI,CAACiU,MAAM,CAACC,QAAQ,CAAC,CAAC,gCAAgC,CAAC,CAAC;SAC3D,MAAM;UACH,IAAI,CAACjB,oBAAoB,CAACW,KAAK,CAACR,GAAG,CAACe,OAAO,CAAC;;MAEpD,CAAC,EAAE,IAAI,EAAE,MAAI;QACTjC,EAAE,CAACe,oBAAoB,CAACmB,OAAO,EAAE;MACrC,CAAC,CAAC;KACL,MAAM;MACH,MAAMC,gBAAgB,GAAG,IAAI,CAAC5O,SAAS,CAACsN,GAAG,CAAEuB,CAAC,KAAM;QAChDC,KAAK,EAAEC,MAAM,CAACF,CAAC,EAAEhM,YAAY,CAAC;QAC9BhD,WAAW,EAAE,MAAM;QACnBoN,UAAU,EAAED,MAAM,CAAC6B,CAAC,EAAExL,IAAI,CAAC;QAC3B8J,UAAU,EAAE,SAAS;QACrBrK,IAAI,EAAE+L,CAAC,EAAE/L,IAAI;QACbG,KAAK,EAAE4L,CAAC,EAAE5L,KAAK;QACfmK,IAAI,EAAE,CAAC;QACP4B,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC5Q,iBAAiB,CAAC6Q,IAAI,CAAC1B,IAAI,IAAGA,IAAI,CAAC1K,YAAY,KAAIgM,CAAC,CAAChM,YAAY;OACnF,CAAC,CAAC;MAEH,MAAMqM,QAAQ,GAAG;QACbvC,OAAO,EAAE,IAAI,CAACC,cAAc,CAACD,OAAO;QACpCpD,GAAG,EAAE,IAAI,CAAC3O,aAAa,CAACC,GAAG,CAAC,KAAK,CAAC,CAAC6P,KAAK;QACxCmC,MAAM,EAAE,IAAI,CAACjS,aAAa,CAACC,GAAG,CAAC,aAAa,CAAC,CAAC6P,KAAK;QACnDoC,UAAU,EAAE,IAAI,CAACvB,mBAAmB,CAAC,IAAI,CAAC3Q,aAAa,CAACC,GAAG,CAAC,WAAW,CAAC,CAAC6P,KAAK,CAAC;QAC/EyE,UAAU,EAAEP,gBAAgB;QAC5B1B,OAAO,EAAE;OACZ;MAEDkC,OAAO,CAACC,GAAG,CAACT,gBAAgB,CAAC;MAE7BnC,EAAE,CAACe,oBAAoB,CAACC,MAAM,EAAE;MAEhC,IAAI,CAACpF,YAAY,CAACiH,YAAY,CAACJ,QAAQ,EAAGvB,GAAG,IAAI;QAE7C,IAAIA,GAAG,CAACC,UAAU,KAAK,cAAc,EAAE;UACnC,IAAIC,EAAE,GAAG,CAAC;UACV,IAAIC,cAAc,GAAG,EAAE;UACvBH,GAAG,CAACtK,IAAI,CAAC2K,OAAO,CAACC,OAAO,IAAG;YACvB,IAAGA,OAAO,CAACC,MAAM,IAAI,CAAC,IAAID,OAAO,CAACE,KAAK,IAAI,EAAE,EAAC;cAC1C;cACAL,cAAc,CAACM,IAAI,CAAC;gBAChBvL,YAAY,EAAE/I,uBAAuB,CAACmU,OAAO,CAACI,GAAG,CAAC;gBAClD;gBACAzG,WAAW,EAAE;eAChB,CAAC;cACFiG,EAAE,GAAG,CAAC;;UAEd,CAAC,CAAC;UACF,IAAGA,EAAE,IAAI,CAAC,EAAC;YACP,IAAI,CAAC/E,OAAO,GAAG,IAAI;YACnB,IAAI,CAACyB,YAAY,GAAG;cAChBC,OAAO,EAAEsD,cAAc;cACvBtG,KAAK,EAAEsG,cAAc,CAACxP;aACzB;YACD,IAAI,CAACgQ,gBAAgB,GAAG;cAAC,GAAG,IAAI,CAAC/D;YAAY,CAAC;YAC9CkC,EAAE,CAACe,oBAAoB,CAACW,KAAK,CAAC1B,EAAE,CAACnS,WAAW,CAACC,SAAS,CAAC,kCAAkC,EAAE;cAACgU,OAAO,EAAE,IAAI,CAACvO,SAAS,CAAC1B,MAAM,GAAG,IAAI,CAACiM,YAAY,CAACC,OAAO,CAAClM,MAAM;cAAEkJ,KAAK,EAAG,IAAI,CAACxH,SAAS,CAAC1B;YAAM,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;WAC/M,MAAK;YACFmO,EAAE,CAACe,oBAAoB,CAACe,OAAO,CAAC9B,EAAE,CAACnS,WAAW,CAACC,SAAS,CAAC,qCAAqC,EAAE;cAACgU,OAAO,EAAE,IAAI,CAACvO,SAAS,CAAC1B,MAAM;cAAEkJ,KAAK,EAAG,IAAI,CAACxH,SAAS,CAAC1B;YAAM,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;YAC9K,IAAI,CAACkQ,MAAM,CAACC,QAAQ,CAAC,CAAC,gCAAgC,CAAC,CAAC;;SAE/D,MAAK,IAAId,GAAG,CAACC,UAAU,KAAK,SAAS,EAAE;UACpCnB,EAAE,CAACe,oBAAoB,CAACe,OAAO,CAAC9B,EAAE,CAACnS,WAAW,CAACC,SAAS,CAAC,wCAAwC,CAAC,EAAC,IAAI,EAAE,KAAK,CAAC;UAC/G,IAAI,CAACiU,MAAM,CAACC,QAAQ,CAAC,CAAC,gCAAgC,CAAC,CAAC;SAC3D,MAAM;UACH,IAAI,CAACjB,oBAAoB,CAACW,KAAK,CAACR,GAAG,CAACe,OAAO,CAAC;;MAEpD,CAAC,EAAE,IAAI,EAAE,MAAI;QACTjC,EAAE,CAACe,oBAAoB,CAACmB,OAAO,EAAE;MACrC,CAAC,CAAC;;IAEN,IAAI,CAACzD,WAAW,GAAG,IAAI;EACzB;EAEAqE,QAAQA,CAAA;IACJ,IAAI9C,EAAE,GAAG,IAAI;IACbA,EAAE,CAACtH,OAAO,GAAG,CACT;MACIrC,IAAI,EAAE,IAAI,CAACxI,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACxDqQ,GAAG,EAAE,cAAc;MACnBC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIlI,IAAI,EAAE,IAAI,CAACxI,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;MAC3DqQ,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIlI,IAAI,EAAE,IAAI,CAACxI,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACxDqQ,GAAG,EAAE,OAAO;MACZC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,KAAK;MACbwE,aAAa,EAAE,IAAI;MACnBC,KAAK,EAAE;QACHC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,OAAO;QACjBC,QAAQ,EAAE,QAAQ;QAClBC,YAAY,EAAE;;KAErB,EACD;MACI/M,IAAI,EAAE2J,EAAE,CAAC5M,WAAW,IAAI,UAAU,GAC5B4M,EAAE,CAACnS,WAAW,CAACC,SAAS,CAAC,4BAA4B,EAAE;QAAC6S,IAAI,EAAE;MAAI,CAAC,CAAC,GACpEX,EAAE,CAAC5M,WAAW,IAAI,WAAW,GACzB4M,EAAE,CAACnS,WAAW,CAACC,SAAS,CAAC,4BAA4B,EAAE;QAAC6S,IAAI,EAAE;MAAM,CAAC,CAAC,GACtE,CAACX,EAAE,CAAC5M,WAAW,IAAI,EAAE,EAAEC,WAAW,EAAE,CAACC,QAAQ,CAAC,SAAS,CAACD,WAAW,EAAE,CAAC,GAClE2M,EAAE,CAACnS,WAAW,CAACC,SAAS,CAAC,4BAA4B,EAAE;QAAC6S,IAAI,EAAE;MAAK,CAAC,CAAC,GACrEX,EAAE,CAACnS,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC;MAC3EqQ,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,KAAK;MACb8E,eAAeA,CAACpF,KAAK;QACjB,QAAQ+B,EAAE,CAAC5M,WAAW;UAClB,KAAK,UAAU;YACX,OAAO,GAAG4M,EAAE,CAACxB,cAAc,EAAES,QAAQ,EAAE,OAAO;UAClD,KAAK,WAAW;YACZ,OAAO,GAAGe,EAAE,CAACxB,cAAc,EAAES,QAAQ,EAAE,SAAS;UACpD,KAAK,SAAS;UACd,KAAK,oBAAoB;UACzB,KAAK,aAAa;YACd,OAAO,GAAGe,EAAE,CAACxB,cAAc,EAAES,QAAQ,EAAE,QAAQ;UACnD;YACI,OAAO,EAAE;;MAErB;KACH,EACD;MACI5I,IAAI,EAAE,IAAI,CAACxI,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAC7DqQ,GAAG,EAAE,kBAAkB;MACvBC,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,KAAK;MACb8E,eAAeA,CAACpF,KAAK;QACjB,OAAO+B,EAAE,CAACsD,iBAAiB,EAAErE,QAAQ,EAAE,GAAG,GAAGe,EAAE,CAACsD,iBAAiB,EAAErE,QAAQ,EAAE,IAAI,GAAG,EAAE;MAC1F;KACH,CACJ;IACD,IAAI,CAACsE,KAAK,GAAG,CAAC;MAACC,KAAK,EAAE,IAAI,CAAC3V,WAAW,CAACC,SAAS,CAAC,+BAA+B;IAAC,CAAC,EAAE;MAChF0V,KAAK,EAAE,IAAI,CAAC3V,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;MAC1D2V,UAAU,EAAE,CAAC,gCAAgC;KAChD,EAAE;MAACD,KAAK,EAAE,IAAI,CAAC3V,WAAW,CAACC,SAAS,CAAC,0BAA0B;IAAC,CAAC,CAAC;IACnE,IAAI,CAAC4V,IAAI,GAAG;MAACC,IAAI,EAAE,YAAY;MAAEF,UAAU,EAAE;IAAG,CAAC;IACjD,IAAI,CAACG,YAAY,EAAE;IACnB,IAAI,CAAC1H,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACF,SAAS,GAAG,EAAE;IACnB;IACA,IAAI,CAAC6H,iBAAiB,CAACC,SAAS,CAAC7W,SAAS,CAAC8W,UAAU,CAACC,iBAAiB,EAAE;MACrEC,IAAI,EAAE,IAAI,CAACC,QAAQ,CAACnU,IAAI,CAAC,IAAI;KAChC,CAAC;IACF,IAAI,CAAC0K,WAAW,GAAG,CAAC;IACpBuF,EAAE,CAACmE,WAAW,GAAG,EAAE;IACnBnE,EAAE,CAACpH,WAAW,GAAG;MACb2E,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,IAAI;MACnBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE,KAAK;MAC1B0G,gBAAgB,EAAE,KAAK;MACvBC,SAAS,EAAE;KACd;IACDrE,EAAE,CAAC/G,UAAU,GAAG;MACZgF,KAAK,EAAE;KACV;IACD+B,EAAE,CAAClH,UAAU,GAAG,CAAC;IACjBkH,EAAE,CAACjH,QAAQ,GAAG,EAAE;IAChBiH,EAAE,CAAChH,IAAI,GAAG,mBAAmB;IAC7BgH,EAAE,CAACrH,OAAO,GAAG;MACToF,OAAO,EAAE,EAAE;MACXhD,KAAK,EAAE;KACV;EACL;EAEAmJ,QAAQA,CAACtN,IAAI;IACT,IAAIA,IAAI,EAAE;MACN,IAAGA,IAAI,CAAC/E,MAAM,GAAG,IAAI,EAAC;QAClB,IAAI,CAACkP,oBAAoB,CAACW,KAAK,CAAC,IAAI,CAAC7T,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,CAAC;QAC7F;;MAEJ,IAAI,CAACyF,SAAS,GAAGqD,IAAI;MACrB,IAAI0N,aAAa,GAAG,IAAI,CAAC3S,iBAAiB;MAC1C,IAAI,CAACA,iBAAiB,GAAG,EAAE;MAC3B;MACAiF,IAAI,CAAC2K,OAAO,CAAC,CAACT,IAAI,EAAEyD,KAAK,KAAI;QACzB,IAAGzD,IAAI,CAACyB,MAAM,KAAKzB,IAAI,CAACyB,MAAM,CAACiC,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE;UAAEC,WAAW,EAAE;QAAM,CAAE,CAAC,KAAK,CAAC,IAAI3D,IAAI,CAACyB,MAAM,CAACiC,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE;UAAEC,WAAW,EAAE;QAAM,CAAE,CAAC,KAAK,CAAC,CAAC,EAAC;UAE7J,IAAI,CAAC9S,iBAAiB,CAACgQ,IAAI,CAACb,IAAI,CAAC;;MAEzC,CAAC,CAAC;MACF,IAAI,CAACnP,iBAAiB,GAAG,CACrB,GAAG,IAAI,CAACA,iBAAiB,EACzB,GAAG2S,aAAa,CACnB;;IAEL,IAAI,CAACI,cAAc,EAAE;IACrB,IAAG,CAAC,IAAI,CAACvR,YAAY,EAAC;MAClB,IAAI,CAACxB,iBAAiB,GAAG,EAAE;MAC3B,IAAI,CAACoP,oBAAoB,CAAC4D,OAAO,CAAC,2FAA2F,CAAC;;EAEtI;EAEAC,YAAYA,CAAA;IACR,IAAI5E,EAAE,GAAG,IAAI;IACb,IAAI,IAAI,CAACvF,WAAW,IAAI,CAAC,EAAE;MACvB,IAAI,CAAC+D,cAAc,GAAG,CAAC;MACvB,IAAI,CAAC8E,iBAAiB,GAAG,CAAC;;IAE9B,IAAG,IAAI,CAAC/P,SAAS,EAAC;MACd,IAAI,CAACA,SAAS,CAACgO,OAAO,CAACT,IAAI,IAAG;QAC1BA,IAAI,CAAClK,IAAI,GAAG,IAAI;QAChBkK,IAAI,CAAChK,OAAO,GAAG,IAAI;MACvB,CAAC,CAAC;KACL,MAAI;MACD,IAAI,CAACvD,SAAS,GAAG,EAAE;;IAEzB,MAAMsR,QAAQ,GAAG,IAAI,CAAC1W,aAAa,CAACC,GAAG,CAAC,aAAa,CAAC,CAAC6P,KAAK;IAC5D,MAAM6G,MAAM,GAAG,IAAI,CAACC,WAAW,EAAEvC,IAAI,CAACwC,CAAC,IAAIA,CAAC,CAAC9E,OAAO,KAAK2E,QAAQ,CAAC;IAElE,IAAIC,MAAM,EAAE;MACV,IAAI,CAAC3E,cAAc,GAAG2E,MAAM;MAC5B,IAAGA,MAAM,CAACG,QAAQ,IAAI,CAAC,EAAC;QACpB,IAAI,CAACxG,WAAW,GAAG,IAAI;QACvB,IAAI,CAACtL,YAAY,GAAG,IAAI;QACxB6M,EAAE,CAACpH,WAAW,GAAG;UACb2E,gBAAgB,EAAE,KAAK;UACvBC,aAAa,EAAE,IAAI;UACnBC,YAAY,EAAE,IAAI;UAClBC,mBAAmB,EAAE,KAAK;UAC1B0G,gBAAgB,EAAE,KAAK;UACvBC,SAAS,EAAE;SACd;OACJ,MAAI;QACD,IAAI,CAAC5F,WAAW,GAAG,KAAK;QACxB,IAAI,CAACtL,YAAY,GAAG,KAAK;QACzB6M,EAAE,CAACpH,WAAW,GAAG;UACb2E,gBAAgB,EAAE,KAAK;UACvBC,aAAa,EAAE,IAAI;UACnBC,YAAY,EAAE,IAAI;UAClBC,mBAAmB,EAAE,KAAK;UAC1B0G,gBAAgB,EAAE,IAAI;UACtBC,SAAS,EAAE;SACd;;MAEH,IAAI,CAACjM,kBAAkB,GAAG,EAAE;MAC5B,IAAI,CAACzG,iBAAiB,GAAG,EAAE;MAC7B,IAAI,CAAC6I,UAAU,GAAG,IAAI,CAAC2F,cAAc,CAAC+E,WAAW;KAClD,MAAM;MACHvC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEiC,QAAQ,CAAC;;IAEtD;IACA,IAAI,CAAC3I,aAAa,GAAG,IAAI;IACzB;IACA,IAAI,CAAC9I,WAAW,GAAG,IAAI,CAAC2R,WAAW,EAAEvC,IAAI,CAACsC,MAAM,IAAIA,MAAM,CAAC5E,OAAO,KAAK,IAAI,CAAC/R,aAAa,CAACC,GAAG,CAAC,aAAa,CAAC,CAAC6P,KAAK,CAAC,EAAE7K,WAAW;IAChI,IAAI,CAAC0H,SAAS,GAAG,IAAI,CAACiK,WAAW,EAAEvC,IAAI,CAACsC,MAAM,IAAIA,MAAM,CAAC5E,OAAO,KAAK,IAAI,CAAC/R,aAAa,CAACC,GAAG,CAAC,aAAa,CAAC,CAAC6P,KAAK,CAAC,EAAEkH,gBAAgB;IACnI,IAAI,CAACtK,eAAe,GAAG,IAAI,CAACkK,WAAW,EAAEvC,IAAI,CAACsC,MAAM,IAAIA,MAAM,CAAC5E,OAAO,KAAK,IAAI,CAAC/R,aAAa,CAACC,GAAG,CAAC,aAAa,CAAC,CAAC6P,KAAK,CAAC,EAAEmH,qBAAqB;IAC9I,IAAI,CAACxK,mBAAmB,GAAG,IAAI,CAACmK,WAAW,EAAEvC,IAAI,CAACsC,MAAM,IAAIA,MAAM,CAAC5E,OAAO,KAAK,IAAI,CAAC/R,aAAa,CAACC,GAAG,CAAC,aAAa,CAAC,CAAC6P,KAAK,CAAC,EAAEmH,qBAAqB;IAClJ,IAAI,CAACC,YAAY,GAAG,IAAI,CAACN,WAAW,EAAEvC,IAAI,CAACsC,MAAM,IAAIA,MAAM,CAAC5E,OAAO,KAAK,IAAI,CAAC/R,aAAa,CAACC,GAAG,CAAC,aAAa,CAAC,CAAC6P,KAAK,CAAC,EAAEmH,qBAAqB;IAE3I,IAAG,CAAC,IAAI,CAAChS,WAAW,IAAI,EAAE,EAAEC,WAAW,EAAE,CAACC,QAAQ,CAAC,SAAS,CAACD,WAAW,EAAE,CAAC,EAAC;MACxE,IAAI,CAACgK,YAAY,GAAG,KAAK;KAC5B,MAAK,IAAI,IAAI,CAACjK,WAAW,IAAI,UAAU,EAAC;MACrC,IAAI,CAACiK,YAAY,GAAG,IAAI;;EAI9B;EAEEvN,mBAAmBA,CAACwV,MAAM,EAAEC,QAAQ;IAChC,OAAO,IAAI,CAAC3J,YAAY,CAAC9L,mBAAmB,CAACwV,MAAM,EAAGE,QAAQ,IAAG;MAC7D,IAAI,CAACxJ,SAAS,GAAGwJ,QAAQ,CAACzH,OAAO;MACjCwH,QAAQ,CAACC,QAAQ,CAAC;IACtB,CAAC,CAAC;EACN;EAEAC,iBAAiBA,CAACH,MAAM,EAAEC,QAAQ;IAC9B,IAAIvF,EAAE,GAAG,IAAI;IACb,IAAI,CAACe,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAI,CAAClF,eAAe,CAACjD,MAAM,CAACyM,MAAM,EAAGE,QAAQ,IAAI;MAC7C;MACAxF,EAAE,CAAC/D,cAAc,GAAGuJ,QAAQ,CAACzH,OAAO;MACpC,IAAInH,IAAI,GAAG;QACPmH,OAAO,EAAEyH,QAAQ,CAACzH,OAAO;QACzB2H,UAAU,EAAEF,QAAQ,CAACG;OACxB;IACL,CAAC,EAAC,IAAI,EAAC,MAAI;MACP,IAAI,CAAC5E,oBAAoB,CAACmB,OAAO,EAAE;IACvC,CAAC,CAAC;EACN;EAEF0B,YAAYA,CAAA;IACR,IAAI5D,EAAE,GAAG,IAAI;IACb,IAAI,CAACe,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAI,CAACnF,aAAa,CAAC+J,iBAAiB,CAAEJ,QAAQ,IAAI;MAC9CxF,EAAE,CAAC+E,WAAW,GAAGS,QAAQ;IAC7B,CAAC,EAAE,IAAI,EAAE,MAAK;MACV,IAAI,CAACzE,oBAAoB,CAACmB,OAAO,EAAE;IACvC,CAAC,CAAC;EACN;EAEAvS,QAAQA,CAACiH,IAAI,EAAEiP,UAAW;IACxB,IAAI7F,EAAE,GAAG,IAAI;IACX,IAAGpJ,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKkP,SAAS,IAAIlP,IAAI,KAAK,EAAE,EAAC;MAClD;;IAEJ,IAAG,IAAI,CAACrD,SAAS,CAAC1B,MAAM,GAAG,GAAG,EAAC;MAC3B,IAAI,CAACkP,oBAAoB,CAACW,KAAK,CAAC,IAAI,CAAC7T,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,CAAC;MAC7F;;IAEN,IAAI,CAACiD,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACwC,SAAS,GAAG,IAAI,CAACwS,UAAU,CAAC,IAAI,CAACxS,SAAS,CAAC;IAChD,MAAM0K,KAAK,GAAG,IAAI,CAACjC,SAAS,CAACwG,IAAI,CAACwD,GAAG,IAAIA,GAAG,CAAC5P,YAAY,KAAKQ,IAAI,CAAC;IACnE,IAAG,IAAI,CAACrD,SAAS,CAACiP,IAAI,CAACyD,CAAC,IAAIA,CAAC,EAAE7P,YAAY,KAAKQ,IAAI,CAAC,EAAC;MACpD,IAAI,CAACmK,oBAAoB,CAACW,KAAK,CAAC,IAAI,CAAC7T,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC,CAAC;KACxF,MAAK;MACF;;;;;;;;MAQA,IAAI,CAACoY,aAAa,CAACjI,KAAK,EAAErH,IAAI,CAAC;MAC/B;;;MAGA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;;IAGF,IAAI,CAAC/G,yBAAyB,CAACsW,MAAM,EAAE;IACvC,IAAI,CAACtW,yBAAyB,CAACuW,UAAU,EAAE;IAC3C,IAAI,CAACvW,yBAAyB,CAACwW,WAAW,EAAE;IAC5C,IAAI,CAACxW,yBAAyB,CAACyW,YAAY,EAAE;IAC7C,IAAI,CAACjX,kBAAkB,GAAG,EAAE;EAChC;EAEE6W,aAAaA,CAACjI,KAAK,EAAErH,IAAI;IACrB,IAAIoJ,EAAE,GAAG,IAAI;IACb,IAAIuG,SAAS;IACb,IAAG,CAAC,IAAI,CAACnT,WAAW,IAAI,EAAE,EAAEC,WAAW,EAAE,CAACC,QAAQ,CAAC,SAAS,CAACD,WAAW,EAAE,CAAC,EAAC;MACxEkT,SAAS,GAAG,EAAE;KACjB,MAAK,IAAG,IAAI,CAACnT,WAAW,IAAI,UAAU,EAAC;MACpCmT,SAAS,GAAG,GAAG;KAClB,MAAI;MACDA,SAAS,GAAG,IAAI;;IAEpB,IAAI,IAAI,CAAC1L,eAAe,GAAG0L,SAAS,EAAC;MACjC,OAAO,IAAI,CAACxF,oBAAoB,CAACW,KAAK,CAAC,IAAI,CAAC7T,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC,CAAC;;IAEvG,IAAGmQ,KAAK,EAAC;MACL,IAAGA,KAAK,EAAEnH,OAAO,EAAC;QACdmH,KAAK,CAACnH,OAAO,GAAC,IAAI;;MAEtBmH,KAAK,CAACrH,IAAI,GAAG2P,SAAS;MACtB,IAAI,CAAChT,SAAS,CAACoO,IAAI,CAAC1D,KAAK,CAAC;MAC1BuI,UAAU,CAAC;QACPxG,EAAE,CAAC3Q,kBAAkB,GAAG,EAAE;MAC9B,CAAC,EAAC,GAAG,CAAC;MACN,IAAI,CAAC0B,aAAa,GAAG,IAAI;MACzB,IAAI,CAACwC,SAAS,GAAG,CAAC,GAAG,IAAI,CAACA,SAAS,CAAC;KACvC,MAAI;MACD,IAAIkT,QAAQ,GAAgB;QACxBrQ,YAAY,EAAEQ,IAAI;QAClBP,IAAI,EAAC,EAAE;QACPG,KAAK,EAAC,EAAE;QACRI,IAAI,EAAC2P;OACR;MACD,IAAI,CAAChT,SAAS,CAACoO,IAAI,CAAC8E,QAAQ,CAAC;MAC7B,IAAI,CAAClT,SAAS,GAAG,CAAC,GAAG,IAAI,CAACA,SAAS,CAAC;;IAExC,IAAI,CAACmR,cAAc,EAAE;EACzB;EAEFlV,aAAaA,CAAA;IACX,IAAI,CAACuB,aAAa,GAAG,IAAI;IACzB,IAAG,CAAC,IAAI,CAACiL,SAAS,CAACwG,IAAI,CAACwD,GAAG,IAAIA,GAAG,CAAC5P,YAAY,KAAK,IAAI,CAAC/G,kBAAkB,CAAC,EAAC;MAC3E,IAAI,CAAC0B,aAAa,GAAG,KAAK;KAC3B,MAAI;MACH,IAAI,CAACA,aAAa,GAAG,IAAI;;IAE3B,IAAG,IAAI,CAAC1B,kBAAkB,IAAI,EAAE,IAAG,IAAI,CAACA,kBAAkB,IAAI,IAAI,IAAI,IAAI,CAACA,kBAAkB,KAAKyW,SAAS,EAAC;MAC1G,IAAI,CAAC/U,aAAa,GAAG,IAAI;;IAG3B,MAAM2V,KAAK,GAAG,gBAAgB;IAC9B,MAAMC,UAAU,GAAG,IAAI,CAACtX,kBAAkB;IAC1C,IAAI,CAAC2B,YAAY,GAAG0V,KAAK,CAAC7H,IAAI,CAAC8H,UAAU,CAAC;EAC5C;EAEEvL,cAAcA,CAAA;IACV,IAAK,IAAI,CAAC7H,SAAS,CAAC1B,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC4I,WAAW,IAAI,CAAC,IACpD,IAAI,CAACG,mBAAmB,KAAK,CAAC,IAAI,IAAI,CAACH,WAAW,IAAI,CAAE,EAAE;MAC3D,OAAO,IAAI;;IAGf,IAAI,IAAI,CAACA,WAAW,IAAI,CAAC,IAAI,IAAI,CAAClH,SAAS,CAAC1B,MAAM,IAAI,CAAC,EAAE;MACrD,KAAK,IAAIiP,IAAI,IAAI,IAAI,CAACvN,SAAS,EAAE;QAC7B,IAAI,CAACuN,IAAI,CAACzK,IAAI,EAAEyK,IAAI,CAACzK,IAAI,GAAG,EAAE;QAC9B,IAAI,CAACyK,IAAI,CAACtK,KAAK,EAAEsK,IAAI,CAACtK,KAAK,GAAG,EAAE;QAEhC,IAAI,CAAC,IAAI,CAACK,kBAAkB,CAACiK,IAAI,CAAC,IAC9B,CAACA,IAAI,CAAClK,IAAI,IACT,IAAI,CAACxD,WAAW,KAAK,UAAU,IAAI0N,IAAI,CAAClK,IAAI,GAAG,GAAI,IACnD,CAAC,IAAI,CAACxD,WAAW,IAAI,EAAE,EAAEC,WAAW,EAAE,CAACC,QAAQ,CAAC,SAAS,CAACD,WAAW,EAAE,CAAC,IAAIyN,IAAI,CAAClK,IAAI,GAAG,EAAG,IAC5FkK,IAAI,CAACzK,IAAI,CAACxE,MAAM,IAAI,EAAE,IACtBiP,IAAI,CAACtK,KAAK,CAAC3E,MAAM,IAAI,GAAG,IACxB,IAAI,CAAC4E,aAAa,CAACqK,IAAI,CAACtK,KAAK,CAAC,EAAE;UAChC,OAAO,IAAI;;;MAGnB,OAAO,KAAK;;IAGhB,IAAI,IAAI,CAACiE,WAAW,IAAI,CAAC,IACrB,IAAI,CAACG,mBAAmB,IACxB,IAAI,CAACA,mBAAmB,IAAI,CAAC,IAC7B,IAAI,CAAC4D,cAAc,GAAG,CAAC,EAAE;MACzB,OAAO,KAAK;;IAGhB,OAAO,IAAI;EACf;EAEFuH,UAAUA,CAACa,GAAU;IACnB,OAAOA,GAAG,CAACC,MAAM,CAAC/F,IAAI,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKgF,SAAS,IAAIhF,IAAI,KAAK,EAAE,CAAC;EAC/E;EAEA5K,UAAUA,CAAC+P,CAAC;IACV,IAAI,CAAClF,oBAAoB,CAAC+F,OAAO,CAAC,IAAI,CAACjZ,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC,EACtF,IAAI,CAACD,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,EAAC;MAC3DsT,EAAE,EAAEA,CAAA,KAAI;QACN,MAAM2F,CAAC,GAAG,IAAI,CAACxT,SAAS,CAAC0S,CAAC,CAAC,CAACrP,IAAI;QAChC,IAAGmQ,CAAC,EAAC;UACH,IAAI,CAACxT,SAAS,CAAC0S,CAAC,CAAC,CAACrP,IAAI,GAAG,IAAI;UAC7B,IAAI,CAACrD,SAAS,CAAC0S,CAAC,CAAC,CAACnP,OAAO,GAAG,IAAI;;QAElC,IAAI,CAACvD,SAAS,GAAG,IAAI,CAACA,SAAS,CAACsT,MAAM,CAAC,CAAC/F,IAAI,EAACyD,KAAK,KAAKA,KAAK,IAAI0B,CAAC,CAAC;QAClE,IAAI,CAACvB,cAAc,EAAE;MACvB;KACD,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;EACF;;EAEAvP,UAAUA,CAAC6R,KAAK,EAAEf,CAAC;IACf,MAAMgB,UAAU,GAAGD,KAAK,CAACE,MAAM,CAACjJ,KAAK;IACrC,IAAI,CAAC1K,SAAS,CAAC0S,CAAC,CAAC,CAACrP,IAAI,GAAGqQ,UAAU;IACnC,IAAI,CAAC1T,SAAS,CAAC0S,CAAC,CAAC,CAACnP,OAAO,GAAGqQ,IAAI,CAACC,KAAK,CAAEH,UAAU,GAAG,IAAI,CAAC5B,YAAY,GAAI,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;IAC1F,IAAI,CAACX,cAAc,EAAE;EACzB;EAEApP,SAASA,CAAC0R,KAAK,EAAEf,CAAC;IAChB,MAAM9H,GAAG,GAAG6I,KAAK,CAAC7I,GAAG;IACrB,MAAMkJ,QAAQ,GAAG,SAAS,CAACxI,IAAI,CAACV,GAAG,CAAC;IAEpC,IAAIA,GAAG,KAAK,GAAG,EAAC;MACZ,OAAO,KAAK;;IAGhB,IAAIpD,KAAK,GAAC,CAAC;IACX,IAAI,CAACxH,SAAS,CAACgO,OAAO,CAAC,CAACT,IAAI,EAACyD,KAAK,KAAI;MACpC,IAAItG,KAAK;MACT,IAAG,CAAC6C,IAAI,CAAClK,IAAI,IAAEkK,IAAI,CAAClK,IAAI,IAAI,IAAI,IAAGkK,IAAI,CAAClK,IAAI,IAAIkP,SAAS,IAAIG,CAAC,IAAI1B,KAAK,EAAC;QACtEtG,KAAK,GAAG,CAAC;OACV,MAAI;QACHA,KAAK,GAAG6C,IAAI,CAAClK,IAAI;;MAEnBmE,KAAK,GAAGwF,MAAM,CAACxF,KAAK,CAAC,GAAGwF,MAAM,CAACtC,KAAK,CAAC;IACvC,CAAC,CAAC;IACF,MAAMgJ,UAAU,GAAGD,KAAK,CAACE,MAAM,CAACjJ,KAAK;IACrC,MAAMqJ,SAAS,GAAGC,QAAQ,CAACN,UAAU,GAAG9I,GAAG,EAAE,EAAE,CAAC;IAChD,IAAIqJ,cAAc,GAAGF,SAAS,GAACvM,KAAK;IACpC,IAAG,IAAI,CAACsK,YAAY,GAAGmC,cAAc,GAAG,CAAC,EAAC;MACxC,IAAI,CAACzG,oBAAoB,CAACW,KAAK,CAAC,IAAI,CAAC7T,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC,CAAC;MAC5F,OAAO,KAAK;;IAEd,OAAO,IAAI;EACb;EAEA2G,cAAcA,CAACuS,KAAK,EAAEf,CAAC;IACrB,MAAMgB,UAAU,GAAGD,KAAK,CAACE,MAAM,CAACjJ,KAAK;IACrC,IAAI,CAAC1K,SAAS,CAAC0S,CAAC,CAAC,CAAC5P,IAAI,GAAG4Q,UAAU;EACrC;EAEAnS,cAAcA,CAACkS,KAAK,EAAEf,CAAC;IACrB,MAAMgB,UAAU,GAAGD,KAAK,CAACE,MAAM,CAACjJ,KAAK;IACrC,IAAI,CAAC1K,SAAS,CAAC0S,CAAC,CAAC,CAACzP,KAAK,GAAGyQ,UAAU;EACtC;EAEAvN,SAASA,CAAA;IACP,IAAI+N,UAAU,GAAG,IAAI,CAAC1C,WAAW,EAAEvC,IAAI,CAACsC,MAAM,IAAIA,MAAM,CAAC5E,OAAO,KAAK,IAAI,CAAC/R,aAAa,CAACC,GAAG,CAAC,aAAa,CAAC,CAAC6P,KAAK,CAAC,EAAEmH,qBAAqB;IACxI,IAAIsC,eAAe,GAAG,CAAC;IACvB,IAAIC,iBAAiB,GAAGF,UAAU,GAAGC,eAAe;IACpD,IAAIE,cAAc,GAAG,IAAI,CAACjP,OAAO,CAACoC,KAAK;IAEvC,IAAI,IAAI,CAACN,WAAW,IAAI,CAAC,EAAE;MACvB,IAAI,IAAI,CAACrH,WAAW,KAAK,UAAU,EAAE;QACjC,IAAIyU,YAAY,GAAGV,IAAI,CAACW,KAAK,CAACH,iBAAiB,GAAGC,cAAc,GAAG,GAAG,CAAC,GAAG,GAAG;QAC7E,IAAKH,UAAU,GAAGI,YAAY,GAAGD,cAAc,GAAI,CAAC,EAAE;UAClD,IAAI,CAAC7G,oBAAoB,CAACW,KAAK,CAAC,6BAA6B,CAAC;SACjE,MAAM;UACH,IAAI,CAAClD,cAAc,GAAG2I,IAAI,CAACW,KAAK,CAACH,iBAAiB,GAAGC,cAAc,GAAG,GAAG,CAAC,GAAG,GAAG;;;MAGxF,IAAI,CAAC,IAAI,CAACxU,WAAW,IAAI,EAAE,EAAEC,WAAW,EAAE,CAACC,QAAQ,CAAC,SAAS,CAACD,WAAW,EAAE,CAAC,EAAE;QAC1E,IAAI,CAACmL,cAAc,GAAG2I,IAAI,CAACW,KAAK,CAACH,iBAAiB,GAAGC,cAAc,GAAG,CAAC,CAAC,GAAG,CAAC;;MAEhF,IAAI,CAAChN,mBAAmB,GAAG+M,iBAAiB,GAAIpH,MAAM,CAAC,IAAI,CAAC/B,cAAc,CAAC,GAAG+B,MAAM,CAACqH,cAAc,CAAE;MACrG,IAAI,CAACtE,iBAAiB,GAAG6D,IAAI,CAACC,KAAK,CAAE,IAAI,CAAC5I,cAAc,GAAGiJ,UAAU,GAAI,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;KAC5F,MAAM;MACH,IAAIM,YAAY,GAAG,IAAI,CAACxU,SAAS,CAAC1B,MAAM;MACxC,IAAI,CAAC0B,SAAS,CAACsT,MAAM,CAACrF,OAAO,IAAIA,OAAO,CAAC9L,MAAM,KAAK,IAAI,CAAC,CAAC6L,OAAO,CAACC,OAAO,IAAG;QACxEkG,eAAe,GAAGnH,MAAM,CAACmH,eAAe,CAAC,GAAGnH,MAAM,CAACiB,OAAO,CAAC5K,IAAI,CAAC;QAChEmR,YAAY,GAAGA,YAAY,GAAG,CAAC;MACnC,CAAC,CAAC;MACF,IAAIJ,iBAAiB,GAAGF,UAAU,GAAGC,eAAe;MACpD,IAAIM,OAAO,GAAGb,IAAI,CAACW,KAAK,CAACH,iBAAiB,GAACI,YAAY,CAAC;MACxD,IAAIE,aAAa,GAAGN,iBAAiB,GAAGI,YAAY;MACpD,IAAI,CAACxU,SAAS,CAACsT,MAAM,CAACrF,OAAO,IAAIA,OAAO,CAAC9L,MAAM,KAAK,KAAK,IAAI,CAAC8L,OAAO,CAAC9L,MAAM,CAAC,CAAC6L,OAAO,CAACC,OAAO,IAAG;QAC5FA,OAAO,CAAC5K,IAAI,GAAGoR,OAAO;QACtBxG,OAAO,CAAC1K,OAAO,GAAGqQ,IAAI,CAACC,KAAK,CAAEY,OAAO,GAAGP,UAAU,GAAI,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;MAC1E,CAAC,CAAC;MACF,IAAI,CAAC5M,eAAe,GAAGoN,aAAa;;EAE1C;EAEAvD,cAAcA,CAAA;IACZ,IAAIwD,aAAa,GAAG,CAAC;IACrB,IAAI,IAAI,CAACzN,WAAW,IAAI,CAAC,EAAE;MACvB,IAAI,CAACG,mBAAmB,GAAG,IAAI,CAACyK,YAAY,IAAI9E,MAAM,CAAC2H,aAAa,CAAC,GAAG3H,MAAM,CAAC,IAAI,CAAC/B,cAAc,CAAC,CAAC;KACvG,MAAM;MACH,IAAI,CAACjL,SAAS,CAACgO,OAAO,CAACT,IAAI,IAAG;QAC1B,IAAI7C,KAAK;QACT,IAAG,CAAC6C,IAAI,CAAClK,IAAI,IAAEkK,IAAI,CAAClK,IAAI,IAAI,IAAI,IAAGkK,IAAI,CAAClK,IAAI,IAAIkP,SAAS,EAAC;UACtD7H,KAAK,GAAG,CAAC;SACZ,MAAI;UACDA,KAAK,GAAG6C,IAAI,CAAClK,IAAI;;QAErBsR,aAAa,GAAG3H,MAAM,CAAC2H,aAAa,CAAC,GAAG3H,MAAM,CAACtC,KAAK,CAAC;MACzD,CAAC,CAAC;MACF,IAAI,CAACpD,eAAe,GAAG,IAAI,CAACwK,YAAY,GAAG6C,aAAa;;EAE9D;EAEArO,UAAUA,CAAA;IACN,IAAI,CAACsC,aAAa,GAAG,IAAI;EAC7B;EAEAgM,iBAAiBA,CAAA;IACb,IAAI,IAAI,CAAC1N,WAAW,IAAI,CAAC,EAAE;MACvB,IAAI,CAAC+D,cAAc,GAAG,IAAI,CAACrQ,aAAa,CAAC6M,QAAQ,CAAC,aAAa,CAAC,CAACiD,KAAK;MACtE,IAAI,CAACqF,iBAAiB,GAAG6D,IAAI,CAACC,KAAK,CAAE,IAAI,CAACjZ,aAAa,CAAC6M,QAAQ,CAAC,aAAa,CAAC,CAACiD,KAAK,GAAG,IAAI,CAACoH,YAAY,GAAI,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;KAChI,MAAM;MACH,IAAI,CAAC9R,SAAS,CAACsT,MAAM,CAACzE,CAAC,IAAIA,CAAC,CAAC1M,MAAM,IAAI,KAAK,IAAI,CAAC0M,CAAC,CAAC1M,MAAM,CAAC,CAAC6L,OAAO,CAACC,OAAO,IAAG;QACzEA,OAAO,CAAC5K,IAAI,GAAG,IAAI,CAACzI,aAAa,CAAC6M,QAAQ,CAAC,aAAa,CAAC,CAACiD,KAAK;QAC/DuD,OAAO,CAAC1K,OAAO,GAAGqQ,IAAI,CAACC,KAAK,CAAE,IAAI,CAACjZ,aAAa,CAAC6M,QAAQ,CAAC,aAAa,CAAC,CAACiD,KAAK,GAAG,IAAI,CAACoH,YAAY,GAAI,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;MAC1H,CAAC,CAAC;;IAEN,IAAI,CAACX,cAAc,EAAE;IACrB,IAAI,CAACvI,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;EAC5C;EAEAnC,SAASA,CAAA;IACP,IAAI,CAAC+G,oBAAoB,CAAC+F,OAAO,CAAC,IAAI,CAACjZ,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC,EAC7F,IAAI,CAACD,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,EAAC;MAC3DsT,EAAE,EAACA,CAAA,KAAK;QACN,IAAI,CAACvG,eAAe,GAAG,IAAI,CAACwK,YAAY;QACtC,IAAI,IAAI,CAAC5K,WAAW,IAAI,CAAC,EAAE;UACvB,IAAI,CAAClH,SAAS,CAACgO,OAAO,CAACT,IAAI,IAAG;YAC1BA,IAAI,CAAClK,IAAI,GAAG,IAAI;YAChBkK,IAAI,CAAChK,OAAO,GAAG,IAAI;UACvB,CAAC,CAAC;SACL,MAAM;UACH,IAAI,CAAC+B,MAAM,CAAC,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,UAAU,CAAC;UACvE,IAAI,CAACuF,cAAc,GAAG,CAAC;UACvB,IAAI,CAAC8E,iBAAiB,GAAG,CAAC;;MAElC;KACD,CAAC;EACJ;EAEA3E,cAAcA,CAAA;IACV,IAAI,CAACyJ,QAAQ,GAAGC,WAAW,CAAC,MAAK;MAC7B,IAAI,IAAI,CAAC5M,SAAS,GAAG,CAAC,EAAE;QACpB,IAAI,CAACA,SAAS,EAAE;OACnB,MAAM;QACH6M,aAAa,CAAC,IAAI,CAACF,QAAQ,CAAC;;IAEpC,CAAC,EAAE,IAAI,CAAC;EACZ;EAEAG,UAAUA,CAAA;IACN,IAAI,CAAC9M,SAAS,GAAG,EAAE;IACnB6M,aAAa,CAAC,IAAI,CAACF,QAAQ,CAAC;IAC5B,IAAI,CAACzJ,cAAc,EAAE;IACrB,IAAI,CAAC6J,OAAO,EAAE;EAChB;EAEFC,YAAYA,CAAA;IACV,IAAI,CAACta,aAAa,CAAC6M,QAAQ,CAAC,aAAa,CAAC,CAAC0N,KAAK,EAAE;EACpD;EAEApO,eAAeA,CAAA;IACX,IAAI,IAAI,CAACmE,WAAW,EAAC;MACjB,IAAI,CAACsB,UAAU,EAAE;KACpB,MAAI;MACD,IAAI,CAACyI,OAAO,EAAE;;EAEtB;EAEE;EACAA,OAAOA,CAAA;IACH,IAAIxI,EAAE,GAAG,IAAI;IACb,IAAI2I,IAAI,GAAG;MACPC,WAAW,EAAE,IAAI,CAACzI,cAAc,EAAE0I,WAAW,EAAEC,OAAO,CAAC,IAAI,EAAE,IAAI;KACpE;IACD9I,EAAE,CAACe,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI,CAACpF,YAAY,CAAC4M,OAAO,CAACG,IAAI,EAAGzH,GAAG,IAAI;MACtC,IAAIA,GAAG,CAAC6H,SAAS,KAAK,cAAc,EAAE;QAClC/I,EAAE,CAACtB,UAAU,EAAE;;IAErB,CAAC,EAAE,IAAI,EAAE,MAAK;MACVsB,EAAE,CAACe,oBAAoB,CAACmB,OAAO,EAAE;IACrC,CAAC,CAAC;EACR;EAEA8G,gBAAgBA,CAAChC,KAAK;IACpB,IAAIe,YAAY,GAAG,IAAI;IACvB,IAAI,IAAI,CAACtN,WAAW,IAAI,CAAC,EAAE;MACvBsN,YAAY,GAAG,IAAI,CAACxU,SAAS,CAAC1B,MAAM;MACpC,IAAI,CAAC0B,SAAS,CAACsT,MAAM,CAACrF,OAAO,IAAIA,OAAO,CAAC9L,MAAM,KAAK,IAAI,CAAC,CAAC6L,OAAO,CAACC,OAAO,IAAG;QACxEuG,YAAY,GAAGA,YAAY,GAAG,CAAC;MACnC,CAAC,CAAC;KACL,MAAM;MACHA,YAAY,GAAG,IAAI,CAACpP,OAAO,CAACoC,KAAK;;IAIrC,IAAI,IAAI,CAAC5M,aAAa,CAAC6M,QAAQ,CAAC,aAAa,CAAC,CAACiD,KAAK,GAAG8J,YAAY,GAAE,IAAI,CAAC1C,YAAY,EAAC;MACrF,IAAI,CAACrI,kBAAkB,GAAG,IAAI;MAC9B;KACD,MAAI;MACH,IAAI,CAACA,kBAAkB,GAAG,KAAK;;IAEjC,IAAI,IAAI,CAAC5J,WAAW,KAAK,UAAU,EAAE;MACnC,IAAI,IAAI,CAACjF,aAAa,CAAC6M,QAAQ,CAAC,aAAa,CAAC,CAACiD,KAAK,GAAG,GAAG,KAAK,CAAC,EAAE;QAChE,IAAI,CAACb,gBAAgB,GAAG,IAAI;OAC7B,MAAM;QACL,IAAI,CAACA,gBAAgB,GAAG,KAAK;;;IAG/B,IAAG,CAAC,IAAI,CAAChK,WAAW,IAAI,EAAE,EAAEC,WAAW,EAAE,CAACC,QAAQ,CAAC,SAAS,CAACD,WAAW,EAAE,CAAC,EAAC;MAC5E,IAAI,IAAI,CAAClF,aAAa,CAAC6M,QAAQ,CAAC,aAAa,CAAC,CAACiD,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;QAC9D,IAAI,CAACb,gBAAgB,GAAG,IAAI;OAC7B,MAAM;QACL,IAAI,CAACA,gBAAgB,GAAG,KAAK;;;EAGnC;EAEE6L,YAAYA,CAAA;IACV,IAAI,CAAChM,wBAAwB,GAAG,KAAK;EACvC;EAEA7L,YAAYA,CAAA;IACR,IAAI4O,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC/C,wBAAwB,GAAG,IAAI;IAClC+C,EAAE,CAAC6D,iBAAiB,CAACI,IAAI,CAAChX,SAAS,CAAC8W,UAAU,CAACmF,mBAAmB,EAAE,EAAE,CAAC;IACvElJ,EAAE,CAAC7C,iBAAiB,GAAG,KAAK;EAChC;EAEAtG,kBAAkBA,CAACiK,IAAS;IACxB,IAAI,IAAI,CAAC1N,WAAW,KAAK,UAAU,EAAE;MACjC,OAAO0N,IAAI,CAAClK,IAAI,GAAG,GAAG,KAAK,CAAC;;IAEhC,IAAI,CAAC,IAAI,CAACxD,WAAW,IAAI,EAAE,EAAEC,WAAW,EAAE,CAACC,QAAQ,CAAC,SAAS,CAACD,WAAW,EAAE,CAAC,EAAE;MAC1E,OAAOyN,IAAI,CAAClK,IAAI,GAAG,CAAC,KAAK,CAAC;;IAE9B,OAAO,IAAI;EACf;EAEAuS,iBAAiBA,CAAA;IACb,IAAI,CAAChM,iBAAiB,GAAG,KAAK;EAClC;EAEAiM,WAAWA,CAAA;IACPd,aAAa,CAAC,IAAI,CAACF,QAAQ,CAAC;EAChC;EAEAiB,gBAAgBA,CAAA;IACZ,IAAI,CAACzN,YAAY,CAAC0N,2BAA2B,EAAE;EACnD;EAEAhZ,aAAaA,CAACiZ,KAAK;IACf,IAAIvJ,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC1D,eAAe,GAAGiN,KAAK,CAACC,EAAE;IAC7BxJ,EAAE,CAACnH,MAAM,CAACmH,EAAE,CAAClH,UAAU,EAAEkH,EAAE,CAACjH,QAAQ,EAAEiH,EAAE,CAAChH,IAAI,EAAEgH,EAAE,CAAC/G,UAAU,CAAC;IAC7D+G,EAAE,CAACpF,mBAAmB,GAAGoF,EAAE,CAAC+E,WAAW,EAAEvC,IAAI,CAACsC,MAAM,IAAIA,MAAM,CAAC5E,OAAO,KAAKF,EAAE,CAAC7R,aAAa,CAACC,GAAG,CAAC,aAAa,CAAC,CAAC6P,KAAK,CAAC,EAAEmH,qBAAqB;IAC5IpF,EAAE,CAACxB,cAAc,GAAG,CAAC;IACrBwB,EAAE,CAACsD,iBAAiB,GAAG,CAAC;EAC5B;EAEAzK,MAAMA,CAAC4Q,IAAI,EAAEC,KAAK,EAAE1Q,IAAI,EAAEsM,MAAM;IAC5B,IAAI,CAACxM,UAAU,GAAG2Q,IAAI;IACtB,IAAI,CAAC1Q,QAAQ,GAAG2Q,KAAK;IACrB,IAAI,CAAC1Q,IAAI,GAAGA,IAAI;IAChB,IAAIgH,EAAE,GAAG,IAAI;IACb,IAAIkI,aAAa,GAAG,CAAC;IACrB,IAAIyB,UAAU,GAAG;MACb,GAAGrE,MAAM;MACTmE,IAAI;MACJrL,IAAI,EAAEsL,KAAK;MACX1Q;KACH;IACDgH,EAAE,CAACe,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI,CAAClF,eAAe,CAAC8N,aAAa,CAACrJ,MAAM,CAACP,EAAE,CAAC1D,eAAe,CAAC,EAAEqN,UAAU,EAAGnE,QAAQ,IAAG;MACnFxF,EAAE,CAACrH,OAAO,GAAG;QACToF,OAAO,EAAEyH,QAAQ,CAACzH,OAAO;QACzBhD,KAAK,EAAEyK,QAAQ,CAACG;OACnB;MACD;MACAuC,aAAa,GAAG3H,MAAM,CAAC2H,aAAa,CAAC,GAAG3H,MAAM,CAACP,EAAE,CAACxB,cAAc,GAAGgH,QAAQ,CAACG,aAAa,CAAC;MAC1F,IAAI,CAAC9K,eAAe,GAAG,IAAI,CAACwK,YAAY,GAAG6C,aAAa;MACxD,IAAI,CAACtN,mBAAmB,GAAG,IAAI,CAACyK,YAAY,GAAG6C,aAAa;IAChE,CAAC,EAAE,IAAI,EAAE,MAAI;MACTlI,EAAE,CAACe,oBAAoB,CAACmB,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAGAzR,kBAAkBA,CAAA;IACd,IAAI,CAAC2H,kBAAkB,GAAG,EAAE;EAChC;EAEAyR,iBAAiBA,CAAA;IACb,IAAI,CAAClY,iBAAiB,GAAG,EAAE;EAC/B;EAEAgB,gBAAgBA,CAAA;IACZ,IAAG,CAAC,IAAI,CAACQ,YAAY,EAAC;MAClB,IAAI,CAAC4N,oBAAoB,CAAC4D,OAAO,CAAC,IAAI,CAAC9W,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC,CAAC;;EAEhG;EAEAgc,WAAWA,CAAA;IACPC,MAAM,CAACC,QAAQ,CAAC7D,MAAM,EAAE;EAC5B;EAEA8D,iBAAiBA,CAAA;IACb,IAAI,CAACC,aAAa,CAAC,IAAI,CAACrI,gBAAgB,CAAC9D,OAAO,CAAC;EACrD;EAEAmM,aAAaA,CAACtT,IAAI;IACd;IACA,MAAMuT,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC;IACtC,MAAMC,SAAS,GAAGxT,IAAI,CAACiK,GAAG,CAAC,CAACC,IAAI,EAAEyD,KAAK,KAAK,CAACA,KAAK,GAAC,CAAC,EAAEzD,IAAI,CAAC1K,YAAY,EAAE0K,IAAI,CAAC3F,WAAW,CAAC,CAAC;IAE3F;IACA,MAAMkP,EAAE,GAAmBld,IAAI,CAACmd,KAAK,CAACC,YAAY,CAAC,CAACJ,MAAM,EAAE,GAAGC,SAAS,CAAC,CAAC;IAE1EC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC;MAACG,GAAG,EAAE;IAAC,CAAC,EAAE;MAACA,GAAG,EAAE;IAAE,CAAC,EAAE;MAACA,GAAG,EAAC;IAAE,CAAC,CAAC;IAE7C;IACA,MAAMC,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IACxCA,WAAW,CAAClJ,OAAO,CAACmJ,IAAI,IAAG;MACvB,IAAIL,EAAE,CAACK,IAAI,CAAC,EAAE;QACVL,EAAE,CAACK,IAAI,CAAC,CAACC,CAAC,GAAG;UACTC,IAAI,EAAE;YACFC,IAAI,EAAE,IAAI,CAAE;WACf;;UACDC,SAAS,EAAE;YACPC,UAAU,EAAE,QAAQ;YACpBC,QAAQ,EAAE,QAAQ,CAAE;;SAE3B;;IAET,CAAC,CAAC;IAEF;IACA,MAAMC,QAAQ,GAAGrU,IAAI,CAAC/E,MAAM;IAC5B,KAAK,IAAIqZ,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAID,QAAQ,GAAG,CAAC,EAAEC,GAAG,EAAE,EAAE;MAC1C,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGhB,MAAM,CAACtY,MAAM,EAAEsZ,GAAG,EAAE,EAAE;QAC1C,MAAMC,OAAO,GAAGje,IAAI,CAACmd,KAAK,CAACe,WAAW,CAAC;UAAEC,CAAC,EAAEJ,GAAG,GAAG,CAAC;UAAEK,CAAC,EAAEJ;QAAG,CAAE,CAAC;QAC9D,IAAId,EAAE,CAACe,OAAO,CAAC,EAAE;UACbf,EAAE,CAACe,OAAO,CAAC,CAACT,CAAC,GAAG;YACZG,SAAS,EAAE;cACPC,UAAU,EAAE,QAAQ;cACpBC,QAAQ,EAAE,QAAQ,CAAE;;WAE3B;;;;IAKb;IACA,MAAMQ,EAAE,GAAkB;MACtBC,MAAM,EAAE;QAAE,2BAA2B,EAAEpB;MAAE,CAAE;MAC3CqB,UAAU,EAAE,CAAC,2BAA2B;KAC3C;IAED,MAAMC,WAAW,GAAQxe,IAAI,CAACye,KAAK,CAACJ,EAAE,EAAE;MAAEK,QAAQ,EAAE,MAAM;MAAElL,IAAI,EAAE;IAAO,CAAE,CAAC;IAC5E,IAAI,CAACmL,eAAe,CAACH,WAAW,EAAE,gBAAgB,CAAC;EACvD;EAEQG,eAAeA,CAACC,MAAW,EAAEC,QAAgB;IACjD,MAAMpV,IAAI,GAAS,IAAIqV,IAAI,CAAC,CAACF,MAAM,CAAC,EAAE;MAAEpL,IAAI,EAAEuL;IAAU,CAAE,CAAC;IAC3D9e,SAAS,CAAC+e,MAAM,CAACvV,IAAI,EAAEoV,QAAQ,GAAG,GAAG,GAAGI,0BAA0B,CAAC,IAAI1P,IAAI,EAAE,CAAC2P,OAAO,EAAE,CAAC,GAAGC,eAAe,CAAC;EAC/G;EAEAC,eAAeA,CAACzT,UAAU,EAAEC,QAAQ;IAChC,MAAMyT,UAAU,GAAG1T,UAAU,GAAGC,QAAQ;IACxC,MAAM0T,QAAQ,GAAGD,UAAU,GAAGzT,QAAQ;IACtC,IAAI,CAAC+E,YAAY,CAACC,OAAO,GAAG,IAAI,CAAC8D,gBAAgB,CAAC9D,OAAO,CAAC2O,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;IACrF9J,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC9E,YAAY,CAAC;IAC9B,IAAI,CAACA,YAAY,GAAG;MAAC,GAAG,IAAI,CAACA;IAAY,CAAC;EAC9C;EAEApG,UAAUA,CAACsP,KAAK;IACZ,IAAI,CAAC2F,gBAAgB,CAACC,KAAK,GAAG5F,KAAK,CAAC4F,KAAK;IACzC,IAAI,CAACD,gBAAgB,CAACE,IAAI,GAAG7F,KAAK,CAAC6F,IAAI;IACvC,IAAI,CAAC1e,aAAa,CAAC6M,QAAQ,CAAC,aAAa,CAAC,CAAC8R,QAAQ,CAAC9F,KAAK,CAAC4F,KAAK,GAAC5F,KAAK,CAAC6F,IAAI,GAAG,CAAC,CAAC;EACnF;EAEA3V,QAAQA,CAAA;IACJ,IAAI6F,WAAW,GAAG,IAAI,CAAC5O,aAAa,CAAC6M,QAAQ,CAAC+B,WAAW,CAACkB,KAAK;IAC/D,MAAMyH,UAAU,GAAGyB,IAAI,CAAC4F,IAAI,CAAC,IAAI,CAACxZ,SAAS,CAAC1B,MAAM,GAAG,IAAI,CAAC8a,gBAAgB,CAACE,IAAI,CAAC;IAChF,IAAI9P,WAAW,GAAG,CAAC,IAAIA,WAAW,GAAG2I,UAAU,EAAE;MAC7C;;IAEJ,IAAI,CAACiH,gBAAgB,CAACC,KAAK,GAAG,CAAC7P,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC4P,gBAAgB,CAACE,IAAI;EAChF;EAEAzV,UAAUA,CAAA;IACN,IAAG,IAAI,CAAC7D,SAAS,CAAC1B,MAAM,GAAG,IAAI,CAAC8a,gBAAgB,CAACE,IAAI,IAAI,CAAC,EAAC;MACvD,OAAO,IAAI,CAACtZ,SAAS,CAAC1B,MAAM,GAAC,IAAI,CAAC8a,gBAAgB,CAACE,IAAI;KAC1D,MAAI;MACD,OAAO1F,IAAI,CAAC4F,IAAI,CAAC,IAAI,CAACxZ,SAAS,CAAC1B,MAAM,GAAC,IAAI,CAAC8a,gBAAgB,CAACE,IAAI,CAAC;;EAE1E;EAEAlS,YAAYA,CAACsD,KAAa;IACtB,OAAO,IAAI+O,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CAACjP,KAAK,CAAC;EACvD;;;uBAljCSvC,kBAAkB,EAAApO,EAAA,CAAA6f,iBAAA,CAoBRtgB,sBAAsB,GAAAS,EAAA,CAAA6f,iBAAA,CACtBpgB,oBAAoB,GAAAO,EAAA,CAAA6f,iBAAA,CACpBjgB,qBAAqB,GAAAI,EAAA,CAAA6f,iBAAA,CAAA7f,EAAA,CAAA8f,QAAA;IAAA;EAAA;;;YAtB/B1R,kBAAkB;MAAA2R,SAAA;MAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCxB/BlgB,EAAA,CAAAC,cAAA,aAAqG;UAEzDD,EAAA,CAAAE,MAAA,GAA0D;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACpGH,EAAA,CAAA0F,SAAA,sBAAoF;UACxF1F,EAAA,CAAAG,YAAA,EAAM;UAWVH,EAAA,CAAAC,cAAA,0BAQC;UAHGD,EAAA,CAAAuB,UAAA,0BAAA6e,qEAAA;YAAA,OAAgBD,GAAA,CAAAxE,YAAA,EAAc;UAAA,EAAC;UAGlC3b,EAAA,CAAAG,YAAA,EAAmB;UAEpBH,EAAA,CAAAC,cAAA,cAA4F;UAAhDD,EAAA,CAAAuB,UAAA,oBAAA8e,mDAAA;YAAA,OAAUF,GAAA,CAAA1N,UAAA,EAAY;UAAA,EAAC;UAC/DzS,EAAA,CAAAC,cAAA,aAAkB;UAEqBD,EAAA,CAAAE,MAAA,IAA6D;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAClGH,EAAA,CAAAC,cAAA,cAAgD;UAEZD,EAAA,CAAAE,MAAA,IAAsD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1FH,EAAA,CAAAC,cAAA,sBAO0B;UAFdD,EAAA,CAAAuB,UAAA,sBAAA+e,4DAAA;YAAA,OAAYH,GAAA,CAAA7I,YAAA,EAAc;UAAA,EAAC;UAGvCtX,EAAA,CAAAG,YAAA,EAAa;UAEjBH,EAAA,CAAAC,cAAA,cAA2C;UACXD,EAAA,CAAAE,MAAA,IAAqD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzFH,EAAA,CAAA0F,SAAA,sBAMc;UAClB1F,EAAA,CAAAG,YAAA,EAAM;UAEVH,EAAA,CAAAC,cAAA,eAA+C;UAC3CD,EAAA,CAAAS,UAAA,KAAA8f,kCAAA,kBAEM;UACVvgB,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAAgD;UAEXD,EAAA,CAAAE,MAAA,IAAuD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5FH,EAAA,CAAA0F,SAAA,oBAOY;UAChB1F,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAiD;UAMzCD,EAAA,CAAAuB,UAAA,2BAAAif,oEAAA/e,MAAA;YAAA,OAAA0e,GAAA,CAAAhT,WAAA,GAAA1L,MAAA;UAAA,EAAyB,qBAAAgf,8DAAA;YAAA,OAEdN,GAAA,CAAAhd,kBAAA,EAAoB;UAAA,EAFN;UAG7BnD,EAAA,CAAAG,YAAA,EAAgB;UAChBH,EAAA,CAAAC,cAAA,yBAQoC;UAFhCD,EAAA,CAAAuB,UAAA,2BAAAmf,oEAAAjf,MAAA;YAAA,OAAA0e,GAAA,CAAAhT,WAAA,GAAA1L,MAAA;UAAA,EAAyB,qBAAAkf,8DAAA;YAAA,OAEdR,GAAA,CAAA5D,iBAAA,EAAmB;UAAA,EAFL;UAG7Bvc,EAAA,CAAAG,YAAA,EAAgB;UAGxBH,EAAA,CAAAC,cAAA,eAAkD;UAC9CD,EAAA,CAAAS,UAAA,KAAAmgB,kCAAA,kBAEM;UACV5gB,EAAA,CAAAG,YAAA,EAAM;UAIdH,EAAA,CAAAS,UAAA,KAAAogB,kCAAA,oBAiLM;UAEN7gB,EAAA,CAAAC,cAAA,oBAAuN;UAA7MD,EAAA,CAAAuB,UAAA,oBAAAuf,wDAAA;YAAA,OAAUX,GAAA,CAAAhF,YAAA,EAAc;UAAA,EAAC,2BAAA4F,+DAAAtf,MAAA;YAAA,OAAA0e,GAAA,CAAAtR,aAAA,GAAApN,MAAA;UAAA;UAC/BzB,EAAA,CAAAC,cAAA,cAA2C;UACrBD,EAAA,CAAAE,MAAA,IAA4E;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtGH,EAAA,CAAAC,cAAA,iBAAiG;UAA5ED,EAAA,CAAAuB,UAAA,mBAAAyf,oDAAAvf,MAAA;YAAA,OAAS0e,GAAA,CAAAzE,gBAAA,CAAAja,MAAA,CAAwB;UAAA,EAAC;UAAvDzB,EAAA,CAAAG,YAAA,EAAiG;UACjGH,EAAA,CAAAS,UAAA,KAAAwgB,kCAAA,kBAEM;UACNjhB,EAAA,CAAAS,UAAA,KAAAygB,kCAAA,kBAEM;UACNlhB,EAAA,CAAAS,UAAA,KAAA0gB,kCAAA,kBAAqH;UACrHnhB,EAAA,CAAAC,cAAA,eAAoB;UACgeD,EAAA,CAAAuB,UAAA,mBAAA6f,qDAAA;YAAA,OAASjB,GAAA,CAAAtF,iBAAA,EAAmB;UAAA,EAAC;UAAC7a,EAAA,CAAAE,MAAA,IAAkD;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzkBH,EAAA,CAAAC,cAAA,kBAAoG;UAAhCD,EAAA,CAAAuB,UAAA,mBAAA8f,qDAAA;YAAA,OAAAlB,GAAA,CAAAtR,aAAA,GAAyB,KAAK;UAAA,EAAC;UAAC7O,EAAA,CAAAE,MAAA,IAAiD;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAK1KH,EAAA,CAAAC,cAAA,oBAA+K;UAA1GD,EAAA,CAAAuB,UAAA,2BAAA+f,+DAAA7f,MAAA;YAAA,OAAA0e,GAAA,CAAArR,QAAA,GAAArN,MAAA;UAAA,EAAsB;UACvFzB,EAAA,CAAAC,cAAA,cAA2C;UACvCD,EAAA,CAAA0F,SAAA,sBAAoG;UACpG1F,EAAA,CAAAC,cAAA,kBAKC;UADGD,EAAA,CAAAuB,UAAA,mBAAAggB,qDAAA;YAAA,OAASpB,GAAA,CAAAlF,UAAA,EAAY;UAAA,EAAC;UACzBjb,EAAA,CAAAE,MAAA,IAA6D;UAAAF,EAAA,CAAAS,UAAA,KAAA+gB,kCAAA,kBAAiJ;UAC/MxhB,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,eAAoB;UACuDD,EAAA,CAAAE,MAAA,IAA+C;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC/HH,EAAA,CAAAC,cAAA,kBAAiF;UAA3BD,EAAA,CAAAuB,UAAA,mBAAAkgB,qDAAA;YAAA,OAAAtB,GAAA,CAAArR,QAAA,GAAoB,KAAK;UAAA,EAAC;UAAC9O,EAAA,CAAAE,MAAA,IAAiD;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKvJH,EAAA,CAAAC,cAAA,oBAAgO;UAAlID,EAAA,CAAAuB,UAAA,2BAAAmgB,+DAAAjgB,MAAA;YAAA,OAAA0e,GAAA,CAAApR,OAAA,GAAAtN,MAAA;UAAA,EAAqB,oBAAAkgB,wDAAA;YAAA,OAA8FxB,GAAA,CAAA3D,WAAA,EAAa;UAAA,EAA3G;UAC/Gxc,EAAA,CAAAC,cAAA,oBAGgE;UAAhCD,EAAA,CAAAuB,UAAA,qBAAAqgB,yDAAA;YAAA,OAAWzB,GAAA,CAAAxD,iBAAA,EAAmB;UAAA,EAAC;UAAC3c,EAAA,CAAAG,YAAA,EAAW;UAC3EH,EAAA,CAAA0F,SAAA,sBAYc;UAClB1F,EAAA,CAAAG,YAAA,EAAW;;;UA3U6BH,EAAA,CAAAI,SAAA,GAA0D;UAA1DJ,EAAA,CAAAK,iBAAA,CAAA8f,GAAA,CAAA5f,WAAA,CAAAC,SAAA,6BAA0D;UACvDR,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAW,UAAA,UAAAwf,GAAA,CAAAlK,KAAA,CAAe,SAAAkK,GAAA,CAAA/J,IAAA;UAa1DpW,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAW,UAAA,cAAAwf,GAAA,CAAAla,SAAA,CAAuB,oBAAAka,GAAA,CAAA5S,eAAA,8BAAA4S,GAAA,CAAAxQ,wBAAA,uBAAAwQ,GAAA,CAAAtQ,iBAAA,iBAAAsQ,GAAA,CAAApI,YAAA,iBAAAoI,GAAA,CAAAra,WAAA;UASX9F,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAW,UAAA,cAAAwf,GAAA,CAAAtf,aAAA,CAA2B;UAGAb,EAAA,CAAAI,SAAA,GAA6D;UAA7DJ,EAAA,CAAAK,iBAAA,CAAA8f,GAAA,CAAA5f,WAAA,CAAAC,SAAA,qCAA6D;UAGxDR,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAK,iBAAA,CAAA8f,GAAA,CAAA5f,WAAA,CAAAC,SAAA,8BAAsD;UACtER,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAW,UAAA,YAAAwf,GAAA,CAAA1I,WAAA,CAAuB,gBAAA0I,GAAA,CAAA5f,WAAA,CAAAC,SAAA,uDAAA2f,GAAA,CAAA5f,WAAA,CAAAC,SAAA;UAWPR,EAAA,CAAAI,SAAA,GAAqD;UAArDJ,EAAA,CAAAK,iBAAA,CAAA8f,GAAA,CAAA5f,WAAA,CAAAC,SAAA,6BAAqD;UAK7ER,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAW,UAAA,kBAAiB;UAMnBX,EAAA,CAAAI,SAAA,GAAwF;UAAxFJ,EAAA,CAAAW,UAAA,SAAAwf,GAAA,CAAAtf,aAAA,CAAAC,GAAA,gBAAA+gB,OAAA,IAAA1B,GAAA,CAAAtf,aAAA,CAAAC,GAAA,gBAAAghB,KAAA,CAAwF;UAM7D9hB,EAAA,CAAAI,SAAA,GAAuD;UAAvDJ,EAAA,CAAAK,iBAAA,CAAA8f,GAAA,CAAA5f,WAAA,CAAAC,SAAA,+BAAuD;UAIhFR,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAW,UAAA,qBAAoB,gBAAAwf,GAAA,CAAA5f,WAAA,CAAAC,SAAA;UASpBR,EAAA,CAAAI,SAAA,GAA6D;UAA7DJ,EAAA,CAAAW,UAAA,UAAAwf,GAAA,CAAA5f,WAAA,CAAAC,SAAA,+BAA6D,YAAA2f,GAAA,CAAAhT,WAAA;UAS7DnN,EAAA,CAAAI,SAAA,GAA8D;UAA9DJ,EAAA,CAAAW,UAAA,UAAAwf,GAAA,CAAA5f,WAAA,CAAAC,SAAA,gCAA8D,YAAA2f,GAAA,CAAAhT,WAAA;UAYhEnN,EAAA,CAAAI,SAAA,GAAwF;UAAxFJ,EAAA,CAAAW,UAAA,SAAAwf,GAAA,CAAAtf,aAAA,CAAAC,GAAA,gBAAA+gB,OAAA,IAAA1B,GAAA,CAAAtf,aAAA,CAAAC,GAAA,gBAAAghB,KAAA,CAAwF;UAO5E9hB,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAW,UAAA,SAAAwf,GAAA,CAAAvR,aAAA,CAAmB;UAmLkG5O,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAA+hB,UAAA,CAAA/hB,EAAA,CAAAkB,eAAA,KAAA8gB,IAAA,EAA2B;UAA1IhiB,EAAA,CAAAW,UAAA,WAAAwf,GAAA,CAAA5f,WAAA,CAAAC,SAAA,oCAAmE,YAAA2f,GAAA,CAAAtR,aAAA;UAE7E7O,EAAA,CAAAI,SAAA,GAA4E;UAA5EJ,EAAA,CAAAK,iBAAA,CAAA8f,GAAA,CAAA5f,WAAA,CAAAC,SAAA,8BAAAR,EAAA,CAAAoJ,eAAA,KAAA6Y,IAAA,EAAA9B,GAAA,CAAApQ,YAAA,GAA4E;UAEnE/P,EAAA,CAAAI,SAAA,GAAkM;UAAlMJ,EAAA,CAAAW,UAAA,UAAAwf,GAAA,CAAAra,WAAA,QAAAC,WAAA,GAAAC,QAAA,gBAAAD,WAAA,QAAAoa,GAAA,CAAAtf,aAAA,CAAA6M,QAAA,gBAAAiD,KAAA,SAAAwP,GAAA,CAAArQ,gBAAA,KAAAqQ,GAAA,CAAAtf,aAAA,CAAA6M,QAAA,gBAAAoU,KAAA,CAAkM;UAGlM9hB,EAAA,CAAAI,SAAA,GAAyJ;UAAzJJ,EAAA,CAAAW,UAAA,SAAAwf,GAAA,CAAAra,WAAA,wBAAAqa,GAAA,CAAAtf,aAAA,CAAA6M,QAAA,gBAAAiD,KAAA,UAAAwP,GAAA,CAAArQ,gBAAA,KAAAqQ,GAAA,CAAAtf,aAAA,CAAA6M,QAAA,gBAAAoU,KAAA,CAAyJ;UAGzJ9hB,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAW,UAAA,SAAAwf,GAAA,CAAAzQ,kBAAA,CAAwB;UAEnB1P,EAAA,CAAAI,SAAA,GAA6b;UAA7bJ,EAAA,CAAAW,UAAA,cAAAwf,GAAA,CAAAra,WAAA,QAAAC,WAAA,GAAAC,QAAA,gBAAAD,WAAA,OAAAoa,GAAA,CAAAtf,aAAA,CAAA6M,QAAA,gBAAAiD,KAAA,SAAAwP,GAAA,CAAArQ,gBAAA,IAAAqQ,GAAA,CAAAra,WAAA,uBAAAqa,GAAA,CAAAtf,aAAA,CAAA6M,QAAA,gBAAAiD,KAAA,UAAAwP,GAAA,CAAAzQ,kBAAA,IAAAyQ,GAAA,CAAAtf,aAAA,CAAA6M,QAAA,gBAAAiD,KAAA,UAAAwP,GAAA,CAAAtf,aAAA,CAAA6M,QAAA,gBAAAiD,KAAA,YAAAwP,GAAA,CAAAtf,aAAA,CAAA6M,QAAA,gBAAAiD,KAAA,IAAA6H,SAAA,IAAA2H,GAAA,CAAArQ,gBAAA,CAA6b;UAAqD9P,EAAA,CAAAI,SAAA,GAAkD;UAAlDJ,EAAA,CAAAK,iBAAA,CAAA8f,GAAA,CAAA5f,WAAA,CAAAC,SAAA,0BAAkD;UAC5dR,EAAA,CAAAI,SAAA,GAAiD;UAAjDJ,EAAA,CAAAK,iBAAA,CAAA8f,GAAA,CAAA5f,WAAA,CAAAC,SAAA,yBAAiD;UAKtDR,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAA+hB,UAAA,CAAA/hB,EAAA,CAAAkB,eAAA,KAAAghB,IAAA,EAA2B;UAA5HliB,EAAA,CAAAW,UAAA,WAAAwf,GAAA,CAAA5f,WAAA,CAAAC,SAAA,2BAA0D,YAAA2f,GAAA,CAAArR,QAAA;UAEL9O,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAW,UAAA,qBAAoB;UAIhCX,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAW,UAAA,aAAAwf,GAAA,CAAAhS,SAAA,KAAwB;UAElEnO,EAAA,CAAAI,SAAA,GAA6D;UAA7DJ,EAAA,CAAAgE,kBAAA,KAAAmc,GAAA,CAAA5f,WAAA,CAAAC,SAAA,yCAA6D;UAAMR,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAW,UAAA,SAAAwf,GAAA,CAAAhS,SAAA,KAAiB;UAGzEnO,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAAW,UAAA,aAAAwf,GAAA,CAAAtf,aAAA,CAAAghB,OAAA,CAAkC;UAA6B7hB,EAAA,CAAAI,SAAA,GAA+C;UAA/CJ,EAAA,CAAAK,iBAAA,CAAA8f,GAAA,CAAA5f,WAAA,CAAAC,SAAA,uBAA+C;UACrCR,EAAA,CAAAI,SAAA,GAAiD;UAAjDJ,EAAA,CAAAK,iBAAA,CAAA8f,GAAA,CAAA5f,WAAA,CAAAC,SAAA,yBAAiD;UAKXR,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAA+hB,UAAA,CAAA/hB,EAAA,CAAAkB,eAAA,KAAAihB,IAAA,EAA2B;UAAlIniB,EAAA,CAAAW,UAAA,WAAAwf,GAAA,CAAA5f,WAAA,CAAAC,SAAA,kCAAiE,YAAA2f,GAAA,CAAApR,OAAA;UAG/E/O,EAAA,CAAAI,SAAA,GAAsE;UAAtEJ,EAAA,CAAAW,UAAA,aAAAwf,GAAA,CAAA5f,WAAA,CAAAC,SAAA,qCAAsE;UAG5ER,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAW,UAAA,gCAA+B,6BAAAwf,GAAA,CAAAvP,YAAA,aAAAuP,GAAA,CAAA3P,YAAA,aAAA2P,GAAA,CAAAnQ,gBAAA,gBAAAmQ,GAAA,CAAA9P,eAAA,cAAA8P,GAAA,CAAA7P,aAAA,UAAA6P,GAAA,CAAA5P,SAAA,YAAA4P,GAAA,CAAAzP,eAAA,cAAAyP,GAAA,CAAAlB,eAAA,CAAAxc,IAAA,CAAA0d,GAAA;;;;;;;;AD4wB3C,MAAMvB,UAAU,GAAG,iFAAiF;AACpG,MAAMI,eAAe,GAAG,OAAO;AAE/B,SAASF,0BAA0BA,CAACsD,SAAiB;EACjD,MAAMC,IAAI,GAAG,IAAIjT,IAAI,CAACgT,SAAS,CAAC;EAEhC,MAAME,EAAE,GAAGtN,MAAM,CAACqN,IAAI,CAACvQ,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAClD,MAAM2Q,EAAE,GAAGvN,MAAM,CAACqN,IAAI,CAACrQ,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACzD,MAAM4Q,IAAI,GAAGH,IAAI,CAACnQ,WAAW,EAAE;EAC/B,MAAMuQ,EAAE,GAAGzN,MAAM,CAACqN,IAAI,CAACjQ,QAAQ,EAAE,CAAC,CAACR,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EACnD,MAAM8Q,EAAE,GAAG1N,MAAM,CAACqN,IAAI,CAAC/P,UAAU,EAAE,CAAC,CAACV,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EACrD,MAAM+Q,EAAE,GAAG3N,MAAM,CAACqN,IAAI,CAAC7P,UAAU,EAAE,CAAC,CAACZ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAErD,OAAO,GAAG0Q,EAAE,GAAGC,EAAE,GAAGC,IAAI,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,EAAE;AAC7C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}