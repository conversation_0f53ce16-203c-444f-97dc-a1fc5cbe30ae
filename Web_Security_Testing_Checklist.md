# Bộ Checklist <PERSON><PERSON><PERSON>t Ứng Dụng Web
*Dựa trên OWASP Web Security Testing Guide (WSTG) v4.2*

## 1. INFORMATION GATHERING (Thu thập thông tin)

### 1.1 Conduct Search Engine Discovery Reconnaissance
- [ ] Kiểm tra thông tin nhạy cảm trên Google/Bing
- [ ] Tìm kiếm file backup, config files
- [ ] Kiểm tra subdomain và directory listing
- [ ] Phân tích robots.txt và sitemap.xml

### 1.2 Fingerprint Web Server
- [ ] Xác định loại web server (Apache, Nginx, IIS)
- [ ] Kiểm tra version và patch level
- [ ] Phân tích HTTP headers
- [ ] Kiểm tra error pages để lộ thông tin

### 1.3 Review Webserver Metafiles
- [ ] Kiểm tra robots.txt
- [ ] Phân tích sitemap.xml
- [ ] Kiểm tra .htaccess files
- [ ] Review security.txt

### 1.4 Enumerate Applications on Webserver
- [ ] Scan port và service
- [ ] Xác định các ứng dụng chạy trên server
- [ ] Kiểm tra virtual hosts
- [ ] Phân tích SSL/TLS certificates

### 1.5 Review Webpage Comments and Metadata
- [ ] Kiểm tra HTML comments
- [ ] Phân tích meta tags
- [ ] Review JavaScript comments
- [ ] Kiểm tra hidden fields

### 1.6 Identify Application Entry Points
- [ ] Map tất cả các URL endpoints
- [ ] Xác định parameters và methods
- [ ] Phân tích form inputs
- [ ] Kiểm tra API endpoints

### 1.7 Map Execution Paths Through Application
- [ ] Trace application workflow
- [ ] Xác định business logic flow
- [ ] Map user roles và permissions
- [ ] Phân tích session management

### 1.8 Fingerprint Web Application Framework
- [ ] Xác định framework (Spring, Django, Laravel)
- [ ] Kiểm tra version và vulnerabilities
- [ ] Phân tích framework-specific files
- [ ] Review default configurations

### 1.9 Fingerprint Web Application
- [ ] Xác định CMS/platform (WordPress, Drupal)
- [ ] Kiểm tra plugins và modules
- [ ] Phân tích version information
- [ ] Review known vulnerabilities

### 1.10 Map Application Architecture
- [ ] Xác định kiến trúc hệ thống
- [ ] Phân tích load balancer
- [ ] Kiểm tra CDN usage
- [ ] Map database connections

## 2. CONFIGURATION AND DEPLOYMENT MANAGEMENT

### 2.1 Test Network Infrastructure Configuration
- [ ] Kiểm tra firewall rules
- [ ] Phân tích network segmentation
- [ ] Test port filtering
- [ ] Kiểm tra DMZ configuration

### 2.2 Test Application Platform Configuration
- [ ] Review server hardening
- [ ] Kiểm tra default accounts
- [ ] Phân tích service configurations
- [ ] Test unnecessary services

### 2.3 Test File Extensions Handling
- [ ] Kiểm tra file upload restrictions
- [ ] Test executable file handling
- [ ] Phân tích MIME type validation
- [ ] Review file extension blacklisting

### 2.4 Review Old Backup and Unreferenced Files
- [ ] Tìm kiếm backup files (.bak, .old)
- [ ] Kiểm tra temporary files
- [ ] Phân tích log files exposure
- [ ] Review unreferenced pages

### 2.5 Enumerate Infrastructure and Application Admin Interfaces
- [ ] Tìm admin panels
- [ ] Kiểm tra management interfaces
- [ ] Test default admin credentials
- [ ] Phân tích admin functionality

### 2.6 Test HTTP Methods
- [ ] Kiểm tra allowed HTTP methods
- [ ] Test dangerous methods (PUT, DELETE)
- [ ] Phân tích OPTIONS method
- [ ] Review method overriding

### 2.7 Test HTTP Strict Transport Security
- [ ] Kiểm tra HSTS header
- [ ] Phân tích max-age value
- [ ] Test includeSubDomains
- [ ] Review preload directive

### 2.8 Test RIA Cross Domain Policy
- [ ] Kiểm tra crossdomain.xml
- [ ] Phân tích clientaccesspolicy.xml
- [ ] Test CORS configuration
- [ ] Review domain restrictions

### 2.9 Test File Permission
- [ ] Kiểm tra file permissions
- [ ] Phân tích directory permissions
- [ ] Test world-readable files
- [ ] Review sensitive file access

### 2.10 Test for Subdomain Takeover
- [ ] Kiểm tra dangling DNS records
- [ ] Test subdomain pointing to external services
- [ ] Phân tích CNAME records
- [ ] Review cloud service configurations

### 2.11 Test Cloud Storage
- [ ] Kiểm tra S3 bucket permissions
- [ ] Test public cloud storage
- [ ] Phân tích access controls
- [ ] Review data exposure

## 3. IDENTITY MANAGEMENT TESTING

### 3.1 Test Role Definitions
- [ ] Kiểm tra role-based access control
- [ ] Phân tích privilege escalation
- [ ] Test role inheritance
- [ ] Review default roles

### 3.2 Test User Registration Process
- [ ] Kiểm tra registration validation
- [ ] Test duplicate account creation
- [ ] Phân tích email verification
- [ ] Review account activation

### 3.3 Test Account Provisioning Process
- [ ] Kiểm tra account creation workflow
- [ ] Test approval processes
- [ ] Phân tích default permissions
- [ ] Review account lifecycle

### 3.4 Testing for Account Enumeration
- [ ] Test username enumeration
- [ ] Kiểm tra email enumeration
- [ ] Phân tích error messages
- [ ] Review timing attacks

### 3.5 Testing for Weak Username Policy
- [ ] Kiểm tra username complexity
- [ ] Test predictable usernames
- [ ] Phân tích username format
- [ ] Review username disclosure

## 4. AUTHENTICATION TESTING

### 4.1 Testing for Credentials Transported over Encrypted Channel
- [ ] Kiểm tra HTTPS usage
- [ ] Test mixed content
- [ ] Phân tích SSL/TLS configuration
- [ ] Review certificate validation

### 4.2 Testing for Default Credentials
- [ ] Kiểm tra default passwords
- [ ] Test vendor default accounts
- [ ] Phân tích system accounts
- [ ] Review application defaults

### 4.3 Testing for Weak Lock Out Mechanism
- [ ] Kiểm tra account lockout policy
- [ ] Test brute force protection
- [ ] Phân tích lockout duration
- [ ] Review unlock mechanisms

### 4.4 Testing for Bypassing Authentication Schema
- [ ] Test authentication bypass
- [ ] Kiểm tra direct object access
- [ ] Phân tích URL manipulation
- [ ] Review forced browsing

### 4.5 Testing for Vulnerable Remember Password
- [ ] Kiểm tra remember me functionality
- [ ] Test persistent login tokens
- [ ] Phân tích token security
- [ ] Review token expiration

### 4.6 Testing for Browser Cache Weaknesses
- [ ] Kiểm tra cache control headers
- [ ] Test sensitive data caching
- [ ] Phân tích browser history
- [ ] Review autocomplete settings

### 4.7 Testing for Weak Password Policy
- [ ] Kiểm tra password complexity
- [ ] Test password length requirements
- [ ] Phân tích password history
- [ ] Review password expiration

### 4.8 Testing for Weak Security Question Answer
- [ ] Kiểm tra security questions
- [ ] Test answer predictability
- [ ] Phân tích question complexity
- [ ] Review answer validation

### 4.9 Testing for Weak Password Change or Reset Functionalities
- [ ] Kiểm tra password reset process
- [ ] Test reset token security
- [ ] Phân tích reset link expiration
- [ ] Review old password validation

### 4.10 Testing for Weaker Authentication in Alternative Channel
- [ ] Kiểm tra mobile app authentication
- [ ] Test API authentication
- [ ] Phân tích alternative interfaces
- [ ] Review SSO implementation

## 5. AUTHORIZATION TESTING

### 5.1 Testing Directory Traversal File Include
- [ ] Test path traversal attacks
- [ ] Kiểm tra file inclusion vulnerabilities
- [ ] Phân tích directory restrictions
- [ ] Review file access controls

### 5.2 Testing for Bypassing Authorization Schema
- [ ] Test horizontal privilege escalation
- [ ] Kiểm tra vertical privilege escalation
- [ ] Phân tích access control bypass
- [ ] Review authorization logic

### 5.3 Testing for Privilege Escalation
- [ ] Test role escalation
- [ ] Kiểm tra function-level access
- [ ] Phân tích administrative functions
- [ ] Review privilege boundaries

### 5.4 Testing for Insecure Direct Object References
- [ ] Test object reference manipulation
- [ ] Kiểm tra parameter tampering
- [ ] Phân tích resource access
- [ ] Review object-level authorization

## 6. SESSION MANAGEMENT TESTING

### 6.1 Testing for Session Management Schema
- [ ] Kiểm tra session token generation
- [ ] Test session randomness
- [ ] Phân tích session entropy
- [ ] Review session algorithms

### 6.2 Testing for Cookies Attributes
- [ ] Kiểm tra Secure flag
- [ ] Test HttpOnly flag
- [ ] Phân tích SameSite attribute
- [ ] Review cookie domain/path

### 6.3 Testing for Session Fixation
- [ ] Test session ID prediction
- [ ] Kiểm tra session regeneration
- [ ] Phân tích login session handling
- [ ] Review session lifecycle

### 6.4 Testing for Exposed Session Variables
- [ ] Kiểm tra session data exposure
- [ ] Test session storage security
- [ ] Phân tích session transmission
- [ ] Review session encryption

### 6.5 Testing for Cross Site Request Forgery
- [ ] Test CSRF protection
- [ ] Kiểm tra anti-CSRF tokens
- [ ] Phân tích referrer validation
- [ ] Review state-changing operations

### 6.6 Testing for Logout Functionality
- [ ] Test session termination
- [ ] Kiểm tra logout completeness
- [ ] Phân tích session cleanup
- [ ] Review logout redirect

### 6.7 Testing Session Timeout
- [ ] Kiểm tra idle timeout
- [ ] Test absolute timeout
- [ ] Phân tích timeout warnings
- [ ] Review session renewal

### 6.8 Testing for Session Puzzling
- [ ] Test session variable confusion
- [ ] Kiểm tra session state manipulation
- [ ] Phán tích session logic flaws
- [ ] Review session consistency

## 7. INPUT VALIDATION TESTING

### 7.1 Testing for Reflected Cross Site Scripting
- [ ] Test XSS in parameters
- [ ] Kiểm tra XSS in headers
- [ ] Phân tích output encoding
- [ ] Review input sanitization

### 7.2 Testing for Stored Cross Site Scripting
- [ ] Test persistent XSS
- [ ] Kiểm tra XSS in user content
- [ ] Phân tích data storage security
- [ ] Review content filtering

### 7.3 Testing for HTTP Verb Tampering
- [ ] Test method override
- [ ] Kiểm tra HTTP method confusion
- [ ] Phân tích verb-based access control
- [ ] Review method validation

### 7.4 Testing for HTTP Parameter Pollution
- [ ] Test parameter duplication
- [ ] Kiểm tra parameter precedence
- [ ] Phân tích parameter parsing
- [ ] Review parameter handling

### 7.5 Testing for SQL Injection
- [ ] Test SQL injection in forms
- [ ] Kiểm tra blind SQL injection
- [ ] Phân tích parameterized queries
- [ ] Review database error handling

### 7.6 Testing for LDAP Injection
- [ ] Test LDAP query manipulation
- [ ] Kiểm tra LDAP filter injection
- [ ] Phân tích LDAP error handling
- [ ] Review LDAP input validation

### 7.7 Testing for XML Injection
- [ ] Test XML entity injection
- [ ] Kiểm tra XXE vulnerabilities
- [ ] Phân tích XML parsing
- [ ] Review XML input validation

### 7.8 Testing for SSI Injection
- [ ] Test server-side includes
- [ ] Kiểm tra SSI directive injection
- [ ] Phân tích SSI processing
- [ ] Review SSI configuration

### 7.9 Testing for XPath Injection
- [ ] Test XPath query manipulation
- [ ] Kiểm tra XPath error handling
- [ ] Phân tích XML data access
- [ ] Review XPath input validation

### 7.10 Testing for IMAP SMTP Injection
- [ ] Test email header injection
- [ ] Kiểm tra command injection
- [ ] Phân tích email processing
- [ ] Review email input validation

### 7.11 Testing for Code Injection
- [ ] Test code execution
- [ ] Kiểm tra script injection
- [ ] Phân tích dynamic code evaluation
- [ ] Review code input validation

### 7.12 Testing for Command Injection
- [ ] Test OS command execution
- [ ] Kiểm tra command chaining
- [ ] Phân tích system call security
- [ ] Review command input validation

### 7.13 Testing for Format String Injection
- [ ] Test format string vulnerabilities
- [ ] Kiểm tra printf-style functions
- [ ] Phân tích format specifiers
- [ ] Review string formatting

### 7.14 Testing for Incubated Vulnerability
- [ ] Test time-delayed attacks
- [ ] Kiểm tra stored malicious content
- [ ] Phân tích delayed execution
- [ ] Review content lifecycle

### 7.15 Testing for HTTP Splitting Smuggling
- [ ] Test HTTP response splitting
- [ ] Kiểm tra request smuggling
- [ ] Phân tích HTTP parsing
- [ ] Review protocol handling

### 7.16 Testing for HTTP Incoming Requests
- [ ] Test request validation
- [ ] Kiểm tra malformed requests
- [ ] Phân tích request parsing
- [ ] Review input boundaries

### 7.17 Testing for Host Header Injection
- [ ] Test Host header manipulation
- [ ] Kiểm tra virtual host confusion
- [ ] Phân tích host validation
- [ ] Review header processing

### 7.18 Testing for Server Side Template Injection
- [ ] Test template injection
- [ ] Kiểm tra template engine security
- [ ] Phân tích template processing
- [ ] Review template input validation

### 7.19 Testing for Server Side Request Forgery
- [ ] Test SSRF vulnerabilities
- [ ] Kiểm tra internal network access
- [ ] Phân tích URL validation
- [ ] Review request filtering

## 8. ERROR HANDLING

### 8.1 Testing for Improper Error Handling
- [ ] Kiểm tra error message disclosure
- [ ] Test stack trace exposure
- [ ] Phân tích debug information
- [ ] Review error page content

### 8.2 Testing for Stack Traces
- [ ] Test application stack traces
- [ ] Kiểm tra framework error pages
- [ ] Phân tích exception handling
- [ ] Review error logging

## 9. CRYPTOGRAPHY

### 9.1 Testing for Weak SSL TLS Ciphers
- [ ] Test cipher suite strength
- [ ] Kiểm tra deprecated protocols
- [ ] Phân tích key exchange
- [ ] Review SSL/TLS configuration

### 9.2 Testing for Padding Oracle
- [ ] Test padding oracle attacks
- [ ] Kiểm tra CBC mode vulnerabilities
- [ ] Phân tích encryption padding
- [ ] Review cryptographic implementation

### 9.3 Testing for Sensitive Information Sent via Unencrypted Channels
- [ ] Test data transmission security
- [ ] Kiểm tra mixed content
- [ ] Phân tích protocol downgrade
- [ ] Review encryption usage

### 9.4 Testing for Weak Encryption
- [ ] Test encryption algorithms
- [ ] Kiểm tra key strength
- [ ] Phân tích cryptographic standards
- [ ] Review encryption implementation

## 10. BUSINESS LOGIC TESTING

### 10.1 Test Business Logic Data Validation
- [ ] Test business rule validation
- [ ] Kiểm tra data integrity
- [ ] Phân tích business constraints
- [ ] Review validation logic

### 10.2 Test Ability to Forge Requests
- [ ] Test request forgery
- [ ] Kiểm tra business process bypass
- [ ] Phân tích workflow manipulation
- [ ] Review process integrity

### 10.3 Test Integrity Checks
- [ ] Test data integrity
- [ ] Kiểm tra checksum validation
- [ ] Phân tích data consistency
- [ ] Review integrity mechanisms

### 10.4 Test for Process Timing
- [ ] Test race conditions
- [ ] Kiểm tra timing attacks
- [ ] Phân tích concurrent access
- [ ] Review synchronization

### 10.5 Test Number of Times a Function Can Be Used
- [ ] Test function limits
- [ ] Kiểm tra rate limiting
- [ ] Phân tích usage quotas
- [ ] Review function controls

### 10.6 Testing for the Circumvention of Work Flows
- [ ] Test workflow bypass
- [ ] Kiểm tra step skipping
- [ ] Phân tích process flow
- [ ] Review workflow controls

### 10.7 Test Defenses Against Application Misuse
- [ ] Test abuse prevention
- [ ] Kiểm tra misuse detection
- [ ] Phân tích defensive mechanisms
- [ ] Review abuse controls

### 10.8 Test Upload of Unexpected File Types
- [ ] Test file type validation
- [ ] Kiểm tra malicious file upload
- [ ] Phân tích file processing
- [ ] Review upload controls

### 10.9 Test Upload of Malicious Files
- [ ] Test malware upload
- [ ] Kiểm tra executable files
- [ ] Phân tích file scanning
- [ ] Review malware detection

## 11. CLIENT SIDE TESTING

### 11.1 Testing for DOM Based Cross Site Scripting
- [ ] Test DOM XSS vulnerabilities
- [ ] Kiểm tra client-side injection
- [ ] Phân tích JavaScript security
- [ ] Review DOM manipulation

### 11.2 Testing for JavaScript Execution
- [ ] Test JavaScript injection
- [ ] Kiểm tra script execution
- [ ] Phân tích code injection
- [ ] Review script validation

### 11.3 Testing for HTML Injection
- [ ] Test HTML injection
- [ ] Kiểm tra content injection
- [ ] Phân tích markup validation
- [ ] Review HTML filtering

### 11.4 Testing for Client Side URL Redirect
- [ ] Test open redirects
- [ ] Kiểm tra URL manipulation
- [ ] Phân tích redirect validation
- [ ] Review redirect controls

### 11.5 Testing for CSS Injection
- [ ] Test CSS injection
- [ ] Kiểm tra style manipulation
- [ ] Phân tích CSS security
- [ ] Review style validation

### 11.6 Testing for Client Side Resource Manipulation
- [ ] Test resource tampering
- [ ] Kiểm tra client-side validation bypass
- [ ] Phân tích resource integrity
- [ ] Review client controls

### 11.7 Test Cross Origin Resource Sharing
- [ ] Test CORS configuration
- [ ] Kiểm tra origin validation
- [ ] Phân tích cross-origin requests
- [ ] Review CORS policies

### 11.8 Testing for Cross Site Flashing
- [ ] Test Flash vulnerabilities
- [ ] Kiểm tra cross-domain policies
- [ ] Phân tích Flash security
- [ ] Review Flash controls

### 11.9 Testing for Clickjacking
- [ ] Test frame busting
- [ ] Kiểm tra X-Frame-Options
- [ ] Phân tích iframe security
- [ ] Review clickjacking protection

### 11.10 Testing WebSockets
- [ ] Test WebSocket security
- [ ] Kiểm tra message validation
- [ ] Phân tích connection security
- [ ] Review WebSocket controls

### 11.11 Test Web Messaging
- [ ] Test postMessage security
- [ ] Kiểm tra origin validation
- [ ] Phân tích message handling
- [ ] Review messaging controls

### 11.12 Test Browser Storage
- [ ] Test localStorage security
- [ ] Kiểm tra sessionStorage
- [ ] Phân tích storage access
- [ ] Review storage controls

### 11.13 Testing for Cross Site Script Inclusion
- [ ] Test JSONP vulnerabilities
- [ ] Kiểm tra script inclusion
- [ ] Phân tích callback validation
- [ ] Review inclusion controls

## 12. API TESTING

### 12.1 Testing GraphQL
- [ ] Test GraphQL injection
- [ ] Kiểm tra query complexity
- [ ] Phân tích schema exposure
- [ ] Review GraphQL security

### 12.2 Testing for Excessive Data Exposure
- [ ] Test API data leakage
- [ ] Kiểm tra response filtering
- [ ] Phân tích data minimization
- [ ] Review data exposure

### 12.3 Testing for Broken Function Level Authorization
- [ ] Test API authorization
- [ ] Kiểm tra function access
- [ ] Phân tích permission checks
- [ ] Review API security

### 12.4 Testing for Unrestricted Resource Consumption
- [ ] Test rate limiting
- [ ] Kiểm tra resource quotas
- [ ] Phân tích DoS protection
- [ ] Review resource controls

### 12.5 Testing for Broken Object Level Authorization
- [ ] Test object access control
- [ ] Kiểm tra IDOR in APIs
- [ ] Phân tích object permissions
- [ ] Review authorization logic

### 12.6 Testing for Mass Assignment
- [ ] Test parameter binding
- [ ] Kiểm tra object modification
- [ ] Phân tích field validation
- [ ] Review mass assignment

### 12.7 Testing for Security Misconfiguration
- [ ] Test API configuration
- [ ] Kiểm tra default settings
- [ ] Phân tích security headers
- [ ] Review API hardening

### 12.8 Testing for Injection
- [ ] Test API injection attacks
- [ ] Kiểm tra parameter validation
- [ ] Phân tích input sanitization
- [ ] Review injection protection

### 12.9 Testing for Improper Assets Management
- [ ] Test API versioning
- [ ] Kiểm tra deprecated endpoints
- [ ] Phân tích asset inventory
- [ ] Review API lifecycle

### 12.10 Testing for Insufficient Logging and Monitoring
- [ ] Test API logging
- [ ] Kiểm tra audit trails
- [ ] Phân tích monitoring
- [ ] Review security events

---

## Ghi chú sử dụng:
- ✅ Đánh dấu các mục đã kiểm tra
- 🔴 Đánh dấu các mục có vấn đề bảo mật
- 📝 Ghi chú chi tiết về findings
- 🔧 Đánh dấu các mục cần khắc phục

## Mức độ ưu tiên:
- **Critical**: Cần khắc phục ngay lập tức
- **High**: Khắc phục trong 1-2 tuần
- **Medium**: Khắc phục trong 1 tháng
- **Low**: Khắc phục khi có thời gian

## Tools hỗ trợ:
- OWASP ZAP
- Burp Suite
- Nmap
- SQLMap
- Nikto
- SSLyze
- Nessus
- Acunetix
