<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{ tranService.translate("diagnose.titlepage.seardDiagnose") }}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">

    </div>
</div>
<form [formGroup]="formSearchDiagnose" (ngSubmit)="onSubmitSearch()" class="pt-3 vnpt-field-set diagnose-search">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid search-grid-2">
            <div class="col-3">
                <vnpt-select
                    class="w-full"
                    [control]="controlComboSelect"
                    [(value)]="searchInfo.msisdn"
                    [placeholder]="tranService.translate('diagnose.label.msisdn')"
                    objectKey="sim"
                    paramKey="msisdn"
                    keyReturn="msisdn"
                    displayPattern="${msisdn}"
                    typeValue="primitive"
                    [isMultiChoice]="false"
                    [required]="true"
                    [paramDefault]="{}"
                    (onchange)="InputMsisdn($event)"
                >
                </vnpt-select>

                <div class="w-full field grid required-select">
                    <label htmlFor="msisdn" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500 block"
                               *ngIf="controlComboSelect.dirty && controlComboSelect.error.required">
                            {{ tranService.translate("global.message.required") }}
                        </small>
                    </div>
                </div>
            </div>

            <div class="col-3">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"

                                [(ngModel)]="searchInfo.dateFrom"
                                formControlName="dateFrom"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [required]="true"
                                [maxDate]="maxDateFrom"
                                [minDate]="minDateFrom"
                                (onSelect)="onChangeDateFrom(searchInfo.dateFrom)"
                                (onInput)="onChangeDateFrom(searchInfo.dateFrom)"
                    ></p-calendar>
                    <label htmlFor="dateFrom">{{ tranService.translate("ticket.label.dateFrom") }}</label>
                </span>
                <div class="w-full field grid">
                    <label htmlFor="dateFrom" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500"
                               *ngIf="formSearchDiagnose.controls.dateFrom.dirty && formSearchDiagnose.controls.dateFrom.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"

                                [(ngModel)]="searchInfo.dateTo"
                                formControlName="dateTo"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [minDate]="minDateTo"
                                [maxDate]="maxDateTo"
                                [required]="true"
                                (onSelect)="onChangeDateTo(searchInfo.dateTo)"
                                (onInput)="onChangeDateTo(searchInfo.dateTo)"
                    />
                    <label htmlFor="dateTo">{{ tranService.translate("ticket.label.dateTo") }}</label>
                </span>
                <div class="w-full field grid">
                    <label htmlFor="dateFrom" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500"
                               *ngIf="formSearchDiagnose.controls.dateTo.dirty && formSearchDiagnose.controls.dateTo.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                    </div>
                </div>
            </div>
            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>
<div class="diagnose-div vnpt-field-set pt-3" *ngIf="showAfterSearch">
    <p-card>
        <div *ngIf="detailLastActivity != null">
        <div class="mt-1 grid flex justify-content-center">
            <div class="col-5">
                <div class="grid">
                    <span style="min-width: 200px; max-width: 200px;"
                          class="inline-block col-fixed">{{ tranService.translate("diagnose.label.lastActivity") }}:</span>
                    <span class="col">{{ detailLastActivity.lastActivity}}</span>
                </div>
                <div class="grid">
                    <span style="min-width: 200px; max-width: 200px;"
                          class="inline-block col-fixed">{{ tranService.translate("diagnose.label.class") }}:</span>
                    <span class="col">{{ detailLastActivity.membershipClass }}</span>
                </div>
                <div class="grid">
                    <span style="min-width: 200px; max-width: 200px;"
                          class="inline-block col-fixed">{{ tranService.translate("diagnose.label.typeNetwork") }}:</span>
                    <div class="col">
                        <p-selectButton
                        [options]="typeNetworkSelect"
                        [(ngModel)]="typeNetwork"
                        optionValue="value"
                        optionLabel = "label"
                        class="p-button-label"
                        (onOptionClick)="onChangeSelectOption($event.option)">
                        </p-selectButton>
                    </div>
                </div>
            </div>
            <div class="col-5">
                <div class="grid">
                    <span style="min-width: 200px; max-width: 200px;"
                          class="inline-block col-fixed">{{ tranService.translate("diagnose.label.celLName") }}:</span>
                    <span class="col uppercase">{{ detailLastActivity.celLName }}</span>
                </div>
                <div class="grid">
                    <span style="min-width: 200px; max-width: 200px;"
                          class="inline-block col-fixed">{{ tranService.translate("diagnose.label.ratingPlan") }}:</span>
                    <span class="col">{{ detailLastActivity.ratingPlan }}</span>
                </div>
            </div>
        </div>
        </div>

        <table-vnpt *ngIf="typeNetwork == CONSTANTS.DIAGNOSE.TYPE_NETWORK.LTE"
            [columns]="columns"
            [dataSet]="dataSet"
            [options]="optionTable"
            [loadData]="search.bind(this)"
            [pageNumber]="pageNumber"
            [pageSize]="pageSize"
            [sort]="sort"
            [params]="searchInfo"
        ></table-vnpt>
        <table-vnpt *ngIf="typeNetwork == CONSTANTS.DIAGNOSE.TYPE_NETWORK.UMTS"
                    [columns]="columns"
                    [dataSet]="dataSet"
                    [options]="optionTable"
                    [loadData]="search.bind(this)"
                    [pageNumber]="pageNumber"
                    [pageSize]="pageSize"
                    [sort]="sort"
                    [params]="searchInfo"
        ></table-vnpt>
    </p-card>
</div>
