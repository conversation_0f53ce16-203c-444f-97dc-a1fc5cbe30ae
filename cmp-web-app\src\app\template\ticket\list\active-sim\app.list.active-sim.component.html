    <div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{ tranService.translate("ticket.menu.activeSim") }}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button styleClass="p-button-info"
                  *ngIf="userInfo.type == userType.CUSTOMER && checkAuthen([CONSTANTS.PERMISSIONS.TICKET.CREATE])"
                  [label]="tranService.translate('global.button.create')"
                  (click)="showModalCreate()" icon="">
        </p-button>
    </div>
</div>

<form [formGroup]="formSearchTicket" (ngSubmit)="onSubmitSearch()" class="pt-3 pb-2 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid search-grid-4">
            <!-- ma tinh -->
            <div *ngIf="this.userInfo.type == this.userType.ADMIN" class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full"
                                [showClear]="true" [filter]="true" filterBy="display"
                                id="provinceCode" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.provinceCode"
                                [required]="false"
                                formControlName="provinceCode"
                                [options]="listProvince"
                                optionLabel="display"
                                optionValue="code"
                    ></p-dropdown>
                    <label class="label-dropdown" htmlFor="provinceCode">{{ tranService.translate("account.label.province") }}</label>
                </span>
            </div>
            <!-- trạng thái ticket -->
            <!--            <div class="col-2">-->
            <!--                <span class="p-float-label">-->
            <!--                    <p-dropdown styleClass="w-full"-->
            <!--                                [showClear]="true" [filter]="true" filterBy="display"-->
            <!--                                id="status" [autoDisplayFirst]="false"-->
            <!--                                [(ngModel)]="searchInfo.status"-->
            <!--                                [required]="false"-->
            <!--                                formControlName="status"-->
            <!--                                [options]="listTicketStatus"-->
            <!--                                optionLabel="label"-->
            <!--                                optionValue="value"-->
            <!--                    ></p-dropdown>-->
            <!--                    <label htmlFor="status">{{tranService.translate("ticket.label.status")}}</label>-->
            <!--                </span>-->
            <!--            </div>-->
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="contactName"
                           [(ngModel)]="searchInfo.contactName"
                           formControlName="contactName"
                           type="text"
                    />
                    <label htmlFor="contactName">{{ tranService.translate("ticket.label.customerName") }}</label>
                </span>
            </div>
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="phone"
                           [(ngModel)]="searchInfo.contactPhone"
                           formControlName="contactPhone"
                           type="number"
                           (keydown)="preventCharacter($event)"
                           min = 0
                    />
                    <label htmlFor="phone">{{ tranService.translate("ticket.label.phone") }}</label>
                </span>
            </div>
            <div class="col-3">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="dateFrom"
                                [(ngModel)]="searchInfo.dateFrom"
                                formControlName="dateFrom"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [maxDate]="maxDateFrom"
                                (onSelect)="onChangeDateFrom(searchInfo.dateFrom)"
                                (onInput)="onChangeDateFrom(searchInfo.dateFrom)"
                    ></p-calendar>
                    <label class="label-calendar" htmlFor="dateFrom">{{ tranService.translate("ticket.label.dateFrom") }}</label>
                </span>
            </div>
            <div class="col-3">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="dateTo"
                                [(ngModel)]="searchInfo.dateTo"
                                formControlName="dateTo"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [minDate]="minDateTo"
                                [maxDate]="maxDateTo"
                                (onSelect)="onChangeDateTo(searchInfo.dateTo)"
                                (onInput)="onChangeDateTo(searchInfo.dateTo)"
                    />
                    <label class="label-calendar" htmlFor="dateTo">{{ tranService.translate("ticket.label.dateTo") }}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>

<table-vnpt
    [tableId]="'tableTicketConfigList'"
    [fieldId]="'provinceCode'"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [pageNumber]="pageNumber"
    [loadData]="search.bind(this)"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="tranService.translate('ticket.menu.requestList')"
></table-vnpt>
<!--    dialog tạo yêu cầu-->
<div class="flex justify-content-center">
    <p-dialog
        [header]="typeRequest == 'view' ? tranService.translate('ticket.label.viewActiveSim'): tranService.translate('ticket.label.requestActiveSim')"
        [(visible)]="isShowCreateRequest"
        [modal]="true" [style]="{ width: '1000px' }" [draggable]="false" [resizable]="false" (onHide)="isHideUpload()">
        <form class="mt-3" [formGroup]="formActiveSim" (ngSubmit)="createRequest()">
            <div class="flex dialog-ticket-sim-1">
                <div class="flex-1 flex col-6">
                    <p-card class="flex-wrap w-full ticket-sim-card">
                        <div class="w-full field grid">
                            <label class="col-fixed"
                                   style="width:180px"><b>{{ tranService.translate("ticket.label.generalInfo") }}</b><span
                            >:</span></label>
                        </div>
                        <div class="w-full field grid" *ngIf="this.userInfo.type == this.userType.ADMIN && typeRequest == 'view'">
                            <label htmlFor="contactName" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("account.label.province") }}<span
                                class="text-red-500">*</span></label>
                            <div class="col">
                                <p-dropdown styleClass="w-full"
                                            [showClear]="false" [filter]="true" filterBy="display"
                                            id="provinceCode" [autoDisplayFirst]="false"
                                            [(ngModel)]="ticket.provinceCode"
                                            formControlName="provinceCode"
                                            [options]="listProvince"
                                            optionLabel="display"
                                            optionValue="code"
                                            [disabled]="true"
                                            [readonly]="true"
                                ></p-dropdown>
                            </div>
                        </div>
                        <!-- contactName -->
                        <div class="w-full field grid">
                            <label htmlFor="contactName" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("ticket.label.customerName") }}<span
                                class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="contactName"
                                       [(ngModel)]="ticket.contactName"
                                       formControlName="contactName"
                                       [required]="true"
                                       [maxLength]="50"
                                       pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$"
                                       [placeholder]="tranService.translate('account.text.inputFullname')"
                                       [readonly]="true"
                                />
                            </div>
                        </div>
                        <div class="w-full field grid">
                            <label htmlFor="email" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("ticket.label.email") }}<span
                                class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="contactEmail"
                                       [(ngModel)]="ticket.contactEmail"
                                       formControlName="contactEmail"
                                       [required]="true"
                                       [maxLength]="50"
                                       pattern="^[a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+)+(\.[a-z]{2,})$"
                                       [placeholder]="tranService.translate('account.text.inputEmail')"
                                       [readonly]="true"
                                />
                            </div>
                        </div>
                        <!-- phone-->
                        <div class="w-full field grid">
                            <label htmlFor="phone" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("ticket.label.phone") }}<span
                                class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="contactPhone"
                                       [(ngModel)]="ticket.contactPhone"
                                       formControlName="contactPhone"
                                       [required]="true"
                                       pattern="^((\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$"
                                       (keydown)="preventCharacter($event)"
                                       [placeholder]="tranService.translate('account.text.inputPhone')"
                                       [readonly]="true"
                                />
                            </div>
                        </div>
                        <!-- content-->
                        <div class="w-full field grid">
                            <label htmlFor="content" class="col-fixed"
                                   style="width:180px;height: fit-content;">{{ tranService.translate("ticket.label.content") }}</label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="content"
                                       [(ngModel)]="ticket.content"
                                       formControlName="content"
                                       [readonly]="typeRequest == 'view' || isShowUpload"
                                       [placeholder]="tranService.translate('ticket.label.content')"
                                       [pTooltip]="ticket.content"
                                       [maxlength]="255"
                                       (keydown)="onKeyDownContent($event)"
                                />
                            </div>
                        </div>
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="content" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formActiveSim.controls.content.errors?.maxLength">{{ tranService.translate("global.message.maxLength", {len: 255}) }}</small>
                            </div>
                            <small class="text-red-500"
                                   *ngIf="formActiveSim.controls.content.errors?.pattern">{{ tranService.translate("global.message.formatCode") }}</small>
                        </div>
                        <!-- note-->
                        <div class="w-full field grid">
                            <label htmlFor="note" class="col-fixed"
                                   style="width:180px;height: fit-content;">{{ tranService.translate("ticket.label.note") }}</label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="note"
                                       [(ngModel)]="ticket.note"
                                       formControlName="note"
                                       [readonly]="typeRequest == 'view' || isShowUpload"
                                       [placeholder]="tranService.translate('ticket.label.note')"
                                       [pTooltip]="ticket.note"
                                       [maxlength]="255"
                                       (keydown)="onKeyDownNote($event)"
                                />
                            </div>
                        </div>
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="note" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formActiveSim.controls.note.errors?.maxLength">{{ tranService.translate("global.message.maxLength", {len: 255}) }}</small>
                                <small class="text-red-500"
                                       *ngIf="formActiveSim.controls.note.errors?.pattern">{{ tranService.translate("global.message.formatCode") }}</small>
                            </div>
                        </div>
                    </p-card>
                </div>
                <div class="flex-1 flex col-6">
                    <div class="block flex-wrap w-full">
                        <p-card class="w-full mb-3" [class]="typeRequest == 'view' ? 'hidden' : ''">
                            <div class="w-full field grid">
                                <label class="col-fixed"
                                       style="width:180px"><b>{{ tranService.translate("ticket.label.enterImsi") }}</b><span
                                    class="text-red-500">*</span><span
                                >:</span></label>
                            </div>
                            <div class="flex items-center">
                                <vnpt-select
                                    [(value)]="listImsisSelected" style="width:70%"
                                    [placeholder]="tranService.translate('ticket.label.listImsi')"
                                    objectKey="activeImsi"
                                    paramKey="imsi"
                                    keyReturn="imsi"
                                    displayPattern="${imsi}"
                                    [isMultiChoice]="true"
                                    [paramDefault]="{status: 0, ticketType: 2}"
                                    [disabled] = "isShowUpload"
                                ></vnpt-select>
                                <div>
                                    <p-button (onClick)="isShowUpload = true" icon="pi pi-upload"
                                              [hidden]="typeRequest == 'view' ? 'hidden' : ''"
                                              [label]="this.tranService.translate('ticket.message.uploadFile')"></p-button>
                                </div>
                            </div>
                        </p-card>

                        <div class="block ticket-sim-card-1" *ngIf="this.listImsisSelected.length > 0" [class]="typeRequest == 'create' ? 'mt-4' : ''">
                            <p-card class="p-grid p-justify-center mt-5">
                                <div class="w-full field grid">
                                    <label class="col-fixed"
                                           style="width:180px"><b>{{ tranService.translate("ticket.label.listactiveImsis") }}</b><span
                                    >:</span></label>
                                </div>
                                <div class="w-full" style="height:400px;overflow-y:scroll;">
                                    <p-table [value]="this.listImsisSelected">
                                        <ng-template pTemplate="header">
                                            <tr>
                                                <th>{{ tranService.translate('global.text.stt') }}</th>
                                                <th>{{ tranService.translate('ticket.label.imsi') }}</th>
                                                <th [hidden]="typeRequest == 'view'">{{ tranService.translate('global.text.action') }}</th>
                                            </tr>
                                        </ng-template>
                                        <ng-template pTemplate="body" let-item let-i="rowIndex">
                                            <tr>
                                                <td>{{ i + 1 }}</td>
                                                <td>{{ item }}</td>
                                                <td [hidden]="typeRequest == 'view'">
                                                    <p-button icon="pi pi-trash" (onClick)="removeImsi(i)" [disabled] = "isShowUpload" ></p-button>
                                                </td>
                                            </tr>
                                        </ng-template>
                                    </p-table>
                                </div>
                            </p-card>
                        </div>

                    </div>
                </div>
            </div>
            <div class="flex flex-row justify-content-center align-items-center mt-3">
                <p-button styleClass="mr-2 p-button-secondary" [label]="tranService.translate('global.button.cancel')"
                          (click)="isShowCreateRequest = false; isShowUpload = false" [hidden]="typeRequest == 'view'"></p-button>
                <p-button type="submit" styleClass="p-button-info" *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.TICKET.CREATE])"
                          [disabled]="formActiveSim.invalid || listImsisSelected.length == 0"
                          [hidden]="typeRequest == 'view'" (onClick)="isShowCreateRequest = false"
                          [label]="tranService.translate('global.button.save')"></p-button>
            </div>
        </form>
    </p-dialog>
    <p-dialog [(visible)]="isShowDownload"
              [modal]="true" [style]="{ width: '500px' }" [draggable]="false" [resizable]="false">
        <div class="grid flex align-items-center justify-content-center">
            <div class="col-10 align-items-center justify-content-center">
                <i class="flex align-items-center justify-content-center pi pi-times-circle"
                   style="font-size: 2em; color: red;"></i>
                <br>
                <b class="flex align-items-center justify-content-center">{{ this.tranService.translate('ticket.message.isError') }} </b>
                <p class="flex align-items-center justify-content-center">{{ this.tranService.translate('ticket.message.isDownloadMessage') }}</p>
                <p-button (onClick)="downloadErrorFile()" class="flex align-items-center justify-content-center"
                          [label]="this.tranService.translate('ticket.message.downloadFile')"></p-button>
            </div>
        </div>
    </p-dialog>
    <p-dialog [(visible)]="isShowUpload" [header]="this.tranService.translate('ticket.label.imsiByFile')">
        <div class="w-full field grid mt-4">
            <div class="col-10 flex flex-row justify-content-start align-items-center">
                <input-file-vnpt class="w-full" [(fileObject)]="fileObject"
                                 [options]="optionInputFile"
                ></input-file-vnpt>
            </div>
            <div class="col-2 flex flex-row justify-content-end align-items-center">
                <p-button icon="pi pi-download" [pTooltip]="tranService.translate('global.button.downloadTemp')"
                          styleClass="p-button-outlined p-button-secondary" (click)="downloadTemplate()"></p-button>
            </div>
        </div>
    </p-dialog>
</div>
