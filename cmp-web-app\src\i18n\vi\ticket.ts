export default {
    label: {
        config: {
            provinceName: "Tên tỉnh",
            provinceCode: "<PERSON>ã tỉnh",
            email: "<PERSON>h sách email",
            clue: "Đầu mối",
        },
        customerName : "Họ và tên liên hệ",
        email : "Email liên hệ",
        phone : "Số điện thoại liên hệ",
        content : "Nội dung",
        type : "Loại yêu cầu",
        createdDate : "Ngày tạo",
        status : "Trạng thái",
        typeRequest : "Loại yêu cầu",
        changeSim : "<PERSON><PERSON> thuê bao thay đổi",
        note : "<PERSON><PERSON> chú",
        noteAdmin: "<PERSON>hi chú của admin",
        createRequest : "Tạo mới yêu cầu",
        updateRequest : "Cập nhật trạng thái xử lý",
        transferProcessing : 'Chuyển xử lý',
        province: 'Tỉnh/Thành phố',
        fullName : '<PERSON>ọ và tên liên hệ',
        requestConfigUpdate : "<PERSON><PERSON><PERSON> đầu mối tỉnh",
        emailSearch : 'Email',
        address: 'Địa chỉ',
        quantity: '<PERSON><PERSON> lượng SIM',
        listImsi: '<PERSON><PERSON><PERSON> IMSI',
        orderAddress: 'Địa chỉ đặt hàng',
        detailAddress: 'Địa chỉ chi tiết',
        district: 'Quận/Huyện',
        commune: 'Phường/Xã',
        updateBy: 'Người cập nhật',
        dateFrom: "Từ ngày",
        dateTo: "Đến ngày",
        imsi: "IMSI",
        allocationDate: "Ngày cấp phát",
        activedDate: "Ngày kích hoạt",
        notActivated: "Chưa kích hoạt",
        awaitingActivation: "Đang chờ kích hoạt",
        activated: "Đã kích hoạt",
        updateOrderSim: "Cập nhật trạng thái yêu cầu",
        processingNotes: "Ghi chú xử lý",
        orderHistory: "Lịch sử đơn hàng",
        updatedDate: "Ngày cập nhật",
        requestActiveSim: "Tạo yêu cầu kích hoạt SIM",
        deliveryAddress: 'Địa chỉ nhận hàng',
        viewOrderSim: 'Chi tiết yêu cầu đặt SIM',
        viewActiveSim: 'Chi tiết yêu cầu kích hoạt SIM',
        listNotes: "Danh sách ghi chú xử lý",
        listImsis: "Danh sách cấp SIM",
        listNote : "Danh sách ghi chú xử lý",
        viewDetailReplaceSim : "Chi tiết yêu cầu thay thế",
        viewDetailTestSim : "Chi tiết yêu cầu thử nghiệm",
        time : "Thời gian",
        implementer :"Người thực hiện",
        historyOrder : "Lịch sử theo dõi đơn hàng",
        generalInfo: "Thông tin chung",
        enterImsi: "Nhập thông tin IMSI",
        listactiveImsis: "Danh sách IMSI",
        imsiByFile: "Nhập IMSI bằng file",
        viewDetailDiagnose: "Chi tiết yêu cầu chẩn đoán"
    },
    menu : {
        config : "Danh sách cấu hình",
        requestMgmt : "Quản lý yêu cầu",
        requestConfig : "Cấu hình đầu mối tỉnh",
        requestList :"Danh sách yêu cầu",
        detail : "Xem chi tiết",
        testSim : 'Yêu cầu thử nghiệm',
        replaceSim : 'Yêu cầu thay thế SIM',
        orderSim: 'Yêu cầu đặt SIM',
        activeSim: 'Yêu cầu kích hoạt SIM',
        listIssuedSim: "Danh sách SIM được cấp",
        errorNote: "Nội dung lỗi",
        diagnose: "Yêu cầu chẩn đoán",
    },
    text : {
        selectEmail : "Chọn email",
        addImsi: "Thêm Imsi",
    },
    status : {
        new : 'Mới',
        received : 'Đã nhận',
        inProgress : 'Đang xử lý',
        reject : 'Từ chối',
        done : 'Hoàn thành'
    },
    type : {
        replaceSim : 'Thay thế SIM',
        testSim : 'Test SIM',
        orderSim: 'Đặt SIM',
        activeSim: 'Kích hoạt SIM',
    },message : {
        invalidPhone : 'Số điện thoại phải là số có đầu 0 (10-11 kí tự) hoặc 84 (11-12 kí tự)',
        noteChangeSim : 'Được phép nhập nhiều số thuê bao, các số ngăn cách nhau bằng dấu phẩy',
        minQuantity: 'Giá trị nhỏ nhất là 1',
        invalidListImsi: 'Định dạng không chính xác, Dãy imsi ngăn cách nhau bởi dấu phẩy',
        large: "Danh sách file lơn hơn \${limitRow}\ dòng",
        empty: "Danh sách đang trống",
        searchInfoNull: "Hãy nhập thông tin tìm kiếm trước khi tải file",
        largeFile: "Dung lượng file không được vượt quá 1MB",
        emptyFile: "File trống thông tin",
        redundantColumns: "File tải lên thừa cột",
        missingColumns: "File tải lên thiếu cột",
        wrongSample: "Sai định dạng file mẫu",
        wrongImsiFormat: "IMSI sai định dạng, IMSI phải là ký tự số và không được vượt quá 18 ký tự",
        missingImsiInfo: "Thiếu thông tin IMSI",
        imsiNotExist: "IMSI không tồn tại",
        imsiIsActivated: "IMSI đã kích hoạt",
        downloadFile: "Tải file về",
        isError: "Đã có lỗi xảy ra!",
        isDownloadMessage: "File tải lên sai thông tin, vui lòng sửa lại!",
        uploadFile: "Import file",
        maxQuantity: "Trường này không được vượt quá 5 ký tự số",
        imsiMaxLength: "Trường này không được vượt quá 18 ký tự số",
        imsiIsExist: "IMSI đã tồn tại trong danh sách",
        hintValidPhone: "Bạn có thể nhấn Enter hoặc dấu phẩy để thêm số điện thoại",
    },
    diagnose: {
      label: {
          name: "Họ và tên",
          email: "Email",
          phone: "Số điện thoại",
          content: "Nội dung yêu cầu",
          number: "Số thuê bao chẩn đoán",
          diagnoseNumber: "Số điện thoại chẩn đoán",
      }
    }
}
