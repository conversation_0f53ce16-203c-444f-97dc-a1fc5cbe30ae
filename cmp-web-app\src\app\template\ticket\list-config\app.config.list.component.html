<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("ticket.menu.requestConfig")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
</div>

<form [formGroup]="formSearchTicketConfig" (ngSubmit)="onSubmitSearch()" class="pt-3 pb-2 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid search-grid-3">
            <!-- ma tinh -->
            <div *ngIf="this.userInfo.type == this.userType.ADMIN" class="col-3 col-4">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full"
                                [showClear]="true" [filter]="true" filterBy="display"
                                id="provinceCode" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.provinceCode"
                                [required]="false"
                                formControlName="provinceCode"
                                [options]="listProvince"
                                optionLabel="display"
                                optionValue="code"
                    ></p-dropdown>
                    <label class="label-dropdown" htmlFor="provinceCode">{{tranService.translate("account.label.province")}}</label>
                </span>
            </div>
            <!-- email -->
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="email"
                           [(ngModel)]="searchInfo.email"
                           formControlName="email"
                    />
                    <label htmlFor="email">{{tranService.translate("ticket.label.emailSearch")}}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>

<table-vnpt
    [tableId]="'tableTicketConfigList'"
    [fieldId]="'provinceCode'"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [pageNumber]="pageNumber"
    [loadData]="search.bind(this)"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="tranService.translate('ticket.menu.config')"
></table-vnpt>

<!--    dialog tạo sửa đầu mối-->
<div *ngIf="isShowUpdateRequestConfig" class="flex justify-content-center dialog-config-list-1">
    <p-dialog [header]="tranService.translate('ticket.label.requestConfigUpdate')" [(visible)]="isShowUpdateRequestConfig" [modal]="true" [style]="{ width: '700px' }" [draggable]="false" [resizable]="false">
        <form class="mt-3" [formGroup]="formTicketConfig" (ngSubmit)="onSubmitUpdate()">
            <!-- danh sách email -->
            <div class="w-full field grid chart-grid">
                <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate("ticket.label.config.clue")}}</label>
                <div class="col" style="max-width: calc(100% - 180px) !important;">
                    <vnpt-select
                        [control]="controlComboSelect"
                        class="w-full"
                        [(value)]="ticketConfig.emailInfos"
                        [placeholder]="tranService.translate('ticket.text.selectEmail')"
                        objectKey="account"
                        paramKey="email"
                        keyReturn="id"
                        displayPattern="${email}"
                        typeValue="object"
                        [paramDefault]="paramSearchCustomerProvince"
                        [loadData]="handleSearch.bind(this)"
                        [required]="true"
                    ></vnpt-select>
                </div>
            </div>
            <div class="w-full field grid text-error-field">
                <label htmlFor="cause" class="col-fixed" style="width:180px"></label>
                <div class="col">
                    <small class="text-red-500" *ngIf="controlComboSelect.dirty && controlComboSelect.error?.required">{{tranService.translate("global.message.required")}}</small>
                </div>
            </div>

            <div class="flex flex-row justify-content-center align-items-center mt-3">
                <p-button styleClass="mr-2 p-button-secondary" [label]="tranService.translate('global.button.cancel')" (click)="isShowUpdateRequestConfig = false"></p-button>
                <p-button type="submit" styleClass="p-button-info" [disabled]="formTicketConfig.invalid || ticketConfig.emailInfos.length == 0 " [label]="tranService.translate('global.button.save')"></p-button>
            </div>
        </form>
    </p-dialog>
</div>
