{"ast": null, "code": "import { ComponentBase } from 'src/app/component.base';\nimport { CONSTANTS } from 'src/app/service/comon/constants';\nimport { TicketService } from \"../../service/ticket/TicketService\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/service/app.layout.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"./app.menuitem.component\";\nimport * as i4 from \"./small-side-bar/sidebar.directive\";\nimport * as i5 from \"../../service/ticket/TicketService\";\nfunction AppMenuComponent_div_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function AppMenuComponent_div_0_div_4_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.layoutService.changeSize(\"small\"));\n    });\n    i0.ɵɵelement(1, \"i\", 10);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppMenuComponent_div_0_ng_container_7_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    const item_r6 = ctx_r10.$implicit;\n    const i_r7 = ctx_r10.index;\n    i0.ɵɵproperty(\"item\", item_r6)(\"index\", i_r7)(\"root\", true);\n  }\n}\nfunction AppMenuComponent_div_0_ng_container_7_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 14);\n  }\n}\nfunction AppMenuComponent_div_0_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AppMenuComponent_div_0_ng_container_7_li_1_Template, 1, 3, \"li\", 11);\n    i0.ɵɵtemplate(2, AppMenuComponent_div_0_ng_container_7_li_2_Template, 1, 0, \"li\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r6.separator);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r6.separator);\n  }\n}\nfunction AppMenuComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵtext(3, \"Menu\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AppMenuComponent_div_0_div_4_Template, 2, 0, \"div\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 6)(6, \"ul\", 7);\n    i0.ɵɵtemplate(7, AppMenuComponent_div_0_ng_container_7_Template, 3, 2, \"ng-container\", 8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.layoutService.typeMenu == \"big\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.model);\n  }\n}\nfunction AppMenuComponent_div_1_div_5_div_2_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 26);\n  }\n  if (rf & 2) {\n    const item_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassMapInterpolate1(\"\", item_r14.icon, \" icon-small-menu mb-3\");\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"active-route-small\": a0\n  };\n};\nfunction AppMenuComponent_div_1_div_5_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24);\n    i0.ɵɵtemplate(2, AppMenuComponent_div_1_div_5_div_2_i_2_Template, 1, 3, \"i\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r14 = ctx.$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"itemInfo\", item_r14);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r13.isActiveRoute(item_r14)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r14.visible != false);\n  }\n}\nfunction AppMenuComponent_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 21);\n    i0.ɵɵtemplate(2, AppMenuComponent_div_1_div_5_div_2_Template, 3, 5, \"div\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", category_r12.items);\n  }\n}\nfunction AppMenuComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function AppMenuComponent_div_1_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.handleOpenBigSidebar());\n    });\n    i0.ɵɵelement(3, \"i\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 19);\n    i0.ɵɵlistener(\"scroll\", function AppMenuComponent_div_1_Template_div_scroll_4_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.handleScroll($event));\n    });\n    i0.ɵɵtemplate(5, AppMenuComponent_div_1_div_5_Template, 3, 1, \"div\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"div\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.model);\n  }\n}\nexport class AppMenuComponent extends ComponentBase {\n  constructor(layoutService, injector, ticketService) {\n    super(injector);\n    this.layoutService = layoutService;\n    this.ticketService = ticketService;\n    // model: ItemMenu[] = [];\n    this.model = [];\n    this.oldScrollTop = 0;\n  }\n  handleOpenBigSidebar() {\n    this.layoutService.changeSize('big');\n    let node = document.querySelector(\".display-subElement\");\n    node?.remove();\n  }\n  handleScroll(event) {\n    let node = document.querySelector(\".display-subElement\");\n    node?.remove();\n    // let delTa = this.oldScrollTop - event.target[\"scrollTop\"];\n    // this.oldScrollTop = event.target[\"scrollTop\"];\n    // let node = document.querySelector(\".display-subElement\")as HTMLDivElement;\n    // if (node) {\n    //   let currentTop = parseInt(node.style.top || \"0\", 10);\n    //   let newTop = currentTop + delTa;\n    //   // Giới hạn vị trí top để không bị quá mức cho phép\n    // //   if (newTop < 0) {\n    // //     newTop = 0;\n    // //   }\n    //   // Gán lại giá trị top cho phần tử\n    //   node.style.top = newTop + \"px\";\n    // }\n  }\n\n  isActiveRoute(item) {\n    if (item.routerLink) {\n      const url = item.routerLink.join('/') || '';\n      return this.router.isActive(url, false) || this.router.url.includes(url) || this.router.url + \"/\" == url;\n    }\n    if (item.items && item.items.length > 0) {\n      return item.items.some(subItem => this.isActiveRoute(subItem));\n    }\n    return false;\n  }\n  ngOnInit() {\n    // console.log(this.isActiveRoute(\"/accounts\"))\n    let userType = this.sessionService.userInfo.type;\n    this.model = [{\n      label: \"\",\n      items: [\n      //dashboard\n      {\n        label: this.tranService.translate(\"global.menu.dashboard\"),\n        icon: \"pi pi-fw pi-chart-bar\",\n        routerLink: [\"/dashboard\"],\n        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DYNAMICCHART.VIEW_LIST])\n      },\n      //sim management\n      {\n        label: this.tranService.translate(\"global.menu.simmgmt\"),\n        icon: \"pi pi-fw pi-slack\",\n        items: [{\n          label: this.tranService.translate(\"global.menu.listsim\"),\n          routerLink: [\"/sims\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.SIM.VIEW_LIST])\n        }, {\n          label: this.tranService.translate(\"global.menu.groupSim\"),\n          // icon: \"pi pi-fw pi-server\",\n          routerLink: [\"/sims/group\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.GROUP_SIM.VIEW_LIST])\n        }, {\n          label: this.tranService.translate(\"global.menu.contract\"),\n          // icon: \"pi pi-fw pi-server\",\n          routerLink: [\"/sims/contract\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.CONTRACT.VIEW_LIST])\n        }\n        // ,\n        // {\n        //     label:  this.tranService.translate(\"recharge.label.menu\"),\n        //     routerLink: [\"/sims/recharge-money\"],\n        // }\n        ],\n\n        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.SIM.VIEW_LIST, CONSTANTS.PERMISSIONS.GROUP_SIM.VIEW_LIST, CONSTANTS.PERMISSIONS.CONTRACT.VIEW_LIST])\n      },\n      //ticket\n      {\n        label: this.tranService.translate(\"ticket.menu.requestMgmt\"),\n        icon: \"pi pi-fw pi-file-excel\",\n        items: [{\n          label: this.tranService.translate(\"ticket.menu.testSim\"),\n          routerLink: [\"/ticket/list-test-sim\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST]),\n          isShowNotify: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT,\n          key: CONSTANTS.KEY_NOTIFY.TEST_SIM\n        }, {\n          label: this.tranService.translate(\"ticket.menu.replaceSim\"),\n          routerLink: [\"/ticket/list-replace-sim\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST]),\n          isShowNotify: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT,\n          key: CONSTANTS.KEY_NOTIFY.REPLACE_SIM\n        }, {\n          label: this.tranService.translate(\"ticket.menu.orderSim\"),\n          routerLink: [\"/ticket/list-order-sim\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST]),\n          isShowNotify: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT,\n          key: CONSTANTS.KEY_NOTIFY.ORDER_SIM,\n          command: () => this.router.navigate([\"/ticket/list-order-sim\"]),\n          items: [{\n            label: this.tranService.translate(\"ticket.menu.listIssuedSim\"),\n            routerLink: [\"/ticket/list-order-sim/sims\"],\n            visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST])\n          }]\n        }, {\n          label: this.tranService.translate(\"ticket.menu.activeSim\"),\n          routerLink: [\"/ticket/list-active-sim\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST]),\n          isShowNotify: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE,\n          key: CONSTANTS.KEY_NOTIFY.ACTIVE_SIM\n        }, {\n          label: this.tranService.translate(\"ticket.menu.diagnose\"),\n          routerLink: [\"/ticket/list-diagnose\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST]) && (this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER),\n          isShowNotify: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE,\n          key: CONSTANTS.KEY_NOTIFY.DIAGNOSE\n        }, {\n          label: this.tranService.translate(\"ticket.menu.requestConfig\"),\n          routerLink: [\"/ticket/list-config/\"],\n          visible: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE\n        }],\n        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST]),\n        isShowNotify: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT,\n        key: CONSTANTS.KEY_NOTIFY.TICKET\n      },\n      //device management\n      {\n        label: this.tranService.translate(\"global.menu.devicemgmt\"),\n        icon: \"pi pi-fw pi-calculator\",\n        items: [{\n          label: this.tranService.translate(\"global.menu.listdevice\"),\n          routerLink: [\"/devices\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.VIEW_LIST])\n        }],\n        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.VIEW_LIST])\n      },\n      //APN Sim\n      {\n        label: this.tranService.translate(\"global.menu.apnsim\"),\n        icon: \"pi pi-fw pi-wifi\",\n        items: [{\n          label: this.tranService.translate(\"global.menu.apnsimlist\"),\n          routerLink: [\"/apnsim\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.APN_SIM.VIEW_LIST])\n        }],\n        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.APN_SIM.VIEW_LIST])\n      },\n      //Cảnh báo\n      {\n        label: this.tranService.translate(\"global.menu.rule\"),\n        icon: \"pi pi-fw pi-bell\",\n        items: [{\n          label: this.tranService.translate(\"global.menu.alertSettings\"),\n          routerLink: [\"/alerts\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.VIEW_LIST])\n        }, {\n          label: this.tranService.translate(\"global.menu.alertReceivingGroup\"),\n          routerLink: [\"/alerts/receiving-group\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.VIEW_LIST])\n        }, {\n          label: this.tranService.translate(\"global.menu.alertHistory\"),\n          routerLink: [\"/alerts/history\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT_HISTORY.VIEW_LIST])\n        }],\n        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.VIEW_LIST, CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.VIEW_LIST, CONSTANTS.PERMISSIONS.ALERT_HISTORY.VIEW_LIST])\n      },\n      // data pool\n      {\n        label: this.tranService.translate(\"global.menu.trafficManagement\"),\n        icon: \"pi pi-fw pi-wallet\",\n        items: [{\n          label: this.tranService.translate(\"global.menu.subTrafficManagement\"),\n          routerLink: [\"/data-pool/walletMgmt/list\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_WALLET])\n        }, {\n          label: this.tranService.translate(\"global.menu.shareManagement\"),\n          routerLink: [\"/data-pool/shareMgmt/listShare\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_SHARE])\n        },\n        // {\n        //     label: this.tranService.translate(\"global.menu.walletConfig\"),\n        //     routerLink:[\"/data-pool/config\"]\n        // },\n        {\n          label: this.tranService.translate(\"global.menu.historyWallet\"),\n          routerLink: [\"/data-pool/history\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_HISTORY_WALLET])\n        }, {\n          label: this.tranService.translate(\"global.menu.listGroupSub\"),\n          routerLink: [\"/data-pool/group/listGroupSub\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.SHARE_GROUP.VIEW_LIST])\n        }, {\n          label: this.tranService.translate(\"global.menu.autoShareGroup\"),\n          routerLink: [\"/data-pool/auto-share-group/list\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.AUTO_SHARE_GROUP.VIEW_LIST])\n        }],\n        // visible: true,\n        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_WALLET, CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_SHARE, CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_HISTORY_WALLET, CONSTANTS.PERMISSIONS.SHARE_GROUP.VIEW_LIST])\n      },\n      //plan management\n      {\n        label: this.tranService.translate(\"global.menu.ratingplanmgmt\"),\n        icon: \"pi pi-fw pi-star\",\n        items: [{\n          label: this.tranService.translate(\"global.menu.listplan\"),\n          routerLink: [\"/plans\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.VIEW_LIST])\n        }, {\n          label: this.tranService.translate(\"global.menu.registerplan\"),\n          routerLink: [\"/plans/registers\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_PLAN])\n        }],\n        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.VIEW_LIST, CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_PLAN])\n      },\n      //account management\n      {\n        label: this.tranService.translate(\"global.menu.accountmgmt\"),\n        icon: \"pi pi-fw pi-users\",\n        items: [{\n          label: this.tranService.translate(\"global.menu.listaccount\"),\n          routerLink: [\"/accounts\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.VIEW_LIST])\n        }, {\n          label: this.tranService.translate(\"global.menu.listroles\"),\n          routerLink: [\"/roles\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ROLE.VIEW_LIST])\n        }, {\n          label: this.tranService.translate(\"global.menu.listpermissions\"),\n          routerLink: [\"/permissions\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.PERMISSION.VIEW_LIST])\n        }, {\n          label: this.tranService.translate(\"global.menu.termpolicy\"),\n          routerLink: [\"/policies\"],\n          visible: userType == CONSTANTS.USER_TYPE.CUSTOMER || userType == CONSTANTS.USER_TYPE.AGENCY\n        }, {\n          label: this.tranService.translate(\"global.menu.termpolicyhistory\"),\n          routerLink: [\"/policies/history\"],\n          visible: userType == CONSTANTS.USER_TYPE.CUSTOMER || userType == CONSTANTS.USER_TYPE.AGENCY\n        }, {\n          label: this.tranService.translate(\"logs.menu.log\"),\n          routerLink: [\"/history-activity/list\"],\n          visible: (userType == CONSTANTS.USER_TYPE.ADMIN || userType == CONSTANTS.USER_TYPE.PROVINCE || userType == CONSTANTS.USER_TYPE.DISTRICT) && this.checkAuthen([CONSTANTS.PERMISSIONS.LOG.VIEW_LIST])\n        }, {\n          label: this.tranService.translate(\"global.menu.apiLogs\"),\n          routerLink: [\"/accounts/logApi\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.SEARCH_LOG_API])\n        }],\n        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.VIEW_LIST, CONSTANTS.PERMISSIONS.ROLE.VIEW_LIST, CONSTANTS.PERMISSIONS.PERMISSION.VIEW_LIST])\n      },\n      //customer management\n      {\n        label: this.tranService.translate(\"global.menu.customermgmt\"),\n        icon: \"pi pi-fw pi-users\",\n        items: [{\n          label: this.tranService.translate(\"global.menu.listcustomer\"),\n          routerLink: [\"/customers\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.CUSTOMER.VIEW_LIST])\n        }],\n        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.CUSTOMER.VIEW_LIST])\n      },\n      //chẩn đoán\n      {\n        label: this.tranService.translate(\"diagnose.menu.diagnose\"),\n        icon: \"pi pi-search-plus\",\n        routerLink: ['/diagnose'],\n        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DIAGNOSE.VIEW_LIST])\n      },\n      //order management\n      {\n        label: this.tranService.translate(\"global.menu.ordermgmt\"),\n        icon: \"pi pi-fw pi-truck\",\n        items: [{\n          label: this.tranService.translate(\"global.menu.listorder\"),\n          routerLink: [\"/orders\"]\n        }],\n        visible: false\n      },\n      //extra service\n      {\n        label: this.tranService.translate(\"global.menu.extraservice\"),\n        icon: \"pi pi-fw pi-th-large\",\n        routerLink: [\"/extra-services\"],\n        visible: false\n      },\n      //logs\n      {\n        label: this.tranService.translate(\"global.menu.log\"),\n        icon: \"pi pi-fw pi-book\",\n        routerLink: [\"/logs\"],\n        visible: false\n      },\n      //report\n      {\n        label: this.tranService.translate(\"global.menu.report\"),\n        icon: \"pi pi-fw pi-file-excel\",\n        items: [{\n          label: this.tranService.translate(\"global.menu.dynamicreport\"),\n          routerLink: [\"/reports/report-dynamic/\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.REPORT_DYNAMIC.VIEW_LIST])\n        }, {\n          label: this.tranService.translate(\"permission.RptContent.RptContent\"),\n          routerLink: [\"/reports/report-dynamic/report-content\"]\n        }, {\n          label: this.tranService.translate(\"global.menu.dynamicreportgroup\"),\n          routerLink: [\"/reports/group-report-dynamic/\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.VIEW_LIST])\n        }],\n        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.REPORT_DYNAMIC.VIEW_LIST, CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.VIEW_LIST], true)\n      },\n      //configuration\n      {\n        label: this.tranService.translate(\"global.menu.configuration\"),\n        icon: \"pi pi-fw pi-cog\",\n        routerLink: [\"/configuration\"],\n        visible: false\n      },\n      //troubleshoot\n      {\n        label: this.tranService.translate(\"global.menu.troubleshoot\"),\n        icon: \"pi pi-fw pi-wrench\",\n        routerLink: [\"/troubleshoot\"],\n        visible: false\n      },\n      //config chart\n      {\n        label: this.tranService.translate(\"global.menu.charts\"),\n        icon: \"pi pi-fw pi-th-large\",\n        routerLink: [\"/config-chart\"],\n        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.CONFIG_DYNAMIC_CHART.VIEW_LIST])\n      },\n      //guide\n      {\n        label: this.tranService.translate(\"global.menu.manual\"),\n        icon: \"pi pi-fw pi-question-circle\",\n        items: [{\n          label: this.tranService.translate(\"global.menu.userGuide\"),\n          icon: \"pi pi-fw pi-info-circle\",\n          routerLink: [\"/docs\"]\n        }, {\n          label: this.tranService.translate(\"global.menu.integrationGuide\"),\n          icon: \"pi pi-fw pi-info-circle\",\n          routerLink: [\"/docs/integration\"],\n          visible: this.checkAuthen([CONSTANTS.PERMISSIONS.GUIDE.INTEGRATION])\n        }]\n        // target: \"_blank\"\n      }]\n    }];\n  }\n\n  static {\n    this.ɵfac = function AppMenuComponent_Factory(t) {\n      return new (t || AppMenuComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(TicketService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppMenuComponent,\n      selectors: [[\"app-menu\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"relative\", 4, \"ngIf\"], [\"class\", \"menuItemCollaped\", 4, \"ngIf\"], [1, \"relative\"], [\"id\", \"menuToggleBlock\", 1, \"flex\", \"flex-row\", \"text-lg\", \"menuItemExpanded\", 2, \"color\", \"white\", \"display\", \"flex\", \"justify-content\", \"space-between\", \"align-items\", \"center\", \"position\", \"sticky\", \"top\", \"0\", \"z-index\", \"9999\", \"outline\", \"0 none\", \"cursor\", \"pointer\", \"padding\", \"0.75rem 2rem\", \"background-color\", \"#021c34\", \"transition\", \"background-color 0.2s, box-shadow 0.2s\", \"box-shadow\", \"-5px 12px 19px 0px rgba(0,0,0,0.51)\", \"-webkit-box-shadow\", \"-5px 12px 19px 0px rgba(0,0,0,0.51)\", \"-moz-box-shadow\", \"-5px 12px 19px 0px rgba(0,0,0,0.51)\", \"min-height\", \"60px\"], [1, \"text-lg\", \"font-semibold\"], [\"style\", \"background-color: #1c3349;\\n        padding: 5px;\\n        width: 30px;\\n        height: 30px;\\n        display: flex;\\n        justify-content: center;\\n        align-items: center;\\n        border-radius: 50%;\", 3, \"click\", 4, \"ngIf\"], [1, \"menu-big-list\"], [1, \"layout-menu\", \"flex-grow-1\"], [4, \"ngFor\", \"ngForOf\"], [2, \"background-color\", \"#1c3349\", \"padding\", \"5px\", \"width\", \"30px\", \"height\", \"30px\", \"display\", \"flex\", \"justify-content\", \"center\", \"align-items\", \"center\", \"border-radius\", \"50%\", 3, \"click\"], [1, \"pi\", \"pi-angle-left\"], [\"class\", \"text-lg menuItemExpanded\", \"app-menuitem\", \"\", 3, \"item\", \"index\", \"root\", 4, \"ngIf\"], [\"class\", \"text-lg menuItemExpanded\", \"class\", \"menu-separator\", 4, \"ngIf\"], [\"app-menuitem\", \"\", 1, \"text-lg\", \"menuItemExpanded\", 3, \"item\", \"index\", \"root\"], [1, \"menu-separator\"], [1, \"menuItemCollaped\"], [1, \"justify-content-center\", \"align-items-center\", 2, \"display\", \"flex\", \"padding\", \"5px\", \"box-shadow\", \"-5px 12px 19px 0px rgba(0,0,0,0.51)\", \"-webkit-box-shadow\", \"-5px 12px 19px 0px rgba(0,0,0,0.51)\", \"-moz-box-shadow\", \"-5px 12px 19px 0px rgba(0,0,0,0.51)\"], [2, \"background-color\", \"#1c3349\", \"color\", \"white\", \"padding\", \"5px\", \"width\", \"30px\", \"height\", \"30px\", \"cursor\", \"pointer\", \"display\", \"flex\", \"justify-content\", \"center\", \"margin\", \"10px 0\", \"align-items\", \"center\", \"border-radius\", \"50%\", 3, \"click\"], [1, \"pi\", \"pi-angle-right\"], [1, \"menu-item-small\", \"flex\", \"flex-column\", \"align-items-center\", \"mt-3\", 2, \"overflow-y\", \"auto\", \"width\", \"60px\", 3, \"scroll\"], [2, \"height\", \"200px\", \"box-shadow\", \"-5px -12px 19px 0px rgba(0,0,0,0.51)\", \"-webkit-box-shadow\", \"-5px -12px 19px 0px rgba(0,0,0,0.51)\", \"-moz-box-shadow\", \"-5px -12px 19px 0px rgba(0,0,0,0.51)\"], [1, \"small-menu-category\"], [\"appSmallSidebarElement\", \"\", 3, \"itemInfo\", 4, \"ngFor\", \"ngForOf\"], [\"appSmallSidebarElement\", \"\", 3, \"itemInfo\"], [3, \"ngClass\"], [\"styleClass\", \"icon-small-menu\", 3, \"class\", 4, \"ngIf\"], [\"styleClass\", \"icon-small-menu\"]],\n      template: function AppMenuComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AppMenuComponent_div_0_Template, 8, 2, \"div\", 0);\n          i0.ɵɵtemplate(1, AppMenuComponent_div_1_Template, 7, 1, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.layoutService.typeMenu == \"big\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.layoutService.typeMenu == \"small\");\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i3.AppMenuitemComponent, i4.SidebarElementDirective],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["ComponentBase", "CONSTANTS", "TicketService", "i0", "ɵɵelementStart", "ɵɵlistener", "AppMenuComponent_div_0_div_4_Template_div_click_0_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "layoutService", "changeSize", "ɵɵelement", "ɵɵelementEnd", "ɵɵproperty", "item_r6", "i_r7", "ɵɵelementContainerStart", "ɵɵtemplate", "AppMenuComponent_div_0_ng_container_7_li_1_Template", "AppMenuComponent_div_0_ng_container_7_li_2_Template", "ɵɵelementContainerEnd", "ɵɵadvance", "separator", "ɵɵtext", "AppMenuComponent_div_0_div_4_Template", "AppMenuComponent_div_0_ng_container_7_Template", "ctx_r0", "typeMenu", "model", "ɵɵclassMapInterpolate1", "item_r14", "icon", "AppMenuComponent_div_1_div_5_div_2_i_2_Template", "ɵɵpureFunction1", "_c0", "ctx_r13", "isActiveRoute", "visible", "AppMenuComponent_div_1_div_5_div_2_Template", "category_r12", "items", "AppMenuComponent_div_1_Template_div_click_2_listener", "_r18", "ctx_r17", "handleOpenBigSidebar", "AppMenuComponent_div_1_Template_div_scroll_4_listener", "$event", "ctx_r19", "handleScroll", "AppMenuComponent_div_1_div_5_Template", "ctx_r1", "AppMenuComponent", "constructor", "injector", "ticketService", "oldScrollTop", "node", "document", "querySelector", "remove", "event", "item", "routerLink", "url", "join", "router", "isActive", "includes", "length", "some", "subItem", "ngOnInit", "userType", "sessionService", "userInfo", "type", "label", "tranService", "translate", "<PERSON><PERSON><PERSON><PERSON>", "PERMISSIONS", "DYNAMICCHART", "VIEW_LIST", "SIM", "GROUP_SIM", "CONTRACT", "TICKET", "isShowNotify", "USER_TYPE", "ADMIN", "PROVINCE", "DISTRICT", "key", "KEY_NOTIFY", "TEST_SIM", "REPLACE_SIM", "ORDER_SIM", "command", "navigate", "ACTIVE_SIM", "CUSTOMER", "DIAGNOSE", "DEVICE", "APN_SIM", "ALERT", "ALERT_RECEIVING_GROUP", "ALERT_HISTORY", "DATAPOOL", "VIEW_WALLET", "VIEW_SHARE", "VIEW_HISTORY_WALLET", "SHARE_GROUP", "AUTO_SHARE_GROUP", "RATING_PLAN", "RATING_PLAN_SIM", "REGISTER_PLAN", "ACCOUNT", "ROLE", "PERMISSION", "AGENCY", "LOG", "SEARCH_LOG_API", "REPORT_DYNAMIC", "GROUP_REPORT_DYNAMIC", "CONFIG_DYNAMIC_CHART", "GUIDE", "INTEGRATION", "ɵɵdirectiveInject", "i1", "LayoutService", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "AppMenuComponent_Template", "rf", "ctx", "AppMenuComponent_div_0_Template", "AppMenuComponent_div_1_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\layout\\app.menu.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\layout\\app.menu.component.html"], "sourcesContent": ["import {AfterContentChecked, Inject, Injector, OnChanges, OnInit} from '@angular/core';\r\nimport { Component } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { ComponentBase } from 'src/app/component.base';\r\nimport { LayoutService } from \"src/app/service/app.layout.service\";\r\nimport { CONSTANTS } from 'src/app/service/comon/constants';\r\nimport { TranslateService } from 'src/app/service/comon/translate.service';\r\nimport { environment } from 'src/environments/environment';\r\nimport {TicketService} from \"../../service/ticket/TicketService\";\r\n\r\ninterface ItemMenu{\r\n    label: string,\r\n    icon?: string,\r\n    routerLink?: Array<string>,\r\n    items?: Array<ItemMenu>,\r\n    routerLinkActiveOptions?: {\r\n        paths?: string,\r\n        queryParams?: string,\r\n        matrixParams?: string,\r\n        fragment?: string\r\n    },\r\n    badge?: string,\r\n    url?: Array<string>,\r\n    target?: string\r\n}\r\n@Component({\r\n    selector: 'app-menu',\r\n    templateUrl: './app.menu.component.html'\r\n})\r\nexport class AppMenuComponent extends ComponentBase implements OnInit{\r\n    // model: ItemMenu[] = [];\r\n    model:  {\r\n                label: string,\r\n                items: any[] // MenuItem[]\r\n            }[] = [];\r\n\r\n    constructor(public layoutService: LayoutService, injector: Injector,\r\n                @Inject(TicketService) private ticketService: TicketService) {\r\n        super(injector);\r\n    }\r\n\r\n    oldScrollTop = 0;\r\n\r\n    handleOpenBigSidebar(){\r\n        this.layoutService.changeSize('big')\r\n        let node = document.querySelector(\".display-subElement\");\r\n        node?.remove();\r\n    }\r\n\r\n    handleScroll(event: any){\r\n        let node = document.querySelector(\".display-subElement\");\r\n        node?.remove();\r\n        // let delTa = this.oldScrollTop - event.target[\"scrollTop\"];\r\n        // this.oldScrollTop = event.target[\"scrollTop\"];\r\n        // let node = document.querySelector(\".display-subElement\")as HTMLDivElement;\r\n        // if (node) {\r\n        //   let currentTop = parseInt(node.style.top || \"0\", 10);\r\n        //   let newTop = currentTop + delTa;\r\n\r\n        //   // Giới hạn vị trí top để không bị quá mức cho phép\r\n        // //   if (newTop < 0) {\r\n        // //     newTop = 0;\r\n        // //   }\r\n\r\n        //   // Gán lại giá trị top cho phần tử\r\n        //   node.style.top = newTop + \"px\";\r\n        // }\r\n      }\r\n\r\n      isActiveRoute(item: any): boolean {\r\n        if (item.routerLink) {\r\n          const url = item.routerLink.join('/') || '';\r\n          return this.router.isActive(url, false) || this.router.url.includes(url) || this.router.url+\"/\"==url;\r\n        }\r\n\r\n        if (item.items && item.items.length > 0) {\r\n          return item.items.some(subItem => this.isActiveRoute(subItem));\r\n        }\r\n\r\n        return false;\r\n      }\r\n\r\n\r\n    ngOnInit() {\r\n        // console.log(this.isActiveRoute(\"/accounts\"))\r\n        let userType  = this.sessionService.userInfo.type;\r\n\r\n        this.model = [\r\n            {\r\n                label: \"\",\r\n                items: [\r\n                    //dashboard\r\n                    {\r\n                        label: this.tranService.translate(\"global.menu.dashboard\"),\r\n                        icon: \"pi pi-fw pi-chart-bar\",\r\n                        routerLink: [\"/dashboard\"],\r\n                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DYNAMICCHART.VIEW_LIST])\r\n                    },\r\n                    //sim management\r\n                    {\r\n                        label: this.tranService.translate(\"global.menu.simmgmt\"),\r\n                        icon: \"pi pi-fw pi-slack\",\r\n                        items: [\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.listsim\"),\r\n                                routerLink: [\"/sims\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.SIM.VIEW_LIST])\r\n                            },\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.groupSim\"),\r\n                                // icon: \"pi pi-fw pi-server\",\r\n                                routerLink:[\"/sims/group\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.GROUP_SIM.VIEW_LIST])\r\n                            },\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.contract\"),\r\n                                // icon: \"pi pi-fw pi-server\",\r\n                                routerLink:[\"/sims/contract\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.CONTRACT.VIEW_LIST])\r\n                            }\r\n                            // ,\r\n                            // {\r\n                            //     label:  this.tranService.translate(\"recharge.label.menu\"),\r\n                            //     routerLink: [\"/sims/recharge-money\"],\r\n                            // }\r\n                        ],\r\n                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.SIM.VIEW_LIST, CONSTANTS.PERMISSIONS.GROUP_SIM.VIEW_LIST, CONSTANTS.PERMISSIONS.CONTRACT.VIEW_LIST])\r\n                    },\r\n                    //ticket\r\n                    {\r\n                        label: this.tranService.translate(\"ticket.menu.requestMgmt\"),\r\n                        icon: \"pi pi-fw pi-file-excel\",\r\n                        items: [\r\n                            {\r\n                                label: this.tranService.translate(\"ticket.menu.testSim\"),\r\n                                routerLink: [\"/ticket/list-test-sim\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST]),\r\n                                isShowNotify: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN\r\n                                    || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE\r\n                                    || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT,\r\n                                key : CONSTANTS.KEY_NOTIFY.TEST_SIM\r\n                            },\r\n                            {\r\n                                label: this.tranService.translate(\"ticket.menu.replaceSim\"),\r\n                                routerLink: [\"/ticket/list-replace-sim\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST]),\r\n                                isShowNotify: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN\r\n                                    || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE\r\n                                    || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT,\r\n                                key : CONSTANTS.KEY_NOTIFY.REPLACE_SIM\r\n                            },{ //yêu cầu đặt mua sim\r\n                                label: this.tranService.translate(\"ticket.menu.orderSim\"),\r\n                                routerLink: [\"/ticket/list-order-sim\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST]),\r\n                                isShowNotify: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN\r\n                                    || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE\r\n                                    || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT,\r\n                                key : CONSTANTS.KEY_NOTIFY.ORDER_SIM,\r\n                                command: () => this.router.navigate([\"/ticket/list-order-sim\"]),\r\n                                items: [\r\n                                    { //Danh sách sim ticket\r\n                                        label: this.tranService.translate(\"ticket.menu.listIssuedSim\"),\r\n                                        routerLink: [\"/ticket/list-order-sim/sims\"],\r\n                                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST]),\r\n                                    },\r\n                                ]\r\n                            },\r\n                            { //yêu cầu kích hoạt sim\r\n                                label: this.tranService.translate(\"ticket.menu.activeSim\"),\r\n                                routerLink: [\"/ticket/list-active-sim\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST]),\r\n                                isShowNotify: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN\r\n                                    || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE ,\r\n                                key : CONSTANTS.KEY_NOTIFY.ACTIVE_SIM,\r\n                            },\r\n                            { //yêu cầu chẩn đoán\r\n                                label: this.tranService.translate(\"ticket.menu.diagnose\"),\r\n                                routerLink: [\"/ticket/list-diagnose\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST])\r\n                                    && (this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN ||\r\n                                        this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE ||\r\n                                        this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER\r\n                                    ),\r\n                                isShowNotify: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN\r\n                                    || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE,\r\n                                key : CONSTANTS.KEY_NOTIFY.DIAGNOSE,\r\n                            },\r\n                            {\r\n                                label: this.tranService.translate(\"ticket.menu.requestConfig\"),\r\n                                routerLink: [\"/ticket/list-config/\"],\r\n                                visible: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE,\r\n                            }\r\n                        ],\r\n                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST]),\r\n                        isShowNotify: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN\r\n                            || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE\r\n                            || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT,\r\n                        key : CONSTANTS.KEY_NOTIFY.TICKET\r\n                    },\r\n                    //device management\r\n                    {\r\n                        label: this.tranService.translate(\"global.menu.devicemgmt\"),\r\n                        icon: \"pi pi-fw pi-calculator\",\r\n                        items: [\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.listdevice\"),\r\n                                routerLink: [\"/devices\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.VIEW_LIST]),\r\n                            },\r\n                        ],\r\n                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.VIEW_LIST]),\r\n                    },\r\n                    //APN Sim\r\n                    {\r\n                        label: this.tranService.translate(\"global.menu.apnsim\"),\r\n                        icon: \"pi pi-fw pi-wifi\",\r\n                        items: [\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.apnsimlist\"),\r\n                                routerLink: [\"/apnsim\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.APN_SIM.VIEW_LIST])\r\n                            },\r\n                        ],\r\n                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.APN_SIM.VIEW_LIST])\r\n                    },\r\n                    //Cảnh báo\r\n                    {\r\n                        label: this.tranService.translate(\"global.menu.rule\"),\r\n                        icon: \"pi pi-fw pi-bell\",\r\n                        items: [\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.alertSettings\"),\r\n                                routerLink: [\"/alerts\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.VIEW_LIST])\r\n\r\n                            },\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.alertReceivingGroup\"),\r\n                                routerLink: [\"/alerts/receiving-group\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.VIEW_LIST])\r\n                            },\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.alertHistory\"),\r\n                                routerLink: [\"/alerts/history\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT_HISTORY.VIEW_LIST]),\r\n                            }\r\n                        ],\r\n                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.VIEW_LIST, CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.VIEW_LIST,CONSTANTS.PERMISSIONS.ALERT_HISTORY.VIEW_LIST])\r\n                    },\r\n                    // data pool\r\n                    {\r\n                        label: this.tranService.translate(\"global.menu.trafficManagement\"),\r\n                        icon: \"pi pi-fw pi-wallet\",\r\n                        items: [\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.subTrafficManagement\"),\r\n                                routerLink:[\"/data-pool/walletMgmt/list\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_WALLET])\r\n                            },\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.shareManagement\"),\r\n                                routerLink:[\"/data-pool/shareMgmt/listShare\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_SHARE])\r\n                            },\r\n                            // {\r\n                            //     label: this.tranService.translate(\"global.menu.walletConfig\"),\r\n                            //     routerLink:[\"/data-pool/config\"]\r\n                            // },\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.historyWallet\"),\r\n                                routerLink:[\"/data-pool/history\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_HISTORY_WALLET])\r\n                            },\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.listGroupSub\"),\r\n                                routerLink:[\"/data-pool/group/listGroupSub\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.SHARE_GROUP.VIEW_LIST])\r\n                            },\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.autoShareGroup\"),\r\n                                routerLink:[\"/data-pool/auto-share-group/list\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.AUTO_SHARE_GROUP.VIEW_LIST])\r\n                            },\r\n\r\n                        ],\r\n                        // visible: true,\r\n                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_WALLET, CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_SHARE, CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_HISTORY_WALLET, CONSTANTS.PERMISSIONS.SHARE_GROUP.VIEW_LIST])\r\n                    },\r\n                    //plan management\r\n                    {\r\n                        label: this.tranService.translate(\"global.menu.ratingplanmgmt\"),\r\n                        icon: \"pi pi-fw pi-star\",\r\n                        items: [\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.listplan\"),\r\n                                routerLink: [\"/plans\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.VIEW_LIST])\r\n                            },\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.registerplan\"),\r\n                                routerLink: [\"/plans/registers\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_PLAN])\r\n                            },\r\n                        ],\r\n                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.VIEW_LIST, CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_PLAN])\r\n                    },\r\n                    //account management\r\n                    {\r\n                        label: this.tranService.translate(\"global.menu.accountmgmt\"),\r\n                        icon: \"pi pi-fw pi-users\",\r\n                        items: [\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.listaccount\"),\r\n                                routerLink: [\"/accounts\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.VIEW_LIST])\r\n                            },\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.listroles\"),\r\n                                routerLink: [\"/roles\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ROLE.VIEW_LIST])\r\n                            },\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.listpermissions\"),\r\n                                routerLink: [\"/permissions\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.PERMISSION.VIEW_LIST])\r\n                            },\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.termpolicy\"),\r\n                                routerLink: [\"/policies\"],\r\n                                visible: userType == CONSTANTS.USER_TYPE.CUSTOMER || userType == CONSTANTS.USER_TYPE.AGENCY\r\n                            },\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.termpolicyhistory\"),\r\n                                routerLink: [\"/policies/history\"],\r\n                                visible: userType == CONSTANTS.USER_TYPE.CUSTOMER || userType == CONSTANTS.USER_TYPE.AGENCY\r\n                            },\r\n                            {\r\n                                label: this.tranService.translate(\"logs.menu.log\"),\r\n                                routerLink: [\"/history-activity/list\"],\r\n                                visible: (userType == CONSTANTS.USER_TYPE.ADMIN || userType == CONSTANTS.USER_TYPE.PROVINCE\r\n                                || userType == CONSTANTS.USER_TYPE.DISTRICT) && this.checkAuthen([CONSTANTS.PERMISSIONS.LOG.VIEW_LIST])\r\n                             },\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.apiLogs\"),\r\n                                routerLink: [\"/accounts/logApi\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.SEARCH_LOG_API])\r\n                            },\r\n                        ],\r\n                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.VIEW_LIST,CONSTANTS.PERMISSIONS.ROLE.VIEW_LIST,CONSTANTS.PERMISSIONS.PERMISSION.VIEW_LIST])\r\n                    },\r\n                    //customer management\r\n                    {\r\n                        label: this.tranService.translate(\"global.menu.customermgmt\"),\r\n                        icon: \"pi pi-fw pi-users\",\r\n                        items: [\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.listcustomer\"),\r\n                                routerLink: [\"/customers\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.CUSTOMER.VIEW_LIST])\r\n                            },\r\n                        ],\r\n                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.CUSTOMER.VIEW_LIST])\r\n                    },\r\n                    //chẩn đoán\r\n                    {\r\n                        label: this.tranService.translate(\"diagnose.menu.diagnose\"),\r\n                        icon: \"pi pi-search-plus\",\r\n                        routerLink: ['/diagnose'],\r\n                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DIAGNOSE.VIEW_LIST])\r\n                    },\r\n\r\n\r\n                    //order management\r\n                    {\r\n                        label: this.tranService.translate(\"global.menu.ordermgmt\"),\r\n                        icon: \"pi pi-fw pi-truck\",\r\n                        items: [\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.listorder\"),\r\n                                routerLink: [\"/orders\"]\r\n                            },\r\n                        ],\r\n                        visible: false\r\n                    },\r\n\r\n\r\n\r\n                    //extra service\r\n                    {\r\n                        label: this.tranService.translate(\"global.menu.extraservice\"),\r\n                        icon: \"pi pi-fw pi-th-large\",\r\n                        routerLink: [\"/extra-services\"],\r\n                        visible: false\r\n                    },\r\n                    //logs\r\n                    {\r\n                        label: this.tranService.translate(\"global.menu.log\"),\r\n                        icon: \"pi pi-fw pi-book\",\r\n                        routerLink: [\"/logs\"],\r\n                        visible: false\r\n                    },\r\n                    //report\r\n                    {\r\n                        label: this.tranService.translate(\"global.menu.report\"),\r\n                        icon: \"pi pi-fw pi-file-excel\",\r\n                        items: [\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.dynamicreport\"),\r\n                                routerLink: [\"/reports/report-dynamic/\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.REPORT_DYNAMIC.VIEW_LIST])\r\n                            },\r\n                            {\r\n                                label: this.tranService.translate(\"permission.RptContent.RptContent\"),\r\n                                routerLink: [\"/reports/report-dynamic/report-content\"]\r\n                            },\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.dynamicreportgroup\"),\r\n                                routerLink: [\"/reports/group-report-dynamic/\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.VIEW_LIST])\r\n                            },\r\n                        ],\r\n                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.REPORT_DYNAMIC.VIEW_LIST, CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.VIEW_LIST], true)\r\n                    },\r\n\r\n                    //configuration\r\n                    {\r\n                        label: this.tranService.translate(\"global.menu.configuration\"),\r\n                        icon: \"pi pi-fw pi-cog\",\r\n                        routerLink: [\"/configuration\"],\r\n                        visible: false\r\n                    },\r\n                    //troubleshoot\r\n                    {\r\n                        label: this.tranService.translate(\"global.menu.troubleshoot\"),\r\n                        icon: \"pi pi-fw pi-wrench\",\r\n                        routerLink: [\"/troubleshoot\"],\r\n                        visible: false\r\n                    },\r\n                    //config chart\r\n                    {\r\n                        label: this.tranService.translate(\"global.menu.charts\"),\r\n                        icon: \"pi pi-fw pi-th-large\",\r\n                        routerLink: [\"/config-chart\"],\r\n                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.CONFIG_DYNAMIC_CHART.VIEW_LIST])\r\n                    },\r\n                     //guide\r\n                    {\r\n                        label: this.tranService.translate(\"global.menu.manual\"),\r\n                        icon: \"pi pi-fw pi-question-circle\",\r\n                        items: [\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.userGuide\"),\r\n                                icon: \"pi pi-fw pi-info-circle\",\r\n                                routerLink: [\"/docs\"],\r\n                            },\r\n                            {\r\n                                label: this.tranService.translate(\"global.menu.integrationGuide\"),\r\n                                icon: \"pi pi-fw pi-info-circle\",\r\n                                routerLink: [\"/docs/integration\"],\r\n                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.GUIDE.INTEGRATION])\r\n                            },\r\n                        ],\r\n\r\n                        // target: \"_blank\"\r\n                    },\r\n                ]\r\n            },\r\n        ];\r\n    }\r\n}\r\n", "<!-- <ul class=\"layout-menu\">\r\n    <ng-container *ngFor=\"let item of model; let i = index;\">\r\n        <li app-menuitem *ngIf=\"!item.separator\" [item]=\"item\" [index]=\"i\" [root]=\"true\"></li>\r\n        <li *ngIf=\"item.separator\" class=\"menu-separator\"></li>\r\n    </ng-container>\r\n</ul> -->\r\n\r\n<!-- <div *ngIf=\"layoutService.typeMenu == 'big'\" (click)=\"layoutService.changeSize('small')\" class=\"button-change-type-menu\" [class]=\"layoutService.isShowMenu == false ? 'hidden': ''\"><div class=\"caret-left\"></div></div> -->\r\n<!-- <div *ngIf=\"layoutService.typeMenu == 'small'\" (click)=\"layoutService.changeSize('big')\" class=\"button-change-type-menu\" [class]=\"layoutService.isShowMenu == false ? 'hidden': ''\"><div class=\"caret-right\"></div></div> -->\r\n\r\n<div *ngIf=\"layoutService.typeMenu == 'big'\" class=\"relative\">\r\n    <div style=\r\n    \"color: white;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    position: sticky;\r\n    top: 0;\r\n    z-index: 9999;\r\n    outline: 0 none;\r\n    cursor: pointer;\r\n    padding: 0.75rem 2rem;\r\n    background-color: #021c34;\r\n    transition: background-color 0.2s, box-shadow 0.2s;\r\n    box-shadow: -5px 12px 19px 0px rgba(0,0,0,0.51);\r\n    -webkit-box-shadow: -5px 12px 19px 0px rgba(0,0,0,0.51);\r\n    -moz-box-shadow: -5px 12px 19px 0px rgba(0,0,0,0.51);\r\n    min-height: 60px;\" class=\"flex flex-row text-lg menuItemExpanded\" id=\"menuToggleBlock\">\r\n        <div class=\"text-lg font-semibold\">Menu</div>\r\n        <div *ngIf=\"layoutService.typeMenu == 'big'\"\r\n        (click)=\"layoutService.changeSize('small')\"\r\n        style=\r\n        \"background-color: #1c3349;\r\n        padding: 5px;\r\n        width: 30px;\r\n        height: 30px;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        border-radius: 50%;\"><i class=\"pi pi-angle-left\"></i></div>\r\n        <!-- <div *ngIf=\"layoutService.typeMenu == 'big'\" (click)=\"layoutService.changeSize('small')\" class=\"button-change-type-menu\" [class]=\"layoutService.isShowMenu == false ? 'hidden': ''\"><div class=\"caret-left\"></div></div> -->\r\n    </div>\r\n    <div class=\"menu-big-list\">\r\n        <ul class=\"layout-menu flex-grow-1\" >\r\n            <ng-container *ngFor=\"let item of model; let i = index;\">\r\n                <li class=\"text-lg menuItemExpanded\" app-menuitem *ngIf=\"!item.separator\" [item]=\"item\" [index]=\"i\" [root]=\"true\"></li>\r\n                <li class=\"text-lg menuItemExpanded\" *ngIf=\"item.separator\" class=\"menu-separator\"></li>\r\n            </ng-container>\r\n        </ul>\r\n<!--        <div style=\"height: 90px;\"></div>-->\r\n    </div>\r\n<!--    Chuyển hotline sang bên phải màn-->\r\n<!--    <div class=\"align-items-end\" style=\" position: sticky;-->\r\n<!--        bottom: 0;-->\r\n<!--        left: 0;-->\r\n<!--        right: 0;-->\r\n<!--        background-color: #021c34;-->\r\n<!--        color: white;-->\r\n<!--        text-align: center;-->\r\n<!--        padding: 10px;-->\r\n<!--        font-weight: bold;-->\r\n<!--        box-shadow: 0 -5px 10px rgba(0,0,0,0.3);-->\r\n<!--        -webkit-box-shadow: -5px -12px 19px 0px rgba(0,0,0,0.51);-->\r\n<!--        -moz-box-shadow: -5px -12px 19px 0px rgba(0,0,0,0.51);\">-->\r\n<!--        <div class=\"flex flex-row justify-content-center align-content-center align-items-center w-full\">-->\r\n<!--            <i class=\"fas fa-phone-volume\" style=\"font-size: 20px; margin-right: 8px;\"></i>-->\r\n<!--            <div style=\"font-size: 15px; font-weight: bold;\">Hotline: 1800 1091</div>-->\r\n<!--        </div>-->\r\n<!--    </div>-->\r\n<!--    <div class=\"font-footer-sbar\" style=\"height: 200px; padding: 15px; color:#959EAD; box-shadow: -5px -12px 19px 0px rgba(0,0,0,0.51);-->\r\n<!--    -webkit-box-shadow: -5px -12px 19px 0px rgba(0,0,0,0.51);-->\r\n<!--    -moz-box-shadow: -5px -12px 19px 0px rgba(0,0,0,0.51);\">-->\r\n<!--        <div class=\"flex flex-row justify-content-start gap-2\">-->\r\n<!--            <i class=\"pi pi-phone\"></i>-->\r\n<!--            <div class=\"font-footer-sbar\">(+84) 38362094</div>-->\r\n<!--        </div>-->\r\n<!--        <div class=\"flex flex-row justify-content-start gap-2 mt-2\">-->\r\n<!--            <i class=\"pi pi-map-marker\"></i>-->\r\n<!--            <div class=\"font-footer-sbar\">124 Hoang Quoc Viet, Cau Giay, Ha Noi</div>-->\r\n<!--        </div>-->\r\n        <!-- <div class=\"flex flex-row justify-content-between\">\r\n            <i class=\"pi pi-user\"></i>\r\n            <div>1.</div>\r\n        </div> -->\r\n<!--        <div style=\"width: 100%; height: 1; border-bottom: 1px solid #2a2f35;\" class=\"pt-2\"></div>-->\r\n<!--        <div class=\"pt-2 font-footer-sbar\">CMP by VNPT-Technology</div>-->\r\n<!--    </div>-->\r\n</div>\r\n<div class=\"menuItemCollaped\" *ngIf=\"layoutService.typeMenu == 'small'\">\r\n    <div style=\"display: flex; padding: 5px;\r\n    box-shadow: -5px 12px 19px 0px rgba(0,0,0,0.51);\r\n    -webkit-box-shadow: -5px 12px 19px 0px rgba(0,0,0,0.51);\r\n    -moz-box-shadow: -5px 12px 19px 0px rgba(0,0,0,0.51);\"class=\"justify-content-center align-items-center\">\r\n        <div\r\n            (click)=\"handleOpenBigSidebar()\"\r\n            style=\r\n            \"background-color: #1c3349;\r\n            color: white;\r\n            padding: 5px;\r\n            width: 30px;\r\n            height: 30px;\r\n            cursor: pointer;\r\n            display: flex;\r\n            justify-content: center;\r\n            margin: 10px 0;\r\n            align-items: center;\r\n            border-radius: 50%;\"><i class=\"pi pi-angle-right\"></i>\r\n        </div>\r\n    </div>\r\n    <!-- <p-tieredMenu [model]=\"model[0].itemsNoLabel\" appendTo=\"body\"></p-tieredMenu> -->\r\n    <div (scroll)=\"handleScroll($event)\" class=\"menu-item-small flex flex-column align-items-center mt-3\"\r\n    style=\"overflow-y: auto;\r\n    width: 60px ;\">\r\n        <div *ngFor=\"let category of model\">\r\n          <div class=\"small-menu-category\">\r\n              <div *ngFor=\"let item of category.items\" appSmallSidebarElement\r\n              [itemInfo]=\"item\">\r\n                <div [ngClass]=\"{'active-route-small': isActiveRoute(item)}\">\r\n                    <i *ngIf=\"item.visible != false\" class=\"{{item.icon}} icon-small-menu mb-3\" styleClass=\"icon-small-menu\">\r\n                    </i>\r\n                </div>\r\n              </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    <div style=\"height: 200px; box-shadow: -5px -12px 19px 0px rgba(0,0,0,0.51);\r\n    -webkit-box-shadow: -5px -12px 19px 0px rgba(0,0,0,0.51);\r\n    -moz-box-shadow: -5px -12px 19px 0px rgba(0,0,0,0.51);\"></div>\r\n</div>\r\n<!-- <p-tieredMenu *ngIf=\"layoutService.typeMenu == 'small'\" [model]=\"model[0].itemsNoLabel\" appendTo=\"body\"></p-tieredMenu> -->\r\n<!-- <p-tooltip for=\"dashboard\" [value]=\"transService.translate('global.menu.dashboard')\"/> -->\r\n"], "mappings": "AAGA,SAASA,aAAa,QAAQ,wBAAwB;AAEtD,SAASC,SAAS,QAAQ,iCAAiC;AAG3D,SAAQC,aAAa,QAAO,oCAAoC;;;;;;;;;;ICqBxDC,EAAA,CAAAC,cAAA,aAUqB;IATrBD,EAAA,CAAAE,UAAA,mBAAAC,2DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,aAAA,CAAAC,UAAA,CAAyB,OAAO,CAAC;IAAA,EAAC;IAStBV,EAAA,CAAAW,SAAA,YAAgC;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;;;IAMnDZ,EAAA,CAAAW,SAAA,aAAuH;;;;;;IAA7CX,EAAA,CAAAa,UAAA,SAAAC,OAAA,CAAa,UAAAC,IAAA;;;;;IACvFf,EAAA,CAAAW,SAAA,aAAwF;;;;;IAF5FX,EAAA,CAAAgB,uBAAA,GAAyD;IACrDhB,EAAA,CAAAiB,UAAA,IAAAC,mDAAA,iBAAuH;IACvHlB,EAAA,CAAAiB,UAAA,IAAAE,mDAAA,iBAAwF;IAC5FnB,EAAA,CAAAoB,qBAAA,EAAe;;;;IAFwCpB,EAAA,CAAAqB,SAAA,GAAqB;IAArBrB,EAAA,CAAAa,UAAA,UAAAC,OAAA,CAAAQ,SAAA,CAAqB;IAClCtB,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAAa,UAAA,SAAAC,OAAA,CAAAQ,SAAA,CAAoB;;;;;IApC1EtB,EAAA,CAAAC,cAAA,aAA8D;IAkBnBD,EAAA,CAAAuB,MAAA,WAAI;IAAAvB,EAAA,CAAAY,YAAA,EAAM;IAC7CZ,EAAA,CAAAiB,UAAA,IAAAO,qCAAA,iBAU2D;IAE/DxB,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,aAA2B;IAEnBD,EAAA,CAAAiB,UAAA,IAAAQ,8CAAA,0BAGe;IACnBzB,EAAA,CAAAY,YAAA,EAAK;;;;IAnBCZ,EAAA,CAAAqB,SAAA,GAAqC;IAArCrB,EAAA,CAAAa,UAAA,SAAAa,MAAA,CAAAjB,aAAA,CAAAkB,QAAA,UAAqC;IAeR3B,EAAA,CAAAqB,SAAA,GAAU;IAAVrB,EAAA,CAAAa,UAAA,YAAAa,MAAA,CAAAE,KAAA,CAAU;;;;;IA0EjC5B,EAAA,CAAAW,SAAA,YACI;;;;IAD6BX,EAAA,CAAA6B,sBAAA,KAAAC,QAAA,CAAAC,IAAA,0BAA0C;;;;;;;;;;IAHjF/B,EAAA,CAAAC,cAAA,cACkB;IAEZD,EAAA,CAAAiB,UAAA,IAAAe,+CAAA,gBACI;IACRhC,EAAA,CAAAY,YAAA,EAAM;;;;;IAJRZ,EAAA,CAAAa,UAAA,aAAAiB,QAAA,CAAiB;IACV9B,EAAA,CAAAqB,SAAA,GAAuD;IAAvDrB,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAiC,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,aAAA,CAAAN,QAAA,GAAuD;IACpD9B,EAAA,CAAAqB,SAAA,GAA2B;IAA3BrB,EAAA,CAAAa,UAAA,SAAAiB,QAAA,CAAAO,OAAA,UAA2B;;;;;IAL3CrC,EAAA,CAAAC,cAAA,UAAoC;IAE9BD,EAAA,CAAAiB,UAAA,IAAAqB,2CAAA,kBAMM;IACVtC,EAAA,CAAAY,YAAA,EAAM;;;;IAPoBZ,EAAA,CAAAqB,SAAA,GAAiB;IAAjBrB,EAAA,CAAAa,UAAA,YAAA0B,YAAA,CAAAC,KAAA,CAAiB;;;;;;IA3BrDxC,EAAA,CAAAC,cAAA,cAAwE;IAM5DD,EAAA,CAAAE,UAAA,mBAAAuC,qDAAA;MAAAzC,EAAA,CAAAI,aAAA,CAAAsC,IAAA;MAAA,MAAAC,OAAA,GAAA3C,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAmC,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IAYX5C,EAAA,CAAAW,SAAA,YAAiC;IAC1DX,EAAA,CAAAY,YAAA,EAAM;IAGVZ,EAAA,CAAAC,cAAA,cAEe;IAFVD,EAAA,CAAAE,UAAA,oBAAA2C,sDAAAC,MAAA;MAAA9C,EAAA,CAAAI,aAAA,CAAAsC,IAAA;MAAA,MAAAK,OAAA,GAAA/C,EAAA,CAAAO,aAAA;MAAA,OAAUP,EAAA,CAAAQ,WAAA,CAAAuC,OAAA,CAAAC,YAAA,CAAAF,MAAA,CAAoB;IAAA,EAAC;IAGhC9C,EAAA,CAAAiB,UAAA,IAAAgC,qCAAA,iBAUM;IACRjD,EAAA,CAAAY,YAAA,EAAM;IACRZ,EAAA,CAAAW,SAAA,cAE8D;IAClEX,EAAA,CAAAY,YAAA,EAAM;;;;IAf4BZ,EAAA,CAAAqB,SAAA,GAAQ;IAARrB,EAAA,CAAAa,UAAA,YAAAqC,MAAA,CAAAtB,KAAA,CAAQ;;;ADpF1C,OAAM,MAAOuB,gBAAiB,SAAQtD,aAAa;EAO/CuD,YAAmB3C,aAA4B,EAAE4C,QAAkB,EACxBC,aAA4B;IACnE,KAAK,CAACD,QAAQ,CAAC;IAFA,KAAA5C,aAAa,GAAbA,aAAa;IACW,KAAA6C,aAAa,GAAbA,aAAa;IAPxD;IACA,KAAA1B,KAAK,GAGS,EAAE;IAOhB,KAAA2B,YAAY,GAAG,CAAC;EAFhB;EAIAX,oBAAoBA,CAAA;IAChB,IAAI,CAACnC,aAAa,CAACC,UAAU,CAAC,KAAK,CAAC;IACpC,IAAI8C,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC;IACxDF,IAAI,EAAEG,MAAM,EAAE;EAClB;EAEAX,YAAYA,CAACY,KAAU;IACnB,IAAIJ,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC;IACxDF,IAAI,EAAEG,MAAM,EAAE;IACd;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IAEA;IACA;IACA;EACF;;EAEAvB,aAAaA,CAACyB,IAAS;IACrB,IAAIA,IAAI,CAACC,UAAU,EAAE;MACnB,MAAMC,GAAG,GAAGF,IAAI,CAACC,UAAU,CAACE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;MAC3C,OAAO,IAAI,CAACC,MAAM,CAACC,QAAQ,CAACH,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,CAACE,MAAM,CAACF,GAAG,CAACI,QAAQ,CAACJ,GAAG,CAAC,IAAI,IAAI,CAACE,MAAM,CAACF,GAAG,GAAC,GAAG,IAAEA,GAAG;;IAGtG,IAAIF,IAAI,CAACrB,KAAK,IAAIqB,IAAI,CAACrB,KAAK,CAAC4B,MAAM,GAAG,CAAC,EAAE;MACvC,OAAOP,IAAI,CAACrB,KAAK,CAAC6B,IAAI,CAACC,OAAO,IAAI,IAAI,CAAClC,aAAa,CAACkC,OAAO,CAAC,CAAC;;IAGhE,OAAO,KAAK;EACd;EAGFC,QAAQA,CAAA;IACJ;IACA,IAAIC,QAAQ,GAAI,IAAI,CAACC,cAAc,CAACC,QAAQ,CAACC,IAAI;IAEjD,IAAI,CAAC/C,KAAK,GAAG,CACT;MACIgD,KAAK,EAAE,EAAE;MACTpC,KAAK,EAAE;MACH;MACA;QACIoC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;QAC1D/C,IAAI,EAAE,uBAAuB;QAC7B+B,UAAU,EAAE,CAAC,YAAY,CAAC;QAC1BzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACC,YAAY,CAACC,SAAS,CAAC;OAC3E;MACD;MACA;QACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;QACxD/C,IAAI,EAAE,mBAAmB;QACzBS,KAAK,EAAE,CACH;UACIoC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;UACxDhB,UAAU,EAAE,CAAC,OAAO,CAAC;UACrBzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACG,GAAG,CAACD,SAAS,CAAC;SAClE,EACD;UACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;UACzD;UACAhB,UAAU,EAAC,CAAC,aAAa,CAAC;UAC1BzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACI,SAAS,CAACF,SAAS,CAAC;SACxE,EACD;UACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;UACzD;UACAhB,UAAU,EAAC,CAAC,gBAAgB,CAAC;UAC7BzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACK,QAAQ,CAACH,SAAS,CAAC;;QAExE;QACA;QACA;QACA;QACA;QAAA,CACH;;QACD7C,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACG,GAAG,CAACD,SAAS,EAAEpF,SAAS,CAACkF,WAAW,CAACI,SAAS,CAACF,SAAS,EAAEpF,SAAS,CAACkF,WAAW,CAACK,QAAQ,CAACH,SAAS,CAAC;OACvJ;MACD;MACA;QACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;QAC5D/C,IAAI,EAAE,wBAAwB;QAC9BS,KAAK,EAAE,CACH;UACIoC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;UACxDhB,UAAU,EAAE,CAAC,uBAAuB,CAAC;UACrCzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACM,MAAM,CAACJ,SAAS,CAAC,CAAC;UACnEK,YAAY,EAAE,IAAI,CAACd,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACC,KAAK,IACrE,IAAI,CAAChB,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACE,QAAQ,IACjE,IAAI,CAACjB,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACG,QAAQ;UACxEC,GAAG,EAAG9F,SAAS,CAAC+F,UAAU,CAACC;SAC9B,EACD;UACIlB,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;UAC3DhB,UAAU,EAAE,CAAC,0BAA0B,CAAC;UACxCzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACM,MAAM,CAACJ,SAAS,CAAC,CAAC;UACnEK,YAAY,EAAE,IAAI,CAACd,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACC,KAAK,IACrE,IAAI,CAAChB,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACE,QAAQ,IACjE,IAAI,CAACjB,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACG,QAAQ;UACxEC,GAAG,EAAG9F,SAAS,CAAC+F,UAAU,CAACE;SAC9B,EAAC;UACEnB,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;UACzDhB,UAAU,EAAE,CAAC,wBAAwB,CAAC;UACtCzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACM,MAAM,CAACJ,SAAS,CAAC,CAAC;UACnEK,YAAY,EAAE,IAAI,CAACd,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACC,KAAK,IACrE,IAAI,CAAChB,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACE,QAAQ,IACjE,IAAI,CAACjB,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACG,QAAQ;UACxEC,GAAG,EAAG9F,SAAS,CAAC+F,UAAU,CAACG,SAAS;UACpCC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAChC,MAAM,CAACiC,QAAQ,CAAC,CAAC,wBAAwB,CAAC,CAAC;UAC/D1D,KAAK,EAAE,CACH;YACIoC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;YAC9DhB,UAAU,EAAE,CAAC,6BAA6B,CAAC;YAC3CzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACM,MAAM,CAACJ,SAAS,CAAC;WACrE;SAER,EACD;UACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;UAC1DhB,UAAU,EAAE,CAAC,yBAAyB,CAAC;UACvCzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACM,MAAM,CAACJ,SAAS,CAAC,CAAC;UACnEK,YAAY,EAAE,IAAI,CAACd,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACC,KAAK,IACrE,IAAI,CAAChB,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACE,QAAQ;UACxEE,GAAG,EAAG9F,SAAS,CAAC+F,UAAU,CAACM;SAC9B,EACD;UACIvB,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;UACzDhB,UAAU,EAAE,CAAC,uBAAuB,CAAC;UACrCzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACM,MAAM,CAACJ,SAAS,CAAC,CAAC,KAC3D,IAAI,CAACT,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACC,KAAK,IAC9D,IAAI,CAAChB,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACE,QAAQ,IACjE,IAAI,CAACjB,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACY,QAAQ,CACpE;UACLb,YAAY,EAAE,IAAI,CAACd,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACC,KAAK,IACrE,IAAI,CAAChB,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACE,QAAQ;UACxEE,GAAG,EAAG9F,SAAS,CAAC+F,UAAU,CAACQ;SAC9B,EACD;UACIzB,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;UAC9DhB,UAAU,EAAE,CAAC,sBAAsB,CAAC;UACpCzB,OAAO,EAAE,IAAI,CAACoC,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACC,KAAK,IAAI,IAAI,CAAChB,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACE;SACvI,CACJ;QACDrD,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACM,MAAM,CAACJ,SAAS,CAAC,CAAC;QACnEK,YAAY,EAAE,IAAI,CAACd,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACC,KAAK,IACrE,IAAI,CAAChB,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACE,QAAQ,IACjE,IAAI,CAACjB,cAAc,CAACC,QAAQ,CAACC,IAAI,IAAI7E,SAAS,CAAC0F,SAAS,CAACG,QAAQ;QACxEC,GAAG,EAAG9F,SAAS,CAAC+F,UAAU,CAACP;OAC9B;MACD;MACA;QACIV,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;QAC3D/C,IAAI,EAAE,wBAAwB;QAC9BS,KAAK,EAAE,CACH;UACIoC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;UAC3DhB,UAAU,EAAE,CAAC,UAAU,CAAC;UACxBzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACsB,MAAM,CAACpB,SAAS,CAAC;SACrE,CACJ;QACD7C,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACsB,MAAM,CAACpB,SAAS,CAAC;OACrE;MACD;MACA;QACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACvD/C,IAAI,EAAE,kBAAkB;QACxBS,KAAK,EAAE,CACH;UACIoC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;UAC3DhB,UAAU,EAAE,CAAC,SAAS,CAAC;UACvBzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACuB,OAAO,CAACrB,SAAS,CAAC;SACtE,CACJ;QACD7C,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACuB,OAAO,CAACrB,SAAS,CAAC;OACtE;MACD;MACA;QACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,kBAAkB,CAAC;QACrD/C,IAAI,EAAE,kBAAkB;QACxBS,KAAK,EAAE,CACH;UACIoC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;UAC9DhB,UAAU,EAAE,CAAC,SAAS,CAAC;UACvBzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACwB,KAAK,CAACtB,SAAS,CAAC;SAEpE,EACD;UACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,iCAAiC,CAAC;UACpEhB,UAAU,EAAE,CAAC,yBAAyB,CAAC;UACvCzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACyB,qBAAqB,CAACvB,SAAS,CAAC;SACpF,EACD;UACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;UAC7DhB,UAAU,EAAE,CAAC,iBAAiB,CAAC;UAC/BzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAAC0B,aAAa,CAACxB,SAAS,CAAC;SAC5E,CACJ;QACD7C,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACwB,KAAK,CAACtB,SAAS,EAAEpF,SAAS,CAACkF,WAAW,CAACyB,qBAAqB,CAACvB,SAAS,EAACpF,SAAS,CAACkF,WAAW,CAAC0B,aAAa,CAACxB,SAAS,CAAC;OACzK;MACD;MACA;QACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;QAClE/C,IAAI,EAAE,oBAAoB;QAC1BS,KAAK,EAAE,CACH;UACIoC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;UACrEhB,UAAU,EAAC,CAAC,4BAA4B,CAAC;UACzCzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAAC2B,QAAQ,CAACC,WAAW,CAAC;SACzE,EACD;UACIhC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;UAChEhB,UAAU,EAAC,CAAC,gCAAgC,CAAC;UAC7CzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAAC2B,QAAQ,CAACE,UAAU,CAAC;SACxE;QACD;QACA;QACA;QACA;QACA;UACIjC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;UAC9DhB,UAAU,EAAC,CAAC,oBAAoB,CAAC;UACjCzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAAC2B,QAAQ,CAACG,mBAAmB,CAAC;SACjF,EACD;UACIlC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;UAC7DhB,UAAU,EAAC,CAAC,+BAA+B,CAAC;UAC5CzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAAC+B,WAAW,CAAC7B,SAAS,CAAC;SAC1E,EACD;UACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;UAC/DhB,UAAU,EAAC,CAAC,kCAAkC,CAAC;UAC/CzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACgC,gBAAgB,CAAC9B,SAAS,CAAC;SAC/E,CAEJ;QACD;QACA7C,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAAC2B,QAAQ,CAACC,WAAW,EAAE9G,SAAS,CAACkF,WAAW,CAAC2B,QAAQ,CAACE,UAAU,EAAE/G,SAAS,CAACkF,WAAW,CAAC2B,QAAQ,CAACG,mBAAmB,EAAEhH,SAAS,CAACkF,WAAW,CAAC+B,WAAW,CAAC7B,SAAS,CAAC;OACrN;MACD;MACA;QACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;QAC/D/C,IAAI,EAAE,kBAAkB;QACxBS,KAAK,EAAE,CACH;UACIoC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;UACzDhB,UAAU,EAAE,CAAC,QAAQ,CAAC;UACtBzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACiC,WAAW,CAAC/B,SAAS,CAAC;SAC1E,EACD;UACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;UAC7DhB,UAAU,EAAE,CAAC,kBAAkB,CAAC;UAChCzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACkC,eAAe,CAACC,aAAa,CAAC;SAClF,CACJ;QACD9E,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACiC,WAAW,CAAC/B,SAAS,EAAEpF,SAAS,CAACkF,WAAW,CAACkC,eAAe,CAACC,aAAa,CAAC;OAC/H;MACD;MACA;QACIvC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;QAC5D/C,IAAI,EAAE,mBAAmB;QACzBS,KAAK,EAAE,CACH;UACIoC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;UAC5DhB,UAAU,EAAE,CAAC,WAAW,CAAC;UACzBzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACoC,OAAO,CAAClC,SAAS,CAAC;SACtE,EACD;UACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;UAC1DhB,UAAU,EAAE,CAAC,QAAQ,CAAC;UACtBzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACqC,IAAI,CAACnC,SAAS,CAAC;SACnE,EACD;UACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;UAChEhB,UAAU,EAAE,CAAC,cAAc,CAAC;UAC5BzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACsC,UAAU,CAACpC,SAAS,CAAC;SACzE,EACD;UACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;UAC3DhB,UAAU,EAAE,CAAC,WAAW,CAAC;UACzBzB,OAAO,EAAEmC,QAAQ,IAAI1E,SAAS,CAAC0F,SAAS,CAACY,QAAQ,IAAI5B,QAAQ,IAAI1E,SAAS,CAAC0F,SAAS,CAAC+B;SACxF,EACD;UACI3C,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;UAClEhB,UAAU,EAAE,CAAC,mBAAmB,CAAC;UACjCzB,OAAO,EAAEmC,QAAQ,IAAI1E,SAAS,CAAC0F,SAAS,CAACY,QAAQ,IAAI5B,QAAQ,IAAI1E,SAAS,CAAC0F,SAAS,CAAC+B;SACxF,EACD;UACI3C,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,eAAe,CAAC;UAClDhB,UAAU,EAAE,CAAC,wBAAwB,CAAC;UACtCzB,OAAO,EAAE,CAACmC,QAAQ,IAAI1E,SAAS,CAAC0F,SAAS,CAACC,KAAK,IAAIjB,QAAQ,IAAI1E,SAAS,CAAC0F,SAAS,CAACE,QAAQ,IACxFlB,QAAQ,IAAI1E,SAAS,CAAC0F,SAAS,CAACG,QAAQ,KAAK,IAAI,CAACZ,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACwC,GAAG,CAACtC,SAAS,CAAC;SACxG,EACF;UACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;UACxDhB,UAAU,EAAE,CAAC,kBAAkB,CAAC;UAChCzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACoC,OAAO,CAACK,cAAc,CAAC;SAC3E,CACJ;QACDpF,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACoC,OAAO,CAAClC,SAAS,EAACpF,SAAS,CAACkF,WAAW,CAACqC,IAAI,CAACnC,SAAS,EAACpF,SAAS,CAACkF,WAAW,CAACsC,UAAU,CAACpC,SAAS,CAAC;OACtJ;MACD;MACA;QACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;QAC7D/C,IAAI,EAAE,mBAAmB;QACzBS,KAAK,EAAE,CACH;UACIoC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;UAC7DhB,UAAU,EAAE,CAAC,YAAY,CAAC;UAC1BzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACoB,QAAQ,CAAClB,SAAS,CAAC;SACvE,CACJ;QACD7C,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACoB,QAAQ,CAAClB,SAAS,CAAC;OACvE;MACD;MACA;QACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;QAC3D/C,IAAI,EAAE,mBAAmB;QACzB+B,UAAU,EAAE,CAAC,WAAW,CAAC;QACzBzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAACqB,QAAQ,CAACnB,SAAS,CAAC;OACvE;MAGD;MACA;QACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;QAC1D/C,IAAI,EAAE,mBAAmB;QACzBS,KAAK,EAAE,CACH;UACIoC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;UAC1DhB,UAAU,EAAE,CAAC,SAAS;SACzB,CACJ;QACDzB,OAAO,EAAE;OACZ;MAID;MACA;QACIuC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;QAC7D/C,IAAI,EAAE,sBAAsB;QAC5B+B,UAAU,EAAE,CAAC,iBAAiB,CAAC;QAC/BzB,OAAO,EAAE;OACZ;MACD;MACA;QACIuC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,iBAAiB,CAAC;QACpD/C,IAAI,EAAE,kBAAkB;QACxB+B,UAAU,EAAE,CAAC,OAAO,CAAC;QACrBzB,OAAO,EAAE;OACZ;MACD;MACA;QACIuC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACvD/C,IAAI,EAAE,wBAAwB;QAC9BS,KAAK,EAAE,CACH;UACIoC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;UAC9DhB,UAAU,EAAE,CAAC,0BAA0B,CAAC;UACxCzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAAC0C,cAAc,CAACxC,SAAS,CAAC;SAC7E,EACD;UACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;UACrEhB,UAAU,EAAE,CAAC,wCAAwC;SACxD,EACD;UACIc,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC;UACnEhB,UAAU,EAAE,CAAC,gCAAgC,CAAC;UAC9CzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAAC2C,oBAAoB,CAACzC,SAAS,CAAC;SACnF,CACJ;QACD7C,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAAC0C,cAAc,CAACxC,SAAS,EAAEpF,SAAS,CAACkF,WAAW,CAAC2C,oBAAoB,CAACzC,SAAS,CAAC,EAAE,IAAI;OACzI;MAED;MACA;QACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;QAC9D/C,IAAI,EAAE,iBAAiB;QACvB+B,UAAU,EAAE,CAAC,gBAAgB,CAAC;QAC9BzB,OAAO,EAAE;OACZ;MACD;MACA;QACIuC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;QAC7D/C,IAAI,EAAE,oBAAoB;QAC1B+B,UAAU,EAAE,CAAC,eAAe,CAAC;QAC7BzB,OAAO,EAAE;OACZ;MACD;MACA;QACIuC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACvD/C,IAAI,EAAE,sBAAsB;QAC5B+B,UAAU,EAAE,CAAC,eAAe,CAAC;QAC7BzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAAC4C,oBAAoB,CAAC1C,SAAS,CAAC;OACnF;MACA;MACD;QACIN,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACvD/C,IAAI,EAAE,6BAA6B;QACnCS,KAAK,EAAE,CACH;UACIoC,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;UAC1D/C,IAAI,EAAE,yBAAyB;UAC/B+B,UAAU,EAAE,CAAC,OAAO;SACvB,EACD;UACIc,KAAK,EAAE,IAAI,CAACC,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC;UACjE/C,IAAI,EAAE,yBAAyB;UAC/B+B,UAAU,EAAE,CAAC,mBAAmB,CAAC;UACjCzB,OAAO,EAAE,IAAI,CAAC0C,WAAW,CAAC,CAACjF,SAAS,CAACkF,WAAW,CAAC6C,KAAK,CAACC,WAAW,CAAC;SACtE;QAGL;OACH;KAER,CACJ;EACL;;;;uBAvbS3E,gBAAgB,EAAAnD,EAAA,CAAA+H,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAjI,EAAA,CAAA+H,iBAAA,CAAA/H,EAAA,CAAAkI,QAAA,GAAAlI,EAAA,CAAA+H,iBAAA,CAQLhI,aAAa;IAAA;EAAA;;;YARxBoD,gBAAgB;MAAAgF,SAAA;MAAAC,QAAA,GAAApI,EAAA,CAAAqI,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnB7B3I,EAAA,CAAAiB,UAAA,IAAA4H,+BAAA,iBA6EM;UACN7I,EAAA,CAAAiB,UAAA,IAAA6H,+BAAA,iBAwCM;;;UAtHA9I,EAAA,CAAAa,UAAA,SAAA+H,GAAA,CAAAnI,aAAA,CAAAkB,QAAA,UAAqC;UA8EZ3B,EAAA,CAAAqB,SAAA,GAAuC;UAAvCrB,EAAA,CAAAa,UAAA,SAAA+H,GAAA,CAAAnI,aAAA,CAAAkB,QAAA,YAAuC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}