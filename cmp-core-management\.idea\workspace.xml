<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="05b7477c-be90-43fd-ad83-75b6661226e8" name="Changes" comment="cập nhật tài liệu 3rd api python và SDK Python" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="dev2.4" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="SOFT" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zceHcNe9b2nIKb5PwXwKKrLjlW" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.cmp-core-management [clean].executor": "Run",
    "Maven.cmp-core-management [install].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.CmpCoreMgmtApplication.executor": "Run",
    "git-widget-placeholder": "dev2.4",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/cmp/cmp-core-management/storage/integration",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\cmp\cmp-core-management\storage\integration" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="CmpCoreMgmtApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="cmp-core-management" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="vn.vnpt.cmp.CmpCoreMgmtApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="vn.vnpt.cmp.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.CmpCoreMgmtApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="05b7477c-be90-43fd-ad83-75b6661226e8" name="Changes" comment="" />
      <created>1752034701908</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752034701908</updated>
      <workItem from="1752034703054" duration="2859000" />
      <workItem from="1752046013448" duration="663000" />
      <workItem from="1752047012032" duration="8763000" />
      <workItem from="1752460232334" duration="2856000" />
      <workItem from="1752552516478" duration="775000" />
      <workItem from="1752553921701" duration="9431000" />
      <workItem from="1752717652780" duration="2911000" />
      <workItem from="1752740919452" duration="668000" />
      <workItem from="1752825771183" duration="8173000" />
      <workItem from="1753328427916" duration="9199000" />
      <workItem from="1753429472175" duration="3594000" />
      <workItem from="1753843300536" duration="5271000" />
    </task>
    <task id="LOCAL-00001" summary="cập nhật tài liệu 3rd api">
      <option name="closed" value="true" />
      <created>1753065449834</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753065449834</updated>
    </task>
    <task id="LOCAL-00002" summary="cập nhật tài liệu 3rd api python và SDK Python">
      <option name="closed" value="true" />
      <created>1753349524526</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753349524526</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="hungnd" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="cập nhật tài liệu 3rd api" />
    <MESSAGE value="cập nhật tài liệu 3rd api python" />
    <MESSAGE value="cập nhật tài liệu 3rd api python và SDK Python" />
    <option name="LAST_COMMIT_MESSAGE" value="cập nhật tài liệu 3rd api python và SDK Python" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/vn/vnpt/cmp/service/WalletService.java</url>
          <line>176</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/vn/vnpt/cmp/service/WalletService.java</url>
          <line>339</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/vn/vnpt/cmp/service/WalletService.java</url>
          <line>337</line>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>