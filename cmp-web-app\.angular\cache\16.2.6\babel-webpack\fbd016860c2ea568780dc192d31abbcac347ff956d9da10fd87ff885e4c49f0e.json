{"ast": null, "code": "export default {\n  User: {\n    User: \"<PERSON><PERSON><PERSON> khoản\",\n    createUser: \"Tạo tài khoản\",\n    deleteUser: \"Xóa tài khoản\",\n    getUser: \"Xem chi tiết tài khoản\",\n    searchUser: \"Xem danh sách tài khoản\",\n    updateUser: \"Cập nhật thông tin tài khoản\",\n    getProfileUser: \"Xem chi tiết thông tin cá nhân\",\n    updateProfile: \"Cập nhật thông tin cá nhân\",\n    changeManageData: \"Chuyển quyền quản lý dữ liệu\"\n  },\n  SimGroup: {\n    SimGroup: \"Nhóm thuê bao\",\n    createSimGroup: \"Tạo nhóm thuê bao\",\n    deleteSimGroup: \"Xóa nhóm thuê bao\",\n    getSimGroup: \"Xem chi tiết nhóm thuê bao\",\n    searchSimGroup: \"Xem danh sách nhóm thuê bao\",\n    updateSimGroup: \"<PERSON>ậ<PERSON> nhật nhóm thuê bao\"\n  },\n  Sim: {\n    Sim: \"Thuê bao\",\n    createSim: \"Tạo thuê bao\",\n    deleteSim: \"Xóa thuê bao\",\n    getSim: \"Xem chi tiết thuê bao\",\n    searchSim: \"Xem danh sách thuê bao\",\n    updateSim: \"Cập nhật thuê bao\"\n  },\n  RatingPlanSim: {\n    RatingPlanSim: \"Gói cước thuê bao\",\n    setRateSim: \"Đăng ký gói cước cho thuê bao\",\n    cancelRateSim: \"Hủy gói cước cho thuê bao\",\n    registerByFile: \"Đăng ký gói cước theo file\",\n    registerByList: \"Đăng ký gói cước theo nhóm\"\n  },\n  Role: {\n    Role: \"Nhóm quyền\",\n    createRole: \"Tạo nhóm quyền\",\n    deleteRole: \"Xóa nhóm quyền\",\n    getRole: \"Xem chi tiết nhóm quyền\",\n    searchRole: \"Xem danh sách nhóm quyền\",\n    updateRole: \"Cập nhật nhóm quyền\"\n  },\n  Report: {\n    Report: \"Báo cáo\",\n    getListReportSimStatus: \"Get List Report Subcriber Status\",\n    exportReportSimRatingPLan: \"Export Report Subcriber Rating Plan\",\n    getListReportRequestApiLog: \"Get List Report Report Api Log\",\n    searchReportRequestApi: \"Search Report Request Api\",\n    getListReportMonthly: \"Get List Report Monthly\",\n    getListReportManageSim: \"Get List Report Manage Subcriber\",\n    getListReportHistorySim: \"Get List Report History SSubcriberim\",\n    getListReportDetailSim: \"Get List Report Detail Subcriber\",\n    getListReportCheckPostage: \"Get List Report Check Postage\",\n    getListReportContract: \"Get List Report Contract\",\n    getListReportBillingCustomer: \"Get List Report Billing Customer\"\n  },\n  RatingPlan: {\n    RatingPlan: \"Gói cước\",\n    changeStatusRatingPlan: \"Kích hoạt/Tạm dừng gói cước\",\n    approveRatingPlan: \"Phê duyệt gói cước\",\n    createRatingPlan: \"Tạo gói cước\",\n    deleteRatingPlan: \"Xóa gói cước\",\n    getRatingPlan: \"Xem chi tiết gói cước\",\n    issueRatingPlan: \"Gán gói cước cho cấp dưới\",\n    searchRatingPlan: \"Xem danh sách gói cước\",\n    updateRatingPlan: \"Cập nhật gói cước\"\n  },\n  Permission: {\n    Permission: \"Quyền\",\n    getPermission: \"Xem chi tiết quyền\",\n    searchPermission: \"Xem danh sách quyền\"\n  },\n  Device: {\n    Device: \"Thiết bị\",\n    createDevice: \"Tạo thiết bị\",\n    deleteDevice: \"Xóa thiết bị\",\n    getDevice: \"Xem chi tiết thiết bị\",\n    searchDevice: \"Xem danh sách thiết bị\",\n    updateDevice: \"Cập nhật thiết bị\"\n  },\n  Customer: {\n    Customer: \"Khách hàng\",\n    changeStatusCustomer: \"Kích hoạt khách hàng\",\n    getCustomer: \"Xem chi tiết khách hàng\",\n    searchCustomer: \"Xem danh sách khách hàng\",\n    updateCustomer: \"Cập nhật khách hàng\",\n    deleteCustomer: \"Xóa khách hàng\",\n    createCustomer: \"Tạo khách hàng\"\n  },\n  CustomAlert: {\n    CustomAlert: \"Cấu hình cảnh báo\",\n    createAlertConfig: \"Tạo cấu hình cảnh báo\",\n    deleteAlertConfig: \"Xóa cấu hình cảnh báo\",\n    getAlertConfig: \"Xem chi tiết cấu hình cảnh báo\",\n    searchAlertConfig: \"Xem danh sách cầu hình cảnh báo\",\n    updateAlertConfig: \"Cập nhật cấu hình cảnh báo\"\n  },\n  AlertRecvGrp: {\n    AlertRecvGrp: \"Nhóm nhận cảnh báo\",\n    createAlertRecvGrp: \"Tạo nhóm nhận cảnh báo\",\n    updateAlertRecvGrp: \"Cập nhật nhóm nhận cảnh báo\",\n    getAlertRecvGrp: \"Xem chi tiết nhóm nhận cảnh báo\",\n    deleteAlertRecvGrp: \"Xóa nhóm nhận cảnh báo\",\n    searchAlertRecvGrp: \"Danh sách nhóm nhận cảnh báo\"\n  },\n  RptCfg: {\n    RptCfg: \"Cấu hình báo cáo động\",\n    createRptCfg: \"Tạo cấu hình báo cáo động\",\n    updateRptCfg: \"Cập nhật cấu hình báo cáo động\",\n    getRptCfg: \"Xem chi tiết cấu hình báo cáo động\",\n    deleteRptCfg: \"Xóa cấu hình báo cáo động\",\n    searchRptCfg: \"Danh sách cấu hình báo cáo động\"\n  },\n  RptRecvGrp: {\n    RptRecvGrp: \"Nhóm nhận báo cáo động\",\n    createRptRecvGrp: \"Tạo nhóm nhận báo cáo động\",\n    updateRptRecvGrp: \"Cập nhật nhóm nhận báo cáo động\",\n    getRptRecvGrp: \"Xem chi tiết nhóm nhận báo cáo động\",\n    deleteRptRecvGrp: \"Xóa nhóm nhận báo cáo động\",\n    searchRptRecvGrp: \"Danh sách nhóm nhận báo cáo động\"\n  },\n  RptSend: {\n    RptSend: \"Cấu hình gửi mail báo cáo\",\n    updateRptSend: \"Cập nhật cấu hình gửi mail báo cáo\"\n  },\n  Contract: {\n    Contract: \"Hợp đồng\",\n    getContract: \"Xem chi tiết hợp đồng\",\n    searchContract: \"Xem danh sách hợp đồng\"\n  },\n  Configuration: {\n    Configuration: \"Cấu hình hệ thống\",\n    getConfiguration: \"Xem chi tiết thông số cấu hình hệ thống\",\n    searchConfiguration: \"Tìm kiếm thông số cấu hình hệ thống\",\n    updateConfiguration: \"Cập nhật thông số cấu hình hệ thống\"\n  },\n  ApnSim: {\n    ApnSim: \"APN Sim\",\n    issueApnSim: \"Issue APN Sim\",\n    searchApnSim: \"Danh sách APN thuê bao\",\n    getApnSim: \"Chi tiết APN thuê bao\"\n  },\n  Apn: {\n    Apn: \"Apn\",\n    activeApn: \"Active APN\",\n    cancelApn: \"Cancel APN\",\n    completeApn: \"Complete APN\",\n    createApn: \"Tạo APN\",\n    deactiveApn: \"Deactive APN\",\n    getApn: \"Xem chi tiết APN\",\n    issueApn: \"Issue APN\",\n    searchApn: \"Danh sách APN\",\n    sentEmailApn: \"Send Email APN\",\n    updateApn: \"Cập nhật APN\"\n  },\n  AlertLog: {\n    AlertLog: \"Lịch sử cảnh báo\",\n    getAlertLog: \"Xem chi tiết lịch sử cảnh báo\",\n    searchAlertLog: \"Xem danh sách lịch sử cảnh báo\"\n  },\n  Alert: {\n    Alert: \"Cảnh báo\",\n    ackAlert: \"Xác nhận cảnh báo\",\n    getAlert: \"Xem chi tiết cảnh báo\",\n    searchAlert: \"Xem danh sách quy tắc\",\n    changeStatusAlert: \"Đổi trạng thái cảnh báo\",\n    createAlert: \"Tạo cảnh báo\",\n    updateAlert: \"Cập nhật cảnh báo\",\n    deleteAlert: \"Xóa cảnh báo\",\n    createAlertWalletThreshold: \"Tạo cảnh báo ví vượt ngưỡng\",\n    createAlertWalletExpiry: \"Tạo cảnh báo ví hết hạn\",\n    updateAlertWalletThreshold: \"Cập nhật cảnh báo ví vượt ngưỡng\",\n    updateAlertWalletExpiry: \"Cập nhật cảnh báo ví hết hạn\"\n  },\n  RptContent: {\n    RptContent: \"Dữ liệu báo cáo động\"\n  },\n  DynamicChart: {\n    DynamicChart: \"Biểu đồ động\",\n    getDashBoardContent: \"Xem Nội dung Dashboard\"\n  },\n  CnfDynamicChart: {\n    CnfDynamicChart: \"Cấu hình biểu đồ động\",\n    searchCnfDynamicChart: \"Tìm kiếm cấu hình biểu đồ động\",\n    getCnfDynamicChart: \"Xem chi tiết cấu hình biểu đồ động\",\n    updateCnfDynamicChart: \"Cập nhật cấu hình biểu đồ động\",\n    createCnfDynamicChart: \"Tạo cấu hình biểu đồ động\",\n    deleteCnfDynamicChart: \"Xóa cấu hình biểu đồ động\"\n  },\n  Log: {\n    Log: \"Nhật ký hoạt động\"\n  },\n  Ticket: {\n    Ticket: \"Quản lý yêu cầu\",\n    getTicket: \"Xem danh sách yêu cầu\",\n    createTicket: \"Tạo yêu cầu\",\n    updateTicket: \"Cập nhật yêu cầu\"\n  },\n  Policy: {\n    Policy: \"Điều khoản và chính sách\",\n    getPersonalDataPolicy: \"Chính sách bảo vệ dữ liệu cá nhân\"\n  },\n  Wallet: {\n    Wallet: \"Quản lý lưu lượng\",\n    searchWallet: \"Tìm kiếm và liệt kê toàn bộ danh sách ví\",\n    accuracyWallet: \"Thêm ví (Xác thực ví)\",\n    shareWallet: \"Chia sẻ\",\n    createShareInfo: \"Thêm người nhận chia sẻ (Cho phép thêm trên web và file)\",\n    searchShareInfo: \"Tìm kiếm và liệt kê toàn bộ danh sách chia sẻ\",\n    walletHistory: \"Xem lịch sử chia sẻ\"\n    // alertWalletThreshold: \"Cảnh báo ví lưu lượng vượt ngưỡng giá trị\",\n    // alertWalletExpiry: \"Cảnh báo hết hạn ví\",\n  },\n\n  ShareGroup: {\n    ShareGroup: \"Nhóm chia sẻ\",\n    searchShareGroup: \"Hiển thị danh sách nhóm chia sẻ\",\n    createShareGroup: \"Tạo nhóm chia sẻ\",\n    editShareGroup: \"Chỉnh sửa nhóm chia sẻ\",\n    detailShareGroup: \"Xem chi tiết nhóm chia sẻ\",\n    deleteShareGroup: \"Xóa nhóm chia sẻ\"\n  },\n  Diagnose: {\n    Diagnose: \"Chẩn đoán\",\n    searchDiagnose: \"Tra cứu chẩn đoán\"\n  },\n  'API Partner': {\n    'API Partner': \"Cấp quyền API\"\n  },\n  Guide: {\n    Guide: \"Hướng dẫn sử dụng\",\n    guideIntegration: \"Xem hướng dẫn tích hợp\"\n  }\n};", "map": {"version": 3, "names": ["User", "createUser", "deleteUser", "getUser", "searchUser", "updateUser", "getProfileUser", "updateProfile", "changeManageData", "SimGroup", "createSimGroup", "deleteSimGroup", "getSimGroup", "searchSimGroup", "updateSimGroup", "<PERSON>m", "createSim", "deleteSim", "getSim", "searchSim", "updateSim", "Rating<PERSON>lan<PERSON>im", "setRateSim", "cancelRateSim", "registerByFile", "registerByList", "Role", "createRole", "deleteRole", "getRole", "searchRole", "updateRole", "Report", "getListReportSimStatus", "exportReportSimRatingPLan", "getListReportRequestApiLog", "searchReportRequestApi", "getListReportMonthly", "getListReportManageSim", "getListReportHistorySim", "getListReportDetailSim", "getListReportCheckPostage", "getListReportContract", "getListReportBillingCustomer", "RatingPlan", "changeStatusRatingPlan", "approveRatingPlan", "createRatingPlan", "deleteRatingPlan", "getRatingPlan", "issueRatingPlan", "searchRatingPlan", "updateRatingPlan", "Permission", "getPermission", "searchPermission", "<PERSON><PERSON>", "createDevice", "deleteDevice", "getDevice", "searchDevice", "updateDevice", "Customer", "changeStatusCustomer", "getCustomer", "searchCustomer", "updateCustomer", "deleteCustomer", "createCustomer", "CustomAlert", "createAlertConfig", "deleteAlertConfig", "getAlertConfig", "searchAlertConfig", "updateAlertConfig", "AlertRecvGrp", "createAlertRecvGrp", "updateAlertRecvGrp", "getAlertRecvGrp", "deleteAlertRecvGrp", "searchAlertRecvGrp", "RptCfg", "createRptCfg", "updateRptCfg", "getRptCfg", "deleteRptCfg", "searchRptCfg", "RptRecvGrp", "createRptRecvGrp", "updateRptRecvGrp", "getRptRecvGrp", "deleteRptRecvGrp", "searchRptRecvGrp", "RptSend", "updateRptSend", "Contract", "getContract", "searchContract", "Configuration", "getConfiguration", "searchConfiguration", "updateConfiguration", "ApnSim", "issueApnSim", "searchApnSim", "getApnSim", "Apn", "activeApn", "cancelApn", "completeApn", "createApn", "deactiveApn", "getApn", "issueApn", "searchApn", "sentEmailApn", "updateApn", "<PERSON><PERSON><PERSON><PERSON>", "getAlertLog", "searchAlertLog", "<PERSON><PERSON>", "a<PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON>", "searchAlert", "changeStatusAlert", "createAlert", "updateAlert", "delete<PERSON><PERSON>t", "createAlertWalletThreshold", "createAlertWalletExpiry", "updateAlertWalletThreshold", "updateAlertWalletExpiry", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DynamicChart", "getDashBoardContent", "CnfDynamicChart", "searchCnfDynamicChart", "getCnfDynamicChart", "updateCnfDynamicChart", "createCnfDynamicChart", "deleteCnfDynamicChart", "Log", "Ticket", "getTicket", "createTicket", "updateTicket", "Policy", "getPersonalDataPolicy", "Wallet", "searchWallet", "accuracyWallet", "shareWallet", "createShareInfo", "searchShareInfo", "walletHistory", "ShareGroup", "searchShareGroup", "createShareGroup", "editShareGroup", "detailShareGroup", "deleteShareGroup", "Diagnose", "searchDiagnose", "Guide", "guideIntegration"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\vi\\permission.ts"], "sourcesContent": ["export default {\r\n    User: {\r\n        User: \"<PERSON><PERSON><PERSON> khoản\",\r\n        createUser: \"Tạo tài khoản\",\r\n        deleteUser: \"Xóa tài khoản\",\r\n        getUser: \"Xem chi tiết tài khoản\",\r\n        searchUser: \"Xem danh sách tài khoản\",\r\n        updateUser: \"Cập nhật thông tin tài khoản\",\r\n        getProfileUser: \"Xem chi tiết thông tin cá nhân\",\r\n        updateProfile: \"Cập nhật thông tin cá nhân\",\r\n        changeManageData: \"Chuyển quyền quản lý dữ liệu\"\r\n    },\r\n    SimGroup: {\r\n        SimGroup: \"Nhóm thuê bao\",\r\n        createSimGroup: \"Tạo nhóm thuê bao\",\r\n        deleteSimGroup: \"Xóa nhóm thuê bao\",\r\n        getSimGroup: \"Xem chi tiết nhóm thuê bao\",\r\n        searchSimGroup: \"Xem danh sách nhóm thuê bao\",\r\n        updateSimGroup: \"<PERSON>ậ<PERSON> nhật nhóm thuê bao\"\r\n    },\r\n    Sim: {\r\n        Sim: \"Thuê bao\",\r\n        createSim: \"Tạo thuê bao\",\r\n        deleteSim: \"Xóa thuê bao\",\r\n        getSim: \"Xem chi tiết thuê bao\",\r\n        searchSim: \"Xem danh sách thuê bao\",\r\n        updateSim: \"Cập nhật thuê bao\"\r\n    },\r\n    RatingPlanSim:{\r\n        RatingPlanSim: \"Gói cước thuê bao\",\r\n        setRateSim: \"Đăng ký gói cước cho thuê bao\",\r\n        cancelRateSim: \"Hủy gói cước cho thuê bao\",\r\n        registerByFile: \"Đăng ký gói cước theo file\",\r\n        registerByList: \"Đăng ký gói cước theo nhóm\"\r\n    },\r\n    Role: {\r\n        Role: \"Nhóm quyền\",\r\n        createRole: \"Tạo nhóm quyền\",\r\n        deleteRole: \"Xóa nhóm quyền\",\r\n        getRole: \"Xem chi tiết nhóm quyền\",\r\n        searchRole: \"Xem danh sách nhóm quyền\",\r\n        updateRole: \"Cập nhật nhóm quyền\"\r\n    },\r\n    Report: {\r\n        Report: \"Báo cáo\",\r\n        getListReportSimStatus: \"Get List Report Subcriber Status\",\r\n        exportReportSimRatingPLan: \"Export Report Subcriber Rating Plan\",\r\n        getListReportRequestApiLog: \"Get List Report Report Api Log\",\r\n        searchReportRequestApi: \"Search Report Request Api\",\r\n        getListReportMonthly: \"Get List Report Monthly\",\r\n        getListReportManageSim: \"Get List Report Manage Subcriber\",\r\n        getListReportHistorySim: \"Get List Report History SSubcriberim\",\r\n        getListReportDetailSim: \"Get List Report Detail Subcriber\",\r\n        getListReportCheckPostage: \"Get List Report Check Postage\",\r\n        getListReportContract: \"Get List Report Contract\",\r\n        getListReportBillingCustomer: \"Get List Report Billing Customer\"\r\n    },\r\n    RatingPlan: {\r\n        RatingPlan: \"Gói cước\",\r\n        changeStatusRatingPlan: \"Kích hoạt/Tạm dừng gói cước\",\r\n        approveRatingPlan: \"Phê duyệt gói cước\",\r\n        createRatingPlan: \"Tạo gói cước\",\r\n        deleteRatingPlan: \"Xóa gói cước\",\r\n        getRatingPlan: \"Xem chi tiết gói cước\",\r\n        issueRatingPlan: \"Gán gói cước cho cấp dưới\",\r\n        searchRatingPlan: \"Xem danh sách gói cước\",\r\n        updateRatingPlan: \"Cập nhật gói cước\",\r\n    },\r\n    Permission:{\r\n        Permission: \"Quyền\",\r\n        getPermission: \"Xem chi tiết quyền\",\r\n        searchPermission: \"Xem danh sách quyền\"\r\n    },\r\n    Device: {\r\n        Device: \"Thiết bị\",\r\n        createDevice: \"Tạo thiết bị\",\r\n        deleteDevice: \"Xóa thiết bị\",\r\n        getDevice: \"Xem chi tiết thiết bị\",\r\n        searchDevice: \"Xem danh sách thiết bị\",\r\n        updateDevice: \"Cập nhật thiết bị\",\r\n    },\r\n    Customer: {\r\n        Customer: \"Khách hàng\",\r\n        changeStatusCustomer: \"Kích hoạt khách hàng\",\r\n        getCustomer: \"Xem chi tiết khách hàng\",\r\n        searchCustomer: \"Xem danh sách khách hàng\",\r\n        updateCustomer: \"Cập nhật khách hàng\",\r\n        deleteCustomer: \"Xóa khách hàng\",\r\n        createCustomer: \"Tạo khách hàng\"\r\n    },\r\n    CustomAlert: {\r\n        CustomAlert: \"Cấu hình cảnh báo\",\r\n        createAlertConfig: \"Tạo cấu hình cảnh báo\",\r\n        deleteAlertConfig: \"Xóa cấu hình cảnh báo\",\r\n        getAlertConfig: \"Xem chi tiết cấu hình cảnh báo\",\r\n        searchAlertConfig: \"Xem danh sách cầu hình cảnh báo\",\r\n        updateAlertConfig: \"Cập nhật cấu hình cảnh báo\"\r\n    },\r\n    AlertRecvGrp: {\r\n        AlertRecvGrp: \"Nhóm nhận cảnh báo\",\r\n        createAlertRecvGrp: \"Tạo nhóm nhận cảnh báo\",\r\n        updateAlertRecvGrp: \"Cập nhật nhóm nhận cảnh báo\",\r\n        getAlertRecvGrp: \"Xem chi tiết nhóm nhận cảnh báo\",\r\n        deleteAlertRecvGrp: \"Xóa nhóm nhận cảnh báo\",\r\n        searchAlertRecvGrp: \"Danh sách nhóm nhận cảnh báo\"\r\n    },\r\n    RptCfg: {\r\n        RptCfg: \"Cấu hình báo cáo động\",\r\n        createRptCfg: \"Tạo cấu hình báo cáo động\",\r\n        updateRptCfg: \"Cập nhật cấu hình báo cáo động\",\r\n        getRptCfg: \"Xem chi tiết cấu hình báo cáo động\",\r\n        deleteRptCfg: \"Xóa cấu hình báo cáo động\",\r\n        searchRptCfg: \"Danh sách cấu hình báo cáo động\"\r\n    },\r\n    RptRecvGrp: {\r\n        RptRecvGrp: \"Nhóm nhận báo cáo động\",\r\n        createRptRecvGrp: \"Tạo nhóm nhận báo cáo động\",\r\n        updateRptRecvGrp: \"Cập nhật nhóm nhận báo cáo động\",\r\n        getRptRecvGrp: \"Xem chi tiết nhóm nhận báo cáo động\",\r\n        deleteRptRecvGrp: \"Xóa nhóm nhận báo cáo động\",\r\n        searchRptRecvGrp: \"Danh sách nhóm nhận báo cáo động\"\r\n    },\r\n    RptSend: {\r\n        RptSend: \"Cấu hình gửi mail báo cáo\",\r\n        updateRptSend: \"Cập nhật cấu hình gửi mail báo cáo\"\r\n    },\r\n    Contract: {\r\n        Contract: \"Hợp đồng\",\r\n        getContract: \"Xem chi tiết hợp đồng\",\r\n        searchContract: \"Xem danh sách hợp đồng\"\r\n    },\r\n    Configuration: {\r\n        Configuration: \"Cấu hình hệ thống\",\r\n        getConfiguration: \"Xem chi tiết thông số cấu hình hệ thống\",\r\n        searchConfiguration: \"Tìm kiếm thông số cấu hình hệ thống\",\r\n        updateConfiguration: \"Cập nhật thông số cấu hình hệ thống\"\r\n    },\r\n    ApnSim: {\r\n        ApnSim: \"APN Sim\",\r\n        issueApnSim: \"Issue APN Sim\",\r\n        searchApnSim: \"Danh sách APN thuê bao\",\r\n        getApnSim: \"Chi tiết APN thuê bao\"\r\n    },\r\n    Apn: {\r\n        Apn: \"Apn\",\r\n        activeApn: \"Active APN\",\r\n        cancelApn: \"Cancel APN\",\r\n        completeApn: \"Complete APN\",\r\n        createApn: \"Tạo APN\",\r\n        deactiveApn: \"Deactive APN\",\r\n        getApn: \"Xem chi tiết APN\",\r\n        issueApn: \"Issue APN\",\r\n        searchApn: \"Danh sách APN\",\r\n        sentEmailApn: \"Send Email APN\",\r\n        updateApn: \"Cập nhật APN\"\r\n    },\r\n    AlertLog: {\r\n        AlertLog: \"Lịch sử cảnh báo\",\r\n        getAlertLog: \"Xem chi tiết lịch sử cảnh báo\",\r\n        searchAlertLog: \"Xem danh sách lịch sử cảnh báo\",\r\n    },\r\n    Alert: {\r\n        Alert: \"Cảnh báo\",\r\n        ackAlert: \"Xác nhận cảnh báo\",\r\n        getAlert: \"Xem chi tiết cảnh báo\",\r\n        searchAlert: \"Xem danh sách quy tắc\",\r\n        changeStatusAlert: \"Đổi trạng thái cảnh báo\",\r\n        createAlert: \"Tạo cảnh báo\",\r\n        updateAlert: \"Cập nhật cảnh báo\",\r\n        deleteAlert: \"Xóa cảnh báo\",\r\n        createAlertWalletThreshold: \"Tạo cảnh báo ví vượt ngưỡng\",\r\n        createAlertWalletExpiry: \"Tạo cảnh báo ví hết hạn\",\r\n        updateAlertWalletThreshold: \"Cập nhật cảnh báo ví vượt ngưỡng\",\r\n        updateAlertWalletExpiry: \"Cập nhật cảnh báo ví hết hạn\",\r\n    },\r\n    RptContent: {\r\n        RptContent: \"Dữ liệu báo cáo động\"\r\n    },\r\n    DynamicChart: {\r\n        DynamicChart: \"Biểu đồ động\",\r\n        getDashBoardContent: \"Xem Nội dung Dashboard\",\r\n    },\r\n    CnfDynamicChart: {\r\n        CnfDynamicChart: \"Cấu hình biểu đồ động\",\r\n        searchCnfDynamicChart: \"Tìm kiếm cấu hình biểu đồ động\",\r\n        getCnfDynamicChart: \"Xem chi tiết cấu hình biểu đồ động\",\r\n        updateCnfDynamicChart: \"Cập nhật cấu hình biểu đồ động\",\r\n        createCnfDynamicChart: \"Tạo cấu hình biểu đồ động\",\r\n        deleteCnfDynamicChart: \"Xóa cấu hình biểu đồ động\"\r\n    },Log : {\r\n        Log : \"Nhật ký hoạt động\"\r\n\r\n    },\r\n    Ticket: {\r\n        Ticket: \"Quản lý yêu cầu\",\r\n        getTicket: \"Xem danh sách yêu cầu\",\r\n        createTicket: \"Tạo yêu cầu\",\r\n        updateTicket: \"Cập nhật yêu cầu\"\r\n    },\r\n    Policy: {\r\n        Policy: \"Điều khoản và chính sách\",\r\n        getPersonalDataPolicy: \"Chính sách bảo vệ dữ liệu cá nhân\"\r\n    },\r\n    Wallet:{\r\n        Wallet: \"Quản lý lưu lượng\",\r\n        searchWallet: \"Tìm kiếm và liệt kê toàn bộ danh sách ví\",\r\n        accuracyWallet: \"Thêm ví (Xác thực ví)\",\r\n        shareWallet: \"Chia sẻ\",\r\n        createShareInfo: \"Thêm người nhận chia sẻ (Cho phép thêm trên web và file)\",\r\n        searchShareInfo: \"Tìm kiếm và liệt kê toàn bộ danh sách chia sẻ\",\r\n        walletHistory: \"Xem lịch sử chia sẻ\",\r\n        // alertWalletThreshold: \"Cảnh báo ví lưu lượng vượt ngưỡng giá trị\",\r\n        // alertWalletExpiry: \"Cảnh báo hết hạn ví\",\r\n    },\r\n    ShareGroup:{\r\n        ShareGroup: \"Nhóm chia sẻ\",\r\n        searchShareGroup: \"Hiển thị danh sách nhóm chia sẻ\",\r\n        createShareGroup: \"Tạo nhóm chia sẻ\",\r\n        editShareGroup: \"Chỉnh sửa nhóm chia sẻ\",\r\n        detailShareGroup: \"Xem chi tiết nhóm chia sẻ\",\r\n        deleteShareGroup: \"Xóa nhóm chia sẻ\",\r\n    },\r\n    Diagnose: {\r\n        Diagnose: \"Chẩn đoán\",\r\n        searchDiagnose: \"Tra cứu chẩn đoán\",\r\n    },\r\n    'API Partner': {\r\n        'API Partner': \"Cấp quyền API\",\r\n    },\r\n    Guide: {\r\n        Guide: \"Hướng dẫn sử dụng\",\r\n        guideIntegration: \"Xem hướng dẫn tích hợp\",\r\n    }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,IAAI,EAAE;IACFA,IAAI,EAAE,WAAW;IACjBC,UAAU,EAAE,eAAe;IAC3BC,UAAU,EAAE,eAAe;IAC3BC,OAAO,EAAE,wBAAwB;IACjCC,UAAU,EAAE,yBAAyB;IACrCC,UAAU,EAAE,8BAA8B;IAC1CC,cAAc,EAAE,gCAAgC;IAChDC,aAAa,EAAE,4BAA4B;IAC3CC,gBAAgB,EAAE;GACrB;EACDC,QAAQ,EAAE;IACNA,QAAQ,EAAE,eAAe;IACzBC,cAAc,EAAE,mBAAmB;IACnCC,cAAc,EAAE,mBAAmB;IACnCC,WAAW,EAAE,4BAA4B;IACzCC,cAAc,EAAE,6BAA6B;IAC7CC,cAAc,EAAE;GACnB;EACDC,GAAG,EAAE;IACDA,GAAG,EAAE,UAAU;IACfC,SAAS,EAAE,cAAc;IACzBC,SAAS,EAAE,cAAc;IACzBC,MAAM,EAAE,uBAAuB;IAC/BC,SAAS,EAAE,wBAAwB;IACnCC,SAAS,EAAE;GACd;EACDC,aAAa,EAAC;IACVA,aAAa,EAAE,mBAAmB;IAClCC,UAAU,EAAE,+BAA+B;IAC3CC,aAAa,EAAE,2BAA2B;IAC1CC,cAAc,EAAE,4BAA4B;IAC5CC,cAAc,EAAE;GACnB;EACDC,IAAI,EAAE;IACFA,IAAI,EAAE,YAAY;IAClBC,UAAU,EAAE,gBAAgB;IAC5BC,UAAU,EAAE,gBAAgB;IAC5BC,OAAO,EAAE,yBAAyB;IAClCC,UAAU,EAAE,0BAA0B;IACtCC,UAAU,EAAE;GACf;EACDC,MAAM,EAAE;IACJA,MAAM,EAAE,SAAS;IACjBC,sBAAsB,EAAE,kCAAkC;IAC1DC,yBAAyB,EAAE,qCAAqC;IAChEC,0BAA0B,EAAE,gCAAgC;IAC5DC,sBAAsB,EAAE,2BAA2B;IACnDC,oBAAoB,EAAE,yBAAyB;IAC/CC,sBAAsB,EAAE,kCAAkC;IAC1DC,uBAAuB,EAAE,sCAAsC;IAC/DC,sBAAsB,EAAE,kCAAkC;IAC1DC,yBAAyB,EAAE,+BAA+B;IAC1DC,qBAAqB,EAAE,0BAA0B;IACjDC,4BAA4B,EAAE;GACjC;EACDC,UAAU,EAAE;IACRA,UAAU,EAAE,UAAU;IACtBC,sBAAsB,EAAE,6BAA6B;IACrDC,iBAAiB,EAAE,oBAAoB;IACvCC,gBAAgB,EAAE,cAAc;IAChCC,gBAAgB,EAAE,cAAc;IAChCC,aAAa,EAAE,uBAAuB;IACtCC,eAAe,EAAE,2BAA2B;IAC5CC,gBAAgB,EAAE,wBAAwB;IAC1CC,gBAAgB,EAAE;GACrB;EACDC,UAAU,EAAC;IACPA,UAAU,EAAE,OAAO;IACnBC,aAAa,EAAE,oBAAoB;IACnCC,gBAAgB,EAAE;GACrB;EACDC,MAAM,EAAE;IACJA,MAAM,EAAE,UAAU;IAClBC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,cAAc;IAC5BC,SAAS,EAAE,uBAAuB;IAClCC,YAAY,EAAE,wBAAwB;IACtCC,YAAY,EAAE;GACjB;EACDC,QAAQ,EAAE;IACNA,QAAQ,EAAE,YAAY;IACtBC,oBAAoB,EAAE,sBAAsB;IAC5CC,WAAW,EAAE,yBAAyB;IACtCC,cAAc,EAAE,0BAA0B;IAC1CC,cAAc,EAAE,qBAAqB;IACrCC,cAAc,EAAE,gBAAgB;IAChCC,cAAc,EAAE;GACnB;EACDC,WAAW,EAAE;IACTA,WAAW,EAAE,mBAAmB;IAChCC,iBAAiB,EAAE,uBAAuB;IAC1CC,iBAAiB,EAAE,uBAAuB;IAC1CC,cAAc,EAAE,gCAAgC;IAChDC,iBAAiB,EAAE,iCAAiC;IACpDC,iBAAiB,EAAE;GACtB;EACDC,YAAY,EAAE;IACVA,YAAY,EAAE,oBAAoB;IAClCC,kBAAkB,EAAE,wBAAwB;IAC5CC,kBAAkB,EAAE,6BAA6B;IACjDC,eAAe,EAAE,iCAAiC;IAClDC,kBAAkB,EAAE,wBAAwB;IAC5CC,kBAAkB,EAAE;GACvB;EACDC,MAAM,EAAE;IACJA,MAAM,EAAE,uBAAuB;IAC/BC,YAAY,EAAE,2BAA2B;IACzCC,YAAY,EAAE,gCAAgC;IAC9CC,SAAS,EAAE,oCAAoC;IAC/CC,YAAY,EAAE,2BAA2B;IACzCC,YAAY,EAAE;GACjB;EACDC,UAAU,EAAE;IACRA,UAAU,EAAE,wBAAwB;IACpCC,gBAAgB,EAAE,4BAA4B;IAC9CC,gBAAgB,EAAE,iCAAiC;IACnDC,aAAa,EAAE,qCAAqC;IACpDC,gBAAgB,EAAE,4BAA4B;IAC9CC,gBAAgB,EAAE;GACrB;EACDC,OAAO,EAAE;IACLA,OAAO,EAAE,2BAA2B;IACpCC,aAAa,EAAE;GAClB;EACDC,QAAQ,EAAE;IACNA,QAAQ,EAAE,UAAU;IACpBC,WAAW,EAAE,uBAAuB;IACpCC,cAAc,EAAE;GACnB;EACDC,aAAa,EAAE;IACXA,aAAa,EAAE,mBAAmB;IAClCC,gBAAgB,EAAE,yCAAyC;IAC3DC,mBAAmB,EAAE,qCAAqC;IAC1DC,mBAAmB,EAAE;GACxB;EACDC,MAAM,EAAE;IACJA,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,eAAe;IAC5BC,YAAY,EAAE,wBAAwB;IACtCC,SAAS,EAAE;GACd;EACDC,GAAG,EAAE;IACDA,GAAG,EAAE,KAAK;IACVC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,YAAY;IACvBC,WAAW,EAAE,cAAc;IAC3BC,SAAS,EAAE,SAAS;IACpBC,WAAW,EAAE,cAAc;IAC3BC,MAAM,EAAE,kBAAkB;IAC1BC,QAAQ,EAAE,WAAW;IACrBC,SAAS,EAAE,eAAe;IAC1BC,YAAY,EAAE,gBAAgB;IAC9BC,SAAS,EAAE;GACd;EACDC,QAAQ,EAAE;IACNA,QAAQ,EAAE,kBAAkB;IAC5BC,WAAW,EAAE,+BAA+B;IAC5CC,cAAc,EAAE;GACnB;EACDC,KAAK,EAAE;IACHA,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE,mBAAmB;IAC7BC,QAAQ,EAAE,uBAAuB;IACjCC,WAAW,EAAE,uBAAuB;IACpCC,iBAAiB,EAAE,yBAAyB;IAC5CC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,mBAAmB;IAChCC,WAAW,EAAE,cAAc;IAC3BC,0BAA0B,EAAE,6BAA6B;IACzDC,uBAAuB,EAAE,yBAAyB;IAClDC,0BAA0B,EAAE,kCAAkC;IAC9DC,uBAAuB,EAAE;GAC5B;EACDC,UAAU,EAAE;IACRA,UAAU,EAAE;GACf;EACDC,YAAY,EAAE;IACVA,YAAY,EAAE,cAAc;IAC5BC,mBAAmB,EAAE;GACxB;EACDC,eAAe,EAAE;IACbA,eAAe,EAAE,uBAAuB;IACxCC,qBAAqB,EAAE,gCAAgC;IACvDC,kBAAkB,EAAE,oCAAoC;IACxDC,qBAAqB,EAAE,gCAAgC;IACvDC,qBAAqB,EAAE,2BAA2B;IAClDC,qBAAqB,EAAE;GAC1B;EAACC,GAAG,EAAG;IACJA,GAAG,EAAG;GAET;EACDC,MAAM,EAAE;IACJA,MAAM,EAAE,iBAAiB;IACzBC,SAAS,EAAE,uBAAuB;IAClCC,YAAY,EAAE,aAAa;IAC3BC,YAAY,EAAE;GACjB;EACDC,MAAM,EAAE;IACJA,MAAM,EAAE,0BAA0B;IAClCC,qBAAqB,EAAE;GAC1B;EACDC,MAAM,EAAC;IACHA,MAAM,EAAE,mBAAmB;IAC3BC,YAAY,EAAE,0CAA0C;IACxDC,cAAc,EAAE,uBAAuB;IACvCC,WAAW,EAAE,SAAS;IACtBC,eAAe,EAAE,0DAA0D;IAC3EC,eAAe,EAAE,+CAA+C;IAChEC,aAAa,EAAE;IACf;IACA;GACH;;EACDC,UAAU,EAAC;IACPA,UAAU,EAAE,cAAc;IAC1BC,gBAAgB,EAAE,iCAAiC;IACnDC,gBAAgB,EAAE,kBAAkB;IACpCC,cAAc,EAAE,wBAAwB;IACxCC,gBAAgB,EAAE,2BAA2B;IAC7CC,gBAAgB,EAAE;GACrB;EACDC,QAAQ,EAAE;IACNA,QAAQ,EAAE,WAAW;IACrBC,cAAc,EAAE;GACnB;EACD,aAAa,EAAE;IACX,aAAa,EAAE;GAClB;EACDC,KAAK,EAAE;IACHA,KAAK,EAAE,mBAAmB;IAC1BC,gBAAgB,EAAE;;CAEzB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}