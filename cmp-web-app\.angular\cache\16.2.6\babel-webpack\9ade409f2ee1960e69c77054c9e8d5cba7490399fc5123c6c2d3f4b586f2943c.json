{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/cmp/cmp-web-app/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { ComponentBase } from \"../../../../component.base\";\nimport { Validators } from \"@angular/forms\";\nimport { PrimeIcons } from \"primeng/api\";\nimport { TrafficWalletService } from \"../../../../service/datapool/TrafficWalletService\";\nimport { FilterInputType } from \"src/app/template/common-module/search-filter-separate/search-filter-separate.component\";\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\nimport { ShareManagementService } from \"../../../../service/datapool/ShareManagementService\";\nimport { digitValidator } from \"src/app/template/common-module/validatorCustoms\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"primeng/button\";\nimport * as i4 from \"../../../common-module/table/table.component\";\nimport * as i5 from \"../../../common-module/search-filter-separate/search-filter-separate.component\";\nimport * as i6 from \"primeng/dialog\";\nimport * as i7 from \"primeng/breadcrumb\";\nimport * as i8 from \"primeng/inputtext\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"primeng/card\";\nimport * as i11 from \"../../../common-module/ng-prime/inputotp/inputotp\";\nimport * as i12 from \"primeng/radiobutton\";\nimport * as i13 from \"../../../../service/datapool/TrafficWalletService\";\nimport * as i14 from \"../../../../service/datapool/ShareManagementService\";\nfunction DataPoolListComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"p-button\", 44);\n    i0.ɵɵlistener(\"click\", function DataPoolListComponent_div_5_Template_p_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.showModalAccuracy());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"datapool.button.createWallet\"));\n  }\n}\nfunction DataPoolListComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"label\", 17);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 18);\n    i0.ɵɵtext(4, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 19);\n    i0.ɵɵelement(6, \"input\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.tranService.translate(\"datapool.label.subCode\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"placeholder\", ctx_r1.tranService.translate(\"datapool.text.subCode\"));\n  }\n}\nfunction DataPoolListComponent_div_18_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r20.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction DataPoolListComponent_div_18_div_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r21 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r21.tranService.translate(\"global.message.formatContainVN\"));\n  }\n}\nconst _c0 = function () {\n  return {\n    len: 64\n  };\n};\nfunction DataPoolListComponent_div_18_div_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r22.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction DataPoolListComponent_div_18_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, DataPoolListComponent_div_18_div_2_div_1_Template, 2, 1, \"div\", 23);\n    i0.ɵɵtemplate(2, DataPoolListComponent_div_18_div_2_div_2_Template, 2, 1, \"div\", 23);\n    i0.ɵɵtemplate(3, DataPoolListComponent_div_18_div_2_div_3_Template, 2, 2, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.formAccuracyWallet.get(\"subCode\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.formAccuracyWallet.get(\"subCode\").errors.pattern);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.formAccuracyWallet.get(\"subCode\").errors.maxlength);\n  }\n}\nfunction DataPoolListComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"label\", 17);\n    i0.ɵɵtemplate(2, DataPoolListComponent_div_18_div_2_Template, 4, 3, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.formAccuracyWallet.get(\"subCode\").invalid && ctx_r2.formAccuracyWallet.get(\"subCode\").dirty);\n  }\n}\nfunction DataPoolListComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"label\", 17);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 18);\n    i0.ɵɵtext(4, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 19);\n    i0.ɵɵelement(6, \"input\", 48);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"datapool.label.payCode\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"placeholder\", ctx_r3.tranService.translate(\"datapool.text.payCode\"));\n  }\n}\nfunction DataPoolListComponent_div_20_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r24.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction DataPoolListComponent_div_20_div_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r25.tranService.translate(\"global.message.formatContainVN\"));\n  }\n}\nfunction DataPoolListComponent_div_20_div_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r26.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction DataPoolListComponent_div_20_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, DataPoolListComponent_div_20_div_2_div_1_Template, 2, 1, \"div\", 23);\n    i0.ɵɵtemplate(2, DataPoolListComponent_div_20_div_2_div_2_Template, 2, 1, \"div\", 23);\n    i0.ɵɵtemplate(3, DataPoolListComponent_div_20_div_2_div_3_Template, 2, 2, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r23.formAccuracyWallet.get(\"payCode\").errors.required);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r23.formAccuracyWallet.get(\"payCode\").errors.pattern);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r23.formAccuracyWallet.get(\"payCode\").errors.maxlength);\n  }\n}\nfunction DataPoolListComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"label\", 17);\n    i0.ɵɵtemplate(2, DataPoolListComponent_div_20_div_2_Template, 4, 3, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.formAccuracyWallet.get(\"payCode\").invalid && ctx_r4.formAccuracyWallet.get(\"payCode\").dirty);\n  }\n}\nfunction DataPoolListComponent_small_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction DataPoolListComponent_small_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r6.tranService.translate(\"global.message.maxLength\", i0.ɵɵpureFunction0(1, _c0)));\n  }\n}\nfunction DataPoolListComponent_small_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.tranService.translate(\"datapool.message.patternError\"));\n  }\n}\nfunction DataPoolListComponent_small_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r8.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction DataPoolListComponent_small_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r9.tranService.translate(\"datapool.message.digitError\"));\n  }\n}\nfunction DataPoolListComponent_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", ctx_r10.tranService.translate(\"datapool.message.in\"), \" \", ctx_r10.countdown, \" \", ctx_r10.tranService.translate(\"datapool.message.sec\"), \" \");\n  }\n}\nfunction DataPoolListComponent_p_dialog_63_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 58)(2, \"div\", 59)(3, \"div\", 53);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 54);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 52)(8, \"div\", 53);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 54);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 52)(13, \"div\", 53);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 54);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 52)(18, \"div\", 53);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 54);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r27.tranService.translate(\"datapool.label.payCode\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r27.walletDetail.payCode);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r27.tranService.translate(\"datapool.label.packageName\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r27.walletDetail.packageName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r27.tranService.translate(\"datapool.label.phoneFull\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r27.walletDetail.phoneActive);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r27.tranService.translate(\"datapool.label.tax\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r27.walletDetail.tax);\n  }\n}\nfunction DataPoolListComponent_p_dialog_63_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r28.formatNumber(ctx_r28.walletDetail.totalRemainingTraffic), \"/ \", ctx_r28.formatNumber(ctx_r28.walletDetail.purchasedTraffic), \" MB\");\n  }\n}\nfunction DataPoolListComponent_p_dialog_63_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\"\", ctx_r29.formatNumber(ctx_r29.walletDetail.totalRemainingTraffic), \"/ \", ctx_r29.formatNumber(ctx_r29.walletDetail.purchasedTraffic), \" \", ctx_r29.tranService.translate(\"alert.label.minutes\"), \"\");\n  }\n}\nfunction DataPoolListComponent_p_dialog_63_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r30.formatNumber(ctx_r30.walletDetail.totalRemainingTraffic), \"/ \", ctx_r30.formatNumber(ctx_r30.walletDetail.purchasedTraffic), \" SMS\");\n  }\n}\nfunction DataPoolListComponent_p_dialog_63_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r31.formatNumber(ctx_r31.walletDetail.totalRemainingTraffic), \"/ \", ctx_r31.formatNumber(ctx_r31.walletDetail.purchasedTraffic), \"\");\n  }\n}\nfunction DataPoolListComponent_p_dialog_63_table_vnpt_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"table-vnpt\", 60);\n  }\n  if (rf & 2) {\n    const ctx_r32 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"columns\", ctx_r32.columnsShareWallet)(\"dataSet\", ctx_r32.dataSetShareWallet)(\"pageSize\", ctx_r32.pageSizeShareWallet)(\"pageNumber\", ctx_r32.pageNumberShareWallet)(\"options\", ctx_r32.optionTableShareWallet)(\"sort\", ctx_r32.sortShareWallet)(\"loadData\", ctx_r32.searchShareWallet.bind(ctx_r32));\n  }\n}\nconst _c1 = function () {\n  return {\n    width: \"980px\"\n  };\n};\nfunction DataPoolListComponent_p_dialog_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dialog\", 8);\n    i0.ɵɵlistener(\"visibleChange\", function DataPoolListComponent_p_dialog_63_Template_p_dialog_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.isShowModalDetail = $event);\n    });\n    i0.ɵɵelementStart(1, \"p-card\", 49)(2, \"div\", 50);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, DataPoolListComponent_p_dialog_63_div_4_Template, 22, 8, \"div\", 34);\n    i0.ɵɵelementStart(5, \"div\", 51)(6, \"div\", 52)(7, \"div\", 53);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 54);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 52)(12, \"div\", 53);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 54);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 52)(17, \"div\", 53);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, DataPoolListComponent_p_dialog_63_div_19_Template, 2, 2, \"div\", 55);\n    i0.ɵɵtemplate(20, DataPoolListComponent_p_dialog_63_div_20_Template, 2, 3, \"div\", 55);\n    i0.ɵɵtemplate(21, DataPoolListComponent_p_dialog_63_div_21_Template, 2, 2, \"div\", 55);\n    i0.ɵɵtemplate(22, DataPoolListComponent_p_dialog_63_div_22_Template, 2, 2, \"div\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 52)(24, \"div\", 53);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 54);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"p-card\")(29, \"div\", 56);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, DataPoolListComponent_p_dialog_63_table_vnpt_31_Template, 1, 8, \"table-vnpt\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(23, _c1));\n    i0.ɵɵproperty(\"header\", ctx_r11.tranService.translate(\"global.button.view\"))(\"visible\", ctx_r11.isShowModalDetail)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r11.subCodeId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.walletDetail);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r11.tranService.translate(\"datapool.label.trafficType\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r11.walletDetail.trafficType);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r11.tranService.translate(\"datapool.label.methodAutoShare\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r11.getValueMethodAutoShare(ctx_r11.walletDetail.autoType));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r11.tranService.translate(\"datapool.label.remainData\"), \"/ \", ctx_r11.tranService.translate(\"datapool.label.purchasedData\"), \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.walletDetail.trafficType == \"G\\u00F3i Data\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.walletDetail.trafficType == \"G\\u00F3i tho\\u1EA1i\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r11.walletDetail.trafficType || \"\").toUpperCase().includes(\"G\\u00F3i SMS\".toUpperCase()));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(ctx_r11.walletDetail.trafficType || \"\").toUpperCase().includes(\"G\\u00F3i SMS\".toUpperCase()) && ctx_r11.walletDetail.trafficType != \"G\\u00F3i Data\" && ctx_r11.walletDetail.trafficType != \"G\\u00F3i tho\\u1EA1i\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r11.tranService.translate(\"datapool.label.usedTime\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r11.walletDetail.timeToUse);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r11.tranService.translate(\"datapool.label.shareInfo\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r11.isShowTableInDialogDetail);\n  }\n}\nconst _c2 = function () {\n  return {\n    width: \"750px\"\n  };\n};\nfunction DataPoolListComponent_p_dialog_64_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dialog\", 30);\n    i0.ɵɵlistener(\"visibleChange\", function DataPoolListComponent_p_dialog_64_Template_p_dialog_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.isShowRegisBySubOrPay = $event);\n    })(\"onHide\", function DataPoolListComponent_p_dialog_64_Template_p_dialog_onHide_0_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.onHideRegisBySubOrPay());\n    });\n    i0.ɵɵelementStart(1, \"div\", 61)(2, \"p\")(3, \"b\", 62);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"br\");\n    i0.ɵɵelementStart(6, \"span\", 63)(7, \"i\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(9, \"br\");\n    i0.ɵɵelementStart(10, \"a\", 64);\n    i0.ɵɵlistener(\"click\", function DataPoolListComponent_p_dialog_64_Template_a_click_10_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.openReadMoreRegister());\n    });\n    i0.ɵɵelementStart(11, \"i\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 65)(14, \"div\", 66)(15, \"p-radioButton\", 67);\n    i0.ɵɵlistener(\"ngModelChange\", function DataPoolListComponent_p_dialog_64_Template_p_radioButton_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.typeAutoShare = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(16, \"div\", 68);\n    i0.ɵɵelementStart(17, \"div\", 66)(18, \"p-radioButton\", 69);\n    i0.ɵɵlistener(\"ngModelChange\", function DataPoolListComponent_p_dialog_64_Template_p_radioButton_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.typeAutoShare = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 70)(20, \"div\", 71)(21, \"span\", 72);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(23, \"div\", 68);\n    i0.ɵɵelementStart(24, \"div\", 71)(25, \"span\", 72);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"div\", 73)(28, \"p-button\", 74);\n    i0.ɵɵlistener(\"click\", function DataPoolListComponent_p_dialog_64_Template_p_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.onHideRegisBySubOrPay());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"p-button\", 75);\n    i0.ɵɵlistener(\"click\", function DataPoolListComponent_p_dialog_64_Template_p_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r42 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r42.register());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(18, _c2));\n    i0.ɵɵproperty(\"header\", ctx_r12.tranService.translate(\"datapool.label.notification\"))(\"visible\", ctx_r12.isShowRegisBySubOrPay)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r12.tranService.translate(\"datapool.message.confirmRegisterAutoShareWallet\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r12.tranService.translate(\"datapool.text.noteAutoShare\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r12.tranService.translate(\"datapool.message.readMore\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r12.tranService.translate(\"datapool.label.registerByPayCode\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r12.typeAutoShare);\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r12.tranService.translate(\"datapool.label.registerBySubCode\"));\n    i0.ɵɵproperty(\"ngModel\", ctx_r12.typeAutoShare);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r12.tranService.translate(\"datapool.text.nonOTPPayCode\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.tranService.translate(\"datapool.text.nonOTPSubCode\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r12.tranService.translate(\"datapool.button.no\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r12.tranService.translate(\"datapool.button.register\"));\n  }\n}\nfunction DataPoolListComponent_p_dialog_65_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dialog\", 30);\n    i0.ɵɵlistener(\"visibleChange\", function DataPoolListComponent_p_dialog_65_Template_p_dialog_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r43 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r43.isShowRegisByPayCode = $event);\n    })(\"onHide\", function DataPoolListComponent_p_dialog_65_Template_p_dialog_onHide_0_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r45 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r45.onHideRegisByPayCode());\n    });\n    i0.ɵɵelementStart(1, \"div\", 61)(2, \"p\")(3, \"b\", 62);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"br\");\n    i0.ɵɵelementStart(6, \"span\", 63)(7, \"i\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(9, \"br\");\n    i0.ɵɵelementStart(10, \"a\", 64);\n    i0.ɵɵlistener(\"click\", function DataPoolListComponent_p_dialog_65_Template_a_click_10_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.openReadMoreRegister());\n    });\n    i0.ɵɵelementStart(11, \"i\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 73)(14, \"p-button\", 74);\n    i0.ɵɵlistener(\"click\", function DataPoolListComponent_p_dialog_65_Template_p_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.onHideRegisByPayCode());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p-button\", 76);\n    i0.ɵɵlistener(\"click\", function DataPoolListComponent_p_dialog_65_Template_p_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r48 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r48.register());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(12, _c2));\n    i0.ɵɵproperty(\"header\", ctx_r13.tranService.translate(\"datapool.label.notification\"))(\"visible\", ctx_r13.isShowRegisByPayCode)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r13.tranService.translate(\"datapool.message.confirmRegisterAutoSharePayCode\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r13.tranService.translate(\"datapool.text.noteRegisPayCode\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r13.tranService.translate(\"datapool.message.readMore\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r13.tranService.translate(\"datapool.button.no\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r13.tranService.translate(\"datapool.button.register\"));\n  }\n}\nfunction DataPoolListComponent_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", ctx_r14.tranService.translate(\"datapool.message.in\"), \" \", ctx_r14.countdown, \" \", ctx_r14.tranService.translate(\"datapool.message.sec\"), \" \");\n  }\n}\nfunction DataPoolListComponent_p_dialog_78_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dialog\", 30);\n    i0.ɵɵlistener(\"visibleChange\", function DataPoolListComponent_p_dialog_78_Template_p_dialog_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r49 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r49.isShowCancelBySubCode = $event);\n    })(\"onHide\", function DataPoolListComponent_p_dialog_78_Template_p_dialog_onHide_0_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r51 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r51.isShowCancelBySubCode = false);\n    });\n    i0.ɵɵelementStart(1, \"div\", 61)(2, \"p\")(3, \"b\", 62);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"br\");\n    i0.ɵɵelementStart(6, \"span\", 77)(7, \"i\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(9, \"br\");\n    i0.ɵɵelementStart(10, \"a\", 64);\n    i0.ɵɵlistener(\"click\", function DataPoolListComponent_p_dialog_78_Template_a_click_10_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r52 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r52.openReadMoreUnRegister());\n    });\n    i0.ɵɵelementStart(11, \"i\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 27)(14, \"p-button\", 74);\n    i0.ɵɵlistener(\"click\", function DataPoolListComponent_p_dialog_78_Template_p_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r53 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r53.isShowCancelBySubCode = false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p-button\", 76);\n    i0.ɵɵlistener(\"click\", function DataPoolListComponent_p_dialog_78_Template_p_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r54 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r54.onCancel());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(12, _c2));\n    i0.ɵɵproperty(\"header\", ctx_r15.tranService.translate(\"datapool.label.notification\"))(\"visible\", ctx_r15.isShowCancelBySubCode)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r15.tranService.translate(\"datapool.message.confirmCancelSubCode\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r15.tranService.translate(\"datapool.text.noteCancelSubCode\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.tranService.translate(\"datapool.message.readMore\"), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r15.tranService.translate(\"datapool.button.no\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r15.tranService.translate(\"datapool.button.yes\"));\n  }\n}\nconst _c3 = function (a0) {\n  return {\n    payCode: a0\n  };\n};\nfunction DataPoolListComponent_p_dialog_79_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r56 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dialog\", 30);\n    i0.ɵɵlistener(\"visibleChange\", function DataPoolListComponent_p_dialog_79_Template_p_dialog_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r55 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r55.isShowCancelByPayCode = $event);\n    })(\"onHide\", function DataPoolListComponent_p_dialog_79_Template_p_dialog_onHide_0_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r57 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r57.isShowCancelByPayCode = false);\n    });\n    i0.ɵɵelementStart(1, \"div\", 61)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\")(5, \"b\", 62);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"br\");\n    i0.ɵɵelementStart(8, \"span\", 77)(9, \"i\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(11, \"br\");\n    i0.ɵɵelementStart(12, \"a\", 64);\n    i0.ɵɵlistener(\"click\", function DataPoolListComponent_p_dialog_79_Template_a_click_12_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.openReadMoreUnRegister());\n    });\n    i0.ɵɵelementStart(13, \"i\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 27)(16, \"p-button\", 74);\n    i0.ɵɵlistener(\"click\", function DataPoolListComponent_p_dialog_79_Template_p_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r59 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r59.isShowCancelByPayCode = false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p-button\", 76);\n    i0.ɵɵlistener(\"click\", function DataPoolListComponent_p_dialog_79_Template_p_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r56);\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.onCancel());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(13, _c2));\n    i0.ɵɵproperty(\"header\", ctx_r16.tranService.translate(\"datapool.label.notification\"))(\"visible\", ctx_r16.isShowCancelByPayCode)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.tranService.translate(\"datapool.text.hasPayCode\", i0.ɵɵpureFunction1(14, _c3, ctx_r16.payCode)), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r16.tranService.translate(\"datapool.message.confirmCancelPayCode\", i0.ɵɵpureFunction1(16, _c3, ctx_r16.payCode)));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r16.tranService.translate(\"datapool.text.noteCancelPayCode\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r16.tranService.translate(\"datapool.message.readMore\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r16.tranService.translate(\"datapool.button.no\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"label\", ctx_r16.tranService.translate(\"datapool.button.yes\"));\n  }\n}\nconst _c4 = function (a0) {\n  return [a0];\n};\nconst _c5 = function () {\n  return {\n    width: \"700px\"\n  };\n};\nconst _c6 = function () {\n  return {\n    width: \"30vw\"\n  };\n};\nexport class DataPoolListComponent extends ComponentBase {\n  startCountdown() {\n    if (this.interval) {\n      clearInterval(this.interval); // Dừng interval cũ nếu có\n    }\n\n    this.interval = setInterval(() => {\n      if (this.countdown > 0) {\n        this.countdown--;\n      } else {\n        clearInterval(this.interval);\n        this.interval = null; // Xóa tham chiếu sau khi hoàn tất\n      }\n    }, 1000);\n  }\n  resetTimer() {\n    this.countdown = 60;\n    clearInterval(this.interval);\n    this.startCountdown();\n    this.sendOTP();\n  }\n  //reset time cho otp chia sẻ tự động\n  resetTimerForRegister() {\n    this.countdown = 60;\n    clearInterval(this.interval);\n    this.startCountdown();\n    this.sendOTPRegister();\n  }\n  extractDateString(dateTimeString) {\n    // Tách ngày, tháng, năm từ chuỗi ban đầu\n    const [day, month, year] = dateTimeString.split(/[/\\s:]+/);\n    // Trả về chuỗi ngày ở định dạng dd/MM/yyyy\n    return `${day}/${month}/${year}`;\n  }\n  openSubmit() {\n    this.isSubmit = true;\n    this.countdown = 60;\n    this.startCountdown();\n  }\n  constructor(trafficWalletService, shareManagementService, formBuilder, injector, primengConfig) {\n    super(injector);\n    this.trafficWalletService = trafficWalletService;\n    this.shareManagementService = shareManagementService;\n    this.formBuilder = formBuilder;\n    this.injector = injector;\n    this.primengConfig = primengConfig;\n    this.isSubmit = false;\n    this.minutesText = this.tranService.translate(\"alert.label.minutes\");\n    this.allPermissions = CONSTANTS.PERMISSIONS;\n    this.isShowModalDetail = false;\n    this.canView = false;\n  }\n  catchSearchDetail(event) {\n    this.searchInfo = this.originSearch;\n    this.searchInfo = {\n      ...this.searchInfo,\n      ...event\n    };\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      let me = _this;\n      _this.items = [{\n        label: _this.tranService.translate(`global.menu.trafficManagement`)\n      }, {\n        label: _this.tranService.translate(\"global.menu.walletList\")\n      }];\n      _this.home = {\n        icon: 'pi pi-home',\n        routerLink: '/'\n      };\n      _this.getPayCode(\"\", \"PAY_CODE\").then(resp => {\n        me.getPackageCbb(\"\", \"WALLET\");\n      });\n      _this.isShowModalWalletAuthen = false;\n      _this.searchInfo = {\n        lstPackageName: null,\n        searchWalletCode: 0,\n        searchPayCode: 0,\n        value: \"\",\n        searchName: 0,\n        searchPhone: 0,\n        lstTrafficType: null,\n        autoType: -1\n      };\n      _this.walletDetail = {\n        payCode: 1,\n        packageName: 2,\n        phone: 3,\n        tax: 4\n      };\n      _this.accuracyWallet = {\n        accuracyType: 0,\n        payCode: null,\n        subCode: null,\n        tax: null,\n        phoneNumber: null,\n        otp: null,\n        transId: 1\n      };\n      _this.withoutOTP = {\n        payCode: null,\n        subCode: null,\n        tax: null,\n        phoneNumber: null,\n        otp: null,\n        sharingType: null,\n        regisType: null,\n        new: null\n      };\n      _this.columns = [{\n        name: _this.tranService.translate(\"datapool.label.walletCode\"),\n        key: \"subCode\",\n        size: \"150px\",\n        align: \"left\",\n        isShow: true,\n        isSort: false,\n        style: {\n          cursor: \"pointer\",\n          color: \"var(--mainColorText)\"\n        },\n        funcClick(id, item) {\n          me.subCodeId = item.subCode;\n          me.resetDialogDetail();\n          me.getWalletDetail();\n          me.purchasedTraffic = item.purchasedTraffic;\n          me.trafficType = item.trafficType;\n        }\n      }, {\n        name: _this.tranService.translate(\"datapool.label.packageName\"),\n        key: \"packageName\",\n        size: \"150px\",\n        align: \"left\",\n        isShow: true,\n        isSort: false\n      }, {\n        name: _this.tranService.translate(\"datapool.label.phone\"),\n        key: \"phoneActive\",\n        size: \"fit-content\",\n        align: \"left\",\n        isShow: true,\n        isSort: false\n      }, {\n        name: _this.tranService.translate(\"datapool.label.payCode\"),\n        key: \"payCode\",\n        size: \"150px\",\n        align: \"left\",\n        isShow: true,\n        isSort: false\n      }, {\n        name: _this.tranService.translate(\"datapool.label.trafficType\"),\n        key: \"trafficType\",\n        size: \"fit-content\",\n        align: \"left\",\n        isShow: true,\n        isSort: false\n      }, {\n        name: _this.tranService.translate('datapool.label.remainData') + \"/ \" + _this.tranService.translate(\"datapool.label.purchasedData\"),\n        key: \"remainData\",\n        size: \"fit-content\",\n        align: \"left\",\n        isShow: true,\n        isSort: false,\n        funcConvertText(value, item) {\n          let remain = me.utilService.convertNumberToString(item.totalRemainingTraffic);\n          let total = me.utilService.convertNumberToString(item.purchasedTraffic);\n          if (item.trafficType == \"Gói Data\") {\n            return remain + \"/ \" + total + \" MB\";\n          } else if (item.trafficType == \"Gói thoại\") {\n            return remain + \"/ \" + total + me.minutesText;\n          } else if ((item.trafficType || \"\").toUpperCase().includes(\"Gói SMS\".toUpperCase())) {\n            return remain + \"/ \" + total + \" SMS\";\n          }\n          return item.totalRemainingTraffic + \"/ \" + item.purchasedTraffic;\n        }\n      }, {\n        name: _this.tranService.translate(\"datapool.label.usedTime\"),\n        key: \"timeUsed\",\n        size: \"fit-content\",\n        align: \"left\",\n        isShow: true,\n        isSort: false\n      }, {\n        name: _this.tranService.translate(\"datapool.label.methodAutoShare\"),\n        key: \"autoType\",\n        size: \"fit-content\",\n        align: \"left\",\n        isShow: true,\n        isSort: false,\n        funcConvertText(value) {\n          return me.getValueMethodAutoShare(value);\n        }\n      }, {\n        name: _this.tranService.translate(\"datapool.label.accuracyDate\"),\n        key: \"accuracyDate\",\n        size: \"fit-content\",\n        align: \"left\",\n        isShow: true,\n        isSort: false,\n        funcConvertText(value) {\n          if (value == null) return \"\";\n          return me.extractDateString(value);\n        }\n      }];\n      _this.payCode = null, _this.optionTable = {\n        hasClearSelected: false,\n        hasShowChoose: false,\n        hasShowIndex: true,\n        hasShowToggleColumn: false,\n        action: [{\n          icon: \"pi pi-window-maximize\",\n          tooltip: _this.tranService.translate(\"datapool.button.share\"),\n          func: function (id, item) {\n            me.router.navigate(['data-pool/walletMgmt/share', item.subCode]);\n          },\n          funcAppear(id, item) {\n            return item.canShare && me.checkAuthen([me.allPermissions.DATAPOOL.SHARE_WALLET]);\n          }\n        }, {\n          icon: PrimeIcons.LOCK,\n          tooltip: _this.tranService.translate(\"datapool.placeholder.registerShare\"),\n          func: function (id, item) {\n            me.showConfirmRegisterSubOrPay(item);\n          },\n          funcAppear(id, item) {\n            return item.canShare && item.autoType == CONSTANTS.METHOD_AUTO_SHARE.NONE && me.checkAuthen([me.allPermissions.DATAPOOL.SHARE_WALLET]);\n          }\n        }, {\n          icon: PrimeIcons.LOCK_OPEN,\n          tooltip: _this.tranService.translate(\"datapool.placeholder.cancelShare\"),\n          func: function (id, item) {\n            if (item.autoType == CONSTANTS.METHOD_AUTO_SHARE.PAY_CODE) {\n              me.payCode = item.payCode;\n              me.showConfirmCancelShareByPayCode(item);\n            } else if (item.autoType == CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE) {\n              me.payCode = item.payCode;\n              me.showConfirmCancelShareBySubCode(item);\n            }\n          },\n          funcAppear(id, item) {\n            return item.canShare && (item.autoType == CONSTANTS.METHOD_AUTO_SHARE.PAY_CODE || item.autoType == CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE) && me.checkAuthen([me.allPermissions.DATAPOOL.SHARE_WALLET]);\n          }\n        }]\n      };\n      _this.pageNumber = 0;\n      _this.pageSize = 10;\n      // this.sort = \"accuracyDate,desc\"\n      _this.sort = \"\";\n      _this.dataSet = {\n        content: [],\n        total: 0\n      };\n      _this.columnsShareWallet = [{\n        name: _this.tranService.translate(\"datapool.label.phoneFull\"),\n        key: \"phoneReceipt\",\n        size: \"fit-content\",\n        align: \"left\",\n        isShow: true,\n        isSort: false\n      }, {\n        name: _this.tranService.translate(\"datapool.label.fullName\"),\n        key: \"name\",\n        size: \"fit-content\",\n        align: \"left\",\n        isShow: true,\n        isSort: false\n      }, {\n        name: _this.tranService.translate(\"datapool.label.email\"),\n        key: \"email\",\n        size: \"fit-content\",\n        align: \"left\",\n        isShow: true,\n        isSort: false\n      }, {\n        name: _this.tranService.translate(\"datapool.label.sharedTime\"),\n        key: \"timeUpdate\",\n        size: \"fit-content\",\n        align: \"left\",\n        isShow: true,\n        isSort: false,\n        funcConvertText(value) {\n          return me.getFormattedDate(value);\n        }\n      }, {\n        name: _this.tranService.translate(\"datapool.label.usedDate\"),\n        key: \"sharingDay\",\n        size: \"fit-content\",\n        align: \"left\",\n        isShow: true,\n        isSort: false,\n        funcConvertText(value, item) {\n          return me.getFormattedDate(value, item.dayExprired);\n        }\n      }, {\n        name: _this.tranService.translate(\"datapool.label.shared\"),\n        key: \"trafficShare\",\n        size: \"fit-content\",\n        align: \"left\",\n        isShow: true,\n        isSort: false,\n        funcConvertText(value) {\n          value = me.formatNumber(value);\n          if ((me.trafficType || '').toUpperCase() == 'Gói Data'.toUpperCase()) return value + ' MB';else if ((me.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) return value + ' SMS';else if ((me.trafficType || '').toUpperCase() == 'Gói thoại'.toUpperCase()) return value + me.tranService.translate('alert.label.minutes');else return value;\n        }\n      }, {\n        name: _this.tranService.translate(\"datapool.label.percentage\"),\n        key: \"trafficShare\",\n        size: \"fit-content\",\n        align: \"left\",\n        isShow: true,\n        isSort: false,\n        funcConvertText(value) {\n          let result = 100.0 * value / me.purchasedTraffic;\n          return Math.round(result * 100.0) / 100.0 + \" %\";\n        }\n      }];\n      _this.dataSetShareWallet = {\n        content: [],\n        total: 0\n      };\n      _this.isShowTableInDialogDetail = false;\n      _this.pageNumberShareWallet = 0, _this.pageSize = 10;\n      _this.sortShareWallet = '';\n      _this.optionTableShareWallet = {\n        hasClearSelected: false,\n        hasShowChoose: false,\n        hasShowIndex: false,\n        hasShowToggleColumn: false,\n        action: null\n      };\n      _this.formSearchWallet = _this.formBuilder.group(_this.searchInfo);\n      _this.formAccuracyWallet = _this.formBuilder.group(_this.accuracyWallet);\n      _this.formWithoutOTP = _this.formBuilder.group(_this.withoutOTP);\n      _this.search(_this.pageNumber, _this.pageSize, _this.sort, _this.searchInfo);\n      _this.primengConfig.setTranslation({\n        emptyFilterMessage: \"Không tìm thấy kết quả\",\n        emptyMessage: \"Không tìm thấy kết quả\"\n      });\n      _this.originSearch = _this.searchInfo;\n      _this.isShowRegisBySubOrPay = false;\n      _this.isShowRegisByPayCode = false;\n      _this.isShowCancelByPayCode = false;\n      _this.isShowCancelBySubCode = false;\n      _this.typeAutoShare = 1;\n      _this.isShowDiaglogOTPRegis = false;\n    })();\n  }\n  getPackageCbb(packageName, type) {\n    let me = this;\n    let params = {\n      packageName,\n      type\n    };\n    this.messageCommonService.onload();\n    this.trafficWalletService.getPackageCbb(params, response => {\n      this.messageCommonService.offload();\n      me.packageCbb = response.map(r => {\n        return {\n          name: r.name,\n          value: r.value\n        };\n      });\n      me.packageCbb = me.packageCbb.filter((item, index, self) => index === self.findIndex(t => t.name === item.name && t.value === item.value));\n      this.searchList = [{\n        name: this.tranService.translate(\"datapool.label.walletCode\"),\n        key: \"searchWalletCode\"\n      }, {\n        name: this.tranService.translate(\"datapool.label.payCode\"),\n        key: \"searchPayCode\"\n      }, {\n        name: this.tranService.translate(\"datapool.label.phone\"),\n        key: \"searchPhone\"\n      }];\n      this.filterList = [{\n        name: this.tranService.translate(\"datapool.label.trafficType\"),\n        type: FilterInputType.multiselect,\n        items: [{\n          name: \"Gói Data\",\n          value: \"Gói Data\"\n        }, {\n          name: \"Gói SMS ngoại mạng\",\n          value: \"Gói SMS ngoại mạng\"\n        }, {\n          name: \"Gói SMS VNP\",\n          value: \"Gói SMS VNP\"\n        }],\n        key: \"lstTrafficType\",\n        itemFilter: true\n      }, {\n        name: this.tranService.translate(\"datapool.label.packageName\"),\n        type: FilterInputType.multiselect,\n        items: this.packageCbb,\n        key: \"lstPackageName\",\n        itemFilter: true\n      }, {\n        name: this.tranService.translate(\"datapool.label.accuracyDate\"),\n        type: FilterInputType.rangeCalendar,\n        key: [\"accuracyStartDate\", \"accuracyEndDate\"],\n        unixTimeString: true\n      }, {\n        name: this.tranService.translate(\"datapool.label.usedTime\"),\n        type: FilterInputType.rangeCalendar,\n        key: [\"startDate\", \"endDate\"],\n        unixTimeString: true\n      }, {\n        name: this.tranService.translate(\"datapool.label.methodAutoShare\"),\n        type: FilterInputType.dropdown,\n        items: [{\n          name: this.tranService.translate(\"datapool.methodAutoShare.subCode\"),\n          value: CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE\n        }, {\n          name: this.tranService.translate(\"datapool.methodAutoShare.payCode\"),\n          value: CONSTANTS.METHOD_AUTO_SHARE.PAY_CODE\n        }, {\n          name: this.tranService.translate(\"datapool.methodAutoShare.none\"),\n          value: CONSTANTS.METHOD_AUTO_SHARE.NONE\n        }],\n        key: \"autoType\",\n        itemFilter: true\n      }, {\n        name: this.tranService.translate(\"datapool.label.payCode\"),\n        type: FilterInputType.multiselect,\n        items: this.payCodeFilter,\n        key: \"lstPaymentCode\",\n        itemFilter: true\n      }];\n    }, null, () => {\n      this.messageCommonService.offload();\n    });\n  }\n  getPayCode(packageName, type) {\n    let me = this;\n    let params = {\n      packageName,\n      type\n    };\n    this.messageCommonService.onload();\n    return new Promise((resolve, reject) => {\n      this.trafficWalletService.getPackageCbb(params, response => {\n        this.messageCommonService.offload();\n        me.payCodeFilter = response.map(r => {\n          return {\n            name: r.name,\n            value: r.value\n          };\n        });\n        me.payCodeFilter = me.payCodeFilter.filter((item, index, self) => index === self.findIndex(t => t.name === item.name && t.value === item.value));\n        resolve(\"\");\n      }, null, () => {\n        this.messageCommonService.offload();\n      });\n    });\n  }\n  search(page, limit, sort, params) {\n    let me = this;\n    this.pageNumber = page;\n    this.pageSize = limit;\n    // this.sort = sort;\n    let dataParams = {\n      page,\n      size: limit\n      // sort\n    };\n\n    this.messageCommonService.onload();\n    Object.keys(this.searchInfo).forEach(key => {\n      dataParams[key] = this.searchInfo[key];\n      if (key == 'autoType' && this.searchInfo[key] == null) {\n        dataParams[key] = -1;\n      }\n    });\n    this.trafficWalletService.searchWallet(dataParams, response => {\n      me.dataSet = {\n        content: response.content,\n        total: response.totalElements\n      };\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  searchShareWallet(page, limit, sort, params) {\n    let me = this;\n    this.pageNumberShareWallet = page, this.pageSizeShareWallet = limit, this.sortShareWallet = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort: sort,\n      subCode: me.subCodeId.toString()\n    };\n    me.messageCommonService.onload();\n    this.trafficWalletService.getListShareWallet(dataParams, response => {\n      me.dataSetShareWallet = {\n        content: response.content,\n        total: response.totalElements\n      };\n      me.isShowTableInDialogDetail = true;\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  onSubmitSearch() {\n    this.pageNumber = 0;\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n  }\n  // send OTP\n  sendOTP() {\n    let body = {};\n    let me = this;\n    body = {\n      phoneNumber: parseInt(String(this.accuracyWallet.phoneNumber)?.replace(/^0/, \"84\"))\n    };\n    this.messageCommonService.onload();\n    this.shareManagementService.sendOTP(body, res => {\n      // me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\n      me.openSubmit();\n      // me.isShowModalWalletAuthen = false;\n      // me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  // Xac thuc va tao vi luu luong\n  accuracyRequest() {\n    Object.keys(this.formAccuracyWallet.controls).forEach(key => {\n      const control = this.formAccuracyWallet.get(key);\n      if (control.invalid) {\n        console.log('Field:', key, 'is invalid. Errors:', control.errors);\n      }\n    });\n    let me = this;\n    let body = {};\n    if (this.accuracyWallet.accuracyType == 0) {\n      body = {\n        accuracyType: this.accuracyWallet.accuracyType,\n        subCode: this.formAccuracyWallet.get(\"subCode\").value,\n        tax: this.accuracyWallet.tax,\n        phoneNumber: this.accuracyWallet.phoneNumber,\n        transId: this.accuracyWallet.transId,\n        otp: this.formAccuracyWallet.get(\"otp\").value\n      };\n    } else {\n      body = {\n        accuracyType: this.accuracyWallet.accuracyType,\n        payCode: this.formAccuracyWallet.get(\"payCode\").value,\n        tax: this.accuracyWallet.tax,\n        phoneNumber: this.accuracyWallet.phoneNumber,\n        transId: this.accuracyWallet.transId,\n        otp: this.formAccuracyWallet.get(\"otp\").value\n      };\n    }\n    me.withoutOTP = {\n      payCode: this.formAccuracyWallet.get(\"payCode\").value,\n      subCode: this.formAccuracyWallet.get(\"subCode\").value,\n      tax: this.accuracyWallet.tax,\n      phoneNumber: this.accuracyWallet.phoneNumber,\n      otp: null,\n      sharingType: '1',\n      regisType: '1',\n      new: true\n    };\n    this.messageCommonService.onload();\n    this.trafficWalletService.accuracyWallet(body, res => {\n      if (res.error == \"BSS-00000000\") {\n        me.messageCommonService.success(me.tranService.translate(\"global.message.accuracySuccess\"));\n        // tạo bằng mã ví\n        if (this.accuracyWallet.accuracyType == 0) {\n          this.messageCommonService.onload();\n          me.trafficWalletService.getById({\n            subCode: me.withoutOTP.subCode\n          }, detail => {\n            if (detail.autoType !== 0) {\n              me.showConfirmRegisterSubOrPay(detail);\n            }\n          }, error => {\n            if (error.error.error.errorCode === \"error.forbidden.view.detail\") {\n              this.messageCommonService.error(\"Bạn không có quyền truy cập thông tin này\");\n              this.router.navigate([\"/data-pool/walletMgmt/list\"]);\n            }\n          }, () => {\n            this.messageCommonService.offload();\n          });\n        } else {\n          // tạo bằng mã thanh toán call api search sau đó tìm trong list có autoType nào = 0 hay chưa\n          // Loại auto (0: Mã thanh toán, 1: mã ví, 2: chưa đăng ký)\n          this.trafficWalletService.searchWallet({\n            \"page\": 0,\n            \"size\": 99999999,\n            \"lstPackageName\": null,\n            \"searchWalletCode\": 1,\n            \"searchPayCode\": 1,\n            \"value\": this.formAccuracyWallet.get(\"payCode\").value,\n            \"searchName\": 0,\n            \"searchPhone\": 1,\n            \"lstTrafficType\": null,\n            \"autoType\": -1\n          }, response => {\n            if (me.dataSet?.content.find(el => el.autoType !== 0)) {\n              me.isShowRegisByPayCode = true;\n            }\n          }, null, () => {\n            me.messageCommonService.offload();\n          });\n        }\n      } else if (res.error === \"err.subCode.exists\") {\n        me.messageCommonService.error(\"Ví lưu lượng đã được xác thực\");\n        return;\n      } else {\n        me.messageCommonService.error(res.messageDetail);\n        return;\n      }\n      me.isShowModalWalletAuthen = false;\n      // me.formAccuracyWallet.reset();\n      this.isSubmit = false;\n      me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  showModalAccuracy() {\n    this.isShowModalWalletAuthen = true;\n    this.accuracyWallet = {\n      accuracyType: 0,\n      payCode: null,\n      subCode: null,\n      tax: null,\n      phoneNumber: null,\n      otp: null,\n      transId: 1\n    };\n    this.formAccuracyWallet = this.formBuilder.group(this.accuracyWallet);\n    this.formAccuracyWallet.get(\"subCode\").setValidators([Validators.required, Validators.maxLength(64), Validators.pattern('^[a-zA-Z0-9\\\\- _\\\\u00C0-\\\\u024F\\\\u1E00-\\\\u1EFF]+$')]);\n    this.formAccuracyWallet.get(\"payCode\").addValidators([Validators.required, Validators.maxLength(64), Validators.pattern('^[a-zA-Z0-9\\\\- _\\\\u00C0-\\\\u024F\\\\u1E00-\\\\u1EFF]+$')]);\n    this.formAccuracyWallet.get(\"tax\").setValidators([Validators.required, Validators.maxLength(64), Validators.pattern('^[a-zA-Z0-9_-]+$')]);\n    this.formAccuracyWallet.get(\"phoneNumber\").setValidators([Validators.required, Validators.pattern(/^0[0-9]{9,10}$/)]);\n    this.formAccuracyWallet.get(\"otp\").setValidators([Validators.required, digitValidator(6)]);\n    this.formAccuracyWallet.get(\"subCode\").enable({\n      emitEvent: false\n    });\n    this.formAccuracyWallet.get('payCode').disable({\n      emitEvent: false\n    });\n    this.formAccuracyWallet.controls.tax.enable();\n    this.formAccuracyWallet.controls.phoneNumber.enable();\n  }\n  checkDisable() {\n    if (this.formAccuracyWallet.get(\"accuracyType\").value == 0) {\n      this.formAccuracyWallet.get(\"subCode\").enable({\n        emitEvent: false\n      });\n      this.formAccuracyWallet.get('payCode').disable({\n        emitEvent: false\n      });\n      return !(this.formAccuracyWallet.controls.subCode.valid && this.formAccuracyWallet.controls.tax.valid && this.formAccuracyWallet.controls.phoneNumber.valid);\n    } else {\n      this.formAccuracyWallet.get(\"payCode\").enable({\n        emitEvent: false\n      });\n      this.formAccuracyWallet.get('subCode').disable({\n        emitEvent: false\n      });\n      return !(this.formAccuracyWallet.controls.payCode.valid && this.formAccuracyWallet.controls.tax.valid && this.formAccuracyWallet.controls.phoneNumber.valid);\n    }\n    return this.formAccuracyWallet.invalid;\n  }\n  getWalletDetail() {\n    let me = this;\n    me.messageCommonService.onload();\n    me.trafficWalletService.getById({\n      subCode: me.subCodeId\n    }, res => {\n      me.walletDetail = res;\n      let startDate = me.getUnixTime(res.startDate);\n      let endDate = me.getUnixTime(res.endDate);\n      me.walletDetail.timeToUse = (endDate - startDate) / (60 * 60 * 24) + \" ngày \" + \"[\" + me.getFormattedDateCrd(res.startDate) + \"-\" + me.getFormattedDateCrd(res.endDate) + \"]\";\n      me.listDetail = res.listShared;\n      me.canView = true;\n      me.isShowModalDetail = true;\n      me.searchShareWallet(0, 10, me.sortShareWallet, {\n        subCode: me.subCodeId\n      });\n    }, error => {\n      if (error.error.error.errorCode === \"error.forbidden.view.detail\") {\n        this.messageCommonService.error(\"Bạn không có quyền truy cập thông tin này\");\n        this.router.navigate([\"/data-pool/walletMgmt/list\"]);\n      }\n    }, () => {\n      this.messageCommonService.offload();\n    });\n  }\n  getFormattedDate(dateString, addDate) {\n    let me = this;\n    let date = new Date(dateString);\n    if (addDate) {\n      date.setDate(date.getDate() + addDate);\n    }\n    const day = date.getDate().toString().padStart(2, '0'); // Lấy ngày và thêm số 0 nếu cần\n    const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Lấy tháng (lưu ý tháng trong JavaScript bắt đầu từ 0)\n    const year = date.getFullYear();\n    const hours = date.getHours().toString().padStart(2, '0');\n    const minutes = date.getMinutes().toString().padStart(2, '0');\n    const seconds = date.getSeconds().toString().padStart(2, '0');\n    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;\n  }\n  parseDateTime(dateString) {\n    // Split the date and time parts\n    let [datePart, timePart] = dateString.split(' ');\n    // Split the date part by '/'\n    let dateParts = datePart.split('/');\n    let day = parseInt(dateParts[0], 10);\n    let month = parseInt(dateParts[1], 10) - 1; // Months are 0-based in JavaScript\n    let year = parseInt(dateParts[2], 10);\n    // Split the time part by ':'\n    let timeParts = timePart.split(':');\n    let hours = parseInt(timeParts[0], 10);\n    let minutes = parseInt(timeParts[1], 10);\n    let seconds = parseInt(timeParts[2], 10);\n    // Create a new Date object\n    return new Date(year, month, day, hours, minutes, seconds);\n  }\n  getUnixTime(dateString) {\n    const date = new Date(dateString);\n    return date.getTime() / 1000; // Chia cho 1000 để chuyển từ milliseconds sang seconds\n  }\n\n  getFormattedDateCrd(dateString, addDate) {\n    let date = new Date(dateString);\n    if (addDate) {\n      date.setUTCDate(date.getUTCDate() + addDate);\n    }\n    const day = date.getUTCDate().toString().padStart(2, '0'); // Lấy ngày và thêm số 0 nếu cần\n    const month = (date.getUTCMonth() + 1).toString().padStart(2, '0'); // Lấy tháng (lưu ý tháng trong JavaScript bắt đầu từ 0)\n    const year = date.getUTCFullYear();\n    const hours = date.getUTCHours().toString().padStart(2, '0');\n    const minutes = date.getUTCMinutes().toString().padStart(2, '0');\n    const seconds = date.getUTCSeconds().toString().padStart(2, '0');\n    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;\n  }\n  onHideOtp() {\n    this.formAccuracyWallet.controls.otp.reset();\n  }\n  ngOnDestroy() {\n    clearInterval(this.interval);\n  }\n  showConfirmRegisterSubOrPay(item) {\n    let me = this;\n    me.isShowRegisBySubOrPay = true;\n    me.withoutOTP = {\n      payCode: item.payCode,\n      subCode: item.subCode,\n      tax: item.tax,\n      phoneNumber: item.phoneActive,\n      otp: null,\n      sharingType: '1',\n      regisType: '1',\n      new: false\n    };\n  }\n  showConfirmCancelShareByPayCode(item) {\n    let me = this;\n    this.isShowCancelByPayCode = true;\n    me.withoutOTP = {\n      payCode: item.payCode,\n      subCode: item.autoType == CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE ? item.subCode : null,\n      tax: item.tax,\n      phoneNumber: item.phoneActive,\n      otp: null,\n      sharingType: '1',\n      regisType: '1',\n      new: false\n    };\n  }\n  showConfirmCancelShareBySubCode(item) {\n    let me = this;\n    this.isShowCancelBySubCode = true;\n    me.withoutOTP = {\n      payCode: item.payCode,\n      subCode: item.autoType == CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE ? item.subCode : null,\n      tax: item.tax,\n      phoneNumber: item.phoneActive,\n      otp: null,\n      sharingType: '1',\n      regisType: '1',\n      new: false\n    };\n  }\n  onHideRegisBySubOrPay() {\n    let me = this;\n    me.isShowRegisBySubOrPay = false;\n    me.typeAutoShare = 1;\n  }\n  onHideRegisByPayCode() {\n    let me = this;\n    me.isShowRegisByPayCode = false;\n    me.typeAutoShare = 1;\n  }\n  register() {\n    let me = this;\n    // thanh toán = mã ví mà chưa chọn cách đăng kí nào -> Hiển thị thông báo “Hãy chọn một cách đăng ký”\n    // thanh toán = mã thanh toán -> ko cần chọn\n    if (me.withoutOTP.new == true) {\n      //nếu là ví mới thì check theo accuracyType\n      if (me.accuracyWallet.accuracyType === 0 && me.typeAutoShare != null) {\n        me.isShowRegisBySubOrPay = false;\n        me.isShowRegisByPayCode = false;\n        me.isShowDiaglogOTPRegis = true;\n        me.formAccuracyWallet.reset();\n        me.sendOTPRegister();\n      } else if (me.accuracyWallet.accuracyType === 1) {\n        me.isShowRegisBySubOrPay = false;\n        me.isShowRegisByPayCode = false;\n        me.isShowDiaglogOTPRegis = true;\n        me.formAccuracyWallet.reset();\n        me.sendOTPRegister();\n      } else {\n        me.messageCommonService.error(me.tranService.translate(\"datapool.text.chooseRegistrationMethod\"));\n      }\n    } else if (me.withoutOTP.new == false) {\n      // nếu là ví cũ thích check typeAutoShare đã chọn\n      if (me.typeAutoShare != null) {\n        me.isShowRegisBySubOrPay = false;\n        me.isShowRegisByPayCode = false;\n        me.isShowDiaglogOTPRegis = true;\n        me.sendOTPRegister();\n      } else {\n        me.messageCommonService.error(me.tranService.translate(\"datapool.text.chooseRegistrationMethod\"));\n      }\n    }\n  }\n  cancel() {}\n  hideDiaglogConfirmOTP() {\n    let me = this;\n    me.isShowDiaglogOTPRegis = false;\n  }\n  // send OTP Register\n  sendOTPRegister() {\n    let body = {};\n    let me = this;\n    body = {\n      phoneNumber: parseInt(String(this.withoutOTP.phoneNumber)?.replace(/^0/, \"84\"))\n    };\n    // me.openOTPRegister();\n    this.messageCommonService.onload();\n    this.shareManagementService.sendOTP(body, res => {\n      me.openOTPRegister();\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  openOTPRegister() {\n    this.countdown = 60;\n    this.startCountdown();\n  }\n  onRegister() {\n    let me = this;\n    let body = {\n      payCode: me.withoutOTP.payCode,\n      subCode: me.typeAutoShare && me.typeAutoShare == 1 ? me.withoutOTP.subCode : null,\n      tax: me.withoutOTP.tax,\n      phoneNumber: me.withoutOTP.phoneNumber,\n      otp: this.formWithoutOTP.get(\"otp\").value,\n      sharingType: '1',\n      regisType: '1'\n    };\n    this.messageCommonService.onload();\n    this.trafficWalletService.registerWithoutOTP(body, res => {\n      if (res && res.error) {\n        me.messageCommonService.error(res.message);\n      } else {\n        me.messageCommonService.success(me.tranService.translate(\"datapool.message.registerSuccess\"));\n      }\n      me.isShowDiaglogOTPRegis = false;\n      me.formWithoutOTP.reset();\n      me.onSubmitSearch();\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  onCancel() {\n    let me = this;\n    let body = {\n      payCode: me.withoutOTP.payCode,\n      subCode: me.withoutOTP.subCode,\n      tax: me.withoutOTP.tax,\n      phoneNumber: me.withoutOTP.phoneNumber,\n      otp: me.withoutOTP.otp,\n      sharingType: '1',\n      regisType: '2'\n    };\n    this.messageCommonService.onload();\n    this.trafficWalletService.unRegisterWithoutOTP(body, res => {\n      me.messageCommonService.success(me.tranService.translate(\"datapool.message.cancelSuccess\"));\n      me.isShowCancelBySubCode = false;\n      me.isShowCancelByPayCode = false;\n      me.onSubmitSearch();\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  getValueMethodAutoShare(value) {\n    let me = this;\n    if (value == CONSTANTS.METHOD_AUTO_SHARE.PAY_CODE) {\n      return me.tranService.translate(\"datapool.methodAutoShare.payCode\");\n    } else if (value == CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE) {\n      return me.tranService.translate(\"datapool.methodAutoShare.subCode\");\n    } else {\n      return me.tranService.translate(\"datapool.methodAutoShare.none\");\n    }\n  }\n  // openReadMore() {\n  //     window.open(`/#/docs/autoShare`, '_blank');\n  // }\n  openReadMoreRegister() {\n    window.open(`/#/docs/registerNonOTP`, '_blank');\n  }\n  openReadMoreUnRegister() {\n    window.open(`/#/docs/unsubNonOTP`, '_blank');\n  }\n  resetDialogDetail() {\n    let me = this;\n    me.walletDetail = {};\n    me.dataSetShareWallet = {\n      content: [],\n      total: 0\n    };\n    me.isShowTableInDialogDetail = false;\n  }\n  formatNumber(value) {\n    return new Intl.NumberFormat('vi-VN').format(value);\n  }\n  static {\n    this.ɵfac = function DataPoolListComponent_Factory(t) {\n      return new (t || DataPoolListComponent)(i0.ɵɵdirectiveInject(TrafficWalletService), i0.ɵɵdirectiveInject(ShareManagementService), i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i2.PrimeNGConfig));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DataPoolListComponent,\n      selectors: [[\"traffic-wallet-list\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 80,\n      vars: 89,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [\"class\", \"col-5 flex flex-row justify-content-end align-items-center\", 4, \"ngIf\"], [3, \"searchList\", \"filterList\", \"searchDetail\"], [3, \"tableId\", \"fieldId\", \"columns\", \"dataSet\", \"options\", \"pageNumber\", \"loadData\", \"pageSize\", \"sort\", \"params\", \"labelTable\"], [1, \"flex\", \"justify-content-center\", \"responsive-dialog\"], [3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"mt-3\", 3, \"formGroup\", \"ngSubmit\"], [1, \"flex\", \"flex-row\", \"flex-wrap\", \"justify-content-between\", \"w-full\"], [1, \"w-full\", \"field\", \"grid\", \"flex\", \"align-items-center\", \"justify-content-start\"], [\"name\", \"accuracyType\", \"formControlName\", \"accuracyType\", \"inputId\", \"1\", 1, \"p-3\", 3, \"label\", \"value\", \"ngModel\", \"ngModelChange\"], [\"name\", \"accuracyType\", \"inputId\", \"2\", \"formControlName\", \"accuracyType\", 3, \"label\", \"value\", \"ngModel\", \"ngModelChange\"], [\"class\", \"w-full field grid mb-0\", 4, \"ngIf\"], [\"class\", \"w-full field grid m-0 p-0 mb-3\", 4, \"ngIf\"], [1, \"w-full\", \"field\", \"grid\"], [\"htmlFor\", \"tax\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"text-red-500\"], [1, \"col\"], [\"pInputText\", \"\", \"id\", \"tax\", \"formControlName\", \"tax\", 1, \"w-full\", 3, \"ngModel\", \"placeholder\", \"ngModelChange\"], [1, \"w-full\", \"field\", \"grid\", \"text-error-field\"], [\"htmlFor\", \"fullName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [\"htmlFor\", \"phone\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"pInputText\", \"\", \"id\", \"phoneNumber\", \"formControlName\", \"phoneNumber\", 1, \"w-full\", 3, \"ngModel\", \"placeholder\", \"ngModelChange\"], [1, \"flex\", \"flex-column\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"align-items-center\", \"mt-3\"], [\"styleClass\", \"mr-2 p-button-secondary\", 3, \"label\", \"click\"], [\"type\", \"button\", \"styleClass\", \"p-button-info\", 3, \"disabled\", \"label\", \"onClick\"], [3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\", \"onHide\"], [1, \"flex\", \"flex-column\", \"gap-2\", \"flex-1\"], [\"formControlName\", \"otp\", \"length\", \"6\", 1, \"mx-auto\", \"my-3\", 3, \"integerOnly\"], [\"type\", \"button\", 1, \"border-none\", \"mb-4\", \"cursor-pointer\", \"flex\", \"flex-row\", \"justify-content-center\", \"font-semibold\", 2, \"background-color\", \"transparent\", 3, \"disabled\", \"click\"], [4, \"ngIf\"], [1, \"m-auto\"], [\"pButton\", \"\", 1, \"m-auto\", \"mr-2\", 3, \"disabled\"], [\"type\", \"button\", \"pButton\", \"\", 1, \"m-auto\", \"ml-2\", \"p-button-outlined\", 3, \"click\"], [1, \"flex\", \"justify-content-center\", \"dialog-vnpt\"], [3, \"header\", \"visible\", \"modal\", \"style\", \"draggable\", \"resizable\", \"visibleChange\", 4, \"ngIf\"], [3, \"header\", \"visible\", \"modal\", \"style\", \"draggable\", \"resizable\", \"visibleChange\", \"onHide\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [\"type\", \"submit\", \"pButton\", \"\", 1, \"m-auto\", \"mr-2\", 3, \"disabled\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [\"styleClass\", \"p-button-info\", \"icon\", \"pi pi-plus\", 3, \"label\", \"click\"], [1, \"w-full\", \"field\", \"grid\", \"mb-0\"], [\"pInputText\", \"\", \"id\", \"subCode\", \"formControlName\", \"subCode\", 1, \"w-full\", 3, \"placeholder\"], [1, \"w-full\", \"field\", \"grid\", \"m-0\", \"p-0\", \"mb-3\"], [\"pInputText\", \"\", \"id\", \"payCode\", \"formControlName\", \"payCode\", 1, \"w-full\", 3, \"placeholder\"], [\"styleClass\", \"my-3\"], [1, \"text-2xl\", \"font-bold\", \"pb-2\"], [1, \"flex\", \"flex-row\", \"p-4\", \"border-round\", \"wallet-detail-div\"], [1, \"flex-1\"], [1, \"font-medium\", \"text-base\"], [1, \"font-semibold\", \"text-lg\"], [\"class\", \"font-semibold text-lg\", 4, \"ngIf\"], [1, \"text-lg\", \"font-bold\"], [3, \"fieldId\", \"columns\", \"dataSet\", \"pageSize\", \"pageNumber\", \"options\", \"sort\", \"loadData\", 4, \"ngIf\"], [1, \"flex\", \"flex-row\", \"surface-200\", \"p-4\", \"border-round\", \"wallet-detail-div\"], [1, \"flex-1\", \"wallet-detail\"], [3, \"fieldId\", \"columns\", \"dataSet\", \"pageSize\", \"pageNumber\", \"options\", \"sort\", \"loadData\"], [1, \"d-flex\", \"ai-center\", \"jc-center\", 2, \"text-align\", \"center\"], [2, \"font-size\", \"large\"], [2, \"color\", \"red\"], [1, \"cursor-pointer\", 2, \"color\", \"blue\", \"text-decoration\", \"underline\", 3, \"click\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"align-items-center\", \"mt-2\"], [1, \"grid\", \"col-4\"], [\"name\", \"registerOption\", \"value\", \"0\", \"inputId\", \"1\", 3, \"ngModel\", \"label\", \"ngModelChange\"], [1, \"col-1\"], [\"name\", \"registerOption\", \"value\", \"1\", \"inputId\", \"2\", 3, \"ngModel\", \"label\", \"ngModelChange\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"align-content-start\"], [1, \"grid\", \"col-4\", \"pl-6\"], [2, \"font-size\", \"0.8em\", \"color\", \"grey\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"mt-3\"], [\"icon\", \"pi pi-times\", \"styleClass\", \"p-button-secondary p-button-outlined\", 3, \"label\", \"click\"], [\"icon\", \"pi pi-check\", \"type\", \"button\", \"styleClass\", \"p-button-info ml-2\", 3, \"label\", \"click\"], [\"icon\", \"pi pi-check\", \"styleClass\", \"p-button-info ml-2\", 3, \"label\", \"click\"], [2, \"color\", \"red\", \"word-wrap\", \"break-word\"]],\n      template: function DataPoolListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, DataPoolListComponent_div_5_Template, 2, 1, \"div\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"search-filter-separate\", 5);\n          i0.ɵɵlistener(\"searchDetail\", function DataPoolListComponent_Template_search_filter_separate_searchDetail_6_listener($event) {\n            return ctx.catchSearchDetail($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"table-vnpt\", 6);\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"p-dialog\", 8);\n          i0.ɵɵlistener(\"visibleChange\", function DataPoolListComponent_Template_p_dialog_visibleChange_9_listener($event) {\n            return ctx.isShowModalWalletAuthen = $event;\n          });\n          i0.ɵɵelementStart(10, \"form\", 9);\n          i0.ɵɵlistener(\"ngSubmit\", function DataPoolListComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.accuracyRequest();\n          });\n          i0.ɵɵelementStart(11, \"div\", 10)(12, \"div\");\n          i0.ɵɵtext(13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 11)(15, \"p-radioButton\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function DataPoolListComponent_Template_p_radioButton_ngModelChange_15_listener($event) {\n            return ctx.accuracyWallet.accuracyType = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p-radioButton\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function DataPoolListComponent_Template_p_radioButton_ngModelChange_16_listener($event) {\n            return ctx.accuracyWallet.accuracyType = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(17, DataPoolListComponent_div_17_Template, 7, 2, \"div\", 14);\n          i0.ɵɵtemplate(18, DataPoolListComponent_div_18_Template, 3, 1, \"div\", 15);\n          i0.ɵɵtemplate(19, DataPoolListComponent_div_19_Template, 7, 2, \"div\", 14);\n          i0.ɵɵtemplate(20, DataPoolListComponent_div_20_Template, 3, 1, \"div\", 15);\n          i0.ɵɵelementStart(21, \"div\", 16)(22, \"label\", 17);\n          i0.ɵɵtext(23);\n          i0.ɵɵelementStart(24, \"span\", 18);\n          i0.ɵɵtext(25, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 19)(27, \"input\", 20);\n          i0.ɵɵlistener(\"ngModelChange\", function DataPoolListComponent_Template_input_ngModelChange_27_listener($event) {\n            return ctx.accuracyWallet.tax = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(28, \"div\", 21);\n          i0.ɵɵelement(29, \"label\", 22);\n          i0.ɵɵelementStart(30, \"div\", 19);\n          i0.ɵɵtemplate(31, DataPoolListComponent_small_31_Template, 2, 1, \"small\", 23);\n          i0.ɵɵtemplate(32, DataPoolListComponent_small_32_Template, 2, 2, \"small\", 23);\n          i0.ɵɵtemplate(33, DataPoolListComponent_small_33_Template, 2, 1, \"small\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"div\", 16)(35, \"label\", 24);\n          i0.ɵɵtext(36);\n          i0.ɵɵelementStart(37, \"span\", 18);\n          i0.ɵɵtext(38, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 19)(40, \"input\", 25);\n          i0.ɵɵlistener(\"ngModelChange\", function DataPoolListComponent_Template_input_ngModelChange_40_listener($event) {\n            return ctx.accuracyWallet.phoneNumber = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"div\", 21);\n          i0.ɵɵelement(42, \"label\", 24);\n          i0.ɵɵelementStart(43, \"div\", 26)(44, \"div\", 19);\n          i0.ɵɵtemplate(45, DataPoolListComponent_small_45_Template, 2, 1, \"small\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"div\", 19);\n          i0.ɵɵtemplate(47, DataPoolListComponent_small_47_Template, 2, 1, \"small\", 23);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(48, \"div\", 27)(49, \"p-button\", 28);\n          i0.ɵɵlistener(\"click\", function DataPoolListComponent_Template_p_button_click_49_listener() {\n            return ctx.isShowModalWalletAuthen = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"p-button\", 29);\n          i0.ɵɵlistener(\"onClick\", function DataPoolListComponent_Template_p_button_onClick_50_listener() {\n            return ctx.sendOTP();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"p-dialog\", 30);\n          i0.ɵɵlistener(\"visibleChange\", function DataPoolListComponent_Template_p_dialog_visibleChange_51_listener($event) {\n            return ctx.isSubmit = $event;\n          })(\"onHide\", function DataPoolListComponent_Template_p_dialog_onHide_51_listener() {\n            return ctx.onHideOtp();\n          });\n          i0.ɵɵelementStart(52, \"div\", 31);\n          i0.ɵɵelement(53, \"p-inputOtp\", 32);\n          i0.ɵɵelementStart(54, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function DataPoolListComponent_Template_button_click_54_listener() {\n            return ctx.resetTimer();\n          });\n          i0.ɵɵtext(55);\n          i0.ɵɵtemplate(56, DataPoolListComponent_div_56_Template, 2, 3, \"div\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 35)(58, \"button\", 36);\n          i0.ɵɵtext(59);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"button\", 37);\n          i0.ɵɵlistener(\"click\", function DataPoolListComponent_Template_button_click_60_listener() {\n            return ctx.isSubmit = false;\n          });\n          i0.ɵɵtext(61);\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(62, \"div\", 38);\n          i0.ɵɵtemplate(63, DataPoolListComponent_p_dialog_63_Template, 32, 24, \"p-dialog\", 39);\n          i0.ɵɵtemplate(64, DataPoolListComponent_p_dialog_64_Template, 30, 19, \"p-dialog\", 40);\n          i0.ɵɵtemplate(65, DataPoolListComponent_p_dialog_65_Template, 16, 13, \"p-dialog\", 40);\n          i0.ɵɵelementStart(66, \"p-dialog\", 30);\n          i0.ɵɵlistener(\"visibleChange\", function DataPoolListComponent_Template_p_dialog_visibleChange_66_listener($event) {\n            return ctx.isShowDiaglogOTPRegis = $event;\n          })(\"onHide\", function DataPoolListComponent_Template_p_dialog_onHide_66_listener() {\n            return ctx.hideDiaglogConfirmOTP();\n          });\n          i0.ɵɵelementStart(67, \"form\", 41);\n          i0.ɵɵlistener(\"ngSubmit\", function DataPoolListComponent_Template_form_ngSubmit_67_listener() {\n            return ctx.onRegister();\n          });\n          i0.ɵɵelementStart(68, \"div\", 31);\n          i0.ɵɵelement(69, \"p-inputOtp\", 32);\n          i0.ɵɵelementStart(70, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function DataPoolListComponent_Template_button_click_70_listener() {\n            return ctx.resetTimerForRegister();\n          });\n          i0.ɵɵtext(71);\n          i0.ɵɵtemplate(72, DataPoolListComponent_div_72_Template, 2, 3, \"div\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"div\", 35)(74, \"button\", 42);\n          i0.ɵɵtext(75);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"button\", 37);\n          i0.ɵɵlistener(\"click\", function DataPoolListComponent_Template_button_click_76_listener() {\n            return ctx.isShowDiaglogOTPRegis = false;\n          });\n          i0.ɵɵtext(77);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(78, DataPoolListComponent_p_dialog_78_Template, 16, 13, \"p-dialog\", 40);\n          i0.ɵɵtemplate(79, DataPoolListComponent_p_dialog_79_Template, 18, 18, \"p-dialog\", 40);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.menu.walletList\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkAuthen(i0.ɵɵpureFunction1(84, _c4, ctx.allPermissions.DATAPOOL.CREATE_WALLET)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"searchList\", ctx.searchList)(\"filterList\", ctx.filterList);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"tableId\", \"tableTrafficWallet\")(\"fieldId\", \"id\")(\"columns\", ctx.columns)(\"dataSet\", ctx.dataSet)(\"options\", ctx.optionTable)(\"pageNumber\", ctx.pageNumber)(\"loadData\", ctx.search.bind(ctx))(\"pageSize\", ctx.pageSize)(\"sort\", ctx.sort)(\"params\", ctx.searchInfo)(\"labelTable\", ctx.tranService.translate(\"global.menu.trafficManagement\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(86, _c5));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"datapool.label.accuracyWallet\"))(\"visible\", ctx.isShowModalWalletAuthen)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formAccuracyWallet);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"datapool.label.authenMethod\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"datapool.label.subCode\"))(\"value\", 0)(\"ngModel\", ctx.accuracyWallet.accuracyType);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"datapool.label.payCode\"))(\"value\", 1)(\"ngModel\", ctx.accuracyWallet.accuracyType);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.accuracyWallet.accuracyType == 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.accuracyWallet.accuracyType == 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.accuracyWallet.accuracyType == 1);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.accuracyWallet.accuracyType == 1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"datapool.label.tax\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.accuracyWallet.tax)(\"placeholder\", ctx.tranService.translate(\"datapool.text.tax\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccuracyWallet.controls.tax.dirty && (ctx.formAccuracyWallet.controls.tax.errors == null ? null : ctx.formAccuracyWallet.controls.tax.errors.required));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccuracyWallet.controls.tax.errors == null ? null : ctx.formAccuracyWallet.controls.tax.errors.maxlength);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccuracyWallet.controls.tax.errors == null ? null : ctx.formAccuracyWallet.controls.tax.errors.pattern);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"datapool.label.phone\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.accuracyWallet.phoneNumber)(\"placeholder\", ctx.tranService.translate(\"account.text.inputPhone\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccuracyWallet.controls.phoneNumber.dirty && (ctx.formAccuracyWallet.controls.phoneNumber.errors == null ? null : ctx.formAccuracyWallet.controls.phoneNumber.errors.required));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.formAccuracyWallet.controls.phoneNumber.errors == null ? null : ctx.formAccuracyWallet.controls.phoneNumber.errors.pattern);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.cancel\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.checkDisable())(\"label\", ctx.tranService.translate(\"global.button.save\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(87, _c6));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"datapool.label.otpCode\"))(\"visible\", ctx.isSubmit)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"integerOnly\", true);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.countdown > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\"\", ctx.tranService.translate(\"datapool.message.resendOtp\"), \"\\u00A0\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.countdown > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.formAccuracyWallet.controls.otp.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.button.save\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.button.cancel\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowModalDetail);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowRegisBySubOrPay);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowRegisByPayCode);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(88, _c6));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"datapool.label.otpCode\"))(\"visible\", ctx.isShowDiaglogOTPRegis)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formWithoutOTP);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"integerOnly\", true);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.countdown > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\"\", ctx.tranService.translate(\"datapool.message.resendOtp\"), \"\\u00A0\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.countdown > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.formWithoutOTP.controls.otp.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\"\", ctx.tranService.translate(\"global.button.save\"), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", ctx.tranService.translate(\"global.button.cancel\"), \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowCancelBySubCode);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowCancelByPayCode);\n        }\n      },\n      dependencies: [i3.ButtonDirective, i3.Button, i4.TableVnptComponent, i5.SearchFilterSeparateComponent, i6.Dialog, i7.Breadcrumb, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i8.InputText, i9.NgIf, i1.FormGroupDirective, i1.FormControlName, i10.Card, i11.InputOtp, i12.RadioButton],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["ComponentBase", "Validators", "PrimeIcons", "TrafficWalletService", "FilterInputType", "CONSTANTS", "ShareManagementService", "digitValidator", "i0", "ɵɵelementStart", "ɵɵlistener", "DataPoolListComponent_div_5_Template_p_button_click_1_listener", "ɵɵrestoreView", "_r18", "ctx_r17", "ɵɵnextContext", "ɵɵresetView", "showModalAccuracy", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "tranService", "translate", "ɵɵtext", "ɵɵelement", "ɵɵtextInterpolate", "ctx_r1", "ctx_r20", "ctx_r21", "ctx_r22", "ɵɵpureFunction0", "_c0", "ɵɵtemplate", "DataPoolListComponent_div_18_div_2_div_1_Template", "DataPoolListComponent_div_18_div_2_div_2_Template", "DataPoolListComponent_div_18_div_2_div_3_Template", "ctx_r19", "formAccuracyWallet", "get", "errors", "required", "pattern", "maxlength", "DataPoolListComponent_div_18_div_2_Template", "ctx_r2", "invalid", "dirty", "ctx_r3", "ctx_r24", "ctx_r25", "ctx_r26", "DataPoolListComponent_div_20_div_2_div_1_Template", "DataPoolListComponent_div_20_div_2_div_2_Template", "DataPoolListComponent_div_20_div_2_div_3_Template", "ctx_r23", "DataPoolListComponent_div_20_div_2_Template", "ctx_r4", "ctx_r5", "ctx_r6", "ctx_r7", "ctx_r8", "ctx_r9", "ɵɵtextInterpolate3", "ctx_r10", "countdown", "ctx_r27", "walletDetail", "payCode", "packageName", "phoneActive", "tax", "ɵɵtextInterpolate2", "ctx_r28", "formatNumber", "totalRemainingTraffic", "purchasedTraffic", "ctx_r29", "ctx_r30", "ctx_r31", "ctx_r32", "columnsShareWallet", "dataSetShareWallet", "pageSizeShareWallet", "pageNumberShareWallet", "optionTableShareWallet", "sortShareWallet", "searchShareWallet", "bind", "DataPoolListComponent_p_dialog_63_Template_p_dialog_visibleChange_0_listener", "$event", "_r34", "ctx_r33", "isShowModalDetail", "DataPoolListComponent_p_dialog_63_div_4_Template", "DataPoolListComponent_p_dialog_63_div_19_Template", "DataPoolListComponent_p_dialog_63_div_20_Template", "DataPoolListComponent_p_dialog_63_div_21_Template", "DataPoolListComponent_p_dialog_63_div_22_Template", "DataPoolListComponent_p_dialog_63_table_vnpt_31_Template", "ɵɵstyleMap", "_c1", "ctx_r11", "subCodeId", "trafficType", "getValueMethodAutoShare", "autoType", "toUpperCase", "includes", "timeToUse", "isShowTableInDialogDetail", "DataPoolListComponent_p_dialog_64_Template_p_dialog_visibleChange_0_listener", "_r36", "ctx_r35", "isShowRegisBySubOrPay", "DataPoolListComponent_p_dialog_64_Template_p_dialog_onHide_0_listener", "ctx_r37", "onHideRegisBySubOrPay", "DataPoolListComponent_p_dialog_64_Template_a_click_10_listener", "ctx_r38", "openReadMoreRegister", "DataPoolListComponent_p_dialog_64_Template_p_radioButton_ngModelChange_15_listener", "ctx_r39", "typeAutoShare", "DataPoolListComponent_p_dialog_64_Template_p_radioButton_ngModelChange_18_listener", "ctx_r40", "DataPoolListComponent_p_dialog_64_Template_p_button_click_28_listener", "ctx_r41", "DataPoolListComponent_p_dialog_64_Template_p_button_click_29_listener", "ctx_r42", "register", "_c2", "ctx_r12", "ɵɵpropertyInterpolate", "ɵɵtextInterpolate1", "DataPoolListComponent_p_dialog_65_Template_p_dialog_visibleChange_0_listener", "_r44", "ctx_r43", "isShowRegisByPayCode", "DataPoolListComponent_p_dialog_65_Template_p_dialog_onHide_0_listener", "ctx_r45", "onHideRegisByPayCode", "DataPoolListComponent_p_dialog_65_Template_a_click_10_listener", "ctx_r46", "DataPoolListComponent_p_dialog_65_Template_p_button_click_14_listener", "ctx_r47", "DataPoolListComponent_p_dialog_65_Template_p_button_click_15_listener", "ctx_r48", "ctx_r13", "ctx_r14", "DataPoolListComponent_p_dialog_78_Template_p_dialog_visibleChange_0_listener", "_r50", "ctx_r49", "isShowCancelBySubCode", "DataPoolListComponent_p_dialog_78_Template_p_dialog_onHide_0_listener", "ctx_r51", "DataPoolListComponent_p_dialog_78_Template_a_click_10_listener", "ctx_r52", "openReadMoreUnRegister", "DataPoolListComponent_p_dialog_78_Template_p_button_click_14_listener", "ctx_r53", "DataPoolListComponent_p_dialog_78_Template_p_button_click_15_listener", "ctx_r54", "onCancel", "ctx_r15", "DataPoolListComponent_p_dialog_79_Template_p_dialog_visibleChange_0_listener", "_r56", "ctx_r55", "isShowCancelByPayCode", "DataPoolListComponent_p_dialog_79_Template_p_dialog_onHide_0_listener", "ctx_r57", "DataPoolListComponent_p_dialog_79_Template_a_click_12_listener", "ctx_r58", "DataPoolListComponent_p_dialog_79_Template_p_button_click_16_listener", "ctx_r59", "DataPoolListComponent_p_dialog_79_Template_p_button_click_17_listener", "ctx_r60", "ctx_r16", "ɵɵpureFunction1", "_c3", "DataPoolListComponent", "startCountdown", "interval", "clearInterval", "setInterval", "resetTimer", "sendOTP", "resetTimerForRegister", "sendOTPRegister", "extractDateString", "dateTimeString", "day", "month", "year", "split", "openSubmit", "isSubmit", "constructor", "trafficWalletService", "shareManagementService", "formBuilder", "injector", "primengConfig", "minutesText", "allPermissions", "PERMISSIONS", "canView", "catchSearchDetail", "event", "searchInfo", "originSearch", "search", "pageNumber", "pageSize", "sort", "ngOnInit", "_this", "_asyncToGenerator", "me", "items", "label", "home", "icon", "routerLink", "getPayCode", "then", "resp", "getPackageCbb", "isShowModalWalletAuthen", "lstPackageName", "searchWalletCode", "searchPayCode", "value", "searchName", "searchPhone", "lstTrafficType", "phone", "accuracyWallet", "accuracyType", "subCode", "phoneNumber", "otp", "transId", "withoutOTP", "sharingType", "regisType", "new", "columns", "name", "key", "size", "align", "isShow", "isSort", "style", "cursor", "color", "funcClick", "id", "item", "resetDialogDetail", "getWalletDetail", "funcConvertText", "remain", "utilService", "convertNumberToString", "total", "optionTable", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "action", "tooltip", "func", "router", "navigate", "funcAppear", "canShare", "<PERSON><PERSON><PERSON><PERSON>", "DATAPOOL", "SHARE_WALLET", "LOCK", "showConfirmRegisterSubOrPay", "METHOD_AUTO_SHARE", "NONE", "LOCK_OPEN", "PAY_CODE", "showConfirmCancelShareByPayCode", "SUB_CODE", "showConfirmCancelShareBySubCode", "dataSet", "content", "getFormattedDate", "dayExprired", "result", "Math", "round", "formSearchWallet", "group", "formWithoutOTP", "setTranslation", "emptyFilterMessage", "emptyMessage", "isShowDiaglogOTPRegis", "type", "params", "messageCommonService", "onload", "response", "offload", "packageCbb", "map", "r", "filter", "index", "self", "findIndex", "t", "searchList", "filterList", "multiselect", "itemFilter", "rangeCalendar", "unixTimeString", "dropdown", "payCodeFilter", "Promise", "resolve", "reject", "page", "limit", "dataParams", "Object", "keys", "for<PERSON>ach", "searchWallet", "totalElements", "toString", "getListShareWallet", "onSubmitSearch", "body", "parseInt", "String", "replace", "res", "accuracyRequest", "controls", "control", "console", "log", "error", "success", "getById", "detail", "errorCode", "find", "el", "messageDetail", "setValidators", "max<PERSON><PERSON><PERSON>", "addValidators", "enable", "emitEvent", "disable", "checkDisable", "valid", "startDate", "getUnixTime", "endDate", "getFormattedDateCrd", "listDetail", "listShared", "dateString", "addDate", "date", "Date", "setDate", "getDate", "padStart", "getMonth", "getFullYear", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "parseDateTime", "datePart", "timePart", "dateParts", "timeParts", "getTime", "setUTCDate", "getUTCDate", "getUTCMonth", "getUTCFullYear", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "onHideOtp", "reset", "ngOnDestroy", "cancel", "hideDiaglogConfirmOTP", "openOTPRegister", "onRegister", "registerWithoutOTP", "message", "unRegisterWithoutOTP", "window", "open", "Intl", "NumberFormat", "format", "ɵɵdirectiveInject", "i1", "FormBuilder", "Injector", "i2", "PrimeNGConfig", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "DataPoolListComponent_Template", "rf", "ctx", "DataPoolListComponent_div_5_Template", "DataPoolListComponent_Template_search_filter_separate_searchDetail_6_listener", "DataPoolListComponent_Template_p_dialog_visibleChange_9_listener", "DataPoolListComponent_Template_form_ngSubmit_10_listener", "DataPoolListComponent_Template_p_radioButton_ngModelChange_15_listener", "DataPoolListComponent_Template_p_radioButton_ngModelChange_16_listener", "DataPoolListComponent_div_17_Template", "DataPoolListComponent_div_18_Template", "DataPoolListComponent_div_19_Template", "DataPoolListComponent_div_20_Template", "DataPoolListComponent_Template_input_ngModelChange_27_listener", "DataPoolListComponent_small_31_Template", "DataPoolListComponent_small_32_Template", "DataPoolListComponent_small_33_Template", "DataPoolListComponent_Template_input_ngModelChange_40_listener", "DataPoolListComponent_small_45_Template", "DataPoolListComponent_small_47_Template", "DataPoolListComponent_Template_p_button_click_49_listener", "DataPoolListComponent_Template_p_button_onClick_50_listener", "DataPoolListComponent_Template_p_dialog_visibleChange_51_listener", "DataPoolListComponent_Template_p_dialog_onHide_51_listener", "DataPoolListComponent_Template_button_click_54_listener", "DataPoolListComponent_div_56_Template", "DataPoolListComponent_Template_button_click_60_listener", "DataPoolListComponent_p_dialog_63_Template", "DataPoolListComponent_p_dialog_64_Template", "DataPoolListComponent_p_dialog_65_Template", "DataPoolListComponent_Template_p_dialog_visibleChange_66_listener", "DataPoolListComponent_Template_p_dialog_onHide_66_listener", "DataPoolListComponent_Template_form_ngSubmit_67_listener", "DataPoolListComponent_Template_button_click_70_listener", "DataPoolListComponent_div_72_Template", "DataPoolListComponent_Template_button_click_76_listener", "DataPoolListComponent_p_dialog_78_Template", "DataPoolListComponent_p_dialog_79_Template", "_c4", "CREATE_WALLET", "_c5", "_c6"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\data-pool\\trafficWallet\\list\\data-pool.list.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\data-pool\\trafficWallet\\list\\data-pool.list.component.html"], "sourcesContent": ["import {Component, Inject, Injector, <PERSON><PERSON><PERSON><PERSON>, OnInit} from \"@angular/core\";\r\nimport {ComponentBase} from \"../../../../component.base\";\r\nimport {FormBuilder, Validators} from \"@angular/forms\";\r\nimport {MenuItem, PrimeIcons, PrimeNGConfig} from \"primeng/api\";\r\nimport {ColumnInfo, OptionTable} from \"../../../common-module/table/table.component\";\r\nimport {TrafficWalletService} from \"../../../../service/datapool/TrafficWalletService\";\r\nimport {\r\n    FilterInputType,\r\n    Pagination,\r\n    SearchType,\r\n    SeperateFilterInfo,\r\n    SeperateSearchInfo\r\n} from \"src/app/template/common-module/search-filter-separate/search-filter-separate.component\";\r\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\r\nimport {ShareManagementService} from \"../../../../service/datapool/ShareManagementService\";\r\nimport { digitValidator } from \"src/app/template/common-module/validatorCustoms\";\r\n\r\n@Component({\r\n    selector: \"traffic-wallet-list\",\r\n    templateUrl: './data-pool.list.component.html'\r\n})\r\nexport class DataPoolListComponent extends ComponentBase implements OnInit, OnDestroy {\r\n\r\n    items: MenuItem[];\r\n    home: MenuItem\r\n    searchInfo: {\r\n        lstPackageName: Array<string> | null,\r\n        searchWalletCode: number | 0,\r\n        searchPayCode: number | 0,\r\n        value: string | \"\",\r\n        searchName: number | 0,\r\n        searchPhone:number | 0,\r\n        lstTrafficType:string | \"\"\r\n        //phuơng thức chia sẻ tự động, mặc định giá trị là -1\r\n        autoType: number| -1\r\n    };\r\n    accuracyWallet: {\r\n        accuracyType: number | null;\r\n        payCode: string | null;\r\n        subCode: string | null;\r\n        tax: string | null;\r\n        phoneNumber: number | null;\r\n        otp:number|null;\r\n        transId: number|null;\r\n    }\r\n    //body đăng ký/hủy chia sẻ tự động\r\n    withoutOTP: {\r\n        payCode: string | null;\r\n        subCode: string | null;\r\n        tax: string | null;\r\n        phoneNumber: number | null;\r\n        otp: number | null;\r\n        sharingType: string | null;\r\n        regisType: string | null;\r\n        //new = true là ví vừa được tạo, false là đã tạo trước đó\r\n        new: boolean\r\n    }\r\n    typeAutoShare: number | null;\r\n    formAccuracyWallet: any;\r\n    formSearchWallet: any;\r\n    formWithoutOTP: any;\r\n    isShowModalWalletAuthen: boolean;\r\n    columns: Array<ColumnInfo>;\r\n    dataSet: {\r\n        content: Array<any>,\r\n        total: number\r\n    };\r\n    countdown:number;\r\n    interval: any;\r\n    optionTable: OptionTable;\r\n    pageNumber: number;\r\n    pageSize: number;\r\n    sort: string;\r\n    pagination: Pagination;\r\n    isSubmit: boolean = false;\r\n    packageCbb: Array<{name:any,value:any}>;\r\n    payCodeFilter: Array<{name:any,value:any}>;\r\n    minutesText = this.tranService.translate(\"alert.label.minutes\");\r\n    originSearch:any;\r\n    allPermissions = CONSTANTS.PERMISSIONS;\r\n    isShowModalDetail: boolean = false;\r\n    subCodeId: number | string;\r\n    payCode: string | null;\r\n    walletDetail: any;\r\n    listDetail:any;\r\n    canView: boolean = false;\r\n    //hiển thị dialog đăng ký chọn mã ví hoặc mã thanh toán\r\n    isShowRegisBySubOrPay: boolean\r\n    //hiển thị dialog đăng ký theo mã thanh toán\r\n    isShowRegisByPayCode: boolean\r\n    //hiển thị dialog hủy đăng ký theo mã thanh toán\r\n    isShowCancelByPayCode: boolean\r\n    //hiển thị dialog hủy đăng ký theo mã ví\r\n    isShowCancelBySubCode: boolean\r\n    //dialog otp cho phần chia sẻ tự động\r\n    isShowDiaglogOTPRegis: boolean\r\n\r\n    columnsShareWallet: Array<ColumnInfo>;\r\n    dataSetShareWallet: {\r\n        content: Array<any>,\r\n        total: number\r\n    };\r\n    optionTableShareWallet: OptionTable;\r\n    pageNumberShareWallet: number;\r\n    pageSizeShareWallet: number;\r\n    sortShareWallet: string;\r\n    // Lưu lại purchasedTraffic để chia phần trăm khi xem chi tiết\r\n    purchasedTraffic: number;\r\n    trafficType: string;\r\n    isShowTableInDialogDetail: boolean;\r\n    startCountdown() {\r\n        if (this.interval) {\r\n            clearInterval(this.interval); // Dừng interval cũ nếu có\r\n        }\r\n\r\n        this.interval = setInterval(() => {\r\n            if (this.countdown > 0) {\r\n                this.countdown--;\r\n            } else {\r\n                clearInterval(this.interval);\r\n                this.interval = null; // Xóa tham chiếu sau khi hoàn tất\r\n            }\r\n        }, 1000);\r\n    }\r\n\r\n    resetTimer() {\r\n        this.countdown = 60;\r\n        clearInterval(this.interval);\r\n        this.startCountdown();\r\n        this.sendOTP()\r\n    }\r\n    //reset time cho otp chia sẻ tự động\r\n    resetTimerForRegister() {\r\n        this.countdown = 60;\r\n        clearInterval(this.interval);\r\n        this.startCountdown();\r\n        this.sendOTPRegister()\r\n    }\r\n\r\n    extractDateString(dateTimeString: string): string {\r\n        // Tách ngày, tháng, năm từ chuỗi ban đầu\r\n        const [day, month, year] = dateTimeString.split(/[/\\s:]+/);\r\n        // Trả về chuỗi ngày ở định dạng dd/MM/yyyy\r\n        return `${day}/${month}/${year}`;\r\n    }\r\n\r\n    openSubmit(){\r\n        this.isSubmit = true\r\n        this.countdown = 60\r\n        this.startCountdown()\r\n    }\r\n\r\n    searchList: Array<SeperateSearchInfo>;\r\n    filterList: Array<SeperateFilterInfo>;\r\n\r\n    constructor(\r\n        @Inject(TrafficWalletService) private trafficWalletService: TrafficWalletService,\r\n        @Inject(ShareManagementService) private shareManagementService : ShareManagementService,\r\n        private formBuilder: FormBuilder,\r\n        private injector: Injector,\r\n        private primengConfig: PrimeNGConfig,\r\n    ) {\r\n        super(injector);\r\n    }\r\n\r\n    catchSearchDetail(event){\r\n        this.searchInfo = this.originSearch\r\n        this.searchInfo = { ...this.searchInfo, ...event}\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n    }\r\n\r\n    async ngOnInit() {\r\n        let me = this;\r\n\r\n        this.items = [{ label: this.tranService.translate(`global.menu.trafficManagement`) }, { label: this.tranService.translate(\"global.menu.walletList\") },];\r\n\r\n        this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n        this.getPayCode(\"\",\"PAY_CODE\").then((resp)=> {\r\n            me.getPackageCbb(\"\", \"WALLET\");\r\n        });\r\n        this.isShowModalWalletAuthen = false;\r\n        this.searchInfo = {\r\n            lstPackageName: null,\r\n            searchWalletCode: 0,\r\n            searchPayCode: 0,\r\n            value: \"\",\r\n            searchName: 0,\r\n            searchPhone: 0,\r\n            lstTrafficType: null,\r\n            autoType: -1,\r\n        };\r\n\r\n        this.walletDetail ={\r\n            payCode: 1,\r\n            packageName: 2,\r\n            phone: 3,\r\n            tax: 4\r\n        };\r\n\r\n        this.accuracyWallet = {\r\n            accuracyType:0,\r\n            payCode:null,\r\n            subCode:null,\r\n            tax:null,\r\n            phoneNumber:null,\r\n            otp:null,\r\n            transId: 1,\r\n        };\r\n        this.withoutOTP = {\r\n            payCode:  null,\r\n            subCode: null,\r\n            tax: null,\r\n            phoneNumber: null,\r\n            otp: null,\r\n            sharingType: null,\r\n            regisType: null,\r\n            new: null,\r\n        }\r\n        this.columns = [\r\n            {\r\n                name: this.tranService.translate(\"datapool.label.walletCode\"),\r\n                key: \"subCode\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n                style:{\r\n                    cursor: \"pointer\",\r\n             color: \"var(--mainColorText)\"\r\n                },\r\n                funcClick(id, item) {\r\n                    me.subCodeId = item.subCode;\r\n                    me.resetDialogDetail()\r\n                    me.getWalletDetail();\r\n                    me.purchasedTraffic = item.purchasedTraffic;\r\n                    me.trafficType = item.trafficType;\r\n                },\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"datapool.label.packageName\"),\r\n                key: \"packageName\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false\r\n            }, {\r\n                name: this.tranService.translate(\"datapool.label.phone\"),\r\n                key: \"phoneActive\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false\r\n            }, {\r\n                name: this.tranService.translate(\"datapool.label.payCode\"),\r\n                key: \"payCode\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false\r\n            }, {\r\n                name: this.tranService.translate(\"datapool.label.trafficType\"),\r\n                key: \"trafficType\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false\r\n            }, {\r\n                name: this.tranService.translate('datapool.label.remainData')+\"/ \"+ this.tranService.translate(\"datapool.label.purchasedData\"),\r\n                key: \"remainData\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n                funcConvertText(value, item) {\r\n                    let remain = me.utilService.convertNumberToString(item.totalRemainingTraffic);\r\n                    let total =  me.utilService.convertNumberToString(item.purchasedTraffic);\r\n                    if(item.trafficType == \"Gói Data\"){\r\n                        return remain+\"/ \"+total +\" MB\";\r\n                    }else if(item.trafficType == \"Gói thoại\"){\r\n                        return remain+\"/ \"+total + me.minutesText;\r\n                    } else if((item.trafficType || \"\").toUpperCase().includes(\"Gói SMS\".toUpperCase())){\r\n                        return remain+\"/ \"+total + \" SMS\";\r\n                    }\r\n                    return item.totalRemainingTraffic+\"/ \"+item.purchasedTraffic;\r\n                },\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"datapool.label.usedTime\"),\r\n                key: \"timeUsed\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"datapool.label.methodAutoShare\"),\r\n                key: \"autoType\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n                funcConvertText(value) {\r\n                    return me.getValueMethodAutoShare(value)\r\n                }\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"datapool.label.accuracyDate\"),\r\n                key: \"accuracyDate\",\r\n                size: \"fit-content\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n                funcConvertText(value){\r\n                    if(value == null) return \"\";\r\n                    return me.extractDateString(value);\r\n                }\r\n            },\r\n        ];\r\n        this.payCode = null,\r\n        this.optionTable = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n            action: [\r\n                {\r\n                    icon: \"pi pi-window-maximize\",\r\n                    tooltip: this.tranService.translate(\"datapool.button.share\"),\r\n                    func: function (id, item) {\r\n                        me.router.navigate(['data-pool/walletMgmt/share',item.subCode])\r\n                    },\r\n                    funcAppear(id, item) {\r\n                        return (item.canShare && me.checkAuthen([me.allPermissions.DATAPOOL.SHARE_WALLET]))\r\n                    },\r\n                },\r\n                {\r\n                    icon: PrimeIcons.LOCK,\r\n                    tooltip: this.tranService.translate(\"datapool.placeholder.registerShare\"),\r\n                    func: function (id, item) {\r\n                        me.showConfirmRegisterSubOrPay(item)\r\n                    },\r\n                    funcAppear(id, item) {\r\n                        return (item.canShare && item.autoType == CONSTANTS.METHOD_AUTO_SHARE.NONE && me.checkAuthen([me.allPermissions.DATAPOOL.SHARE_WALLET]))\r\n                    },\r\n                },\r\n                {\r\n                    icon: PrimeIcons.LOCK_OPEN,\r\n                    tooltip: this.tranService.translate(\"datapool.placeholder.cancelShare\"),\r\n                    func: function (id, item) {\r\n                        if (item.autoType == CONSTANTS.METHOD_AUTO_SHARE.PAY_CODE) {\r\n                            me.payCode = item.payCode\r\n                            me.showConfirmCancelShareByPayCode(item)\r\n                        } else if (item.autoType == CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE) {\r\n                            me.payCode = item.payCode\r\n                            me.showConfirmCancelShareBySubCode(item)\r\n                        }\r\n                    },\r\n                    funcAppear(id, item) {\r\n                        return (item.canShare && (item.autoType == CONSTANTS.METHOD_AUTO_SHARE.PAY_CODE || item.autoType == CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE) && me.checkAuthen([me.allPermissions.DATAPOOL.SHARE_WALLET]))\r\n                    },\r\n                },\r\n                ],\r\n        }\r\n        this.pageNumber = 0;\r\n        this.pageSize = 10;\r\n        // this.sort = \"accuracyDate,desc\"\r\n        this.sort = \"\"\r\n        this.dataSet = {\r\n            content: [],\r\n            total: 0\r\n        }\r\n        this.columnsShareWallet = [{\r\n            name: this.tranService.translate(\"datapool.label.phoneFull\"),\r\n            key: \"phoneReceipt\",\r\n            size: \"fit-content\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: false\r\n        },{\r\n            name: this.tranService.translate(\"datapool.label.fullName\"),\r\n            key: \"name\",\r\n            size: \"fit-content\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: false\r\n        },{\r\n            name: this.tranService.translate(\"datapool.label.email\"),\r\n            key: \"email\",\r\n            size: \"fit-content\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: false\r\n        },{\r\n            name: this.tranService.translate(\"datapool.label.sharedTime\"),\r\n            key: \"timeUpdate\",\r\n            size: \"fit-content\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: false,\r\n            funcConvertText(value) {\r\n                return me.getFormattedDate(value)\r\n            }\r\n        },{\r\n            name: this.tranService.translate(\"datapool.label.usedDate\"),\r\n            key: \"sharingDay\",\r\n            size: \"fit-content\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: false,\r\n            funcConvertText(value, item) {\r\n                return me.getFormattedDate(value, item.dayExprired)\r\n            }\r\n        },{\r\n            name: this.tranService.translate(\"datapool.label.shared\"),\r\n            key: \"trafficShare\",\r\n            size: \"fit-content\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: false,\r\n            funcConvertText(value) {\r\n                value = me.formatNumber(value)\r\n                if ((me.trafficType || '').toUpperCase() == 'Gói Data'.toUpperCase()) return value + ' MB'\r\n                else if ((me.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) return value + ' SMS'\r\n                else if ((me.trafficType || '').toUpperCase() == 'Gói thoại'.toUpperCase()) return value + me.tranService.translate('alert.label.minutes')\r\n                else return value;\r\n            }\r\n        },{\r\n            name: this.tranService.translate(\"datapool.label.percentage\"),\r\n            key: \"trafficShare\",\r\n            size: \"fit-content\",\r\n            align: \"left\",\r\n            isShow: true,\r\n            isSort: false,\r\n            funcConvertText(value) {\r\n                let result = 100.0 * value/me.purchasedTraffic;\r\n                return Math.round(result * 100.0)/100.0 + \" %\"\r\n            }\r\n        },\r\n        ]\r\n        this.dataSetShareWallet = {\r\n            content: [],\r\n            total: 0\r\n        }\r\n        this.isShowTableInDialogDetail = false;\r\n        this.pageNumberShareWallet = 0,\r\n        this.pageSize = 10;\r\n        this.sortShareWallet = '';\r\n        this.optionTableShareWallet = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: false,\r\n            hasShowIndex: false,\r\n            hasShowToggleColumn: false,\r\n            action: null\r\n        }\r\n\r\n        this.formSearchWallet = this.formBuilder.group(this.searchInfo);\r\n        this.formAccuracyWallet = this.formBuilder.group(this.accuracyWallet);\r\n        this.formWithoutOTP = this.formBuilder.group(this.withoutOTP)\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n\r\n        this.primengConfig.setTranslation({\r\n            emptyFilterMessage:\"Không tìm thấy kết quả\",\r\n            emptyMessage:\"Không tìm thấy kết quả\"\r\n        })\r\n        this.originSearch = this.searchInfo\r\n        this.isShowRegisBySubOrPay = false\r\n        this.isShowRegisByPayCode = false\r\n        this.isShowCancelByPayCode = false\r\n        this.isShowCancelBySubCode = false\r\n        this.typeAutoShare = 1;\r\n        this.isShowDiaglogOTPRegis = false;\r\n    }\r\n\r\n    getPackageCbb(packageName, type){\r\n        let me = this;\r\n        let params = {\r\n            packageName,\r\n            type\r\n        }\r\n        this.messageCommonService.onload()\r\n        this.trafficWalletService.getPackageCbb(params, (response) => {\r\n            this.messageCommonService.offload()\r\n            me.packageCbb = response.map(r => {\r\n                return {\r\n                    name: r.name,\r\n                    value: r.value\r\n                }\r\n            });\r\n\r\n            me.packageCbb = me.packageCbb.filter((item, index, self) =>\r\n                index === self.findIndex((t) => (\r\n                    t.name === item.name && t.value === item.value\r\n                ))\r\n            );\r\n\r\n            this.searchList = [{\r\n                name:this.tranService.translate(\"datapool.label.walletCode\"),\r\n                key:\"searchWalletCode\"\r\n            },{\r\n                name:this.tranService.translate(\"datapool.label.payCode\"),\r\n                key:\"searchPayCode\"\r\n            },{\r\n                name:this.tranService.translate(\"datapool.label.phone\"),\r\n                key:\"searchPhone\"\r\n            }];\r\n\r\n            this.filterList = [{\r\n                name:this.tranService.translate(\"datapool.label.trafficType\"),\r\n                type: FilterInputType.multiselect,\r\n                items:[{name:\"Gói Data\", value:\"Gói Data\"},{name:\"Gói SMS ngoại mạng\", value:\"Gói SMS ngoại mạng\"},{name:\"Gói SMS VNP\", value:\"Gói SMS VNP\"}],\r\n                key:\"lstTrafficType\",\r\n                itemFilter: true\r\n            },{\r\n                name:this.tranService.translate(\"datapool.label.packageName\"),\r\n                type: FilterInputType.multiselect,\r\n                items: this.packageCbb,\r\n                key:\"lstPackageName\",\r\n                itemFilter:true\r\n            },{\r\n                name:this.tranService.translate(\"datapool.label.accuracyDate\"),\r\n                type: FilterInputType.rangeCalendar,\r\n                key:[\"accuracyStartDate\",\"accuracyEndDate\"],\r\n                unixTimeString: true\r\n            },{\r\n                name:this.tranService.translate(\"datapool.label.usedTime\"),\r\n                type: FilterInputType.rangeCalendar,\r\n                key:[\"startDate\",\"endDate\"],\r\n                unixTimeString: true\r\n            },\r\n                {\r\n                    name:this.tranService.translate(\"datapool.label.methodAutoShare\"),\r\n                    type: FilterInputType.dropdown,\r\n                    items:[{name: this.tranService.translate(\"datapool.methodAutoShare.subCode\"), value: CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE},\r\n                        {name: this.tranService.translate(\"datapool.methodAutoShare.payCode\"), value: CONSTANTS.METHOD_AUTO_SHARE.PAY_CODE},\r\n                        {name: this.tranService.translate(\"datapool.methodAutoShare.none\"), value: CONSTANTS.METHOD_AUTO_SHARE.NONE},\r\n                        ],\r\n                    key:\"autoType\",\r\n                    itemFilter: true\r\n                },\r\n                {\r\n                    name:this.tranService.translate(\"datapool.label.payCode\"),\r\n                    type: FilterInputType.multiselect,\r\n                    items: this.payCodeFilter,\r\n                    key:\"lstPaymentCode\",\r\n                    itemFilter: true\r\n                }\r\n            ]\r\n        },null, ()=>{\r\n            this.messageCommonService.offload()\r\n        })\r\n    }\r\n\r\n    getPayCode(packageName, type) : Promise<any> {\r\n        let me = this;\r\n        let params = {\r\n            packageName,\r\n            type\r\n        }\r\n        this.messageCommonService.onload()\r\n        return new Promise((resolve,reject) => {\r\n            this.trafficWalletService.getPackageCbb(params, (response) => {\r\n                this.messageCommonService.offload()\r\n                me.payCodeFilter = response.map(r => {\r\n                    return {\r\n                        name: r.name,\r\n                        value: r.value\r\n                    }\r\n                });\r\n\r\n                me.payCodeFilter = me.payCodeFilter.filter((item, index, self) =>\r\n                    index === self.findIndex((t) => (\r\n                        t.name === item.name && t.value === item.value\r\n                    ))\r\n                );\r\n                resolve(\"\");\r\n            },null, ()=>{\r\n                this.messageCommonService.offload()\r\n            })\r\n        });\r\n\r\n    }\r\n\r\n    search(page, limit, sort, params) {\r\n        let me = this;\r\n        this.pageNumber = page;\r\n        this.pageSize = limit;\r\n        // this.sort = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            // sort\r\n        }\r\n        this.messageCommonService.onload()\r\n\r\n        Object.keys(this.searchInfo).forEach(key => {\r\n            dataParams[key] = this.searchInfo[key];\r\n            if (key =='autoType' && this.searchInfo[key] == null) {\r\n                dataParams[key] = -1\r\n            }\r\n        })\r\n\r\n        this.trafficWalletService.searchWallet(dataParams, (response) => {\r\n            me.dataSet = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n        }, null, () => {\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n    searchShareWallet(page,limit, sort, params) {\r\n        let me = this;\r\n            this.pageNumberShareWallet = page,\r\n            this.pageSizeShareWallet = limit,\r\n            this.sortShareWallet = sort\r\n        let dataParams = {\r\n                page,\r\n                size: limit,\r\n                sort: sort,\r\n                subCode: me.subCodeId.toString(),\r\n        }\r\n        me.messageCommonService.onload()\r\n        this.trafficWalletService.getListShareWallet(dataParams, (response) => {\r\n            me.dataSetShareWallet = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n            me.isShowTableInDialogDetail = true;\r\n        }, null, () => {\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    onSubmitSearch() {\r\n        this.pageNumber = 0;\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n    }\r\n\r\n    // send OTP\r\n    sendOTP(){\r\n        let body = {};\r\n        let me = this;\r\n            body = {\r\n                phoneNumber: parseInt(String(this.accuracyWallet.phoneNumber)?.replace(/^0/,\"84\"))\r\n            }\r\n        this.messageCommonService.onload()\r\n        this.shareManagementService.sendOTP(body, (res) => {\r\n            // me.messageCommonService.success(me.tranService.translate(\"global.message.saveSuccess\"));\r\n            me.openSubmit();\r\n            // me.isShowModalWalletAuthen = false;\r\n            // me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)\r\n        }, null, () => {\r\n            me.messageCommonService.offload()\r\n        })\r\n    }\r\n\r\n    // Xac thuc va tao vi luu luong\r\n    accuracyRequest() {\r\n\r\n        Object.keys(this.formAccuracyWallet.controls).forEach(key => {\r\n            const control = this.formAccuracyWallet.get(key);\r\n            if (control.invalid) {\r\n              console.log('Field:', key, 'is invalid. Errors:', control.errors);\r\n            }\r\n          });\r\n        let me = this;\r\n        let body = {};\r\n        if (this.accuracyWallet.accuracyType == 0) {\r\n            body = {\r\n                accuracyType: this.accuracyWallet.accuracyType ,\r\n                subCode: this.formAccuracyWallet.get(\"subCode\").value,\r\n                tax: this.accuracyWallet.tax,\r\n                phoneNumber: this.accuracyWallet.phoneNumber,\r\n                transId: this.accuracyWallet.transId,\r\n                otp: this.formAccuracyWallet.get(\"otp\").value\r\n            }\r\n        } else {\r\n            body = {\r\n                accuracyType: this.accuracyWallet.accuracyType,\r\n                payCode: this.formAccuracyWallet.get(\"payCode\").value,\r\n                tax: this.accuracyWallet.tax,\r\n                phoneNumber: this.accuracyWallet.phoneNumber,\r\n                transId: this.accuracyWallet.transId,\r\n                otp: this.formAccuracyWallet.get(\"otp\").value\r\n            }\r\n        }\r\n\r\n        me.withoutOTP = {\r\n            payCode: this.formAccuracyWallet.get(\"payCode\").value,\r\n            subCode: this.formAccuracyWallet.get(\"subCode\").value,\r\n            tax: this.accuracyWallet.tax,\r\n            phoneNumber: this.accuracyWallet.phoneNumber,\r\n            otp: null,\r\n            sharingType: '1',\r\n            regisType: '1',\r\n            new: true,\r\n        }\r\n        this.messageCommonService.onload();\r\n        this.trafficWalletService.accuracyWallet(body, (res) => {\r\n            if (res.error==\"BSS-00000000\"){\r\n                me.messageCommonService.success(me.tranService.translate(\"global.message.accuracySuccess\"));\r\n                // tạo bằng mã ví\r\n                if (this.accuracyWallet.accuracyType == 0) {\r\n                    this.messageCommonService.onload();\r\n                    me.trafficWalletService.getById({subCode: me.withoutOTP.subCode}, (detail) => {\r\n                        if(detail.autoType !== 0) {\r\n                            me.showConfirmRegisterSubOrPay(detail)\r\n                        }\r\n                    },(error)=>{\r\n                        if(error.error.error.errorCode === \"error.forbidden.view.detail\"){\r\n                            this.messageCommonService.error(\"Bạn không có quyền truy cập thông tin này\")\r\n                            this.router.navigate([\"/data-pool/walletMgmt/list\"])\r\n                        }\r\n                    },()=>{ this.messageCommonService.offload() })\r\n                } else {\r\n                    // tạo bằng mã thanh toán call api search sau đó tìm trong list có autoType nào = 0 hay chưa\r\n                    // Loại auto (0: Mã thanh toán, 1: mã ví, 2: chưa đăng ký)\r\n                    this.trafficWalletService.searchWallet({\r\n                        \"page\": 0,\r\n                        \"size\": 99999999,\r\n                        \"lstPackageName\": null,\r\n                        \"searchWalletCode\": 1,\r\n                        \"searchPayCode\": 1,\r\n                        \"value\": this.formAccuracyWallet.get(\"payCode\").value,\r\n                        \"searchName\": 0,\r\n                        \"searchPhone\": 1,\r\n                        \"lstTrafficType\": null,\r\n                        \"autoType\": -1\r\n                    }, (response) => {\r\n                        if(me.dataSet?.content.find(el => el.autoType !== 0)){\r\n                            me.isShowRegisByPayCode = true;\r\n                        }\r\n                    }, null, () => {\r\n                        me.messageCommonService.offload();\r\n                    })\r\n                }\r\n            } else if(res.error ===\"err.subCode.exists\") {\r\n                me.messageCommonService.error(\"Ví lưu lượng đã được xác thực\")\r\n                return\r\n            } else {\r\n                me.messageCommonService.error(res.messageDetail);\r\n                return\r\n            }\r\n\r\n            me.isShowModalWalletAuthen = false;\r\n            // me.formAccuracyWallet.reset();\r\n            this.isSubmit = false\r\n            me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)\r\n        }, null, () => {\r\n            me.messageCommonService.offload()\r\n        })\r\n    }\r\n\r\n    showModalAccuracy() {\r\n        this.isShowModalWalletAuthen = true;\r\n        this.accuracyWallet = {\r\n            accuracyType:0,\r\n            payCode:null,\r\n            subCode:null,\r\n            tax:null,\r\n            phoneNumber:null,\r\n            otp:null,\r\n            transId: 1,\r\n        }\r\n        this.formAccuracyWallet = this.formBuilder.group(this.accuracyWallet);\r\n        this.formAccuracyWallet.get(\"subCode\").setValidators([\r\n            Validators.required,\r\n            Validators.maxLength(64),\r\n            Validators.pattern('^[a-zA-Z0-9\\\\- _\\\\u00C0-\\\\u024F\\\\u1E00-\\\\u1EFF]+$')\r\n        ])\r\n        this.formAccuracyWallet.get(\"payCode\").addValidators([\r\n            Validators.required,\r\n            Validators.maxLength(64),\r\n            Validators.pattern('^[a-zA-Z0-9\\\\- _\\\\u00C0-\\\\u024F\\\\u1E00-\\\\u1EFF]+$')\r\n        ])\r\n        this.formAccuracyWallet.get(\"tax\").setValidators([\r\n            Validators.required,\r\n            Validators.maxLength(64),\r\n            Validators.pattern('^[a-zA-Z0-9_-]+$')\r\n        ])\r\n        this.formAccuracyWallet.get(\"phoneNumber\").setValidators([\r\n            Validators.required,\r\n            Validators.pattern(/^0[0-9]{9,10}$/)\r\n        ])\r\n\r\n        this.formAccuracyWallet.get(\"otp\").setValidators([\r\n            Validators.required,\r\n            digitValidator(6),\r\n        ])\r\n\r\n        this.formAccuracyWallet.get(\"subCode\").enable({emitEvent:false})\r\n        this.formAccuracyWallet.get('payCode').disable({ emitEvent: false });\r\n        this.formAccuracyWallet.controls.tax.enable();\r\n        this.formAccuracyWallet.controls.phoneNumber.enable();\r\n    }\r\n\r\n    checkDisable(){\r\n        if(this.formAccuracyWallet.get(\"accuracyType\").value == 0){\r\n            this.formAccuracyWallet.get(\"subCode\").enable({emitEvent:false})\r\n            this.formAccuracyWallet.get('payCode').disable({ emitEvent: false });\r\n            return !(this.formAccuracyWallet.controls.subCode.valid &&\r\n                   this.formAccuracyWallet.controls.tax.valid &&\r\n                   this.formAccuracyWallet.controls.phoneNumber.valid)\r\n        }else{\r\n            this.formAccuracyWallet.get(\"payCode\").enable({emitEvent:false})\r\n            this.formAccuracyWallet.get('subCode').disable({ emitEvent: false });\r\n            return !(this.formAccuracyWallet.controls.payCode.valid &&\r\n                   this.formAccuracyWallet.controls.tax.valid &&\r\n                   this.formAccuracyWallet.controls.phoneNumber.valid)\r\n        }\r\n        return this.formAccuracyWallet.invalid;\r\n    }\r\n\r\n    getWalletDetail() {\r\n        let me = this;\r\n        me.messageCommonService.onload();\r\n\r\n        me.trafficWalletService.getById({subCode:me.subCodeId}, (res) => {\r\n            me.walletDetail = res\r\n            let startDate = me.getUnixTime(res.startDate);\r\n            let endDate = me.getUnixTime(res.endDate);\r\n            me.walletDetail.timeToUse = (endDate-startDate)/(60 * 60 * 24) +\" ngày \" +\"[\"+me.getFormattedDateCrd(res.startDate)+\"-\"+me.getFormattedDateCrd(res.endDate)+\"]\"\r\n            me.listDetail = res.listShared\r\n            me.canView = true\r\n            me.isShowModalDetail = true;\r\n            me.searchShareWallet(0, 10, me.sortShareWallet, {subCode: me.subCodeId})\r\n        },(error)=>{\r\n            if(error.error.error.errorCode === \"error.forbidden.view.detail\"){\r\n                this.messageCommonService.error(\"Bạn không có quyền truy cập thông tin này\")\r\n                this.router.navigate([\"/data-pool/walletMgmt/list\"])\r\n            }\r\n        },()=>{ this.messageCommonService.offload() })\r\n    };\r\n\r\n    getFormattedDate(dateString: string, addDate?: number): string {\r\n        let me = this;\r\n        let date = new Date(dateString);\r\n\r\n        if (addDate) {\r\n            date.setDate(date.getDate() + addDate);\r\n        }\r\n\r\n        const day = date.getDate().toString().padStart(2, '0'); // Lấy ngày và thêm số 0 nếu cần\r\n        const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Lấy tháng (lưu ý tháng trong JavaScript bắt đầu từ 0)\r\n        const year = date.getFullYear();\r\n        const hours = date.getHours().toString().padStart(2, '0');\r\n        const minutes = date.getMinutes().toString().padStart(2, '0');\r\n        const seconds = date.getSeconds().toString().padStart(2, '0');\r\n\r\n        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;\r\n    };\r\n\r\n    parseDateTime(dateString) {\r\n        // Split the date and time parts\r\n        let [datePart, timePart] = dateString.split(' ');\r\n\r\n        // Split the date part by '/'\r\n        let dateParts = datePart.split('/');\r\n        let day = parseInt(dateParts[0], 10);\r\n        let month = parseInt(dateParts[1], 10) - 1; // Months are 0-based in JavaScript\r\n        let year = parseInt(dateParts[2], 10);\r\n\r\n        // Split the time part by ':'\r\n        let timeParts = timePart.split(':');\r\n        let hours = parseInt(timeParts[0], 10);\r\n        let minutes = parseInt(timeParts[1], 10);\r\n        let seconds = parseInt(timeParts[2], 10);\r\n\r\n        // Create a new Date object\r\n        return new Date(year, month, day, hours, minutes, seconds);\r\n    };\r\n\r\n    getUnixTime(dateString: string): number {\r\n        const date = new Date(dateString);\r\n        return date.getTime() / 1000; // Chia cho 1000 để chuyển từ milliseconds sang seconds\r\n    };\r\n\r\n    getFormattedDateCrd(dateString: string, addDate?: number): string {\r\n        let date = new Date(dateString);\r\n\r\n        if (addDate) {\r\n            date.setUTCDate(date.getUTCDate() + addDate);\r\n        }\r\n\r\n        const day = date.getUTCDate().toString().padStart(2, '0'); // Lấy ngày và thêm số 0 nếu cần\r\n        const month = (date.getUTCMonth() + 1).toString().padStart(2, '0'); // Lấy tháng (lưu ý tháng trong JavaScript bắt đầu từ 0)\r\n        const year = date.getUTCFullYear();\r\n        const hours = date.getUTCHours().toString().padStart(2, '0');\r\n        const minutes = date.getUTCMinutes().toString().padStart(2, '0');\r\n        const seconds = date.getUTCSeconds().toString().padStart(2, '0');\r\n\r\n        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;\r\n    }\r\n\r\n    onHideOtp(){\r\n        this.formAccuracyWallet.controls.otp.reset()\r\n    }\r\n\r\n    ngOnDestroy(): void {\r\n        clearInterval(this.interval)\r\n    }\r\n\r\n    showConfirmRegisterSubOrPay(item) {\r\n        let me = this;\r\n        me.isShowRegisBySubOrPay = true\r\n        me.withoutOTP = {\r\n            payCode: item.payCode,\r\n            subCode: item.subCode,\r\n            tax: item.tax,\r\n            phoneNumber: item.phoneActive,\r\n            otp: null,\r\n            sharingType: '1',\r\n            regisType: '1',\r\n            new: false\r\n        }\r\n    }\r\n\r\n    showConfirmCancelShareByPayCode(item) {\r\n        let me = this;\r\n        this.isShowCancelByPayCode = true\r\n        me.withoutOTP = {\r\n            payCode: item.payCode,\r\n            subCode: item.autoType == CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE ? item.subCode : null,\r\n            tax: item.tax,\r\n            phoneNumber: item.phoneActive,\r\n            otp: null,\r\n            sharingType: '1',\r\n            regisType: '1',\r\n            new: false,\r\n        }\r\n    }\r\n\r\n    showConfirmCancelShareBySubCode(item) {\r\n        let me = this;\r\n        this.isShowCancelBySubCode = true\r\n        me.withoutOTP = {\r\n            payCode: item.payCode,\r\n            subCode: item.autoType == CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE ? item.subCode : null,\r\n            tax: item.tax,\r\n            phoneNumber: item.phoneActive,\r\n            otp: null,\r\n            sharingType: '1',\r\n            regisType: '1',\r\n            new: false,\r\n        }\r\n    }\r\n\r\n    onHideRegisBySubOrPay() {\r\n        let me = this;\r\n        me.isShowRegisBySubOrPay = false\r\n        me.typeAutoShare = 1\r\n    }\r\n    onHideRegisByPayCode() {\r\n        let me = this;\r\n        me.isShowRegisByPayCode = false\r\n        me.typeAutoShare = 1\r\n    }\r\n\r\n    register() {\r\n        let me = this;\r\n        // thanh toán = mã ví mà chưa chọn cách đăng kí nào -> Hiển thị thông báo “Hãy chọn một cách đăng ký”\r\n        // thanh toán = mã thanh toán -> ko cần chọn\r\n\r\n        if (me.withoutOTP.new == true) { //nếu là ví mới thì check theo accuracyType\r\n            if (me.accuracyWallet.accuracyType === 0 && me.typeAutoShare != null) {\r\n                me.isShowRegisBySubOrPay = false;\r\n                me.isShowRegisByPayCode = false\r\n                me.isShowDiaglogOTPRegis = true;\r\n                me.formAccuracyWallet.reset()\r\n                me.sendOTPRegister()\r\n            } else if (me.accuracyWallet.accuracyType === 1) {\r\n                me.isShowRegisBySubOrPay = false;\r\n                me.isShowRegisByPayCode = false\r\n                me.isShowDiaglogOTPRegis = true;\r\n                me.formAccuracyWallet.reset()\r\n                me.sendOTPRegister()\r\n            } else {\r\n                me.messageCommonService.error(me.tranService.translate(\"datapool.text.chooseRegistrationMethod\"));\r\n            }\r\n        } else if (me.withoutOTP.new == false) { // nếu là ví cũ thích check typeAutoShare đã chọn\r\n            if(me.typeAutoShare != null) {\r\n                me.isShowRegisBySubOrPay = false;\r\n                me.isShowRegisByPayCode = false\r\n                me.isShowDiaglogOTPRegis = true;\r\n                me.sendOTPRegister()\r\n            }else {\r\n                me.messageCommonService.error(me.tranService.translate(\"datapool.text.chooseRegistrationMethod\"));\r\n            }\r\n        }\r\n    }\r\n\r\n    cancel() {\r\n    }\r\n\r\n    hideDiaglogConfirmOTP() {\r\n        let me = this;\r\n        me.isShowDiaglogOTPRegis = false\r\n    }\r\n\r\n    // send OTP Register\r\n    sendOTPRegister() {\r\n        let body = {};\r\n        let me = this;\r\n        body = {\r\n            phoneNumber: parseInt(String(this.withoutOTP.phoneNumber)?.replace(/^0/, \"84\"))\r\n        }\r\n        // me.openOTPRegister();\r\n        this.messageCommonService.onload()\r\n        this.shareManagementService.sendOTP(body, (res) => {\r\n            me.openOTPRegister();\r\n        }, null, () => {\r\n            me.messageCommonService.offload()\r\n        })\r\n    }\r\n\r\n    openOTPRegister() {\r\n        this.countdown = 60\r\n        this.startCountdown()\r\n    }\r\n\r\n    onRegister() {\r\n        let me = this\r\n        let body = {\r\n            payCode: me.withoutOTP.payCode,\r\n            subCode:  me.typeAutoShare && me.typeAutoShare == 1 ? me.withoutOTP.subCode : null,\r\n            tax: me.withoutOTP.tax,\r\n            phoneNumber: me.withoutOTP.phoneNumber,\r\n            otp: this.formWithoutOTP.get(\"otp\").value,\r\n            sharingType: '1',\r\n            regisType: '1',\r\n        }\r\n        this.messageCommonService.onload()\r\n        this.trafficWalletService.registerWithoutOTP(body, (res) => {\r\n            if (res && res.error) {\r\n                me.messageCommonService.error(res.message)\r\n            } else {\r\n                me.messageCommonService.success(me.tranService.translate(\"datapool.message.registerSuccess\"))\r\n            }\r\n            me.isShowDiaglogOTPRegis = false;\r\n            me.formWithoutOTP.reset()\r\n            me.onSubmitSearch()\r\n        }, null, () => {\r\n            me.messageCommonService.offload()\r\n        })\r\n    }\r\n\r\n    onCancel() {\r\n        let me = this\r\n        let body = {\r\n            payCode: me.withoutOTP.payCode,\r\n            subCode: me.withoutOTP.subCode,\r\n            tax: me.withoutOTP.tax,\r\n            phoneNumber: me.withoutOTP.phoneNumber,\r\n            otp: me.withoutOTP.otp,\r\n            sharingType: '1',\r\n            regisType: '2',\r\n        }\r\n        this.messageCommonService.onload()\r\n        this.trafficWalletService.unRegisterWithoutOTP(body, (res) => {\r\n            me.messageCommonService.success(me.tranService.translate(\"datapool.message.cancelSuccess\"))\r\n            me.isShowCancelBySubCode = false;\r\n            me.isShowCancelByPayCode = false;\r\n            me.onSubmitSearch()\r\n        }, null, () => {\r\n            me.messageCommonService.offload()\r\n        })\r\n    }\r\n    getValueMethodAutoShare(value) {\r\n        let me = this;\r\n        if (value == CONSTANTS.METHOD_AUTO_SHARE.PAY_CODE) {\r\n            return me.tranService.translate(\"datapool.methodAutoShare.payCode\")\r\n        } else if (value == CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE) {\r\n            return me.tranService.translate(\"datapool.methodAutoShare.subCode\")\r\n        } else {\r\n            return me.tranService.translate(\"datapool.methodAutoShare.none\")\r\n        }\r\n    }\r\n    // openReadMore() {\r\n    //     window.open(`/#/docs/autoShare`, '_blank');\r\n    // }\r\n    openReadMoreRegister() {\r\n        window.open(`/#/docs/registerNonOTP`, '_blank');\r\n    }\r\n    openReadMoreUnRegister() {\r\n        window.open(`/#/docs/unsubNonOTP`, '_blank');\r\n    }\r\n\r\n    resetDialogDetail() {\r\n        let me = this;\r\n        me.walletDetail ={\r\n        };\r\n        me.dataSetShareWallet = {\r\n            content: [],\r\n            total: 0,\r\n        }\r\n        me.isShowTableInDialogDetail = false\r\n    }\r\n\r\n    formatNumber(value: number): string {\r\n        return new Intl.NumberFormat('vi-VN').format(value);\r\n    }\r\n}\r\n", "<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{tranService.translate(\"global.menu.walletList\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n\r\n    <div *ngIf=\"checkAuthen([allPermissions.DATAPOOL.CREATE_WALLET])\" class=\"col-5 flex flex-row justify-content-end align-items-center\">\r\n        <p-button\r\n            styleClass=\"p-button-info\"\r\n            icon=\"pi pi-plus\"\r\n            [label]=\"tranService.translate('datapool.button.createWallet')\"\r\n            (click)=\"showModalAccuracy()\">\r\n        </p-button>\r\n    </div>\r\n</div>\r\n\r\n\r\n<search-filter-separate\r\n [searchList]=\"searchList\"\r\n [filterList]=\"filterList\"\r\n (searchDetail)=\"catchSearchDetail($event)\"\r\n></search-filter-separate>\r\n\r\n\r\n<table-vnpt\r\n    [tableId]=\"'tableTrafficWallet'\"\r\n    [fieldId]=\"'id'\"\r\n    [columns]=\"columns\"\r\n    [dataSet]=\"dataSet\"\r\n    [options]=\"optionTable\"\r\n    [pageNumber]=\"pageNumber\"\r\n    [loadData]=\"search.bind(this)\"\r\n    [pageSize]=\"pageSize\"\r\n    [sort]=\"sort\"\r\n    [params]=\"searchInfo\"\r\n    [labelTable]=\"tranService.translate('global.menu.trafficManagement')\"\r\n></table-vnpt>\r\n<!-- dialog tạo + xác thực ví-->\r\n<div class=\"flex justify-content-center responsive-dialog\">\r\n    <p-dialog [header]=\"tranService.translate('datapool.label.accuracyWallet')\" [(visible)]=\"isShowModalWalletAuthen\" [modal]=\"true\" [style]=\"{ width: '700px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n        <form class=\"mt-3\" [formGroup]=\"formAccuracyWallet\" (ngSubmit)=\"accuracyRequest()\">\r\n            <div class=\"flex flex-row flex-wrap justify-content-between w-full\">\r\n                <div>{{tranService.translate(\"datapool.label.authenMethod\")}}</div>\r\n                <!-- phuong thuc thanh toan -->\r\n                <div class=\"w-full field grid flex align-items-center justify-content-start\">\r\n                    <p-radioButton\r\n                        [label]=\"tranService.translate('datapool.label.subCode')\"\r\n                        name=\"accuracyType\" [value]=\"0\"\r\n                        [(ngModel)]=\"accuracyWallet.accuracyType\"\r\n                        formControlName=\"accuracyType\"\r\n                        class=\"p-3\"\r\n                        inputId=\"1\">\r\n                    </p-radioButton>\r\n                    <p-radioButton\r\n                        [label]=\"tranService.translate('datapool.label.payCode')\"\r\n                        name=\"accuracyType\"\r\n                        [value]=\"1\"\r\n                        inputId=\"2\"\r\n                        [(ngModel)]=\"accuracyWallet.accuracyType\"\r\n                        formControlName=\"accuracyType\">\r\n                    </p-radioButton>\r\n                </div>\r\n                <!-- wallet Code -->\r\n                <div *ngIf=\"accuracyWallet.accuracyType == 0\" class=\"w-full field grid mb-0\">\r\n                    <label htmlFor=\"tax\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"datapool.label.subCode\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\">\r\n                        <input class=\"w-full\"\r\n                               pInputText id=\"subCode\"\r\n                               formControlName=\"subCode\"\r\n                               [placeholder]=\"tranService.translate('datapool.text.subCode')\"\r\n                        />\r\n                    </div>\r\n                </div>\r\n                <div *ngIf=\"accuracyWallet.accuracyType == 0\" class=\"w-full field grid m-0 p-0 mb-3\">\r\n                    <label htmlFor=\"tax\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div *ngIf=\"formAccuracyWallet.get('subCode').invalid && formAccuracyWallet.get('subCode').dirty\">\r\n                        <div *ngIf=\"formAccuracyWallet.get('subCode').errors.required\" class=\"text-red-500\" >{{tranService.translate(\"global.message.required\")}}</div>\r\n                        <div *ngIf=\"formAccuracyWallet.get('subCode').errors.pattern\" class=\"text-red-500\" >{{tranService.translate(\"global.message.formatContainVN\")}}</div>\r\n                        <div *ngIf=\"formAccuracyWallet.get('subCode').errors.maxlength\" class=\"text-red-500\" >{{tranService.translate(\"global.message.maxLength\",{len:64})}}</div>\r\n                    </div>\r\n                </div>\r\n                <!-- payment Code -->\r\n                <div *ngIf=\"accuracyWallet.accuracyType == 1\" class=\"w-full field grid mb-0\">\r\n                    <label htmlFor=\"tax\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"datapool.label.payCode\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\">\r\n                        <input class=\"w-full\"\r\n                               pInputText id=\"payCode\"\r\n                               formControlName=\"payCode\"\r\n                               [placeholder]=\"tranService.translate('datapool.text.payCode')\"\r\n                        />\r\n                    </div>\r\n                </div>\r\n                <div *ngIf=\"accuracyWallet.accuracyType == 1\" class=\"w-full field grid m-0 p-0 mb-3\">\r\n                    <label htmlFor=\"tax\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div *ngIf=\"formAccuracyWallet.get('payCode').invalid && formAccuracyWallet.get('payCode').dirty\">\r\n                        <div *ngIf=\"formAccuracyWallet.get('payCode').errors.required\" class=\"text-red-500\" >{{tranService.translate(\"global.message.required\")}}</div>\r\n                        <div *ngIf=\"formAccuracyWallet.get('payCode').errors.pattern\" class=\"text-red-500\" >{{tranService.translate(\"global.message.formatContainVN\")}}</div>\r\n                        <div *ngIf=\"formAccuracyWallet.get('payCode').errors.maxlength\" class=\"text-red-500\" >{{tranService.translate(\"global.message.maxLength\",{len:64})}}</div>\r\n                    </div>\r\n                </div>\r\n                <!-- tax -->\r\n                <div class=\"w-full field grid\">\r\n                    <label htmlFor=\"tax\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"datapool.label.tax\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\">\r\n                        <input class=\"w-full\"\r\n                               pInputText id=\"tax\"\r\n                               [(ngModel)]=\"accuracyWallet.tax\"\r\n                               formControlName=\"tax\"\r\n                               [placeholder]=\"tranService.translate('datapool.text.tax')\"\r\n                        />\r\n                    </div>\r\n                </div>\r\n                <!-- error tax -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"fullName\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"col\">\r\n                        <small class=\"text-red-500\" *ngIf=\"formAccuracyWallet.controls.tax.dirty && formAccuracyWallet.controls.tax.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                        <small class=\"text-red-500\" *ngIf=\"formAccuracyWallet.controls.tax.errors?.maxlength\">{{tranService.translate(\"global.message.maxLength\",{len:64})}}</small>\r\n                        <small class=\"text-red-500\" *ngIf=\"formAccuracyWallet.controls.tax.errors?.pattern\">{{tranService.translate(\"datapool.message.patternError\")}}</small>\r\n                    </div>\r\n                </div>\r\n                <!-- phone -->\r\n                <div class=\"w-full field grid\">\r\n                    <label htmlFor=\"phone\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"datapool.label.phone\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\">\r\n                        <input class=\"w-full\"\r\n                               pInputText id=\"phoneNumber\"\r\n                               [(ngModel)]=\"accuracyWallet.phoneNumber\"\r\n                               formControlName=\"phoneNumber\"\r\n                               [placeholder]=\"tranService.translate('account.text.inputPhone')\"\r\n                        />\r\n                    </div>\r\n                </div>\r\n                <!-- error phone -->\r\n                <div class=\"w-full field grid text-error-field\">\r\n                    <label htmlFor=\"phone\" class=\"col-fixed\" style=\"width:180px\"></label>\r\n                    <div class=\"flex flex-column\">\r\n                        <div class=\"col\">\r\n                            <small class=\"text-red-500\" *ngIf=\"formAccuracyWallet.controls.phoneNumber.dirty && formAccuracyWallet.controls.phoneNumber.errors?.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                        </div>\r\n                        <div class=\"col\">\r\n                            <small class=\"text-red-500\" *ngIf=\"formAccuracyWallet.controls.phoneNumber.errors?.pattern\">{{tranService.translate(\"datapool.message.digitError\")}}</small>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n            </div>\r\n            <div class=\"flex flex-row justify-content-center align-items-center mt-3\">\r\n                <p-button styleClass=\"mr-2 p-button-secondary\" [label]=\"tranService.translate('global.button.cancel')\" (click)=\"isShowModalWalletAuthen = false\"></p-button>\r\n                <p-button type=\"button\" styleClass=\"p-button-info\" [disabled]=\"checkDisable()\" [label]=\"tranService.translate('global.button.save')\" (onClick)=\"sendOTP()\"></p-button>\r\n            </div>\r\n            <p-dialog [header]=\"tranService.translate('datapool.label.otpCode')\" [(visible)]=\"isSubmit\" [modal]=\"true\" [style]=\"{ width: '30vw' }\" [draggable]=\"false\" (onHide)=\"onHideOtp()\" [resizable]=\"false\">\r\n                <div class=\"flex flex-column gap-2 flex-1\">\r\n                    <p-inputOtp formControlName=\"otp\" class=\"mx-auto my-3\" [integerOnly]=\"true\" length=\"6\"></p-inputOtp>\r\n                    <button type=\"button\" class=\"border-none mb-4 cursor-pointer flex flex-row justify-content-center font-semibold\" style=\"background-color: transparent;\" [disabled]=\"countdown>0\" (click)=\"resetTimer()\">{{tranService.translate(\"datapool.message.resendOtp\")}}&nbsp;<div *ngIf=\"countdown>0\"> {{tranService.translate(\"datapool.message.in\")}} {{countdown}} {{tranService.translate(\"datapool.message.sec\")}} </div></button>\r\n                    <div class=\"m-auto\">\r\n                        <button class=\"m-auto mr-2\" [disabled]=\"formAccuracyWallet.controls.otp.invalid\" pButton>{{tranService.translate(\"global.button.save\")}}</button>\r\n                        <button type=\"button\" class=\"m-auto ml-2 p-button-outlined\" pButton (click)=\"isSubmit = false\">{{tranService.translate(\"global.button.cancel\")}}</button>\r\n                    </div>\r\n                </div>\r\n            </p-dialog>\r\n        </form>\r\n    </p-dialog>\r\n</div>\r\n\r\n<!-- popup detail wallet -->\r\n<div class=\"flex justify-content-center dialog-vnpt\">\r\n    <p-dialog [header]=\"tranService.translate('global.button.view')\" [(visible)]=\"isShowModalDetail\" [modal]=\"true\" [style]=\"{ width: '980px' }\" [draggable]=\"false\" [resizable]=\"false\" *ngIf=\"isShowModalDetail\">\r\n        <p-card styleClass=\"my-3\">\r\n            <div class=\"text-2xl font-bold pb-2\">{{subCodeId}}</div>\r\n            <div *ngIf=\"walletDetail\">\r\n                <div class=\"flex flex-row surface-200 p-4 border-round wallet-detail-div\">\r\n                    <div class=\"flex-1 wallet-detail\">\r\n                        <div class=\"font-medium text-base\">{{tranService.translate('datapool.label.payCode')}}</div>\r\n                        <div class=\"font-semibold text-lg\">{{walletDetail.payCode}}</div>\r\n                    </div>\r\n                    <div class=\"flex-1\">\r\n                        <div class=\"font-medium text-base\">{{tranService.translate('datapool.label.packageName')}}</div>\r\n                        <div class=\"font-semibold text-lg\">{{walletDetail.packageName}}</div>\r\n                    </div>\r\n                    <div class=\"flex-1\">\r\n                        <div class=\"font-medium text-base\">{{tranService.translate('datapool.label.phoneFull')}}</div>\r\n                        <div class=\"font-semibold text-lg\">{{walletDetail.phoneActive}}</div>\r\n                    </div>\r\n                    <div class=\"flex-1\">\r\n                        <div class=\"font-medium text-base\">{{tranService.translate(\"datapool.label.tax\")}}</div>\r\n                        <div class=\"font-semibold text-lg\">{{walletDetail.tax}}</div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex flex-row p-4 border-round wallet-detail-div\">\r\n                <div class=\"flex-1\">\r\n                    <div class=\"font-medium text-base\">{{tranService.translate(\"datapool.label.trafficType\")}}</div>\r\n                    <div class=\"font-semibold text-lg\">{{walletDetail.trafficType}}</div>\r\n                </div>\r\n                <div class=\"flex-1\">\r\n                    <div class=\"font-medium text-base\">{{tranService.translate(\"datapool.label.methodAutoShare\")}}</div>\r\n                    <div class=\"font-semibold text-lg\">{{getValueMethodAutoShare(walletDetail.autoType)}}</div>\r\n                </div>\r\n                <div class=\"flex-1\">\r\n                    <div class=\"font-medium text-base\">{{tranService.translate('datapool.label.remainData')}}/ {{tranService.translate(\"datapool.label.purchasedData\")}}</div>\r\n                    <div class=\"font-semibold text-lg\" *ngIf=\"walletDetail.trafficType == 'Gói Data'\" >{{formatNumber(walletDetail.totalRemainingTraffic)}}/ {{formatNumber(walletDetail.purchasedTraffic)}} MB</div>\r\n                    <div class=\"font-semibold text-lg\" *ngIf=\"walletDetail.trafficType == 'Gói thoại'\" >{{formatNumber(walletDetail.totalRemainingTraffic)}}/ {{formatNumber(walletDetail.purchasedTraffic)}} {{tranService.translate('alert.label.minutes')}}</div>\r\n                    <div class=\"font-semibold text-lg\" *ngIf=\"(( walletDetail.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase()))\" >{{formatNumber(walletDetail.totalRemainingTraffic)}}/ {{formatNumber(walletDetail.purchasedTraffic)}} SMS</div>\r\n                    <div class=\"font-semibold text-lg\" *ngIf=\"!(( walletDetail.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) && walletDetail.trafficType != 'Gói Data'&&walletDetail.trafficType != 'Gói thoại'\" >{{formatNumber(walletDetail.totalRemainingTraffic)}}/ {{formatNumber(walletDetail.purchasedTraffic)}}</div>\r\n                </div>\r\n                <div class=\"flex-1\">\r\n                    <div class=\"font-medium text-base\">{{tranService.translate(\"datapool.label.usedTime\")}}</div>\r\n                    <div class=\"font-semibold text-lg\">{{walletDetail.timeToUse}}</div>\r\n                </div>\r\n            </div>\r\n        </p-card>\r\n\r\n        <p-card>\r\n            <div class=\"text-lg font-bold\">{{tranService.translate(\"datapool.label.shareInfo\")}}</div>\r\n<!--            <p-table-->\r\n<!--                #dt2-->\r\n<!--                [value]=\"listDetail\"-->\r\n<!--                dataKey=\"id\"-->\r\n<!--                [rows]=\"10\"-->\r\n<!--                [rowsPerPageOptions]=\"[5, 10, 25, 50]\"-->\r\n<!--                [paginator]=\"true\"-->\r\n<!--                [tableStyle]=\"{ 'margin-top': '10px' }\"-->\r\n<!--            >-->\r\n<!--                <ng-template pTemplate=\"header\">-->\r\n<!--                    <tr>-->\r\n<!--                        <th>{{tranService.translate('datapool.label.phoneFull')}}</th>-->\r\n<!--                        <th>{{tranService.translate('datapool.label.fullName')}}</th>-->\r\n<!--                        <th>{{tranService.translate('datapool.label.email')}}</th>-->\r\n<!--                        <th>{{tranService.translate('datapool.label.sharedTime')}}</th>-->\r\n<!--                        <th>{{tranService.translate('datapool.label.usedDate')}}</th>-->\r\n<!--                        <th>{{tranService.translate('datapool.label.shared')}}</th>-->\r\n<!--                        <th >{{tranService.translate('datapool.label.percentage')}}</th>-->\r\n<!--                    </tr>-->\r\n<!--                </ng-template>-->\r\n<!--                <ng-template pTemplate=\"body\" let-listDetail>-->\r\n<!--                    <tr>-->\r\n<!--                        <td>-->\r\n<!--                            {{ listDetail.phoneReceipt }}-->\r\n<!--                        </td>-->\r\n<!--                        <td>-->\r\n<!--                            {{ listDetail.name }}-->\r\n<!--                        </td>-->\r\n<!--                        <td>-->\r\n<!--                            {{ listDetail.email }}-->\r\n<!--                        </td>-->\r\n<!--                        <td>-->\r\n<!--                            {{ getFormattedDate(listDetail.timeUpdate) }}-->\r\n<!--                        </td>-->\r\n<!--                        <td>-->\r\n<!--                            {{ getFormattedDate(listDetail.timeUpdate, listDetail?.dayExprired) }}-->\r\n<!--                        </td>-->\r\n<!--                        <td *ngIf=\"listDetail.trafficType == 'Gói Data'\">-->\r\n<!--                            {{ listDetail.trafficShare }} MB-->\r\n<!--                        </td>-->\r\n<!--                        <td *ngIf=\"listDetail.trafficType == 'Gói thoại'\">-->\r\n<!--                            {{ listDetail.trafficShare }} {{tranService.translate('alert.label.minutes')}}-->\r\n<!--                        </td>-->\r\n<!--                        <td *ngIf=\" ((listDetail.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase()))\">-->\r\n<!--                            {{ listDetail.trafficShare }} SMS-->\r\n<!--                        </td>-->\r\n<!--                        <td *ngIf=\"!((listDetail.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) && listDetail.trafficType != 'Gói Data' && listDetail.trafficType != 'Gói thoại'\">-->\r\n<!--                            {{ listDetail.trafficShare }}-->\r\n<!--                        </td>-->\r\n<!--                        <td>-->\r\n<!--                            {{ listDetail.percentTraffic }}%-->\r\n<!--                        </td>-->\r\n<!--                    </tr>-->\r\n<!--                </ng-template>-->\r\n<!--            </p-table>-->\r\n            <table-vnpt *ngIf=\"isShowTableInDialogDetail\"\r\n                [fieldId]=\"'id'\"\r\n                [columns]=\"columnsShareWallet\"\r\n                [dataSet]=\"dataSetShareWallet\"\r\n                [pageSize]=\"pageSizeShareWallet\"\r\n                [pageNumber]=\"pageNumberShareWallet\"\r\n                [options]=\"optionTableShareWallet\"\r\n                [sort]=\"sortShareWallet\"\r\n                [loadData]=\"searchShareWallet.bind(this)\"\r\n            ></table-vnpt>\r\n        </p-card>\r\n    </p-dialog>\r\n<!--    dialog confirm register by SubCode or PayCode-->\r\n    <p-dialog [header]=\"tranService.translate('datapool.label.notification')\"\r\n              [(visible)]=\"isShowRegisBySubOrPay\" [modal]=\"true\" [style]=\"{ width: '750px' }\"\r\n              [draggable]=\"false\" [resizable]=\"false\" *ngIf=\"isShowRegisBySubOrPay\"\r\n              (onHide)=\"onHideRegisBySubOrPay()\">\r\n        <div class=\"d-flex ai-center jc-center\" style=\"text-align: center;\">\r\n            <p>\r\n                <b style=\"font-size: large\">{{ tranService.translate('datapool.message.confirmRegisterAutoShareWallet') }}</b>\r\n                <br>\r\n                <span style=\"color: red;\" >\r\n        <i>{{ tranService.translate('datapool.text.noteAutoShare') }}</i>\r\n      </span>\r\n                <br>\r\n                <a (click)=\"openReadMoreRegister()\" class=\"cursor-pointer\"\r\n                   style=\"color: blue; text-decoration: underline;\"><i>{{ tranService.translate('datapool.message.readMore') }}</i></a>\r\n            </p>\r\n        </div>\r\n\r\n        <div class=\"flex flex-row justify-content-center align-items-center mt-2\">\r\n            <div class=\"grid col-4\">\r\n                <p-radioButton name=\"registerOption\" value=\"0\"\r\n                               [(ngModel)]=\"typeAutoShare\"\r\n                               inputId=\"1\"\r\n                               label=\"{{ tranService.translate('datapool.label.registerByPayCode') }}\"></p-radioButton>\r\n            </div>\r\n            <div class=\"col-1\"></div>\r\n            <div class=\"grid col-4\">\r\n                <p-radioButton name=\"registerOption\" value=\"1\"\r\n                               [(ngModel)]=\"typeAutoShare\"\r\n                               inputId=\"2\"\r\n                               label=\"{{ tranService.translate('datapool.label.registerBySubCode') }}\"></p-radioButton>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex flex-row justify-content-center align-content-start\">\r\n            <div class=\"grid col-4 pl-6\">\r\n                <span\r\n                    style=\"font-size: 0.8em; color: grey;\">{{ tranService.translate('datapool.text.nonOTPPayCode') }}</span>\r\n            </div>\r\n            <div class=\"col-1\"></div>\r\n            <div class=\"grid col-4 pl-6\">\r\n                <span\r\n                    style=\"font-size: 0.8em; color: grey;\"> {{ tranService.translate('datapool.text.nonOTPSubCode') }}</span>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex flex-row justify-content-center  mt-3\">\r\n            <p-button label=\"{{ tranService.translate('datapool.button.no') }}\" icon=\"pi pi-times\"\r\n                      styleClass=\"p-button-secondary p-button-outlined\"\r\n                      (click)=\"onHideRegisBySubOrPay()\"></p-button>\r\n            <p-button label=\"{{ tranService.translate('datapool.button.register') }}\" icon=\"pi pi-check\"\r\n                      type=\"button\"\r\n                      styleClass=\"p-button-info ml-2\"\r\n                      (click)=\"register()\"></p-button>\r\n        </div>\r\n    </p-dialog>\r\n\r\n    <!--    dialog confirm register by PayCode-->\r\n    <p-dialog [header]=\"tranService.translate('datapool.label.notification')\"\r\n              [(visible)]=\"isShowRegisByPayCode\" [modal]=\"true\" [style]=\"{ width: '750px' }\"\r\n              [draggable]=\"false\" [resizable]=\"false\" *ngIf=\"isShowRegisByPayCode\"\r\n              (onHide)=\"onHideRegisByPayCode()\">\r\n        <div class=\"d-flex ai-center jc-center\" style=\"text-align: center;\">\r\n            <p>\r\n                <b style=\"font-size: large\">{{ tranService.translate('datapool.message.confirmRegisterAutoSharePayCode') }}</b>\r\n                <br>\r\n                <span style=\"color: red;\" >\r\n        <i>{{ tranService.translate('datapool.text.noteRegisPayCode') }}</i>\r\n      </span>\r\n                <br>\r\n                <a (click)=\"openReadMoreRegister()\" class=\"cursor-pointer\"\r\n                   style=\"color: blue; text-decoration: underline;\"><i>{{ tranService.translate('datapool.message.readMore') }}</i></a>\r\n            </p>\r\n        </div>\r\n        <div class=\"flex flex-row justify-content-center  mt-3\">\r\n            <p-button label=\"{{ tranService.translate('datapool.button.no') }}\" icon=\"pi pi-times\"\r\n                      styleClass=\"p-button-secondary p-button-outlined\"\r\n                      (click)=\"onHideRegisByPayCode()\"></p-button>\r\n            <p-button label=\"{{ tranService.translate('datapool.button.register') }}\" icon=\"pi pi-check\"\r\n                      styleClass=\"p-button-info ml-2\"\r\n                      (click)=\"register()\"></p-button>\r\n        </div>\r\n    </p-dialog>\r\n\r\n<!--    dialog confirm otp regis-->\r\n    <p-dialog [header]=\"tranService.translate('datapool.label.otpCode')\" [(visible)]=\"isShowDiaglogOTPRegis\"\r\n              [modal]=\"true\" [style]=\"{ width: '30vw' }\" [draggable]=\"false\" (onHide)=\"hideDiaglogConfirmOTP()\"\r\n              [resizable]=\"false\">\r\n        <form [formGroup]=\"formWithoutOTP\" (ngSubmit)=\"onRegister()\">\r\n            <div class=\"flex flex-column gap-2 flex-1\">\r\n                <p-inputOtp formControlName=\"otp\" class=\"mx-auto my-3\" [integerOnly]=\"true\" length=\"6\"></p-inputOtp>\r\n                <button type=\"button\"\r\n                        class=\"border-none mb-4 cursor-pointer flex flex-row justify-content-center font-semibold\"\r\n                        style=\"background-color: transparent;\" [disabled]=\"countdown>0\"\r\n                        (click)=\"resetTimerForRegister()\">{{ tranService.translate(\"datapool.message.resendOtp\") }}&nbsp;<div\r\n                    *ngIf=\"countdown>0\"> {{ tranService.translate(\"datapool.message.in\") }} {{ countdown }} {{ tranService.translate(\"datapool.message.sec\") }}\r\n                </div>\r\n                </button>\r\n                <div class=\"m-auto\">\r\n                    <button class=\"m-auto mr-2\" [disabled]=\"formWithoutOTP.controls.otp.invalid\" type=\"submit\"\r\n                            pButton>{{ tranService.translate(\"global.button.save\") }}\r\n                    </button>\r\n                    <button type=\"button\" class=\"m-auto ml-2 p-button-outlined\" pButton\r\n                            (click)=\"isShowDiaglogOTPRegis = false\">{{ tranService.translate(\"global.button.cancel\") }}\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </form>\r\n    </p-dialog>\r\n\r\n<!--dialog cancel subCode-->\r\n    <p-dialog [header]=\"tranService.translate('datapool.label.notification')\"\r\n              [(visible)]=\"isShowCancelBySubCode\" [modal]=\"true\" [style]=\"{ width: '750px' }\"\r\n              [draggable]=\"false\" [resizable]=\"false\" *ngIf=\"isShowCancelBySubCode\"\r\n              (onHide)=\"isShowCancelBySubCode = false\">\r\n        <div class=\"d-flex ai-center jc-center\" style=\"text-align: center;\">\r\n            <p>\r\n                <b style=\"font-size: large\">{{ tranService.translate('datapool.message.confirmCancelSubCode') }}</b>\r\n                <br>\r\n                    <span style=\"color: red; word-wrap:break-word;\">\r\n                        <i>{{ tranService.translate('datapool.text.noteCancelSubCode') }}</i>\r\n                        </span>\r\n                <br>\r\n                <a (click)=\"openReadMoreUnRegister()\" class=\"cursor-pointer\"\r\n                   style=\"color: blue; text-decoration: underline;\"><i> {{tranService.translate('datapool.message.readMore') }}</i></a>\r\n            </p>\r\n        </div>\r\n        <div class=\"flex flex-row justify-content-center align-items-center mt-3\">\r\n            <p-button label=\"{{ tranService.translate('datapool.button.no') }}\" icon=\"pi pi-times\"\r\n                      styleClass=\"p-button-secondary p-button-outlined\"\r\n                      (click)=\"isShowCancelBySubCode = false\"></p-button>\r\n            <p-button label=\"{{ tranService.translate('datapool.button.yes') }}\" icon=\"pi pi-check\"\r\n                      styleClass=\"p-button-info ml-2\"\r\n                      (click)=\"onCancel()\"></p-button>\r\n        </div>\r\n    </p-dialog>\r\n    <!--dialog cancel payCode-->\r\n    <p-dialog [header]=\"tranService.translate('datapool.label.notification')\"\r\n              [(visible)]=\"isShowCancelByPayCode\" [modal]=\"true\" [style]=\"{ width: '750px' }\"\r\n              [draggable]=\"false\" [resizable]=\"false\" *ngIf=\"isShowCancelByPayCode\"\r\n              (onHide)=\"isShowCancelByPayCode = false\">\r\n        <div class=\"d-flex ai-center jc-center\" style=\"text-align: center;\">\r\n            <span>\r\n                    {{ tranService.translate('datapool.text.hasPayCode', {payCode: payCode}) }}\r\n                </span>\r\n            <p>\r\n                <b style=\"font-size: large\">{{ tranService.translate('datapool.message.confirmCancelPayCode', {payCode: payCode}) }}</b>\r\n                <br>\r\n                <span style=\"color: red; word-wrap:break-word;\">\r\n        <i>{{ tranService.translate('datapool.text.noteCancelPayCode') }}</i>\r\n      </span>\r\n                <br>\r\n                <a (click)=\"openReadMoreUnRegister()\" class=\"cursor-pointer\"\r\n                   style=\"color: blue; text-decoration: underline;\"> <i>{{ tranService.translate('datapool.message.readMore') }}</i></a>\r\n            </p>\r\n        </div>\r\n        <div class=\"flex flex-row justify-content-center align-items-center mt-3\">\r\n            <p-button label=\"{{ tranService.translate('datapool.button.no') }}\" icon=\"pi pi-times\"\r\n                      styleClass=\"p-button-secondary p-button-outlined\"\r\n                      (click)=\"isShowCancelByPayCode = false\"></p-button>\r\n            <p-button label=\"{{ tranService.translate('datapool.button.yes') }}\" icon=\"pi pi-check\"\r\n                      styleClass=\"p-button-info ml-2\"\r\n                      (click)=\"onCancel()\"></p-button>\r\n        </div>\r\n    </p-dialog>\r\n</div>\r\n"], "mappings": ";AACA,SAAQA,aAAa,QAAO,4BAA4B;AACxD,SAAqBC,UAAU,QAAO,gBAAgB;AACtD,SAAkBC,UAAU,QAAsB,aAAa;AAE/D,SAAQC,oBAAoB,QAAO,mDAAmD;AACtF,SACIC,eAAe,QAKZ,wFAAwF;AAC/F,SAASC,SAAS,QAAQ,iCAAiC;AAC3D,SAAQC,sBAAsB,QAAO,qDAAqD;AAC1F,SAASC,cAAc,QAAQ,iDAAiD;;;;;;;;;;;;;;;;;;;ICT5EC,EAAA,CAAAC,cAAA,cAAqI;IAK7HD,EAAA,CAAAE,UAAA,mBAAAC,+DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,OAAA,CAAAG,iBAAA,EAAmB;IAAA,EAAC;IACjCT,EAAA,CAAAU,YAAA,EAAW;;;;IAFPV,EAAA,CAAAW,SAAA,GAA+D;IAA/DX,EAAA,CAAAY,UAAA,UAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,iCAA+D;;;;;IAqD3Df,EAAA,CAAAC,cAAA,cAA6E;IACdD,EAAA,CAAAgB,MAAA,GAAmD;IAAAhB,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAgB,MAAA,QAAC;IAAAhB,EAAA,CAAAU,YAAA,EAAO;IACjJV,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAiB,SAAA,gBAIE;IACNjB,EAAA,CAAAU,YAAA,EAAM;;;;IAPqDV,EAAA,CAAAW,SAAA,GAAmD;IAAnDX,EAAA,CAAAkB,iBAAA,CAAAC,MAAA,CAAAL,WAAA,CAAAC,SAAA,2BAAmD;IAKnGf,EAAA,CAAAW,SAAA,GAA8D;IAA9DX,EAAA,CAAAY,UAAA,gBAAAO,MAAA,CAAAL,WAAA,CAAAC,SAAA,0BAA8D;;;;;IAOrEf,EAAA,CAAAC,cAAA,cAAqF;IAAAD,EAAA,CAAAgB,MAAA,GAAoD;IAAAhB,EAAA,CAAAU,YAAA,EAAM;;;;IAA1DV,EAAA,CAAAW,SAAA,GAAoD;IAApDX,EAAA,CAAAkB,iBAAA,CAAAE,OAAA,CAAAN,WAAA,CAAAC,SAAA,4BAAoD;;;;;IACzIf,EAAA,CAAAC,cAAA,cAAoF;IAAAD,EAAA,CAAAgB,MAAA,GAA2D;IAAAhB,EAAA,CAAAU,YAAA,EAAM;;;;IAAjEV,EAAA,CAAAW,SAAA,GAA2D;IAA3DX,EAAA,CAAAkB,iBAAA,CAAAG,OAAA,CAAAP,WAAA,CAAAC,SAAA,mCAA2D;;;;;;;;;;IAC/If,EAAA,CAAAC,cAAA,cAAsF;IAAAD,EAAA,CAAAgB,MAAA,GAA8D;IAAAhB,EAAA,CAAAU,YAAA,EAAM;;;;IAApEV,EAAA,CAAAW,SAAA,GAA8D;IAA9DX,EAAA,CAAAkB,iBAAA,CAAAI,OAAA,CAAAR,WAAA,CAAAC,SAAA,6BAAAf,EAAA,CAAAuB,eAAA,IAAAC,GAAA,GAA8D;;;;;IAHxJxB,EAAA,CAAAC,cAAA,UAAkG;IAC9FD,EAAA,CAAAyB,UAAA,IAAAC,iDAAA,kBAA+I;IAC/I1B,EAAA,CAAAyB,UAAA,IAAAE,iDAAA,kBAAqJ;IACrJ3B,EAAA,CAAAyB,UAAA,IAAAG,iDAAA,kBAA0J;IAC9J5B,EAAA,CAAAU,YAAA,EAAM;;;;IAHIV,EAAA,CAAAW,SAAA,GAAuD;IAAvDX,EAAA,CAAAY,UAAA,SAAAiB,OAAA,CAAAC,kBAAA,CAAAC,GAAA,YAAAC,MAAA,CAAAC,QAAA,CAAuD;IACvDjC,EAAA,CAAAW,SAAA,GAAsD;IAAtDX,EAAA,CAAAY,UAAA,SAAAiB,OAAA,CAAAC,kBAAA,CAAAC,GAAA,YAAAC,MAAA,CAAAE,OAAA,CAAsD;IACtDlC,EAAA,CAAAW,SAAA,GAAwD;IAAxDX,EAAA,CAAAY,UAAA,SAAAiB,OAAA,CAAAC,kBAAA,CAAAC,GAAA,YAAAC,MAAA,CAAAG,SAAA,CAAwD;;;;;IALtEnC,EAAA,CAAAC,cAAA,cAAqF;IACjFD,EAAA,CAAAiB,SAAA,gBAAmE;IACnEjB,EAAA,CAAAyB,UAAA,IAAAW,2CAAA,kBAIM;IACVpC,EAAA,CAAAU,YAAA,EAAM;;;;IALIV,EAAA,CAAAW,SAAA,GAA0F;IAA1FX,EAAA,CAAAY,UAAA,SAAAyB,MAAA,CAAAP,kBAAA,CAAAC,GAAA,YAAAO,OAAA,IAAAD,MAAA,CAAAP,kBAAA,CAAAC,GAAA,YAAAQ,KAAA,CAA0F;;;;;IAOpGvC,EAAA,CAAAC,cAAA,cAA6E;IACdD,EAAA,CAAAgB,MAAA,GAAmD;IAAAhB,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAgB,MAAA,QAAC;IAAAhB,EAAA,CAAAU,YAAA,EAAO;IACjJV,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAiB,SAAA,gBAIE;IACNjB,EAAA,CAAAU,YAAA,EAAM;;;;IAPqDV,EAAA,CAAAW,SAAA,GAAmD;IAAnDX,EAAA,CAAAkB,iBAAA,CAAAsB,MAAA,CAAA1B,WAAA,CAAAC,SAAA,2BAAmD;IAKnGf,EAAA,CAAAW,SAAA,GAA8D;IAA9DX,EAAA,CAAAY,UAAA,gBAAA4B,MAAA,CAAA1B,WAAA,CAAAC,SAAA,0BAA8D;;;;;IAOrEf,EAAA,CAAAC,cAAA,cAAqF;IAAAD,EAAA,CAAAgB,MAAA,GAAoD;IAAAhB,EAAA,CAAAU,YAAA,EAAM;;;;IAA1DV,EAAA,CAAAW,SAAA,GAAoD;IAApDX,EAAA,CAAAkB,iBAAA,CAAAuB,OAAA,CAAA3B,WAAA,CAAAC,SAAA,4BAAoD;;;;;IACzIf,EAAA,CAAAC,cAAA,cAAoF;IAAAD,EAAA,CAAAgB,MAAA,GAA2D;IAAAhB,EAAA,CAAAU,YAAA,EAAM;;;;IAAjEV,EAAA,CAAAW,SAAA,GAA2D;IAA3DX,EAAA,CAAAkB,iBAAA,CAAAwB,OAAA,CAAA5B,WAAA,CAAAC,SAAA,mCAA2D;;;;;IAC/If,EAAA,CAAAC,cAAA,cAAsF;IAAAD,EAAA,CAAAgB,MAAA,GAA8D;IAAAhB,EAAA,CAAAU,YAAA,EAAM;;;;IAApEV,EAAA,CAAAW,SAAA,GAA8D;IAA9DX,EAAA,CAAAkB,iBAAA,CAAAyB,OAAA,CAAA7B,WAAA,CAAAC,SAAA,6BAAAf,EAAA,CAAAuB,eAAA,IAAAC,GAAA,GAA8D;;;;;IAHxJxB,EAAA,CAAAC,cAAA,UAAkG;IAC9FD,EAAA,CAAAyB,UAAA,IAAAmB,iDAAA,kBAA+I;IAC/I5C,EAAA,CAAAyB,UAAA,IAAAoB,iDAAA,kBAAqJ;IACrJ7C,EAAA,CAAAyB,UAAA,IAAAqB,iDAAA,kBAA0J;IAC9J9C,EAAA,CAAAU,YAAA,EAAM;;;;IAHIV,EAAA,CAAAW,SAAA,GAAuD;IAAvDX,EAAA,CAAAY,UAAA,SAAAmC,OAAA,CAAAjB,kBAAA,CAAAC,GAAA,YAAAC,MAAA,CAAAC,QAAA,CAAuD;IACvDjC,EAAA,CAAAW,SAAA,GAAsD;IAAtDX,EAAA,CAAAY,UAAA,SAAAmC,OAAA,CAAAjB,kBAAA,CAAAC,GAAA,YAAAC,MAAA,CAAAE,OAAA,CAAsD;IACtDlC,EAAA,CAAAW,SAAA,GAAwD;IAAxDX,EAAA,CAAAY,UAAA,SAAAmC,OAAA,CAAAjB,kBAAA,CAAAC,GAAA,YAAAC,MAAA,CAAAG,SAAA,CAAwD;;;;;IALtEnC,EAAA,CAAAC,cAAA,cAAqF;IACjFD,EAAA,CAAAiB,SAAA,gBAAmE;IACnEjB,EAAA,CAAAyB,UAAA,IAAAuB,2CAAA,kBAIM;IACVhD,EAAA,CAAAU,YAAA,EAAM;;;;IALIV,EAAA,CAAAW,SAAA,GAA0F;IAA1FX,EAAA,CAAAY,UAAA,SAAAqC,MAAA,CAAAnB,kBAAA,CAAAC,GAAA,YAAAO,OAAA,IAAAW,MAAA,CAAAnB,kBAAA,CAAAC,GAAA,YAAAQ,KAAA,CAA0F;;;;;IAsB5FvC,EAAA,CAAAC,cAAA,gBAA8H;IAAAD,EAAA,CAAAgB,MAAA,GAAoD;IAAAhB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA5DV,EAAA,CAAAW,SAAA,GAAoD;IAApDX,EAAA,CAAAkB,iBAAA,CAAAgC,MAAA,CAAApC,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAClLf,EAAA,CAAAC,cAAA,gBAAsF;IAAAD,EAAA,CAAAgB,MAAA,GAA8D;IAAAhB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAtEV,EAAA,CAAAW,SAAA,GAA8D;IAA9DX,EAAA,CAAAkB,iBAAA,CAAAiC,MAAA,CAAArC,WAAA,CAAAC,SAAA,6BAAAf,EAAA,CAAAuB,eAAA,IAAAC,GAAA,GAA8D;;;;;IACpJxB,EAAA,CAAAC,cAAA,gBAAoF;IAAAD,EAAA,CAAAgB,MAAA,GAA0D;IAAAhB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAlEV,EAAA,CAAAW,SAAA,GAA0D;IAA1DX,EAAA,CAAAkB,iBAAA,CAAAkC,MAAA,CAAAtC,WAAA,CAAAC,SAAA,kCAA0D;;;;;IAoB1If,EAAA,CAAAC,cAAA,gBAA8I;IAAAD,EAAA,CAAAgB,MAAA,GAAoD;IAAAhB,EAAA,CAAAU,YAAA,EAAQ;;;;IAA5DV,EAAA,CAAAW,SAAA,GAAoD;IAApDX,EAAA,CAAAkB,iBAAA,CAAAmC,MAAA,CAAAvC,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAGlMf,EAAA,CAAAC,cAAA,gBAA4F;IAAAD,EAAA,CAAAgB,MAAA,GAAwD;IAAAhB,EAAA,CAAAU,YAAA,EAAQ;;;;IAAhEV,EAAA,CAAAW,SAAA,GAAwD;IAAxDX,EAAA,CAAAkB,iBAAA,CAAAoC,MAAA,CAAAxC,WAAA,CAAAC,SAAA,gCAAwD;;;;;IAayGf,EAAA,CAAAC,cAAA,UAAyB;IAACD,EAAA,CAAAgB,MAAA,GAAiH;IAAAhB,EAAA,CAAAU,YAAA,EAAM;;;;IAAvHV,EAAA,CAAAW,SAAA,GAAiH;IAAjHX,EAAA,CAAAuD,kBAAA,MAAAC,OAAA,CAAA1C,WAAA,CAAAC,SAAA,8BAAAyC,OAAA,CAAAC,SAAA,OAAAD,OAAA,CAAA1C,WAAA,CAAAC,SAAA,8BAAiH;;;;;IAgBxZf,EAAA,CAAAC,cAAA,UAA0B;IAGqBD,EAAA,CAAAgB,MAAA,GAAmD;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IAC5FV,EAAA,CAAAC,cAAA,cAAmC;IAAAD,EAAA,CAAAgB,MAAA,GAAwB;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IAErEV,EAAA,CAAAC,cAAA,cAAoB;IACmBD,EAAA,CAAAgB,MAAA,GAAuD;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IAChGV,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAgB,MAAA,IAA4B;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IAEzEV,EAAA,CAAAC,cAAA,eAAoB;IACmBD,EAAA,CAAAgB,MAAA,IAAqD;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IAC9FV,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAgB,MAAA,IAA4B;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IAEzEV,EAAA,CAAAC,cAAA,eAAoB;IACmBD,EAAA,CAAAgB,MAAA,IAA+C;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IACxFV,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAgB,MAAA,IAAoB;IAAAhB,EAAA,CAAAU,YAAA,EAAM;;;;IAb1BV,EAAA,CAAAW,SAAA,GAAmD;IAAnDX,EAAA,CAAAkB,iBAAA,CAAAwC,OAAA,CAAA5C,WAAA,CAAAC,SAAA,2BAAmD;IACnDf,EAAA,CAAAW,SAAA,GAAwB;IAAxBX,EAAA,CAAAkB,iBAAA,CAAAwC,OAAA,CAAAC,YAAA,CAAAC,OAAA,CAAwB;IAGxB5D,EAAA,CAAAW,SAAA,GAAuD;IAAvDX,EAAA,CAAAkB,iBAAA,CAAAwC,OAAA,CAAA5C,WAAA,CAAAC,SAAA,+BAAuD;IACvDf,EAAA,CAAAW,SAAA,GAA4B;IAA5BX,EAAA,CAAAkB,iBAAA,CAAAwC,OAAA,CAAAC,YAAA,CAAAE,WAAA,CAA4B;IAG5B7D,EAAA,CAAAW,SAAA,GAAqD;IAArDX,EAAA,CAAAkB,iBAAA,CAAAwC,OAAA,CAAA5C,WAAA,CAAAC,SAAA,6BAAqD;IACrDf,EAAA,CAAAW,SAAA,GAA4B;IAA5BX,EAAA,CAAAkB,iBAAA,CAAAwC,OAAA,CAAAC,YAAA,CAAAG,WAAA,CAA4B;IAG5B9D,EAAA,CAAAW,SAAA,GAA+C;IAA/CX,EAAA,CAAAkB,iBAAA,CAAAwC,OAAA,CAAA5C,WAAA,CAAAC,SAAA,uBAA+C;IAC/Cf,EAAA,CAAAW,SAAA,GAAoB;IAApBX,EAAA,CAAAkB,iBAAA,CAAAwC,OAAA,CAAAC,YAAA,CAAAI,GAAA,CAAoB;;;;;IAe3D/D,EAAA,CAAAC,cAAA,cAAmF;IAAAD,EAAA,CAAAgB,MAAA,GAAwG;IAAAhB,EAAA,CAAAU,YAAA,EAAM;;;;IAA9GV,EAAA,CAAAW,SAAA,GAAwG;IAAxGX,EAAA,CAAAgE,kBAAA,KAAAC,OAAA,CAAAC,YAAA,CAAAD,OAAA,CAAAN,YAAA,CAAAQ,qBAAA,SAAAF,OAAA,CAAAC,YAAA,CAAAD,OAAA,CAAAN,YAAA,CAAAS,gBAAA,SAAwG;;;;;IAC3LpE,EAAA,CAAAC,cAAA,cAAoF;IAAAD,EAAA,CAAAgB,MAAA,GAAsJ;IAAAhB,EAAA,CAAAU,YAAA,EAAM;;;;IAA5JV,EAAA,CAAAW,SAAA,GAAsJ;IAAtJX,EAAA,CAAAuD,kBAAA,KAAAc,OAAA,CAAAH,YAAA,CAAAG,OAAA,CAAAV,YAAA,CAAAQ,qBAAA,SAAAE,OAAA,CAAAH,YAAA,CAAAG,OAAA,CAAAV,YAAA,CAAAS,gBAAA,QAAAC,OAAA,CAAAvD,WAAA,CAAAC,SAAA,4BAAsJ;;;;;IAC1Of,EAAA,CAAAC,cAAA,cAAgI;IAAAD,EAAA,CAAAgB,MAAA,GAAyG;IAAAhB,EAAA,CAAAU,YAAA,EAAM;;;;IAA/GV,EAAA,CAAAW,SAAA,GAAyG;IAAzGX,EAAA,CAAAgE,kBAAA,KAAAM,OAAA,CAAAJ,YAAA,CAAAI,OAAA,CAAAX,YAAA,CAAAQ,qBAAA,SAAAG,OAAA,CAAAJ,YAAA,CAAAI,OAAA,CAAAX,YAAA,CAAAS,gBAAA,UAAyG;;;;;IACzOpE,EAAA,CAAAC,cAAA,cAAoN;IAAAD,EAAA,CAAAgB,MAAA,GAAqG;IAAAhB,EAAA,CAAAU,YAAA,EAAM;;;;IAA3GV,EAAA,CAAAW,SAAA,GAAqG;IAArGX,EAAA,CAAAgE,kBAAA,KAAAO,OAAA,CAAAL,YAAA,CAAAK,OAAA,CAAAZ,YAAA,CAAAQ,qBAAA,SAAAI,OAAA,CAAAL,YAAA,CAAAK,OAAA,CAAAZ,YAAA,CAAAS,gBAAA,MAAqG;;;;;IAkEjUpE,EAAA,CAAAiB,SAAA,qBASc;;;;IARVjB,EAAA,CAAAY,UAAA,iBAAgB,YAAA4D,OAAA,CAAAC,kBAAA,aAAAD,OAAA,CAAAE,kBAAA,cAAAF,OAAA,CAAAG,mBAAA,gBAAAH,OAAA,CAAAI,qBAAA,aAAAJ,OAAA,CAAAK,sBAAA,UAAAL,OAAA,CAAAM,eAAA,cAAAN,OAAA,CAAAO,iBAAA,CAAAC,IAAA,CAAAR,OAAA;;;;;;;;;;;IAxG5BxE,EAAA,CAAAC,cAAA,kBAA+M;IAA9ID,EAAA,CAAAE,UAAA,2BAAA+E,6EAAAC,MAAA;MAAAlF,EAAA,CAAAI,aAAA,CAAA+E,IAAA;MAAA,MAAAC,OAAA,GAAApF,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAA4E,OAAA,CAAAC,iBAAA,GAAAH,MAAA;IAAA,EAA+B;IAC5FlF,EAAA,CAAAC,cAAA,iBAA0B;IACeD,EAAA,CAAAgB,MAAA,GAAa;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IACxDV,EAAA,CAAAyB,UAAA,IAAA6D,gDAAA,mBAmBM;IACNtF,EAAA,CAAAC,cAAA,cAA8D;IAEnBD,EAAA,CAAAgB,MAAA,GAAuD;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IAChGV,EAAA,CAAAC,cAAA,cAAmC;IAAAD,EAAA,CAAAgB,MAAA,IAA4B;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IAEzEV,EAAA,CAAAC,cAAA,eAAoB;IACmBD,EAAA,CAAAgB,MAAA,IAA2D;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IACpGV,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAgB,MAAA,IAAkD;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IAE/FV,EAAA,CAAAC,cAAA,eAAoB;IACmBD,EAAA,CAAAgB,MAAA,IAAiH;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IAC1JV,EAAA,CAAAyB,UAAA,KAAA8D,iDAAA,kBAAiM;IACjMvF,EAAA,CAAAyB,UAAA,KAAA+D,iDAAA,kBAAgP;IAChPxF,EAAA,CAAAyB,UAAA,KAAAgE,iDAAA,kBAA+O;IAC/OzF,EAAA,CAAAyB,UAAA,KAAAiE,iDAAA,kBAA+T;IACnU1F,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,eAAoB;IACmBD,EAAA,CAAAgB,MAAA,IAAoD;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IAC7FV,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAgB,MAAA,IAA0B;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IAK/EV,EAAA,CAAAC,cAAA,cAAQ;IAC2BD,EAAA,CAAAgB,MAAA,IAAqD;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IAwD1FV,EAAA,CAAAyB,UAAA,KAAAkE,wDAAA,yBASc;IAClB3F,EAAA,CAAAU,YAAA,EAAS;;;;IAjHmGV,EAAA,CAAA4F,UAAA,CAAA5F,EAAA,CAAAuB,eAAA,KAAAsE,GAAA,EAA4B;IAAlI7F,EAAA,CAAAY,UAAA,WAAAkF,OAAA,CAAAhF,WAAA,CAAAC,SAAA,uBAAsD,YAAA+E,OAAA,CAAAT,iBAAA;IAEnBrF,EAAA,CAAAW,SAAA,GAAa;IAAbX,EAAA,CAAAkB,iBAAA,CAAA4E,OAAA,CAAAC,SAAA,CAAa;IAC5C/F,EAAA,CAAAW,SAAA,GAAkB;IAAlBX,EAAA,CAAAY,UAAA,SAAAkF,OAAA,CAAAnC,YAAA,CAAkB;IAsBmB3D,EAAA,CAAAW,SAAA,GAAuD;IAAvDX,EAAA,CAAAkB,iBAAA,CAAA4E,OAAA,CAAAhF,WAAA,CAAAC,SAAA,+BAAuD;IACvDf,EAAA,CAAAW,SAAA,GAA4B;IAA5BX,EAAA,CAAAkB,iBAAA,CAAA4E,OAAA,CAAAnC,YAAA,CAAAqC,WAAA,CAA4B;IAG5BhG,EAAA,CAAAW,SAAA,GAA2D;IAA3DX,EAAA,CAAAkB,iBAAA,CAAA4E,OAAA,CAAAhF,WAAA,CAAAC,SAAA,mCAA2D;IAC3Df,EAAA,CAAAW,SAAA,GAAkD;IAAlDX,EAAA,CAAAkB,iBAAA,CAAA4E,OAAA,CAAAG,uBAAA,CAAAH,OAAA,CAAAnC,YAAA,CAAAuC,QAAA,EAAkD;IAGlDlG,EAAA,CAAAW,SAAA,GAAiH;IAAjHX,EAAA,CAAAgE,kBAAA,KAAA8B,OAAA,CAAAhF,WAAA,CAAAC,SAAA,qCAAA+E,OAAA,CAAAhF,WAAA,CAAAC,SAAA,qCAAiH;IAChHf,EAAA,CAAAW,SAAA,GAA4C;IAA5CX,EAAA,CAAAY,UAAA,SAAAkF,OAAA,CAAAnC,YAAA,CAAAqC,WAAA,oBAA4C;IAC5ChG,EAAA,CAAAW,SAAA,GAA6C;IAA7CX,EAAA,CAAAY,UAAA,SAAAkF,OAAA,CAAAnC,YAAA,CAAAqC,WAAA,0BAA6C;IAC7ChG,EAAA,CAAAW,SAAA,GAAyF;IAAzFX,EAAA,CAAAY,UAAA,UAAAkF,OAAA,CAAAnC,YAAA,CAAAqC,WAAA,QAAAG,WAAA,GAAAC,QAAA,gBAAAD,WAAA,IAAyF;IACzFnG,EAAA,CAAAW,SAAA,GAA6K;IAA7KX,EAAA,CAAAY,UAAA,WAAAkF,OAAA,CAAAnC,YAAA,CAAAqC,WAAA,QAAAG,WAAA,GAAAC,QAAA,gBAAAD,WAAA,OAAAL,OAAA,CAAAnC,YAAA,CAAAqC,WAAA,uBAAAF,OAAA,CAAAnC,YAAA,CAAAqC,WAAA,0BAA6K;IAG9KhG,EAAA,CAAAW,SAAA,GAAoD;IAApDX,EAAA,CAAAkB,iBAAA,CAAA4E,OAAA,CAAAhF,WAAA,CAAAC,SAAA,4BAAoD;IACpDf,EAAA,CAAAW,SAAA,GAA0B;IAA1BX,EAAA,CAAAkB,iBAAA,CAAA4E,OAAA,CAAAnC,YAAA,CAAA0C,SAAA,CAA0B;IAMtCrG,EAAA,CAAAW,SAAA,GAAqD;IAArDX,EAAA,CAAAkB,iBAAA,CAAA4E,OAAA,CAAAhF,WAAA,CAAAC,SAAA,6BAAqD;IAwDvEf,EAAA,CAAAW,SAAA,GAA+B;IAA/BX,EAAA,CAAAY,UAAA,SAAAkF,OAAA,CAAAQ,yBAAA,CAA+B;;;;;;;;;;;IAapDtG,EAAA,CAAAC,cAAA,mBAG6C;IAFnCD,EAAA,CAAAE,UAAA,2BAAAqG,6EAAArB,MAAA;MAAAlF,EAAA,CAAAI,aAAA,CAAAoG,IAAA;MAAA,MAAAC,OAAA,GAAAzG,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAiG,OAAA,CAAAC,qBAAA,GAAAxB,MAAA;IAAA,EAAmC,oBAAAyB,sEAAA;MAAA3G,EAAA,CAAAI,aAAA,CAAAoG,IAAA;MAAA,MAAAI,OAAA,GAAA5G,EAAA,CAAAO,aAAA;MAAA,OAEzBP,EAAA,CAAAQ,WAAA,CAAAoG,OAAA,CAAAC,qBAAA,EAAuB;IAAA,EAFE;IAGzC7G,EAAA,CAAAC,cAAA,cAAoE;IAEhCD,EAAA,CAAAgB,MAAA,GAA8E;IAAAhB,EAAA,CAAAU,YAAA,EAAI;IAC9GV,EAAA,CAAAiB,SAAA,SAAI;IACJjB,EAAA,CAAAC,cAAA,eAA2B;IAChCD,EAAA,CAAAgB,MAAA,GAA0D;IAAAhB,EAAA,CAAAU,YAAA,EAAI;IAEzDV,EAAA,CAAAiB,SAAA,SAAI;IACJjB,EAAA,CAAAC,cAAA,aACoD;IADjDD,EAAA,CAAAE,UAAA,mBAAA4G,+DAAA;MAAA9G,EAAA,CAAAI,aAAA,CAAAoG,IAAA;MAAA,MAAAO,OAAA,GAAA/G,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAuG,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAAC;IACiBhH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAgB,MAAA,IAAwD;IAAAhB,EAAA,CAAAU,YAAA,EAAI;IAI3HV,EAAA,CAAAC,cAAA,eAA0E;IAGnDD,EAAA,CAAAE,UAAA,2BAAA+G,mFAAA/B,MAAA;MAAAlF,EAAA,CAAAI,aAAA,CAAAoG,IAAA;MAAA,MAAAU,OAAA,GAAAlH,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAA0G,OAAA,CAAAC,aAAA,GAAAjC,MAAA;IAAA,EAA2B;IAE6ClF,EAAA,CAAAU,YAAA,EAAgB;IAE3GV,EAAA,CAAAiB,SAAA,eAAyB;IACzBjB,EAAA,CAAAC,cAAA,eAAwB;IAELD,EAAA,CAAAE,UAAA,2BAAAkH,mFAAAlC,MAAA;MAAAlF,EAAA,CAAAI,aAAA,CAAAoG,IAAA;MAAA,MAAAa,OAAA,GAAArH,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAA6G,OAAA,CAAAF,aAAA,GAAAjC,MAAA;IAAA,EAA2B;IAE6ClF,EAAA,CAAAU,YAAA,EAAgB;IAG/GV,EAAA,CAAAC,cAAA,eAAsE;IAGnBD,EAAA,CAAAgB,MAAA,IAA0D;IAAAhB,EAAA,CAAAU,YAAA,EAAO;IAEhHV,EAAA,CAAAiB,SAAA,eAAyB;IACzBjB,EAAA,CAAAC,cAAA,eAA6B;IAEmBD,EAAA,CAAAgB,MAAA,IAA0D;IAAAhB,EAAA,CAAAU,YAAA,EAAO;IAGrHV,EAAA,CAAAC,cAAA,eAAwD;IAG1CD,EAAA,CAAAE,UAAA,mBAAAoH,sEAAA;MAAAtH,EAAA,CAAAI,aAAA,CAAAoG,IAAA;MAAA,MAAAe,OAAA,GAAAvH,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA+G,OAAA,CAAAV,qBAAA,EAAuB;IAAA,EAAC;IAAC7G,EAAA,CAAAU,YAAA,EAAW;IACvDV,EAAA,CAAAC,cAAA,oBAG+B;IAArBD,EAAA,CAAAE,UAAA,mBAAAsH,sEAAA;MAAAxH,EAAA,CAAAI,aAAA,CAAAoG,IAAA;MAAA,MAAAiB,OAAA,GAAAzH,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAiH,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAAC1H,EAAA,CAAAU,YAAA,EAAW;;;;IAjDWV,EAAA,CAAA4F,UAAA,CAAA5F,EAAA,CAAAuB,eAAA,KAAAoG,GAAA,EAA4B;IAD/E3H,EAAA,CAAAY,UAAA,WAAAgH,OAAA,CAAA9G,WAAA,CAAAC,SAAA,gCAA+D,YAAA6G,OAAA,CAAAlB,qBAAA;IAMjC1G,EAAA,CAAAW,SAAA,GAA8E;IAA9EX,EAAA,CAAAkB,iBAAA,CAAA0G,OAAA,CAAA9G,WAAA,CAAAC,SAAA,oDAA8E;IAG/Gf,EAAA,CAAAW,SAAA,GAA0D;IAA1DX,EAAA,CAAAkB,iBAAA,CAAA0G,OAAA,CAAA9G,WAAA,CAAAC,SAAA,gCAA0D;IAIEf,EAAA,CAAAW,SAAA,GAAwD;IAAxDX,EAAA,CAAAkB,iBAAA,CAAA0G,OAAA,CAAA9G,WAAA,CAAAC,SAAA,8BAAwD;IAShGf,EAAA,CAAAW,SAAA,GAAuE;IAAvEX,EAAA,CAAA6H,qBAAA,UAAAD,OAAA,CAAA9G,WAAA,CAAAC,SAAA,qCAAuE;IAFvEf,EAAA,CAAAY,UAAA,YAAAgH,OAAA,CAAAT,aAAA,CAA2B;IAS3BnH,EAAA,CAAAW,SAAA,GAAuE;IAAvEX,EAAA,CAAA6H,qBAAA,UAAAD,OAAA,CAAA9G,WAAA,CAAAC,SAAA,qCAAuE;IAFvEf,EAAA,CAAAY,UAAA,YAAAgH,OAAA,CAAAT,aAAA,CAA2B;IAQCnH,EAAA,CAAAW,SAAA,GAA0D;IAA1DX,EAAA,CAAAkB,iBAAA,CAAA0G,OAAA,CAAA9G,WAAA,CAAAC,SAAA,gCAA0D;IAKzDf,EAAA,CAAAW,SAAA,GAA0D;IAA1DX,EAAA,CAAA8H,kBAAA,MAAAF,OAAA,CAAA9G,WAAA,CAAAC,SAAA,oCAA0D;IAIhGf,EAAA,CAAAW,SAAA,GAAyD;IAAzDX,EAAA,CAAA6H,qBAAA,UAAAD,OAAA,CAAA9G,WAAA,CAAAC,SAAA,uBAAyD;IAGzDf,EAAA,CAAAW,SAAA,GAA+D;IAA/DX,EAAA,CAAA6H,qBAAA,UAAAD,OAAA,CAAA9G,WAAA,CAAAC,SAAA,6BAA+D;;;;;;IAQjFf,EAAA,CAAAC,cAAA,mBAG4C;IAFlCD,EAAA,CAAAE,UAAA,2BAAA6H,6EAAA7C,MAAA;MAAAlF,EAAA,CAAAI,aAAA,CAAA4H,IAAA;MAAA,MAAAC,OAAA,GAAAjI,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAyH,OAAA,CAAAC,oBAAA,GAAAhD,MAAA;IAAA,EAAkC,oBAAAiD,sEAAA;MAAAnI,EAAA,CAAAI,aAAA,CAAA4H,IAAA;MAAA,MAAAI,OAAA,GAAApI,EAAA,CAAAO,aAAA;MAAA,OAExBP,EAAA,CAAAQ,WAAA,CAAA4H,OAAA,CAAAC,oBAAA,EAAsB;IAAA,EAFE;IAGxCrI,EAAA,CAAAC,cAAA,cAAoE;IAEhCD,EAAA,CAAAgB,MAAA,GAA+E;IAAAhB,EAAA,CAAAU,YAAA,EAAI;IAC/GV,EAAA,CAAAiB,SAAA,SAAI;IACJjB,EAAA,CAAAC,cAAA,eAA2B;IAChCD,EAAA,CAAAgB,MAAA,GAA6D;IAAAhB,EAAA,CAAAU,YAAA,EAAI;IAE5DV,EAAA,CAAAiB,SAAA,SAAI;IACJjB,EAAA,CAAAC,cAAA,aACoD;IADjDD,EAAA,CAAAE,UAAA,mBAAAoI,+DAAA;MAAAtI,EAAA,CAAAI,aAAA,CAAA4H,IAAA;MAAA,MAAAO,OAAA,GAAAvI,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA+H,OAAA,CAAAvB,oBAAA,EAAsB;IAAA,EAAC;IACiBhH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAgB,MAAA,IAAwD;IAAAhB,EAAA,CAAAU,YAAA,EAAI;IAG3HV,EAAA,CAAAC,cAAA,eAAwD;IAG1CD,EAAA,CAAAE,UAAA,mBAAAsI,sEAAA;MAAAxI,EAAA,CAAAI,aAAA,CAAA4H,IAAA;MAAA,MAAAS,OAAA,GAAAzI,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAiI,OAAA,CAAAJ,oBAAA,EAAsB;IAAA,EAAC;IAACrI,EAAA,CAAAU,YAAA,EAAW;IACtDV,EAAA,CAAAC,cAAA,oBAE+B;IAArBD,EAAA,CAAAE,UAAA,mBAAAwI,sEAAA;MAAA1I,EAAA,CAAAI,aAAA,CAAA4H,IAAA;MAAA,MAAAW,OAAA,GAAA3I,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAmI,OAAA,CAAAjB,QAAA,EAAU;IAAA,EAAC;IAAC1H,EAAA,CAAAU,YAAA,EAAW;;;;IArBUV,EAAA,CAAA4F,UAAA,CAAA5F,EAAA,CAAAuB,eAAA,KAAAoG,GAAA,EAA4B;IAD9E3H,EAAA,CAAAY,UAAA,WAAAgI,OAAA,CAAA9H,WAAA,CAAAC,SAAA,gCAA+D,YAAA6H,OAAA,CAAAV,oBAAA;IAMjClI,EAAA,CAAAW,SAAA,GAA+E;IAA/EX,EAAA,CAAAkB,iBAAA,CAAA0H,OAAA,CAAA9H,WAAA,CAAAC,SAAA,qDAA+E;IAGhHf,EAAA,CAAAW,SAAA,GAA6D;IAA7DX,EAAA,CAAAkB,iBAAA,CAAA0H,OAAA,CAAA9H,WAAA,CAAAC,SAAA,mCAA6D;IAIDf,EAAA,CAAAW,SAAA,GAAwD;IAAxDX,EAAA,CAAAkB,iBAAA,CAAA0H,OAAA,CAAA9H,WAAA,CAAAC,SAAA,8BAAwD;IAIzGf,EAAA,CAAAW,SAAA,GAAyD;IAAzDX,EAAA,CAAA6H,qBAAA,UAAAe,OAAA,CAAA9H,WAAA,CAAAC,SAAA,uBAAyD;IAGzDf,EAAA,CAAAW,SAAA,GAA+D;IAA/DX,EAAA,CAAA6H,qBAAA,UAAAe,OAAA,CAAA9H,WAAA,CAAAC,SAAA,6BAA+D;;;;;IAgBoCf,EAAA,CAAAC,cAAA,UACjF;IAACD,EAAA,CAAAgB,MAAA,GACzB;IAAAhB,EAAA,CAAAU,YAAA,EAAM;;;;IADmBV,EAAA,CAAAW,SAAA,GACzB;IADyBX,EAAA,CAAAuD,kBAAA,MAAAsF,OAAA,CAAA/H,WAAA,CAAAC,SAAA,8BAAA8H,OAAA,CAAApF,SAAA,OAAAoF,OAAA,CAAA/H,WAAA,CAAAC,SAAA,8BACzB;;;;;;IAeZf,EAAA,CAAAC,cAAA,mBAGmD;IAFzCD,EAAA,CAAAE,UAAA,2BAAA4I,6EAAA5D,MAAA;MAAAlF,EAAA,CAAAI,aAAA,CAAA2I,IAAA;MAAA,MAAAC,OAAA,GAAAhJ,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAwI,OAAA,CAAAC,qBAAA,GAAA/D,MAAA;IAAA,EAAmC,oBAAAgE,sEAAA;MAAAlJ,EAAA,CAAAI,aAAA,CAAA2I,IAAA;MAAA,MAAAI,OAAA,GAAAnJ,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAA2I,OAAA,CAAAF,qBAAA,GAED,KAAK;IAAA,EAFJ;IAGzCjJ,EAAA,CAAAC,cAAA,cAAoE;IAEhCD,EAAA,CAAAgB,MAAA,GAAoE;IAAAhB,EAAA,CAAAU,YAAA,EAAI;IACpGV,EAAA,CAAAiB,SAAA,SAAI;IACAjB,EAAA,CAAAC,cAAA,eAAgD;IACzCD,EAAA,CAAAgB,MAAA,GAA8D;IAAAhB,EAAA,CAAAU,YAAA,EAAI;IAE7EV,EAAA,CAAAiB,SAAA,SAAI;IACJjB,EAAA,CAAAC,cAAA,aACoD;IADjDD,EAAA,CAAAE,UAAA,mBAAAkJ,+DAAA;MAAApJ,EAAA,CAAAI,aAAA,CAAA2I,IAAA;MAAA,MAAAM,OAAA,GAAArJ,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA6I,OAAA,CAAAC,sBAAA,EAAwB;IAAA,EAAC;IACetJ,EAAA,CAAAC,cAAA,SAAG;IAACD,EAAA,CAAAgB,MAAA,IAAuD;IAAAhB,EAAA,CAAAU,YAAA,EAAI;IAG3HV,EAAA,CAAAC,cAAA,eAA0E;IAG5DD,EAAA,CAAAE,UAAA,mBAAAqJ,sEAAA;MAAAvJ,EAAA,CAAAI,aAAA,CAAA2I,IAAA;MAAA,MAAAS,OAAA,GAAAxJ,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAgJ,OAAA,CAAAP,qBAAA,GAAiC,KAAK;IAAA,EAAC;IAACjJ,EAAA,CAAAU,YAAA,EAAW;IAC7DV,EAAA,CAAAC,cAAA,oBAE+B;IAArBD,EAAA,CAAAE,UAAA,mBAAAuJ,sEAAA;MAAAzJ,EAAA,CAAAI,aAAA,CAAA2I,IAAA;MAAA,MAAAW,OAAA,GAAA1J,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAkJ,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAAC3J,EAAA,CAAAU,YAAA,EAAW;;;;IArBWV,EAAA,CAAA4F,UAAA,CAAA5F,EAAA,CAAAuB,eAAA,KAAAoG,GAAA,EAA4B;IAD/E3H,EAAA,CAAAY,UAAA,WAAAgJ,OAAA,CAAA9I,WAAA,CAAAC,SAAA,gCAA+D,YAAA6I,OAAA,CAAAX,qBAAA;IAMjCjJ,EAAA,CAAAW,SAAA,GAAoE;IAApEX,EAAA,CAAAkB,iBAAA,CAAA0I,OAAA,CAAA9I,WAAA,CAAAC,SAAA,0CAAoE;IAGrFf,EAAA,CAAAW,SAAA,GAA8D;IAA9DX,EAAA,CAAAkB,iBAAA,CAAA0I,OAAA,CAAA9I,WAAA,CAAAC,SAAA,oCAA8D;IAIjBf,EAAA,CAAAW,SAAA,GAAuD;IAAvDX,EAAA,CAAA8H,kBAAA,MAAA8B,OAAA,CAAA9I,WAAA,CAAAC,SAAA,kCAAuD;IAIzGf,EAAA,CAAAW,SAAA,GAAyD;IAAzDX,EAAA,CAAA6H,qBAAA,UAAA+B,OAAA,CAAA9I,WAAA,CAAAC,SAAA,uBAAyD;IAGzDf,EAAA,CAAAW,SAAA,GAA0D;IAA1DX,EAAA,CAAA6H,qBAAA,UAAA+B,OAAA,CAAA9I,WAAA,CAAAC,SAAA,wBAA0D;;;;;;;;;;;IAM5Ef,EAAA,CAAAC,cAAA,mBAGmD;IAFzCD,EAAA,CAAAE,UAAA,2BAAA2J,6EAAA3E,MAAA;MAAAlF,EAAA,CAAAI,aAAA,CAAA0J,IAAA;MAAA,MAAAC,OAAA,GAAA/J,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAuJ,OAAA,CAAAC,qBAAA,GAAA9E,MAAA;IAAA,EAAmC,oBAAA+E,sEAAA;MAAAjK,EAAA,CAAAI,aAAA,CAAA0J,IAAA;MAAA,MAAAI,OAAA,GAAAlK,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAA0J,OAAA,CAAAF,qBAAA,GAED,KAAK;IAAA,EAFJ;IAGzChK,EAAA,CAAAC,cAAA,cAAoE;IAExDD,EAAA,CAAAgB,MAAA,GACJ;IAAAhB,EAAA,CAAAU,YAAA,EAAO;IACXV,EAAA,CAAAC,cAAA,QAAG;IAC6BD,EAAA,CAAAgB,MAAA,GAAwF;IAAAhB,EAAA,CAAAU,YAAA,EAAI;IACxHV,EAAA,CAAAiB,SAAA,SAAI;IACJjB,EAAA,CAAAC,cAAA,eAAgD;IACrDD,EAAA,CAAAgB,MAAA,IAA8D;IAAAhB,EAAA,CAAAU,YAAA,EAAI;IAE7DV,EAAA,CAAAiB,SAAA,UAAI;IACJjB,EAAA,CAAAC,cAAA,aACoD;IADjDD,EAAA,CAAAE,UAAA,mBAAAiK,+DAAA;MAAAnK,EAAA,CAAAI,aAAA,CAAA0J,IAAA;MAAA,MAAAM,OAAA,GAAApK,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA4J,OAAA,CAAAd,sBAAA,EAAwB;IAAA,EAAC;IACgBtJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAgB,MAAA,IAAwD;IAAAhB,EAAA,CAAAU,YAAA,EAAI;IAG5HV,EAAA,CAAAC,cAAA,eAA0E;IAG5DD,EAAA,CAAAE,UAAA,mBAAAmK,sEAAA;MAAArK,EAAA,CAAAI,aAAA,CAAA0J,IAAA;MAAA,MAAAQ,OAAA,GAAAtK,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAA8J,OAAA,CAAAN,qBAAA,GAAiC,KAAK;IAAA,EAAC;IAAChK,EAAA,CAAAU,YAAA,EAAW;IAC7DV,EAAA,CAAAC,cAAA,oBAE+B;IAArBD,EAAA,CAAAE,UAAA,mBAAAqK,sEAAA;MAAAvK,EAAA,CAAAI,aAAA,CAAA0J,IAAA;MAAA,MAAAU,OAAA,GAAAxK,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAgK,OAAA,CAAAb,QAAA,EAAU;IAAA,EAAC;IAAC3J,EAAA,CAAAU,YAAA,EAAW;;;;IAxBWV,EAAA,CAAA4F,UAAA,CAAA5F,EAAA,CAAAuB,eAAA,KAAAoG,GAAA,EAA4B;IAD/E3H,EAAA,CAAAY,UAAA,WAAA6J,OAAA,CAAA3J,WAAA,CAAAC,SAAA,gCAA+D,YAAA0J,OAAA,CAAAT,qBAAA;IAMzDhK,EAAA,CAAAW,SAAA,GACJ;IADIX,EAAA,CAAA8H,kBAAA,MAAA2C,OAAA,CAAA3J,WAAA,CAAAC,SAAA,6BAAAf,EAAA,CAAA0K,eAAA,KAAAC,GAAA,EAAAF,OAAA,CAAA7G,OAAA,QACJ;IAE4B5D,EAAA,CAAAW,SAAA,GAAwF;IAAxFX,EAAA,CAAAkB,iBAAA,CAAAuJ,OAAA,CAAA3J,WAAA,CAAAC,SAAA,0CAAAf,EAAA,CAAA0K,eAAA,KAAAC,GAAA,EAAAF,OAAA,CAAA7G,OAAA,GAAwF;IAGzH5D,EAAA,CAAAW,SAAA,GAA8D;IAA9DX,EAAA,CAAAkB,iBAAA,CAAAuJ,OAAA,CAAA3J,WAAA,CAAAC,SAAA,oCAA8D;IAIDf,EAAA,CAAAW,SAAA,GAAwD;IAAxDX,EAAA,CAAAkB,iBAAA,CAAAuJ,OAAA,CAAA3J,WAAA,CAAAC,SAAA,8BAAwD;IAI1Gf,EAAA,CAAAW,SAAA,GAAyD;IAAzDX,EAAA,CAAA6H,qBAAA,UAAA4C,OAAA,CAAA3J,WAAA,CAAAC,SAAA,uBAAyD;IAGzDf,EAAA,CAAAW,SAAA,GAA0D;IAA1DX,EAAA,CAAA6H,qBAAA,UAAA4C,OAAA,CAAA3J,WAAA,CAAAC,SAAA,wBAA0D;;;;;;;;;;;;;;;;ADnahF,OAAM,MAAO6J,qBAAsB,SAAQpL,aAAa;EAyFpDqL,cAAcA,CAAA;IACV,IAAI,IAAI,CAACC,QAAQ,EAAE;MACfC,aAAa,CAAC,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC;;;IAGlC,IAAI,CAACA,QAAQ,GAAGE,WAAW,CAAC,MAAK;MAC7B,IAAI,IAAI,CAACvH,SAAS,GAAG,CAAC,EAAE;QACpB,IAAI,CAACA,SAAS,EAAE;OACnB,MAAM;QACHsH,aAAa,CAAC,IAAI,CAACD,QAAQ,CAAC;QAC5B,IAAI,CAACA,QAAQ,GAAG,IAAI,CAAC,CAAC;;IAE9B,CAAC,EAAE,IAAI,CAAC;EACZ;EAEAG,UAAUA,CAAA;IACN,IAAI,CAACxH,SAAS,GAAG,EAAE;IACnBsH,aAAa,CAAC,IAAI,CAACD,QAAQ,CAAC;IAC5B,IAAI,CAACD,cAAc,EAAE;IACrB,IAAI,CAACK,OAAO,EAAE;EAClB;EACA;EACAC,qBAAqBA,CAAA;IACjB,IAAI,CAAC1H,SAAS,GAAG,EAAE;IACnBsH,aAAa,CAAC,IAAI,CAACD,QAAQ,CAAC;IAC5B,IAAI,CAACD,cAAc,EAAE;IACrB,IAAI,CAACO,eAAe,EAAE;EAC1B;EAEAC,iBAAiBA,CAACC,cAAsB;IACpC;IACA,MAAM,CAACC,GAAG,EAAEC,KAAK,EAAEC,IAAI,CAAC,GAAGH,cAAc,CAACI,KAAK,CAAC,SAAS,CAAC;IAC1D;IACA,OAAO,GAAGH,GAAG,IAAIC,KAAK,IAAIC,IAAI,EAAE;EACpC;EAEAE,UAAUA,CAAA;IACN,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACnI,SAAS,GAAG,EAAE;IACnB,IAAI,CAACoH,cAAc,EAAE;EACzB;EAKAgB,YAC0CC,oBAA0C,EACxCC,sBAA+C,EAC/EC,WAAwB,EACxBC,QAAkB,EAClBC,aAA4B;IAEpC,KAAK,CAACD,QAAQ,CAAC;IANuB,KAAAH,oBAAoB,GAApBA,oBAAoB;IAClB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtD,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,aAAa,GAAbA,aAAa;IAtFzB,KAAAN,QAAQ,GAAY,KAAK;IAGzB,KAAAO,WAAW,GAAG,IAAI,CAACrL,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;IAE/D,KAAAqL,cAAc,GAAGvM,SAAS,CAACwM,WAAW;IACtC,KAAAhH,iBAAiB,GAAY,KAAK;IAKlC,KAAAiH,OAAO,GAAY,KAAK;EA8ExB;EAEAC,iBAAiBA,CAACC,KAAK;IACnB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACC,YAAY;IACnC,IAAI,CAACD,UAAU,GAAG;MAAE,GAAG,IAAI,CAACA,UAAU;MAAE,GAAGD;IAAK,CAAC;IACjD,IAAI,CAACG,MAAM,CAAC,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACL,UAAU,CAAC;EAC3E;EAEMM,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACV,IAAIC,EAAE,GAAGF,KAAI;MAEbA,KAAI,CAACG,KAAK,GAAG,CAAC;QAAEC,KAAK,EAAEJ,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,+BAA+B;MAAC,CAAE,EAAE;QAAEqM,KAAK,EAAEJ,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,wBAAwB;MAAC,CAAE,CAAE;MAEvJiM,KAAI,CAACK,IAAI,GAAG;QAAEC,IAAI,EAAE,YAAY;QAAEC,UAAU,EAAE;MAAG,CAAE;MACnDP,KAAI,CAACQ,UAAU,CAAC,EAAE,EAAC,UAAU,CAAC,CAACC,IAAI,CAAEC,IAAI,IAAG;QACxCR,EAAE,CAACS,aAAa,CAAC,EAAE,EAAE,QAAQ,CAAC;MAClC,CAAC,CAAC;MACFX,KAAI,CAACY,uBAAuB,GAAG,KAAK;MACpCZ,KAAI,CAACP,UAAU,GAAG;QACdoB,cAAc,EAAE,IAAI;QACpBC,gBAAgB,EAAE,CAAC;QACnBC,aAAa,EAAE,CAAC;QAChBC,KAAK,EAAE,EAAE;QACTC,UAAU,EAAE,CAAC;QACbC,WAAW,EAAE,CAAC;QACdC,cAAc,EAAE,IAAI;QACpBjI,QAAQ,EAAE,CAAC;OACd;MAED8G,KAAI,CAACrJ,YAAY,GAAE;QACfC,OAAO,EAAE,CAAC;QACVC,WAAW,EAAE,CAAC;QACduK,KAAK,EAAE,CAAC;QACRrK,GAAG,EAAE;OACR;MAEDiJ,KAAI,CAACqB,cAAc,GAAG;QAClBC,YAAY,EAAC,CAAC;QACd1K,OAAO,EAAC,IAAI;QACZ2K,OAAO,EAAC,IAAI;QACZxK,GAAG,EAAC,IAAI;QACRyK,WAAW,EAAC,IAAI;QAChBC,GAAG,EAAC,IAAI;QACRC,OAAO,EAAE;OACZ;MACD1B,KAAI,CAAC2B,UAAU,GAAG;QACd/K,OAAO,EAAG,IAAI;QACd2K,OAAO,EAAE,IAAI;QACbxK,GAAG,EAAE,IAAI;QACTyK,WAAW,EAAE,IAAI;QACjBC,GAAG,EAAE,IAAI;QACTG,WAAW,EAAE,IAAI;QACjBC,SAAS,EAAE,IAAI;QACfC,GAAG,EAAE;OACR;MACD9B,KAAI,CAAC+B,OAAO,GAAG,CACX;QACIC,IAAI,EAAEhC,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;QAC7DkO,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,KAAK;QACbC,KAAK,EAAC;UACFC,MAAM,EAAE,SAAS;UACxBC,KAAK,EAAE;SACH;QACDC,SAASA,CAACC,EAAE,EAAEC,IAAI;UACdzC,EAAE,CAACnH,SAAS,GAAG4J,IAAI,CAACpB,OAAO;UAC3BrB,EAAE,CAAC0C,iBAAiB,EAAE;UACtB1C,EAAE,CAAC2C,eAAe,EAAE;UACpB3C,EAAE,CAAC9I,gBAAgB,GAAGuL,IAAI,CAACvL,gBAAgB;UAC3C8I,EAAE,CAAClH,WAAW,GAAG2J,IAAI,CAAC3J,WAAW;QACrC;OACH,EACD;QACIgJ,IAAI,EAAEhC,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;QAC9DkO,GAAG,EAAE,aAAa;QAClBC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;OACX,EAAE;QACCL,IAAI,EAAEhC,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QACxDkO,GAAG,EAAE,aAAa;QAClBC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;OACX,EAAE;QACCL,IAAI,EAAEhC,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;QAC1DkO,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;OACX,EAAE;QACCL,IAAI,EAAEhC,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;QAC9DkO,GAAG,EAAE,aAAa;QAClBC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;OACX,EAAE;QACCL,IAAI,EAAEhC,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC,GAAC,IAAI,GAAEiM,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC;QAC9HkO,GAAG,EAAE,YAAY;QACjBC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,KAAK;QACbS,eAAeA,CAAC9B,KAAK,EAAE2B,IAAI;UACvB,IAAII,MAAM,GAAG7C,EAAE,CAAC8C,WAAW,CAACC,qBAAqB,CAACN,IAAI,CAACxL,qBAAqB,CAAC;UAC7E,IAAI+L,KAAK,GAAIhD,EAAE,CAAC8C,WAAW,CAACC,qBAAqB,CAACN,IAAI,CAACvL,gBAAgB,CAAC;UACxE,IAAGuL,IAAI,CAAC3J,WAAW,IAAI,UAAU,EAAC;YAC9B,OAAO+J,MAAM,GAAC,IAAI,GAACG,KAAK,GAAE,KAAK;WAClC,MAAK,IAAGP,IAAI,CAAC3J,WAAW,IAAI,WAAW,EAAC;YACrC,OAAO+J,MAAM,GAAC,IAAI,GAACG,KAAK,GAAGhD,EAAE,CAACf,WAAW;WAC5C,MAAM,IAAG,CAACwD,IAAI,CAAC3J,WAAW,IAAI,EAAE,EAAEG,WAAW,EAAE,CAACC,QAAQ,CAAC,SAAS,CAACD,WAAW,EAAE,CAAC,EAAC;YAC/E,OAAO4J,MAAM,GAAC,IAAI,GAACG,KAAK,GAAG,MAAM;;UAErC,OAAOP,IAAI,CAACxL,qBAAqB,GAAC,IAAI,GAACwL,IAAI,CAACvL,gBAAgB;QAChE;OACH,EACD;QACI4K,IAAI,EAAEhC,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;QAC3DkO,GAAG,EAAE,UAAU;QACfC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;OACX,EACD;QACIL,IAAI,EAAEhC,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC;QAClEkO,GAAG,EAAE,UAAU;QACfC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,KAAK;QACbS,eAAeA,CAAC9B,KAAK;UACjB,OAAOd,EAAE,CAACjH,uBAAuB,CAAC+H,KAAK,CAAC;QAC5C;OACH,EACD;QACIgB,IAAI,EAAEhC,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;QAC/DkO,GAAG,EAAE,cAAc;QACnBC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,KAAK;QACbS,eAAeA,CAAC9B,KAAK;UACjB,IAAGA,KAAK,IAAI,IAAI,EAAE,OAAO,EAAE;UAC3B,OAAOd,EAAE,CAAC7B,iBAAiB,CAAC2C,KAAK,CAAC;QACtC;OACH,CACJ;MACDhB,KAAI,CAACpJ,OAAO,GAAG,IAAI,EACnBoJ,KAAI,CAACmD,WAAW,GAAG;QACfC,gBAAgB,EAAE,KAAK;QACvBC,aAAa,EAAE,KAAK;QACpBC,YAAY,EAAE,IAAI;QAClBC,mBAAmB,EAAE,KAAK;QAC1BC,MAAM,EAAE,CACJ;UACIlD,IAAI,EAAE,uBAAuB;UAC7BmD,OAAO,EAAEzD,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;UAC5D2P,IAAI,EAAE,SAAAA,CAAUhB,EAAE,EAAEC,IAAI;YACpBzC,EAAE,CAACyD,MAAM,CAACC,QAAQ,CAAC,CAAC,4BAA4B,EAACjB,IAAI,CAACpB,OAAO,CAAC,CAAC;UACnE,CAAC;UACDsC,UAAUA,CAACnB,EAAE,EAAEC,IAAI;YACf,OAAQA,IAAI,CAACmB,QAAQ,IAAI5D,EAAE,CAAC6D,WAAW,CAAC,CAAC7D,EAAE,CAACd,cAAc,CAAC4E,QAAQ,CAACC,YAAY,CAAC,CAAC;UACtF;SACH,EACD;UACI3D,IAAI,EAAE5N,UAAU,CAACwR,IAAI;UACrBT,OAAO,EAAEzD,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC;UACzE2P,IAAI,EAAE,SAAAA,CAAUhB,EAAE,EAAEC,IAAI;YACpBzC,EAAE,CAACiE,2BAA2B,CAACxB,IAAI,CAAC;UACxC,CAAC;UACDkB,UAAUA,CAACnB,EAAE,EAAEC,IAAI;YACf,OAAQA,IAAI,CAACmB,QAAQ,IAAInB,IAAI,CAACzJ,QAAQ,IAAIrG,SAAS,CAACuR,iBAAiB,CAACC,IAAI,IAAInE,EAAE,CAAC6D,WAAW,CAAC,CAAC7D,EAAE,CAACd,cAAc,CAAC4E,QAAQ,CAACC,YAAY,CAAC,CAAC;UAC3I;SACH,EACD;UACI3D,IAAI,EAAE5N,UAAU,CAAC4R,SAAS;UAC1Bb,OAAO,EAAEzD,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;UACvE2P,IAAI,EAAE,SAAAA,CAAUhB,EAAE,EAAEC,IAAI;YACpB,IAAIA,IAAI,CAACzJ,QAAQ,IAAIrG,SAAS,CAACuR,iBAAiB,CAACG,QAAQ,EAAE;cACvDrE,EAAE,CAACtJ,OAAO,GAAG+L,IAAI,CAAC/L,OAAO;cACzBsJ,EAAE,CAACsE,+BAA+B,CAAC7B,IAAI,CAAC;aAC3C,MAAM,IAAIA,IAAI,CAACzJ,QAAQ,IAAIrG,SAAS,CAACuR,iBAAiB,CAACK,QAAQ,EAAE;cAC9DvE,EAAE,CAACtJ,OAAO,GAAG+L,IAAI,CAAC/L,OAAO;cACzBsJ,EAAE,CAACwE,+BAA+B,CAAC/B,IAAI,CAAC;;UAEhD,CAAC;UACDkB,UAAUA,CAACnB,EAAE,EAAEC,IAAI;YACf,OAAQA,IAAI,CAACmB,QAAQ,KAAKnB,IAAI,CAACzJ,QAAQ,IAAIrG,SAAS,CAACuR,iBAAiB,CAACG,QAAQ,IAAI5B,IAAI,CAACzJ,QAAQ,IAAIrG,SAAS,CAACuR,iBAAiB,CAACK,QAAQ,CAAC,IAAIvE,EAAE,CAAC6D,WAAW,CAAC,CAAC7D,EAAE,CAACd,cAAc,CAAC4E,QAAQ,CAACC,YAAY,CAAC,CAAC;UAC1M;SACH;OAER;MACDjE,KAAI,CAACJ,UAAU,GAAG,CAAC;MACnBI,KAAI,CAACH,QAAQ,GAAG,EAAE;MAClB;MACAG,KAAI,CAACF,IAAI,GAAG,EAAE;MACdE,KAAI,CAAC2E,OAAO,GAAG;QACXC,OAAO,EAAE,EAAE;QACX1B,KAAK,EAAE;OACV;MACDlD,KAAI,CAACvI,kBAAkB,GAAG,CAAC;QACvBuK,IAAI,EAAEhC,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC;QAC5DkO,GAAG,EAAE,cAAc;QACnBC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;OACX,EAAC;QACEL,IAAI,EAAEhC,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;QAC3DkO,GAAG,EAAE,MAAM;QACXC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;OACX,EAAC;QACEL,IAAI,EAAEhC,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QACxDkO,GAAG,EAAE,OAAO;QACZC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE;OACX,EAAC;QACEL,IAAI,EAAEhC,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;QAC7DkO,GAAG,EAAE,YAAY;QACjBC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,KAAK;QACbS,eAAeA,CAAC9B,KAAK;UACjB,OAAOd,EAAE,CAAC2E,gBAAgB,CAAC7D,KAAK,CAAC;QACrC;OACH,EAAC;QACEgB,IAAI,EAAEhC,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;QAC3DkO,GAAG,EAAE,YAAY;QACjBC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,KAAK;QACbS,eAAeA,CAAC9B,KAAK,EAAE2B,IAAI;UACvB,OAAOzC,EAAE,CAAC2E,gBAAgB,CAAC7D,KAAK,EAAE2B,IAAI,CAACmC,WAAW,CAAC;QACvD;OACH,EAAC;QACE9C,IAAI,EAAEhC,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;QACzDkO,GAAG,EAAE,cAAc;QACnBC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,KAAK;QACbS,eAAeA,CAAC9B,KAAK;UACjBA,KAAK,GAAGd,EAAE,CAAChJ,YAAY,CAAC8J,KAAK,CAAC;UAC9B,IAAI,CAACd,EAAE,CAAClH,WAAW,IAAI,EAAE,EAAEG,WAAW,EAAE,IAAI,UAAU,CAACA,WAAW,EAAE,EAAE,OAAO6H,KAAK,GAAG,KAAK,MACrF,IAAI,CAACd,EAAE,CAAClH,WAAW,IAAI,EAAE,EAAEG,WAAW,EAAE,CAACC,QAAQ,CAAC,SAAS,CAACD,WAAW,EAAE,CAAC,EAAE,OAAO6H,KAAK,GAAG,MAAM,MACjG,IAAI,CAACd,EAAE,CAAClH,WAAW,IAAI,EAAE,EAAEG,WAAW,EAAE,IAAI,WAAW,CAACA,WAAW,EAAE,EAAE,OAAO6H,KAAK,GAAGd,EAAE,CAACpM,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC,MACrI,OAAOiN,KAAK;QACrB;OACH,EAAC;QACEgB,IAAI,EAAEhC,KAAI,CAAClM,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;QAC7DkO,GAAG,EAAE,cAAc;QACnBC,IAAI,EAAE,aAAa;QACnBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,KAAK;QACbS,eAAeA,CAAC9B,KAAK;UACjB,IAAI+D,MAAM,GAAG,KAAK,GAAG/D,KAAK,GAACd,EAAE,CAAC9I,gBAAgB;UAC9C,OAAO4N,IAAI,CAACC,KAAK,CAACF,MAAM,GAAG,KAAK,CAAC,GAAC,KAAK,GAAG,IAAI;QAClD;OACH,CACA;MACD/E,KAAI,CAACtI,kBAAkB,GAAG;QACtBkN,OAAO,EAAE,EAAE;QACX1B,KAAK,EAAE;OACV;MACDlD,KAAI,CAAC1G,yBAAyB,GAAG,KAAK;MACtC0G,KAAI,CAACpI,qBAAqB,GAAG,CAAC,EAC9BoI,KAAI,CAACH,QAAQ,GAAG,EAAE;MAClBG,KAAI,CAAClI,eAAe,GAAG,EAAE;MACzBkI,KAAI,CAACnI,sBAAsB,GAAG;QAC1BuL,gBAAgB,EAAE,KAAK;QACvBC,aAAa,EAAE,KAAK;QACpBC,YAAY,EAAE,KAAK;QACnBC,mBAAmB,EAAE,KAAK;QAC1BC,MAAM,EAAE;OACX;MAEDxD,KAAI,CAACkF,gBAAgB,GAAGlF,KAAI,CAAChB,WAAW,CAACmG,KAAK,CAACnF,KAAI,CAACP,UAAU,CAAC;MAC/DO,KAAI,CAAClL,kBAAkB,GAAGkL,KAAI,CAAChB,WAAW,CAACmG,KAAK,CAACnF,KAAI,CAACqB,cAAc,CAAC;MACrErB,KAAI,CAACoF,cAAc,GAAGpF,KAAI,CAAChB,WAAW,CAACmG,KAAK,CAACnF,KAAI,CAAC2B,UAAU,CAAC;MAC7D3B,KAAI,CAACL,MAAM,CAACK,KAAI,CAACJ,UAAU,EAAEI,KAAI,CAACH,QAAQ,EAAEG,KAAI,CAACF,IAAI,EAAEE,KAAI,CAACP,UAAU,CAAC;MAEvEO,KAAI,CAACd,aAAa,CAACmG,cAAc,CAAC;QAC9BC,kBAAkB,EAAC,wBAAwB;QAC3CC,YAAY,EAAC;OAChB,CAAC;MACFvF,KAAI,CAACN,YAAY,GAAGM,KAAI,CAACP,UAAU;MACnCO,KAAI,CAACtG,qBAAqB,GAAG,KAAK;MAClCsG,KAAI,CAAC9E,oBAAoB,GAAG,KAAK;MACjC8E,KAAI,CAAChD,qBAAqB,GAAG,KAAK;MAClCgD,KAAI,CAAC/D,qBAAqB,GAAG,KAAK;MAClC+D,KAAI,CAAC7F,aAAa,GAAG,CAAC;MACtB6F,KAAI,CAACwF,qBAAqB,GAAG,KAAK;IAAC;EACvC;EAEA7E,aAAaA,CAAC9J,WAAW,EAAE4O,IAAI;IAC3B,IAAIvF,EAAE,GAAG,IAAI;IACb,IAAIwF,MAAM,GAAG;MACT7O,WAAW;MACX4O;KACH;IACD,IAAI,CAACE,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAI,CAAC9G,oBAAoB,CAAC6B,aAAa,CAAC+E,MAAM,EAAGG,QAAQ,IAAI;MACzD,IAAI,CAACF,oBAAoB,CAACG,OAAO,EAAE;MACnC5F,EAAE,CAAC6F,UAAU,GAAGF,QAAQ,CAACG,GAAG,CAACC,CAAC,IAAG;QAC7B,OAAO;UACHjE,IAAI,EAAEiE,CAAC,CAACjE,IAAI;UACZhB,KAAK,EAAEiF,CAAC,CAACjF;SACZ;MACL,CAAC,CAAC;MAEFd,EAAE,CAAC6F,UAAU,GAAG7F,EAAE,CAAC6F,UAAU,CAACG,MAAM,CAAC,CAACvD,IAAI,EAAEwD,KAAK,EAAEC,IAAI,KACnDD,KAAK,KAAKC,IAAI,CAACC,SAAS,CAAEC,CAAC,IACvBA,CAAC,CAACtE,IAAI,KAAKW,IAAI,CAACX,IAAI,IAAIsE,CAAC,CAACtF,KAAK,KAAK2B,IAAI,CAAC3B,KAC5C,CAAC,CACL;MAED,IAAI,CAACuF,UAAU,GAAG,CAAC;QACfvE,IAAI,EAAC,IAAI,CAAClO,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;QAC5DkO,GAAG,EAAC;OACP,EAAC;QACED,IAAI,EAAC,IAAI,CAAClO,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;QACzDkO,GAAG,EAAC;OACP,EAAC;QACED,IAAI,EAAC,IAAI,CAAClO,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QACvDkO,GAAG,EAAC;OACP,CAAC;MAEF,IAAI,CAACuE,UAAU,GAAG,CAAC;QACfxE,IAAI,EAAC,IAAI,CAAClO,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;QAC7D0R,IAAI,EAAE7S,eAAe,CAAC6T,WAAW;QACjCtG,KAAK,EAAC,CAAC;UAAC6B,IAAI,EAAC,UAAU;UAAEhB,KAAK,EAAC;QAAU,CAAC,EAAC;UAACgB,IAAI,EAAC,oBAAoB;UAAEhB,KAAK,EAAC;QAAoB,CAAC,EAAC;UAACgB,IAAI,EAAC,aAAa;UAAEhB,KAAK,EAAC;QAAa,CAAC,CAAC;QAC7IiB,GAAG,EAAC,gBAAgB;QACpByE,UAAU,EAAE;OACf,EAAC;QACE1E,IAAI,EAAC,IAAI,CAAClO,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;QAC7D0R,IAAI,EAAE7S,eAAe,CAAC6T,WAAW;QACjCtG,KAAK,EAAE,IAAI,CAAC4F,UAAU;QACtB9D,GAAG,EAAC,gBAAgB;QACpByE,UAAU,EAAC;OACd,EAAC;QACE1E,IAAI,EAAC,IAAI,CAAClO,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;QAC9D0R,IAAI,EAAE7S,eAAe,CAAC+T,aAAa;QACnC1E,GAAG,EAAC,CAAC,mBAAmB,EAAC,iBAAiB,CAAC;QAC3C2E,cAAc,EAAE;OACnB,EAAC;QACE5E,IAAI,EAAC,IAAI,CAAClO,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;QAC1D0R,IAAI,EAAE7S,eAAe,CAAC+T,aAAa;QACnC1E,GAAG,EAAC,CAAC,WAAW,EAAC,SAAS,CAAC;QAC3B2E,cAAc,EAAE;OACnB,EACG;QACI5E,IAAI,EAAC,IAAI,CAAClO,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC;QACjE0R,IAAI,EAAE7S,eAAe,CAACiU,QAAQ;QAC9B1G,KAAK,EAAC,CAAC;UAAC6B,IAAI,EAAE,IAAI,CAAClO,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;UAAEiN,KAAK,EAAEnO,SAAS,CAACuR,iBAAiB,CAACK;QAAQ,CAAC,EACtH;UAACzC,IAAI,EAAE,IAAI,CAAClO,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;UAAEiN,KAAK,EAAEnO,SAAS,CAACuR,iBAAiB,CAACG;QAAQ,CAAC,EACnH;UAACvC,IAAI,EAAE,IAAI,CAAClO,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;UAAEiN,KAAK,EAAEnO,SAAS,CAACuR,iBAAiB,CAACC;QAAI,CAAC,CAC3G;QACLpC,GAAG,EAAC,UAAU;QACdyE,UAAU,EAAE;OACf,EACD;QACI1E,IAAI,EAAC,IAAI,CAAClO,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;QACzD0R,IAAI,EAAE7S,eAAe,CAAC6T,WAAW;QACjCtG,KAAK,EAAE,IAAI,CAAC2G,aAAa;QACzB7E,GAAG,EAAC,gBAAgB;QACpByE,UAAU,EAAE;OACf,CACJ;IACL,CAAC,EAAC,IAAI,EAAE,MAAI;MACR,IAAI,CAACf,oBAAoB,CAACG,OAAO,EAAE;IACvC,CAAC,CAAC;EACN;EAEAtF,UAAUA,CAAC3J,WAAW,EAAE4O,IAAI;IACxB,IAAIvF,EAAE,GAAG,IAAI;IACb,IAAIwF,MAAM,GAAG;MACT7O,WAAW;MACX4O;KACH;IACD,IAAI,CAACE,oBAAoB,CAACC,MAAM,EAAE;IAClC,OAAO,IAAImB,OAAO,CAAC,CAACC,OAAO,EAACC,MAAM,KAAI;MAClC,IAAI,CAACnI,oBAAoB,CAAC6B,aAAa,CAAC+E,MAAM,EAAGG,QAAQ,IAAI;QACzD,IAAI,CAACF,oBAAoB,CAACG,OAAO,EAAE;QACnC5F,EAAE,CAAC4G,aAAa,GAAGjB,QAAQ,CAACG,GAAG,CAACC,CAAC,IAAG;UAChC,OAAO;YACHjE,IAAI,EAAEiE,CAAC,CAACjE,IAAI;YACZhB,KAAK,EAAEiF,CAAC,CAACjF;WACZ;QACL,CAAC,CAAC;QAEFd,EAAE,CAAC4G,aAAa,GAAG5G,EAAE,CAAC4G,aAAa,CAACZ,MAAM,CAAC,CAACvD,IAAI,EAAEwD,KAAK,EAAEC,IAAI,KACzDD,KAAK,KAAKC,IAAI,CAACC,SAAS,CAAEC,CAAC,IACvBA,CAAC,CAACtE,IAAI,KAAKW,IAAI,CAACX,IAAI,IAAIsE,CAAC,CAACtF,KAAK,KAAK2B,IAAI,CAAC3B,KAC5C,CAAC,CACL;QACDgG,OAAO,CAAC,EAAE,CAAC;MACf,CAAC,EAAC,IAAI,EAAE,MAAI;QACR,IAAI,CAACrB,oBAAoB,CAACG,OAAO,EAAE;MACvC,CAAC,CAAC;IACN,CAAC,CAAC;EAEN;EAEAnG,MAAMA,CAACuH,IAAI,EAAEC,KAAK,EAAErH,IAAI,EAAE4F,MAAM;IAC5B,IAAIxF,EAAE,GAAG,IAAI;IACb,IAAI,CAACN,UAAU,GAAGsH,IAAI;IACtB,IAAI,CAACrH,QAAQ,GAAGsH,KAAK;IACrB;IACA,IAAIC,UAAU,GAAG;MACbF,IAAI;MACJhF,IAAI,EAAEiF;MACN;KACH;;IACD,IAAI,CAACxB,oBAAoB,CAACC,MAAM,EAAE;IAElCyB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7H,UAAU,CAAC,CAAC8H,OAAO,CAACtF,GAAG,IAAG;MACvCmF,UAAU,CAACnF,GAAG,CAAC,GAAG,IAAI,CAACxC,UAAU,CAACwC,GAAG,CAAC;MACtC,IAAIA,GAAG,IAAG,UAAU,IAAI,IAAI,CAACxC,UAAU,CAACwC,GAAG,CAAC,IAAI,IAAI,EAAE;QAClDmF,UAAU,CAACnF,GAAG,CAAC,GAAG,CAAC,CAAC;;IAE5B,CAAC,CAAC;IAEF,IAAI,CAACnD,oBAAoB,CAAC0I,YAAY,CAACJ,UAAU,EAAGvB,QAAQ,IAAI;MAC5D3F,EAAE,CAACyE,OAAO,GAAG;QACTC,OAAO,EAAEiB,QAAQ,CAACjB,OAAO;QACzB1B,KAAK,EAAE2C,QAAQ,CAAC4B;OACnB;IACL,CAAC,EAAE,IAAI,EAAE,MAAK;MACVvH,EAAE,CAACyF,oBAAoB,CAACG,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EACA/N,iBAAiBA,CAACmP,IAAI,EAACC,KAAK,EAAErH,IAAI,EAAE4F,MAAM;IACtC,IAAIxF,EAAE,GAAG,IAAI;IACT,IAAI,CAACtI,qBAAqB,GAAGsP,IAAI,EACjC,IAAI,CAACvP,mBAAmB,GAAGwP,KAAK,EAChC,IAAI,CAACrP,eAAe,GAAGgI,IAAI;IAC/B,IAAIsH,UAAU,GAAG;MACTF,IAAI;MACJhF,IAAI,EAAEiF,KAAK;MACXrH,IAAI,EAAEA,IAAI;MACVyB,OAAO,EAAErB,EAAE,CAACnH,SAAS,CAAC2O,QAAQ;KACrC;IACDxH,EAAE,CAACyF,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI,CAAC9G,oBAAoB,CAAC6I,kBAAkB,CAACP,UAAU,EAAGvB,QAAQ,IAAI;MAClE3F,EAAE,CAACxI,kBAAkB,GAAG;QACpBkN,OAAO,EAAEiB,QAAQ,CAACjB,OAAO;QACzB1B,KAAK,EAAE2C,QAAQ,CAAC4B;OACnB;MACDvH,EAAE,CAAC5G,yBAAyB,GAAG,IAAI;IACvC,CAAC,EAAE,IAAI,EAAE,MAAK;MACV4G,EAAE,CAACyF,oBAAoB,CAACG,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEA8B,cAAcA,CAAA;IACV,IAAI,CAAChI,UAAU,GAAG,CAAC;IACnB,IAAI,CAACD,MAAM,CAAC,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACL,UAAU,CAAC;EAC3E;EAEA;EACAvB,OAAOA,CAAA;IACH,IAAI2J,IAAI,GAAG,EAAE;IACb,IAAI3H,EAAE,GAAG,IAAI;IACT2H,IAAI,GAAG;MACHrG,WAAW,EAAEsG,QAAQ,CAACC,MAAM,CAAC,IAAI,CAAC1G,cAAc,CAACG,WAAW,CAAC,EAAEwG,OAAO,CAAC,IAAI,EAAC,IAAI,CAAC;KACpF;IACL,IAAI,CAACrC,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAI,CAAC7G,sBAAsB,CAACb,OAAO,CAAC2J,IAAI,EAAGI,GAAG,IAAI;MAC9C;MACA/H,EAAE,CAACvB,UAAU,EAAE;MACf;MACA;IACJ,CAAC,EAAE,IAAI,EAAE,MAAK;MACVuB,EAAE,CAACyF,oBAAoB,CAACG,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEA;EACAoC,eAAeA,CAAA;IAEXb,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxS,kBAAkB,CAACqT,QAAQ,CAAC,CAACZ,OAAO,CAACtF,GAAG,IAAG;MACxD,MAAMmG,OAAO,GAAG,IAAI,CAACtT,kBAAkB,CAACC,GAAG,CAACkN,GAAG,CAAC;MAChD,IAAImG,OAAO,CAAC9S,OAAO,EAAE;QACnB+S,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAErG,GAAG,EAAE,qBAAqB,EAAEmG,OAAO,CAACpT,MAAM,CAAC;;IAErE,CAAC,CAAC;IACJ,IAAIkL,EAAE,GAAG,IAAI;IACb,IAAI2H,IAAI,GAAG,EAAE;IACb,IAAI,IAAI,CAACxG,cAAc,CAACC,YAAY,IAAI,CAAC,EAAE;MACvCuG,IAAI,GAAG;QACHvG,YAAY,EAAE,IAAI,CAACD,cAAc,CAACC,YAAY;QAC9CC,OAAO,EAAE,IAAI,CAACzM,kBAAkB,CAACC,GAAG,CAAC,SAAS,CAAC,CAACiM,KAAK;QACrDjK,GAAG,EAAE,IAAI,CAACsK,cAAc,CAACtK,GAAG;QAC5ByK,WAAW,EAAE,IAAI,CAACH,cAAc,CAACG,WAAW;QAC5CE,OAAO,EAAE,IAAI,CAACL,cAAc,CAACK,OAAO;QACpCD,GAAG,EAAE,IAAI,CAAC3M,kBAAkB,CAACC,GAAG,CAAC,KAAK,CAAC,CAACiM;OAC3C;KACJ,MAAM;MACH6G,IAAI,GAAG;QACHvG,YAAY,EAAE,IAAI,CAACD,cAAc,CAACC,YAAY;QAC9C1K,OAAO,EAAE,IAAI,CAAC9B,kBAAkB,CAACC,GAAG,CAAC,SAAS,CAAC,CAACiM,KAAK;QACrDjK,GAAG,EAAE,IAAI,CAACsK,cAAc,CAACtK,GAAG;QAC5ByK,WAAW,EAAE,IAAI,CAACH,cAAc,CAACG,WAAW;QAC5CE,OAAO,EAAE,IAAI,CAACL,cAAc,CAACK,OAAO;QACpCD,GAAG,EAAE,IAAI,CAAC3M,kBAAkB,CAACC,GAAG,CAAC,KAAK,CAAC,CAACiM;OAC3C;;IAGLd,EAAE,CAACyB,UAAU,GAAG;MACZ/K,OAAO,EAAE,IAAI,CAAC9B,kBAAkB,CAACC,GAAG,CAAC,SAAS,CAAC,CAACiM,KAAK;MACrDO,OAAO,EAAE,IAAI,CAACzM,kBAAkB,CAACC,GAAG,CAAC,SAAS,CAAC,CAACiM,KAAK;MACrDjK,GAAG,EAAE,IAAI,CAACsK,cAAc,CAACtK,GAAG;MAC5ByK,WAAW,EAAE,IAAI,CAACH,cAAc,CAACG,WAAW;MAC5CC,GAAG,EAAE,IAAI;MACTG,WAAW,EAAE,GAAG;MAChBC,SAAS,EAAE,GAAG;MACdC,GAAG,EAAE;KACR;IACD,IAAI,CAAC6D,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAI,CAAC9G,oBAAoB,CAACuC,cAAc,CAACwG,IAAI,EAAGI,GAAG,IAAI;MACnD,IAAIA,GAAG,CAACM,KAAK,IAAE,cAAc,EAAC;QAC1BrI,EAAE,CAACyF,oBAAoB,CAAC6C,OAAO,CAACtI,EAAE,CAACpM,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,CAAC;QAC3F;QACA,IAAI,IAAI,CAACsN,cAAc,CAACC,YAAY,IAAI,CAAC,EAAE;UACvC,IAAI,CAACqE,oBAAoB,CAACC,MAAM,EAAE;UAClC1F,EAAE,CAACpB,oBAAoB,CAAC2J,OAAO,CAAC;YAAClH,OAAO,EAAErB,EAAE,CAACyB,UAAU,CAACJ;UAAO,CAAC,EAAGmH,MAAM,IAAI;YACzE,IAAGA,MAAM,CAACxP,QAAQ,KAAK,CAAC,EAAE;cACtBgH,EAAE,CAACiE,2BAA2B,CAACuE,MAAM,CAAC;;UAE9C,CAAC,EAAEH,KAAK,IAAG;YACP,IAAGA,KAAK,CAACA,KAAK,CAACA,KAAK,CAACI,SAAS,KAAK,6BAA6B,EAAC;cAC7D,IAAI,CAAChD,oBAAoB,CAAC4C,KAAK,CAAC,2CAA2C,CAAC;cAC5E,IAAI,CAAC5E,MAAM,CAACC,QAAQ,CAAC,CAAC,4BAA4B,CAAC,CAAC;;UAE5D,CAAC,EAAC,MAAI;YAAE,IAAI,CAAC+B,oBAAoB,CAACG,OAAO,EAAE;UAAC,CAAC,CAAC;SACjD,MAAM;UACH;UACA;UACA,IAAI,CAAChH,oBAAoB,CAAC0I,YAAY,CAAC;YACnC,MAAM,EAAE,CAAC;YACT,MAAM,EAAE,QAAQ;YAChB,gBAAgB,EAAE,IAAI;YACtB,kBAAkB,EAAE,CAAC;YACrB,eAAe,EAAE,CAAC;YAClB,OAAO,EAAE,IAAI,CAAC1S,kBAAkB,CAACC,GAAG,CAAC,SAAS,CAAC,CAACiM,KAAK;YACrD,YAAY,EAAE,CAAC;YACf,aAAa,EAAE,CAAC;YAChB,gBAAgB,EAAE,IAAI;YACtB,UAAU,EAAE,CAAC;WAChB,EAAG6E,QAAQ,IAAI;YACZ,IAAG3F,EAAE,CAACyE,OAAO,EAAEC,OAAO,CAACgE,IAAI,CAACC,EAAE,IAAIA,EAAE,CAAC3P,QAAQ,KAAK,CAAC,CAAC,EAAC;cACjDgH,EAAE,CAAChF,oBAAoB,GAAG,IAAI;;UAEtC,CAAC,EAAE,IAAI,EAAE,MAAK;YACVgF,EAAE,CAACyF,oBAAoB,CAACG,OAAO,EAAE;UACrC,CAAC,CAAC;;OAET,MAAM,IAAGmC,GAAG,CAACM,KAAK,KAAI,oBAAoB,EAAE;QACzCrI,EAAE,CAACyF,oBAAoB,CAAC4C,KAAK,CAAC,+BAA+B,CAAC;QAC9D;OACH,MAAM;QACHrI,EAAE,CAACyF,oBAAoB,CAAC4C,KAAK,CAACN,GAAG,CAACa,aAAa,CAAC;QAChD;;MAGJ5I,EAAE,CAACU,uBAAuB,GAAG,KAAK;MAClC;MACA,IAAI,CAAChC,QAAQ,GAAG,KAAK;MACrBsB,EAAE,CAACP,MAAM,CAACO,EAAE,CAACN,UAAU,EAAEM,EAAE,CAACL,QAAQ,EAAEK,EAAE,CAACJ,IAAI,EAAEI,EAAE,CAACT,UAAU,CAAC;IACjE,CAAC,EAAE,IAAI,EAAE,MAAK;MACVS,EAAE,CAACyF,oBAAoB,CAACG,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEArS,iBAAiBA,CAAA;IACb,IAAI,CAACmN,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAACS,cAAc,GAAG;MAClBC,YAAY,EAAC,CAAC;MACd1K,OAAO,EAAC,IAAI;MACZ2K,OAAO,EAAC,IAAI;MACZxK,GAAG,EAAC,IAAI;MACRyK,WAAW,EAAC,IAAI;MAChBC,GAAG,EAAC,IAAI;MACRC,OAAO,EAAE;KACZ;IACD,IAAI,CAAC5M,kBAAkB,GAAG,IAAI,CAACkK,WAAW,CAACmG,KAAK,CAAC,IAAI,CAAC9D,cAAc,CAAC;IACrE,IAAI,CAACvM,kBAAkB,CAACC,GAAG,CAAC,SAAS,CAAC,CAACgU,aAAa,CAAC,CACjDtW,UAAU,CAACwC,QAAQ,EACnBxC,UAAU,CAACuW,SAAS,CAAC,EAAE,CAAC,EACxBvW,UAAU,CAACyC,OAAO,CAAC,mDAAmD,CAAC,CAC1E,CAAC;IACF,IAAI,CAACJ,kBAAkB,CAACC,GAAG,CAAC,SAAS,CAAC,CAACkU,aAAa,CAAC,CACjDxW,UAAU,CAACwC,QAAQ,EACnBxC,UAAU,CAACuW,SAAS,CAAC,EAAE,CAAC,EACxBvW,UAAU,CAACyC,OAAO,CAAC,mDAAmD,CAAC,CAC1E,CAAC;IACF,IAAI,CAACJ,kBAAkB,CAACC,GAAG,CAAC,KAAK,CAAC,CAACgU,aAAa,CAAC,CAC7CtW,UAAU,CAACwC,QAAQ,EACnBxC,UAAU,CAACuW,SAAS,CAAC,EAAE,CAAC,EACxBvW,UAAU,CAACyC,OAAO,CAAC,kBAAkB,CAAC,CACzC,CAAC;IACF,IAAI,CAACJ,kBAAkB,CAACC,GAAG,CAAC,aAAa,CAAC,CAACgU,aAAa,CAAC,CACrDtW,UAAU,CAACwC,QAAQ,EACnBxC,UAAU,CAACyC,OAAO,CAAC,gBAAgB,CAAC,CACvC,CAAC;IAEF,IAAI,CAACJ,kBAAkB,CAACC,GAAG,CAAC,KAAK,CAAC,CAACgU,aAAa,CAAC,CAC7CtW,UAAU,CAACwC,QAAQ,EACnBlC,cAAc,CAAC,CAAC,CAAC,CACpB,CAAC;IAEF,IAAI,CAAC+B,kBAAkB,CAACC,GAAG,CAAC,SAAS,CAAC,CAACmU,MAAM,CAAC;MAACC,SAAS,EAAC;IAAK,CAAC,CAAC;IAChE,IAAI,CAACrU,kBAAkB,CAACC,GAAG,CAAC,SAAS,CAAC,CAACqU,OAAO,CAAC;MAAED,SAAS,EAAE;IAAK,CAAE,CAAC;IACpE,IAAI,CAACrU,kBAAkB,CAACqT,QAAQ,CAACpR,GAAG,CAACmS,MAAM,EAAE;IAC7C,IAAI,CAACpU,kBAAkB,CAACqT,QAAQ,CAAC3G,WAAW,CAAC0H,MAAM,EAAE;EACzD;EAEAG,YAAYA,CAAA;IACR,IAAG,IAAI,CAACvU,kBAAkB,CAACC,GAAG,CAAC,cAAc,CAAC,CAACiM,KAAK,IAAI,CAAC,EAAC;MACtD,IAAI,CAAClM,kBAAkB,CAACC,GAAG,CAAC,SAAS,CAAC,CAACmU,MAAM,CAAC;QAACC,SAAS,EAAC;MAAK,CAAC,CAAC;MAChE,IAAI,CAACrU,kBAAkB,CAACC,GAAG,CAAC,SAAS,CAAC,CAACqU,OAAO,CAAC;QAAED,SAAS,EAAE;MAAK,CAAE,CAAC;MACpE,OAAO,EAAE,IAAI,CAACrU,kBAAkB,CAACqT,QAAQ,CAAC5G,OAAO,CAAC+H,KAAK,IAChD,IAAI,CAACxU,kBAAkB,CAACqT,QAAQ,CAACpR,GAAG,CAACuS,KAAK,IAC1C,IAAI,CAACxU,kBAAkB,CAACqT,QAAQ,CAAC3G,WAAW,CAAC8H,KAAK,CAAC;KAC7D,MAAI;MACD,IAAI,CAACxU,kBAAkB,CAACC,GAAG,CAAC,SAAS,CAAC,CAACmU,MAAM,CAAC;QAACC,SAAS,EAAC;MAAK,CAAC,CAAC;MAChE,IAAI,CAACrU,kBAAkB,CAACC,GAAG,CAAC,SAAS,CAAC,CAACqU,OAAO,CAAC;QAAED,SAAS,EAAE;MAAK,CAAE,CAAC;MACpE,OAAO,EAAE,IAAI,CAACrU,kBAAkB,CAACqT,QAAQ,CAACvR,OAAO,CAAC0S,KAAK,IAChD,IAAI,CAACxU,kBAAkB,CAACqT,QAAQ,CAACpR,GAAG,CAACuS,KAAK,IAC1C,IAAI,CAACxU,kBAAkB,CAACqT,QAAQ,CAAC3G,WAAW,CAAC8H,KAAK,CAAC;;IAE9D,OAAO,IAAI,CAACxU,kBAAkB,CAACQ,OAAO;EAC1C;EAEAuN,eAAeA,CAAA;IACX,IAAI3C,EAAE,GAAG,IAAI;IACbA,EAAE,CAACyF,oBAAoB,CAACC,MAAM,EAAE;IAEhC1F,EAAE,CAACpB,oBAAoB,CAAC2J,OAAO,CAAC;MAAClH,OAAO,EAACrB,EAAE,CAACnH;IAAS,CAAC,EAAGkP,GAAG,IAAI;MAC5D/H,EAAE,CAACvJ,YAAY,GAAGsR,GAAG;MACrB,IAAIsB,SAAS,GAAGrJ,EAAE,CAACsJ,WAAW,CAACvB,GAAG,CAACsB,SAAS,CAAC;MAC7C,IAAIE,OAAO,GAAGvJ,EAAE,CAACsJ,WAAW,CAACvB,GAAG,CAACwB,OAAO,CAAC;MACzCvJ,EAAE,CAACvJ,YAAY,CAAC0C,SAAS,GAAG,CAACoQ,OAAO,GAACF,SAAS,KAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,GAAE,QAAQ,GAAE,GAAG,GAACrJ,EAAE,CAACwJ,mBAAmB,CAACzB,GAAG,CAACsB,SAAS,CAAC,GAAC,GAAG,GAACrJ,EAAE,CAACwJ,mBAAmB,CAACzB,GAAG,CAACwB,OAAO,CAAC,GAAC,GAAG;MAC/JvJ,EAAE,CAACyJ,UAAU,GAAG1B,GAAG,CAAC2B,UAAU;MAC9B1J,EAAE,CAACZ,OAAO,GAAG,IAAI;MACjBY,EAAE,CAAC7H,iBAAiB,GAAG,IAAI;MAC3B6H,EAAE,CAACnI,iBAAiB,CAAC,CAAC,EAAE,EAAE,EAAEmI,EAAE,CAACpI,eAAe,EAAE;QAACyJ,OAAO,EAAErB,EAAE,CAACnH;MAAS,CAAC,CAAC;IAC5E,CAAC,EAAEwP,KAAK,IAAG;MACP,IAAGA,KAAK,CAACA,KAAK,CAACA,KAAK,CAACI,SAAS,KAAK,6BAA6B,EAAC;QAC7D,IAAI,CAAChD,oBAAoB,CAAC4C,KAAK,CAAC,2CAA2C,CAAC;QAC5E,IAAI,CAAC5E,MAAM,CAACC,QAAQ,CAAC,CAAC,4BAA4B,CAAC,CAAC;;IAE5D,CAAC,EAAC,MAAI;MAAE,IAAI,CAAC+B,oBAAoB,CAACG,OAAO,EAAE;IAAC,CAAC,CAAC;EAClD;EAEAjB,gBAAgBA,CAACgF,UAAkB,EAAEC,OAAgB;IACjD,IAAI5J,EAAE,GAAG,IAAI;IACb,IAAI6J,IAAI,GAAG,IAAIC,IAAI,CAACH,UAAU,CAAC;IAE/B,IAAIC,OAAO,EAAE;MACTC,IAAI,CAACE,OAAO,CAACF,IAAI,CAACG,OAAO,EAAE,GAAGJ,OAAO,CAAC;;IAG1C,MAAMvL,GAAG,GAAGwL,IAAI,CAACG,OAAO,EAAE,CAACxC,QAAQ,EAAE,CAACyC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACxD,MAAM3L,KAAK,GAAG,CAACuL,IAAI,CAACK,QAAQ,EAAE,GAAG,CAAC,EAAE1C,QAAQ,EAAE,CAACyC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACjE,MAAM1L,IAAI,GAAGsL,IAAI,CAACM,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGP,IAAI,CAACQ,QAAQ,EAAE,CAAC7C,QAAQ,EAAE,CAACyC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACzD,MAAMK,OAAO,GAAGT,IAAI,CAACU,UAAU,EAAE,CAAC/C,QAAQ,EAAE,CAACyC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC7D,MAAMO,OAAO,GAAGX,IAAI,CAACY,UAAU,EAAE,CAACjD,QAAQ,EAAE,CAACyC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAE7D,OAAO,GAAG5L,GAAG,IAAIC,KAAK,IAAIC,IAAI,IAAI6L,KAAK,IAAIE,OAAO,IAAIE,OAAO,EAAE;EACnE;EAEAE,aAAaA,CAACf,UAAU;IACpB;IACA,IAAI,CAACgB,QAAQ,EAAEC,QAAQ,CAAC,GAAGjB,UAAU,CAACnL,KAAK,CAAC,GAAG,CAAC;IAEhD;IACA,IAAIqM,SAAS,GAAGF,QAAQ,CAACnM,KAAK,CAAC,GAAG,CAAC;IACnC,IAAIH,GAAG,GAAGuJ,QAAQ,CAACiD,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACpC,IAAIvM,KAAK,GAAGsJ,QAAQ,CAACiD,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC5C,IAAItM,IAAI,GAAGqJ,QAAQ,CAACiD,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAErC;IACA,IAAIC,SAAS,GAAGF,QAAQ,CAACpM,KAAK,CAAC,GAAG,CAAC;IACnC,IAAI4L,KAAK,GAAGxC,QAAQ,CAACkD,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACtC,IAAIR,OAAO,GAAG1C,QAAQ,CAACkD,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACxC,IAAIN,OAAO,GAAG5C,QAAQ,CAACkD,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAExC;IACA,OAAO,IAAIhB,IAAI,CAACvL,IAAI,EAAED,KAAK,EAAED,GAAG,EAAE+L,KAAK,EAAEE,OAAO,EAAEE,OAAO,CAAC;EAC9D;EAEAlB,WAAWA,CAACK,UAAkB;IAC1B,MAAME,IAAI,GAAG,IAAIC,IAAI,CAACH,UAAU,CAAC;IACjC,OAAOE,IAAI,CAACkB,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;EAClC;;EAEAvB,mBAAmBA,CAACG,UAAkB,EAAEC,OAAgB;IACpD,IAAIC,IAAI,GAAG,IAAIC,IAAI,CAACH,UAAU,CAAC;IAE/B,IAAIC,OAAO,EAAE;MACTC,IAAI,CAACmB,UAAU,CAACnB,IAAI,CAACoB,UAAU,EAAE,GAAGrB,OAAO,CAAC;;IAGhD,MAAMvL,GAAG,GAAGwL,IAAI,CAACoB,UAAU,EAAE,CAACzD,QAAQ,EAAE,CAACyC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3D,MAAM3L,KAAK,GAAG,CAACuL,IAAI,CAACqB,WAAW,EAAE,GAAG,CAAC,EAAE1D,QAAQ,EAAE,CAACyC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACpE,MAAM1L,IAAI,GAAGsL,IAAI,CAACsB,cAAc,EAAE;IAClC,MAAMf,KAAK,GAAGP,IAAI,CAACuB,WAAW,EAAE,CAAC5D,QAAQ,EAAE,CAACyC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC5D,MAAMK,OAAO,GAAGT,IAAI,CAACwB,aAAa,EAAE,CAAC7D,QAAQ,EAAE,CAACyC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAChE,MAAMO,OAAO,GAAGX,IAAI,CAACyB,aAAa,EAAE,CAAC9D,QAAQ,EAAE,CAACyC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAEhE,OAAO,GAAG5L,GAAG,IAAIC,KAAK,IAAIC,IAAI,IAAI6L,KAAK,IAAIE,OAAO,IAAIE,OAAO,EAAE;EACnE;EAEAe,SAASA,CAAA;IACL,IAAI,CAAC3W,kBAAkB,CAACqT,QAAQ,CAAC1G,GAAG,CAACiK,KAAK,EAAE;EAChD;EAEAC,WAAWA,CAAA;IACP5N,aAAa,CAAC,IAAI,CAACD,QAAQ,CAAC;EAChC;EAEAqG,2BAA2BA,CAACxB,IAAI;IAC5B,IAAIzC,EAAE,GAAG,IAAI;IACbA,EAAE,CAACxG,qBAAqB,GAAG,IAAI;IAC/BwG,EAAE,CAACyB,UAAU,GAAG;MACZ/K,OAAO,EAAE+L,IAAI,CAAC/L,OAAO;MACrB2K,OAAO,EAAEoB,IAAI,CAACpB,OAAO;MACrBxK,GAAG,EAAE4L,IAAI,CAAC5L,GAAG;MACbyK,WAAW,EAAEmB,IAAI,CAAC7L,WAAW;MAC7B2K,GAAG,EAAE,IAAI;MACTG,WAAW,EAAE,GAAG;MAChBC,SAAS,EAAE,GAAG;MACdC,GAAG,EAAE;KACR;EACL;EAEA0C,+BAA+BA,CAAC7B,IAAI;IAChC,IAAIzC,EAAE,GAAG,IAAI;IACb,IAAI,CAAClD,qBAAqB,GAAG,IAAI;IACjCkD,EAAE,CAACyB,UAAU,GAAG;MACZ/K,OAAO,EAAE+L,IAAI,CAAC/L,OAAO;MACrB2K,OAAO,EAAEoB,IAAI,CAACzJ,QAAQ,IAAIrG,SAAS,CAACuR,iBAAiB,CAACK,QAAQ,GAAG9B,IAAI,CAACpB,OAAO,GAAG,IAAI;MACpFxK,GAAG,EAAE4L,IAAI,CAAC5L,GAAG;MACbyK,WAAW,EAAEmB,IAAI,CAAC7L,WAAW;MAC7B2K,GAAG,EAAE,IAAI;MACTG,WAAW,EAAE,GAAG;MAChBC,SAAS,EAAE,GAAG;MACdC,GAAG,EAAE;KACR;EACL;EAEA4C,+BAA+BA,CAAC/B,IAAI;IAChC,IAAIzC,EAAE,GAAG,IAAI;IACb,IAAI,CAACjE,qBAAqB,GAAG,IAAI;IACjCiE,EAAE,CAACyB,UAAU,GAAG;MACZ/K,OAAO,EAAE+L,IAAI,CAAC/L,OAAO;MACrB2K,OAAO,EAAEoB,IAAI,CAACzJ,QAAQ,IAAIrG,SAAS,CAACuR,iBAAiB,CAACK,QAAQ,GAAG9B,IAAI,CAACpB,OAAO,GAAG,IAAI;MACpFxK,GAAG,EAAE4L,IAAI,CAAC5L,GAAG;MACbyK,WAAW,EAAEmB,IAAI,CAAC7L,WAAW;MAC7B2K,GAAG,EAAE,IAAI;MACTG,WAAW,EAAE,GAAG;MAChBC,SAAS,EAAE,GAAG;MACdC,GAAG,EAAE;KACR;EACL;EAEAjI,qBAAqBA,CAAA;IACjB,IAAIqG,EAAE,GAAG,IAAI;IACbA,EAAE,CAACxG,qBAAqB,GAAG,KAAK;IAChCwG,EAAE,CAAC/F,aAAa,GAAG,CAAC;EACxB;EACAkB,oBAAoBA,CAAA;IAChB,IAAI6E,EAAE,GAAG,IAAI;IACbA,EAAE,CAAChF,oBAAoB,GAAG,KAAK;IAC/BgF,EAAE,CAAC/F,aAAa,GAAG,CAAC;EACxB;EAEAO,QAAQA,CAAA;IACJ,IAAIwF,EAAE,GAAG,IAAI;IACb;IACA;IAEA,IAAIA,EAAE,CAACyB,UAAU,CAACG,GAAG,IAAI,IAAI,EAAE;MAAE;MAC7B,IAAI5B,EAAE,CAACmB,cAAc,CAACC,YAAY,KAAK,CAAC,IAAIpB,EAAE,CAAC/F,aAAa,IAAI,IAAI,EAAE;QAClE+F,EAAE,CAACxG,qBAAqB,GAAG,KAAK;QAChCwG,EAAE,CAAChF,oBAAoB,GAAG,KAAK;QAC/BgF,EAAE,CAACsF,qBAAqB,GAAG,IAAI;QAC/BtF,EAAE,CAACpL,kBAAkB,CAAC4W,KAAK,EAAE;QAC7BxL,EAAE,CAAC9B,eAAe,EAAE;OACvB,MAAM,IAAI8B,EAAE,CAACmB,cAAc,CAACC,YAAY,KAAK,CAAC,EAAE;QAC7CpB,EAAE,CAACxG,qBAAqB,GAAG,KAAK;QAChCwG,EAAE,CAAChF,oBAAoB,GAAG,KAAK;QAC/BgF,EAAE,CAACsF,qBAAqB,GAAG,IAAI;QAC/BtF,EAAE,CAACpL,kBAAkB,CAAC4W,KAAK,EAAE;QAC7BxL,EAAE,CAAC9B,eAAe,EAAE;OACvB,MAAM;QACH8B,EAAE,CAACyF,oBAAoB,CAAC4C,KAAK,CAACrI,EAAE,CAACpM,WAAW,CAACC,SAAS,CAAC,wCAAwC,CAAC,CAAC;;KAExG,MAAM,IAAImM,EAAE,CAACyB,UAAU,CAACG,GAAG,IAAI,KAAK,EAAE;MAAE;MACrC,IAAG5B,EAAE,CAAC/F,aAAa,IAAI,IAAI,EAAE;QACzB+F,EAAE,CAACxG,qBAAqB,GAAG,KAAK;QAChCwG,EAAE,CAAChF,oBAAoB,GAAG,KAAK;QAC/BgF,EAAE,CAACsF,qBAAqB,GAAG,IAAI;QAC/BtF,EAAE,CAAC9B,eAAe,EAAE;OACvB,MAAK;QACF8B,EAAE,CAACyF,oBAAoB,CAAC4C,KAAK,CAACrI,EAAE,CAACpM,WAAW,CAACC,SAAS,CAAC,wCAAwC,CAAC,CAAC;;;EAG7G;EAEA6X,MAAMA,CAAA,GACN;EAEAC,qBAAqBA,CAAA;IACjB,IAAI3L,EAAE,GAAG,IAAI;IACbA,EAAE,CAACsF,qBAAqB,GAAG,KAAK;EACpC;EAEA;EACApH,eAAeA,CAAA;IACX,IAAIyJ,IAAI,GAAG,EAAE;IACb,IAAI3H,EAAE,GAAG,IAAI;IACb2H,IAAI,GAAG;MACHrG,WAAW,EAAEsG,QAAQ,CAACC,MAAM,CAAC,IAAI,CAACpG,UAAU,CAACH,WAAW,CAAC,EAAEwG,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;KACjF;IACD;IACA,IAAI,CAACrC,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAI,CAAC7G,sBAAsB,CAACb,OAAO,CAAC2J,IAAI,EAAGI,GAAG,IAAI;MAC9C/H,EAAE,CAAC4L,eAAe,EAAE;IACxB,CAAC,EAAE,IAAI,EAAE,MAAK;MACV5L,EAAE,CAACyF,oBAAoB,CAACG,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAgG,eAAeA,CAAA;IACX,IAAI,CAACrV,SAAS,GAAG,EAAE;IACnB,IAAI,CAACoH,cAAc,EAAE;EACzB;EAEAkO,UAAUA,CAAA;IACN,IAAI7L,EAAE,GAAG,IAAI;IACb,IAAI2H,IAAI,GAAG;MACPjR,OAAO,EAAEsJ,EAAE,CAACyB,UAAU,CAAC/K,OAAO;MAC9B2K,OAAO,EAAGrB,EAAE,CAAC/F,aAAa,IAAI+F,EAAE,CAAC/F,aAAa,IAAI,CAAC,GAAG+F,EAAE,CAACyB,UAAU,CAACJ,OAAO,GAAG,IAAI;MAClFxK,GAAG,EAAEmJ,EAAE,CAACyB,UAAU,CAAC5K,GAAG;MACtByK,WAAW,EAAEtB,EAAE,CAACyB,UAAU,CAACH,WAAW;MACtCC,GAAG,EAAE,IAAI,CAAC2D,cAAc,CAACrQ,GAAG,CAAC,KAAK,CAAC,CAACiM,KAAK;MACzCY,WAAW,EAAE,GAAG;MAChBC,SAAS,EAAE;KACd;IACD,IAAI,CAAC8D,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAI,CAAC9G,oBAAoB,CAACkN,kBAAkB,CAACnE,IAAI,EAAGI,GAAG,IAAI;MACvD,IAAIA,GAAG,IAAIA,GAAG,CAACM,KAAK,EAAE;QAClBrI,EAAE,CAACyF,oBAAoB,CAAC4C,KAAK,CAACN,GAAG,CAACgE,OAAO,CAAC;OAC7C,MAAM;QACH/L,EAAE,CAACyF,oBAAoB,CAAC6C,OAAO,CAACtI,EAAE,CAACpM,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC,CAAC;;MAEjGmM,EAAE,CAACsF,qBAAqB,GAAG,KAAK;MAChCtF,EAAE,CAACkF,cAAc,CAACsG,KAAK,EAAE;MACzBxL,EAAE,CAAC0H,cAAc,EAAE;IACvB,CAAC,EAAE,IAAI,EAAE,MAAK;MACV1H,EAAE,CAACyF,oBAAoB,CAACG,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAnJ,QAAQA,CAAA;IACJ,IAAIuD,EAAE,GAAG,IAAI;IACb,IAAI2H,IAAI,GAAG;MACPjR,OAAO,EAAEsJ,EAAE,CAACyB,UAAU,CAAC/K,OAAO;MAC9B2K,OAAO,EAAErB,EAAE,CAACyB,UAAU,CAACJ,OAAO;MAC9BxK,GAAG,EAAEmJ,EAAE,CAACyB,UAAU,CAAC5K,GAAG;MACtByK,WAAW,EAAEtB,EAAE,CAACyB,UAAU,CAACH,WAAW;MACtCC,GAAG,EAAEvB,EAAE,CAACyB,UAAU,CAACF,GAAG;MACtBG,WAAW,EAAE,GAAG;MAChBC,SAAS,EAAE;KACd;IACD,IAAI,CAAC8D,oBAAoB,CAACC,MAAM,EAAE;IAClC,IAAI,CAAC9G,oBAAoB,CAACoN,oBAAoB,CAACrE,IAAI,EAAGI,GAAG,IAAI;MACzD/H,EAAE,CAACyF,oBAAoB,CAAC6C,OAAO,CAACtI,EAAE,CAACpM,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC,CAAC;MAC3FmM,EAAE,CAACjE,qBAAqB,GAAG,KAAK;MAChCiE,EAAE,CAAClD,qBAAqB,GAAG,KAAK;MAChCkD,EAAE,CAAC0H,cAAc,EAAE;IACvB,CAAC,EAAE,IAAI,EAAE,MAAK;MACV1H,EAAE,CAACyF,oBAAoB,CAACG,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EACA7M,uBAAuBA,CAAC+H,KAAK;IACzB,IAAId,EAAE,GAAG,IAAI;IACb,IAAIc,KAAK,IAAInO,SAAS,CAACuR,iBAAiB,CAACG,QAAQ,EAAE;MAC/C,OAAOrE,EAAE,CAACpM,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;KACtE,MAAM,IAAIiN,KAAK,IAAInO,SAAS,CAACuR,iBAAiB,CAACK,QAAQ,EAAE;MACtD,OAAOvE,EAAE,CAACpM,WAAW,CAACC,SAAS,CAAC,kCAAkC,CAAC;KACtE,MAAM;MACH,OAAOmM,EAAE,CAACpM,WAAW,CAACC,SAAS,CAAC,+BAA+B,CAAC;;EAExE;EACA;EACA;EACA;EACAiG,oBAAoBA,CAAA;IAChBmS,MAAM,CAACC,IAAI,CAAC,wBAAwB,EAAE,QAAQ,CAAC;EACnD;EACA9P,sBAAsBA,CAAA;IAClB6P,MAAM,CAACC,IAAI,CAAC,qBAAqB,EAAE,QAAQ,CAAC;EAChD;EAEAxJ,iBAAiBA,CAAA;IACb,IAAI1C,EAAE,GAAG,IAAI;IACbA,EAAE,CAACvJ,YAAY,GAAE,EAChB;IACDuJ,EAAE,CAACxI,kBAAkB,GAAG;MACpBkN,OAAO,EAAE,EAAE;MACX1B,KAAK,EAAE;KACV;IACDhD,EAAE,CAAC5G,yBAAyB,GAAG,KAAK;EACxC;EAEApC,YAAYA,CAAC8J,KAAa;IACtB,OAAO,IAAIqL,IAAI,CAACC,YAAY,CAAC,OAAO,CAAC,CAACC,MAAM,CAACvL,KAAK,CAAC;EACvD;;;uBAvjCSpD,qBAAqB,EAAA5K,EAAA,CAAAwZ,iBAAA,CAuIlB7Z,oBAAoB,GAAAK,EAAA,CAAAwZ,iBAAA,CACpB1Z,sBAAsB,GAAAE,EAAA,CAAAwZ,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1Z,EAAA,CAAAwZ,iBAAA,CAAAxZ,EAAA,CAAA2Z,QAAA,GAAA3Z,EAAA,CAAAwZ,iBAAA,CAAAI,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAxIzBjP,qBAAqB;MAAAkP,SAAA;MAAAC,QAAA,GAAA/Z,EAAA,CAAAga,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCrBlCta,EAAA,CAAAC,cAAA,aAAqG;UAEzDD,EAAA,CAAAgB,MAAA,GAAmD;UAAAhB,EAAA,CAAAU,YAAA,EAAM;UAC7FV,EAAA,CAAAiB,SAAA,sBAAoF;UACxFjB,EAAA,CAAAU,YAAA,EAAM;UAENV,EAAA,CAAAyB,UAAA,IAAA+Y,oCAAA,iBAOM;UACVxa,EAAA,CAAAU,YAAA,EAAM;UAGNV,EAAA,CAAAC,cAAA,gCAIC;UADAD,EAAA,CAAAE,UAAA,0BAAAua,8EAAAvV,MAAA;YAAA,OAAgBqV,GAAA,CAAAhO,iBAAA,CAAArH,MAAA,CAAyB;UAAA,EAAC;UAC1ClF,EAAA,CAAAU,YAAA,EAAyB;UAG1BV,EAAA,CAAAiB,SAAA,oBAYc;UAEdjB,EAAA,CAAAC,cAAA,aAA2D;UACqBD,EAAA,CAAAE,UAAA,2BAAAwa,iEAAAxV,MAAA;YAAA,OAAAqV,GAAA,CAAA3M,uBAAA,GAAA1I,MAAA;UAAA,EAAqC;UAC7GlF,EAAA,CAAAC,cAAA,eAAmF;UAA/BD,EAAA,CAAAE,UAAA,sBAAAya,yDAAA;YAAA,OAAYJ,GAAA,CAAArF,eAAA,EAAiB;UAAA,EAAC;UAC9ElV,EAAA,CAAAC,cAAA,eAAoE;UAC3DD,EAAA,CAAAgB,MAAA,IAAwD;UAAAhB,EAAA,CAAAU,YAAA,EAAM;UAEnEV,EAAA,CAAAC,cAAA,eAA6E;UAIrED,EAAA,CAAAE,UAAA,2BAAA0a,uEAAA1V,MAAA;YAAA,OAAAqV,GAAA,CAAAlM,cAAA,CAAAC,YAAA,GAAApJ,MAAA;UAAA,EAAyC;UAI7ClF,EAAA,CAAAU,YAAA,EAAgB;UAChBV,EAAA,CAAAC,cAAA,yBAMmC;UAD/BD,EAAA,CAAAE,UAAA,2BAAA2a,uEAAA3V,MAAA;YAAA,OAAAqV,GAAA,CAAAlM,cAAA,CAAAC,YAAA,GAAApJ,MAAA;UAAA,EAAyC;UAE7ClF,EAAA,CAAAU,YAAA,EAAgB;UAGpBV,EAAA,CAAAyB,UAAA,KAAAqZ,qCAAA,kBASM;UACN9a,EAAA,CAAAyB,UAAA,KAAAsZ,qCAAA,kBAOM;UAEN/a,EAAA,CAAAyB,UAAA,KAAAuZ,qCAAA,kBASM;UACNhb,EAAA,CAAAyB,UAAA,KAAAwZ,qCAAA,kBAOM;UAENjb,EAAA,CAAAC,cAAA,eAA+B;UACgCD,EAAA,CAAAgB,MAAA,IAA+C;UAAAhB,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAgB,MAAA,SAAC;UAAAhB,EAAA,CAAAU,YAAA,EAAO;UAC7IV,EAAA,CAAAC,cAAA,eAAiB;UAGND,EAAA,CAAAE,UAAA,2BAAAgb,+DAAAhW,MAAA;YAAA,OAAAqV,GAAA,CAAAlM,cAAA,CAAAtK,GAAA,GAAAmB,MAAA;UAAA,EAAgC;UAFvClF,EAAA,CAAAU,YAAA,EAKE;UAIVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAAiB,SAAA,iBAAwE;UACxEjB,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAyB,UAAA,KAAA0Z,uCAAA,oBAA0L;UAC1Lnb,EAAA,CAAAyB,UAAA,KAAA2Z,uCAAA,oBAA4J;UAC5Jpb,EAAA,CAAAyB,UAAA,KAAA4Z,uCAAA,oBAAsJ;UAC1Jrb,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAC,cAAA,eAA+B;UACkCD,EAAA,CAAAgB,MAAA,IAAiD;UAAAhB,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAgB,MAAA,SAAC;UAAAhB,EAAA,CAAAU,YAAA,EAAO;UACjJV,EAAA,CAAAC,cAAA,eAAiB;UAGND,EAAA,CAAAE,UAAA,2BAAAob,+DAAApW,MAAA;YAAA,OAAAqV,GAAA,CAAAlM,cAAA,CAAAG,WAAA,GAAAtJ,MAAA;UAAA,EAAwC;UAF/ClF,EAAA,CAAAU,YAAA,EAKE;UAIVV,EAAA,CAAAC,cAAA,eAAgD;UAC5CD,EAAA,CAAAiB,SAAA,iBAAqE;UACrEjB,EAAA,CAAAC,cAAA,eAA8B;UAEtBD,EAAA,CAAAyB,UAAA,KAAA8Z,uCAAA,oBAA0M;UAC9Mvb,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAC,cAAA,eAAiB;UACbD,EAAA,CAAAyB,UAAA,KAAA+Z,uCAAA,oBAA4J;UAChKxb,EAAA,CAAAU,YAAA,EAAM;UAKlBV,EAAA,CAAAC,cAAA,eAA0E;UACiCD,EAAA,CAAAE,UAAA,mBAAAub,0DAAA;YAAA,OAAAlB,GAAA,CAAA3M,uBAAA,GAAmC,KAAK;UAAA,EAAC;UAAC5N,EAAA,CAAAU,YAAA,EAAW;UAC5JV,EAAA,CAAAC,cAAA,oBAA2J;UAAtBD,EAAA,CAAAE,UAAA,qBAAAwb,4DAAA;YAAA,OAAWnB,GAAA,CAAArP,OAAA,EAAS;UAAA,EAAC;UAAClL,EAAA,CAAAU,YAAA,EAAW;UAE1KV,EAAA,CAAAC,cAAA,oBAAsM;UAAjID,EAAA,CAAAE,UAAA,2BAAAyb,kEAAAzW,MAAA;YAAA,OAAAqV,GAAA,CAAA3O,QAAA,GAAA1G,MAAA;UAAA,EAAsB,oBAAA0W,2DAAA;YAAA,OAA0ErB,GAAA,CAAA9B,SAAA,EAAW;UAAA,EAArF;UACvFzY,EAAA,CAAAC,cAAA,eAA2C;UACvCD,EAAA,CAAAiB,SAAA,sBAAoG;UACpGjB,EAAA,CAAAC,cAAA,kBAAwM;UAAvBD,EAAA,CAAAE,UAAA,mBAAA2b,wDAAA;YAAA,OAAStB,GAAA,CAAAtP,UAAA,EAAY;UAAA,EAAC;UAACjL,EAAA,CAAAgB,MAAA,IAA6D;UAAAhB,EAAA,CAAAyB,UAAA,KAAAqa,qCAAA,kBAAiJ;UAAA9b,EAAA,CAAAU,YAAA,EAAS;UAC/ZV,EAAA,CAAAC,cAAA,eAAoB;UACyED,EAAA,CAAAgB,MAAA,IAA+C;UAAAhB,EAAA,CAAAU,YAAA,EAAS;UACjJV,EAAA,CAAAC,cAAA,kBAA+F;UAA3BD,EAAA,CAAAE,UAAA,mBAAA6b,wDAAA;YAAA,OAAAxB,GAAA,CAAA3O,QAAA,GAAoB,KAAK;UAAA,EAAC;UAAC5L,EAAA,CAAAgB,MAAA,IAAiD;UAAAhB,EAAA,CAAAU,YAAA,EAAS;UASjLV,EAAA,CAAAC,cAAA,eAAqD;UACjDD,EAAA,CAAAyB,UAAA,KAAAua,0CAAA,yBAkHW;UAEXhc,EAAA,CAAAyB,UAAA,KAAAwa,0CAAA,yBAoDW;UAGXjc,EAAA,CAAAyB,UAAA,KAAAya,0CAAA,yBAwBW;UAGXlc,EAAA,CAAAC,cAAA,oBAE8B;UAFuCD,EAAA,CAAAE,UAAA,2BAAAic,kEAAAjX,MAAA;YAAA,OAAAqV,GAAA,CAAA/H,qBAAA,GAAAtN,MAAA;UAAA,EAAmC,oBAAAkX,2DAAA;YAAA,OACrB7B,GAAA,CAAA1B,qBAAA,EAAuB;UAAA,EADF;UAGpG7Y,EAAA,CAAAC,cAAA,gBAA6D;UAA1BD,EAAA,CAAAE,UAAA,sBAAAmc,yDAAA;YAAA,OAAY9B,GAAA,CAAAxB,UAAA,EAAY;UAAA,EAAC;UACxD/Y,EAAA,CAAAC,cAAA,eAA2C;UACvCD,EAAA,CAAAiB,SAAA,sBAAoG;UACpGjB,EAAA,CAAAC,cAAA,kBAG0C;UAAlCD,EAAA,CAAAE,UAAA,mBAAAoc,wDAAA;YAAA,OAAS/B,GAAA,CAAApP,qBAAA,EAAuB;UAAA,EAAC;UAACnL,EAAA,CAAAgB,MAAA,IAA+D;UAAAhB,EAAA,CAAAyB,UAAA,KAAA8a,qCAAA,kBAEnG;UACNvc,EAAA,CAAAU,YAAA,EAAS;UACTV,EAAA,CAAAC,cAAA,eAAoB;UAEAD,EAAA,CAAAgB,MAAA,IAChB;UAAAhB,EAAA,CAAAU,YAAA,EAAS;UACTV,EAAA,CAAAC,cAAA,kBACgD;UAAxCD,EAAA,CAAAE,UAAA,mBAAAsc,wDAAA;YAAA,OAAAjC,GAAA,CAAA/H,qBAAA,GAAiC,KAAK;UAAA,EAAC;UAACxS,EAAA,CAAAgB,MAAA,IAChD;UAAAhB,EAAA,CAAAU,YAAA,EAAS;UAOzBV,EAAA,CAAAyB,UAAA,KAAAgb,0CAAA,yBAwBW;UAEXzc,EAAA,CAAAyB,UAAA,KAAAib,0CAAA,yBA2BW;UACf1c,EAAA,CAAAU,YAAA,EAAM;;;UA3bsCV,EAAA,CAAAW,SAAA,GAAmD;UAAnDX,EAAA,CAAAkB,iBAAA,CAAAqZ,GAAA,CAAAzZ,WAAA,CAAAC,SAAA,2BAAmD;UAChDf,EAAA,CAAAW,SAAA,GAAe;UAAfX,EAAA,CAAAY,UAAA,UAAA2Z,GAAA,CAAApN,KAAA,CAAe,SAAAoN,GAAA,CAAAlN,IAAA;UAGpDrN,EAAA,CAAAW,SAAA,GAA0D;UAA1DX,EAAA,CAAAY,UAAA,SAAA2Z,GAAA,CAAAxJ,WAAA,CAAA/Q,EAAA,CAAA0K,eAAA,KAAAiS,GAAA,EAAApC,GAAA,CAAAnO,cAAA,CAAA4E,QAAA,CAAA4L,aAAA,GAA0D;UAYnE5c,EAAA,CAAAW,SAAA,GAAyB;UAAzBX,EAAA,CAAAY,UAAA,eAAA2Z,GAAA,CAAAhH,UAAA,CAAyB,eAAAgH,GAAA,CAAA/G,UAAA;UAOtBxT,EAAA,CAAAW,SAAA,GAAgC;UAAhCX,EAAA,CAAAY,UAAA,iCAAgC,6BAAA2Z,GAAA,CAAAxL,OAAA,aAAAwL,GAAA,CAAA5I,OAAA,aAAA4I,GAAA,CAAApK,WAAA,gBAAAoK,GAAA,CAAA3N,UAAA,cAAA2N,GAAA,CAAA5N,MAAA,CAAA3H,IAAA,CAAAuV,GAAA,eAAAA,GAAA,CAAA1N,QAAA,UAAA0N,GAAA,CAAAzN,IAAA,YAAAyN,GAAA,CAAA9N,UAAA,gBAAA8N,GAAA,CAAAzZ,WAAA,CAAAC,SAAA;UAciGf,EAAA,CAAAW,SAAA,GAA4B;UAA5BX,EAAA,CAAA4F,UAAA,CAAA5F,EAAA,CAAAuB,eAAA,KAAAsb,GAAA,EAA4B;UAAnJ7c,EAAA,CAAAY,UAAA,WAAA2Z,GAAA,CAAAzZ,WAAA,CAAAC,SAAA,kCAAiE,YAAAwZ,GAAA,CAAA3M,uBAAA;UACpD5N,EAAA,CAAAW,SAAA,GAAgC;UAAhCX,EAAA,CAAAY,UAAA,cAAA2Z,GAAA,CAAAzY,kBAAA,CAAgC;UAEtC9B,EAAA,CAAAW,SAAA,GAAwD;UAAxDX,EAAA,CAAAkB,iBAAA,CAAAqZ,GAAA,CAAAzZ,WAAA,CAAAC,SAAA,gCAAwD;UAIrDf,EAAA,CAAAW,SAAA,GAAyD;UAAzDX,EAAA,CAAAY,UAAA,UAAA2Z,GAAA,CAAAzZ,WAAA,CAAAC,SAAA,2BAAyD,wBAAAwZ,GAAA,CAAAlM,cAAA,CAAAC,YAAA;UAQzDtO,EAAA,CAAAW,SAAA,GAAyD;UAAzDX,EAAA,CAAAY,UAAA,UAAA2Z,GAAA,CAAAzZ,WAAA,CAAAC,SAAA,2BAAyD,wBAAAwZ,GAAA,CAAAlM,cAAA,CAAAC,YAAA;UAS3DtO,EAAA,CAAAW,SAAA,GAAsC;UAAtCX,EAAA,CAAAY,UAAA,SAAA2Z,GAAA,CAAAlM,cAAA,CAAAC,YAAA,MAAsC;UAUtCtO,EAAA,CAAAW,SAAA,GAAsC;UAAtCX,EAAA,CAAAY,UAAA,SAAA2Z,GAAA,CAAAlM,cAAA,CAAAC,YAAA,MAAsC;UAStCtO,EAAA,CAAAW,SAAA,GAAsC;UAAtCX,EAAA,CAAAY,UAAA,SAAA2Z,GAAA,CAAAlM,cAAA,CAAAC,YAAA,MAAsC;UAUtCtO,EAAA,CAAAW,SAAA,GAAsC;UAAtCX,EAAA,CAAAY,UAAA,SAAA2Z,GAAA,CAAAlM,cAAA,CAAAC,YAAA,MAAsC;UAUmBtO,EAAA,CAAAW,SAAA,GAA+C;UAA/CX,EAAA,CAAAkB,iBAAA,CAAAqZ,GAAA,CAAAzZ,WAAA,CAAAC,SAAA,uBAA+C;UAI/Ff,EAAA,CAAAW,SAAA,GAAgC;UAAhCX,EAAA,CAAAY,UAAA,YAAA2Z,GAAA,CAAAlM,cAAA,CAAAtK,GAAA,CAAgC,gBAAAwW,GAAA,CAAAzZ,WAAA,CAAAC,SAAA;UAUVf,EAAA,CAAAW,SAAA,GAA+F;UAA/FX,EAAA,CAAAY,UAAA,SAAA2Z,GAAA,CAAAzY,kBAAA,CAAAqT,QAAA,CAAApR,GAAA,CAAAxB,KAAA,KAAAgY,GAAA,CAAAzY,kBAAA,CAAAqT,QAAA,CAAApR,GAAA,CAAA/B,MAAA,kBAAAuY,GAAA,CAAAzY,kBAAA,CAAAqT,QAAA,CAAApR,GAAA,CAAA/B,MAAA,CAAAC,QAAA,EAA+F;UAC/FjC,EAAA,CAAAW,SAAA,GAAuD;UAAvDX,EAAA,CAAAY,UAAA,SAAA2Z,GAAA,CAAAzY,kBAAA,CAAAqT,QAAA,CAAApR,GAAA,CAAA/B,MAAA,kBAAAuY,GAAA,CAAAzY,kBAAA,CAAAqT,QAAA,CAAApR,GAAA,CAAA/B,MAAA,CAAAG,SAAA,CAAuD;UACvDnC,EAAA,CAAAW,SAAA,GAAqD;UAArDX,EAAA,CAAAY,UAAA,SAAA2Z,GAAA,CAAAzY,kBAAA,CAAAqT,QAAA,CAAApR,GAAA,CAAA/B,MAAA,kBAAAuY,GAAA,CAAAzY,kBAAA,CAAAqT,QAAA,CAAApR,GAAA,CAAA/B,MAAA,CAAAE,OAAA,CAAqD;UAKzBlC,EAAA,CAAAW,SAAA,GAAiD;UAAjDX,EAAA,CAAAkB,iBAAA,CAAAqZ,GAAA,CAAAzZ,WAAA,CAAAC,SAAA,yBAAiD;UAInGf,EAAA,CAAAW,SAAA,GAAwC;UAAxCX,EAAA,CAAAY,UAAA,YAAA2Z,GAAA,CAAAlM,cAAA,CAAAG,WAAA,CAAwC,gBAAA+L,GAAA,CAAAzZ,WAAA,CAAAC,SAAA;UAWdf,EAAA,CAAAW,SAAA,GAA+G;UAA/GX,EAAA,CAAAY,UAAA,SAAA2Z,GAAA,CAAAzY,kBAAA,CAAAqT,QAAA,CAAA3G,WAAA,CAAAjM,KAAA,KAAAgY,GAAA,CAAAzY,kBAAA,CAAAqT,QAAA,CAAA3G,WAAA,CAAAxM,MAAA,kBAAAuY,GAAA,CAAAzY,kBAAA,CAAAqT,QAAA,CAAA3G,WAAA,CAAAxM,MAAA,CAAAC,QAAA,EAA+G;UAG/GjC,EAAA,CAAAW,SAAA,GAA6D;UAA7DX,EAAA,CAAAY,UAAA,SAAA2Z,GAAA,CAAAzY,kBAAA,CAAAqT,QAAA,CAAA3G,WAAA,CAAAxM,MAAA,kBAAAuY,GAAA,CAAAzY,kBAAA,CAAAqT,QAAA,CAAA3G,WAAA,CAAAxM,MAAA,CAAAE,OAAA,CAA6D;UAOvDlC,EAAA,CAAAW,SAAA,GAAuD;UAAvDX,EAAA,CAAAY,UAAA,UAAA2Z,GAAA,CAAAzZ,WAAA,CAAAC,SAAA,yBAAuD;UACnDf,EAAA,CAAAW,SAAA,GAA2B;UAA3BX,EAAA,CAAAY,UAAA,aAAA2Z,GAAA,CAAAlE,YAAA,GAA2B,UAAAkE,GAAA,CAAAzZ,WAAA,CAAAC,SAAA;UAEyBf,EAAA,CAAAW,SAAA,GAA2B;UAA3BX,EAAA,CAAA4F,UAAA,CAAA5F,EAAA,CAAAuB,eAAA,KAAAub,GAAA,EAA2B;UAA5H9c,EAAA,CAAAY,UAAA,WAAA2Z,GAAA,CAAAzZ,WAAA,CAAAC,SAAA,2BAA0D,YAAAwZ,GAAA,CAAA3O,QAAA;UAEL5L,EAAA,CAAAW,SAAA,GAAoB;UAApBX,EAAA,CAAAY,UAAA,qBAAoB;UAC6EZ,EAAA,CAAAW,SAAA,GAAwB;UAAxBX,EAAA,CAAAY,UAAA,aAAA2Z,GAAA,CAAA9W,SAAA,KAAwB;UAAwBzD,EAAA,CAAAW,SAAA,GAA6D;UAA7DX,EAAA,CAAA8H,kBAAA,KAAAyS,GAAA,CAAAzZ,WAAA,CAAAC,SAAA,yCAA6D;UAAMf,EAAA,CAAAW,SAAA,GAAiB;UAAjBX,EAAA,CAAAY,UAAA,SAAA2Z,GAAA,CAAA9W,SAAA,KAAiB;UAE5PzD,EAAA,CAAAW,SAAA,GAAoD;UAApDX,EAAA,CAAAY,UAAA,aAAA2Z,GAAA,CAAAzY,kBAAA,CAAAqT,QAAA,CAAA1G,GAAA,CAAAnM,OAAA,CAAoD;UAAStC,EAAA,CAAAW,SAAA,GAA+C;UAA/CX,EAAA,CAAAkB,iBAAA,CAAAqZ,GAAA,CAAAzZ,WAAA,CAAAC,SAAA,uBAA+C;UACzCf,EAAA,CAAAW,SAAA,GAAiD;UAAjDX,EAAA,CAAAkB,iBAAA,CAAAqZ,GAAA,CAAAzZ,WAAA,CAAAC,SAAA,yBAAiD;UAUkBf,EAAA,CAAAW,SAAA,GAAuB;UAAvBX,EAAA,CAAAY,UAAA,SAAA2Z,GAAA,CAAAlV,iBAAA,CAAuB;UAsH1JrF,EAAA,CAAAW,SAAA,GAA2B;UAA3BX,EAAA,CAAAY,UAAA,SAAA2Z,GAAA,CAAA7T,qBAAA,CAA2B;UAuD3B1G,EAAA,CAAAW,SAAA,GAA0B;UAA1BX,EAAA,CAAAY,UAAA,SAAA2Z,GAAA,CAAArS,oBAAA,CAA0B;UA0BpDlI,EAAA,CAAAW,SAAA,GAA2B;UAA3BX,EAAA,CAAA4F,UAAA,CAAA5F,EAAA,CAAAuB,eAAA,KAAAub,GAAA,EAA2B;UAD1C9c,EAAA,CAAAY,UAAA,WAAA2Z,GAAA,CAAAzZ,WAAA,CAAAC,SAAA,2BAA0D,YAAAwZ,GAAA,CAAA/H,qBAAA;UAG1DxS,EAAA,CAAAW,SAAA,GAA4B;UAA5BX,EAAA,CAAAY,UAAA,cAAA2Z,GAAA,CAAAnI,cAAA,CAA4B;UAE6BpS,EAAA,CAAAW,SAAA,GAAoB;UAApBX,EAAA,CAAAY,UAAA,qBAAoB;UAG5BZ,EAAA,CAAAW,SAAA,GAAwB;UAAxBX,EAAA,CAAAY,UAAA,aAAA2Z,GAAA,CAAA9W,SAAA,KAAwB;UAC7BzD,EAAA,CAAAW,SAAA,GAA+D;UAA/DX,EAAA,CAAA8H,kBAAA,KAAAyS,GAAA,CAAAzZ,WAAA,CAAAC,SAAA,yCAA+D;UACpGf,EAAA,CAAAW,SAAA,GAAiB;UAAjBX,EAAA,CAAAY,UAAA,SAAA2Z,GAAA,CAAA9W,SAAA,KAAiB;UAIUzD,EAAA,CAAAW,SAAA,GAAgD;UAAhDX,EAAA,CAAAY,UAAA,aAAA2Z,GAAA,CAAAnI,cAAA,CAAA+C,QAAA,CAAA1G,GAAA,CAAAnM,OAAA,CAAgD;UAC5DtC,EAAA,CAAAW,SAAA,GAChB;UADgBX,EAAA,CAAA8H,kBAAA,KAAAyS,GAAA,CAAAzZ,WAAA,CAAAC,SAAA,4BAChB;UAEgDf,EAAA,CAAAW,SAAA,GAChD;UADgDX,EAAA,CAAA8H,kBAAA,KAAAyS,GAAA,CAAAzZ,WAAA,CAAAC,SAAA,8BAChD;UASmCf,EAAA,CAAAW,SAAA,GAA2B;UAA3BX,EAAA,CAAAY,UAAA,SAAA2Z,GAAA,CAAAtR,qBAAA,CAA2B;UA0B3BjJ,EAAA,CAAAW,SAAA,GAA2B;UAA3BX,EAAA,CAAAY,UAAA,SAAA2Z,GAAA,CAAAvQ,qBAAA,CAA2B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}