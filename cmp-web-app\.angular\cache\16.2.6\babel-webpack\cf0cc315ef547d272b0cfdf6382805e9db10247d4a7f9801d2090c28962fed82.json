{"ast": null, "code": "import { RolesService } from \"src/app/service/account/RolesService\";\nimport { CONSTANTS } from 'src/app/service/comon/constants';\nimport { ComponentBase } from \"../../../../component.base\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"primeng/breadcrumb\";\nimport * as i5 from \"primeng/inputtext\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"../../../common-module/table/table.component\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/dialog\";\nimport * as i10 from \"primeng/tree\";\nimport * as i11 from \"primeng/panel\";\nimport * as i12 from \"src/app/service/account/RolesService\";\nconst _c0 = [\"class\", \"roles list\"];\nconst _c1 = function () {\n  return [\"/roles/create\"];\n};\nfunction AppRolesListComponent_p_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-button\", 29);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"global.button.create\"))(\"routerLink\", i0.ɵɵpureFunction0(2, _c1));\n  }\n}\nconst _c2 = function (a0) {\n  return [a0];\n};\nconst _c3 = function () {\n  return {\n    width: \"980px\"\n  };\n};\nconst _c4 = function () {\n  return {\n    \"max-height\": \"500px\",\n    \"overflow-y\": \"scroll\"\n  };\n};\nexport class AppRolesListComponent extends ComponentBase {\n  // tranService: TranslateService;\n  constructor(rolesService, formBuilder, injector) {\n    super(injector);\n    this.rolesService = rolesService;\n    this.formBuilder = formBuilder;\n    this.injector = injector;\n    this.isShowDialogChangeManageLevel = false;\n    this.isShowModalDetail = false;\n    //Danh sách nhóm quyền của tài khoản đang xem\n    this.roles = [];\n    this.CONSTANTS = CONSTANTS;\n  }\n  ngOnInit() {\n    let me = this;\n    this.userType = this.sessionService.userInfo.type;\n    //Lấy nhóm quyền của tk\n    this.roles = this.sessionService.userInfo.roles;\n    this.items = [{\n      label: this.tranService.translate(\"global.menu.accountmgmt\")\n    }, {\n      label: this.tranService.translate(\"global.menu.listroles\")\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.searchInfo = {\n      name: null,\n      type: null,\n      status: null\n    };\n    this.formSearchRoles = this.formBuilder.group(this.searchInfo);\n    this.pageNumber = 0;\n    this.pageSize = 10;\n    this.sort = \"name,asc\";\n    this.dataSet = {\n      content: [],\n      total: 0\n    };\n    this.dataSetForDetail = {\n      content: [],\n      total: 0\n    };\n    this.dataSetForDetail = {\n      content: [{\n        label: this.tranService.translate(\"global.text.all\"),\n        key: \"all\",\n        children: null,\n        data: null\n      }],\n      total: 0\n    };\n    let fullTypeRole = [{\n      name: this.tranService.translate(\"roles.type.admin\"),\n      value: CONSTANTS.ROLE_TYPE.ADMIN,\n      accepts: [CONSTANTS.ROLE_TYPE.ADMIN]\n    }, {\n      name: this.tranService.translate(\"roles.type.all\"),\n      value: CONSTANTS.ROLE_TYPE.ALL,\n      accepts: []\n    }, {\n      name: this.tranService.translate(\"roles.type.customer\"),\n      value: CONSTANTS.ROLE_TYPE.CUSTOMER,\n      accepts: [CONSTANTS.ROLE_TYPE.ADMIN, CONSTANTS.ROLE_TYPE.CUSTOMER, CONSTANTS.ROLE_TYPE.PROVINCE, CONSTANTS.ROLE_TYPE.TELLER]\n    }, {\n      name: this.tranService.translate(\"roles.type.province\"),\n      value: CONSTANTS.ROLE_TYPE.PROVINCE,\n      accepts: [CONSTANTS.ROLE_TYPE.ADMIN]\n    }, {\n      name: this.tranService.translate(\"roles.type.teller\"),\n      value: CONSTANTS.ROLE_TYPE.TELLER,\n      accepts: [CONSTANTS.ROLE_TYPE.ADMIN, CONSTANTS.ROLE_TYPE.PROVINCE]\n    }, {\n      name: this.tranService.translate(\"roles.type.agency\"),\n      value: CONSTANTS.ROLE_TYPE.AGENCY,\n      accepts: [CONSTANTS.ROLE_TYPE.ADMIN, CONSTANTS.ROLE_TYPE.PROVINCE, CONSTANTS.ROLE_TYPE.TELLER, CONSTANTS.ROLE_TYPE.AGENCY]\n    }];\n    this.roleType = fullTypeRole.filter(el => el.accepts.includes(this.userType) || el.accepts.length == 0);\n    this.selectItems = [];\n    this.roleInfo = {\n      name: null,\n      type: null,\n      status: null,\n      roles: null,\n      description: null,\n      permissionIds: null\n    };\n    this.optionTable = {\n      hasClearSelected: true,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false,\n      action: [{\n        icon: \"pi pi-pencil\",\n        tooltip: this.tranService.translate(\"global.button.edit\"),\n        func: function (id, item) {\n          me.router.navigate([`/roles/edit/${id}`]);\n        },\n        funcAppear: function (id, item) {\n          //   console.log(me.userType);\n          //tài khoản khách hàng\n          if (me.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\n            //Nếu nhóm quyền này đang được gán với tài khoản khách hàng này thì không cho tác động\n            if ((me.roles || []).includes(item.name)) {\n              return false;\n            } else {\n              return true;\n            }\n          }\n          return (me.userType == CONSTANTS.USER_TYPE.ADMIN || item.type != CONSTANTS.ROLE_TYPE.ALL) && item.createdBy != 0;\n          ;\n        }\n      }, {\n        icon: \"pi pi-lock\",\n        tooltip: this.tranService.translate(\"global.button.changeStatus\"),\n        func: function (id, item) {\n          me.messageCommonService.confirm(me.tranService.translate(\"global.message.titleConfirmChangeStatusRole\"), me.tranService.translate(\"global.message.confirmChangeStatusRole\"), {\n            ok: () => {\n              me.rolesService.changeStatusRole(id, response => {\n                me.messageCommonService.success(me.tranService.translate(\"global.message.changeStatusSuccess\"));\n                me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n              });\n              //   me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n            },\n\n            cancel: () => {\n              // me.messageCommonService.error(me.tranService.translate(\"global.message.changeStatusFail\"));\n            }\n          });\n        },\n        funcAppear: function (id, item) {\n          //tài khoản khách hàng\n          if (me.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\n            //Nếu nhóm quyền này đang được gán với tài khoản khách hàng này thì không cho tác động\n            if ((me.roles || []).includes(item.name)) {\n              return false;\n            } else {\n              return true;\n            }\n          }\n          return item.status == CONSTANTS.ROlES_STATUS.ACTIVE && (me.userType == CONSTANTS.USER_TYPE.ADMIN || item.type != CONSTANTS.ROLE_TYPE.ALL) && item.createdBy != 0;\n        }\n      }, {\n        icon: \"pi pi-lock-open\",\n        tooltip: this.tranService.translate(\"global.button.changeStatus\"),\n        func: function (id, item) {\n          me.messageCommonService.confirm(me.tranService.translate(\"global.message.titleConfirmChangeStatusRole\"), me.tranService.translate(\"global.message.confirmChangeStatusRole\"), {\n            ok: () => {\n              //   console.log(id)\n              me.rolesService.changeStatusRole(id, response => {\n                me.messageCommonService.success(me.tranService.translate(\"global.message.changeStatusSuccess\"));\n                me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n              });\n              me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n            },\n            cancel: () => {\n              // me.messageCommonService.error(me.tranService.translate(\"global.message.changeStatusFail\"));\n            }\n          });\n        },\n        funcAppear: function (id, item) {\n          return item.status == CONSTANTS.ROlES_STATUS.INACTIVE && (me.userType == CONSTANTS.USER_TYPE.ADMIN || item.type != CONSTANTS.ROLE_TYPE.ALL) && item.createdBy != 0;\n        }\n      }, {\n        icon: \"pi pi-trash\",\n        tooltip: this.tranService.translate(\"global.button.delete\"),\n        func: function (id, item) {\n          me.messageCommonService.confirm(me.tranService.translate(\"global.message.titleConfirmDeleteRoles\"), me.tranService.translate(\"global.message.confirmDeleteRoles\"), {\n            ok: () => {\n              me.rolesService.deleteRole(id, response => {\n                me.messageCommonService.success(me.tranService.translate(\"global.message.deleteSuccess\"));\n                me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n              });\n            },\n            cancel: () => {\n              // me.messageCommonService.error(me.tranService.translate(\"global.message.deleteFail\"));\n            }\n          });\n        },\n        funcAppear: function (id, item) {\n          //tài khoản khách hàng\n          if (me.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\n            //Nếu nhóm quyền này đang được gán với tài khoản khách hàng này thì không cho tác động\n            if ((me.roles || []).includes(item.name)) {\n              return false;\n            } else {\n              return true;\n            }\n          }\n          return (me.userType == CONSTANTS.USER_TYPE.ADMIN || item.type != CONSTANTS.ROLE_TYPE.ALL) && item.createdBy != 0;\n        }\n      }]\n    }, this.columns = [{\n      name: this.tranService.translate(\"roles.label.rolename\"),\n      key: \"name\",\n      size: \"30%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      style: {\n        cursor: \"pointer\",\n        color: \"var(--mainColorText)\"\n      },\n      funcClick(id, item) {\n        me.roleId = id;\n        me.getDetail();\n        me.isShowModalDetail = true;\n      }\n    }, {\n      name: this.tranService.translate(\"roles.label.status\"),\n      key: \"status\",\n      size: \"25%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      style: {\n        color: \"white\"\n      },\n      funcGetClassname: value => {\n        if (value == 1) {\n          return ['p-2', \"text-green-800\", \"bg-green-100\", \"border-round\", \"inline-block\"];\n        } else if (value == CONSTANTS.ROlES_STATUS.INACTIVE) {\n          return ['p-2', \"text-red-700\", \"bg-red-100\", \"border-round\", \"inline-block\"];\n        }\n        return [];\n      },\n      funcConvertText: value => {\n        if (value == 1) {\n          return me.tranService.translate(\"roles.status.active\");\n        } else if (value == CONSTANTS.ROlES_STATUS.INACTIVE) {\n          return me.tranService.translate(\"roles.status.inactive\");\n        }\n        return \"\";\n      }\n    }, {\n      name: this.tranService.translate(\"roles.label.usertype\"),\n      key: \"type\",\n      size: \"25%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        if (value == CONSTANTS.ROLE_TYPE.ADMIN) {\n          return me.tranService.translate(\"roles.type.admin\");\n        } else if (value == CONSTANTS.ROLE_TYPE.CUSTOMER) {\n          return me.tranService.translate(\"roles.type.customer\");\n        } else if (value == CONSTANTS.ROLE_TYPE.PROVINCE) {\n          return me.tranService.translate(\"roles.type.province\");\n        } else if (value == CONSTANTS.ROLE_TYPE.TELLER) {\n          return me.tranService.translate(\"roles.type.teller\");\n        } else if (value == CONSTANTS.ROLE_TYPE.AGENCY) {\n          return me.tranService.translate(\"roles.type.agency\");\n        } else if (value == CONSTANTS.ROLE_TYPE.ALL) {\n          return me.tranService.translate(\"roles.type.all\");\n        } else {\n          return \"\";\n        }\n      }\n    }];\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n    // throw new Error('Method not implemented.');\n  }\n\n  onSubmitSearch() {\n    this.pageNumber = 0;\n    this.search(0, this.pageSize, this.sort, this.searchInfo);\n  }\n  search(page, limit, sort, params) {\n    this.pageNumber = page;\n    this.pageSize = limit;\n    this.sort = sort;\n    let me = this;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    Object.keys(this.searchInfo).forEach(key => {\n      if (this.searchInfo[key] != null) {\n        dataParams[key] = this.searchInfo[key];\n      }\n    });\n    me.messageCommonService.onload();\n    this.rolesService.search(dataParams, response => {\n      me.dataSet = {\n        content: response.content,\n        total: response.totalElements\n      };\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  getType(value) {\n    if (value == CONSTANTS.ROLE_TYPE.ADMIN) {\n      return this.tranService.translate(\"roles.type.admin\");\n    } else if (value == CONSTANTS.ROLE_TYPE.CUSTOMER) {\n      return this.tranService.translate(\"roles.type.customer\");\n    } else if (value == CONSTANTS.ROLE_TYPE.PROVINCE) {\n      return this.tranService.translate(\"roles.type.province\");\n    } else if (value == CONSTANTS.ROLE_TYPE.TELLER) {\n      return this.tranService.translate(\"roles.type.teller\");\n    } else if (value == CONSTANTS.ROLE_TYPE.AGENCY) {\n      return this.tranService.translate(\"roles.type.agency\");\n    } else if (value == CONSTANTS.ROLE_TYPE.ALL) {\n      return this.tranService.translate(\"roles.type.all\");\n    }\n    return \"\";\n  }\n  getStatus(value) {\n    if (value == CONSTANTS.ROlES_STATUS.INACTIVE) {\n      return this.tranService.translate(\"roles.status.inactive\");\n    } else if (value == CONSTANTS.ROlES_STATUS.ACTIVE) {\n      return this.tranService.translate(\"roles.status.active\");\n    }\n    return \"\";\n  }\n  getDetail() {\n    let me = this;\n    me.messageCommonService.onload();\n    me.rolesService.getById(me.roleId, response => {\n      me.roleInfo.name = response.name;\n      me.roleInfo.type = response.type;\n      me.roleInfo.status = response.status;\n      me.roleInfo.permissionIds = response.permissionIds;\n      me.getTreeRoles();\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  getTreeRoles() {\n    let me = this;\n    me.rolesService.getTreeRoles(response => {\n      response.forEach(el => {\n        el.label = me.tranService.translate(`permission.${el.key}.${el.key}`);\n        el.partialSelected = true;\n        if (el.children) {\n          el.children.forEach(item => {\n            item.label = me.tranService.translate(`permission.${el.key}.${item.key}`, {}, item.data?.description);\n          });\n        }\n      });\n      me.dataSetForDetail = {\n        content: [{\n          label: this.tranService.translate(\"global.text.all\"),\n          key: \"all\",\n          children: response,\n          data: null,\n          expanded: true,\n          partialSelected: true\n        }],\n        total: 0\n      };\n      // let permissionIds = [1, 2, 3, 4, 5];\n      me.roleInfo.roles = [];\n      let totalOfTotal = 0;\n      me.dataSetForDetail.content[0].children.forEach(el => {\n        if (el.children != null) {\n          let total = 0;\n          el.children.forEach(item => {\n            if (this.roleInfo.permissionIds.includes(item.data.id)) {\n              me.roleInfo.roles.push(item);\n              total++;\n            }\n          });\n          if (total != 0 && total == el.children.length) {\n            me.roleInfo.roles.push(el);\n            el.partialSelected = false;\n            totalOfTotal++;\n          } else if (total == 0) {\n            el.partialSelected = false;\n          }\n        }\n      });\n      if (totalOfTotal != 0 && totalOfTotal == me.dataSetForDetail.content[0].children.length) {\n        let element = me.dataSetForDetail.content[0];\n        element.partialSelected = false;\n        me.roleInfo.roles.push(element);\n      }\n    });\n  }\n  closeForm() {\n    this.router.navigate(['/roles']);\n  }\n  ngAfterContentChecked() {\n    // throw new Error('Method not implemented.');\n  }\n  static {\n    this.ɵfac = function AppRolesListComponent_Factory(t) {\n      return new (t || AppRolesListComponent)(i0.ɵɵdirectiveInject(RolesService), i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppRolesListComponent,\n      selectors: [[\"app-app\", 8, \"roles\", \"list\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      attrs: _c0,\n      decls: 50,\n      vars: 47,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [\"styleClass\", \"p-button-info\", \"icon\", \"pi pi-plus\", \"routerLinkActive\", \"router-link-active\", 3, \"label\", \"routerLink\", 4, \"ngIf\"], [1, \"pb-2\", \"pt-3\", \"vnpt-field-set\", 3, \"formGroup\", \"ngSubmit\"], [3, \"toggleable\", \"header\"], [1, \"grid\", \"search-grid-3\"], [1, \"col-3\"], [1, \"p-float-label\"], [\"styleClass\", \"w-full\", \"id\", \"type\", \"formControlName\", \"type\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"ngModelChange\"], [\"for\", \"type\"], [\"pInputText\", \"\", \"pInputText\", \"\", \"id\", \"name\", \"formControlName\", \"name\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"name\"], [1, \"col-3\", \"pb-0\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"submit\"], [3, \"fieldId\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"pageNumber\", \"pageSize\", \"sort\", \"params\", \"labelTable\", \"selectItemsChange\"], [1, \"flex\", \"justify-content-center\", \"dialog-vnpt\"], [3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"flex\", \"flex-row\", \"justify-content-between\", \"role-create\"], [2, \"width\", \"49%\"], [1, \"mt-1\", \"ml-5\", \"grid\", \"role-info-grid\"], [1, \"inline-block\", \"col-fixed\", 2, \"min-width\", \"200px\", \"max-width\", \"200px\"], [1, \"col\"], [1, \"role-create-div\", 2, \"width\", \"51%\"], [\"for\", \"roles\", 1, \"col-fixed\", \"inline-block\", \"mt-1\", 2, \"width\", \"180px\"], [1, \"text-red-500\"], [\"disabled\", \"true\", \"id\", \"roles\", \"selectionMode\", \"checkbox\", 1, \"w-full\", \"md:w-30rem\", 3, \"value\", \"selection\", \"selectionChange\"], [\"styleClass\", \"p-button-info\", \"icon\", \"pi pi-plus\", \"routerLinkActive\", \"router-link-active\", 3, \"label\", \"routerLink\"]],\n      template: function AppRolesListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵtemplate(6, AppRolesListComponent_p_button_6_Template, 1, 3, \"p-button\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"form\", 6);\n          i0.ɵɵlistener(\"ngSubmit\", function AppRolesListComponent_Template_form_ngSubmit_7_listener() {\n            return ctx.onSubmitSearch();\n          });\n          i0.ɵɵelementStart(8, \"p-panel\", 7)(9, \"div\", 8)(10, \"div\", 9)(11, \"span\", 10)(12, \"p-dropdown\", 11);\n          i0.ɵɵlistener(\"ngModelChange\", function AppRolesListComponent_Template_p_dropdown_ngModelChange_12_listener($event) {\n            return ctx.searchInfo.type = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"label\", 12);\n          i0.ɵɵtext(14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 9)(16, \"span\", 10)(17, \"input\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function AppRolesListComponent_Template_input_ngModelChange_17_listener($event) {\n            return ctx.searchInfo.name = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"label\", 14);\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 15);\n          i0.ɵɵelement(21, \"p-button\", 16);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"table-vnpt\", 17);\n          i0.ɵɵlistener(\"selectItemsChange\", function AppRolesListComponent_Template_table_vnpt_selectItemsChange_22_listener($event) {\n            return ctx.selectItems = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 18)(24, \"p-dialog\", 19);\n          i0.ɵɵlistener(\"visibleChange\", function AppRolesListComponent_Template_p_dialog_visibleChange_24_listener($event) {\n            return ctx.isShowModalDetail = $event;\n          });\n          i0.ɵɵelementStart(25, \"div\")(26, \"div\", 20)(27, \"div\", 21)(28, \"div\", 22)(29, \"span\", 23);\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"span\", 24);\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 22)(34, \"span\", 23);\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"span\", 24);\n          i0.ɵɵtext(37);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 22)(39, \"span\", 23);\n          i0.ɵɵtext(40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"span\", 24);\n          i0.ɵɵtext(42);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(43, \"div\", 25)(44, \"label\", 26);\n          i0.ɵɵtext(45);\n          i0.ɵɵelementStart(46, \"span\", 27);\n          i0.ɵɵtext(47, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 24)(49, \"p-tree\", 28);\n          i0.ɵɵlistener(\"selectionChange\", function AppRolesListComponent_Template_p_tree_selectionChange_49_listener($event) {\n            return ctx.roleInfo.roles = $event;\n          });\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.menu.listroles\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkAuthen(i0.ɵɵpureFunction1(43, _c2, ctx.CONSTANTS.PERMISSIONS.ROLE.CREATE)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formSearchRoles);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"toggleable\", true)(\"header\", ctx.tranService.translate(\"global.text.filter\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.searchInfo.type)(\"options\", ctx.roleType);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"roles.label.usertype\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.name);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"roles.label.rolename\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"fieldId\", \"id\")(\"selectItems\", ctx.selectItems)(\"columns\", ctx.columns)(\"dataSet\", ctx.dataSet)(\"options\", ctx.optionTable)(\"loadData\", ctx.search.bind(ctx))(\"pageNumber\", ctx.pageNumber)(\"pageSize\", ctx.pageSize)(\"sort\", ctx.sort)(\"params\", ctx.searchInfo)(\"labelTable\", ctx.tranService.translate(\"global.menu.listroles\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(45, _c3));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"global.menu.detailroles\"))(\"visible\", ctx.isShowModalDetail)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"roles.label.rolename\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.roleInfo.name);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"roles.label.usertype\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getType(ctx.roleInfo.type));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"roles.label.status\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getStatus(ctx.roleInfo.status));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"roles.label.rolelist\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(46, _c4));\n          i0.ɵɵproperty(\"value\", ctx.dataSetForDetail.content)(\"selection\", ctx.roleInfo.roles);\n        }\n      },\n      dependencies: [i2.NgIf, i3.RouterLink, i3.RouterLinkActive, i4.Breadcrumb, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.InputText, i6.Button, i7.TableVnptComponent, i8.Dropdown, i9.Dialog, i10.Tree, i11.Panel]\n    });\n  }\n}", "map": {"version": 3, "names": ["RolesService", "CONSTANTS", "ComponentBase", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "tranService", "translate", "ɵɵpureFunction0", "_c1", "AppRolesListComponent", "constructor", "rolesService", "formBuilder", "injector", "isShowDialogChangeManageLevel", "isShowModalDetail", "roles", "ngOnInit", "me", "userType", "sessionService", "userInfo", "type", "items", "label", "home", "icon", "routerLink", "searchInfo", "name", "status", "formSearchRoles", "group", "pageNumber", "pageSize", "sort", "dataSet", "content", "total", "dataSetForDetail", "key", "children", "data", "fullTypeRole", "value", "ROLE_TYPE", "ADMIN", "accepts", "ALL", "CUSTOMER", "PROVINCE", "TELLER", "AGENCY", "roleType", "filter", "el", "includes", "length", "selectItems", "roleInfo", "description", "permissionIds", "optionTable", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "action", "tooltip", "func", "id", "item", "router", "navigate", "funcAppear", "USER_TYPE", "created<PERSON>y", "messageCommonService", "confirm", "ok", "changeStatusRole", "response", "success", "search", "cancel", "ROlES_STATUS", "ACTIVE", "INACTIVE", "deleteRole", "columns", "size", "align", "isShow", "isSort", "style", "cursor", "color", "funcClick", "roleId", "getDetail", "funcGetClassname", "funcConvertText", "onSubmitSearch", "page", "limit", "params", "dataParams", "Object", "keys", "for<PERSON>ach", "onload", "totalElements", "offload", "getType", "getStatus", "getById", "getTreeRoles", "partialSelected", "expanded", "totalOfTotal", "push", "element", "closeForm", "ngAfterContentChecked", "ɵɵdirectiveInject", "i1", "FormBuilder", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "attrs", "_c0", "decls", "vars", "consts", "template", "AppRolesListComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "AppRolesListComponent_p_button_6_Template", "ɵɵlistener", "AppRolesListComponent_Template_form_ngSubmit_7_listener", "AppRolesListComponent_Template_p_dropdown_ngModelChange_12_listener", "$event", "AppRolesListComponent_Template_input_ngModelChange_17_listener", "AppRolesListComponent_Template_table_vnpt_selectItemsChange_22_listener", "AppRolesListComponent_Template_p_dialog_visibleChange_24_listener", "AppRolesListComponent_Template_p_tree_selectionChange_49_listener", "ɵɵadvance", "ɵɵtextInterpolate", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵpureFunction1", "_c2", "PERMISSIONS", "ROLE", "CREATE", "bind", "ɵɵstyleMap", "_c3", "_c4"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\account-management\\roles\\list\\app.roles.list.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\account-management\\roles\\list\\app.roles.list.component.html"], "sourcesContent": ["import {Component, Inject, OnInit, AfterContentChecked, Injector} from '@angular/core';\r\nimport { FormBuilder } from '@angular/forms';\r\nimport { MenuItem, TreeNode } from \"primeng/api\";\r\nimport { MessageCommonService } from 'src/app/service/comon/message-common.service';\r\nimport { TranslateService } from \"src/app/service/comon/translate.service\";\r\nimport { RolesService} from \"src/app/service/account/RolesService\"\r\nimport { ColumnInfo, OptionTable } from 'src/app/template/common-module/table/table.component';\r\nimport { UtilService } from 'src/app/service/comon/util.service';\r\nimport { Router } from '@angular/router';\r\nimport { CONSTANTS } from 'src/app/service/comon/constants';\r\nimport {ComponentBase} from \"../../../../component.base\";\r\n\r\n@Component({\r\n  selector: 'app-app.roles.list',\r\n  templateUrl: './app.roles.list.component.html',\r\n})\r\nexport class AppRolesListComponent extends ComponentBase implements OnInit, AfterContentChecked{\r\n      home: MenuItem\r\n      items: MenuItem[];\r\n      formSearchRoles: any;\r\n      pageNumber: number;\r\n      pageSize: number;\r\n      sort: string;\r\n      selectItems: Array<any>;\r\n      columns: Array<ColumnInfo>;\r\n      optionTable: OptionTable;\r\n      roleType: Array<any>;\r\n    isShowDialogChangeManageLevel: boolean = false;\r\n      accountId: number;\r\n\r\n\r\n      dataSet: {\r\n        content: Array<any>,\r\n        total: number\r\n      };\r\n      dataSetForDetail: {\r\n        content: TreeNode[],\r\n        total: number\r\n      };\r\n      searchInfo: {\r\n          name: string|null,\r\n          type: number|null,\r\n\r\n          status:string|null,\r\n      };\r\n    userType: number;\r\n    userTypes: Array<any>;\r\n    isShowModalDetail: boolean = false;\r\n    roleInfo: {\r\n        name: string| null,\r\n        type: number|null,\r\n        status: number|null,\r\n        description: string|null\r\n        roles: Array<any>,\r\n        permissionIds: Array<any>,\r\n    };\r\n    roleId: number;\r\n    // tranService: TranslateService;\r\n      constructor(\r\n            @Inject(RolesService) private rolesService: RolesService,\r\n            private formBuilder: FormBuilder,\r\n            private injector: Injector\r\n      ) {\r\n          super(injector);\r\n      }\r\n      //Danh sách nhóm quyền của tài khoản đang xem\r\n      roles: Array<any> = [];\r\n      ngOnInit(): void {\r\n        let me = this;\r\n          this.userType = this.sessionService.userInfo.type;\r\n          //Lấy nhóm quyền của tk\r\n          this.roles = this.sessionService.userInfo.roles;\r\n          this.items = [{ label: this.tranService.translate(\"global.menu.accountmgmt\") }, { label: this.tranService.translate(\"global.menu.listroles\") },];\r\n        this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n        this.searchInfo = {\r\n          name: null,\r\n          type: null,\r\n          status: null,\r\n        }\r\n        this.formSearchRoles = this.formBuilder.group(this.searchInfo);\r\n        this.pageNumber = 0;\r\n        this.pageSize= 10;\r\n        this.sort = \"name,asc\"\r\n        this.dataSet ={\r\n          content: [],\r\n          total: 0\r\n        };\r\n        this.dataSetForDetail = {\r\n            content: [],\r\n            total: 0\r\n        };\r\n        this.dataSetForDetail = {\r\n              content: [{\r\n                  label: this.tranService.translate(\"global.text.all\"),\r\n                  key: \"all\",\r\n                  children: null,\r\n                  data: null\r\n              }],\r\n              total: 0\r\n        }\r\n          let fullTypeRole = [\r\n              {name: this.tranService.translate(\"roles.type.admin\"),value:CONSTANTS.ROLE_TYPE.ADMIN, accepts:[CONSTANTS.ROLE_TYPE.ADMIN]},\r\n              {name: this.tranService.translate(\"roles.type.all\"),value:CONSTANTS.ROLE_TYPE.ALL, accepts:[]},\r\n              {name: this.tranService.translate(\"roles.type.customer\"),value:CONSTANTS.ROLE_TYPE.CUSTOMER,accepts:[CONSTANTS.ROLE_TYPE.ADMIN, CONSTANTS.ROLE_TYPE.CUSTOMER, CONSTANTS.ROLE_TYPE.PROVINCE, CONSTANTS.ROLE_TYPE.TELLER]},\r\n              {name: this.tranService.translate(\"roles.type.province\"),value:CONSTANTS.ROLE_TYPE.PROVINCE,accepts:[CONSTANTS.ROLE_TYPE.ADMIN]},\r\n              {name: this.tranService.translate(\"roles.type.teller\"),value:CONSTANTS.ROLE_TYPE.TELLER,accepts:[CONSTANTS.ROLE_TYPE.ADMIN, CONSTANTS.ROLE_TYPE.PROVINCE]},\r\n              {name: this.tranService.translate(\"roles.type.agency\"),value:CONSTANTS.ROLE_TYPE.AGENCY,accepts:[CONSTANTS.ROLE_TYPE.ADMIN, CONSTANTS.ROLE_TYPE.PROVINCE, CONSTANTS.ROLE_TYPE.TELLER, CONSTANTS.ROLE_TYPE.AGENCY]},\r\n          ]\r\n          this.roleType = fullTypeRole.filter(el => el.accepts.includes(this.userType) || el.accepts.length == 0);\r\n        this.selectItems = [];\r\n        this.roleInfo = {\r\n          name: null,\r\n          type: null,\r\n          status: null,\r\n          roles: null,\r\n          description: null,\r\n          permissionIds: null,\r\n        };\r\n        this.optionTable = {\r\n          hasClearSelected: true,\r\n          hasShowChoose: false,\r\n          hasShowIndex: true,\r\n          hasShowToggleColumn: false,\r\n          action: [\r\n              {\r\n                  icon: \"pi pi-pencil\",\r\n                  tooltip: this.tranService.translate(\"global.button.edit\"),\r\n                  func: function(id, item){\r\n                      me.router.navigate([`/roles/edit/${id}`]);\r\n                  },\r\n                  funcAppear: function(id, item) {\r\n                    //   console.log(me.userType);\r\n                      //tài khoản khách hàng\r\n                      if (me.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\r\n                          //Nếu nhóm quyền này đang được gán với tài khoản khách hàng này thì không cho tác động\r\n                          if ((me.roles || []).includes(item.name)) {\r\n                              return false;\r\n                          } else {\r\n                              return true;\r\n                          }\r\n                      }\r\n                      return (me.userType == CONSTANTS.USER_TYPE.ADMIN ||  item.type != CONSTANTS.ROLE_TYPE.ALL) && item.createdBy != 0;;\r\n                  }\r\n              },\r\n              {\r\n                  icon: \"pi pi-lock\",\r\n                  tooltip: this.tranService.translate(\"global.button.changeStatus\"),\r\n                  func: function(id, item){\r\n                      me.messageCommonService.confirm(\r\n                          me.tranService.translate(\"global.message.titleConfirmChangeStatusRole\"),\r\n                          me.tranService.translate(\"global.message.confirmChangeStatusRole\"),\r\n                          {\r\n                              ok:()=>{\r\n                                  me.rolesService.changeStatusRole(id, (response)=>{\r\n                                      me.messageCommonService.success(me.tranService.translate(\"global.message.changeStatusSuccess\"));\r\n                                      me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\r\n\r\n                                  })\r\n                                //   me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\r\n                              },\r\n                              cancel: ()=>{\r\n                                  // me.messageCommonService.error(me.tranService.translate(\"global.message.changeStatusFail\"));\r\n                              }\r\n                          }\r\n                      )\r\n                  },\r\n                  funcAppear: function(id, item) {\r\n                      //tài khoản khách hàng\r\n                      if (me.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\r\n                          //Nếu nhóm quyền này đang được gán với tài khoản khách hàng này thì không cho tác động\r\n                          if ((me.roles || []).includes(item.name)) {\r\n                              return false;\r\n                          } else {\r\n                              return true;\r\n                          }\r\n                      }\r\n                      return (item.status == CONSTANTS.ROlES_STATUS.ACTIVE && (me.userType == CONSTANTS.USER_TYPE.ADMIN ||  item.type != CONSTANTS.ROLE_TYPE.ALL)) && item.createdBy != 0;\r\n                  }\r\n              },\r\n              {\r\n                  icon: \"pi pi-lock-open\",\r\n                  tooltip: this.tranService.translate(\"global.button.changeStatus\"),\r\n                  func: function(id, item){\r\n                      me.messageCommonService.confirm(\r\n                          me.tranService.translate(\"global.message.titleConfirmChangeStatusRole\"),\r\n                          me.tranService.translate(\"global.message.confirmChangeStatusRole\"),\r\n                          {\r\n                              ok:()=>{\r\n                                //   console.log(id)\r\n                                  me.rolesService.changeStatusRole(id, (response)=>{\r\n                                      me.messageCommonService.success(me.tranService.translate(\"global.message.changeStatusSuccess\"));\r\n                                      me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\r\n                                  })\r\n                                  me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\r\n                              },\r\n                              cancel: ()=>{\r\n                                  // me.messageCommonService.error(me.tranService.translate(\"global.message.changeStatusFail\"));\r\n                              }\r\n                          }\r\n                      )\r\n                  },\r\n                  funcAppear: function(id, item) {\r\n                      return (item.status == CONSTANTS.ROlES_STATUS.INACTIVE && (me.userType == CONSTANTS.USER_TYPE.ADMIN || item.type != CONSTANTS.ROLE_TYPE.ALL)) && item.createdBy != 0;\r\n                  }\r\n              },\r\n              {\r\n                  icon: \"pi pi-trash\",\r\n                  tooltip: this.tranService.translate(\"global.button.delete\"),\r\n                  func: function(id, item){\r\n                      me.messageCommonService.confirm(\r\n                          me.tranService.translate(\"global.message.titleConfirmDeleteRoles\"),\r\n                          me.tranService.translate(\"global.message.confirmDeleteRoles\"),\r\n                          {\r\n                              ok:()=>{\r\n                                  me.rolesService.deleteRole(id, (response)=>{\r\n                                      me.messageCommonService.success(me.tranService.translate(\"global.message.deleteSuccess\"));\r\n                                      me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\r\n                                  })\r\n                              },\r\n                              cancel: ()=>{\r\n                                  // me.messageCommonService.error(me.tranService.translate(\"global.message.deleteFail\"));\r\n                              }\r\n                          }\r\n                      )\r\n                  },\r\n                  funcAppear: function(id, item) {\r\n                      //tài khoản khách hàng\r\n                      if (me.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\r\n                          //Nếu nhóm quyền này đang được gán với tài khoản khách hàng này thì không cho tác động\r\n                          if ((me.roles || []).includes(item.name)) {\r\n                              return false;\r\n                          } else {\r\n                              return true;\r\n                          }\r\n                      }\r\n                      return (me.userType == CONSTANTS.USER_TYPE.ADMIN || item.type != CONSTANTS.ROLE_TYPE.ALL) && item.createdBy != 0;\r\n                  }\r\n              },\r\n          ]\r\n      },\r\n\r\n        this.columns = [\r\n          {\r\n              name: this.tranService.translate(\"roles.label.rolename\"),\r\n              key: \"name\",\r\n              size: \"30%\",\r\n              align: \"left\",\r\n              isShow: true,\r\n              isSort: true,\r\n              style:{\r\n                  cursor: \"pointer\",\r\n             color: \"var(--mainColorText)\"\r\n              },\r\n              funcClick(id, item) {\r\n                  me.roleId = id;\r\n                  me.getDetail();\r\n                  me.isShowModalDetail = true;\r\n              },\r\n          },\r\n          {\r\n              name: this.tranService.translate(\"roles.label.status\"),\r\n              key: \"status\",\r\n              size: \"25%\",\r\n              align: \"left\",\r\n              isShow: true,\r\n              isSort: true,\r\n              style: {\r\n                  color: \"white\"\r\n              },\r\n              funcGetClassname: (value) => {\r\n                  if(value == 1){\r\n                      return ['p-2',\"text-green-800\", \"bg-green-100\",\"border-round\",\"inline-block\"];\r\n                  }else if(value == CONSTANTS.ROlES_STATUS.INACTIVE){\r\n                      return ['p-2', \"text-red-700\", \"bg-red-100\", \"border-round\",\"inline-block\"];\r\n                  }\r\n                  return [];\r\n              },\r\n              funcConvertText: (value)=>{\r\n                  if(value == 1){\r\n                      return me.tranService.translate(\"roles.status.active\");\r\n                  }else if(value == CONSTANTS.ROlES_STATUS.INACTIVE){\r\n                      return me.tranService.translate(\"roles.status.inactive\");\r\n                  }\r\n                  return \"\";\r\n              }\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"roles.label.usertype\"),\r\n                key: \"type\",\r\n                size: \"25%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcConvertText(value) {\r\n                    if(value == CONSTANTS.ROLE_TYPE.ADMIN){\r\n                        return me.tranService.translate(\"roles.type.admin\");\r\n                    }else if(value == CONSTANTS.ROLE_TYPE.CUSTOMER){\r\n                        return me.tranService.translate(\"roles.type.customer\");\r\n                    }else if(value == CONSTANTS.ROLE_TYPE.PROVINCE){\r\n                        return me.tranService.translate(\"roles.type.province\");\r\n                    }else if(value == CONSTANTS.ROLE_TYPE.TELLER){\r\n                        return me.tranService.translate(\"roles.type.teller\");\r\n                    }else if(value == CONSTANTS.ROLE_TYPE.AGENCY){\r\n                        return me.tranService.translate(\"roles.type.agency\");\r\n                    }else if(value == CONSTANTS.ROLE_TYPE.ALL){\r\n                        return me.tranService.translate(\"roles.type.all\");\r\n                    }else {\r\n                        return \"\";\r\n                    }\r\n                },\r\n            },\r\n        ]\r\n\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n\r\n        // throw new Error('Method not implemented.');\r\n      }\r\n      onSubmitSearch(){\r\n        this.pageNumber = 0;\r\n        this.search(0, this.pageSize, this.sort, this.searchInfo);\r\n     }\r\n\r\n    search(page, limit, sort, params){\r\n        this.pageNumber = page;\r\n        this.pageSize = limit;\r\n        this.sort = sort;\r\n        let me = this;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort,\r\n        }\r\n\r\n        Object.keys(this.searchInfo).forEach(key => {\r\n            if(this.searchInfo[key] != null){\r\n                dataParams[key] = this.searchInfo[key];\r\n            }\r\n        })\r\n\r\n        me.messageCommonService.onload();\r\n        this.rolesService.search(dataParams, (response)=>{\r\n            me.dataSet = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n        }, null, ()=>{\r\n          me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    getType(value){\r\n        if(value == CONSTANTS.ROLE_TYPE.ADMIN){\r\n            return this.tranService.translate(\"roles.type.admin\");\r\n        }else if(value == CONSTANTS.ROLE_TYPE.CUSTOMER){\r\n            return this.tranService.translate(\"roles.type.customer\");\r\n        }else if(value == CONSTANTS.ROLE_TYPE.PROVINCE){\r\n            return this.tranService.translate(\"roles.type.province\");\r\n        }else if(value == CONSTANTS.ROLE_TYPE.TELLER){\r\n            return this.tranService.translate(\"roles.type.teller\");\r\n        }else if(value == CONSTANTS.ROLE_TYPE.AGENCY){\r\n            return this.tranService.translate(\"roles.type.agency\");\r\n        }else if(value == CONSTANTS.ROLE_TYPE.ALL){\r\n            return this.tranService.translate(\"roles.type.all\");\r\n        }\r\n        return \"\";\r\n    }\r\n\r\n    getStatus(value){\r\n        if(value == CONSTANTS.ROlES_STATUS.INACTIVE){\r\n            return this.tranService.translate(\"roles.status.inactive\");\r\n        }else if(value == CONSTANTS.ROlES_STATUS.ACTIVE){\r\n            return this.tranService.translate(\"roles.status.active\");\r\n        }\r\n        return \"\";\r\n    }\r\n\r\n    getDetail(){\r\n        let me = this;\r\n        me.messageCommonService.onload();\r\n        me.rolesService.getById(me.roleId, (response)=>{\r\n            me.roleInfo.name = response.name\r\n            me.roleInfo.type = response.type\r\n            me.roleInfo.status = response.status\r\n            me.roleInfo.permissionIds = response.permissionIds\r\n            me.getTreeRoles();\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    getTreeRoles(){\r\n        let me = this;\r\n        me.rolesService.getTreeRoles((response)=>{\r\n            response.forEach(el => {\r\n                el.label = me.tranService.translate(`permission.${el.key}.${el.key}`)\r\n                el.partialSelected = true;\r\n                if(el.children){\r\n                    el.children.forEach(item => {\r\n                        item.label = me.tranService.translate(`permission.${el.key}.${item.key}`, {}, item.data?.description)\r\n                    })\r\n                }\r\n            });\r\n            me.dataSetForDetail = {\r\n                content: [{\r\n                    label: this.tranService.translate(\"global.text.all\"),\r\n                    key: \"all\",\r\n                    children: response,\r\n                    data: null,\r\n                    expanded: true,\r\n                    partialSelected: true\r\n                }],\r\n                total: 0\r\n            }\r\n            // let permissionIds = [1, 2, 3, 4, 5];\r\n            me.roleInfo.roles = [];\r\n            let totalOfTotal = 0;\r\n            me.dataSetForDetail.content[0].children.forEach(el => {\r\n                if(el.children != null){\r\n                    let total = 0;\r\n                    el.children.forEach(item => {\r\n                        if(this.roleInfo.permissionIds.includes(item.data.id)){\r\n                            me.roleInfo.roles.push(item);\r\n                            total ++;\r\n                        }\r\n                    });\r\n                    if(total != 0 && total == el.children.length){\r\n                        me.roleInfo.roles.push(el);\r\n                        el.partialSelected = false;\r\n                        totalOfTotal ++;\r\n                    }else if(total == 0){\r\n                        el.partialSelected = false;\r\n                    }\r\n                }\r\n            })\r\n            if(totalOfTotal != 0 && totalOfTotal == me.dataSetForDetail.content[0].children.length){\r\n                let element = me.dataSetForDetail.content[0];\r\n                element.partialSelected = false;\r\n                me.roleInfo.roles.push(element);\r\n            }\r\n        })\r\n\r\n    }\r\n\r\n    closeForm(){\r\n        this.router.navigate(['/roles'])\r\n    }\r\n\r\n      ngAfterContentChecked(): void {\r\n        // throw new Error('Method not implemented.');\r\n      }\r\n\r\n    protected readonly CONSTANTS = CONSTANTS;\r\n}\r\n", "<style>\r\n    /* .col-3{\r\n        padding: 10px;\r\n    } */\r\n</style>\r\n\r\n<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{this.tranService.translate(\"global.menu.listroles\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"col-5 flex flex-row justify-content-end align-items-center\">\r\n        <p-button styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.create')\" icon=\"pi pi-plus\"\r\n                  *ngIf=\"checkAuthen([CONSTANTS.PERMISSIONS.ROLE.CREATE])\"\r\n                  [routerLink]=\"['/roles/create']\" routerLinkActive=\"router-link-active\"></p-button>\r\n    </div>\r\n</div>\r\n\r\n<form [formGroup]=\"formSearchRoles\" (ngSubmit)=\"onSubmitSearch()\" class=\"pb-2 pt-3 vnpt-field-set\">\r\n    <p-panel [toggleable]=\"true\" [header]=\"tranService.translate('global.text.filter')\">\r\n        <div class=\"grid search-grid-3\">\r\n            <!-- loai nhom quyen -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\" [showClear]=\"true\"\r\n                                id=\"type\" [autoDisplayFirst]=\"false\"\r\n                                [(ngModel)]=\"searchInfo.type\"\r\n                                formControlName=\"type\"\r\n                                [options]=\"roleType\"\r\n                                optionLabel=\"name\"\r\n                                optionValue=\"value\"\r\n                    ></p-dropdown>\r\n                    <label for=\"type\">{{tranService.translate(\"roles.label.usertype\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- ten nhom quyen -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input pInputText\r\n                            class=\"w-full\"\r\n                            pInputText id=\"name\"\r\n                            [(ngModel)]=\"searchInfo.name\"\r\n                            formControlName=\"name\"\r\n                    />\r\n                    <label htmlFor=\"name\">{{tranService.translate(\"roles.label.rolename\")}}</label>\r\n                </span>\r\n            </div>\r\n            <div class=\"col-3 pb-0\">\r\n                <p-button icon=\"pi pi-search\"\r\n                            styleClass=\"p-button-rounded p-button-secondary p-button-text button-search\"\r\n                            type=\"submit\"\r\n                ></p-button>\r\n            </div>\r\n        </div>\r\n    </p-panel>\r\n</form>\r\n\r\n<table-vnpt\r\n    [fieldId]=\"'id'\"\r\n    [(selectItems)]=\"selectItems\"\r\n    [columns]=\"columns\"\r\n    [dataSet]=\"dataSet\"\r\n    [options]=\"optionTable\"\r\n    [loadData]=\"search.bind(this)\"\r\n    [pageNumber]=\"pageNumber\"\r\n    [pageSize]=\"pageSize\"\r\n    [sort]=\"sort\"\r\n    [params]=\"searchInfo\"\r\n    [labelTable]=\"this.tranService.translate('global.menu.listroles')\"\r\n></table-vnpt>\r\n\r\n<div class=\"flex justify-content-center dialog-vnpt\">\r\n    <p-dialog\r\n        [header]=\"tranService.translate('global.menu.detailroles')\"\r\n        [(visible)]=\"isShowModalDetail\"\r\n        [modal]=\"true\"\r\n        [style]=\"{ width: '980px' }\"\r\n        [draggable]=\"false\"\r\n        [resizable]=\"false\"\r\n    >\r\n        <div>\r\n            <div class=\"flex flex-row justify-content-between role-create\">\r\n                <div style=\"width: 49%;\">\r\n                    <!-- username -->\r\n                    <div class=\"mt-1 ml-5 grid role-info-grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"roles.label.rolename\")}}</span>\r\n                        <span class=\"col\">{{roleInfo.name}}</span>\r\n                    </div>\r\n                    <!-- loai tai khoan -->\r\n                    <div class=\"mt-1 ml-5 grid role-info-grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"roles.label.usertype\")}}</span>\r\n                        <span class=\"col\" >{{getType(roleInfo.type)}}</span>\r\n                    </div>\r\n                    <!-- trang thai -->\r\n                    <div class=\"mt-1 ml-5 grid role-info-grid\">\r\n                        <span style=\"min-width: 200px;max-width: 200px;\" class=\"inline-block col-fixed\">{{tranService.translate(\"roles.label.status\")}}</span>\r\n                        <span class=\"col\" >{{getStatus(roleInfo.status)}}</span>\r\n                    </div>\r\n                </div>\r\n                <div class=\"role-create-div\" style=\"width: 51%;\">\r\n                    <label for=\"roles\" class=\" col-fixed inline-block mt-1 \" style=\"width:180px;\">{{tranService.translate(\"roles.label.rolelist\")}}<span class=\"text-red-500\">*</span></label>\r\n                    <div class=\"col\">\r\n                        <p-tree\r\n                            disabled=\"true\"\r\n                            id=\"roles\"\r\n                            [value]=\"dataSetForDetail.content\"\r\n                            selectionMode=\"checkbox\"\r\n                            class=\"w-full md:w-30rem\"\r\n                            [(selection)]=\"roleInfo.roles\"\r\n                            [style]=\"{'max-height':'500px', 'overflow-y':'scroll'}\"\r\n                        ></p-tree>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n<!--            <div class=\"flex flex-row justify-content-center align-items-center mt-6 mb-3\">-->\r\n<!--                <p-button [label]=\"tranService.translate('global.button.cancel')\" styleClass=\"p-button-secondary p-button-outlined mr-2\" (click)=\"closeForm()\"></p-button>-->\r\n<!--            </div>-->\r\n        </div>\r\n    </p-dialog>\r\n</div>\r\n"], "mappings": "AAKA,SAASA,YAAY,QAAO,sCAAsC;AAIlE,SAASC,SAAS,QAAQ,iCAAiC;AAC3D,SAAQC,aAAa,QAAO,4BAA4B;;;;;;;;;;;;;;;;;;;;ICEhDC,EAAA,CAAAC,SAAA,mBAE4F;;;;IAFvDD,EAAA,CAAAE,UAAA,UAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,yBAAuD,eAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA;;;;;;;;;;;;;;;;;ADIpG,OAAM,MAAOC,qBAAsB,SAAQT,aAAa;EAyCpD;EACEU,YACoCC,YAA0B,EAChDC,WAAwB,EACxBC,QAAkB;IAE5B,KAAK,CAACA,QAAQ,CAAC;IAJiB,KAAAF,YAAY,GAAZA,YAAY;IAClC,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAlCxB,KAAAC,6BAA6B,GAAY,KAAK;IAoB9C,KAAAC,iBAAiB,GAAY,KAAK;IAkBhC;IACA,KAAAC,KAAK,GAAe,EAAE;IAiYL,KAAAjB,SAAS,GAAGA,SAAS;EAnYtC;EAGAkB,QAAQA,CAAA;IACN,IAAIC,EAAE,GAAG,IAAI;IACX,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACC,QAAQ,CAACC,IAAI;IACjD;IACA,IAAI,CAACN,KAAK,GAAG,IAAI,CAACI,cAAc,CAACC,QAAQ,CAACL,KAAK;IAC/C,IAAI,CAACO,KAAK,GAAG,CAAC;MAAEC,KAAK,EAAE,IAAI,CAACnB,WAAW,CAACC,SAAS,CAAC,yBAAyB;IAAC,CAAE,EAAE;MAAEkB,KAAK,EAAE,IAAI,CAACnB,WAAW,CAACC,SAAS,CAAC,uBAAuB;IAAC,CAAE,CAAE;IAClJ,IAAI,CAACmB,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;IACnD,IAAI,CAACC,UAAU,GAAG;MAChBC,IAAI,EAAE,IAAI;MACVP,IAAI,EAAE,IAAI;MACVQ,MAAM,EAAE;KACT;IACD,IAAI,CAACC,eAAe,GAAG,IAAI,CAACnB,WAAW,CAACoB,KAAK,CAAC,IAAI,CAACJ,UAAU,CAAC;IAC9D,IAAI,CAACK,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAE,EAAE;IACjB,IAAI,CAACC,IAAI,GAAG,UAAU;IACtB,IAAI,CAACC,OAAO,GAAE;MACZC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACR;IACD,IAAI,CAACC,gBAAgB,GAAG;MACpBF,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD,IAAI,CAACC,gBAAgB,GAAG;MAClBF,OAAO,EAAE,CAAC;QACNb,KAAK,EAAE,IAAI,CAACnB,WAAW,CAACC,SAAS,CAAC,iBAAiB,CAAC;QACpDkC,GAAG,EAAE,KAAK;QACVC,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE;OACT,CAAC;MACFJ,KAAK,EAAE;KACZ;IACC,IAAIK,YAAY,GAAG,CACf;MAACd,IAAI,EAAE,IAAI,CAACxB,WAAW,CAACC,SAAS,CAAC,kBAAkB,CAAC;MAACsC,KAAK,EAAC7C,SAAS,CAAC8C,SAAS,CAACC,KAAK;MAAEC,OAAO,EAAC,CAAChD,SAAS,CAAC8C,SAAS,CAACC,KAAK;IAAC,CAAC,EAC3H;MAACjB,IAAI,EAAE,IAAI,CAACxB,WAAW,CAACC,SAAS,CAAC,gBAAgB,CAAC;MAACsC,KAAK,EAAC7C,SAAS,CAAC8C,SAAS,CAACG,GAAG;MAAED,OAAO,EAAC;IAAE,CAAC,EAC9F;MAAClB,IAAI,EAAE,IAAI,CAACxB,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MAACsC,KAAK,EAAC7C,SAAS,CAAC8C,SAAS,CAACI,QAAQ;MAACF,OAAO,EAAC,CAAChD,SAAS,CAAC8C,SAAS,CAACC,KAAK,EAAE/C,SAAS,CAAC8C,SAAS,CAACI,QAAQ,EAAElD,SAAS,CAAC8C,SAAS,CAACK,QAAQ,EAAEnD,SAAS,CAAC8C,SAAS,CAACM,MAAM;IAAC,CAAC,EACxN;MAACtB,IAAI,EAAE,IAAI,CAACxB,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MAACsC,KAAK,EAAC7C,SAAS,CAAC8C,SAAS,CAACK,QAAQ;MAACH,OAAO,EAAC,CAAChD,SAAS,CAAC8C,SAAS,CAACC,KAAK;IAAC,CAAC,EAChI;MAACjB,IAAI,EAAE,IAAI,CAACxB,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;MAACsC,KAAK,EAAC7C,SAAS,CAAC8C,SAAS,CAACM,MAAM;MAACJ,OAAO,EAAC,CAAChD,SAAS,CAAC8C,SAAS,CAACC,KAAK,EAAE/C,SAAS,CAAC8C,SAAS,CAACK,QAAQ;IAAC,CAAC,EAC1J;MAACrB,IAAI,EAAE,IAAI,CAACxB,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;MAACsC,KAAK,EAAC7C,SAAS,CAAC8C,SAAS,CAACO,MAAM;MAACL,OAAO,EAAC,CAAChD,SAAS,CAAC8C,SAAS,CAACC,KAAK,EAAE/C,SAAS,CAAC8C,SAAS,CAACK,QAAQ,EAAEnD,SAAS,CAAC8C,SAAS,CAACM,MAAM,EAAEpD,SAAS,CAAC8C,SAAS,CAACO,MAAM;IAAC,CAAC,CACrN;IACD,IAAI,CAACC,QAAQ,GAAGV,YAAY,CAACW,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACR,OAAO,CAACS,QAAQ,CAAC,IAAI,CAACrC,QAAQ,CAAC,IAAIoC,EAAE,CAACR,OAAO,CAACU,MAAM,IAAI,CAAC,CAAC;IACzG,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,QAAQ,GAAG;MACd9B,IAAI,EAAE,IAAI;MACVP,IAAI,EAAE,IAAI;MACVQ,MAAM,EAAE,IAAI;MACZd,KAAK,EAAE,IAAI;MACX4C,WAAW,EAAE,IAAI;MACjBC,aAAa,EAAE;KAChB;IACD,IAAI,CAACC,WAAW,GAAG;MACjBC,gBAAgB,EAAE,IAAI;MACtBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE,KAAK;MAC1BC,MAAM,EAAE,CACJ;QACIzC,IAAI,EAAE,cAAc;QACpB0C,OAAO,EAAE,IAAI,CAAC/D,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACzD+D,IAAI,EAAE,SAAAA,CAASC,EAAE,EAAEC,IAAI;UACnBrD,EAAE,CAACsD,MAAM,CAACC,QAAQ,CAAC,CAAC,eAAeH,EAAE,EAAE,CAAC,CAAC;QAC7C,CAAC;QACDI,UAAU,EAAE,SAAAA,CAASJ,EAAE,EAAEC,IAAI;UAC3B;UACE;UACA,IAAIrD,EAAE,CAACC,QAAQ,IAAIpB,SAAS,CAAC4E,SAAS,CAAC1B,QAAQ,EAAE;YAC7C;YACA,IAAI,CAAC/B,EAAE,CAACF,KAAK,IAAI,EAAE,EAAEwC,QAAQ,CAACe,IAAI,CAAC1C,IAAI,CAAC,EAAE;cACtC,OAAO,KAAK;aACf,MAAM;cACH,OAAO,IAAI;;;UAGnB,OAAO,CAACX,EAAE,CAACC,QAAQ,IAAIpB,SAAS,CAAC4E,SAAS,CAAC7B,KAAK,IAAKyB,IAAI,CAACjD,IAAI,IAAIvB,SAAS,CAAC8C,SAAS,CAACG,GAAG,KAAKuB,IAAI,CAACK,SAAS,IAAI,CAAC;UAAC;QACtH;OACH,EACD;QACIlD,IAAI,EAAE,YAAY;QAClB0C,OAAO,EAAE,IAAI,CAAC/D,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;QACjE+D,IAAI,EAAE,SAAAA,CAASC,EAAE,EAAEC,IAAI;UACnBrD,EAAE,CAAC2D,oBAAoB,CAACC,OAAO,CAC3B5D,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,6CAA6C,CAAC,EACvEY,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,wCAAwC,CAAC,EAClE;YACIyE,EAAE,EAACA,CAAA,KAAI;cACH7D,EAAE,CAACP,YAAY,CAACqE,gBAAgB,CAACV,EAAE,EAAGW,QAAQ,IAAG;gBAC7C/D,EAAE,CAAC2D,oBAAoB,CAACK,OAAO,CAAChE,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC,CAAC;gBAC/FY,EAAE,CAACiE,MAAM,CAACjE,EAAE,CAACe,UAAU,EAAEf,EAAE,CAACgB,QAAQ,EAAEhB,EAAE,CAACiB,IAAI,EAAEjB,EAAE,CAACU,UAAU,CAAC;cAEjE,CAAC,CAAC;cACJ;YACF,CAAC;;YACDwD,MAAM,EAAEA,CAAA,KAAI;cACR;YAAA;WAEP,CACJ;QACL,CAAC;QACDV,UAAU,EAAE,SAAAA,CAASJ,EAAE,EAAEC,IAAI;UACzB;UACA,IAAIrD,EAAE,CAACC,QAAQ,IAAIpB,SAAS,CAAC4E,SAAS,CAAC1B,QAAQ,EAAE;YAC7C;YACA,IAAI,CAAC/B,EAAE,CAACF,KAAK,IAAI,EAAE,EAAEwC,QAAQ,CAACe,IAAI,CAAC1C,IAAI,CAAC,EAAE;cACtC,OAAO,KAAK;aACf,MAAM;cACH,OAAO,IAAI;;;UAGnB,OAAQ0C,IAAI,CAACzC,MAAM,IAAI/B,SAAS,CAACsF,YAAY,CAACC,MAAM,KAAKpE,EAAE,CAACC,QAAQ,IAAIpB,SAAS,CAAC4E,SAAS,CAAC7B,KAAK,IAAKyB,IAAI,CAACjD,IAAI,IAAIvB,SAAS,CAAC8C,SAAS,CAACG,GAAG,CAAC,IAAKuB,IAAI,CAACK,SAAS,IAAI,CAAC;QACvK;OACH,EACD;QACIlD,IAAI,EAAE,iBAAiB;QACvB0C,OAAO,EAAE,IAAI,CAAC/D,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;QACjE+D,IAAI,EAAE,SAAAA,CAASC,EAAE,EAAEC,IAAI;UACnBrD,EAAE,CAAC2D,oBAAoB,CAACC,OAAO,CAC3B5D,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,6CAA6C,CAAC,EACvEY,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,wCAAwC,CAAC,EAClE;YACIyE,EAAE,EAACA,CAAA,KAAI;cACL;cACE7D,EAAE,CAACP,YAAY,CAACqE,gBAAgB,CAACV,EAAE,EAAGW,QAAQ,IAAG;gBAC7C/D,EAAE,CAAC2D,oBAAoB,CAACK,OAAO,CAAChE,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC,CAAC;gBAC/FY,EAAE,CAACiE,MAAM,CAACjE,EAAE,CAACe,UAAU,EAAEf,EAAE,CAACgB,QAAQ,EAAEhB,EAAE,CAACiB,IAAI,EAAEjB,EAAE,CAACU,UAAU,CAAC;cACjE,CAAC,CAAC;cACFV,EAAE,CAACiE,MAAM,CAACjE,EAAE,CAACe,UAAU,EAAEf,EAAE,CAACgB,QAAQ,EAAEhB,EAAE,CAACiB,IAAI,EAAEjB,EAAE,CAACU,UAAU,CAAC;YACjE,CAAC;YACDwD,MAAM,EAAEA,CAAA,KAAI;cACR;YAAA;WAEP,CACJ;QACL,CAAC;QACDV,UAAU,EAAE,SAAAA,CAASJ,EAAE,EAAEC,IAAI;UACzB,OAAQA,IAAI,CAACzC,MAAM,IAAI/B,SAAS,CAACsF,YAAY,CAACE,QAAQ,KAAKrE,EAAE,CAACC,QAAQ,IAAIpB,SAAS,CAAC4E,SAAS,CAAC7B,KAAK,IAAIyB,IAAI,CAACjD,IAAI,IAAIvB,SAAS,CAAC8C,SAAS,CAACG,GAAG,CAAC,IAAKuB,IAAI,CAACK,SAAS,IAAI,CAAC;QACxK;OACH,EACD;QACIlD,IAAI,EAAE,aAAa;QACnB0C,OAAO,EAAE,IAAI,CAAC/D,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QAC3D+D,IAAI,EAAE,SAAAA,CAASC,EAAE,EAAEC,IAAI;UACnBrD,EAAE,CAAC2D,oBAAoB,CAACC,OAAO,CAC3B5D,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,wCAAwC,CAAC,EAClEY,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,mCAAmC,CAAC,EAC7D;YACIyE,EAAE,EAACA,CAAA,KAAI;cACH7D,EAAE,CAACP,YAAY,CAAC6E,UAAU,CAAClB,EAAE,EAAGW,QAAQ,IAAG;gBACvC/D,EAAE,CAAC2D,oBAAoB,CAACK,OAAO,CAAChE,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC,CAAC;gBACzFY,EAAE,CAACiE,MAAM,CAACjE,EAAE,CAACe,UAAU,EAAEf,EAAE,CAACgB,QAAQ,EAAEhB,EAAE,CAACiB,IAAI,EAAEjB,EAAE,CAACU,UAAU,CAAC;cACjE,CAAC,CAAC;YACN,CAAC;YACDwD,MAAM,EAAEA,CAAA,KAAI;cACR;YAAA;WAEP,CACJ;QACL,CAAC;QACDV,UAAU,EAAE,SAAAA,CAASJ,EAAE,EAAEC,IAAI;UACzB;UACA,IAAIrD,EAAE,CAACC,QAAQ,IAAIpB,SAAS,CAAC4E,SAAS,CAAC1B,QAAQ,EAAE;YAC7C;YACA,IAAI,CAAC/B,EAAE,CAACF,KAAK,IAAI,EAAE,EAAEwC,QAAQ,CAACe,IAAI,CAAC1C,IAAI,CAAC,EAAE;cACtC,OAAO,KAAK;aACf,MAAM;cACH,OAAO,IAAI;;;UAGnB,OAAO,CAACX,EAAE,CAACC,QAAQ,IAAIpB,SAAS,CAAC4E,SAAS,CAAC7B,KAAK,IAAIyB,IAAI,CAACjD,IAAI,IAAIvB,SAAS,CAAC8C,SAAS,CAACG,GAAG,KAAKuB,IAAI,CAACK,SAAS,IAAI,CAAC;QACpH;OACH;KAER,EAEC,IAAI,CAACa,OAAO,GAAG,CACb;MACI5D,IAAI,EAAE,IAAI,CAACxB,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACxDkC,GAAG,EAAE,MAAM;MACXkD,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAC;QACFC,MAAM,EAAE,SAAS;QACtBC,KAAK,EAAE;OACL;MACDC,SAASA,CAAC3B,EAAE,EAAEC,IAAI;QACdrD,EAAE,CAACgF,MAAM,GAAG5B,EAAE;QACdpD,EAAE,CAACiF,SAAS,EAAE;QACdjF,EAAE,CAACH,iBAAiB,GAAG,IAAI;MAC/B;KACH,EACD;MACIc,IAAI,EAAE,IAAI,CAACxB,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;MACtDkC,GAAG,EAAE,QAAQ;MACbkD,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE;QACHE,KAAK,EAAE;OACV;MACDI,gBAAgB,EAAGxD,KAAK,IAAI;QACxB,IAAGA,KAAK,IAAI,CAAC,EAAC;UACV,OAAO,CAAC,KAAK,EAAC,gBAAgB,EAAE,cAAc,EAAC,cAAc,EAAC,cAAc,CAAC;SAChF,MAAK,IAAGA,KAAK,IAAI7C,SAAS,CAACsF,YAAY,CAACE,QAAQ,EAAC;UAC9C,OAAO,CAAC,KAAK,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAC,cAAc,CAAC;;QAE/E,OAAO,EAAE;MACb,CAAC;MACDc,eAAe,EAAGzD,KAAK,IAAG;QACtB,IAAGA,KAAK,IAAI,CAAC,EAAC;UACV,OAAO1B,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;SACzD,MAAK,IAAGsC,KAAK,IAAI7C,SAAS,CAACsF,YAAY,CAACE,QAAQ,EAAC;UAC9C,OAAOrE,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;;QAE5D,OAAO,EAAE;MACb;KACD,EACD;MACIuB,IAAI,EAAE,IAAI,CAACxB,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACxDkC,GAAG,EAAE,MAAM;MACXkD,IAAI,EAAE,KAAK;MACXC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZQ,eAAeA,CAACzD,KAAK;QACjB,IAAGA,KAAK,IAAI7C,SAAS,CAAC8C,SAAS,CAACC,KAAK,EAAC;UAClC,OAAO5B,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,kBAAkB,CAAC;SACtD,MAAK,IAAGsC,KAAK,IAAI7C,SAAS,CAAC8C,SAAS,CAACI,QAAQ,EAAC;UAC3C,OAAO/B,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;SACzD,MAAK,IAAGsC,KAAK,IAAI7C,SAAS,CAAC8C,SAAS,CAACK,QAAQ,EAAC;UAC3C,OAAOhC,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;SACzD,MAAK,IAAGsC,KAAK,IAAI7C,SAAS,CAAC8C,SAAS,CAACM,MAAM,EAAC;UACzC,OAAOjC,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;SACvD,MAAK,IAAGsC,KAAK,IAAI7C,SAAS,CAAC8C,SAAS,CAACO,MAAM,EAAC;UACzC,OAAOlC,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;SACvD,MAAK,IAAGsC,KAAK,IAAI7C,SAAS,CAAC8C,SAAS,CAACG,GAAG,EAAC;UACtC,OAAO9B,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,gBAAgB,CAAC;SACpD,MAAK;UACF,OAAO,EAAE;;MAEjB;KACH,CACJ;IAED,IAAI,CAAC6E,MAAM,CAAC,IAAI,CAAClD,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACP,UAAU,CAAC;IAEvE;EACF;;EACA0E,cAAcA,CAAA;IACZ,IAAI,CAACrE,UAAU,GAAG,CAAC;IACnB,IAAI,CAACkD,MAAM,CAAC,CAAC,EAAE,IAAI,CAACjD,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACP,UAAU,CAAC;EAC5D;EAEDuD,MAAMA,CAACoB,IAAI,EAAEC,KAAK,EAAErE,IAAI,EAAEsE,MAAM;IAC5B,IAAI,CAACxE,UAAU,GAAGsE,IAAI;IACtB,IAAI,CAACrE,QAAQ,GAAGsE,KAAK;IACrB,IAAI,CAACrE,IAAI,GAAGA,IAAI;IAChB,IAAIjB,EAAE,GAAG,IAAI;IACb,IAAIwF,UAAU,GAAG;MACbH,IAAI;MACJb,IAAI,EAAEc,KAAK;MACXrE;KACH;IAEDwE,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChF,UAAU,CAAC,CAACiF,OAAO,CAACrE,GAAG,IAAG;MACvC,IAAG,IAAI,CAACZ,UAAU,CAACY,GAAG,CAAC,IAAI,IAAI,EAAC;QAC5BkE,UAAU,CAAClE,GAAG,CAAC,GAAG,IAAI,CAACZ,UAAU,CAACY,GAAG,CAAC;;IAE9C,CAAC,CAAC;IAEFtB,EAAE,CAAC2D,oBAAoB,CAACiC,MAAM,EAAE;IAChC,IAAI,CAACnG,YAAY,CAACwE,MAAM,CAACuB,UAAU,EAAGzB,QAAQ,IAAG;MAC7C/D,EAAE,CAACkB,OAAO,GAAG;QACTC,OAAO,EAAE4C,QAAQ,CAAC5C,OAAO;QACzBC,KAAK,EAAE2C,QAAQ,CAAC8B;OACnB;IACL,CAAC,EAAE,IAAI,EAAE,MAAI;MACX7F,EAAE,CAAC2D,oBAAoB,CAACmC,OAAO,EAAE;IACnC,CAAC,CAAC;EACN;EAEAC,OAAOA,CAACrE,KAAK;IACT,IAAGA,KAAK,IAAI7C,SAAS,CAAC8C,SAAS,CAACC,KAAK,EAAC;MAClC,OAAO,IAAI,CAACzC,WAAW,CAACC,SAAS,CAAC,kBAAkB,CAAC;KACxD,MAAK,IAAGsC,KAAK,IAAI7C,SAAS,CAAC8C,SAAS,CAACI,QAAQ,EAAC;MAC3C,OAAO,IAAI,CAAC5C,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;KAC3D,MAAK,IAAGsC,KAAK,IAAI7C,SAAS,CAAC8C,SAAS,CAACK,QAAQ,EAAC;MAC3C,OAAO,IAAI,CAAC7C,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;KAC3D,MAAK,IAAGsC,KAAK,IAAI7C,SAAS,CAAC8C,SAAS,CAACM,MAAM,EAAC;MACzC,OAAO,IAAI,CAAC9C,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;KACzD,MAAK,IAAGsC,KAAK,IAAI7C,SAAS,CAAC8C,SAAS,CAACO,MAAM,EAAC;MACzC,OAAO,IAAI,CAAC/C,WAAW,CAACC,SAAS,CAAC,mBAAmB,CAAC;KACzD,MAAK,IAAGsC,KAAK,IAAI7C,SAAS,CAAC8C,SAAS,CAACG,GAAG,EAAC;MACtC,OAAO,IAAI,CAAC3C,WAAW,CAACC,SAAS,CAAC,gBAAgB,CAAC;;IAEvD,OAAO,EAAE;EACb;EAEA4G,SAASA,CAACtE,KAAK;IACX,IAAGA,KAAK,IAAI7C,SAAS,CAACsF,YAAY,CAACE,QAAQ,EAAC;MACxC,OAAO,IAAI,CAAClF,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC;KAC7D,MAAK,IAAGsC,KAAK,IAAI7C,SAAS,CAACsF,YAAY,CAACC,MAAM,EAAC;MAC5C,OAAO,IAAI,CAACjF,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;;IAE5D,OAAO,EAAE;EACb;EAEA6F,SAASA,CAAA;IACL,IAAIjF,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC2D,oBAAoB,CAACiC,MAAM,EAAE;IAChC5F,EAAE,CAACP,YAAY,CAACwG,OAAO,CAACjG,EAAE,CAACgF,MAAM,EAAGjB,QAAQ,IAAG;MAC3C/D,EAAE,CAACyC,QAAQ,CAAC9B,IAAI,GAAGoD,QAAQ,CAACpD,IAAI;MAChCX,EAAE,CAACyC,QAAQ,CAACrC,IAAI,GAAG2D,QAAQ,CAAC3D,IAAI;MAChCJ,EAAE,CAACyC,QAAQ,CAAC7B,MAAM,GAAGmD,QAAQ,CAACnD,MAAM;MACpCZ,EAAE,CAACyC,QAAQ,CAACE,aAAa,GAAGoB,QAAQ,CAACpB,aAAa;MAClD3C,EAAE,CAACkG,YAAY,EAAE;IACrB,CAAC,EAAE,IAAI,EAAE,MAAI;MACTlG,EAAE,CAAC2D,oBAAoB,CAACmC,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAI,YAAYA,CAAA;IACR,IAAIlG,EAAE,GAAG,IAAI;IACbA,EAAE,CAACP,YAAY,CAACyG,YAAY,CAAEnC,QAAQ,IAAG;MACrCA,QAAQ,CAAC4B,OAAO,CAACtD,EAAE,IAAG;QAClBA,EAAE,CAAC/B,KAAK,GAAGN,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,cAAciD,EAAE,CAACf,GAAG,IAAIe,EAAE,CAACf,GAAG,EAAE,CAAC;QACrEe,EAAE,CAAC8D,eAAe,GAAG,IAAI;QACzB,IAAG9D,EAAE,CAACd,QAAQ,EAAC;UACXc,EAAE,CAACd,QAAQ,CAACoE,OAAO,CAACtC,IAAI,IAAG;YACvBA,IAAI,CAAC/C,KAAK,GAAGN,EAAE,CAACb,WAAW,CAACC,SAAS,CAAC,cAAciD,EAAE,CAACf,GAAG,IAAI+B,IAAI,CAAC/B,GAAG,EAAE,EAAE,EAAE,EAAE+B,IAAI,CAAC7B,IAAI,EAAEkB,WAAW,CAAC;UACzG,CAAC,CAAC;;MAEV,CAAC,CAAC;MACF1C,EAAE,CAACqB,gBAAgB,GAAG;QAClBF,OAAO,EAAE,CAAC;UACNb,KAAK,EAAE,IAAI,CAACnB,WAAW,CAACC,SAAS,CAAC,iBAAiB,CAAC;UACpDkC,GAAG,EAAE,KAAK;UACVC,QAAQ,EAAEwC,QAAQ;UAClBvC,IAAI,EAAE,IAAI;UACV4E,QAAQ,EAAE,IAAI;UACdD,eAAe,EAAE;SACpB,CAAC;QACF/E,KAAK,EAAE;OACV;MACD;MACApB,EAAE,CAACyC,QAAQ,CAAC3C,KAAK,GAAG,EAAE;MACtB,IAAIuG,YAAY,GAAG,CAAC;MACpBrG,EAAE,CAACqB,gBAAgB,CAACF,OAAO,CAAC,CAAC,CAAC,CAACI,QAAQ,CAACoE,OAAO,CAACtD,EAAE,IAAG;QACjD,IAAGA,EAAE,CAACd,QAAQ,IAAI,IAAI,EAAC;UACnB,IAAIH,KAAK,GAAG,CAAC;UACbiB,EAAE,CAACd,QAAQ,CAACoE,OAAO,CAACtC,IAAI,IAAG;YACvB,IAAG,IAAI,CAACZ,QAAQ,CAACE,aAAa,CAACL,QAAQ,CAACe,IAAI,CAAC7B,IAAI,CAAC4B,EAAE,CAAC,EAAC;cAClDpD,EAAE,CAACyC,QAAQ,CAAC3C,KAAK,CAACwG,IAAI,CAACjD,IAAI,CAAC;cAC5BjC,KAAK,EAAG;;UAEhB,CAAC,CAAC;UACF,IAAGA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAIiB,EAAE,CAACd,QAAQ,CAACgB,MAAM,EAAC;YACzCvC,EAAE,CAACyC,QAAQ,CAAC3C,KAAK,CAACwG,IAAI,CAACjE,EAAE,CAAC;YAC1BA,EAAE,CAAC8D,eAAe,GAAG,KAAK;YAC1BE,YAAY,EAAG;WAClB,MAAK,IAAGjF,KAAK,IAAI,CAAC,EAAC;YAChBiB,EAAE,CAAC8D,eAAe,GAAG,KAAK;;;MAGtC,CAAC,CAAC;MACF,IAAGE,YAAY,IAAI,CAAC,IAAIA,YAAY,IAAIrG,EAAE,CAACqB,gBAAgB,CAACF,OAAO,CAAC,CAAC,CAAC,CAACI,QAAQ,CAACgB,MAAM,EAAC;QACnF,IAAIgE,OAAO,GAAGvG,EAAE,CAACqB,gBAAgB,CAACF,OAAO,CAAC,CAAC,CAAC;QAC5CoF,OAAO,CAACJ,eAAe,GAAG,KAAK;QAC/BnG,EAAE,CAACyC,QAAQ,CAAC3C,KAAK,CAACwG,IAAI,CAACC,OAAO,CAAC;;IAEvC,CAAC,CAAC;EAEN;EAEAC,SAASA,CAAA;IACL,IAAI,CAAClD,MAAM,CAACC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EACpC;EAEEkD,qBAAqBA,CAAA;IACnB;EAAA;;;uBAhbKlH,qBAAqB,EAAAR,EAAA,CAAA2H,iBAAA,CA2Cd9H,YAAY,GAAAG,EAAA,CAAA2H,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7H,EAAA,CAAA2H,iBAAA,CAAA3H,EAAA,CAAA8H,QAAA;IAAA;EAAA;;;YA3CnBtH,qBAAqB;MAAAuH,SAAA;MAAAC,QAAA,GAAAhI,EAAA,CAAAiI,0BAAA;MAAAC,KAAA,EAAAC,GAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVlCzI,EAAA,CAAA2I,cAAA,aAAqG;UAEzD3I,EAAA,CAAA4I,MAAA,GAAuD;UAAA5I,EAAA,CAAA6I,YAAA,EAAM;UACjG7I,EAAA,CAAAC,SAAA,sBAAoF;UACxFD,EAAA,CAAA6I,YAAA,EAAM;UACN7I,EAAA,CAAA2I,cAAA,aAAwE;UACpE3I,EAAA,CAAA8I,UAAA,IAAAC,yCAAA,sBAE4F;UAChG/I,EAAA,CAAA6I,YAAA,EAAM;UAGV7I,EAAA,CAAA2I,cAAA,cAAmG;UAA/D3I,EAAA,CAAAgJ,UAAA,sBAAAC,wDAAA;YAAA,OAAYP,GAAA,CAAArC,cAAA,EAAgB;UAAA,EAAC;UAC7DrG,EAAA,CAAA2I,cAAA,iBAAoF;UAOxD3I,EAAA,CAAAgJ,UAAA,2BAAAE,oEAAAC,MAAA;YAAA,OAAAT,GAAA,CAAA/G,UAAA,CAAAN,IAAA,GAAA8H,MAAA;UAAA,EAA6B;UAKxCnJ,EAAA,CAAA6I,YAAA,EAAa;UACd7I,EAAA,CAAA2I,cAAA,iBAAkB;UAAA3I,EAAA,CAAA4I,MAAA,IAAiD;UAAA5I,EAAA,CAAA6I,YAAA,EAAQ;UAInF7I,EAAA,CAAA2I,cAAA,cAAmB;UAKH3I,EAAA,CAAAgJ,UAAA,2BAAAI,+DAAAD,MAAA;YAAA,OAAAT,GAAA,CAAA/G,UAAA,CAAAC,IAAA,GAAAuH,MAAA;UAAA,EAA6B;UAHrCnJ,EAAA,CAAA6I,YAAA,EAKE;UACF7I,EAAA,CAAA2I,cAAA,iBAAsB;UAAA3I,EAAA,CAAA4I,MAAA,IAAiD;UAAA5I,EAAA,CAAA6I,YAAA,EAAQ;UAGvF7I,EAAA,CAAA2I,cAAA,eAAwB;UACpB3I,EAAA,CAAAC,SAAA,oBAGY;UAChBD,EAAA,CAAA6I,YAAA,EAAM;UAKlB7I,EAAA,CAAA2I,cAAA,sBAYC;UAVG3I,EAAA,CAAAgJ,UAAA,+BAAAK,wEAAAF,MAAA;YAAA,OAAAT,GAAA,CAAAjF,WAAA,GAAA0F,MAAA;UAAA,EAA6B;UAUhCnJ,EAAA,CAAA6I,YAAA,EAAa;UAEd7I,EAAA,CAAA2I,cAAA,eAAqD;UAG7C3I,EAAA,CAAAgJ,UAAA,2BAAAM,kEAAAH,MAAA;YAAA,OAAAT,GAAA,CAAA5H,iBAAA,GAAAqI,MAAA;UAAA,EAA+B;UAM/BnJ,EAAA,CAAA2I,cAAA,WAAK;UAK2F3I,EAAA,CAAA4I,MAAA,IAAiD;UAAA5I,EAAA,CAAA6I,YAAA,EAAO;UACxI7I,EAAA,CAAA2I,cAAA,gBAAkB;UAAA3I,EAAA,CAAA4I,MAAA,IAAiB;UAAA5I,EAAA,CAAA6I,YAAA,EAAO;UAG9C7I,EAAA,CAAA2I,cAAA,eAA2C;UACyC3I,EAAA,CAAA4I,MAAA,IAAiD;UAAA5I,EAAA,CAAA6I,YAAA,EAAO;UACxI7I,EAAA,CAAA2I,cAAA,gBAAmB;UAAA3I,EAAA,CAAA4I,MAAA,IAA0B;UAAA5I,EAAA,CAAA6I,YAAA,EAAO;UAGxD7I,EAAA,CAAA2I,cAAA,eAA2C;UACyC3I,EAAA,CAAA4I,MAAA,IAA+C;UAAA5I,EAAA,CAAA6I,YAAA,EAAO;UACtI7I,EAAA,CAAA2I,cAAA,gBAAmB;UAAA3I,EAAA,CAAA4I,MAAA,IAA8B;UAAA5I,EAAA,CAAA6I,YAAA,EAAO;UAGhE7I,EAAA,CAAA2I,cAAA,eAAiD;UACiC3I,EAAA,CAAA4I,MAAA,IAAiD;UAAA5I,EAAA,CAAA2I,cAAA,gBAA2B;UAAA3I,EAAA,CAAA4I,MAAA,SAAC;UAAA5I,EAAA,CAAA6I,YAAA,EAAO;UAClK7I,EAAA,CAAA2I,cAAA,eAAiB;UAOT3I,EAAA,CAAAgJ,UAAA,6BAAAO,kEAAAJ,MAAA;YAAA,OAAAT,GAAA,CAAAhF,QAAA,CAAA3C,KAAA,GAAAoI,MAAA;UAAA,EAA8B;UAEjCnJ,EAAA,CAAA6I,YAAA,EAAS;;;UAtGU7I,EAAA,CAAAwJ,SAAA,GAAuD;UAAvDxJ,EAAA,CAAAyJ,iBAAA,CAAAf,GAAA,CAAAtI,WAAA,CAAAC,SAAA,0BAAuD;UACpDL,EAAA,CAAAwJ,SAAA,GAAe;UAAfxJ,EAAA,CAAAE,UAAA,UAAAwI,GAAA,CAAApH,KAAA,CAAe,SAAAoH,GAAA,CAAAlH,IAAA;UAI3CxB,EAAA,CAAAwJ,SAAA,GAAsD;UAAtDxJ,EAAA,CAAAE,UAAA,SAAAwI,GAAA,CAAAgB,WAAA,CAAA1J,EAAA,CAAA2J,eAAA,KAAAC,GAAA,EAAAlB,GAAA,CAAA5I,SAAA,CAAA+J,WAAA,CAAAC,IAAA,CAAAC,MAAA,GAAsD;UAKnE/J,EAAA,CAAAwJ,SAAA,GAA6B;UAA7BxJ,EAAA,CAAAE,UAAA,cAAAwI,GAAA,CAAA5G,eAAA,CAA6B;UACtB9B,EAAA,CAAAwJ,SAAA,GAAmB;UAAnBxJ,EAAA,CAAAE,UAAA,oBAAmB,WAAAwI,GAAA,CAAAtI,WAAA,CAAAC,SAAA;UAKoBL,EAAA,CAAAwJ,SAAA,GAAkB;UAAlBxJ,EAAA,CAAAE,UAAA,mBAAkB,uCAAAwI,GAAA,CAAA/G,UAAA,CAAAN,IAAA,aAAAqH,GAAA,CAAAtF,QAAA;UAQhCpD,EAAA,CAAAwJ,SAAA,GAAiD;UAAjDxJ,EAAA,CAAAyJ,iBAAA,CAAAf,GAAA,CAAAtI,WAAA,CAAAC,SAAA,yBAAiD;UAS3DL,EAAA,CAAAwJ,SAAA,GAA6B;UAA7BxJ,EAAA,CAAAE,UAAA,YAAAwI,GAAA,CAAA/G,UAAA,CAAAC,IAAA,CAA6B;UAGf5B,EAAA,CAAAwJ,SAAA,GAAiD;UAAjDxJ,EAAA,CAAAyJ,iBAAA,CAAAf,GAAA,CAAAtI,WAAA,CAAAC,SAAA,yBAAiD;UAcvFL,EAAA,CAAAwJ,SAAA,GAAgB;UAAhBxJ,EAAA,CAAAE,UAAA,iBAAgB,gBAAAwI,GAAA,CAAAjF,WAAA,aAAAiF,GAAA,CAAAlD,OAAA,aAAAkD,GAAA,CAAAvG,OAAA,aAAAuG,GAAA,CAAA7E,WAAA,cAAA6E,GAAA,CAAAxD,MAAA,CAAA8E,IAAA,CAAAtB,GAAA,iBAAAA,GAAA,CAAA1G,UAAA,cAAA0G,GAAA,CAAAzG,QAAA,UAAAyG,GAAA,CAAAxG,IAAA,YAAAwG,GAAA,CAAA/G,UAAA,gBAAA+G,GAAA,CAAAtI,WAAA,CAAAC,SAAA;UAkBZL,EAAA,CAAAwJ,SAAA,GAA4B;UAA5BxJ,EAAA,CAAAiK,UAAA,CAAAjK,EAAA,CAAAM,eAAA,KAAA4J,GAAA,EAA4B;UAH5BlK,EAAA,CAAAE,UAAA,WAAAwI,GAAA,CAAAtI,WAAA,CAAAC,SAAA,4BAA2D,YAAAqI,GAAA,CAAA5H,iBAAA;UAYqCd,EAAA,CAAAwJ,SAAA,GAAiD;UAAjDxJ,EAAA,CAAAyJ,iBAAA,CAAAf,GAAA,CAAAtI,WAAA,CAAAC,SAAA,yBAAiD;UAC/GL,EAAA,CAAAwJ,SAAA,GAAiB;UAAjBxJ,EAAA,CAAAyJ,iBAAA,CAAAf,GAAA,CAAAhF,QAAA,CAAA9B,IAAA,CAAiB;UAI6C5B,EAAA,CAAAwJ,SAAA,GAAiD;UAAjDxJ,EAAA,CAAAyJ,iBAAA,CAAAf,GAAA,CAAAtI,WAAA,CAAAC,SAAA,yBAAiD;UAC9GL,EAAA,CAAAwJ,SAAA,GAA0B;UAA1BxJ,EAAA,CAAAyJ,iBAAA,CAAAf,GAAA,CAAA1B,OAAA,CAAA0B,GAAA,CAAAhF,QAAA,CAAArC,IAAA,EAA0B;UAImCrB,EAAA,CAAAwJ,SAAA,GAA+C;UAA/CxJ,EAAA,CAAAyJ,iBAAA,CAAAf,GAAA,CAAAtI,WAAA,CAAAC,SAAA,uBAA+C;UAC5GL,EAAA,CAAAwJ,SAAA,GAA8B;UAA9BxJ,EAAA,CAAAyJ,iBAAA,CAAAf,GAAA,CAAAzB,SAAA,CAAAyB,GAAA,CAAAhF,QAAA,CAAA7B,MAAA,EAA8B;UAIyB7B,EAAA,CAAAwJ,SAAA,GAAiD;UAAjDxJ,EAAA,CAAAyJ,iBAAA,CAAAf,GAAA,CAAAtI,WAAA,CAAAC,SAAA,yBAAiD;UASvHL,EAAA,CAAAwJ,SAAA,GAAuD;UAAvDxJ,EAAA,CAAAiK,UAAA,CAAAjK,EAAA,CAAAM,eAAA,KAAA6J,GAAA,EAAuD;UAJvDnK,EAAA,CAAAE,UAAA,UAAAwI,GAAA,CAAApG,gBAAA,CAAAF,OAAA,CAAkC,cAAAsG,GAAA,CAAAhF,QAAA,CAAA3C,KAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}