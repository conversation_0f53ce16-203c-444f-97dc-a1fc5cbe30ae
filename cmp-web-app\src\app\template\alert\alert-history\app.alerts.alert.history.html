<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.alerthistory")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
</div>
<form [formGroup]="formSearchAlertHistory" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid search-grid-3">
            <!-- so thue bao -->
            <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="msisdn"
                           [(ngModel)]="searchInfo.msisdn"
                           formControlName="msisdn"
                    />
                    <label htmlFor="msisdn">{{tranService.translate("sim.label.sothuebao")}}</label>
                </span>
            </div>
            <!-- goi cuoc data pool -->
            <div class="col-3">
               <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="ratingPlanCode"
                           [(ngModel)]="searchInfo.ratingPlanCode"
                           formControlName="ratingPlanCode"
                    />
                    <label htmlFor="ratingPlanCode">{{tranService.translate("historyRegisterPlan.label.ratingPlan")}}</label>
               </span>
            </div>
            <!-- khach hang -->
            <div class="col-3">
               <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="customerName"
                           [(ngModel)]="searchInfo.customerId"
                           formControlName="customerId"
                    />
                    <label htmlFor="customerName">{{tranService.translate('sim.label.khachhangvathue')}}</label>
               </span>
            </div>
            <!-- dieu kien -->
            <div class="col-3">
                <span class="p-float-label">
                <p-dropdown styleClass="w-full"
                            [showClear]="true"
                            id="eventType" [autoDisplayFirst]="false"
                            [(ngModel)]="searchInfo.statusSim"
                            formControlName="statusSim"
                            [options]="eventOptions"
                            optionLabel="name"
                            optionValue="value"
                ></p-dropdown>
                    <label class="label-dropdown" for="statusSim"> {{tranService.translate('alert.label.event')}}</label>
                </span>
            </div>
            <!-- loai hanh dong -->
            <div class="col-3">
                <span class="p-float-label">
                <p-dropdown styleClass="w-full"
                            [showClear]="true" [autoDisplayFirst]="false"
                            id="actionType"
                            [(ngModel)]="searchInfo.actionType"
                            formControlName="actionType"
                            [options]="actionOptions"
                            optionLabel="name"
                            optionValue="value"
                ></p-dropdown>
                    <label class="label-dropdown" for="actionType"> {{tranService.translate('alert.label.action')}}</label>
                </span>
            </div>
            <!-- Mã ví -->
            <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="dataPoolSubCode"
                           [(ngModel)]="searchInfo.dataPoolSubCode"
                           formControlName="dataPoolSubCode"
                    />
                    <label htmlFor="msisdn">{{tranService.translate("sim.label.dataPoolSubCode")}}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="dateFrom"
                                [(ngModel)]="searchInfo.fromDate"
                                formControlName="fromDate"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [maxDate]="maxDateFrom"
                                (onSelect)="onChangeDateFrom(searchInfo.fromDate)"
                                (onInput)="onChangeDateFrom(searchInfo.fromDate)"
                    ></p-calendar>
                    <label class="label-calendar" htmlFor="dateFrom">{{tranService.translate("alert.label.fromdate")}}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="dateTo"
                                [(ngModel)]="searchInfo.toDate"
                                formControlName="toDate"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [minDate]="minDateTo"
                                [maxDate]="maxDateTo"
                                (onSelect)="onChangeDateTo(searchInfo.toDate)"
                                (onInput)="onChangeDateTo(searchInfo.toDate)"
                    />
                    <label class="label-calendar" htmlFor="dateTo">{{tranService.translate("alert.label.todate")}}</label>
                </span>
            </div>

            <!--            button search-->
            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>

<table-vnpt
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.alerthistory')"
></table-vnpt>
