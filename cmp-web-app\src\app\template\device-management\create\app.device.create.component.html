<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.listdevice")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
</div>
<p-card styleClass="mt-3 responsive-form">
    <form [formGroup]="formCreateDevice" (ngSubmit)="create()">
        <div class="grid mx-4 my-3">
            <!--            imei-->
            <div class="col-3 ">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="imei"
                           [(ngModel)]="deviceInfo.imei"
                           formControlName="imei"
                           pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,64}$"
                           (ngModelChange)="checkExistsImei()"
                    >
                    <div *ngIf="formCreateDevice.controls['imei'].invalid && (formCreateDevice.controls['imei'].dirty || formCreateDevice.controls['imei'].touched)">
                        <div class="text-red-500" *ngIf="formCreateDevice.controls['imei'].errors?.pattern">
                            {{tranService.translate("global.message.invalidinformation64")}}
                        </div>
                    </div>
                    <div class="text-red-500" *ngIf="deviceInfo.imei != null && deviceInfo.imei.trim() != '' && isShowExistsImei">
                        {{tranService.translate("global.message.exists",{type: tranService.translate("device.label.imei").toLowerCase()})}}
                    </div>
                    <label htmlFor="imei">{{tranService.translate("device.label.imei")}}</label>
                </span>
            </div>
            <!--            vị trí-->
<!--            <div class="col-3">-->
<!--                <span class="p-float-label">-->
<!--                    <input class="w-full"-->
<!--                           pInputText id="location"-->
<!--                           [(ngModel)]="deviceInfo.location"-->
<!--                           formControlName="location"-->
<!--                           pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,255}$">-->
<!--                    <div-->
<!--                        *ngIf="formCreateDevice.controls['location'].invalid && (formCreateDevice.controls['location'].dirty || formCreateDevice.controls['location'].touched)">-->
<!--                    <div class="text-red-500" *ngIf="formCreateDevice.controls['location'].errors?.pattern">-->
<!--                        {{tranService.translate("global.message.invalidinformation")}}-->
<!--                    </div>-->
<!--                </div>-->
<!--            <label htmlFor="location">{{tranService.translate("device.label.location")}}</label>-->
<!--            </span>-->
<!--            </div>-->
            <!--            so thue bao-->
<!--            <div class="col-3">-->
<!--                <span class="p-float-label">-->
<!--                    <p-dropdown styleClass="w-full"-->
<!--                        [options]="listSubscription"-->
<!--                        [(ngModel)]="deviceInfo.msisdn"-->
<!--                                optionLabel="msisdn"-->
<!--                                optionValue="msisdn"-->
<!--                                filter="true"-->
<!--                                filterBy="msisdn"-->
<!--                                formControlName="msisdn"-->
<!--                    ></p-dropdown>-->
<!--            <label htmlFor="msisdn">{{tranService.translate("device.label.msisdn")}}</label>-->
<!--            </span>-->
<!--            </div>-->
            <div class="col-3">
                <span class="p-float-label">
                    <p-autoComplete styleClass="w-full" inputStyle=""
                                [suggestions]="filteredSubscription"
                                    (completeMethod)="filterMsisdn($event)"
                                    field="msisdn"
                                    inputStyleClass="w-full"
                                    formControlName="msisdn"
                                    (input)="onInput($event)"
                                    (onSelect)="onSelect($event)"
                                    [required] = "true"
                    ></p-autoComplete>
            <label htmlFor="msisdn">{{tranService.translate("device.label.msisdn")}}</label>
            </span>
                <div class="text-red-500" *ngIf="!msisdnEntered">
                {{tranService.translate("global.message.required")}}
            </div>
                <div class="text-red-500" *ngIf="msisdnEntered && showValidationMsisdnError">
                    {{tranService.translate("global.message.invalidPhone")}}
                </div>
                <div class="text-red-500" *ngIf="msisdnEntered && !showValidationMsisdnError && notPermissionMisidn">
                    {{tranService.translate("global.message.notPermissionMisidn")}}
                </div>
            </div>
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="country"
                           [(ngModel)]="deviceInfo.country"
                           formControlName="country"
                           pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,32}$">
                    <div
                        *ngIf="formCreateDevice.controls['country'].invalid && (formCreateDevice.controls['country'].dirty || formCreateDevice.controls['country'].touched)">
                    <div class="text-red-500" *ngIf="formCreateDevice.controls['country'].errors?.pattern">
                        {{tranService.translate("global.message.invalidinformation32")}}
                    </div>
                </div>
            <label htmlFor="country">{{tranService.translate("device.label.country")}}</label>
            </span>
            </div>
<!--category-->
<!--            <div class="col-3">-->
<!--                <span class="p-float-label">-->
<!--                    <input class="w-full"-->
<!--                           pInputText id="category"-->
<!--                           [(ngModel)]="deviceInfo.category"-->
<!--                           formControlName="category"-->
<!--                           pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,255}$">-->
<!--                    <div-->
<!--                        *ngIf="formCreateDevice.controls['category'].invalid && (formCreateDevice.controls['category'].dirty || formCreateDevice.controls['category'].touched)">-->
<!--                    <div class="text-red-500" *ngIf="formCreateDevice.controls['category'].errors?.pattern">-->
<!--                        {{tranService.translate("global.message.invalidinformation")}}-->
<!--                    </div>-->
<!--                </div>-->
<!--            <label htmlFor="category">{{tranService.translate("device.label.category")}}</label>-->
<!--            </span>-->
<!--            </div>-->

            <div class="col-3 ">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="expiredDate"
                                [(ngModel)]="deviceInfo.expiredDate"
                                formControlName="expiredDate"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                    ></p-calendar>
            <label htmlFor="expiredDate">{{tranService.translate("device.label.expireDate")}}</label>
            </span>
            </div>

            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="deviceType"
                           [(ngModel)]="deviceInfo.deviceType"
                           formControlName="deviceType"
                           pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,64}$">
                   <div
                       *ngIf="formCreateDevice.controls['deviceType'].invalid && (formCreateDevice.controls['deviceType'].dirty || formCreateDevice.controls['deviceType'].touched)">
                    <div class="text-red-500" *ngIf="formCreateDevice.controls['deviceType'].errors?.pattern">
                        {{tranService.translate("global.message.invalidinformation64")}}
                    </div>
                </div>
            <label htmlFor="deviceType">{{tranService.translate("device.label.deviceType")}}</label>
            </span>
            </div>
            <div class="col-3">
                <label class="flex align-items-center">
                    <p-checkbox [(ngModel)]="deviceInfo.iotLink"  formControlName="iotLink" name="iotLink" [binary]="true" inputId="iotLink" [ngStyle]="{'margin': '6px'}"></p-checkbox>
                    <label class="w-12" for="iotLink">{{tranService.translate("device.label.iotLink")}}</label>
                </label>
            </div>
<!--&lt;!&ndash;            ghi chú&ndash;&gt;-->
<!--            <div class="col-3">-->
<!--                <span class="p-float-label">-->
<!--                    <input class="w-full"-->
<!--                           pInputText id="note"-->
<!--                           [(ngModel)]="deviceInfo.note"-->
<!--                           formControlName="note"-->
<!--                           pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,255}$">-->
<!--                    <div-->
<!--                        *ngIf="formCreateDevice.controls['note'].invalid && (formCreateDevice.controls['note'].dirty || formCreateDevice.controls['note'].touched)">-->
<!--                    <div class="text-red-500" *ngIf="formCreateDevice.controls['note'].errors?.pattern">-->
<!--                        {{tranService.translate("global.message.invalidinformation")}}-->
<!--                    </div>-->
<!--                </div>-->
<!--            <label htmlFor="note">{{tranService.translate("device.label.note")}}</label>-->
<!--            </span>-->
<!--            </div>-->

        </div>
        <div class="flex flex-row justify-content-center align-items-center">
            <p-button styleClass="p-button-secondary p-button-outlined mr-2" (click)="goBack()">
                {{ tranService.translate("global.button.cancel") }}
            </p-button>
            <p-button styleClass="p-button-info" type="submit" [disabled]="formCreateDevice.invalid || !msisdnEntered || showValidationMsisdnError || notPermissionMisidn || isShowExistsImei" *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.CREATE])">
                {{ tranService.translate("global.button.save") }}
            </p-button>
        </div>
    </form>
</p-card>
