{"ast": null, "code": "import { CONSTANTS } from \"src/app/service/comon/constants\";\nimport { ComponentBase } from \"../../../component.base\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/service/account/AccountService\";\nimport * as i2 from \"../../../service/customer/CustomerService\";\nimport * as i3 from \"../../../service/contract/ContractService\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/breadcrumb\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"primeng/inputtext\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"../../common-module/table/table.component\";\nimport * as i10 from \"primeng/dropdown\";\nimport * as i11 from \"primeng/card\";\nimport * as i12 from \"primeng/tabview\";\nimport * as i13 from \"primeng/radiobutton\";\nfunction AppProfileDetailComponent_div_6_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"label\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 16)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r5.tranService.translate(\"account.label.province\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r5.accountResponse.provinceName, \" (\", ctx_r5.accountResponse.provinceCode, \")\");\n  }\n}\nfunction AppProfileDetailComponent_div_6_div_45_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r10.username, \" \");\n  }\n}\nfunction AppProfileDetailComponent_div_6_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 27);\n    i0.ɵɵtemplate(4, AppProfileDetailComponent_div_6_div_45_div_4_Template, 2, 1, \"div\", 30);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.tranService.translate(\"account.label.customerAccount\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.accountResponse == null ? null : ctx_r6.accountResponse.userManages);\n  }\n}\nfunction AppProfileDetailComponent_div_6_div_46_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.accountResponse.rootAccount.username, \" \");\n  }\n}\nfunction AppProfileDetailComponent_div_6_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"label\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 27);\n    i0.ɵɵtemplate(4, AppProfileDetailComponent_div_6_div_46_div_4_Template, 2, 1, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r7.tranService.translate(\"account.label.customerAccount\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.accountResponse == null ? null : ctx_r7.accountResponse.rootAccount == null ? null : ctx_r7.accountResponse.rootAccount.username);\n  }\n}\nfunction AppProfileDetailComponent_div_6_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r12.roleName, \" \");\n  }\n}\nfunction AppProfileDetailComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13)(2, \"div\", 14)(3, \"label\", 15);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 16);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 14)(8, \"label\", 17);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 18);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 14)(13, \"label\", 19);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 20);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 14)(18, \"label\", 21);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 16);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 14)(23, \"label\", 22);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 20);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 14)(28, \"label\", 23);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 16);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 13)(33, \"div\", 14)(34, \"label\", 24);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 16)(37, \"span\");\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(39, AppProfileDetailComponent_div_6_div_39_Template, 6, 3, \"div\", 25);\n    i0.ɵɵelementStart(40, \"div\", 14)(41, \"label\", 26);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 27);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(45, AppProfileDetailComponent_div_6_div_45_Template, 5, 2, \"div\", 28);\n    i0.ɵɵtemplate(46, AppProfileDetailComponent_div_6_div_46_Template, 5, 2, \"div\", 28);\n    i0.ɵɵelementStart(47, \"div\", 14)(48, \"label\", 29);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 27);\n    i0.ɵɵtemplate(51, AppProfileDetailComponent_div_6_div_51_Template, 2, 1, \"div\", 30);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.username\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.accountResponse.username, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.status\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getStringUserStatus(ctx_r0.accountResponse.status), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.fullname\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.accountResponse.fullName, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.phone\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.accountResponse.phone, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.email\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.accountResponse.email, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.description\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.accountResponse.description, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.userType\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.getStringUserType(ctx_r0.accountResponse.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.accountInfo.userType != ctx_r0.optionUserType.ADMIN && ctx_r0.accountInfo.userType != ctx_r0.optionUserType.AGENCY);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.accountInfo.userType == ctx_r0.optionUserType.CUSTOMER && (ctx_r0.userType == ctx_r0.optionUserType.ADMIN || ctx_r0.userType == ctx_r0.optionUserType.PROVINCE) ? \"\" : \"hidden\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.managerName\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.accountResponse == null ? null : ctx_r0.accountResponse.manager == null ? null : ctx_r0.accountResponse.manager.username, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.accountInfo.userType == ctx_r0.optionUserType.DISTRICT);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.accountInfo.userType == ctx_r0.optionUserType.CUSTOMER && !(ctx_r0.accountResponse == null ? null : ctx_r0.accountResponse.isRootCustomer));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.tranService.translate(\"account.label.role\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.accountResponse.roles);\n  }\n}\nconst _c0 = function () {\n  return {\n    standalone: true\n  };\n};\nconst _c1 = function () {\n  return [5, 10, 20, 25, 50];\n};\nfunction AppProfileDetailComponent_p_tabPanel_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 5)(1, \"div\", 34)(2, \"input\", 35);\n    i0.ɵɵlistener(\"keydown.enter\", function AppProfileDetailComponent_p_tabPanel_7_Template_input_keydown_enter_2_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onSearchCustomer(true));\n    })(\"ngModelChange\", function AppProfileDetailComponent_p_tabPanel_7_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.paramQuickSearchCustomer.keyword = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-button\", 36);\n    i0.ɵɵlistener(\"click\", function AppProfileDetailComponent_p_tabPanel_7_Template_p_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.onSearchCustomer(true));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(4, \"table-vnpt\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r1.tranService.translate(\"global.menu.listcustomer\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"placeholder\", ctx_r1.tranService.translate(\"sim.label.quickSearch\"))(\"ngModel\", ctx_r1.paramQuickSearchCustomer.keyword)(\"ngModelOptions\", i0.ɵɵpureFunction0(15, _c0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"pageNumber\", ctx_r1.paginationCustomer.page)(\"pageSize\", ctx_r1.paginationCustomer.size)(\"columns\", ctx_r1.columnInfoCustomer)(\"dataSet\", ctx_r1.dataSetCustomer)(\"options\", ctx_r1.optionTableCustomer)(\"loadData\", ctx_r1.searchCustomer.bind(ctx_r1))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(16, _c1))(\"scrollHeight\", \"400px\")(\"sort\", ctx_r1.paginationCustomer.sortBy)(\"params\", ctx_r1.paramQuickSearchCustomer);\n  }\n}\nfunction AppProfileDetailComponent_p_tabPanel_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 5)(1, \"div\", 34)(2, \"input\", 35);\n    i0.ɵɵlistener(\"keydown.enter\", function AppProfileDetailComponent_p_tabPanel_8_Template_input_keydown_enter_2_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.onSearchContract(true));\n    })(\"ngModelChange\", function AppProfileDetailComponent_p_tabPanel_8_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.paramQuickSearchContract.keyword = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-button\", 36);\n    i0.ɵɵlistener(\"click\", function AppProfileDetailComponent_p_tabPanel_8_Template_p_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.onSearchContract(true));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(4, \"table-vnpt\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r2.tranService.translate(\"global.menu.listbill\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"placeholder\", ctx_r2.tranService.translate(\"sim.label.quickSearch\"))(\"ngModel\", ctx_r2.paramQuickSearchContract.keyword)(\"ngModelOptions\", i0.ɵɵpureFunction0(15, _c0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"pageNumber\", ctx_r2.paginationContract.page)(\"pageSize\", ctx_r2.paginationContract.size)(\"columns\", ctx_r2.columnInfoContract)(\"dataSet\", ctx_r2.dataSetContract)(\"options\", ctx_r2.optionTableContract)(\"loadData\", ctx_r2.searchContract.bind(ctx_r2))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(16, _c1))(\"scrollHeight\", \"400px\")(\"sort\", ctx_r2.paginationContract.sortBy)(\"params\", ctx_r2.paramQuickSearchContract);\n  }\n}\nfunction AppProfileDetailComponent_p_tabPanel_9_label_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 57);\n    i0.ɵɵlistener(\"click\", function AppProfileDetailComponent_p_tabPanel_9_label_18_Template_label_click_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r23.isShowSecretKey = true);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppProfileDetailComponent_p_tabPanel_9_label_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 58);\n    i0.ɵɵlistener(\"click\", function AppProfileDetailComponent_p_tabPanel_9_label_19_Template_label_click_0_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.isShowSecretKey = false);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppProfileDetailComponent_p_tabPanel_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 38)(1, \"div\", 39)(2, \"p-panel\", 40)(3, \"div\", 41)(4, \"p-radioButton\", 42);\n    i0.ɵɵlistener(\"ngModelChange\", function AppProfileDetailComponent_p_tabPanel_9_Template_p_radioButton_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.statusGrantApi = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p-radioButton\", 43);\n    i0.ɵɵlistener(\"ngModelChange\", function AppProfileDetailComponent_p_tabPanel_9_Template_p_radioButton_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.statusGrantApi = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 44)(7, \"div\", 45)(8, \"div\", 46)(9, \"label\", 47);\n    i0.ɵɵtext(10, \"Client ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 48);\n    i0.ɵɵlistener(\"ngModelChange\", function AppProfileDetailComponent_p_tabPanel_9_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.genGrantApi.clientId = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 45)(13, \"div\", 46)(14, \"label\", 47);\n    i0.ɵɵtext(15, \"Secret Key\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 49)(17, \"input\", 50);\n    i0.ɵɵlistener(\"ngModelChange\", function AppProfileDetailComponent_p_tabPanel_9_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.genGrantApi.secretKey = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, AppProfileDetailComponent_p_tabPanel_9_label_18_Template, 1, 0, \"label\", 51);\n    i0.ɵɵtemplate(19, AppProfileDetailComponent_p_tabPanel_9_label_19_Template, 1, 0, \"label\", 52);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(20, \"div\")(21, \"p-panel\", 53)(22, \"div\", 44)(23, \"div\", 54)(24, \"p-dropdown\", 55);\n    i0.ɵɵlistener(\"ngModelChange\", function AppProfileDetailComponent_p_tabPanel_9_Template_p_dropdown_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.paramsSearchGrantApi.module = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 54)(26, \"input\", 56);\n    i0.ɵɵlistener(\"ngModelChange\", function AppProfileDetailComponent_p_tabPanel_9_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.paramsSearchGrantApi.api = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"p-button\", 36);\n    i0.ɵɵlistener(\"click\", function AppProfileDetailComponent_p_tabPanel_9_Template_p_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.onSearchGrantApi(true));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(28, \"table-vnpt\", 37);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r3.tranService.translate(\"account.text.grantApi\"));\n    i0.ɵɵproperty(\"pt\", \"ProfileTab\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"showHeader\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r3.tranService.translate(\"account.text.working\"))(\"ngModel\", ctx_r3.statusGrantApi)(\"disabled\", true)(\"ngModelOptions\", i0.ɵɵpureFunction0(40, _c0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", ctx_r3.tranService.translate(\"account.text.notWorking\"))(\"ngModel\", ctx_r3.statusGrantApi)(\"disabled\", true)(\"ngModelOptions\", i0.ɵɵpureFunction0(41, _c0));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.genGrantApi.clientId)(\"disabled\", true)(\"ngModelOptions\", i0.ɵɵpureFunction0(42, _c0));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.genGrantApi.secretKey)(\"ngModelOptions\", i0.ɵɵpureFunction0(43, _c0))(\"type\", ctx_r3.isShowSecretKey ? \"text\" : \"password\")(\"disabled\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isShowSecretKey == false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isShowSecretKey == true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"showHeader\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"showClear\", true)(\"ngModel\", ctx_r3.paramsSearchGrantApi.module)(\"ngModelOptions\", i0.ɵɵpureFunction0(44, _c0))(\"options\", ctx_r3.listModule)(\"emptyFilterMessage\", ctx_r3.tranService.translate(\"global.text.nodata\"))(\"placeholder\", ctx_r3.tranService.translate(\"account.text.module\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.paramsSearchGrantApi.api)(\"ngModelOptions\", i0.ɵɵpureFunction0(45, _c0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"pageNumber\", ctx_r3.paginationGrantApi.page)(\"pageSize\", ctx_r3.paginationGrantApi.size)(\"columns\", ctx_r3.columnInfoGrantApi)(\"dataSet\", ctx_r3.dataSetGrantApi)(\"options\", ctx_r3.optionTableGrantApi)(\"loadData\", ctx_r3.searchGrantApi.bind(ctx_r3))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(46, _c1))(\"scrollHeight\", \"400px\")(\"sort\", ctx_r3.paginationGrantApi.sortBy)(\"params\", ctx_r3.paramsSearchGrantApi);\n  }\n}\nfunction AppProfileDetailComponent_p_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r36 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 59);\n    i0.ɵɵlistener(\"click\", function AppProfileDetailComponent_p_button_11_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r36);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.goToEdit());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", ctx_r4.tranService.translate(\"global.menu.editAccount\"));\n  }\n}\nconst _c2 = function (a0) {\n  return [a0];\n};\nexport class AppProfileDetailComponent extends ComponentBase {\n  constructor(accountService, customerService, contractService, injector) {\n    super(injector);\n    this.accountService = accountService;\n    this.customerService = customerService;\n    this.contractService = contractService;\n    this.oldUserType = null;\n    this.isShowSecretKey = true;\n    this.listModule = [];\n    //sẽ lưu lại list api sau khi đã chọn\n    this.selectItemGrantApi = [];\n    this.paramsSearchGrantApi = {\n      api: null,\n      module: null\n    };\n    this.genGrantApi = {\n      clientId: '',\n      secretKey: ''\n    };\n    this.CONSTANTS = CONSTANTS;\n  }\n  ngOnInit() {\n    this.userInfo = this.sessionService.userInfo;\n    this.accountId = this.userInfo.id;\n    this.userType = this.sessionService.userInfo.type;\n    this.optionUserType = CONSTANTS.USER_TYPE;\n    this.items = [{\n      label: this.tranService.translate(\"global.menu.account\")\n    }, {\n      label: this.tranService.translate(\"global.menu.detailAccount\")\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    let fullTypeAccount = [{\n      name: this.tranService.translate(\"account.usertype.admin\"),\n      value: CONSTANTS.USER_TYPE.ADMIN,\n      accepts: [CONSTANTS.USER_TYPE.ADMIN]\n    }, {\n      name: this.tranService.translate(\"account.usertype.customer\"),\n      value: CONSTANTS.USER_TYPE.CUSTOMER,\n      accepts: [CONSTANTS.USER_TYPE.ADMIN, CONSTANTS.USER_TYPE.PROVINCE, CONSTANTS.USER_TYPE.DISTRICT, CONSTANTS.USER_TYPE.AGENCY, CONSTANTS.USER_TYPE.CUSTOMER]\n    }, {\n      name: this.tranService.translate(\"account.usertype.province\"),\n      value: CONSTANTS.USER_TYPE.PROVINCE,\n      accepts: [CONSTANTS.USER_TYPE.ADMIN]\n    }, {\n      name: this.tranService.translate(\"account.usertype.district\"),\n      value: CONSTANTS.USER_TYPE.DISTRICT,\n      accepts: [CONSTANTS.USER_TYPE.ADMIN, CONSTANTS.USER_TYPE.PROVINCE]\n    }, {\n      name: this.tranService.translate(\"account.usertype.agency\"),\n      value: CONSTANTS.USER_TYPE.AGENCY,\n      accepts: [CONSTANTS.USER_TYPE.ADMIN, CONSTANTS.USER_TYPE.PROVINCE, CONSTANTS.USER_TYPE.DISTRICT]\n    }];\n    this.accountInfo = {\n      accountName: null,\n      fullName: null,\n      email: null,\n      phone: null,\n      userType: null,\n      province: null,\n      roles: null,\n      description: null,\n      manager: null,\n      customers: null\n    };\n    this.paginationGrantApi = {\n      page: 0,\n      size: 10,\n      sortBy: \"id,desc\"\n    };\n    this.columnInfoGrantApi = [{\n      name: \"API\",\n      key: \"name\",\n      size: \"30%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: \"Module\",\n      key: \"module\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }];\n    this.dataSetGrantApi = {\n      content: [],\n      total: 0\n    };\n    this.optionTableGrantApi = {\n      hasClearSelected: false,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false\n    };\n    this.accountResponse = {};\n    this.getListProvince();\n    this.getDetail();\n    this.paramQuickSearchCustomer = {\n      keyword: null,\n      accountRootId: Number(this.accountId)\n    };\n    this.columnInfoCustomer = [{\n      name: this.tranService.translate(\"customer.label.customerCode\"),\n      key: \"code\",\n      size: \"30%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"customer.label.customerName\"),\n      key: \"name\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }];\n    this.dataSetCustomer = {\n      content: [],\n      total: 0\n    };\n    this.paginationCustomer = {\n      page: 0,\n      size: 10,\n      sortBy: \"name,asc;id,asc\"\n    };\n    this.optionTableCustomer = {\n      hasClearSelected: false,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false\n    };\n    this.paramQuickSearchContract = {\n      keyword: null,\n      accountRootId: Number(this.accountId),\n      customerIds: []\n    };\n    this.columnInfoContract = [{\n      name: this.tranService.translate(\"customer.label.customerCode\"),\n      key: \"customerCode\",\n      size: \"30%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"customer.label.customerName\"),\n      key: \"customerName\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"contract.label.contractCode\"),\n      key: \"contractCode\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }];\n    this.dataSetContract = {\n      content: [],\n      total: 0\n    };\n    this.paginationContract = {\n      page: 0,\n      size: 10,\n      sortBy: \"customerName,asc;id,asc\"\n    };\n    this.optionTableContract = {\n      hasClearSelected: false,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false\n    };\n  }\n  ngAfterContentChecked() {}\n  getDetail() {\n    let me = this;\n    let accountid = this.userInfo.id;\n    // console.log(accountid)\n    me.messageCommonService.onload();\n    this.accountService.viewProfile(response => {\n      me.accountResponse = response;\n      me.accountInfo.accountName = response.username;\n      me.accountInfo.fullName = response.fullName;\n      me.accountInfo.email = response.email;\n      me.accountInfo.description = response.description;\n      me.accountInfo.phone = response.phone;\n      me.accountInfo.province = response.provinceCode;\n      me.accountInfo.userType = response.type;\n      me.getListRole(false);\n      if (me.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\n        me.resetPaginationCustomerAndContract();\n        me.paramQuickSearchCustomer.accountRootId = Number(me.accountId);\n        me.paramQuickSearchContract.accountRootId = Number(me.accountId);\n        me.paramQuickSearchContract.customerIds = (me.accountResponse.customers || []).map(customer => customer.customerId);\n      }\n      me.statusGrantApi = response.statusApi;\n      me.selectItemGrantApi = response.listApiId ? response.listApiId.map(el => ({\n        id: el\n      })) : [{\n        id: -99\n      }];\n      me.genGrantApi.secretKey = response.secretId;\n      me.genGrantApi.clientId = response.username;\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  getListRole(isClear) {\n    this.accountService.getListRole(this.accountInfo.userType, response => {\n      this.listRole = response.map(el => {\n        return {\n          id: el.id,\n          name: el.name\n        };\n      });\n      if (isClear) {\n        this.accountInfo.roles = null;\n      } else {\n        this.accountInfo.roles = this.listRole.filter(el => (this.accountResponse.roles || []).includes(el.id));\n      }\n    });\n  }\n  getListProvince() {\n    this.accountService.getListProvince(response => {\n      this.listProvince = response.map(el => {\n        return {\n          id: el.code,\n          name: `${el.name} (${el.code})`\n        };\n      });\n    });\n  }\n  goToEdit() {\n    this.router.navigate([`/profile/edit/`]);\n  }\n  getStringCustomers() {\n    return (this.accountResponse.customers || []).map(el => el.customerName + ' - ' + el.customerCode).toLocaleString();\n  }\n  getStringRoles() {\n    return (this.accountResponse.roles || []).map(el => el.roleName).toLocaleString();\n  }\n  getStringUserType(value) {\n    if (value == CONSTANTS.USER_TYPE.ADMIN) {\n      return this.tranService.translate(\"account.usertype.admin\");\n    } else if (value == CONSTANTS.USER_TYPE.CUSTOMER) {\n      return this.tranService.translate(\"account.usertype.customer\");\n    } else if (value == CONSTANTS.USER_TYPE.PROVINCE) {\n      return this.tranService.translate(\"account.usertype.province\");\n    } else if (value == CONSTANTS.USER_TYPE.DISTRICT) {\n      return this.tranService.translate(\"account.usertype.district\");\n    } else if (value == CONSTANTS.USER_TYPE.AGENCY) {\n      return this.tranService.translate(\"account.usertype.agency\");\n    } else {\n      return \"\";\n    }\n  }\n  goToChangePass() {\n    this.router.navigate(['/profile/change-password']);\n  }\n  onTabChange(event) {\n    const tabName = event.originalEvent.target.innerText;\n    let me = this;\n    if (event && tabName.includes(this.tranService.translate('account.text.grantApi'))) {\n      me.onSearchGrantApi();\n    } else if (event && tabName.includes(this.tranService.translate('global.menu.listbill'))) {\n      me.onSearchContract();\n    } else if (event && tabName.includes(this.tranService.translate('global.menu.listcustomer'))) {\n      me.onSearchCustomer();\n    }\n  }\n  onSearchCustomer(back) {\n    let me = this;\n    if (back) {\n      me.paginationCustomer.page = 0;\n    }\n    me.searchCustomer(me.paginationCustomer.page, me.paginationCustomer.size, me.paginationCustomer.sortBy, me.paramQuickSearchCustomer);\n  }\n  onSearchContract(back) {\n    let me = this;\n    if (back) {\n      me.paginationContract.page = 0;\n    }\n    me.searchContract(me.paginationContract.page, me.paginationContract.size, me.paginationContract.sortBy, me.paramQuickSearchContract);\n  }\n  searchCustomer(page, limit, sort, params) {\n    let me = this;\n    this.paginationCustomer.page = page;\n    this.paginationCustomer.size = limit;\n    this.paginationCustomer.sortBy = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    Object.keys(this.paramQuickSearchCustomer).forEach(key => {\n      if (this.paramQuickSearchCustomer[key] != null) {\n        dataParams[key] = this.paramQuickSearchCustomer[key];\n      }\n    });\n    me.messageCommonService.onload();\n    this.customerService.quickSearchCustomer(dataParams, this.paramQuickSearchCustomer, response => {\n      me.dataSetCustomer = {\n        content: response.content,\n        total: response.totalElements\n      };\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n    // console.log(this.selectItemCustomer)\n  }\n\n  searchContract(page, limit, sort, params) {\n    let me = this;\n    this.paginationContract.page = page;\n    this.paginationContract.size = limit;\n    this.paginationContract.sortBy = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    // Object.keys(this.paramQuickSearchContract).forEach(key => {\n    //     if(this.paramQuickSearchContract[key] != null){\n    //         dataParams[key] = this.paramQuickSearchContract[key];\n    //     }\n    // })\n    me.messageCommonService.onload();\n    this.contractService.quickSearchContract(dataParams, this.paramQuickSearchContract, response => {\n      me.dataSetContract = {\n        content: response.content,\n        total: response.totalElements\n      };\n      // console.log(response)\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  getStringUserStatus(value) {\n    if (value == CONSTANTS.USER_STATUS.ACTIVE) {\n      return this.tranService.translate(\"account.userstatus.active\");\n    } else if (value == CONSTANTS.USER_STATUS.INACTIVE) {\n      return this.tranService.translate(\"account.userstatus.inactive\");\n    } else {\n      return \"\";\n    }\n  }\n  resetPaginationCustomerAndContract() {\n    this.paginationCustomer = {\n      page: 0,\n      size: 10,\n      sortBy: \"name,asc;id,asc\"\n    };\n    this.paginationContract = {\n      page: 0,\n      size: 10,\n      sortBy: \"customerName,asc;id,asc\"\n    };\n  }\n  searchGrantApi(page, limit, sort, params) {\n    let me = this;\n    this.paginationGrantApi.page = page;\n    this.paginationGrantApi.size = limit;\n    this.paginationGrantApi.sortBy = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort,\n      selectedApiIds: this.selectItemGrantApi.map(el => el.id).join(',')\n    };\n    Object.keys(this.paramsSearchGrantApi).forEach(key => {\n      if (this.paramsSearchGrantApi[key] != null) {\n        dataParams[key] = this.paramsSearchGrantApi[key];\n      }\n    });\n    console.log(dataParams);\n    me.messageCommonService.onload();\n    this.accountService.searchGrantApi(dataParams, response => {\n      me.dataSetGrantApi = {\n        content: response.content,\n        total: response.totalElements\n      };\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n    let copyParam = {\n      ...dataParams\n    };\n    copyParam.size = *********;\n    this.accountService.searchGrantApi(copyParam, response => {\n      me.listModule = [...new Set(response.content.map(el => el.module))];\n      me.listModule = me.listModule.map(el => ({\n        name: el,\n        value: el\n      }));\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  generateToken(n) {\n    var chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    var token = '';\n    for (var i = 0; i < n; i++) {\n      token += chars[Math.floor(Math.random() * chars.length)];\n    }\n    return token;\n  }\n  genToken() {\n    this.genGrantApi.secretKey = this.generateToken(20);\n  }\n  onSearchGrantApi(back) {\n    let me = this;\n    console.log(me.paramsSearchGrantApi);\n    if (back) {\n      me.paginationGrantApi.page = 0;\n    }\n    me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);\n  }\n  static {\n    this.ɵfac = function AppProfileDetailComponent_Factory(t) {\n      return new (t || AppProfileDetailComponent)(i0.ɵɵdirectiveInject(i1.AccountService), i0.ɵɵdirectiveInject(i2.CustomerService), i0.ɵɵdirectiveInject(i3.ContractService), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppProfileDetailComponent,\n      selectors: [[\"app-account-detail\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 13,\n      vars: 11,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"max-w-full\", \"col-12\", 3, \"model\", \"home\"], [\"styleClass\", \"mt-3 responsive-form\"], [1, \"w-full\"], [3, \"onChange\"], [3, \"header\"], [\"class\", \"flex flex-row justify-content-between profile-create\", 4, \"ngIf\"], [3, \"header\", 4, \"ngIf\"], [3, \"header\", \"pt\", 4, \"ngIf\"], [1, \"flex\", \"justify-content-center\"], [\"styleClass\", \"p-button-info\", \"class\", \"mx-5 \", 3, \"label\", \"click\", 4, \"ngIf\"], [\"styleClass\", \"p-button-info bg-cyan-500 border-none\", 1, \"mx-5\", 3, \"label\", \"click\"], [1, \"flex\", \"flex-row\", \"justify-content-between\", \"profile-create\"], [2, \"width\", \"49%\"], [1, \"w-full\", \"field\", \"grid\"], [\"htmlFor\", \"accountName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"col\"], [\"htmlFor\", \"fullName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"col\", \"wrap-div\"], [\"htmlFor\", \"fullName\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [1, \"col\", \"wrap-div\", 2, \"width\", \"calc(100% - 180px)\", \"overflow-wrap\", \"break-word\"], [\"htmlFor\", \"phone\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"htmlFor\", \"email\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"htmlFor\", \"description\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"for\", \"userType\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"class\", \"w-full field grid\", 4, \"ngIf\"], [\"htmlFor\", \"roles\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"col\", 2, \"max-width\", \"calc(100% - 180px) !important\"], [\"class\", \"w-full field grid align-items-start\", 4, \"ngIf\"], [\"htmlFor\", \"roles\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [4, \"ngFor\", \"ngForOf\"], [\"htmlFor\", \"province\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"w-full\", \"field\", \"grid\", \"align-items-start\"], [4, \"ngIf\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"gap-3\", \"mt-4\"], [\"type\", \"text\", \"pInputText\", \"\", 2, \"min-width\", \"35vw\", 3, \"placeholder\", \"ngModel\", \"ngModelOptions\", \"keydown.enter\", \"ngModelChange\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"ml-3 p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"button\", 3, \"click\"], [3, \"fieldId\", \"pageNumber\", \"pageSize\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"rowsPerPageOptions\", \"scrollHeight\", \"sort\", \"params\"], [3, \"header\", \"pt\"], [1, \"mb-3\"], [3, \"showHeader\"], [1, \"flex\", \"gap-2\"], [\"value\", \"1\", 1, \"p-3\", 3, \"label\", \"ngModel\", \"disabled\", \"ngModelOptions\", \"ngModelChange\"], [\"value\", \"0\", 1, \"p-3\", 3, \"label\", \"ngModel\", \"disabled\", \"ngModelOptions\", \"ngModelChange\"], [1, \"flex\", \"gap-3\", \"align-items-center\"], [1, \"col-6\"], [1, \"flex\", \"align-items-center\"], [1, \"mr-3\", 2, \"min-width\", \"100px\"], [\"type\", \"text\", \"pInputText\", \"\", 1, \"w-full\", 3, \"ngModel\", \"disabled\", \"ngModelOptions\", \"ngModelChange\"], [1, \"w-full\", \"flex\", \"align-items-center\"], [\"pInputText\", \"\", 1, \"w-full\", \"mr-2\", 2, \"padding-right\", \"30px\", 3, \"ngModel\", \"ngModelOptions\", \"type\", \"disabled\", \"ngModelChange\"], [\"style\", \"margin-left: -30px;z-index: 1;\", \"class\", \"pi pi-eye toggle-password\", 3, \"click\", 4, \"ngIf\"], [\"style\", \"margin-left: -30px;z-index: 1;\", \"class\", \"pi pi-eye-slash toggle-password\", 3, \"click\", 4, \"ngIf\"], [1, \"\", 3, \"showHeader\"], [1, \"col-3\"], [\"optionLabel\", \"name\", \"optionValue\", \"value\", \"filter\", \"true\", 1, \"w-full\", 3, \"showClear\", \"ngModel\", \"ngModelOptions\", \"options\", \"emptyFilterMessage\", \"placeholder\", \"ngModelChange\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"API\", 1, \"w-full\", \"mr-2\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [1, \"pi\", \"pi-eye\", \"toggle-password\", 2, \"margin-left\", \"-30px\", \"z-index\", \"1\", 3, \"click\"], [1, \"pi\", \"pi-eye-slash\", \"toggle-password\", 2, \"margin-left\", \"-30px\", \"z-index\", \"1\", 3, \"click\"], [\"styleClass\", \"p-button-info\", 1, \"mx-5\", 3, \"label\", \"click\"]],\n      template: function AppProfileDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"p-breadcrumb\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"p-card\", 2)(3, \"div\", 3)(4, \"p-tabView\", 4);\n          i0.ɵɵlistener(\"onChange\", function AppProfileDetailComponent_Template_p_tabView_onChange_4_listener($event) {\n            return ctx.onTabChange($event);\n          });\n          i0.ɵɵelementStart(5, \"p-tabPanel\", 5);\n          i0.ɵɵtemplate(6, AppProfileDetailComponent_div_6_Template, 52, 23, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, AppProfileDetailComponent_p_tabPanel_7_Template, 5, 17, \"p-tabPanel\", 7);\n          i0.ɵɵtemplate(8, AppProfileDetailComponent_p_tabPanel_8_Template, 5, 17, \"p-tabPanel\", 7);\n          i0.ɵɵtemplate(9, AppProfileDetailComponent_p_tabPanel_9_Template, 29, 47, \"p-tabPanel\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 9);\n          i0.ɵɵtemplate(11, AppProfileDetailComponent_p_button_11_Template, 1, 1, \"p-button\", 10);\n          i0.ɵɵelementStart(12, \"p-button\", 11);\n          i0.ɵɵlistener(\"click\", function AppProfileDetailComponent_Template_p_button_click_12_listener() {\n            return ctx.goToChangePass();\n          });\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(4);\n          i0.ɵɵpropertyInterpolate(\"header\", ctx.tranService.translate(\"account.label.generalInfo\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.accountResponse);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.accountInfo.userType == ctx.CONSTANTS.USER_TYPE.CUSTOMER);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.accountInfo.userType == ctx.CONSTANTS.USER_TYPE.CUSTOMER);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.userType == ctx.optionUserType.CUSTOMER && ctx.genGrantApi.secretKey != null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkAuthen(i0.ɵɵpureFunction1(9, _c2, ctx.CONSTANTS.PERMISSIONS.PROFILE.UPDATE)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.changePass\"));\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.Breadcrumb, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel, i7.InputText, i8.Button, i9.TableVnptComponent, i10.Dropdown, i11.Card, i12.TabView, i12.TabPanel, i13.RadioButton],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CONSTANTS", "ComponentBase", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r5", "tranService", "translate", "ɵɵtextInterpolate2", "accountResponse", "provinceName", "provinceCode", "ɵɵtextInterpolate1", "item_r10", "username", "ɵɵtemplate", "AppProfileDetailComponent_div_6_div_45_div_4_Template", "ctx_r6", "ɵɵproperty", "userManages", "ctx_r11", "rootAccount", "AppProfileDetailComponent_div_6_div_46_div_4_Template", "ctx_r7", "item_r12", "<PERSON><PERSON><PERSON>", "AppProfileDetailComponent_div_6_div_39_Template", "AppProfileDetailComponent_div_6_div_45_Template", "AppProfileDetailComponent_div_6_div_46_Template", "AppProfileDetailComponent_div_6_div_51_Template", "ctx_r0", "getStringUserStatus", "status", "fullName", "phone", "email", "description", "getStringUserType", "type", "accountInfo", "userType", "optionUserType", "ADMIN", "AGENCY", "ɵɵclassMap", "CUSTOMER", "PROVINCE", "manager", "DISTRICT", "isRootCustomer", "roles", "ɵɵlistener", "AppProfileDetailComponent_p_tabPanel_7_Template_input_keydown_enter_2_listener", "ɵɵrestoreView", "_r14", "ctx_r13", "ɵɵnextContext", "ɵɵresetView", "onSearchCustomer", "AppProfileDetailComponent_p_tabPanel_7_Template_input_ngModelChange_2_listener", "$event", "ctx_r15", "paramQuickSearchCustomer", "keyword", "AppProfileDetailComponent_p_tabPanel_7_Template_p_button_click_3_listener", "ctx_r16", "ɵɵelement", "ɵɵpropertyInterpolate", "ctx_r1", "ɵɵpureFunction0", "_c0", "paginationCustomer", "page", "size", "columnInfoCustomer", "dataSetCustomer", "optionTableCustomer", "searchCustomer", "bind", "_c1", "sortBy", "AppProfileDetailComponent_p_tabPanel_8_Template_input_keydown_enter_2_listener", "_r18", "ctx_r17", "onSearchContract", "AppProfileDetailComponent_p_tabPanel_8_Template_input_ngModelChange_2_listener", "ctx_r19", "paramQuickSearchContract", "AppProfileDetailComponent_p_tabPanel_8_Template_p_button_click_3_listener", "ctx_r20", "ctx_r2", "paginationContract", "columnInfoContract", "dataSetContract", "optionTableContract", "searchContract", "AppProfileDetailComponent_p_tabPanel_9_label_18_Template_label_click_0_listener", "_r24", "ctx_r23", "isShowSecretKey", "AppProfileDetailComponent_p_tabPanel_9_label_19_Template_label_click_0_listener", "_r26", "ctx_r25", "AppProfileDetailComponent_p_tabPanel_9_Template_p_radioButton_ngModelChange_4_listener", "_r28", "ctx_r27", "statusGrantApi", "AppProfileDetailComponent_p_tabPanel_9_Template_p_radioButton_ngModelChange_5_listener", "ctx_r29", "AppProfileDetailComponent_p_tabPanel_9_Template_input_ngModelChange_11_listener", "ctx_r30", "genGrant<PERSON>pi", "clientId", "AppProfileDetailComponent_p_tabPanel_9_Template_input_ngModelChange_17_listener", "ctx_r31", "secret<PERSON>ey", "AppProfileDetailComponent_p_tabPanel_9_label_18_Template", "AppProfileDetailComponent_p_tabPanel_9_label_19_Template", "AppProfileDetailComponent_p_tabPanel_9_Template_p_dropdown_ngModelChange_24_listener", "ctx_r32", "paramsSearchGrantApi", "module", "AppProfileDetailComponent_p_tabPanel_9_Template_input_ngModelChange_26_listener", "ctx_r33", "api", "AppProfileDetailComponent_p_tabPanel_9_Template_p_button_click_27_listener", "ctx_r34", "onSearchGrantApi", "ctx_r3", "listModule", "paginationGrantApi", "columnInfoGrantApi", "dataSetGrantApi", "optionTableGrantApi", "searchGrantApi", "AppProfileDetailComponent_p_button_11_Template_p_button_click_0_listener", "_r36", "ctx_r35", "goToEdit", "ctx_r4", "AppProfileDetailComponent", "constructor", "accountService", "customerService", "contractService", "injector", "oldUserType", "selectItemGrantApi", "ngOnInit", "userInfo", "sessionService", "accountId", "id", "USER_TYPE", "items", "label", "home", "icon", "routerLink", "fullTypeAccount", "name", "value", "accepts", "accountName", "province", "customers", "key", "align", "isShow", "isSort", "content", "total", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "getListProvince", "getDetail", "accountRootId", "Number", "customerIds", "ngAfterContentChecked", "me", "accountid", "messageCommonService", "onload", "viewProfile", "response", "getListRole", "resetPaginationCustomerAndContract", "map", "customer", "customerId", "statusApi", "listApiId", "el", "secretId", "offload", "isClear", "listRole", "filter", "includes", "listProvince", "code", "router", "navigate", "getStringCustomers", "customerName", "customerCode", "toLocaleString", "getStringRoles", "goToChangePass", "onTabChange", "event", "tabName", "originalEvent", "target", "innerText", "back", "limit", "sort", "params", "dataParams", "Object", "keys", "for<PERSON>ach", "quickSearchCustomer", "totalElements", "quickSearchContract", "USER_STATUS", "ACTIVE", "INACTIVE", "selectedApiIds", "join", "console", "log", "copyParam", "Set", "generateToken", "n", "chars", "token", "i", "Math", "floor", "random", "length", "genToken", "ɵɵdirectiveInject", "i1", "AccountService", "i2", "CustomerService", "i3", "ContractService", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "AppProfileDetailComponent_Template", "rf", "ctx", "AppProfileDetailComponent_Template_p_tabView_onChange_4_listener", "AppProfileDetailComponent_div_6_Template", "AppProfileDetailComponent_p_tabPanel_7_Template", "AppProfileDetailComponent_p_tabPanel_8_Template", "AppProfileDetailComponent_p_tabPanel_9_Template", "AppProfileDetailComponent_p_button_11_Template", "AppProfileDetailComponent_Template_p_button_click_12_listener", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵpureFunction1", "_c2", "PERMISSIONS", "PROFILE", "UPDATE"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\profile\\detail\\app.profile.detail.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\profile\\detail\\app.profile.detail.component.html"], "sourcesContent": ["import {AfterContentChecked, Component, Injector, OnInit} from \"@angular/core\";\r\nimport { MenuItem } from \"primeng/api\";\r\nimport { AccountService } from \"src/app/service/account/AccountService\";\r\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\r\nimport {ComponentBase} from \"../../../component.base\";\r\nimport {CustomerService} from \"../../../service/customer/CustomerService\";\r\nimport {ContractService} from \"../../../service/contract/ContractService\";\r\nimport {ColumnInfo, OptionTable} from \"../../common-module/table/table.component\";\r\n\r\n@Component({\r\n    selector: \"app-account-detail\",\r\n    templateUrl: './app.profile.detail.component.html'\r\n})\r\nexport class AppProfileDetailComponent extends ComponentBase implements OnInit, AfterContentChecked{\r\n    constructor(\r\n                public accountService: AccountService,\r\n                private customerService: CustomerService,\r\n                private contractService: ContractService,\r\n                injector: Injector\r\n                ) {\r\n        super(injector);\r\n    }\r\n    items: Array<MenuItem>;\r\n    home: MenuItem;\r\n    userInfo : any\r\n    accountInfo: {\r\n        accountName: string| null,\r\n        fullName: string|null,\r\n        email: string|null,\r\n        phone: string|null,\r\n        userType: number| null,\r\n        province: any,\r\n        roles: Array<any>,\r\n        description: string|null,\r\n        manager: any,\r\n        customers: Array<any>\r\n    };\r\n    listRole: Array<any>;\r\n    listProvince: Array<any>;\r\n    listCustomer: Array<any>;\r\n    optionUserType: any;\r\n    oldUserType: number | null = null;\r\n    accountResponse: any;\r\n\r\n    paginationCustomer: {\r\n        page: number|null,\r\n        size: number|null,\r\n        sortBy: string|null,\r\n    }\r\n    paramQuickSearchCustomer: {\r\n        keyword: string|null,\r\n        accountRootId: number| null,\r\n    }\r\n    dataSetCustomer: {\r\n        content: Array<any>,\r\n        total: number,\r\n    }\r\n    paginationContract: {\r\n        page: number|null,\r\n        size: number|null,\r\n        sortBy: string|null,\r\n    }\r\n    paramQuickSearchContract: {\r\n        keyword: string|null,\r\n        accountRootId: number| null,\r\n        customerIds: Array<{ id: number }>|null,\r\n    }\r\n    dataSetContract: {\r\n        content: Array<any>,\r\n        total: number,\r\n    }\r\n    userType: number;\r\n    columnInfoCustomer: Array<ColumnInfo>;\r\n    optionTableCustomer: OptionTable;\r\n    columnInfoContract: Array<ColumnInfo>;\r\n    optionTableContract: OptionTable;\r\n    accountId: number | string;\r\n\r\n    isShowSecretKey = true\r\n    listModule = []\r\n    //sẽ lưu lại list api sau khi đã chọn\r\n    selectItemGrantApi: Array<any> = []\r\n    paginationGrantApi: {\r\n        page: number|null,\r\n        size: number|null,\r\n        sortBy: string|null,\r\n    }\r\n    columnInfoGrantApi: Array<ColumnInfo>;\r\n\r\n    dataSetGrantApi: {\r\n        content: Array<any>,\r\n        total: number,\r\n    }\r\n    optionTableGrantApi: OptionTable;\r\n\r\n    paramsSearchGrantApi = {api : null, module : null}\r\n\r\n    genGrantApi = {clientId: '', secretKey: ''}\r\n\r\n    statusGrantApi : any;\r\n\r\n\r\n    ngOnInit(): void {\r\n        this.userInfo = this.sessionService.userInfo;\r\n        this.accountId = this.userInfo.id;\r\n        this.userType = this.sessionService.userInfo.type;\r\n        this.optionUserType = CONSTANTS.USER_TYPE;\r\n        this.items = [\r\n            { label: this.tranService.translate(\"global.menu.account\")},\r\n            { label: this.tranService.translate(\"global.menu.detailAccount\") }\r\n        ];\r\n        this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n\r\n        let fullTypeAccount = [\r\n            {name: this.tranService.translate(\"account.usertype.admin\"),value:CONSTANTS.USER_TYPE.ADMIN, accepts:[CONSTANTS.USER_TYPE.ADMIN]},\r\n            {name: this.tranService.translate(\"account.usertype.customer\"),value:CONSTANTS.USER_TYPE.CUSTOMER,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT, CONSTANTS.USER_TYPE.AGENCY, CONSTANTS.USER_TYPE.CUSTOMER]},\r\n            {name: this.tranService.translate(\"account.usertype.province\"),value:CONSTANTS.USER_TYPE.PROVINCE,accepts:[CONSTANTS.USER_TYPE.ADMIN]},\r\n            {name: this.tranService.translate(\"account.usertype.district\"),value:CONSTANTS.USER_TYPE.DISTRICT,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE]},\r\n            {name: this.tranService.translate(\"account.usertype.agency\"),value:CONSTANTS.USER_TYPE.AGENCY,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT]},\r\n        ]\r\n        this.accountInfo = {\r\n            accountName: null,\r\n            fullName: null,\r\n            email: null,\r\n            phone: null,\r\n            userType: null,\r\n            province: null,\r\n            roles: null,\r\n            description: null,\r\n            manager: null,\r\n            customers: null\r\n        }\r\n        this.paginationGrantApi = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"id,desc\",\r\n        }\r\n        this.columnInfoGrantApi = [\r\n            {\r\n                name: \"API\",\r\n                key: \"name\",\r\n                size: \"30%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n            },\r\n            {\r\n                name: \"Module\",\r\n                key: \"module\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n            }\r\n        ]\r\n\r\n        this.dataSetGrantApi = {\r\n            content: [],\r\n            total: 0,\r\n        }\r\n\r\n        this.optionTableGrantApi = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n        }\r\n        this.accountResponse = {}\r\n        this.getListProvince();\r\n        this.getDetail();\r\n\r\n        this.paramQuickSearchCustomer = {\r\n            keyword: null,\r\n            accountRootId: Number(this.accountId),\r\n        }\r\n        this.columnInfoCustomer = [\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerCode\"),\r\n                key: \"code\",\r\n                size: \"30%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerName\"),\r\n                key: \"name\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n        ]\r\n        this.dataSetCustomer = {\r\n            content: [],\r\n            total: 0,\r\n        }\r\n        this.paginationCustomer = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"name,asc;id,asc\",\r\n        }\r\n        this.optionTableCustomer = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n        }\r\n        this.paramQuickSearchContract = {\r\n            keyword: null,\r\n            accountRootId: Number(this.accountId),\r\n            customerIds: [],\r\n        }\r\n        this.columnInfoContract = [\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerCode\"),\r\n                key: \"customerCode\",\r\n                size: \"30%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerName\"),\r\n                key: \"customerName\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"contract.label.contractCode\"),\r\n                key: \"contractCode\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n        ]\r\n        this.dataSetContract = {\r\n            content: [],\r\n            total: 0,\r\n        }\r\n        this.paginationContract = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"customerName,asc;id,asc\",\r\n        }\r\n        this.optionTableContract = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n        }\r\n    }\r\n\r\n    ngAfterContentChecked(): void {\r\n\r\n    }\r\n\r\n    getDetail(){\r\n        let me = this;\r\n        let accountid = this.userInfo.id;\r\n        // console.log(accountid)\r\n        me.messageCommonService.onload();\r\n        this.accountService.viewProfile((response)=>{\r\n            me.accountResponse = response;\r\n            me.accountInfo.accountName = response.username;\r\n            me.accountInfo.fullName = response.fullName;\r\n            me.accountInfo.email = response.email;\r\n            me.accountInfo.description = response.description;\r\n            me.accountInfo.phone = response.phone;\r\n            me.accountInfo.province = response.provinceCode;\r\n            me.accountInfo.userType = response.type;\r\n            me.getListRole(false);\r\n            if (me.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\r\n                me.resetPaginationCustomerAndContract()\r\n                me.paramQuickSearchCustomer.accountRootId = Number(me.accountId)\r\n                me.paramQuickSearchContract.accountRootId = Number(me.accountId)\r\n                me.paramQuickSearchContract.customerIds = (me.accountResponse.customers|| []).map(customer => customer.customerId)\r\n            }\r\n            me.statusGrantApi = response.statusApi\r\n            me.selectItemGrantApi = response.listApiId? response.listApiId.map(el=> ({id: el})) : [{id:-99}]\r\n            me.genGrantApi.secretKey = response.secretId\r\n            me.genGrantApi.clientId = response.username\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    getListRole(isClear){\r\n        this.accountService.getListRole(this.accountInfo.userType, (response)=>{\r\n            this.listRole = response.map(el => {\r\n                return {\r\n                    id: el.id,\r\n                    name: el.name\r\n                }\r\n            });\r\n            if(isClear){\r\n                this.accountInfo.roles = null;\r\n            }else{\r\n                this.accountInfo.roles = this.listRole.filter(el => (this.accountResponse.roles||[]).includes(el.id));\r\n            }\r\n        })\r\n    }\r\n\r\n\r\n    getListProvince(){\r\n        this.accountService.getListProvince((response)=>{\r\n            this.listProvince = response.map(el => {\r\n                return {\r\n                    id: el.code,\r\n                    name: `${el.name} (${el.code})`\r\n                }\r\n            })\r\n        })\r\n    }\r\n\r\n    goToEdit(){\r\n        this.router.navigate([`/profile/edit/`]);\r\n    }\r\n\r\n    getStringCustomers(){\r\n        return (this.accountResponse.customers || []).map(el => el.customerName + ' - ' + el.customerCode).toLocaleString();\r\n    }\r\n\r\n    getStringRoles(){\r\n        return (this.accountResponse.roles || []).map(el => el.roleName).toLocaleString()\r\n    }\r\n\r\n    getStringUserType(value) {\r\n        if(value == CONSTANTS.USER_TYPE.ADMIN){\r\n            return this.tranService.translate(\"account.usertype.admin\");\r\n        }else if(value == CONSTANTS.USER_TYPE.CUSTOMER){\r\n            return this.tranService.translate(\"account.usertype.customer\");\r\n        }else if(value == CONSTANTS.USER_TYPE.PROVINCE){\r\n            return this.tranService.translate(\"account.usertype.province\");\r\n        }else if(value == CONSTANTS.USER_TYPE.DISTRICT){\r\n            return this.tranService.translate(\"account.usertype.district\");\r\n        }else if(value == CONSTANTS.USER_TYPE.AGENCY){\r\n            return this.tranService.translate(\"account.usertype.agency\");\r\n        }else{\r\n            return \"\";\r\n        }\r\n    }\r\n    goToChangePass() {\r\n        this.router.navigate(['/profile/change-password'])\r\n    }\r\n\r\n    onTabChange(event) {\r\n        const tabName = event.originalEvent.target.innerText;\r\n        let me = this;\r\n        if (event && tabName.includes(this.tranService.translate('account.text.grantApi'))) {\r\n            me.onSearchGrantApi()\r\n        } else if (event && tabName.includes(this.tranService.translate('global.menu.listbill'))) {\r\n            me.onSearchContract()\r\n        } else if (event && tabName.includes(this.tranService.translate('global.menu.listcustomer'))) {\r\n            me.onSearchCustomer()\r\n        }\r\n    }\r\n    onSearchCustomer(back?) {\r\n        let me = this;\r\n        if (back) {\r\n            me.paginationCustomer.page = 0;\r\n        }\r\n        me.searchCustomer(me.paginationCustomer.page, me.paginationCustomer.size, me.paginationCustomer.sortBy, me.paramQuickSearchCustomer);\r\n    }\r\n    onSearchContract(back?) {\r\n        let me = this;\r\n        if (back) {\r\n            me.paginationContract.page = 0;\r\n        }\r\n        me.searchContract(me.paginationContract.page, me.paginationContract.size, me.paginationContract.sortBy, me.paramQuickSearchContract);\r\n    }\r\n    searchCustomer(page, limit, sort, params){\r\n        let me = this;\r\n        this.paginationCustomer.page = page;\r\n        this.paginationCustomer.size = limit;\r\n        this.paginationCustomer.sortBy = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        Object.keys(this.paramQuickSearchCustomer).forEach(key => {\r\n            if(this.paramQuickSearchCustomer[key] != null){\r\n                dataParams[key] = this.paramQuickSearchCustomer[key];\r\n            }\r\n        })\r\n        me.messageCommonService.onload();\r\n        this.customerService.quickSearchCustomer(dataParams, this.paramQuickSearchCustomer,(response)=>{\r\n            me.dataSetCustomer = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n        // console.log(this.selectItemCustomer)\r\n    }\r\n    searchContract(page, limit, sort, params){\r\n        let me = this;\r\n        this.paginationContract.page = page;\r\n        this.paginationContract.size = limit;\r\n        this.paginationContract.sortBy = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        // Object.keys(this.paramQuickSearchContract).forEach(key => {\r\n        //     if(this.paramQuickSearchContract[key] != null){\r\n        //         dataParams[key] = this.paramQuickSearchContract[key];\r\n        //     }\r\n        // })\r\n        me.messageCommonService.onload();\r\n        this.contractService.quickSearchContract(dataParams, this.paramQuickSearchContract,(response)=>{\r\n            me.dataSetContract = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n            // console.log(response)\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n    getStringUserStatus(value) {\r\n        if(value == CONSTANTS.USER_STATUS.ACTIVE){\r\n            return this.tranService.translate(\"account.userstatus.active\");\r\n        }else if(value == CONSTANTS.USER_STATUS.INACTIVE){\r\n            return this.tranService.translate(\"account.userstatus.inactive\");\r\n        }else{\r\n            return \"\";\r\n        }\r\n    }\r\n    resetPaginationCustomerAndContract() {\r\n        this.paginationCustomer = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"name,asc;id,asc\",\r\n        }\r\n        this.paginationContract = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"customerName,asc;id,asc\",\r\n        }\r\n    }\r\n\r\n    searchGrantApi(page, limit, sort, params){\r\n        let me = this;\r\n        this.paginationGrantApi.page = page;\r\n        this.paginationGrantApi.size = limit;\r\n        this.paginationGrantApi.sortBy = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort,\r\n            selectedApiIds: this.selectItemGrantApi.map(el=>el.id).join(',')\r\n        }\r\n        Object.keys(this.paramsSearchGrantApi).forEach(key => {\r\n            if(this.paramsSearchGrantApi[key] != null){\r\n                dataParams[key] = this.paramsSearchGrantApi[key];\r\n            }\r\n        })\r\n        console.log(dataParams)\r\n        me.messageCommonService.onload();\r\n        this.accountService.searchGrantApi(dataParams,(response)=>{\r\n            me.dataSetGrantApi = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n        let copyParam = {...dataParams};\r\n        copyParam.size = *********;\r\n        this.accountService.searchGrantApi(copyParam,(response)=>{\r\n            me.listModule = [...new Set(response.content.map(el=>el.module))]\r\n            me.listModule = me.listModule.map(el=>({\r\n                name : el,\r\n                value : el\r\n            }))\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    generateToken(n) {\r\n        var chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r\n        var token = '';\r\n        for(var i = 0; i < n; i++) {\r\n            token += chars[Math.floor(Math.random() * chars.length)];\r\n        }\r\n        return token;\r\n    }\r\n\r\n    genToken(){\r\n        this.genGrantApi.secretKey = this.generateToken(20);\r\n    }\r\n\r\n    onSearchGrantApi(back?) {\r\n        let me = this;\r\n        console.log(me.paramsSearchGrantApi)\r\n        if(back) {\r\n            me.paginationGrantApi.page = 0;\r\n        }\r\n        me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);\r\n    }\r\n\r\n    protected readonly CONSTANTS = CONSTANTS;\r\n}\r\n", "<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <p-breadcrumb class=\"max-w-full col-12\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n</div>\r\n\r\n<p-card styleClass=\"mt-3 responsive-form\">\r\n    <div class=\"w-full\">\r\n        <p-tabView (onChange)=\"onTabChange($event)\">\r\n            <p-tabPanel header=\"{{tranService.translate('account.label.generalInfo')}}\">\r\n                <div class=\"flex flex-row justify-content-between profile-create\" *ngIf=\"accountResponse\">\r\n                    <div style=\"width: 49%;\">\r\n                        <!-- username -->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"accountName\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.username\")}}</label>\r\n                            <div class=\"col\">\r\n                                {{accountResponse.username}}\r\n                            </div>\r\n                        </div>\r\n                        <!-- status -->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"fullName\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.status\")}}</label>\r\n                            <div class=\"col wrap-div\">\r\n                                {{getStringUserStatus(accountResponse.status)}}\r\n                            </div>\r\n                        </div>\r\n                        <!-- fullname -->\r\n                        <div class=\"w-full field grid \">\r\n                            <label htmlFor=\"fullName\" class=\"col-fixed\" style=\"width:180px;height: fit-content;\">{{tranService.translate(\"account.label.fullname\")}}</label>\r\n                            <div class=\"col wrap-div\" style=\"width: calc(100% - 180px);overflow-wrap: break-word;\">\r\n                                {{accountResponse.fullName}}\r\n                            </div>\r\n                        </div>\r\n                        <!-- phone -->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"phone\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.phone\")}}</label>\r\n                            <div class=\"col\">\r\n                                {{accountResponse.phone}}\r\n                            </div>\r\n                        </div>\r\n                        <!-- email -->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"email\" class=\"col-fixed\" style=\"width:180px;height: fit-content;\">{{tranService.translate(\"account.label.email\")}}</label>\r\n                            <div class=\"col wrap-div\" style=\"width: calc(100% - 180px);overflow-wrap: break-word;\">\r\n                                {{accountResponse.email}}\r\n                            </div>\r\n                        </div>\r\n                        <!-- description -->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"description\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.description\")}}</label>\r\n                            <div class=\"col\">\r\n                                {{accountResponse.description}}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div style=\"width: 49%;\">\r\n                        <!-- loai tai khoan -->\r\n                        <div class=\"w-full field grid\">\r\n                            <label for=\"userType\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.userType\")}}</label>\r\n                            <div class=\"col\">\r\n                                <span>{{getStringUserType(accountResponse.type)}}</span>\r\n                            </div>\r\n                        </div>\r\n                        <!-- Tinh thanh pho -->\r\n                        <div class=\"w-full field grid\" *ngIf=\"accountInfo.userType != optionUserType.ADMIN && accountInfo.userType != optionUserType.AGENCY\">\r\n                            <label htmlFor=\"province\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.province\")}}</label>\r\n                            <div class=\"col\">\r\n                                <span>{{accountResponse.provinceName}} ({{accountResponse.provinceCode}})</span>\r\n                            </div>\r\n                        </div>\r\n                        <!--                &lt;!&ndash; ten khach hang &ndash;&gt;-->\r\n                        <!--                <div class=\"w-full field grid\" *ngIf=\"accountInfo.userType == optionUserType.CUSTOMER\">-->\r\n                        <!--                    <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px;height: fit-content;\">{{tranService.translate(\"account.label.customerName\")}}</label>-->\r\n                        <!--                    <div class=\"col\" style=\"max-width: calc(100% - 180px) !important;\">-->\r\n                        <!--                        <div *ngFor=\"let item of accountResponse.customers\">-->\r\n                        <!--                            {{ item.customerName + ' - ' + item.customerCode}}-->\r\n                        <!--                        </div>-->\r\n                        <!--                    </div>-->\r\n                        <!--                </div>-->\r\n                        <!-- GDV quan ly-->\r\n                        <div class=\"w-full field grid\" [class]=\"accountInfo.userType == optionUserType.CUSTOMER && (userType == optionUserType.ADMIN || userType == optionUserType.PROVINCE) ? '' : 'hidden'\">\r\n                            <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.managerName\")}}</label>\r\n                            <div class=\"col\" style=\"max-width: calc(100% - 180px) !important;\">\r\n                                {{ accountResponse?.manager?.username }}\r\n                            </div>\r\n                        </div>\r\n                        <!-- Danh sach tai khoan khach hang -->\r\n                        <div class=\"w-full field grid align-items-start\" *ngIf=\"accountInfo.userType == optionUserType.DISTRICT\">\r\n                            <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.customerAccount\")}}</label>\r\n                            <div class=\"col\" style=\"max-width: calc(100% - 180px) !important;\">\r\n                                <div *ngFor=\"let item of accountResponse?.userManages\">\r\n                                    {{ item.username}}\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <!-- Tài khoản khách hàng root khi-->\r\n                        <div class=\"w-full field grid align-items-start\" *ngIf=\"accountInfo.userType == optionUserType.CUSTOMER && !accountResponse?.isRootCustomer\">\r\n                            <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.customerAccount\")}}</label>\r\n                            <div class=\"col\" style=\"max-width: calc(100% - 180px) !important;\">\r\n                                <div *ngIf=\"accountResponse?.rootAccount?.username\">\r\n                                    {{ accountResponse.rootAccount.username}}\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <!-- nhom quyen -->\r\n                        <div class=\"w-full field grid\">\r\n                            <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px; height: fit-content;\">{{tranService.translate(\"account.label.role\")}}</label>\r\n                            <div class=\"col\" style=\"max-width: calc(100% - 180px) !important;\">\r\n                                <!-- <div>{{getStringRoles()}}</div> -->\r\n                                <div *ngFor=\"let item of accountResponse.roles\">\r\n                                    {{ item.roleName}}\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </p-tabPanel>\r\n            <p-tabPanel header=\"{{tranService.translate('global.menu.listcustomer')}}\" *ngIf=\"accountInfo.userType ==  CONSTANTS.USER_TYPE.CUSTOMER\">\r\n                <div class=\"flex flex-row justify-content-center gap-3 mt-4\">\r\n                    <input style=\"min-width: 35vw\"  type=\"text\" pInputText [placeholder]=\"tranService.translate('sim.label.quickSearch')\" (keydown.enter)=\"onSearchCustomer(true)\" [(ngModel)]=\"paramQuickSearchCustomer.keyword\" [ngModelOptions]=\"{standalone: true}\">\r\n                    <p-button icon=\"pi pi-search\"\r\n                              styleClass=\"ml-3 p-button-rounded p-button-secondary p-button-text button-search\"\r\n                              type=\"button\"\r\n                              (click)=\"onSearchCustomer(true)\"\r\n                    ></p-button>\r\n                </div>\r\n                <table-vnpt\r\n                    [fieldId]=\"'id'\"\r\n                    [pageNumber]=\"paginationCustomer.page\"\r\n                    [pageSize]=\"paginationCustomer.size\"\r\n                    [columns]=\"columnInfoCustomer\"\r\n                    [dataSet]=\"dataSetCustomer\"\r\n                    [options]=\"optionTableCustomer\"\r\n                    [loadData]=\"searchCustomer.bind(this)\"\r\n                    [rowsPerPageOptions]=\"[5,10,20,25,50]\"\r\n                    [scrollHeight]=\"'400px'\"\r\n                    [sort]=\"paginationCustomer.sortBy\"\r\n                    [params]=\"paramQuickSearchCustomer\"\r\n                ></table-vnpt>\r\n            </p-tabPanel>\r\n            <p-tabPanel header=\"{{tranService.translate('global.menu.listbill')}}\" *ngIf=\"accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER\">\r\n                <div class=\"flex flex-row justify-content-center gap-3 mt-4\">\r\n                    <input style=\"min-width: 35vw\"  type=\"text\" pInputText [placeholder]=\"tranService.translate('sim.label.quickSearch')\" (keydown.enter)=\"onSearchContract(true)\" [(ngModel)]=\"paramQuickSearchContract.keyword\" [ngModelOptions]=\"{standalone: true}\">\r\n                    <p-button icon=\"pi pi-search\"\r\n                              styleClass=\"ml-3 p-button-rounded p-button-secondary p-button-text button-search\"\r\n                              type=\"button\"\r\n                              (click)=\"onSearchContract(true)\"\r\n                    ></p-button>\r\n                </div>\r\n                <table-vnpt\r\n                    [fieldId]=\"'id'\"\r\n                    [pageNumber]=\"paginationContract.page\"\r\n                    [pageSize]=\"paginationContract.size\"\r\n                    [columns]=\"columnInfoContract\"\r\n                    [dataSet]=\"dataSetContract\"\r\n                    [options]=\"optionTableContract\"\r\n                    [loadData]=\"searchContract.bind(this)\"\r\n                    [rowsPerPageOptions]=\"[5,10,20,25,50]\"\r\n                    [scrollHeight]=\"'400px'\"\r\n                    [sort]=\"paginationContract.sortBy\"\r\n                    [params]=\"paramQuickSearchContract\"\r\n                ></table-vnpt>\r\n            </p-tabPanel>\r\n            <p-tabPanel header=\"{{tranService.translate('account.text.grantApi')}}\" *ngIf=\"userType == optionUserType.CUSTOMER && genGrantApi.secretKey != null\" [pt]=\"'ProfileTab'\">\r\n                <div class=\"mb-3\">\r\n                    <p-panel [showHeader]=\"false\">\r\n                        <div class=\"flex gap-2\">\r\n                            <p-radioButton\r\n                                    [label]=\"tranService.translate('account.text.working')\"\r\n                                    value=\"1\"\r\n                                    class=\"p-3\"\r\n                                    [(ngModel)]=\"statusGrantApi\"\r\n                                    [disabled]=\"true\"\r\n                                    [ngModelOptions]=\"{standalone: true}\"\r\n                            ></p-radioButton>\r\n\r\n                            <p-radioButton\r\n                                    [label]=\"tranService.translate('account.text.notWorking')\"\r\n                                    value=\"0\"\r\n                                    class=\"p-3\"\r\n                                    [(ngModel)]=\"statusGrantApi\"\r\n                                    [disabled]=\"true\"\r\n                                    [ngModelOptions]=\"{standalone: true}\"\r\n                            ></p-radioButton>\r\n                        </div>\r\n                        <div class=\"flex gap-3 align-items-center\">\r\n                            <div class=\"col-6\">\r\n                                <div class=\"flex align-items-center\">\r\n                                    <label style=\"min-width: 100px\" class=\"mr-3\">Client ID</label>\r\n                                    <input [(ngModel)]=\"genGrantApi.clientId\"  [disabled]=\"true\" [ngModelOptions]=\"{standalone: true}\" class=\"w-full\" type=\"text\" pInputText>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-6\">\r\n                                <div class=\"flex align-items-center\">\r\n                                    <label style=\"min-width: 100px\" class=\"mr-3\">Secret Key</label>\r\n                                    <div class=\"w-full flex align-items-center\">\r\n                                        <input class=\"w-full mr-2\" style=\"padding-right: 30px;\"\r\n                                               [(ngModel)]=\"genGrantApi.secretKey\"\r\n                                               [ngModelOptions]=\"{standalone: true}\"\r\n                                               [type]=\"isShowSecretKey ? 'text': 'password'\"\r\n                                               pInputText\r\n                                               [disabled]=\"true\"\r\n                                        />\r\n                                        <label style=\"margin-left: -30px;z-index: 1;\" *ngIf=\"isShowSecretKey == false\" class=\"pi pi-eye toggle-password\" (click)=\"isShowSecretKey = true\"></label>\r\n                                        <label style=\"margin-left: -30px;z-index: 1;\" *ngIf=\"isShowSecretKey == true\" class=\"pi pi-eye-slash toggle-password\" (click)=\"isShowSecretKey = false\"></label>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </p-panel>\r\n                </div>\r\n                <div>\r\n                    <p-panel [showHeader]=\"false\" class=\"  \">\r\n                        <div class=\"flex gap-3 align-items-center\">\r\n                            <div class=\"col-3\">\r\n                                <p-dropdown class=\"w-full\"\r\n                                            [showClear]=\"true\"\r\n                                            [(ngModel)]=\"paramsSearchGrantApi.module\"\r\n                                            [ngModelOptions]=\"{standalone: true}\"\r\n                                            [options]=\"listModule\"\r\n                                            optionLabel=\"name\"\r\n                                            optionValue=\"value\"\r\n                                            [emptyFilterMessage]=\"tranService.translate('global.text.nodata')\"\r\n                                            filter=\"true\"\r\n                                            [placeholder]=\"tranService.translate('account.text.module')\"\r\n                                ></p-dropdown>\r\n                            </div>\r\n                            <div class=\"col-3\">\r\n                                <input [(ngModel)]=\"paramsSearchGrantApi.api\" [ngModelOptions]=\"{standalone: true}\" class=\"w-full mr-2\" type=\"text\" pInputText placeholder=\"API\"/>\r\n                            </div>\r\n                            <p-button icon=\"pi pi-search\"\r\n                                      styleClass=\"ml-3 p-button-rounded p-button-secondary p-button-text button-search\"\r\n                                      type=\"button\"\r\n                                      (click)=\"onSearchGrantApi(true)\"\r\n                            ></p-button>\r\n                        </div>\r\n\r\n                        <table-vnpt\r\n                                [fieldId]=\"'id'\"\r\n                                [pageNumber]=\"paginationGrantApi.page\"\r\n                                [pageSize]=\"paginationGrantApi.size\"\r\n                                [columns]=\"columnInfoGrantApi\"\r\n                                [dataSet]=\"dataSetGrantApi\"\r\n                                [options]=\"optionTableGrantApi\"\r\n                                [loadData]=\"searchGrantApi.bind(this)\"\r\n                                [rowsPerPageOptions]=\"[5,10,20,25,50]\"\r\n                                [scrollHeight]=\"'400px'\"\r\n                                [sort]=\"paginationGrantApi.sortBy\"\r\n                                [params]=\"paramsSearchGrantApi\"\r\n                        ></table-vnpt>\r\n                    </p-panel>\r\n                </div>\r\n            </p-tabPanel>\r\n        </p-tabView>\r\n    </div>\r\n    <div class=\"flex justify-content-center\">\r\n        <p-button *ngIf=\"checkAuthen([CONSTANTS.PERMISSIONS.PROFILE.UPDATE])\" [label]=\"tranService.translate('global.menu.editAccount')\"  styleClass=\"p-button-info\" class=\"mx-5 \" (click)=\"goToEdit()\"></p-button>\r\n        <p-button [label]=\"tranService.translate('global.button.changePass')\" styleClass=\"p-button-info bg-cyan-500 border-none\" class=\"mx-5\" (click)=\"goToChangePass()\"></p-button>\r\n<!--        <p-button [label]=\"tranService.translate('global.button.back')\" class=\"px-5\" styleClass=\"p-button-info\"></p-button>-->\r\n    </div>\r\n</p-card>\r\n"], "mappings": "AAGA,SAASA,SAAS,QAAQ,iCAAiC;AAC3D,SAAQC,aAAa,QAAO,yBAAyB;;;;;;;;;;;;;;;;;IC0D7BC,EAAA,CAAAC,cAAA,cAAqI;IACjED,EAAA,CAAAE,MAAA,GAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3HH,EAAA,CAAAC,cAAA,cAAiB;IACPD,EAAA,CAAAE,MAAA,GAAmE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAFpBH,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,2BAAmD;IAEzGR,EAAA,CAAAI,SAAA,GAAmE;IAAnEJ,EAAA,CAAAS,kBAAA,KAAAH,MAAA,CAAAI,eAAA,CAAAC,YAAA,QAAAL,MAAA,CAAAI,eAAA,CAAAE,YAAA,MAAmE;;;;;IAuBzEZ,EAAA,CAAAC,cAAA,UAAuD;IACnDD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAa,kBAAA,MAAAC,QAAA,CAAAC,QAAA,MACJ;;;;;IALRf,EAAA,CAAAC,cAAA,cAAyG;IACxCD,EAAA,CAAAE,MAAA,GAA0D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/HH,EAAA,CAAAC,cAAA,cAAmE;IAC/DD,EAAA,CAAAgB,UAAA,IAAAC,qDAAA,kBAEM;IACVjB,EAAA,CAAAG,YAAA,EAAM;;;;IALuDH,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAK,iBAAA,CAAAa,MAAA,CAAAX,WAAA,CAAAC,SAAA,kCAA0D;IAE7FR,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAmB,UAAA,YAAAD,MAAA,CAAAR,eAAA,kBAAAQ,MAAA,CAAAR,eAAA,CAAAU,WAAA,CAA+B;;;;;IASrDpB,EAAA,CAAAC,cAAA,UAAoD;IAChDD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAa,kBAAA,MAAAQ,OAAA,CAAAX,eAAA,CAAAY,WAAA,CAAAP,QAAA,MACJ;;;;;IALRf,EAAA,CAAAC,cAAA,cAA6I;IAC5ED,EAAA,CAAAE,MAAA,GAA0D;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/HH,EAAA,CAAAC,cAAA,cAAmE;IAC/DD,EAAA,CAAAgB,UAAA,IAAAO,qDAAA,kBAEM;IACVvB,EAAA,CAAAG,YAAA,EAAM;;;;IALuDH,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAK,iBAAA,CAAAmB,MAAA,CAAAjB,WAAA,CAAAC,SAAA,kCAA0D;IAE7GR,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAmB,UAAA,SAAAK,MAAA,CAAAd,eAAA,kBAAAc,MAAA,CAAAd,eAAA,CAAAY,WAAA,kBAAAE,MAAA,CAAAd,eAAA,CAAAY,WAAA,CAAAP,QAAA,CAA4C;;;;;IAUlDf,EAAA,CAAAC,cAAA,UAAgD;IAC5CD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAa,kBAAA,MAAAY,QAAA,CAAAC,QAAA,MACJ;;;;;IArGhB1B,EAAA,CAAAC,cAAA,cAA0F;IAIXD,EAAA,CAAAE,MAAA,GAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9HH,EAAA,CAAAC,cAAA,cAAiB;IACbD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,cAA+B;IACqCD,EAAA,CAAAE,MAAA,GAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzHH,EAAA,CAAAC,cAAA,eAA0B;IACtBD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAAgC;IACyDD,EAAA,CAAAE,MAAA,IAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAChJH,EAAA,CAAAC,cAAA,eAAuF;IACnFD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA+B;IACkCD,EAAA,CAAAE,MAAA,IAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrHH,EAAA,CAAAC,cAAA,eAAiB;IACbD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA+B;IACuDD,EAAA,CAAAE,MAAA,IAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1IH,EAAA,CAAAC,cAAA,eAAuF;IACnFD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAA+B;IACwCD,EAAA,CAAAE,MAAA,IAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjIH,EAAA,CAAAC,cAAA,eAAiB;IACbD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGdH,EAAA,CAAAC,cAAA,eAAyB;IAG2CD,EAAA,CAAAE,MAAA,IAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvHH,EAAA,CAAAC,cAAA,eAAiB;IACPD,EAAA,CAAAE,MAAA,IAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIhEH,EAAA,CAAAgB,UAAA,KAAAW,+CAAA,kBAKM;IAWN3B,EAAA,CAAAC,cAAA,eAAsL;IACrHD,EAAA,CAAAE,MAAA,IAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3HH,EAAA,CAAAC,cAAA,eAAmE;IAC/DD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAgB,UAAA,KAAAY,+CAAA,kBAOM;IAEN5B,EAAA,CAAAgB,UAAA,KAAAa,+CAAA,kBAOM;IAEN7B,EAAA,CAAAC,cAAA,eAA+B;IACwDD,EAAA,CAAAE,MAAA,IAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1IH,EAAA,CAAAC,cAAA,eAAmE;IAE/DD,EAAA,CAAAgB,UAAA,KAAAc,+CAAA,kBAEM;IACV9B,EAAA,CAAAG,YAAA,EAAM;;;;IAlG6DH,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAA0B,MAAA,CAAAxB,WAAA,CAAAC,SAAA,2BAAmD;IAElHR,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAa,kBAAA,MAAAkB,MAAA,CAAArB,eAAA,CAAAK,QAAA,MACJ;IAIgEf,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAK,iBAAA,CAAA0B,MAAA,CAAAxB,WAAA,CAAAC,SAAA,yBAAiD;IAE7GR,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAa,kBAAA,MAAAkB,MAAA,CAAAC,mBAAA,CAAAD,MAAA,CAAArB,eAAA,CAAAuB,MAAA,OACJ;IAIqFjC,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAA0B,MAAA,CAAAxB,WAAA,CAAAC,SAAA,2BAAmD;IAEpIR,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAa,kBAAA,MAAAkB,MAAA,CAAArB,eAAA,CAAAwB,QAAA,MACJ;IAI6DlC,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,iBAAA,CAAA0B,MAAA,CAAAxB,WAAA,CAAAC,SAAA,wBAAgD;IAEzGR,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAa,kBAAA,MAAAkB,MAAA,CAAArB,eAAA,CAAAyB,KAAA,MACJ;IAIkFnC,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAK,iBAAA,CAAA0B,MAAA,CAAAxB,WAAA,CAAAC,SAAA,wBAAgD;IAE9HR,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAa,kBAAA,MAAAkB,MAAA,CAAArB,eAAA,CAAA0B,KAAA,MACJ;IAImEpC,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAA0B,MAAA,CAAAxB,WAAA,CAAAC,SAAA,8BAAsD;IAErHR,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAa,kBAAA,MAAAkB,MAAA,CAAArB,eAAA,CAAA2B,WAAA,MACJ;IAM4DrC,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,iBAAA,CAAA0B,MAAA,CAAAxB,WAAA,CAAAC,SAAA,2BAAmD;IAErGR,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAK,iBAAA,CAAA0B,MAAA,CAAAO,iBAAA,CAAAP,MAAA,CAAArB,eAAA,CAAA6B,IAAA,EAA2C;IAIzBvC,EAAA,CAAAI,SAAA,GAAmG;IAAnGJ,EAAA,CAAAmB,UAAA,SAAAY,MAAA,CAAAS,WAAA,CAAAC,QAAA,IAAAV,MAAA,CAAAW,cAAA,CAAAC,KAAA,IAAAZ,MAAA,CAAAS,WAAA,CAAAC,QAAA,IAAAV,MAAA,CAAAW,cAAA,CAAAE,MAAA,CAAmG;IAgBpG5C,EAAA,CAAAI,SAAA,GAAsJ;IAAtJJ,EAAA,CAAA6C,UAAA,CAAAd,MAAA,CAAAS,WAAA,CAAAC,QAAA,IAAAV,MAAA,CAAAW,cAAA,CAAAI,QAAA,KAAAf,MAAA,CAAAU,QAAA,IAAAV,MAAA,CAAAW,cAAA,CAAAC,KAAA,IAAAZ,MAAA,CAAAU,QAAA,IAAAV,MAAA,CAAAW,cAAA,CAAAK,QAAA,kBAAsJ;IACpH/C,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,iBAAA,CAAA0B,MAAA,CAAAxB,WAAA,CAAAC,SAAA,8BAAsD;IAE/GR,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAa,kBAAA,MAAAkB,MAAA,CAAArB,eAAA,kBAAAqB,MAAA,CAAArB,eAAA,CAAAsC,OAAA,kBAAAjB,MAAA,CAAArB,eAAA,CAAAsC,OAAA,CAAAjC,QAAA,MACJ;IAG8Cf,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAmB,UAAA,SAAAY,MAAA,CAAAS,WAAA,CAAAC,QAAA,IAAAV,MAAA,CAAAW,cAAA,CAAAO,QAAA,CAAqD;IASrDjD,EAAA,CAAAI,SAAA,GAAyF;IAAzFJ,EAAA,CAAAmB,UAAA,SAAAY,MAAA,CAAAS,WAAA,CAAAC,QAAA,IAAAV,MAAA,CAAAW,cAAA,CAAAI,QAAA,MAAAf,MAAA,CAAArB,eAAA,kBAAAqB,MAAA,CAAArB,eAAA,CAAAwC,cAAA,EAAyF;IAUpDlD,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAK,iBAAA,CAAA0B,MAAA,CAAAxB,WAAA,CAAAC,SAAA,uBAA+C;IAGxGR,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAmB,UAAA,YAAAY,MAAA,CAAArB,eAAA,CAAAyC,KAAA,CAAwB;;;;;;;;;;;;;;IAQlEnD,EAAA,CAAAC,cAAA,oBAAyI;IAEXD,EAAA,CAAAoD,UAAA,2BAAAC,+EAAA;MAAArD,EAAA,CAAAsD,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAxD,EAAA,CAAAyD,aAAA;MAAA,OAAiBzD,EAAA,CAAA0D,WAAA,CAAAF,OAAA,CAAAG,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC,2BAAAC,+EAAAC,MAAA;MAAA7D,EAAA,CAAAsD,aAAA,CAAAC,IAAA;MAAA,MAAAO,OAAA,GAAA9D,EAAA,CAAAyD,aAAA;MAAA,OAAczD,EAAA,CAAA0D,WAAA,CAAAI,OAAA,CAAAC,wBAAA,CAAAC,OAAA,GAAAH,MAAA,CAAwC;IAAA,EAAtD;IAA9J7D,EAAA,CAAAG,YAAA,EAAoP;IACpPH,EAAA,CAAAC,cAAA,mBAIC;IADSD,EAAA,CAAAoD,UAAA,mBAAAa,0EAAA;MAAAjE,EAAA,CAAAsD,aAAA,CAAAC,IAAA;MAAA,MAAAW,OAAA,GAAAlE,EAAA,CAAAyD,aAAA;MAAA,OAASzD,EAAA,CAAA0D,WAAA,CAAAQ,OAAA,CAAAP,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC;IACzC3D,EAAA,CAAAG,YAAA,EAAW;IAEhBH,EAAA,CAAAmE,SAAA,qBAYc;IAClBnE,EAAA,CAAAG,YAAA,EAAa;;;;IAtBDH,EAAA,CAAAoE,qBAAA,WAAAC,MAAA,CAAA9D,WAAA,CAAAC,SAAA,6BAA8D;IAEXR,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAmB,UAAA,gBAAAkD,MAAA,CAAA9D,WAAA,CAAAC,SAAA,0BAA8D,YAAA6D,MAAA,CAAAN,wBAAA,CAAAC,OAAA,oBAAAhE,EAAA,CAAAsE,eAAA,KAAAC,GAAA;IAQrHvE,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAmB,UAAA,iBAAgB,eAAAkD,MAAA,CAAAG,kBAAA,CAAAC,IAAA,cAAAJ,MAAA,CAAAG,kBAAA,CAAAE,IAAA,aAAAL,MAAA,CAAAM,kBAAA,aAAAN,MAAA,CAAAO,eAAA,aAAAP,MAAA,CAAAQ,mBAAA,cAAAR,MAAA,CAAAS,cAAA,CAAAC,IAAA,CAAAV,MAAA,yBAAArE,EAAA,CAAAsE,eAAA,KAAAU,GAAA,oCAAAX,MAAA,CAAAG,kBAAA,CAAAS,MAAA,YAAAZ,MAAA,CAAAN,wBAAA;;;;;;IAaxB/D,EAAA,CAAAC,cAAA,oBAAoI;IAEND,EAAA,CAAAoD,UAAA,2BAAA8B,+EAAA;MAAAlF,EAAA,CAAAsD,aAAA,CAAA6B,IAAA;MAAA,MAAAC,OAAA,GAAApF,EAAA,CAAAyD,aAAA;MAAA,OAAiBzD,EAAA,CAAA0D,WAAA,CAAA0B,OAAA,CAAAC,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC,2BAAAC,+EAAAzB,MAAA;MAAA7D,EAAA,CAAAsD,aAAA,CAAA6B,IAAA;MAAA,MAAAI,OAAA,GAAAvF,EAAA,CAAAyD,aAAA;MAAA,OAAczD,EAAA,CAAA0D,WAAA,CAAA6B,OAAA,CAAAC,wBAAA,CAAAxB,OAAA,GAAAH,MAAA,CAAwC;IAAA,EAAtD;IAA9J7D,EAAA,CAAAG,YAAA,EAAoP;IACpPH,EAAA,CAAAC,cAAA,mBAIC;IADSD,EAAA,CAAAoD,UAAA,mBAAAqC,0EAAA;MAAAzF,EAAA,CAAAsD,aAAA,CAAA6B,IAAA;MAAA,MAAAO,OAAA,GAAA1F,EAAA,CAAAyD,aAAA;MAAA,OAASzD,EAAA,CAAA0D,WAAA,CAAAgC,OAAA,CAAAL,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC;IACzCrF,EAAA,CAAAG,YAAA,EAAW;IAEhBH,EAAA,CAAAmE,SAAA,qBAYc;IAClBnE,EAAA,CAAAG,YAAA,EAAa;;;;IAtBDH,EAAA,CAAAoE,qBAAA,WAAAuB,MAAA,CAAApF,WAAA,CAAAC,SAAA,yBAA0D;IAEPR,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAmB,UAAA,gBAAAwE,MAAA,CAAApF,WAAA,CAAAC,SAAA,0BAA8D,YAAAmF,MAAA,CAAAH,wBAAA,CAAAxB,OAAA,oBAAAhE,EAAA,CAAAsE,eAAA,KAAAC,GAAA;IAQrHvE,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAmB,UAAA,iBAAgB,eAAAwE,MAAA,CAAAC,kBAAA,CAAAnB,IAAA,cAAAkB,MAAA,CAAAC,kBAAA,CAAAlB,IAAA,aAAAiB,MAAA,CAAAE,kBAAA,aAAAF,MAAA,CAAAG,eAAA,aAAAH,MAAA,CAAAI,mBAAA,cAAAJ,MAAA,CAAAK,cAAA,CAAAjB,IAAA,CAAAY,MAAA,yBAAA3F,EAAA,CAAAsE,eAAA,KAAAU,GAAA,oCAAAW,MAAA,CAAAC,kBAAA,CAAAX,MAAA,YAAAU,MAAA,CAAAH,wBAAA;;;;;;IAqDIxF,EAAA,CAAAC,cAAA,gBAAkJ;IAAjCD,EAAA,CAAAoD,UAAA,mBAAA6C,gFAAA;MAAAjG,EAAA,CAAAsD,aAAA,CAAA4C,IAAA;MAAA,MAAAC,OAAA,GAAAnG,EAAA,CAAAyD,aAAA;MAAA,OAAAzD,EAAA,CAAA0D,WAAA,CAAAyC,OAAA,CAAAC,eAAA,GAA2B,IAAI;IAAA,EAAC;IAACpG,EAAA,CAAAG,YAAA,EAAQ;;;;;;IAC1JH,EAAA,CAAAC,cAAA,gBAAwJ;IAAlCD,EAAA,CAAAoD,UAAA,mBAAAiD,gFAAA;MAAArG,EAAA,CAAAsD,aAAA,CAAAgD,IAAA;MAAA,MAAAC,OAAA,GAAAvG,EAAA,CAAAyD,aAAA;MAAA,OAAAzD,EAAA,CAAA0D,WAAA,CAAA6C,OAAA,CAAAH,eAAA,GAA2B,KAAK;IAAA,EAAC;IAACpG,EAAA,CAAAG,YAAA,EAAQ;;;;;;IAzC5LH,EAAA,CAAAC,cAAA,qBAAyK;IAQjJD,EAAA,CAAAoD,UAAA,2BAAAoD,uFAAA3C,MAAA;MAAA7D,EAAA,CAAAsD,aAAA,CAAAmD,IAAA;MAAA,MAAAC,OAAA,GAAA1G,EAAA,CAAAyD,aAAA;MAAA,OAAAzD,EAAA,CAAA0D,WAAA,CAAAgD,OAAA,CAAAC,cAAA,GAAA9C,MAAA;IAAA,EAA4B;IAGnC7D,EAAA,CAAAG,YAAA,EAAgB;IAEjBH,EAAA,CAAAC,cAAA,wBAOC;IAHOD,EAAA,CAAAoD,UAAA,2BAAAwD,uFAAA/C,MAAA;MAAA7D,EAAA,CAAAsD,aAAA,CAAAmD,IAAA;MAAA,MAAAI,OAAA,GAAA7G,EAAA,CAAAyD,aAAA;MAAA,OAAAzD,EAAA,CAAA0D,WAAA,CAAAmD,OAAA,CAAAF,cAAA,GAAA9C,MAAA;IAAA,EAA4B;IAGnC7D,EAAA,CAAAG,YAAA,EAAgB;IAErBH,EAAA,CAAAC,cAAA,cAA2C;IAGcD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAC,cAAA,iBAAyI;IAAlID,EAAA,CAAAoD,UAAA,2BAAA0D,gFAAAjD,MAAA;MAAA7D,EAAA,CAAAsD,aAAA,CAAAmD,IAAA;MAAA,MAAAM,OAAA,GAAA/G,EAAA,CAAAyD,aAAA;MAAA,OAAazD,EAAA,CAAA0D,WAAA,CAAAqD,OAAA,CAAAC,WAAA,CAAAC,QAAA,GAAApD,MAAA,CAA4B;IAAA,EAAP;IAAzC7D,EAAA,CAAAG,YAAA,EAAyI;IAGjJH,EAAA,CAAAC,cAAA,eAAmB;IAEkCD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/DH,EAAA,CAAAC,cAAA,eAA4C;IAEjCD,EAAA,CAAAoD,UAAA,2BAAA8D,gFAAArD,MAAA;MAAA7D,EAAA,CAAAsD,aAAA,CAAAmD,IAAA;MAAA,MAAAU,OAAA,GAAAnH,EAAA,CAAAyD,aAAA;MAAA,OAAazD,EAAA,CAAA0D,WAAA,CAAAyD,OAAA,CAAAH,WAAA,CAAAI,SAAA,GAAAvD,MAAA,CACvD;IAAA,EAD6E;IAD1C7D,EAAA,CAAAG,YAAA,EAME;IACFH,EAAA,CAAAgB,UAAA,KAAAqG,wDAAA,oBAA0J;IAC1JrH,EAAA,CAAAgB,UAAA,KAAAsG,wDAAA,oBAAgK;IACpKtH,EAAA,CAAAG,YAAA,EAAM;IAM1BH,EAAA,CAAAC,cAAA,WAAK;IAMuBD,EAAA,CAAAoD,UAAA,2BAAAmE,qFAAA1D,MAAA;MAAA7D,EAAA,CAAAsD,aAAA,CAAAmD,IAAA;MAAA,MAAAe,OAAA,GAAAxH,EAAA,CAAAyD,aAAA;MAAA,OAAazD,EAAA,CAAA0D,WAAA,CAAA8D,OAAA,CAAAC,oBAAA,CAAAC,MAAA,GAAA7D,MAAA,CACpD;IAAA,EADgF;IAQpD7D,EAAA,CAAAG,YAAA,EAAa;IAElBH,EAAA,CAAAC,cAAA,eAAmB;IACRD,EAAA,CAAAoD,UAAA,2BAAAuE,gFAAA9D,MAAA;MAAA7D,EAAA,CAAAsD,aAAA,CAAAmD,IAAA;MAAA,MAAAmB,OAAA,GAAA5H,EAAA,CAAAyD,aAAA;MAAA,OAAazD,EAAA,CAAA0D,WAAA,CAAAkE,OAAA,CAAAH,oBAAA,CAAAI,GAAA,GAAAhE,MAAA,CAAgC;IAAA,EAAP;IAA7C7D,EAAA,CAAAG,YAAA,EAAkJ;IAEtJH,EAAA,CAAAC,cAAA,oBAIC;IADSD,EAAA,CAAAoD,UAAA,mBAAA0E,2EAAA;MAAA9H,EAAA,CAAAsD,aAAA,CAAAmD,IAAA;MAAA,MAAAsB,OAAA,GAAA/H,EAAA,CAAAyD,aAAA;MAAA,OAASzD,EAAA,CAAA0D,WAAA,CAAAqE,OAAA,CAAAC,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC;IACzChI,EAAA,CAAAG,YAAA,EAAW;IAGhBH,EAAA,CAAAmE,SAAA,sBAYc;IAClBnE,EAAA,CAAAG,YAAA,EAAU;;;;IAvFNH,EAAA,CAAAoE,qBAAA,WAAA6D,MAAA,CAAA1H,WAAA,CAAAC,SAAA,0BAA2D;IAA8ER,EAAA,CAAAmB,UAAA,oBAAmB;IAEvJnB,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAmB,UAAA,qBAAoB;IAGbnB,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAmB,UAAA,UAAA8G,MAAA,CAAA1H,WAAA,CAAAC,SAAA,yBAAuD,YAAAyH,MAAA,CAAAtB,cAAA,sCAAA3G,EAAA,CAAAsE,eAAA,KAAAC,GAAA;IASvDvE,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAAmB,UAAA,UAAA8G,MAAA,CAAA1H,WAAA,CAAAC,SAAA,4BAA0D,YAAAyH,MAAA,CAAAtB,cAAA,sCAAA3G,EAAA,CAAAsE,eAAA,KAAAC,GAAA;IAYnDvE,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAmB,UAAA,YAAA8G,MAAA,CAAAjB,WAAA,CAAAC,QAAA,CAAkC,qCAAAjH,EAAA,CAAAsE,eAAA,KAAAC,GAAA;IAQ9BvE,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAmB,UAAA,YAAA8G,MAAA,CAAAjB,WAAA,CAAAI,SAAA,CAAmC,mBAAApH,EAAA,CAAAsE,eAAA,KAAAC,GAAA,WAAA0D,MAAA,CAAA7B,eAAA;IAMKpG,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAmB,UAAA,SAAA8G,MAAA,CAAA7B,eAAA,UAA8B;IAC9BpG,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAmB,UAAA,SAAA8G,MAAA,CAAA7B,eAAA,SAA6B;IAQvFpG,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAmB,UAAA,qBAAoB;IAILnB,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAmB,UAAA,mBAAkB,YAAA8G,MAAA,CAAAR,oBAAA,CAAAC,MAAA,oBAAA1H,EAAA,CAAAsE,eAAA,KAAAC,GAAA,cAAA0D,MAAA,CAAAC,UAAA,wBAAAD,MAAA,CAAA1H,WAAA,CAAAC,SAAA,uCAAAyH,MAAA,CAAA1H,WAAA,CAAAC,SAAA;IAYvBR,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAmB,UAAA,YAAA8G,MAAA,CAAAR,oBAAA,CAAAI,GAAA,CAAsC,mBAAA7H,EAAA,CAAAsE,eAAA,KAAAC,GAAA;IAU7CvE,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAmB,UAAA,iBAAgB,eAAA8G,MAAA,CAAAE,kBAAA,CAAA1D,IAAA,cAAAwD,MAAA,CAAAE,kBAAA,CAAAzD,IAAA,aAAAuD,MAAA,CAAAG,kBAAA,aAAAH,MAAA,CAAAI,eAAA,aAAAJ,MAAA,CAAAK,mBAAA,cAAAL,MAAA,CAAAM,cAAA,CAAAxD,IAAA,CAAAkD,MAAA,yBAAAjI,EAAA,CAAAsE,eAAA,KAAAU,GAAA,oCAAAiD,MAAA,CAAAE,kBAAA,CAAAlD,MAAA,YAAAgD,MAAA,CAAAR,oBAAA;;;;;;IAkBxCzH,EAAA,CAAAC,cAAA,mBAAgM;IAArBD,EAAA,CAAAoD,UAAA,mBAAAoF,yEAAA;MAAAxI,EAAA,CAAAsD,aAAA,CAAAmF,IAAA;MAAA,MAAAC,OAAA,GAAA1I,EAAA,CAAAyD,aAAA;MAAA,OAASzD,EAAA,CAAA0D,WAAA,CAAAgF,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAAC3I,EAAA,CAAAG,YAAA,EAAW;;;;IAArIH,EAAA,CAAAmB,UAAA,UAAAyH,MAAA,CAAArI,WAAA,CAAAC,SAAA,4BAA0D;;;;;;ADjPxI,OAAM,MAAOqI,yBAA0B,SAAQ9I,aAAa;EACxD+I,YACmBC,cAA8B,EAC7BC,eAAgC,EAChCC,eAAgC,EACxCC,QAAkB;IAE1B,KAAK,CAACA,QAAQ,CAAC;IALA,KAAAH,cAAc,GAAdA,cAAc;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IAwBnC,KAAAE,WAAW,GAAkB,IAAI;IAqCjC,KAAA/C,eAAe,GAAG,IAAI;IACtB,KAAA8B,UAAU,GAAG,EAAE;IACf;IACA,KAAAkB,kBAAkB,GAAe,EAAE;IAcnC,KAAA3B,oBAAoB,GAAG;MAACI,GAAG,EAAG,IAAI;MAAEH,MAAM,EAAG;IAAI,CAAC;IAElD,KAAAV,WAAW,GAAG;MAACC,QAAQ,EAAE,EAAE;MAAEG,SAAS,EAAE;IAAE,CAAC;IA4ZxB,KAAAtH,SAAS,GAAGA,SAAS;EAxexC;EAiFAuJ,QAAQA,CAAA;IACJ,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACD,QAAQ;IAC5C,IAAI,CAACE,SAAS,GAAG,IAAI,CAACF,QAAQ,CAACG,EAAE;IACjC,IAAI,CAAChH,QAAQ,GAAG,IAAI,CAAC8G,cAAc,CAACD,QAAQ,CAAC/G,IAAI;IACjD,IAAI,CAACG,cAAc,GAAG5C,SAAS,CAAC4J,SAAS;IACzC,IAAI,CAACC,KAAK,GAAG,CACT;MAAEC,KAAK,EAAE,IAAI,CAACrJ,WAAW,CAACC,SAAS,CAAC,qBAAqB;IAAC,CAAC,EAC3D;MAAEoJ,KAAK,EAAE,IAAI,CAACrJ,WAAW,CAACC,SAAS,CAAC,2BAA2B;IAAC,CAAE,CACrE;IACD,IAAI,CAACqJ,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;IAEnD,IAAIC,eAAe,GAAG,CAClB;MAACC,IAAI,EAAE,IAAI,CAAC1J,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAAC0J,KAAK,EAACpK,SAAS,CAAC4J,SAAS,CAAC/G,KAAK;MAAEwH,OAAO,EAAC,CAACrK,SAAS,CAAC4J,SAAS,CAAC/G,KAAK;IAAC,CAAC,EACjI;MAACsH,IAAI,EAAE,IAAI,CAAC1J,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAAC0J,KAAK,EAACpK,SAAS,CAAC4J,SAAS,CAAC5G,QAAQ;MAACqH,OAAO,EAAC,CAACrK,SAAS,CAAC4J,SAAS,CAAC/G,KAAK,EAAC7C,SAAS,CAAC4J,SAAS,CAAC3G,QAAQ,EAACjD,SAAS,CAAC4J,SAAS,CAACzG,QAAQ,EAAEnD,SAAS,CAAC4J,SAAS,CAAC9G,MAAM,EAAE9C,SAAS,CAAC4J,SAAS,CAAC5G,QAAQ;IAAC,CAAC,EAC1P;MAACmH,IAAI,EAAE,IAAI,CAAC1J,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAAC0J,KAAK,EAACpK,SAAS,CAAC4J,SAAS,CAAC3G,QAAQ;MAACoH,OAAO,EAAC,CAACrK,SAAS,CAAC4J,SAAS,CAAC/G,KAAK;IAAC,CAAC,EACtI;MAACsH,IAAI,EAAE,IAAI,CAAC1J,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAAC0J,KAAK,EAACpK,SAAS,CAAC4J,SAAS,CAACzG,QAAQ;MAACkH,OAAO,EAAC,CAACrK,SAAS,CAAC4J,SAAS,CAAC/G,KAAK,EAAC7C,SAAS,CAAC4J,SAAS,CAAC3G,QAAQ;IAAC,CAAC,EACnK;MAACkH,IAAI,EAAE,IAAI,CAAC1J,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;MAAC0J,KAAK,EAACpK,SAAS,CAAC4J,SAAS,CAAC9G,MAAM;MAACuH,OAAO,EAAC,CAACrK,SAAS,CAAC4J,SAAS,CAAC/G,KAAK,EAAC7C,SAAS,CAAC4J,SAAS,CAAC3G,QAAQ,EAACjD,SAAS,CAAC4J,SAAS,CAACzG,QAAQ;IAAC,CAAC,CAC/L;IACD,IAAI,CAACT,WAAW,GAAG;MACf4H,WAAW,EAAE,IAAI;MACjBlI,QAAQ,EAAE,IAAI;MACdE,KAAK,EAAE,IAAI;MACXD,KAAK,EAAE,IAAI;MACXM,QAAQ,EAAE,IAAI;MACd4H,QAAQ,EAAE,IAAI;MACdlH,KAAK,EAAE,IAAI;MACXd,WAAW,EAAE,IAAI;MACjBW,OAAO,EAAE,IAAI;MACbsH,SAAS,EAAE;KACd;IACD,IAAI,CAACnC,kBAAkB,GAAG;MACtB1D,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRO,MAAM,EAAE;KACX;IACD,IAAI,CAACmD,kBAAkB,GAAG,CACtB;MACI6B,IAAI,EAAE,KAAK;MACXM,GAAG,EAAE,MAAM;MACX7F,IAAI,EAAE,KAAK;MACX8F,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIT,IAAI,EAAE,QAAQ;MACdM,GAAG,EAAE,QAAQ;MACb7F,IAAI,EAAE,KAAK;MACX8F,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IAED,IAAI,CAACrC,eAAe,GAAG;MACnBsC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IAED,IAAI,CAACtC,mBAAmB,GAAG;MACvBuC,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;IACD,IAAI,CAACtK,eAAe,GAAG,EAAE;IACzB,IAAI,CAACuK,eAAe,EAAE;IACtB,IAAI,CAACC,SAAS,EAAE;IAEhB,IAAI,CAACnH,wBAAwB,GAAG;MAC5BC,OAAO,EAAE,IAAI;MACbmH,aAAa,EAAEC,MAAM,CAAC,IAAI,CAAC5B,SAAS;KACvC;IACD,IAAI,CAAC7E,kBAAkB,GAAG,CACtB;MACIsF,IAAI,EAAE,IAAI,CAAC1J,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/D+J,GAAG,EAAE,MAAM;MACX7F,IAAI,EAAE,KAAK;MACX8F,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIT,IAAI,EAAE,IAAI,CAAC1J,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/D+J,GAAG,EAAE,MAAM;MACX7F,IAAI,EAAE,KAAK;MACX8F,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IACD,IAAI,CAAC9F,eAAe,GAAG;MACnB+F,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD,IAAI,CAACpG,kBAAkB,GAAG;MACtBC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRO,MAAM,EAAE;KACX;IACD,IAAI,CAACJ,mBAAmB,GAAG;MACvBgG,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;IACD,IAAI,CAACxF,wBAAwB,GAAG;MAC5BxB,OAAO,EAAE,IAAI;MACbmH,aAAa,EAAEC,MAAM,CAAC,IAAI,CAAC5B,SAAS,CAAC;MACrC6B,WAAW,EAAE;KAChB;IACD,IAAI,CAACxF,kBAAkB,GAAG,CACtB;MACIoE,IAAI,EAAE,IAAI,CAAC1J,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/D+J,GAAG,EAAE,cAAc;MACnB7F,IAAI,EAAE,KAAK;MACX8F,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIT,IAAI,EAAE,IAAI,CAAC1J,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/D+J,GAAG,EAAE,cAAc;MACnB7F,IAAI,EAAE,KAAK;MACX8F,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIT,IAAI,EAAE,IAAI,CAAC1J,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/D+J,GAAG,EAAE,cAAc;MACnB7F,IAAI,EAAE,KAAK;MACX8F,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IACD,IAAI,CAAC5E,eAAe,GAAG;MACnB6E,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD,IAAI,CAAChF,kBAAkB,GAAG;MACtBnB,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRO,MAAM,EAAE;KACX;IACD,IAAI,CAACc,mBAAmB,GAAG;MACvB8E,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;EACL;EAEAM,qBAAqBA,CAAA,GAErB;EAEAJ,SAASA,CAAA;IACL,IAAIK,EAAE,GAAG,IAAI;IACb,IAAIC,SAAS,GAAG,IAAI,CAAClC,QAAQ,CAACG,EAAE;IAChC;IACA8B,EAAE,CAACE,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI,CAAC3C,cAAc,CAAC4C,WAAW,CAAEC,QAAQ,IAAG;MACxCL,EAAE,CAAC7K,eAAe,GAAGkL,QAAQ;MAC7BL,EAAE,CAAC/I,WAAW,CAAC4H,WAAW,GAAGwB,QAAQ,CAAC7K,QAAQ;MAC9CwK,EAAE,CAAC/I,WAAW,CAACN,QAAQ,GAAG0J,QAAQ,CAAC1J,QAAQ;MAC3CqJ,EAAE,CAAC/I,WAAW,CAACJ,KAAK,GAAGwJ,QAAQ,CAACxJ,KAAK;MACrCmJ,EAAE,CAAC/I,WAAW,CAACH,WAAW,GAAGuJ,QAAQ,CAACvJ,WAAW;MACjDkJ,EAAE,CAAC/I,WAAW,CAACL,KAAK,GAAGyJ,QAAQ,CAACzJ,KAAK;MACrCoJ,EAAE,CAAC/I,WAAW,CAAC6H,QAAQ,GAAGuB,QAAQ,CAAChL,YAAY;MAC/C2K,EAAE,CAAC/I,WAAW,CAACC,QAAQ,GAAGmJ,QAAQ,CAACrJ,IAAI;MACvCgJ,EAAE,CAACM,WAAW,CAAC,KAAK,CAAC;MACrB,IAAIN,EAAE,CAAC/I,WAAW,CAACC,QAAQ,IAAI3C,SAAS,CAAC4J,SAAS,CAAC5G,QAAQ,EAAE;QACzDyI,EAAE,CAACO,kCAAkC,EAAE;QACvCP,EAAE,CAACxH,wBAAwB,CAACoH,aAAa,GAAGC,MAAM,CAACG,EAAE,CAAC/B,SAAS,CAAC;QAChE+B,EAAE,CAAC/F,wBAAwB,CAAC2F,aAAa,GAAGC,MAAM,CAACG,EAAE,CAAC/B,SAAS,CAAC;QAChE+B,EAAE,CAAC/F,wBAAwB,CAAC6F,WAAW,GAAG,CAACE,EAAE,CAAC7K,eAAe,CAAC4J,SAAS,IAAG,EAAE,EAAEyB,GAAG,CAACC,QAAQ,IAAIA,QAAQ,CAACC,UAAU,CAAC;;MAEtHV,EAAE,CAAC5E,cAAc,GAAGiF,QAAQ,CAACM,SAAS;MACtCX,EAAE,CAACnC,kBAAkB,GAAGwC,QAAQ,CAACO,SAAS,GAAEP,QAAQ,CAACO,SAAS,CAACJ,GAAG,CAACK,EAAE,KAAI;QAAC3C,EAAE,EAAE2C;MAAE,CAAC,CAAC,CAAC,GAAG,CAAC;QAAC3C,EAAE,EAAC,CAAC;MAAE,CAAC,CAAC;MAChG8B,EAAE,CAACvE,WAAW,CAACI,SAAS,GAAGwE,QAAQ,CAACS,QAAQ;MAC5Cd,EAAE,CAACvE,WAAW,CAACC,QAAQ,GAAG2E,QAAQ,CAAC7K,QAAQ;IAC/C,CAAC,EAAE,IAAI,EAAE,MAAI;MACTwK,EAAE,CAACE,oBAAoB,CAACa,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAT,WAAWA,CAACU,OAAO;IACf,IAAI,CAACxD,cAAc,CAAC8C,WAAW,CAAC,IAAI,CAACrJ,WAAW,CAACC,QAAQ,EAAGmJ,QAAQ,IAAG;MACnE,IAAI,CAACY,QAAQ,GAAGZ,QAAQ,CAACG,GAAG,CAACK,EAAE,IAAG;QAC9B,OAAO;UACH3C,EAAE,EAAE2C,EAAE,CAAC3C,EAAE;UACTQ,IAAI,EAAEmC,EAAE,CAACnC;SACZ;MACL,CAAC,CAAC;MACF,IAAGsC,OAAO,EAAC;QACP,IAAI,CAAC/J,WAAW,CAACW,KAAK,GAAG,IAAI;OAChC,MAAI;QACD,IAAI,CAACX,WAAW,CAACW,KAAK,GAAG,IAAI,CAACqJ,QAAQ,CAACC,MAAM,CAACL,EAAE,IAAI,CAAC,IAAI,CAAC1L,eAAe,CAACyC,KAAK,IAAE,EAAE,EAAEuJ,QAAQ,CAACN,EAAE,CAAC3C,EAAE,CAAC,CAAC;;IAE7G,CAAC,CAAC;EACN;EAGAwB,eAAeA,CAAA;IACX,IAAI,CAAClC,cAAc,CAACkC,eAAe,CAAEW,QAAQ,IAAG;MAC5C,IAAI,CAACe,YAAY,GAAGf,QAAQ,CAACG,GAAG,CAACK,EAAE,IAAG;QAClC,OAAO;UACH3C,EAAE,EAAE2C,EAAE,CAACQ,IAAI;UACX3C,IAAI,EAAE,GAAGmC,EAAE,CAACnC,IAAI,KAAKmC,EAAE,CAACQ,IAAI;SAC/B;MACL,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEAjE,QAAQA,CAAA;IACJ,IAAI,CAACkE,MAAM,CAACC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC5C;EAEAC,kBAAkBA,CAAA;IACd,OAAO,CAAC,IAAI,CAACrM,eAAe,CAAC4J,SAAS,IAAI,EAAE,EAAEyB,GAAG,CAACK,EAAE,IAAIA,EAAE,CAACY,YAAY,GAAG,KAAK,GAAGZ,EAAE,CAACa,YAAY,CAAC,CAACC,cAAc,EAAE;EACvH;EAEAC,cAAcA,CAAA;IACV,OAAO,CAAC,IAAI,CAACzM,eAAe,CAACyC,KAAK,IAAI,EAAE,EAAE4I,GAAG,CAACK,EAAE,IAAIA,EAAE,CAAC1K,QAAQ,CAAC,CAACwL,cAAc,EAAE;EACrF;EAEA5K,iBAAiBA,CAAC4H,KAAK;IACnB,IAAGA,KAAK,IAAIpK,SAAS,CAAC4J,SAAS,CAAC/G,KAAK,EAAC;MAClC,OAAO,IAAI,CAACpC,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;KAC9D,MAAK,IAAG0J,KAAK,IAAIpK,SAAS,CAAC4J,SAAS,CAAC5G,QAAQ,EAAC;MAC3C,OAAO,IAAI,CAACvC,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;KACjE,MAAK,IAAG0J,KAAK,IAAIpK,SAAS,CAAC4J,SAAS,CAAC3G,QAAQ,EAAC;MAC3C,OAAO,IAAI,CAACxC,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;KACjE,MAAK,IAAG0J,KAAK,IAAIpK,SAAS,CAAC4J,SAAS,CAACzG,QAAQ,EAAC;MAC3C,OAAO,IAAI,CAAC1C,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;KACjE,MAAK,IAAG0J,KAAK,IAAIpK,SAAS,CAAC4J,SAAS,CAAC9G,MAAM,EAAC;MACzC,OAAO,IAAI,CAACrC,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;KAC/D,MAAI;MACD,OAAO,EAAE;;EAEjB;EACA4M,cAAcA,CAAA;IACV,IAAI,CAACP,MAAM,CAACC,QAAQ,CAAC,CAAC,0BAA0B,CAAC,CAAC;EACtD;EAEAO,WAAWA,CAACC,KAAK;IACb,MAAMC,OAAO,GAAGD,KAAK,CAACE,aAAa,CAACC,MAAM,CAACC,SAAS;IACpD,IAAInC,EAAE,GAAG,IAAI;IACb,IAAI+B,KAAK,IAAIC,OAAO,CAACb,QAAQ,CAAC,IAAI,CAACnM,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC,CAAC,EAAE;MAChF+K,EAAE,CAACvD,gBAAgB,EAAE;KACxB,MAAM,IAAIsF,KAAK,IAAIC,OAAO,CAACb,QAAQ,CAAC,IAAI,CAACnM,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC,CAAC,EAAE;MACtF+K,EAAE,CAAClG,gBAAgB,EAAE;KACxB,MAAM,IAAIiI,KAAK,IAAIC,OAAO,CAACb,QAAQ,CAAC,IAAI,CAACnM,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC,CAAC,EAAE;MAC1F+K,EAAE,CAAC5H,gBAAgB,EAAE;;EAE7B;EACAA,gBAAgBA,CAACgK,IAAK;IAClB,IAAIpC,EAAE,GAAG,IAAI;IACb,IAAIoC,IAAI,EAAE;MACNpC,EAAE,CAAC/G,kBAAkB,CAACC,IAAI,GAAG,CAAC;;IAElC8G,EAAE,CAACzG,cAAc,CAACyG,EAAE,CAAC/G,kBAAkB,CAACC,IAAI,EAAE8G,EAAE,CAAC/G,kBAAkB,CAACE,IAAI,EAAE6G,EAAE,CAAC/G,kBAAkB,CAACS,MAAM,EAAEsG,EAAE,CAACxH,wBAAwB,CAAC;EACxI;EACAsB,gBAAgBA,CAACsI,IAAK;IAClB,IAAIpC,EAAE,GAAG,IAAI;IACb,IAAIoC,IAAI,EAAE;MACNpC,EAAE,CAAC3F,kBAAkB,CAACnB,IAAI,GAAG,CAAC;;IAElC8G,EAAE,CAACvF,cAAc,CAACuF,EAAE,CAAC3F,kBAAkB,CAACnB,IAAI,EAAE8G,EAAE,CAAC3F,kBAAkB,CAAClB,IAAI,EAAE6G,EAAE,CAAC3F,kBAAkB,CAACX,MAAM,EAAEsG,EAAE,CAAC/F,wBAAwB,CAAC;EACxI;EACAV,cAAcA,CAACL,IAAI,EAAEmJ,KAAK,EAAEC,IAAI,EAAEC,MAAM;IACpC,IAAIvC,EAAE,GAAG,IAAI;IACb,IAAI,CAAC/G,kBAAkB,CAACC,IAAI,GAAGA,IAAI;IACnC,IAAI,CAACD,kBAAkB,CAACE,IAAI,GAAGkJ,KAAK;IACpC,IAAI,CAACpJ,kBAAkB,CAACS,MAAM,GAAG4I,IAAI;IACrC,IAAIE,UAAU,GAAG;MACbtJ,IAAI;MACJC,IAAI,EAAEkJ,KAAK;MACXC;KACH;IACDG,MAAM,CAACC,IAAI,CAAC,IAAI,CAAClK,wBAAwB,CAAC,CAACmK,OAAO,CAAC3D,GAAG,IAAG;MACrD,IAAG,IAAI,CAACxG,wBAAwB,CAACwG,GAAG,CAAC,IAAI,IAAI,EAAC;QAC1CwD,UAAU,CAACxD,GAAG,CAAC,GAAG,IAAI,CAACxG,wBAAwB,CAACwG,GAAG,CAAC;;IAE5D,CAAC,CAAC;IACFgB,EAAE,CAACE,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI,CAAC1C,eAAe,CAACmF,mBAAmB,CAACJ,UAAU,EAAE,IAAI,CAAChK,wBAAwB,EAAE6H,QAAQ,IAAG;MAC3FL,EAAE,CAAC3G,eAAe,GAAG;QACjB+F,OAAO,EAAEiB,QAAQ,CAACjB,OAAO;QACzBC,KAAK,EAAEgB,QAAQ,CAACwC;OACnB;IACL,CAAC,EAAE,IAAI,EAAE,MAAI;MACT7C,EAAE,CAACE,oBAAoB,CAACa,OAAO,EAAE;IACrC,CAAC,CAAC;IACF;EACJ;;EACAtG,cAAcA,CAACvB,IAAI,EAAEmJ,KAAK,EAAEC,IAAI,EAAEC,MAAM;IACpC,IAAIvC,EAAE,GAAG,IAAI;IACb,IAAI,CAAC3F,kBAAkB,CAACnB,IAAI,GAAGA,IAAI;IACnC,IAAI,CAACmB,kBAAkB,CAAClB,IAAI,GAAGkJ,KAAK;IACpC,IAAI,CAAChI,kBAAkB,CAACX,MAAM,GAAG4I,IAAI;IACrC,IAAIE,UAAU,GAAG;MACbtJ,IAAI;MACJC,IAAI,EAAEkJ,KAAK;MACXC;KACH;IACD;IACA;IACA;IACA;IACA;IACAtC,EAAE,CAACE,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI,CAACzC,eAAe,CAACoF,mBAAmB,CAACN,UAAU,EAAE,IAAI,CAACvI,wBAAwB,EAAEoG,QAAQ,IAAG;MAC3FL,EAAE,CAACzF,eAAe,GAAG;QACjB6E,OAAO,EAAEiB,QAAQ,CAACjB,OAAO;QACzBC,KAAK,EAAEgB,QAAQ,CAACwC;OACnB;MACD;IACJ,CAAC,EAAE,IAAI,EAAE,MAAI;MACT7C,EAAE,CAACE,oBAAoB,CAACa,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EACAtK,mBAAmBA,CAACkI,KAAK;IACrB,IAAGA,KAAK,IAAIpK,SAAS,CAACwO,WAAW,CAACC,MAAM,EAAC;MACrC,OAAO,IAAI,CAAChO,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;KACjE,MAAK,IAAG0J,KAAK,IAAIpK,SAAS,CAACwO,WAAW,CAACE,QAAQ,EAAC;MAC7C,OAAO,IAAI,CAACjO,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;KACnE,MAAI;MACD,OAAO,EAAE;;EAEjB;EACAsL,kCAAkCA,CAAA;IAC9B,IAAI,CAACtH,kBAAkB,GAAG;MACtBC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRO,MAAM,EAAE;KACX;IACD,IAAI,CAACW,kBAAkB,GAAG;MACtBnB,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRO,MAAM,EAAE;KACX;EACL;EAEAsD,cAAcA,CAAC9D,IAAI,EAAEmJ,KAAK,EAAEC,IAAI,EAAEC,MAAM;IACpC,IAAIvC,EAAE,GAAG,IAAI;IACb,IAAI,CAACpD,kBAAkB,CAAC1D,IAAI,GAAGA,IAAI;IACnC,IAAI,CAAC0D,kBAAkB,CAACzD,IAAI,GAAGkJ,KAAK;IACpC,IAAI,CAACzF,kBAAkB,CAAClD,MAAM,GAAG4I,IAAI;IACrC,IAAIE,UAAU,GAAG;MACbtJ,IAAI;MACJC,IAAI,EAAEkJ,KAAK;MACXC,IAAI;MACJY,cAAc,EAAE,IAAI,CAACrF,kBAAkB,CAAC2C,GAAG,CAACK,EAAE,IAAEA,EAAE,CAAC3C,EAAE,CAAC,CAACiF,IAAI,CAAC,GAAG;KAClE;IACDV,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxG,oBAAoB,CAAC,CAACyG,OAAO,CAAC3D,GAAG,IAAG;MACjD,IAAG,IAAI,CAAC9C,oBAAoB,CAAC8C,GAAG,CAAC,IAAI,IAAI,EAAC;QACtCwD,UAAU,CAACxD,GAAG,CAAC,GAAG,IAAI,CAAC9C,oBAAoB,CAAC8C,GAAG,CAAC;;IAExD,CAAC,CAAC;IACFoE,OAAO,CAACC,GAAG,CAACb,UAAU,CAAC;IACvBxC,EAAE,CAACE,oBAAoB,CAACC,MAAM,EAAE;IAChC,IAAI,CAAC3C,cAAc,CAACR,cAAc,CAACwF,UAAU,EAAEnC,QAAQ,IAAG;MACtDL,EAAE,CAAClD,eAAe,GAAG;QACjBsC,OAAO,EAAEiB,QAAQ,CAACjB,OAAO;QACzBC,KAAK,EAAEgB,QAAQ,CAACwC;OACnB;IACL,CAAC,EAAE,IAAI,EAAE,MAAI;MACT7C,EAAE,CAACE,oBAAoB,CAACa,OAAO,EAAE;IACrC,CAAC,CAAC;IACF,IAAIuC,SAAS,GAAG;MAAC,GAAGd;IAAU,CAAC;IAC/Bc,SAAS,CAACnK,IAAI,GAAG,SAAS;IAC1B,IAAI,CAACqE,cAAc,CAACR,cAAc,CAACsG,SAAS,EAAEjD,QAAQ,IAAG;MACrDL,EAAE,CAACrD,UAAU,GAAG,CAAC,GAAG,IAAI4G,GAAG,CAAClD,QAAQ,CAACjB,OAAO,CAACoB,GAAG,CAACK,EAAE,IAAEA,EAAE,CAAC1E,MAAM,CAAC,CAAC,CAAC;MACjE6D,EAAE,CAACrD,UAAU,GAAGqD,EAAE,CAACrD,UAAU,CAAC6D,GAAG,CAACK,EAAE,KAAG;QACnCnC,IAAI,EAAGmC,EAAE;QACTlC,KAAK,EAAGkC;OACX,CAAC,CAAC;IACP,CAAC,EAAE,IAAI,EAAE,MAAI;MACTb,EAAE,CAACE,oBAAoB,CAACa,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAyC,aAAaA,CAACC,CAAC;IACX,IAAIC,KAAK,GAAG,gEAAgE;IAC5E,IAAIC,KAAK,GAAG,EAAE;IACd,KAAI,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MACvBD,KAAK,IAAID,KAAK,CAACG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGL,KAAK,CAACM,MAAM,CAAC,CAAC;;IAE5D,OAAOL,KAAK;EAChB;EAEAM,QAAQA,CAAA;IACJ,IAAI,CAACxI,WAAW,CAACI,SAAS,GAAG,IAAI,CAAC2H,aAAa,CAAC,EAAE,CAAC;EACvD;EAEA/G,gBAAgBA,CAAC2F,IAAK;IAClB,IAAIpC,EAAE,GAAG,IAAI;IACboD,OAAO,CAACC,GAAG,CAACrD,EAAE,CAAC9D,oBAAoB,CAAC;IACpC,IAAGkG,IAAI,EAAE;MACLpC,EAAE,CAACpD,kBAAkB,CAAC1D,IAAI,GAAG,CAAC;;IAElC8G,EAAE,CAAChD,cAAc,CAACgD,EAAE,CAACpD,kBAAkB,CAAC1D,IAAI,EAAE8G,EAAE,CAACpD,kBAAkB,CAACzD,IAAI,EAAE6G,EAAE,CAACpD,kBAAkB,CAAClD,MAAM,EAAEsG,EAAE,CAAC9D,oBAAoB,CAAC;EACpI;;;uBA9eSoB,yBAAyB,EAAA7I,EAAA,CAAAyP,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA3P,EAAA,CAAAyP,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA7P,EAAA,CAAAyP,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA/P,EAAA,CAAAyP,iBAAA,CAAAzP,EAAA,CAAAgQ,QAAA;IAAA;EAAA;;;YAAzBnH,yBAAyB;MAAAoH,SAAA;MAAAC,QAAA,GAAAlQ,EAAA,CAAAmQ,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbtCzQ,EAAA,CAAAC,cAAA,aAAqG;UACjGD,EAAA,CAAAmE,SAAA,sBAAqF;UACzFnE,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,gBAA0C;UAEvBD,EAAA,CAAAoD,UAAA,sBAAAuN,iEAAA9M,MAAA;YAAA,OAAY6M,GAAA,CAAArD,WAAA,CAAAxJ,MAAA,CAAmB;UAAA,EAAC;UACvC7D,EAAA,CAAAC,cAAA,oBAA4E;UACxED,EAAA,CAAAgB,UAAA,IAAA4P,wCAAA,mBAyGM;UACV5Q,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAgB,UAAA,IAAA6P,+CAAA,yBAsBa;UACb7Q,EAAA,CAAAgB,UAAA,IAAA8P,+CAAA,yBAsBa;UACb9Q,EAAA,CAAAgB,UAAA,IAAA+P,+CAAA,0BAyFa;UACjB/Q,EAAA,CAAAG,YAAA,EAAY;UAEhBH,EAAA,CAAAC,cAAA,cAAyC;UACrCD,EAAA,CAAAgB,UAAA,KAAAgQ,8CAAA,uBAA2M;UAC3MhR,EAAA,CAAAC,cAAA,oBAAiK;UAA3BD,EAAA,CAAAoD,UAAA,mBAAA6N,8DAAA;YAAA,OAASP,GAAA,CAAAtD,cAAA,EAAgB;UAAA,EAAC;UAACpN,EAAA,CAAAG,YAAA,EAAW;;;UA9PxIH,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAmB,UAAA,UAAAuP,GAAA,CAAA/G,KAAA,CAAe,SAAA+G,GAAA,CAAA7G,IAAA;UAMnC7J,EAAA,CAAAI,SAAA,GAA+D;UAA/DJ,EAAA,CAAAoE,qBAAA,WAAAsM,GAAA,CAAAnQ,WAAA,CAAAC,SAAA,8BAA+D;UACJR,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAmB,UAAA,SAAAuP,GAAA,CAAAhQ,eAAA,CAAqB;UA2GhBV,EAAA,CAAAI,SAAA,GAA2D;UAA3DJ,EAAA,CAAAmB,UAAA,SAAAuP,GAAA,CAAAlO,WAAA,CAAAC,QAAA,IAAAiO,GAAA,CAAA5Q,SAAA,CAAA4J,SAAA,CAAA5G,QAAA,CAA2D;UAuB/D9C,EAAA,CAAAI,SAAA,GAA0D;UAA1DJ,EAAA,CAAAmB,UAAA,SAAAuP,GAAA,CAAAlO,WAAA,CAAAC,QAAA,IAAAiO,GAAA,CAAA5Q,SAAA,CAAA4J,SAAA,CAAA5G,QAAA,CAA0D;UAuBzD9C,EAAA,CAAAI,SAAA,GAA0E;UAA1EJ,EAAA,CAAAmB,UAAA,SAAAuP,GAAA,CAAAjO,QAAA,IAAAiO,GAAA,CAAAhO,cAAA,CAAAI,QAAA,IAAA4N,GAAA,CAAA1J,WAAA,CAAAI,SAAA,SAA0E;UA6F5IpH,EAAA,CAAAI,SAAA,GAAyD;UAAzDJ,EAAA,CAAAmB,UAAA,SAAAuP,GAAA,CAAAQ,WAAA,CAAAlR,EAAA,CAAAmR,eAAA,IAAAC,GAAA,EAAAV,GAAA,CAAA5Q,SAAA,CAAAuR,WAAA,CAAAC,OAAA,CAAAC,MAAA,GAAyD;UAC1DvR,EAAA,CAAAI,SAAA,GAA2D;UAA3DJ,EAAA,CAAAmB,UAAA,UAAAuP,GAAA,CAAAnQ,WAAA,CAAAC,SAAA,6BAA2D"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}