server:
  port: 8080
  servlet:
    context-path: /api
spring:
  rabbitmq:
    addresses: ************:5672, ************:5672, ************:5672
    port: 5672
    username: admin
    password: ssdc_cmp
#  datasource:
#    url: ***************************************
#    username: m2m
#    password: m2m
#    driver-class-name: oracle.jdbc.driver.OracleDriver
#    hikari:
#      data-source-properties:
#        stringtype: unspecified
#  jpa:
#    database-platform: org.hibernate.dialect.Oracle12cDialect
#    use-new-id-generator-mappings: false
#    show-sql: true
#    hibernate:
#      # Drop n create table, good for testing, comment this in production
#      ddl-auto: none
  redis:
    #    password: ssdc_cmp
    #    sentinel:
    #      master: mymaster
    #      nodes: ************:26379, ************:26379, ************:26379
    #    Standalone, Sentinel, Cluster
    mode: Sentinel
    standalone:
      host: ************
      port: 6379
      username: aaaa
      password: oneiot@2020
    cluster:
      nodes: *********:6380,*********:6381,*********:6385
      username: default
      password:
    sentinel:
      password: ssdc_cmp
      master: mymaster
      nodes: ************:26379, ************:26379, ************:26379
    jedis:
      pool:
        max-active: 1000
        min-idle: 0
        max-wait: -1
        max-idle: 10
management:
  endpoints:
    web:
      exposure:
        include: health