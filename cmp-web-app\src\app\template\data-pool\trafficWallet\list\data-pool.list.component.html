<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.walletList")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>

    <div *ngIf="checkAuthen([allPermissions.DATAPOOL.CREATE_WALLET])" class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button
            styleClass="p-button-info"
            icon="pi pi-plus"
            [label]="tranService.translate('datapool.button.createWallet')"
            (click)="showModalAccuracy()">
        </p-button>
    </div>
</div>


<search-filter-separate
 [searchList]="searchList"
 [filterList]="filterList"
 (searchDetail)="catchSearchDetail($event)"
></search-filter-separate>


<table-vnpt
    [tableId]="'tableTrafficWallet'"
    [fieldId]="'id'"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [pageNumber]="pageNumber"
    [loadData]="search.bind(this)"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="tranService.translate('global.menu.trafficManagement')"
></table-vnpt>
<!-- dialog tạo + xác thực ví-->
<div class="flex justify-content-center responsive-dialog">
    <p-dialog [header]="tranService.translate('datapool.label.accuracyWallet')" [(visible)]="isShowModalWalletAuthen" [modal]="true" [style]="{ width: '700px' }" [draggable]="false" [resizable]="false">
        <form class="mt-3" [formGroup]="formAccuracyWallet" (ngSubmit)="accuracyRequest()">
            <div class="flex flex-row flex-wrap justify-content-between w-full">
                <div>{{tranService.translate("datapool.label.authenMethod")}}</div>
                <!-- phuong thuc thanh toan -->
                <div class="w-full field grid flex align-items-center justify-content-start">
                    <p-radioButton
                        [label]="tranService.translate('datapool.label.subCode')"
                        name="accuracyType" [value]="0"
                        [(ngModel)]="accuracyWallet.accuracyType"
                        formControlName="accuracyType"
                        class="p-3"
                        inputId="1">
                    </p-radioButton>
                    <p-radioButton
                        [label]="tranService.translate('datapool.label.payCode')"
                        name="accuracyType"
                        [value]="1"
                        inputId="2"
                        [(ngModel)]="accuracyWallet.accuracyType"
                        formControlName="accuracyType">
                    </p-radioButton>
                </div>
                <!-- wallet Code -->
                <div *ngIf="accuracyWallet.accuracyType == 0" class="w-full field grid mb-0">
                    <label htmlFor="tax" class="col-fixed" style="width:180px">{{tranService.translate("datapool.label.subCode")}}<span class="text-red-500">*</span></label>
                    <div class="col">
                        <input class="w-full"
                               pInputText id="subCode"
                               formControlName="subCode"
                               [placeholder]="tranService.translate('datapool.text.subCode')"
                        />
                    </div>
                </div>
                <div *ngIf="accuracyWallet.accuracyType == 0" class="w-full field grid m-0 p-0 mb-3">
                    <label htmlFor="tax" class="col-fixed" style="width:180px"></label>
                    <div *ngIf="formAccuracyWallet.get('subCode').invalid && formAccuracyWallet.get('subCode').dirty">
                        <div *ngIf="formAccuracyWallet.get('subCode').errors.required" class="text-red-500" >{{tranService.translate("global.message.required")}}</div>
                        <div *ngIf="formAccuracyWallet.get('subCode').errors.pattern" class="text-red-500" >{{tranService.translate("global.message.formatContainVN")}}</div>
                        <div *ngIf="formAccuracyWallet.get('subCode').errors.maxlength" class="text-red-500" >{{tranService.translate("global.message.maxLength",{len:64})}}</div>
                    </div>
                </div>
                <!-- payment Code -->
                <div *ngIf="accuracyWallet.accuracyType == 1" class="w-full field grid mb-0">
                    <label htmlFor="tax" class="col-fixed" style="width:180px">{{tranService.translate("datapool.label.payCode")}}<span class="text-red-500">*</span></label>
                    <div class="col">
                        <input class="w-full"
                               pInputText id="payCode"
                               formControlName="payCode"
                               [placeholder]="tranService.translate('datapool.text.payCode')"
                        />
                    </div>
                </div>
                <div *ngIf="accuracyWallet.accuracyType == 1" class="w-full field grid m-0 p-0 mb-3">
                    <label htmlFor="tax" class="col-fixed" style="width:180px"></label>
                    <div *ngIf="formAccuracyWallet.get('payCode').invalid && formAccuracyWallet.get('payCode').dirty">
                        <div *ngIf="formAccuracyWallet.get('payCode').errors.required" class="text-red-500" >{{tranService.translate("global.message.required")}}</div>
                        <div *ngIf="formAccuracyWallet.get('payCode').errors.pattern" class="text-red-500" >{{tranService.translate("global.message.formatContainVN")}}</div>
                        <div *ngIf="formAccuracyWallet.get('payCode').errors.maxlength" class="text-red-500" >{{tranService.translate("global.message.maxLength",{len:64})}}</div>
                    </div>
                </div>
                <!-- tax -->
                <div class="w-full field grid">
                    <label htmlFor="tax" class="col-fixed" style="width:180px">{{tranService.translate("datapool.label.tax")}}<span class="text-red-500">*</span></label>
                    <div class="col">
                        <input class="w-full"
                               pInputText id="tax"
                               [(ngModel)]="accuracyWallet.tax"
                               formControlName="tax"
                               [placeholder]="tranService.translate('datapool.text.tax')"
                        />
                    </div>
                </div>
                <!-- error tax -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="fullName" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500" *ngIf="formAccuracyWallet.controls.tax.dirty && formAccuracyWallet.controls.tax.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        <small class="text-red-500" *ngIf="formAccuracyWallet.controls.tax.errors?.maxlength">{{tranService.translate("global.message.maxLength",{len:64})}}</small>
                        <small class="text-red-500" *ngIf="formAccuracyWallet.controls.tax.errors?.pattern">{{tranService.translate("datapool.message.patternError")}}</small>
                    </div>
                </div>
                <!-- phone -->
                <div class="w-full field grid">
                    <label htmlFor="phone" class="col-fixed" style="width:180px">{{tranService.translate("datapool.label.phone")}}<span class="text-red-500">*</span></label>
                    <div class="col">
                        <input class="w-full"
                               pInputText id="phoneNumber"
                               [(ngModel)]="accuracyWallet.phoneNumber"
                               formControlName="phoneNumber"
                               [placeholder]="tranService.translate('account.text.inputPhone')"
                        />
                    </div>
                </div>
                <!-- error phone -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="phone" class="col-fixed" style="width:180px"></label>
                    <div class="flex flex-column">
                        <div class="col">
                            <small class="text-red-500" *ngIf="formAccuracyWallet.controls.phoneNumber.dirty && formAccuracyWallet.controls.phoneNumber.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        </div>
                        <div class="col">
                            <small class="text-red-500" *ngIf="formAccuracyWallet.controls.phoneNumber.errors?.pattern">{{tranService.translate("datapool.message.digitError")}}</small>
                        </div>
                    </div>
                </div>

            </div>
            <div class="flex flex-row justify-content-center align-items-center mt-3">
                <p-button styleClass="mr-2 p-button-secondary" [label]="tranService.translate('global.button.cancel')" (click)="isShowModalWalletAuthen = false"></p-button>
                <p-button type="button" styleClass="p-button-info" [disabled]="checkDisable()" [label]="tranService.translate('global.button.save')" (onClick)="sendOTP()"></p-button>
            </div>
            <p-dialog [header]="tranService.translate('datapool.label.otpCode')" [(visible)]="isSubmit" [modal]="true" [style]="{ width: '30vw' }" [draggable]="false" (onHide)="onHideOtp()" [resizable]="false">
                <div class="flex flex-column gap-2 flex-1">
                    <p-inputOtp formControlName="otp" class="mx-auto my-3" [integerOnly]="true" length="6"></p-inputOtp>
                    <button type="button" class="border-none mb-4 cursor-pointer flex flex-row justify-content-center font-semibold" style="background-color: transparent;" [disabled]="countdown>0" (click)="resetTimer()">{{tranService.translate("datapool.message.resendOtp")}}&nbsp;<div *ngIf="countdown>0"> {{tranService.translate("datapool.message.in")}} {{countdown}} {{tranService.translate("datapool.message.sec")}} </div></button>
                    <div class="m-auto">
                        <button class="m-auto mr-2" [disabled]="formAccuracyWallet.controls.otp.invalid" pButton>{{tranService.translate("global.button.save")}}</button>
                        <button type="button" class="m-auto ml-2 p-button-outlined" pButton (click)="isSubmit = false">{{tranService.translate("global.button.cancel")}}</button>
                    </div>
                </div>
            </p-dialog>
        </form>
    </p-dialog>
</div>

<!-- popup detail wallet -->
<div class="flex justify-content-center dialog-vnpt">
    <p-dialog [header]="tranService.translate('global.button.view')" [(visible)]="isShowModalDetail" [modal]="true" [style]="{ width: '980px' }" [draggable]="false" [resizable]="false" *ngIf="isShowModalDetail">
        <p-card styleClass="my-3">
            <div class="text-2xl font-bold pb-2">{{subCodeId}}</div>
            <div *ngIf="walletDetail">
                <div class="flex flex-row surface-200 p-4 border-round wallet-detail-div">
                    <div class="flex-1 wallet-detail">
                        <div class="font-medium text-base">{{tranService.translate('datapool.label.payCode')}}</div>
                        <div class="font-semibold text-lg">{{walletDetail.payCode}}</div>
                    </div>
                    <div class="flex-1">
                        <div class="font-medium text-base">{{tranService.translate('datapool.label.packageName')}}</div>
                        <div class="font-semibold text-lg">{{walletDetail.packageName}}</div>
                    </div>
                    <div class="flex-1">
                        <div class="font-medium text-base">{{tranService.translate('datapool.label.phoneFull')}}</div>
                        <div class="font-semibold text-lg">{{walletDetail.phoneActive}}</div>
                    </div>
                    <div class="flex-1">
                        <div class="font-medium text-base">{{tranService.translate("datapool.label.tax")}}</div>
                        <div class="font-semibold text-lg">{{walletDetail.tax}}</div>
                    </div>
                </div>
            </div>
            <div class="flex flex-row p-4 border-round wallet-detail-div">
                <div class="flex-1">
                    <div class="font-medium text-base">{{tranService.translate("datapool.label.trafficType")}}</div>
                    <div class="font-semibold text-lg">{{walletDetail.trafficType}}</div>
                </div>
                <div class="flex-1">
                    <div class="font-medium text-base">{{tranService.translate("datapool.label.methodAutoShare")}}</div>
                    <div class="font-semibold text-lg">{{getValueMethodAutoShare(walletDetail.autoType)}}</div>
                </div>
                <div class="flex-1">
                    <div class="font-medium text-base">{{tranService.translate('datapool.label.remainData')}}/ {{tranService.translate("datapool.label.purchasedData")}}</div>
                    <div class="font-semibold text-lg" *ngIf="walletDetail.trafficType == 'Gói Data'" >{{formatNumber(walletDetail.totalRemainingTraffic)}}/ {{formatNumber(walletDetail.purchasedTraffic)}} MB</div>
                    <div class="font-semibold text-lg" *ngIf="walletDetail.trafficType == 'Gói thoại'" >{{formatNumber(walletDetail.totalRemainingTraffic)}}/ {{formatNumber(walletDetail.purchasedTraffic)}} {{tranService.translate('alert.label.minutes')}}</div>
                    <div class="font-semibold text-lg" *ngIf="(( walletDetail.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase()))" >{{formatNumber(walletDetail.totalRemainingTraffic)}}/ {{formatNumber(walletDetail.purchasedTraffic)}} SMS</div>
                    <div class="font-semibold text-lg" *ngIf="!(( walletDetail.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) && walletDetail.trafficType != 'Gói Data'&&walletDetail.trafficType != 'Gói thoại'" >{{formatNumber(walletDetail.totalRemainingTraffic)}}/ {{formatNumber(walletDetail.purchasedTraffic)}}</div>
                </div>
                <div class="flex-1">
                    <div class="font-medium text-base">{{tranService.translate("datapool.label.usedTime")}}</div>
                    <div class="font-semibold text-lg">{{walletDetail.timeToUse}}</div>
                </div>
            </div>
        </p-card>

        <p-card>
            <div class="text-lg font-bold">{{tranService.translate("datapool.label.shareInfo")}}</div>
<!--            <p-table-->
<!--                #dt2-->
<!--                [value]="listDetail"-->
<!--                dataKey="id"-->
<!--                [rows]="10"-->
<!--                [rowsPerPageOptions]="[5, 10, 25, 50]"-->
<!--                [paginator]="true"-->
<!--                [tableStyle]="{ 'margin-top': '10px' }"-->
<!--            >-->
<!--                <ng-template pTemplate="header">-->
<!--                    <tr>-->
<!--                        <th>{{tranService.translate('datapool.label.phoneFull')}}</th>-->
<!--                        <th>{{tranService.translate('datapool.label.fullName')}}</th>-->
<!--                        <th>{{tranService.translate('datapool.label.email')}}</th>-->
<!--                        <th>{{tranService.translate('datapool.label.sharedTime')}}</th>-->
<!--                        <th>{{tranService.translate('datapool.label.usedDate')}}</th>-->
<!--                        <th>{{tranService.translate('datapool.label.shared')}}</th>-->
<!--                        <th >{{tranService.translate('datapool.label.percentage')}}</th>-->
<!--                    </tr>-->
<!--                </ng-template>-->
<!--                <ng-template pTemplate="body" let-listDetail>-->
<!--                    <tr>-->
<!--                        <td>-->
<!--                            {{ listDetail.phoneReceipt }}-->
<!--                        </td>-->
<!--                        <td>-->
<!--                            {{ listDetail.name }}-->
<!--                        </td>-->
<!--                        <td>-->
<!--                            {{ listDetail.email }}-->
<!--                        </td>-->
<!--                        <td>-->
<!--                            {{ getFormattedDate(listDetail.timeUpdate) }}-->
<!--                        </td>-->
<!--                        <td>-->
<!--                            {{ getFormattedDate(listDetail.timeUpdate, listDetail?.dayExprired) }}-->
<!--                        </td>-->
<!--                        <td *ngIf="listDetail.trafficType == 'Gói Data'">-->
<!--                            {{ listDetail.trafficShare }} MB-->
<!--                        </td>-->
<!--                        <td *ngIf="listDetail.trafficType == 'Gói thoại'">-->
<!--                            {{ listDetail.trafficShare }} {{tranService.translate('alert.label.minutes')}}-->
<!--                        </td>-->
<!--                        <td *ngIf=" ((listDetail.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase()))">-->
<!--                            {{ listDetail.trafficShare }} SMS-->
<!--                        </td>-->
<!--                        <td *ngIf="!((listDetail.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) && listDetail.trafficType != 'Gói Data' && listDetail.trafficType != 'Gói thoại'">-->
<!--                            {{ listDetail.trafficShare }}-->
<!--                        </td>-->
<!--                        <td>-->
<!--                            {{ listDetail.percentTraffic }}%-->
<!--                        </td>-->
<!--                    </tr>-->
<!--                </ng-template>-->
<!--            </p-table>-->
            <table-vnpt *ngIf="isShowTableInDialogDetail"
                [fieldId]="'id'"
                [columns]="columnsShareWallet"
                [dataSet]="dataSetShareWallet"
                [pageSize]="pageSizeShareWallet"
                [pageNumber]="pageNumberShareWallet"
                [options]="optionTableShareWallet"
                [sort]="sortShareWallet"
                [loadData]="searchShareWallet.bind(this)"
            ></table-vnpt>
        </p-card>
    </p-dialog>
<!--    dialog confirm register by SubCode or PayCode-->
    <p-dialog [header]="tranService.translate('datapool.label.notification')"
              [(visible)]="isShowRegisBySubOrPay" [modal]="true" [style]="{ width: '750px' }"
              [draggable]="false" [resizable]="false" *ngIf="isShowRegisBySubOrPay"
              (onHide)="onHideRegisBySubOrPay()">
        <div class="d-flex ai-center jc-center" style="text-align: center;">
            <p>
                <b style="font-size: large">{{ tranService.translate('datapool.message.confirmRegisterAutoShareWallet') }}</b>
                <br>
                <span style="color: red;" >
        <i>{{ tranService.translate('datapool.text.noteAutoShare') }}</i>
      </span>
                <br>
                <a (click)="openReadMoreRegister()" class="cursor-pointer"
                   style="color: blue; text-decoration: underline;"><i>{{ tranService.translate('datapool.message.readMore') }}</i></a>
            </p>
        </div>

        <div class="flex flex-row justify-content-center align-items-center mt-2">
            <div class="grid col-4">
                <p-radioButton name="registerOption" value="0"
                               [(ngModel)]="typeAutoShare"
                               inputId="1"
                               label="{{ tranService.translate('datapool.label.registerByPayCode') }}"></p-radioButton>
            </div>
            <div class="col-1"></div>
            <div class="grid col-4">
                <p-radioButton name="registerOption" value="1"
                               [(ngModel)]="typeAutoShare"
                               inputId="2"
                               label="{{ tranService.translate('datapool.label.registerBySubCode') }}"></p-radioButton>
            </div>
        </div>
        <div class="flex flex-row justify-content-center align-content-start">
            <div class="grid col-4 pl-6">
                <span
                    style="font-size: 0.8em; color: grey;">{{ tranService.translate('datapool.text.nonOTPPayCode') }}</span>
            </div>
            <div class="col-1"></div>
            <div class="grid col-4 pl-6">
                <span
                    style="font-size: 0.8em; color: grey;"> {{ tranService.translate('datapool.text.nonOTPSubCode') }}</span>
            </div>
        </div>
        <div class="flex flex-row justify-content-center  mt-3">
            <p-button label="{{ tranService.translate('datapool.button.no') }}" icon="pi pi-times"
                      styleClass="p-button-secondary p-button-outlined"
                      (click)="onHideRegisBySubOrPay()"></p-button>
            <p-button label="{{ tranService.translate('datapool.button.register') }}" icon="pi pi-check"
                      type="button"
                      styleClass="p-button-info ml-2"
                      (click)="register()"></p-button>
        </div>
    </p-dialog>

    <!--    dialog confirm register by PayCode-->
    <p-dialog [header]="tranService.translate('datapool.label.notification')"
              [(visible)]="isShowRegisByPayCode" [modal]="true" [style]="{ width: '750px' }"
              [draggable]="false" [resizable]="false" *ngIf="isShowRegisByPayCode"
              (onHide)="onHideRegisByPayCode()">
        <div class="d-flex ai-center jc-center" style="text-align: center;">
            <p>
                <b style="font-size: large">{{ tranService.translate('datapool.message.confirmRegisterAutoSharePayCode') }}</b>
                <br>
                <span style="color: red;" >
        <i>{{ tranService.translate('datapool.text.noteRegisPayCode') }}</i>
      </span>
                <br>
                <a (click)="openReadMoreRegister()" class="cursor-pointer"
                   style="color: blue; text-decoration: underline;"><i>{{ tranService.translate('datapool.message.readMore') }}</i></a>
            </p>
        </div>
        <div class="flex flex-row justify-content-center  mt-3">
            <p-button label="{{ tranService.translate('datapool.button.no') }}" icon="pi pi-times"
                      styleClass="p-button-secondary p-button-outlined"
                      (click)="onHideRegisByPayCode()"></p-button>
            <p-button label="{{ tranService.translate('datapool.button.register') }}" icon="pi pi-check"
                      styleClass="p-button-info ml-2"
                      (click)="register()"></p-button>
        </div>
    </p-dialog>

<!--    dialog confirm otp regis-->
    <p-dialog [header]="tranService.translate('datapool.label.otpCode')" [(visible)]="isShowDiaglogOTPRegis"
              [modal]="true" [style]="{ width: '30vw' }" [draggable]="false" (onHide)="hideDiaglogConfirmOTP()"
              [resizable]="false">
        <form [formGroup]="formWithoutOTP" (ngSubmit)="onRegister()">
            <div class="flex flex-column gap-2 flex-1">
                <p-inputOtp formControlName="otp" class="mx-auto my-3" [integerOnly]="true" length="6"></p-inputOtp>
                <button type="button"
                        class="border-none mb-4 cursor-pointer flex flex-row justify-content-center font-semibold"
                        style="background-color: transparent;" [disabled]="countdown>0"
                        (click)="resetTimerForRegister()">{{ tranService.translate("datapool.message.resendOtp") }}&nbsp;<div
                    *ngIf="countdown>0"> {{ tranService.translate("datapool.message.in") }} {{ countdown }} {{ tranService.translate("datapool.message.sec") }}
                </div>
                </button>
                <div class="m-auto">
                    <button class="m-auto mr-2" [disabled]="formWithoutOTP.controls.otp.invalid" type="submit"
                            pButton>{{ tranService.translate("global.button.save") }}
                    </button>
                    <button type="button" class="m-auto ml-2 p-button-outlined" pButton
                            (click)="isShowDiaglogOTPRegis = false">{{ tranService.translate("global.button.cancel") }}
                    </button>
                </div>
            </div>
        </form>
    </p-dialog>

<!--dialog cancel subCode-->
    <p-dialog [header]="tranService.translate('datapool.label.notification')"
              [(visible)]="isShowCancelBySubCode" [modal]="true" [style]="{ width: '750px' }"
              [draggable]="false" [resizable]="false" *ngIf="isShowCancelBySubCode"
              (onHide)="isShowCancelBySubCode = false">
        <div class="d-flex ai-center jc-center" style="text-align: center;">
            <p>
                <b style="font-size: large">{{ tranService.translate('datapool.message.confirmCancelSubCode') }}</b>
                <br>
                    <span style="color: red; word-wrap:break-word;">
                        <i>{{ tranService.translate('datapool.text.noteCancelSubCode') }}</i>
                        </span>
                <br>
                <a (click)="openReadMoreUnRegister()" class="cursor-pointer"
                   style="color: blue; text-decoration: underline;"><i> {{tranService.translate('datapool.message.readMore') }}</i></a>
            </p>
        </div>
        <div class="flex flex-row justify-content-center align-items-center mt-3">
            <p-button label="{{ tranService.translate('datapool.button.no') }}" icon="pi pi-times"
                      styleClass="p-button-secondary p-button-outlined"
                      (click)="isShowCancelBySubCode = false"></p-button>
            <p-button label="{{ tranService.translate('datapool.button.yes') }}" icon="pi pi-check"
                      styleClass="p-button-info ml-2"
                      (click)="onCancel()"></p-button>
        </div>
    </p-dialog>
    <!--dialog cancel payCode-->
    <p-dialog [header]="tranService.translate('datapool.label.notification')"
              [(visible)]="isShowCancelByPayCode" [modal]="true" [style]="{ width: '750px' }"
              [draggable]="false" [resizable]="false" *ngIf="isShowCancelByPayCode"
              (onHide)="isShowCancelByPayCode = false">
        <div class="d-flex ai-center jc-center" style="text-align: center;">
            <span>
                    {{ tranService.translate('datapool.text.hasPayCode', {payCode: payCode}) }}
                </span>
            <p>
                <b style="font-size: large">{{ tranService.translate('datapool.message.confirmCancelPayCode', {payCode: payCode}) }}</b>
                <br>
                <span style="color: red; word-wrap:break-word;">
        <i>{{ tranService.translate('datapool.text.noteCancelPayCode') }}</i>
      </span>
                <br>
                <a (click)="openReadMoreUnRegister()" class="cursor-pointer"
                   style="color: blue; text-decoration: underline;"> <i>{{ tranService.translate('datapool.message.readMore') }}</i></a>
            </p>
        </div>
        <div class="flex flex-row justify-content-center align-items-center mt-3">
            <p-button label="{{ tranService.translate('datapool.button.no') }}" icon="pi pi-times"
                      styleClass="p-button-secondary p-button-outlined"
                      (click)="isShowCancelByPayCode = false"></p-button>
            <p-button label="{{ tranService.translate('datapool.button.yes') }}" icon="pi pi-check"
                      styleClass="p-button-info ml-2"
                      (click)="onCancel()"></p-button>
        </div>
    </p-dialog>
</div>
