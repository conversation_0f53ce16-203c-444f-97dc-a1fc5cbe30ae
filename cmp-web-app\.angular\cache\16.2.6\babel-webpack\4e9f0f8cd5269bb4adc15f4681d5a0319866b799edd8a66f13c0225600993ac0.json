{"ast": null, "code": "export default {\n  label: {\n    email: \"<PERSON><PERSON><PERSON> điện tử\",\n    password: \"<PERSON><PERSON><PERSON> khẩu\",\n    signIn: \"<PERSON><PERSON><PERSON> nhập\",\n    forgotPass: \"<PERSON>u<PERSON>n mật khẩu\",\n    m2mTitle: \"<PERSON><PERSON> thống Quản lý Thuê bao M2M\",\n    resetPass: \"<PERSON>h<PERSON><PERSON> phục mật khẩu\",\n    editProfile: \"Thông tin tài khoản\",\n    getProfileUser: \"Xem chi tiết thông tin cá nhân\",\n    updateProfile: \"Cập nhật thông tin cá nhân\",\n    changePassword: \"Đổi mật khẩu\",\n    logout: \"<PERSON><PERSON>ng xuất\"\n  },\n  text: {\n    newUI: \"Chúng tôi xin thông báo giao diện mới đang được thử nghiệm từ ngày 09/07/2024. Quý khách hàng vui lòng trải nghiệm và góp ý để chúng tôi hoàn thiện hơn. <PERSON><PERSON> cảm ơn!\"\n  }\n};", "map": {"version": 3, "names": ["label", "email", "password", "signIn", "forgot<PERSON>ass", "m2mTitle", "resetPass", "editProfile", "getProfileUser", "updateProfile", "changePassword", "logout", "text", "newUI"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\i18n\\vi\\login.ts"], "sourcesContent": ["export default {\r\n    label: {\r\n        email: \"<PERSON><PERSON><PERSON> điện tử\",\r\n        password: \"<PERSON><PERSON><PERSON> khẩu\",\r\n        signIn : \"<PERSON><PERSON><PERSON> nhập\",\r\n        forgotPass : \"<PERSON>u<PERSON>n mật khẩu\",\r\n        m2mTitle : \"<PERSON><PERSON> thống Quản lý Thuê bao M2M\",\r\n        resetPass : \"<PERSON>h<PERSON><PERSON> phục mật khẩu\",\r\n        editProfile : \"Thông tin tài khoản\",\r\n        getProfileUser: \"Xem chi tiết thông tin cá nhân\",\r\n        updateProfile: \"Cập nhật thông tin cá nhân\",\r\n        changePassword : \"Đổi mật khẩu\",\r\n        logout : \"<PERSON><PERSON>ng xuất\",\r\n    },\r\n    text: {\r\n        newUI: \"Chúng tôi xin thông báo giao diện mới đang được thử nghiệm từ ngày 09/07/2024. Quý khách hàng vui lòng trải nghiệm và góp ý để chúng tôi hoàn thiện hơn. <PERSON><PERSON> cảm ơn!\"\r\n    }\r\n}\r\n"], "mappings": "AAAA,eAAe;EACXA,KAAK,EAAE;IACHC,KAAK,EAAE,aAAa;IACpBC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAG,WAAW;IACpBC,UAAU,EAAG,eAAe;IAC5BC,QAAQ,EAAG,+BAA+B;IAC1CC,SAAS,EAAG,oBAAoB;IAChCC,WAAW,EAAG,qBAAqB;IACnCC,cAAc,EAAE,gCAAgC;IAChDC,aAAa,EAAE,4BAA4B;IAC3CC,cAAc,EAAG,cAAc;IAC/BC,MAAM,EAAG;GACZ;EACDC,IAAI,EAAE;IACFC,KAAK,EAAE;;CAEd"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}