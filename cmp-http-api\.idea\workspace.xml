<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8f9c9fab-7932-4b7c-8b89-8928ac1b871f" name="Changes" comment="bỏ bớt tham số cho getDetailTrafficWallet" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="dev2.4-dev3rd" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="UPDATE_TYPE" value="REBASE" />
  </component>
  <component name="GitRewordedCommitMessages">
    <option name="commitMessagesMapping">
      <RewordedCommitMessageMapping>
        <option name="originalMessage" value="3rd api" />
        <option name="rewordedMessage" value="3rd api" />
      </RewordedCommitMessageMapping>
    </option>
    <option name="currentCommit" value="1" />
    <option name="onto" value="fc687a83f1a6028086eedde2ea4e2ffed4941b66" />
  </component>
  <component name="HttpClientEndpointsTabState">
    <option name="cachedRequestData" value="&lt;CachedHttpClientTabRequests&gt;&#10;  &lt;entry key=&quot;9cfe50d4dccd998e0093ce42ddb296b62eb9663a&quot; value=&quot;###&amp;#10;GET http://localhost:8080/api/msimapi/getListContract?&amp;#10;    centerCode={{$random.alphanumeric(8)}}&amp;amp;&amp;#10;    contractCode={{$random.alphanumeric(8)}}&amp;amp;&amp;#10;    paymentName={{$random.alphanumeric(8)}}&amp;amp;&amp;#10;    customerCode={{$random.alphanumeric(8)}}&amp;amp;&amp;#10;    contactPhone={{$random.alphanumeric(8)}}&amp;amp;&amp;#10;    customerName={{$random.alphanumeric(8)}}&amp;amp;&amp;#10;    contractor={{$random.alphanumeric(8)}}&amp;amp;&amp;#10;    page={{$random.integer(100)}}&amp;amp;&amp;#10;    size={{$random.integer(100)}}&amp;amp;&amp;#10;    sort={{$random.alphanumeric(8)}}&quot; /&gt;&#10;&lt;/CachedHttpClientTabRequests&gt;" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2zGZnUEXqOz21jEvNv0A6fe7kWY" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;HTTP Request.generated-requests | #6.executor&quot;: &quot;Run&quot;,
    &quot;Maven.cmp-http-api [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.cmp-http-api [install].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;SHELLCHECK.PATH&quot;: &quot;C:\\Users\\<USER>\\AppData\\Roaming\\JetBrains\\IntelliJIdea2023.1\\plugins\\Shell Script\\shellcheck.exe&quot;,
    &quot;Spring Boot.CmpHttpApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev2.4&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager" selected="Spring Boot.CmpHttpApplication">
    <configuration name="generated-requests | #6" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$APPLICATION_CONFIG_DIR$/scratches/generated-requests.http" executionIdentifier="#6" index="6" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="CmpHttpApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="cmp-http-api" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="vn.vnpt.cmp.CmpHttpApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="vn.vnpt.cmp.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.CmpHttpApplication" />
        <item itemvalue="HTTP Request.generated-requests | #6" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="8f9c9fab-7932-4b7c-8b89-8928ac1b871f" name="Changes" comment="" />
      <created>1751359536586</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751359536586</updated>
      <workItem from="1751359537747" duration="899000" />
      <workItem from="1751431273536" duration="281000" />
      <workItem from="1751431642722" duration="1429000" />
      <workItem from="1751513049055" duration="179000" />
      <workItem from="1751514855793" duration="14079000" />
      <workItem from="1751600949602" duration="39652000" />
      <workItem from="1752025038754" duration="8245000" />
      <workItem from="1752046022901" duration="972000" />
      <workItem from="1752047006306" duration="47805000" />
      <workItem from="1752460208626" duration="7885000" />
      <workItem from="1752549211155" duration="4061000" />
      <workItem from="1752553922533" duration="17946000" />
      <workItem from="1752717658580" duration="10268000" />
      <workItem from="1752829569833" duration="2422000" />
      <workItem from="1753327765400" duration="5789000" />
      <workItem from="1753415659144" duration="21417000" />
      <workItem from="1753843321384" duration="3717000" />
    </task>
    <task id="LOCAL-00001" summary="innit 3rd api device">
      <option name="closed" value="true" />
      <created>1752031749626</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752031749626</updated>
    </task>
    <task id="LOCAL-00002" summary="innit 3rd api device">
      <option name="closed" value="true" />
      <created>1752137751578</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752137751578</updated>
    </task>
    <task id="LOCAL-00003" summary="innit 3rd api device">
      <option name="closed" value="true" />
      <created>1752204350159</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752204350159</updated>
    </task>
    <task id="LOCAL-00004" summary="innit 3rd api device">
      <option name="closed" value="true" />
      <created>1752206331922</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752206331922</updated>
    </task>
    <task id="LOCAL-00005" summary="innit 3rd api device">
      <option name="closed" value="true" />
      <created>1752228250483</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752228250483</updated>
    </task>
    <task id="LOCAL-00006" summary="innit 3rd api device">
      <option name="closed" value="true" />
      <created>1752573043123</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752573043123</updated>
    </task>
    <task id="LOCAL-00007" summary="innit 3rd api device">
      <option name="closed" value="true" />
      <created>1752573091913</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1752573091913</updated>
    </task>
    <task id="LOCAL-00008" summary="bổ sung check quyền theo msisdn cho getTerminalUsageDataDetails">
      <option name="closed" value="true" />
      <created>1752717844691</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1752717844691</updated>
    </task>
    <task id="LOCAL-00009" summary="bổ sung id cho api 3rd getListCustomer">
      <option name="closed" value="true" />
      <created>1753686247780</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753686247780</updated>
    </task>
    <task id="LOCAL-00010" summary="bỏ bớt tham số cho getDetailTrafficWallet">
      <option name="closed" value="true" />
      <created>1753698995852</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753698995852</updated>
    </task>
    <option name="localTasksCounter" value="11" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="innit 3rd api device" />
    <MESSAGE value="3rd api" />
    <MESSAGE value="bổ sung check quyền theo msisdn cho getTerminalUsageDataDetails" />
    <MESSAGE value="bổ sung id cho api 3rd getListCustomer" />
    <MESSAGE value="bỏ bớt tham số cho getDetailTrafficWallet" />
    <option name="LAST_COMMIT_MESSAGE" value="bỏ bớt tham số cho getDetailTrafficWallet" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/vn/vnpt/cmp/service/ThirdPartyService.java</url>
          <line>814</line>
          <option name="timeStamp" value="16" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/vn/vnpt/cmp/service/WalletService.java</url>
          <line>59</line>
          <option name="timeStamp" value="17" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/vn/vnpt/cmp/controller/WalletController.java</url>
          <line>50</line>
          <option name="timeStamp" value="18" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/vn/vnpt/cmp/service/ThirdPartyService.java</url>
          <line>664</line>
          <option name="timeStamp" value="23" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/vn/vnpt/cmp/service/ThirdPartyService.java</url>
          <line>714</line>
          <option name="timeStamp" value="24" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/vn/vnpt/cmp/controller/ThirdPartyController.java</url>
          <line>279</line>
          <option name="timeStamp" value="25" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>