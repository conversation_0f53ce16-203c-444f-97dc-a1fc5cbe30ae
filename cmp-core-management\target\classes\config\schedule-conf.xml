<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
                            http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd
                            http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.0.xsd">



    <task:scheduled-tasks scheduler="jobImportDataSim1">
        <task:scheduled ref="importData1" method="execute" fixed-delay="30000"/>
    </task:scheduled-tasks>
    <task:scheduler id="jobImportDataSim1" pool-size="1"/>
    <task:scheduled-tasks scheduler="jobImportDataSim2">
        <task:scheduled ref="importData2" method="execute" fixed-delay="30000"/>
    </task:scheduled-tasks>
    <task:scheduler id="jobImportDataSim2" pool-size="1"/>

    <task:scheduled-tasks scheduler="jobUpdateDataSim1">
        <task:scheduled ref="updateData" method="execute" fixed-delay="30000"/>
    </task:scheduled-tasks>
    <task:scheduler id="jobUpdateDataSim1" pool-size="1"/>

</beans>
