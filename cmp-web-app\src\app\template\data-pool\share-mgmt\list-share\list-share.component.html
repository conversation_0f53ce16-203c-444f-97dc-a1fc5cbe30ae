<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{this.tranService.translate("global.menu.shareList")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="flex flex-row justify-content-end align-items-center gap-2 responsive-container">
        <p-splitButton *ngIf="checkAuthen([allPermissions.DATAPOOL.CREATE_SHARE])" styleClass="p-button-success equal-button" [label]="tranService.translate('datapool.label.addSharePhone')" icon="pi pi-plus" [model]="itemAddShare"></p-splitButton>
<!--        <p-button (click)="visible=true" icon="pi pi-plus" [label]="tranService.translate('datapool.button.add')" *ngIf="checkAuthen([allPermissions.DATAPOOL.CREATE_SHARE])"></p-button>-->
        <p-button *ngIf="checkAuthen([allPermissions.DATAPOOL.SHARE_WALLET])" styleClass="equal-button" icon="pi pi-share-alt" [label]="this.tranService.translate('datapool.button.share')" [routerLink]="['/data-pool/shareMgmt/share']"></p-button>
<!--        <p-button icon="pi pi-file-import" [label]="tranService.translate('global.button.import')" (click)="importByFile()" styleClass="p-button-success" *ngIf="checkAuthen([allPermissions.DATAPOOL.CREATE_SHARE])"></p-button>-->
    </div>
</div>

<search-filter-separate
    [searchList]="searchList"
    [filterList]="filterList"
    (searchDetail)="catchSearchList($event)"
>
</search-filter-separate>

<table-vnpt
    [tableId]="'tableShareList'"
    [fieldId]="'walletCode'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.shareList')"
    actionWidth="50px"
></table-vnpt>

<div class="flex justify-content-center dialog-push-group">
    <p-dialog [header]="tranService.translate('datapool.text.importByFile')" [(visible)]="isShowDialogImportByFile" [modal]="true" [style]="{ width: '700px' }" [draggable]="false" [resizable]="false" styleClass="dialog-upload-device">
        <div class="w-full field grid">
            <div class="col-10 flex flex-row justify-content-start align-items-center">
                <input-file-vnpt class="w-full upload-device-file" [(fileObject)]="fileObject" [clearFileCallback]="clearFileCallback.bind(this)"
                                 [options]="optionInputFile"
                ></input-file-vnpt>
            </div>
            <div class="col-2 flex flex-row justify-content-end align-items-center">
                <p-button icon="pi pi-download" [pTooltip]="tranService.translate('global.button.downloadTemp')" styleClass="p-button-outlined p-button-secondary" (click)="downloadTemplate()"></p-button>
            </div>
        </div>
        <div class="grid"><div class="col pt-0"><small class="text-red-500" *ngIf="isShowErrorUpload">{{messageErrorUpload}}</small></div></div>
    </p-dialog>
</div>

<p-dialog (onHide)="onHideAdd()" [header]="tranService.translate('datapool.button.add')" [(visible)]="visible" [style]="{width: '50vw'}" [draggable]="false" [resizable]="false" [modal]="true" styleClass="responsive-dialog-listShare">
    <form action="" [formGroup]="addPhoneGroup" (submit)="submitForm()" class="flex flex-column">
        <div class="px-4 flex flex-row flex-nowrap align-items-center">
            <label htmlFor="fullName"  style="min-width: 140px;">{{tranService.translate("datapool.label.fullName")}}</label>
            <input class="flex-1" formControlName="name" pInputText id="fullName" type="text" [placeholder]="tranService.translate('datapool.placeholder.fullName')">
        </div>
        <div class="px-4 py-0 flex flex-row flex-nowrap align-items-center">
            <label htmlFor="fullName"  style="min-width: 140px;"></label>
            <div class="text-red-500" *ngIf="addPhoneGroup.controls.name.errors?.['maxlength'] && addPhoneGroup.controls.name.dirty">{{tranService.translate("global.message.maxLength",{len:50})}}</div>
            <div class="text-red-500" *ngIf="addPhoneGroup.controls.name.errors?.['pattern'] && addPhoneGroup.controls.name.dirty">{{tranService.translate("global.message.formatCode")}}</div>
        </div>
        <div class="px-4 flex flex-row flex-nowrap align-items-center pt-3">
            <label htmlFor="phone"  style="min-width: 140px;">{{tranService.translate("datapool.label.phone")}}<span class="text-red-500">*</span></label>
            <input class="flex-1" formControlName="phone" pInputText id="phone" type="text" [placeholder]="tranService.translate('datapool.placeholder.phone')">
        </div>
        <div class="px-4 py-0 flex flex-row flex-nowrap align-items-center">
            <label htmlFor="fullName"  style="min-width: 140px;"></label>
            <div class="text-red-500" *ngIf="addPhoneGroup.controls.phone.errors?.['required'] && addPhoneGroup.controls.phone.dirty">{{tranService.translate("global.message.required")}}</div>
            <small class="text-red-500" *ngIf="addPhoneGroup.controls.phone.errors?.['pattern'] || addPhoneGroup.controls.phone.errors?.['numericMaxLength']">{{tranService.translate("datapool.message.digitError")}}</small>
            <div class="text-red-500" *ngIf="addPhoneGroup.controls.phone.errors?.['existed'] && addPhoneGroup.controls.phone.dirty">{{tranService.translate("datapool.message.existedPhone")}}</div>
        </div>
        <div class="px-4 flex flex-row flex-nowrap align-items-center pt-3">
            <label htmlFor="email"  style="min-width: 140px;">{{tranService.translate("datapool.label.email")}}</label>
            <input class="flex-1" formControlName="email" pInputText id="email" type="text" [placeholder]="tranService.translate('datapool.placeholder.email')">
        </div>
        <div class="px-4 pt-0 flex flex-row flex-nowrap align-items-center pb-3">
            <label htmlFor="fullName"  style="min-width: 140px;"></label>
            <div class="text-red-500" *ngIf="(addPhoneGroup.controls.email.errors?.['pattern']||addPhoneGroup.controls.email.errors?.['email']) && addPhoneGroup.controls.email.dirty">{{tranService.translate("global.message.formatEmail")}}</div>
            <div class="text-red-500" *ngIf="addPhoneGroup.controls.email.errors?.['maxlength'] && addPhoneGroup.controls.email.dirty">{{tranService.translate("global.message.maxLength",{len:100})}}</div>
        </div>
        <div class="flex flex-row gap-2 justify-content-center">
            <button type="button" pButton class="p-button-secondary" [label]="tranService.translate('global.button.cancel')" (click)="closeForm()"></button>
            <button pButton class="" [disabled]="!addPhoneGroup.valid" [label]="tranService.translate('global.button.save')" ></button>
        </div>
    </form>
</p-dialog>

<p-dialog [header]="tranService.translate('global.button.edit')" [(visible)]="visibleEdit" [style]="{width: '50vw'}" [draggable]="false" [resizable]="false" [modal]="true" styleClass="responsive-dialog-listShare">
    <form action="" [formGroup]="sharedEditGroup" (submit)="submitEditForm()" class="flex flex-column">
        <div class="px-4 flex flex-row flex-nowrap align-items-center">
            <label htmlFor="fullName"  style="min-width: 140px;">{{tranService.translate("datapool.label.fullName")}}</label>
            <input class="flex-1" formControlName="name" pInputText id="fullName" type="text" [placeholder]="tranService.translate('datapool.placeholder.fullName')">
        </div>
        <div class="px-4 py-0 flex flex-row flex-nowrap align-items-center">
            <label htmlFor="fullName"  style="min-width: 140px;"></label>
            <div class="text-red-500" *ngIf="sharedEditGroup.controls.name.errors?.['required'] && sharedEditGroup.controls.name.dirty">{{tranService.translate("global.message.required")}}</div>
            <div class="text-red-500" *ngIf="sharedEditGroup.controls.name.errors?.['maxlength'] && sharedEditGroup.controls.name.dirty">{{tranService.translate("global.message.maxLength",{len:50})}}</div>
            <div class="text-red-500" *ngIf="( sharedEditGroup.controls.name.errors?.['pattern'] || addPhoneGroup.controls.email.errors?.['email'] ) && sharedEditGroup.controls.name.dirty">{{tranService.translate("global.message.formatCode")}}</div>
        </div>
        <div class="px-4 flex flex-row flex-nowrap align-items-center pt-3">
            <label htmlFor="phone"  style="min-width: 140px;">{{tranService.translate("datapool.label.phone")}}<span class="text-red-500">*</span></label>
            <input class="flex-1" formControlName="phone" pInputText id="phone" type="text" [placeholder]="tranService.translate('datapool.placeholder.phone')">
        </div>
        <div class="px-4 py-0 flex flex-row flex-nowrap align-items-center">
            <label htmlFor="fullName"  style="min-width: 140px;"></label>
            <div class="text-red-500" *ngIf="sharedEditGroup.controls.phone.errors?.['required'] && sharedEditGroup.controls.phone.dirty">{{tranService.translate("global.message.required")}}</div>
            <small class="text-red-500" *ngIf="sharedEditGroup.controls.phone.errors?.['pattern']">{{tranService.translate("global.message.invalidPhone")}}</small>
            <div class="text-red-500" *ngIf="sharedEditGroup.controls.phone.errors?.['duplicateItem'] && sharedEditGroup.controls.phone.dirty">{{tranService.translate("datapool.message.existedPhone")}}</div>
        </div>
        <div class="px-4 py-0 flex flex-row flex-nowrap align-items-center">
            <label htmlFor="fullName"  style="min-width: 140px;"></label>
            <div class="text-red-500" *ngIf="sharedEditGroup.controls.phone.errors?.['numericLength'] && sharedEditGroup.controls.phone.dirty">{{tranService.translate("datapool.message.digitError")}}</div>
        </div>
        <div class="px-4 flex flex-row flex-nowrap align-items-center pt-3">
            <label htmlFor="email"  style="min-width: 140px;">{{tranService.translate("datapool.label.email")}}</label>
            <input class="flex-1" formControlName="email" pInputText id="email" type="text" [placeholder]="tranService.translate('datapool.placeholder.email')">
        </div>
        <div class="px-4 pt-0 flex flex-row flex-nowrap align-items-center pb-3">
            <label htmlFor="fullName"  style="min-width: 140px;"></label>
            <div class="text-red-500" *ngIf="sharedEditGroup.controls.email.errors?.['pattern'] && sharedEditGroup.controls.email.dirty">{{tranService.translate("global.message.formatEmail")}}</div>
            <div class="text-red-500" *ngIf="sharedEditGroup.controls.email.errors?.['maxlength'] && sharedEditGroup.controls.email.dirty">{{tranService.translate("global.message.maxLength",{len:100})}}</div>
        </div>
        <div class="flex flex-row gap-2 justify-content-center">
            <button type="button" pButton class="p-button-secondary" [label]="tranService.translate('global.button.cancel')" (click)="closeForm()"></button>
            <button pButton class="" [disabled]="sharedEditGroup.invalid" [label]="tranService.translate('global.button.save')" ></button>
        </div>
    </form>
</p-dialog>

<div class="flex justify-content-center dialog-vnpt">
    <p-dialog [header]="tranService.translate('datapool.label.detailSharing')" [(visible)]="isShowModalDetail" [modal]="true" [style]="{ width: '980px' }" [draggable]="false" [resizable]="false" *ngIf="isShowModalDetail">
        <div *ngIf="isPrivilage">
            <div class="vnpt mb-3 flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
                <div class="">
                    <div class="text-xl font-bold mb-1">{{this.tranService.translate("datapool.label.shareInfo")}}</div>
                    <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
                </div>
            </div>

            <p-card>
                <div class="flex flex-row surface-200 p-4 border-round wallet-detail-div">
                    <div class="flex-1">
                        <div class="font-medium text-base">{{tranService.translate('device.label.msisdn')}}</div>
                        <div class="font-semibold text-lg">{{phone}}</div>
                    </div>
                    <div class="flex-1">
                        <div class="font-medium text-base">{{tranService.translate('datapool.label.fullName')}}</div>
                        <div class="font-semibold text-lg">{{name}}</div>
                    </div>
                    <div class="flex-1">
                        <div class="font-medium text-base">Email</div>
                        <div class="font-semibold text-lg">{{email}}</div>
                    </div>
                </div>
            </p-card>

            <search-filter-separate
                [searchList]="searchListDetail"
                [filterList]="filterListDetail"
                (searchDetail)="catchSearchDetail($event)">
            </search-filter-separate>

            <table-vnpt
                [tableId]="'tableDetailSharing'"
                [fieldId]="'walletCode'"
                [(selectItems)]="selectItems"
                [columns]="columnsInDetailShare"
                [dataSet]="dataSetShareDetail"
                [options]="optionTableDetail"
                [loadData]="searchInDetail.bind(this)"
                [pageNumber]="pageNumberDetail"
                [pageSize]="pageSizeDetail"
                [sort]="sortDetail"
                [labelTable]="this.tranService.translate('datapool.label.shareInfo')"
            ></table-vnpt>

        </div>
    </p-dialog>
</div>
