{"ast": null, "code": "import { AccountService } from \"src/app/service/account/AccountService\";\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\nimport { ComponentBase } from \"src/app/component.base\";\nimport { ComboLazyControl } from \"../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/service/customer/CustomerService\";\nimport * as i2 from \"../../../service/contract/ContractService\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/breadcrumb\";\nimport * as i7 from \"primeng/inputtext\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"../../common-module/table/table.component\";\nimport * as i10 from \"../../common-module/combobox-lazyload/combobox.lazyload\";\nimport * as i11 from \"primeng/autocomplete\";\nimport * as i12 from \"primeng/dropdown\";\nimport * as i13 from \"primeng/dialog\";\nimport * as i14 from \"primeng/panel\";\nimport * as i15 from \"primeng/radiobutton\";\nimport * as i16 from \"primeng/tabview\";\nimport * as i17 from \"src/app/service/account/AccountService\";\nconst _c0 = function () {\n  return [\"/accounts/create\"];\n};\nfunction AppAccountListComponent_p_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-button\", 38);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", ctx_r0.tranService.translate(\"global.button.create\"))(\"routerLink\", i0.ɵɵpureFunction0(2, _c0));\n  }\n}\nfunction AppAccountListComponent_p_dialog_43_div_3_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"label\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 32)(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r7.tranService.translate(\"account.label.province\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r7.accountResponse.provinceName, \" (\", ctx_r7.accountResponse.provinceCode, \")\");\n  }\n}\nfunction AppAccountListComponent_p_dialog_43_div_3_div_45_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r12.username);\n  }\n}\nfunction AppAccountListComponent_p_dialog_43_div_3_div_45_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, AppAccountListComponent_p_dialog_43_div_3_div_45_div_4_div_1_Template, 2, 1, \"div\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r12 == null ? null : item_r12.isRootCustomer);\n  }\n}\nfunction AppAccountListComponent_p_dialog_43_div_3_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"label\", 57);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 58);\n    i0.ɵɵtemplate(4, AppAccountListComponent_p_dialog_43_div_3_div_45_div_4_Template, 2, 1, \"div\", 61);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r8.tranService.translate(\"account.label.customerAccount\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.accountResponse == null ? null : ctx_r8.accountResponse.userManages);\n  }\n}\nfunction AppAccountListComponent_p_dialog_43_div_3_div_46_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.accountResponse.rootAccount.username, \" \");\n  }\n}\nfunction AppAccountListComponent_p_dialog_43_div_3_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"label\", 57);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 58);\n    i0.ɵɵtemplate(4, AppAccountListComponent_p_dialog_43_div_3_div_46_div_4_Template, 2, 1, \"div\", 64);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r9.tranService.translate(\"account.label.customerAccount\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.accountResponse == null ? null : ctx_r9.accountResponse.rootAccount == null ? null : ctx_r9.accountResponse.rootAccount.username);\n  }\n}\nfunction AppAccountListComponent_p_dialog_43_div_3_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r16 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r16.roleName, \" \");\n  }\n}\nfunction AppAccountListComponent_p_dialog_43_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 45)(2, \"div\", 46)(3, \"label\", 47);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 32);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 48)(8, \"label\", 49);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 32);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 48)(13, \"label\", 50);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 51);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 48)(18, \"label\", 52);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 32);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 48)(23, \"label\", 53);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 51);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 48)(28, \"label\", 54);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 32);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 45)(33, \"div\", 48)(34, \"label\", 55);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 32)(37, \"span\");\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(39, AppAccountListComponent_p_dialog_43_div_3_div_39_Template, 6, 3, \"div\", 56);\n    i0.ɵɵelementStart(40, \"div\", 48)(41, \"label\", 57);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 58);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(45, AppAccountListComponent_p_dialog_43_div_3_div_45_Template, 5, 2, \"div\", 59);\n    i0.ɵɵtemplate(46, AppAccountListComponent_p_dialog_43_div_3_div_46_Template, 5, 2, \"div\", 59);\n    i0.ɵɵelementStart(47, \"div\", 48)(48, \"label\", 60);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 58);\n    i0.ɵɵtemplate(51, AppAccountListComponent_p_dialog_43_div_3_div_51_Template, 2, 1, \"div\", 61);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"account.label.username\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.accountResponse.username, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"account.label.status\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getStringUserStatus(ctx_r3.accountResponse.status), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"account.label.fullname\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.accountResponse.fullName, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"account.label.phone\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.accountResponse.phone, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"account.label.email\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.accountResponse.email, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"account.label.description\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.accountResponse.description, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"account.label.userType\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.getStringUserType(ctx_r3.accountResponse.type));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.accountInfo.userType != ctx_r3.optionUserType.ADMIN && ctx_r3.accountInfo.userType != ctx_r3.optionUserType.AGENCY);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r3.accountInfo.userType == ctx_r3.optionUserType.CUSTOMER && (ctx_r3.userType == ctx_r3.optionUserType.ADMIN || ctx_r3.userType == ctx_r3.optionUserType.PROVINCE) ? \"\" : \"hidden\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"account.label.managerName\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.accountResponse == null ? null : ctx_r3.accountResponse.manager == null ? null : ctx_r3.accountResponse.manager.username, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.accountInfo.userType == ctx_r3.optionUserType.DISTRICT);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.accountInfo.userType == ctx_r3.optionUserType.CUSTOMER && !(ctx_r3.accountResponse == null ? null : ctx_r3.accountResponse.isRootCustomer));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r3.tranService.translate(\"account.label.role\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.accountResponse.roles);\n  }\n}\nconst _c1 = function () {\n  return {\n    standalone: true\n  };\n};\nconst _c2 = function () {\n  return [5, 10, 20, 25, 50];\n};\nfunction AppAccountListComponent_p_dialog_43_p_tabPanel_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 40)(1, \"div\", 65)(2, \"input\", 66);\n    i0.ɵɵlistener(\"keydown.enter\", function AppAccountListComponent_p_dialog_43_p_tabPanel_4_Template_input_keydown_enter_2_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.onSearchCustomer(true));\n    })(\"ngModelChange\", function AppAccountListComponent_p_dialog_43_p_tabPanel_4_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.paramQuickSearchCustomer.keyword = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-button\", 67);\n    i0.ɵɵlistener(\"click\", function AppAccountListComponent_p_dialog_43_p_tabPanel_4_Template_p_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.onSearchCustomer(true));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(4, \"table-vnpt\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r4.tranService.translate(\"global.menu.listcustomer\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"placeholder\", ctx_r4.tranService.translate(\"sim.label.quickSearch\"))(\"ngModel\", ctx_r4.paramQuickSearchCustomer.keyword)(\"ngModelOptions\", i0.ɵɵpureFunction0(15, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"pageNumber\", ctx_r4.paginationCustomer.page)(\"pageSize\", ctx_r4.paginationCustomer.size)(\"columns\", ctx_r4.columnInfoCustomer)(\"dataSet\", ctx_r4.dataSetCustomer)(\"options\", ctx_r4.optionTableCustomer)(\"loadData\", ctx_r4.searchCustomer.bind(ctx_r4))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(16, _c2))(\"scrollHeight\", \"400px\")(\"sort\", ctx_r4.paginationCustomer.sortBy)(\"params\", ctx_r4.paramQuickSearchCustomer);\n  }\n}\nfunction AppAccountListComponent_p_dialog_43_p_tabPanel_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 40)(1, \"div\", 65)(2, \"input\", 66);\n    i0.ɵɵlistener(\"keydown.enter\", function AppAccountListComponent_p_dialog_43_p_tabPanel_5_Template_input_keydown_enter_2_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.onSearchContract(true));\n    })(\"ngModelChange\", function AppAccountListComponent_p_dialog_43_p_tabPanel_5_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r23.paramQuickSearchContract.keyword = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-button\", 67);\n    i0.ɵɵlistener(\"click\", function AppAccountListComponent_p_dialog_43_p_tabPanel_5_Template_p_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.onSearchContract(true));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(4, \"table-vnpt\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r5.tranService.translate(\"global.menu.listbill\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"placeholder\", ctx_r5.tranService.translate(\"sim.label.quickSearch\"))(\"ngModel\", ctx_r5.paramQuickSearchContract.keyword)(\"ngModelOptions\", i0.ɵɵpureFunction0(15, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"pageNumber\", ctx_r5.paginationContract.page)(\"pageSize\", ctx_r5.paginationContract.size)(\"columns\", ctx_r5.columnInfoContract)(\"dataSet\", ctx_r5.dataSetContract)(\"options\", ctx_r5.optionTableContract)(\"loadData\", ctx_r5.searchContract.bind(ctx_r5))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(16, _c2))(\"scrollHeight\", \"400px\")(\"sort\", ctx_r5.paginationContract.sortBy)(\"params\", ctx_r5.paramQuickSearchContract);\n  }\n}\nfunction AppAccountListComponent_p_dialog_43_p_tabPanel_6_label_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 88);\n    i0.ɵɵlistener(\"click\", function AppAccountListComponent_p_dialog_43_p_tabPanel_6_label_18_Template_label_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r27.isShowSecretKey = true);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppAccountListComponent_p_dialog_43_p_tabPanel_6_label_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 89);\n    i0.ɵɵlistener(\"click\", function AppAccountListComponent_p_dialog_43_p_tabPanel_6_label_19_Template_label_click_0_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r29.isShowSecretKey = false);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppAccountListComponent_p_dialog_43_p_tabPanel_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-tabPanel\", 69)(1, \"div\", 70)(2, \"p-panel\", 71)(3, \"div\", 72)(4, \"p-radioButton\", 73);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountListComponent_p_dialog_43_p_tabPanel_6_Template_p_radioButton_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.statusGrantApi = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p-radioButton\", 74);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountListComponent_p_dialog_43_p_tabPanel_6_Template_p_radioButton_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.statusGrantApi = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 75)(7, \"div\", 76)(8, \"div\", 77)(9, \"label\", 78);\n    i0.ɵɵtext(10, \"Client ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"input\", 79);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountListComponent_p_dialog_43_p_tabPanel_6_Template_input_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r34 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r34.genGrantApi.clientId = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(12, \"div\", 76)(13, \"div\", 77)(14, \"label\", 78);\n    i0.ɵɵtext(15, \"Secret Key\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 80)(17, \"input\", 81);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountListComponent_p_dialog_43_p_tabPanel_6_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.genGrantApi.secretKey = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, AppAccountListComponent_p_dialog_43_p_tabPanel_6_label_18_Template, 1, 0, \"label\", 82);\n    i0.ɵɵtemplate(19, AppAccountListComponent_p_dialog_43_p_tabPanel_6_label_19_Template, 1, 0, \"label\", 83);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(20, \"div\")(21, \"p-panel\", 71)(22, \"div\", 84)(23, \"div\", 85)(24, \"p-dropdown\", 86);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountListComponent_p_dialog_43_p_tabPanel_6_Template_p_dropdown_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.paramsSearchGrantApi.module = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 85)(26, \"input\", 87);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountListComponent_p_dialog_43_p_tabPanel_6_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.paramsSearchGrantApi.api = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"p-button\", 67);\n    i0.ɵɵlistener(\"click\", function AppAccountListComponent_p_dialog_43_p_tabPanel_6_Template_p_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r38 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r38.onSearchGrantApi(true));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(28, \"table-vnpt\", 68);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r6.tranService.translate(\"account.text.grantApi\"));\n    i0.ɵɵproperty(\"pt\", \"ProfileTab\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"showHeader\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r6.tranService.translate(\"account.text.working\"))(\"ngModel\", ctx_r6.statusGrantApi)(\"disabled\", true)(\"ngModelOptions\", i0.ɵɵpureFunction0(40, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", ctx_r6.tranService.translate(\"account.text.notWorking\"))(\"ngModel\", ctx_r6.statusGrantApi)(\"disabled\", true)(\"ngModelOptions\", i0.ɵɵpureFunction0(41, _c1));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r6.genGrantApi.clientId)(\"disabled\", true)(\"ngModelOptions\", i0.ɵɵpureFunction0(42, _c1));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r6.genGrantApi.secretKey)(\"ngModelOptions\", i0.ɵɵpureFunction0(43, _c1))(\"type\", ctx_r6.isShowSecretKey ? \"text\" : \"password\")(\"disabled\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isShowSecretKey == false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isShowSecretKey == true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"showHeader\", false);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"showClear\", true)(\"ngModel\", ctx_r6.paramsSearchGrantApi.module)(\"ngModelOptions\", i0.ɵɵpureFunction0(44, _c1))(\"options\", ctx_r6.listModule)(\"emptyFilterMessage\", ctx_r6.tranService.translate(\"global.text.nodata\"))(\"placeholder\", ctx_r6.tranService.translate(\"account.text.module\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r6.paramsSearchGrantApi.api)(\"ngModelOptions\", i0.ɵɵpureFunction0(45, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"fieldId\", \"id\")(\"pageNumber\", ctx_r6.paginationGrantApi.page)(\"pageSize\", ctx_r6.paginationGrantApi.size)(\"columns\", ctx_r6.columnInfoGrantApi)(\"dataSet\", ctx_r6.dataSetGrantApi)(\"options\", ctx_r6.optionTableGrantApi)(\"loadData\", ctx_r6.searchGrantApi.bind(ctx_r6))(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(46, _c2))(\"scrollHeight\", \"400px\")(\"sort\", ctx_r6.paginationGrantApi.sortBy)(\"params\", ctx_r6.paramsSearchGrantApi);\n  }\n}\nconst _c3 = function () {\n  return {\n    width: \"980px\"\n  };\n};\nfunction AppAccountListComponent_p_dialog_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-dialog\", 29);\n    i0.ɵɵlistener(\"visibleChange\", function AppAccountListComponent_p_dialog_43_Template_p_dialog_visibleChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.isShowModalDetail = $event);\n    });\n    i0.ɵɵelementStart(1, \"p-tabView\", 39);\n    i0.ɵɵlistener(\"onChange\", function AppAccountListComponent_p_dialog_43_Template_p_tabView_onChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.onTabChange($event));\n    });\n    i0.ɵɵelementStart(2, \"p-tabPanel\", 40);\n    i0.ɵɵtemplate(3, AppAccountListComponent_p_dialog_43_div_3_Template, 52, 23, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, AppAccountListComponent_p_dialog_43_p_tabPanel_4_Template, 5, 17, \"p-tabPanel\", 42);\n    i0.ɵɵtemplate(5, AppAccountListComponent_p_dialog_43_p_tabPanel_5_Template, 5, 17, \"p-tabPanel\", 42);\n    i0.ɵɵtemplate(6, AppAccountListComponent_p_dialog_43_p_tabPanel_6_Template, 29, 47, \"p-tabPanel\", 43);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(12, _c3));\n    i0.ɵɵproperty(\"header\", ctx_r1.tranService.translate(\"global.button.view\"))(\"visible\", ctx_r1.isShowModalDetail)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"header\", ctx_r1.tranService.translate(\"account.label.generalInfo\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.accountResponse);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.accountInfo.userType == ctx_r1.CONSTANTS.USER_TYPE.CUSTOMER);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.accountInfo.userType == ctx_r1.CONSTANTS.USER_TYPE.CUSTOMER);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.genGrantApi.secretKey != null);\n  }\n}\nfunction AppAccountListComponent_div_55_small_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 92);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r42.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction AppAccountListComponent_div_55_div_30_small_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 92);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r44 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r44.tranService.translate(\"global.message.required\"));\n  }\n}\nfunction AppAccountListComponent_div_55_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95);\n    i0.ɵɵelement(1, \"label\", 96);\n    i0.ɵɵelementStart(2, \"div\", 107);\n    i0.ɵɵtemplate(3, AppAccountListComponent_div_55_div_30_small_3_Template, 2, 1, \"small\", 98);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r43 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r43.searchUserCustomerController.dirty && ctx_r43.searchUserCustomerController.error.required);\n  }\n}\nconst _c4 = function () {\n  return {\n    width: \"500px\"\n  };\n};\nfunction AppAccountListComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"p-dialog\", 29);\n    i0.ɵɵlistener(\"visibleChange\", function AppAccountListComponent_div_55_Template_p_dialog_visibleChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r45 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r45.isShowDialogChangeManageData = $event);\n    });\n    i0.ɵɵelementStart(2, \"form\", 90);\n    i0.ɵɵlistener(\"ngSubmit\", function AppAccountListComponent_div_55_Template_form_ngSubmit_2_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.changeManageData());\n    });\n    i0.ɵɵelementStart(3, \"div\", 30)(4, \"label\", 91);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementStart(6, \"span\", 92);\n    i0.ɵɵtext(7, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 93)(9, \"vnpt-select\", 94);\n    i0.ɵɵlistener(\"valueChange\", function AppAccountListComponent_div_55_Template_vnpt_select_valueChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r48 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r48.changeManageDataInfo.userId = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 95);\n    i0.ɵɵelement(11, \"label\", 96);\n    i0.ɵɵelementStart(12, \"div\", 97);\n    i0.ɵɵtemplate(13, AppAccountListComponent_div_55_small_13_Template, 2, 1, \"small\", 98);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 30)(15, \"div\", 32)(16, \"p-radioButton\", 99);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountListComponent_div_55_Template_p_radioButton_ngModelChange_16_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r49 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r49.changeManageDataInfo.typeSelect = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 100);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 32)(20, \"p-radioButton\", 101);\n    i0.ɵɵlistener(\"ngModelChange\", function AppAccountListComponent_div_55_Template_p_radioButton_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r50.changeManageDataInfo.typeSelect = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"label\", 102);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 30)(24, \"label\", 91);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementStart(26, \"span\", 92);\n    i0.ɵɵtext(27, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 97)(29, \"vnpt-select\", 103);\n    i0.ɵɵlistener(\"valueChange\", function AppAccountListComponent_div_55_Template_vnpt_select_valueChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r51 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r51.changeManageDataInfo.accountCustomerIds = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(30, AppAccountListComponent_div_55_div_30_Template, 4, 1, \"div\", 104);\n    i0.ɵɵelementStart(31, \"div\", 34)(32, \"p-button\", 105);\n    i0.ɵɵlistener(\"click\", function AppAccountListComponent_div_55_Template_p_button_click_32_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const ctx_r52 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r52.isShowDialogChangeManageData = false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(33, \"p-button\", 106);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(38, _c4));\n    i0.ɵɵproperty(\"header\", ctx_r2.tranService.translate(\"global.text.changeManageData\"))(\"visible\", ctx_r2.isShowDialogChangeManageData)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.formChangeManageData);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"account.usertype.district\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"control\", ctx_r2.searchUserTellerController)(\"value\", ctx_r2.changeManageDataInfo.userId)(\"required\", true)(\"isAutoComplete\", false)(\"isMultiChoice\", false)(\"paramDefault\", ctx_r2.paramSearchTeller)(\"lazyLoad\", true)(\"listExclude\", ctx_r2.listTellerExcludes);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.searchUserTellerController.dirty && ctx_r2.searchUserTellerController.error.required);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", 0)(\"ngModel\", ctx_r2.changeManageDataInfo.typeSelect);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.tranService.translate(\"account.text.typeSelectAll\"), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 1)(\"ngModel\", ctx_r2.changeManageDataInfo.typeSelect);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.tranService.translate(\"account.text.typeSelectList\"), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r2.changeManageDataInfo.typeSelect == 1 ? \"\" : \"hidden\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.tranService.translate(\"account.usertype.customer\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"control\", ctx_r2.searchUserCustomerController)(\"value\", ctx_r2.changeManageDataInfo.accountCustomerIds)(\"required\", true)(\"isAutoComplete\", false)(\"isMultiChoice\", true)(\"loadData\", ctx_r2.loadListUserCustomerOfTeller.bind(ctx_r2))(\"lazyLoad\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.changeManageDataInfo.typeSelect == 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r2.tranService.translate(\"global.button.cancel\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"label\", ctx_r2.tranService.translate(\"global.button.save\"))(\"disabled\", ctx_r2.formChangeManageData.invalid || ctx_r2.searchUserTellerController.invalid === true || ctx_r2.changeManageDataInfo.typeSelect == 1 && ctx_r2.searchUserCustomerController.invalid === true);\n  }\n}\nconst _c5 = function (a0) {\n  return [a0];\n};\nexport class AppAccountListComponent extends ComponentBase {\n  constructor(accountService, customerService, contractService, formBuilder, injector) {\n    super(injector);\n    this.accountService = accountService;\n    this.customerService = customerService;\n    this.contractService = contractService;\n    this.formBuilder = formBuilder;\n    this.injector = injector;\n    this.isShowDialogChangeManageLevel = false;\n    this.allPermissions = CONSTANTS.PERMISSIONS;\n    this.isShowDialogChangeManageData = false;\n    this.paramSearchTeller = {\n      status: CONSTANTS.USER_STATUS.ACTIVE,\n      type: CONSTANTS.USER_TYPE.DISTRICT,\n      provinceCode: this.sessionService.userInfo.provinceCode\n    };\n    this.listTellerExcludes = [];\n    this.searchUserTellerController = new ComboLazyControl();\n    this.searchUserCustomerController = new ComboLazyControl();\n    this.isShowModalDetail = false;\n    this.isShowSecretKey = true;\n    this.listModule = [];\n    //sẽ lưu lại list api sau khi đã chọn\n    this.selectItemGrantApi = [];\n    this.paramsSearchGrantApi = {\n      api: null,\n      module: null\n    };\n    this.genGrantApi = {\n      clientId: null,\n      secretKey: null\n    };\n    this.statusGrantApi = null;\n    this.userInfo = this.sessionService.userInfo;\n    this.CONSTANTS = CONSTANTS;\n  }\n  ngOnInit() {\n    let me = this;\n    this.optionUserType = CONSTANTS.USER_TYPE;\n    this.userType = this.sessionService.userInfo.type;\n    this.items = [{\n      label: this.tranService.translate(\"global.menu.accountmgmt\")\n    }, {\n      label: this.tranService.translate(\"global.menu.listaccount\")\n    }];\n    this.home = {\n      icon: 'pi pi-home',\n      routerLink: '/'\n    };\n    this.searchInfo = {\n      username: null,\n      type: null,\n      email: null,\n      provinceCode: null,\n      fullName: null,\n      status: null,\n      loggable: null\n    };\n    this.accountInfo = {\n      accountName: null,\n      fullName: null,\n      email: null,\n      phone: null,\n      userType: null,\n      province: null,\n      roles: null,\n      description: null,\n      manager: null,\n      customers: null\n    };\n    if (this.userType == CONSTANTS.USER_TYPE.ADMIN) {\n      this.statusAccounts = [{\n        name: this.tranService.translate(\"account.usertype.admin\"),\n        value: CONSTANTS.USER_TYPE.ADMIN\n      }, {\n        name: this.tranService.translate(\"account.usertype.customer\"),\n        value: CONSTANTS.USER_TYPE.CUSTOMER\n      }, {\n        name: this.tranService.translate(\"account.usertype.province\"),\n        value: CONSTANTS.USER_TYPE.PROVINCE\n      }, {\n        name: this.tranService.translate(\"account.usertype.district\"),\n        value: CONSTANTS.USER_TYPE.DISTRICT\n      }\n      // {name: this.tranService.translate(\"account.usertype.agency\"),value:CONSTANTS.USER_TYPE.AGENCY},\n      ];\n    } else if (this.userType == CONSTANTS.USER_TYPE.PROVINCE) {\n      this.statusAccounts = [{\n        name: this.tranService.translate(\"account.usertype.customer\"),\n        value: CONSTANTS.USER_TYPE.CUSTOMER\n      },\n      // {name: this.tranService.translate(\"account.usertype.province\"),value:CONSTANTS.USER_TYPE.PROVINCE},\n      {\n        name: this.tranService.translate(\"account.usertype.district\"),\n        value: CONSTANTS.USER_TYPE.DISTRICT\n      }\n      // {name: this.tranService.translate(\"account.usertype.agency\"),value:CONSTANTS.USER_TYPE.AGENCY},\n      ];\n    } else if (this.userType == CONSTANTS.USER_TYPE.DISTRICT) {\n      this.statusAccounts = [{\n        name: this.tranService.translate(\"account.usertype.customer\"),\n        value: CONSTANTS.USER_TYPE.CUSTOMER\n      }\n      // {name: this.tranService.translate(\"account.usertype.district\"),value:CONSTANTS.USER_TYPE.DISTRICT},\n      // {name: this.tranService.translate(\"account.usertype.agency\"),value:CONSTANTS.USER_TYPE.AGENCY},\n      ];\n    } else if (this.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\n      this.statusAccounts = [{\n        name: this.tranService.translate(\"account.usertype.customer\"),\n        value: CONSTANTS.USER_TYPE.CUSTOMER\n      }];\n    } else if (this.userType == CONSTANTS.USER_TYPE.AGENCY) {\n      this.statusAccounts = [{\n        name: this.tranService.translate(\"account.usertype.customer\"),\n        value: CONSTANTS.USER_TYPE.AGENCY\n      }];\n    }\n    this.listStatus = [{\n      name: this.tranService.translate(\"account.userstatus.active\"),\n      value: CONSTANTS.USER_STATUS.ACTIVE\n    }, {\n      name: this.tranService.translate(\"account.userstatus.inactive\"),\n      value: CONSTANTS.USER_STATUS.INACTIVE\n    }];\n    this.paginationGrantApi = {\n      page: 0,\n      size: 10,\n      sortBy: \"id,desc\"\n    };\n    this.columnInfoGrantApi = [{\n      name: \"API\",\n      key: \"name\",\n      size: \"30%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: \"Module\",\n      key: \"module\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }];\n    this.dataSetGrantApi = {\n      content: [],\n      total: 0\n    };\n    this.optionTableGrantApi = {\n      hasClearSelected: false,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false\n    };\n    this.formSearchAccount = this.formBuilder.group(this.searchInfo);\n    this.selectItems = [];\n    this.pageNumber = 0;\n    this.pageSize = 10;\n    this.sort = \"createdDate,asc\";\n    this.optionTable = {\n      hasClearSelected: true,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false,\n      action: [{\n        icon: \"pi pi-user-edit\",\n        tooltip: this.tranService.translate(\"global.button.edit\"),\n        func: function (id, item) {\n          me.router.navigate([`/accounts/edit/${id}`]);\n        },\n        funcAppear: function (id, item) {\n          return me.checkPermission([CONSTANTS.PERMISSIONS.ACCOUNT.UPDATE]);\n        }\n      }, {\n        icon: \"pi pi-lock\",\n        tooltip: this.tranService.translate(\"global.button.changeStatus\"),\n        func: function (id, item) {\n          me.messageCommonService.confirm(me.tranService.translate(\"global.message.titleConfirmChangeStatusAccount\"), me.tranService.translate(\"global.message.confirmChangeStatusAccount\"), {\n            ok: () => {\n              me.accountService.changeStatus(id, response => {\n                me.messageCommonService.success(me.tranService.translate(\"global.message.changeStatusSuccess\"));\n                me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n              });\n            },\n            cancel: () => {\n              // me.messageCommonService.error(me.tranService.translate(\"global.message.changeStatusFail\"));\n            }\n          });\n        },\n        funcAppear: function (id, item) {\n          return item.status == CONSTANTS.USER_STATUS.ACTIVE && me.checkPermission([CONSTANTS.PERMISSIONS.ACCOUNT.CHANGE_STATUS]);\n          ;\n        }\n      }, {\n        icon: \"pi pi-lock-open\",\n        tooltip: this.tranService.translate(\"global.button.changeStatus\"),\n        func: function (id, item) {\n          me.messageCommonService.confirm(me.tranService.translate(\"global.message.titleConfirmChangeStatusAccount\"), me.tranService.translate(\"global.message.confirmChangeStatusAccount\"), {\n            ok: () => {\n              me.accountService.changeStatus(id, response => {\n                me.messageCommonService.success(me.tranService.translate(\"global.message.changeStatusSuccess\"));\n                me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n              });\n            },\n            cancel: () => {\n              // me.messageCommonService.error(me.tranService.translate(\"global.message.changeStatusFail\"));\n            }\n          });\n        },\n        funcAppear: function (id, item) {\n          return item.status == CONSTANTS.USER_STATUS.INACTIVE && me.checkPermission([CONSTANTS.PERMISSIONS.ACCOUNT.CHANGE_STATUS]);\n          ;\n        }\n      },\n      // {\n      //     icon: \"pi pi-sync\",\n      //     func: function(id, item){\n      //         me.isShowDialogChangeManageLevel = true;\n      //         me.accountId = parseInt(id);\n      //     },\n      //     funcAppear: function(id, item) {\n      //         return item.isHasChild === true;\n      //     }\n      // },\n      {\n        icon: \"pi pi-sync\",\n        tooltip: this.tranService.translate(\"global.button.changeManageData\"),\n        func: function (id, item) {\n          me.isShowDialogChangeManageData = true;\n          me.changeManageDataInfo = {\n            userId: null,\n            accountCustomerIds: [],\n            typeSelect: 0\n          };\n          me.tellerSelected = item;\n          me.listTellerExcludes = [id];\n          me.paramSearchTeller.provinceCode = item.provinceCode;\n          me.formChangeManageData = me.formBuilder.group(me.changeManageDataInfo);\n          me.searchUserTellerController.reload();\n          me.searchUserCustomerController.reload();\n        },\n        funcAppear: function (id, item) {\n          return item.type == CONSTANTS.USER_TYPE.DISTRICT && item.status == CONSTANTS.USER_STATUS.INACTIVE && me.checkPermission([CONSTANTS.PERMISSIONS.ACCOUNT.CHANGE_MANAGER_DATA]);\n          ;\n        }\n      }, {\n        icon: \"pi pi-trash\",\n        tooltip: this.tranService.translate(\"global.button.delete\"),\n        func: function (id, item) {\n          me.messageCommonService.confirm(me.tranService.translate(\"global.message.titleConfirmDeleteAccount\"), me.tranService.translate(\"global.message.confirmDeleteAccount\"), {\n            ok: () => {\n              me.accountService.deleleUser(id, response => {\n                me.messageCommonService.success(me.tranService.translate(\"global.message.deleteSuccess\"));\n                me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\n              });\n            },\n            cancel: () => {\n              // me.messageCommonService.error(me.tranService.translate(\"global.message.deleteFail\"));\n            }\n          });\n        },\n        funcAppear: function (id, item) {\n          if (item.isRootCustomer !== true && me.checkPermission([CONSTANTS.PERMISSIONS.ACCOUNT.DELETE])) {\n            return true;\n          } else if (item.isRootCustomer == true && item.isHasChild !== true && me.checkPermission([CONSTANTS.PERMISSIONS.ACCOUNT.DELETE])) {\n            return true;\n          } else {\n            return false;\n          }\n        }\n      }]\n    }, this.columns = [{\n      name: this.tranService.translate(\"account.label.username\"),\n      key: \"username\",\n      size: \"250px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      style: {\n        cursor: \"pointer\",\n        color: \"var(--mainColorText)\"\n      },\n      funcClick(id, item) {\n        me.accountId = id;\n        me.getDetail();\n        me.isShowModalDetail = true;\n      }\n    }, {\n      name: this.tranService.translate(\"account.label.fullname\"),\n      key: \"fullName\",\n      size: \"300px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      isShowTooltip: true,\n      funcGetClassname() {\n        return [\"max-w-13rem\", \"text-overflow-ellipsis\", \"inline-block\", \"overflow-hidden\"];\n      }\n    }, {\n      name: this.tranService.translate(\"account.label.userType\"),\n      key: \"type\",\n      size: \"150px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        if (value == CONSTANTS.USER_TYPE.ADMIN) {\n          return me.tranService.translate(\"account.usertype.admin\");\n        } else if (value == CONSTANTS.USER_TYPE.CUSTOMER) {\n          return me.tranService.translate(\"account.usertype.customer\");\n        } else if (value == CONSTANTS.USER_TYPE.PROVINCE) {\n          return me.tranService.translate(\"account.usertype.province\");\n        } else if (value == CONSTANTS.USER_TYPE.DISTRICT) {\n          return me.tranService.translate(\"account.usertype.district\");\n        } else if (value == CONSTANTS.USER_TYPE.AGENCY) {\n          return me.tranService.translate(\"account.usertype.agency\");\n        } else {\n          return \"\";\n        }\n      }\n    }, {\n      name: this.tranService.translate(\"account.label.email\"),\n      key: \"email\",\n      size: \"300px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"account.label.province\"),\n      key: \"provinceCode\",\n      size: \"175px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        if (!me.listProvince) return value;\n        for (let i = 0; i < me.listProvince.length; i++) {\n          if (me.listProvince[i].code == value) {\n            return `${me.listProvince[i].name} (${value})`;\n          }\n        }\n        return \"\";\n      }\n    }, {\n      name: this.tranService.translate(\"account.label.time\"),\n      key: \"createdDate\",\n      size: \"125px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true\n    }, {\n      name: this.tranService.translate(\"account.label.status\"),\n      key: \"status\",\n      size: \"175px\",\n      align: \"left\",\n      isShow: true,\n      isSort: true,\n      funcConvertText(value) {\n        if (value == CONSTANTS.USER_STATUS.ACTIVE) {\n          return me.tranService.translate(\"account.userstatus.active\");\n        } else if (value == CONSTANTS.USER_STATUS.INACTIVE) {\n          return me.tranService.translate(\"account.userstatus.inactive\");\n        } else {\n          return \"\";\n        }\n      }\n    }];\n    this.getListProvince();\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n    this.paramQuickSearchCustomer = {\n      keyword: null,\n      accountRootId: Number(this.accountId)\n    };\n    this.columnInfoCustomer = [{\n      name: this.tranService.translate(\"customer.label.customerCode\"),\n      key: \"code\",\n      size: \"30%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"customer.label.customerName\"),\n      key: \"name\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }];\n    this.dataSetCustomer = {\n      content: [],\n      total: 0\n    };\n    this.paginationCustomer = {\n      page: 0,\n      size: 10,\n      sortBy: \"name,asc;id,asc\"\n    };\n    this.optionTableCustomer = {\n      hasClearSelected: false,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false\n    };\n    this.paramQuickSearchContract = {\n      keyword: null,\n      customerIds: [],\n      accountRootId: -1\n    };\n    this.columnInfoContract = [{\n      name: this.tranService.translate(\"customer.label.customerCode\"),\n      key: \"customerCode\",\n      size: \"30%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"customer.label.customerName\"),\n      key: \"customerName\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }, {\n      name: this.tranService.translate(\"contract.label.contractCode\"),\n      key: \"contractCode\",\n      size: \"50%\",\n      align: \"left\",\n      isShow: true,\n      isSort: false\n    }];\n    this.dataSetContract = {\n      content: [],\n      total: 0\n    };\n    this.paginationContract = {\n      page: 0,\n      size: 10,\n      sortBy: \"customerName,asc;id,asc\"\n    };\n    this.optionTableContract = {\n      hasClearSelected: false,\n      hasShowChoose: false,\n      hasShowIndex: true,\n      hasShowToggleColumn: false\n    };\n  }\n  ngAfterContentChecked() {\n    if (this.isShowDialogChangeManageLevel == false) {\n      this.accountSelected = null;\n    }\n  }\n  onSubmitSearch() {\n    this.pageNumber = 0;\n    this.searchInfo.loggable = true;\n    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\n  }\n  search(page, limit, sort, params) {\n    let me = this;\n    this.pageNumber = page;\n    this.pageSize = limit;\n    this.sort = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    Object.keys(this.searchInfo).forEach(key => {\n      if (this.searchInfo[key] != null) {\n        dataParams[key] = this.searchInfo[key];\n      }\n    });\n    me.messageCommonService.onload();\n    this.accountService.search(dataParams, response => {\n      me.dataSet = {\n        content: response.content,\n        total: response.totalElements\n      };\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  filterListAccount(event) {\n    let valueFilter = event.query;\n    this.listAccounts = [{\n      id: 1,\n      name: \"Tai khoan 1\"\n    }, {\n      id: 2,\n      name: \"Tai khoan 2\"\n    }, {\n      id: 3,\n      name: \"Tai khoan 3\"\n    }, {\n      id: 4,\n      name: \"Tai khoan 4\"\n    }].filter(el => el.name.indexOf(valueFilter) >= 0);\n  }\n  getListProvince() {\n    this.accountService.getListProvince(response => {\n      this.listProvince = response.map(el => {\n        return {\n          ...el,\n          display: `${el.code} - ${el.name}`\n        };\n      });\n    });\n  }\n  checkPermission(permissions) {\n    return this.checkAuthen(permissions);\n  }\n  changeManageLevel() {}\n  loadListUserCustomerOfTeller(params, callback) {\n    if (this.tellerSelected) {\n      let p = {\n        fullname: params.fullName,\n        userManageId: this.tellerSelected.id,\n        page: params.page,\n        size: params.size,\n        sort: params.sort\n      };\n      this.accountService.searchAccountUserOfUser(p, callback);\n    }\n  }\n  changeManageData() {\n    let data = {\n      newUserId: this.changeManageDataInfo.userId,\n      oldUserId: this.tellerSelected.id,\n      accountCustomerIds: this.changeManageDataInfo.typeSelect == 0 ? null : this.changeManageDataInfo.accountCustomerIds\n    };\n    let me = this;\n    me.messageCommonService.onload();\n    this.accountService.changeManageData(data, response => {\n      me.isShowDialogChangeManageData = false;\n      me.messageCommonService.success(me.tranService.translate(\"global.message.success\"));\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  getDetail() {\n    let me = this;\n    me.messageCommonService.onload();\n    this.accountService.getById(Number(me.accountId), response => {\n      me.accountResponse = response;\n      me.accountInfo.accountName = response.username;\n      me.accountInfo.fullName = response.fullName;\n      me.accountInfo.email = response.email;\n      me.accountInfo.description = response.description;\n      me.accountInfo.phone = response.phone;\n      me.accountInfo.province = response.provinceCode;\n      me.accountInfo.userType = response.type;\n      me.getListRole(false);\n      if (me.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\n        me.resetPaginationCustomerAndContract();\n        me.paramQuickSearchCustomer.accountRootId = Number(me.accountId);\n        me.paramQuickSearchContract.customerIds = (me.accountResponse.customers || []).map(customer => customer.customerId);\n        me.paramQuickSearchContract.accountRootId = Number(me.accountId);\n      }\n      me.statusGrantApi = String(response.statusApi);\n      me.selectItemGrantApi = response.listApiId ? response.listApiId.map(el => ({\n        id: el\n      })) : [{\n        id: -99\n      }];\n      me.genGrantApi.secretKey = response.secretId;\n      me.genGrantApi.clientId = response.username;\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  getListRole(isClear) {\n    this.accountService.getListRole(this.accountInfo.userType, response => {\n      this.listRole = response.map(el => {\n        return {\n          id: el.id,\n          name: el.name\n        };\n      });\n      if (isClear) {\n        this.accountInfo.roles = null;\n      } else {\n        this.accountInfo.roles = this.listRole.filter(el => (this.accountResponse.roles || []).includes(el.id));\n      }\n    });\n  }\n  getStringUserStatus(value) {\n    if (value == CONSTANTS.USER_STATUS.ACTIVE) {\n      return this.tranService.translate(\"account.userstatus.active\");\n    } else if (value == CONSTANTS.USER_STATUS.INACTIVE) {\n      return this.tranService.translate(\"account.userstatus.inactive\");\n    } else {\n      return \"\";\n    }\n  }\n  getStringUserType(value) {\n    if (value == CONSTANTS.USER_TYPE.ADMIN) {\n      return this.tranService.translate(\"account.usertype.admin\");\n    } else if (value == CONSTANTS.USER_TYPE.CUSTOMER) {\n      return this.tranService.translate(\"account.usertype.customer\");\n    } else if (value == CONSTANTS.USER_TYPE.PROVINCE) {\n      return this.tranService.translate(\"account.usertype.province\");\n    } else if (value == CONSTANTS.USER_TYPE.DISTRICT) {\n      return this.tranService.translate(\"account.usertype.district\");\n    } else if (value == CONSTANTS.USER_TYPE.AGENCY) {\n      return this.tranService.translate(\"account.usertype.agency\");\n    } else {\n      return \"\";\n    }\n  }\n  onTabChange(event) {\n    const tabName = event.originalEvent.target.innerText;\n    let me = this;\n    if (event && tabName.includes(this.tranService.translate('account.text.grantApi'))) {\n      me.onSearchGrantApi();\n    } else if (event && tabName.includes(this.tranService.translate('global.menu.listbill'))) {\n      me.onSearchContract();\n    } else if (event && tabName.includes(this.tranService.translate('global.menu.listcustomer'))) {\n      me.onSearchCustomer();\n    }\n  }\n  onSearchCustomer(back) {\n    let me = this;\n    if (back) {\n      me.paginationCustomer.page = 0;\n    }\n    me.searchCustomer(me.paginationCustomer.page, me.paginationCustomer.size, me.paginationCustomer.sortBy, me.paramQuickSearchCustomer);\n  }\n  searchCustomer(page, limit, sort, params) {\n    let me = this;\n    this.paginationCustomer.page = page;\n    this.paginationCustomer.size = limit;\n    this.paginationCustomer.sortBy = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    Object.keys(this.paramQuickSearchCustomer).forEach(key => {\n      if (this.paramQuickSearchCustomer[key] != null) {\n        dataParams[key] = this.paramQuickSearchCustomer[key];\n      }\n    });\n    me.messageCommonService.onload();\n    this.customerService.quickSearchCustomer(dataParams, this.paramQuickSearchCustomer, response => {\n      me.dataSetCustomer = {\n        content: response.content,\n        total: response.totalElements\n      };\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n    // console.log(this.selectItemCustomer)\n  }\n\n  onSearchContract(back) {\n    let me = this;\n    if (back) {\n      me.paginationContract.page = 0;\n    }\n    me.searchContract(me.paginationContract.page, me.paginationContract.size, me.paginationContract.sortBy, me.paramQuickSearchContract);\n  }\n  searchContract(page, limit, sort, params) {\n    let me = this;\n    this.paginationContract.page = page;\n    this.paginationContract.size = limit;\n    this.paginationContract.sortBy = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort\n    };\n    // Object.keys(this.paramQuickSearchContract).forEach(key => {\n    //     if(this.paramQuickSearchContract[key] != null){\n    //         dataParams[key] = this.paramQuickSearchContract[key];\n    //     }\n    // })\n    me.messageCommonService.onload();\n    this.contractService.quickSearchContract(dataParams, this.paramQuickSearchContract, response => {\n      me.dataSetContract = {\n        content: response.content,\n        total: response.totalElements\n      };\n      // console.log(response)\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  resetPaginationCustomerAndContract() {\n    this.paginationCustomer = {\n      page: 0,\n      size: 10,\n      sortBy: \"name,asc;id,asc\"\n    };\n    this.paginationContract = {\n      page: 0,\n      size: 10,\n      sortBy: \"customerName,asc;id,asc\"\n    };\n  }\n  searchGrantApi(page, limit, sort, params) {\n    let me = this;\n    this.paginationGrantApi.page = page;\n    this.paginationGrantApi.size = limit;\n    this.paginationGrantApi.sortBy = sort;\n    let dataParams = {\n      page,\n      size: limit,\n      sort,\n      selectedApiIds: this.selectItemGrantApi.map(el => el.id).join(',')\n    };\n    Object.keys(this.paramsSearchGrantApi).forEach(key => {\n      if (this.paramsSearchGrantApi[key] != null) {\n        dataParams[key] = this.paramsSearchGrantApi[key];\n      }\n    });\n    console.log(dataParams);\n    me.messageCommonService.onload();\n    this.accountService.searchGrantApi(dataParams, response => {\n      me.dataSetGrantApi = {\n        content: response.content,\n        total: response.totalElements\n      };\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n    let copyParam = {\n      ...dataParams\n    };\n    copyParam.size = *********;\n    this.accountService.searchGrantApi(copyParam, response => {\n      me.listModule = [...new Set(response.content.map(el => el.module))];\n      me.listModule = me.listModule.map(el => ({\n        name: el,\n        value: el\n      }));\n    }, null, () => {\n      me.messageCommonService.offload();\n    });\n  }\n  generateToken(n) {\n    var chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n    var token = '';\n    for (var i = 0; i < n; i++) {\n      token += chars[Math.floor(Math.random() * chars.length)];\n    }\n    return token;\n  }\n  genToken() {\n    this.genGrantApi.secretKey = this.generateToken(20);\n  }\n  onSearchGrantApi(back) {\n    let me = this;\n    console.log(me.paramsSearchGrantApi);\n    if (back) {\n      me.paginationGrantApi.page = 0;\n    }\n    me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);\n  }\n  static {\n    this.ɵfac = function AppAccountListComponent_Factory(t) {\n      return new (t || AppAccountListComponent)(i0.ɵɵdirectiveInject(AccountService), i0.ɵɵdirectiveInject(i1.CustomerService), i0.ɵɵdirectiveInject(i2.ContractService), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppAccountListComponent,\n      selectors: [[\"app-account-list\"]],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      decls: 56,\n      vars: 63,\n      consts: [[1, \"vnpt\", \"flex\", \"flex-row\", \"justify-content-between\", \"align-items-center\", \"bg-white\", \"p-2\", \"border-round\"], [1, \"\"], [1, \"text-xl\", \"font-bold\", \"mb-1\"], [1, \"max-w-full\", \"col-7\", 3, \"model\", \"home\"], [1, \"col-5\", \"flex\", \"flex-row\", \"justify-content-end\", \"align-items-center\"], [\"styleClass\", \"p-button-info\", \"icon\", \"\", \"routerLinkActive\", \"router-link-active\", 3, \"label\", \"routerLink\", 4, \"ngIf\"], [1, \"pt-3\", \"pb-2\", \"vnpt-field-set\", 3, \"formGroup\", \"ngSubmit\"], [3, \"toggleable\", \"header\"], [1, \"grid\", \"search-grid-4\"], [1, \"col-3\"], [1, \"p-float-label\"], [\"pInputText\", \"\", \"pInputText\", \"\", \"id\", \"username\", \"formControlName\", \"username\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"username\"], [\"pInputText\", \"\", \"pInputText\", \"\", \"id\", \"fullName\", \"formControlName\", \"fullName\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"fullName\"], [\"styleClass\", \"w-full\", \"id\", \"type\", \"formControlName\", \"type\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"showClear\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"ngModelChange\"], [\"for\", \"type\", 1, \"label-dropdown\"], [\"pInputText\", \"\", \"id\", \"email\", \"formControlName\", \"email\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"htmlFor\", \"email\"], [\"styleClass\", \"w-full\", \"filterBy\", \"display\", \"id\", \"provinceCode\", \"formControlName\", \"provinceCode\", \"optionLabel\", \"display\", \"optionValue\", \"code\", 3, \"showClear\", \"filter\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"emptyFilterMessage\", \"ngModelChange\"], [\"htmlFor\", \"provinceCode\", 1, \"label-dropdown\"], [\"styleClass\", \"w-full\", \"filterBy\", \"display\", \"id\", \"status\", \"formControlName\", \"status\", \"optionLabel\", \"name\", \"optionValue\", \"value\", 3, \"showClear\", \"filter\", \"autoDisplayFirst\", \"ngModel\", \"options\", \"emptyFilterMessage\", \"ngModelChange\"], [\"htmlFor\", \"status\", 1, \"label-dropdown\"], [1, \"col-3\", \"pb-0\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"submit\"], [1, \"flex\", \"justify-content-center\", \"dialog-vnpt\"], [3, \"header\", \"visible\", \"modal\", \"style\", \"draggable\", \"resizable\", \"visibleChange\", 4, \"ngIf\"], [3, \"fieldId\", \"selectItems\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"pageNumber\", \"pageSize\", \"sort\", \"params\", \"labelTable\", \"selectItemsChange\"], [1, \"flex\", \"justify-content-center\", \"dialog-push-group\"], [3, \"header\", \"visible\", \"modal\", \"draggable\", \"resizable\", \"visibleChange\"], [1, \"w-full\", \"field\", \"grid\"], [\"htmlFor\", \"account\", 1, \"col-fixed\", 2, \"width\", \"100px\"], [1, \"col\"], [\"styleClass\", \"w-full\", \"id\", \"account\", \"field\", \"name\", 3, \"ngModel\", \"suggestions\", \"dropdown\", \"placeholder\", \"ngModelChange\", \"completeMethod\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"align-items-center\"], [\"styleClass\", \"mr-2 p-button-secondary\", 3, \"label\", \"click\"], [\"styleClass\", \"p-button-info\", 3, \"label\", \"disabled\", \"click\"], [\"class\", \"flex justify-content-center dialog-vnpt\", 4, \"ngIf\"], [\"styleClass\", \"p-button-info\", \"icon\", \"\", \"routerLinkActive\", \"router-link-active\", 3, \"label\", \"routerLink\"], [3, \"onChange\"], [3, \"header\"], [\"class\", \"flex flex-row justify-content-between account-create\", 4, \"ngIf\"], [3, \"header\", 4, \"ngIf\"], [3, \"header\", \"pt\", 4, \"ngIf\"], [1, \"flex\", \"flex-row\", \"justify-content-between\", \"account-create\"], [2, \"width\", \"49%\"], [1, \"w-full\", \"field\", \"grid\", \"dialog\", \"account-info-grid\"], [\"htmlFor\", \"accountName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"w-full\", \"field\", \"grid\", \"account-info-grid\"], [\"htmlFor\", \"fullName\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"htmlFor\", \"fullName\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [1, \"col\", 2, \"width\", \"calc(100% - 180px)\", \"overflow-wrap\", \"break-word\"], [\"htmlFor\", \"phone\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"htmlFor\", \"email\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [\"htmlFor\", \"description\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"for\", \"userType\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [\"class\", \"w-full field grid account-info-grid\", 4, \"ngIf\"], [\"htmlFor\", \"roles\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"col\", 2, \"max-width\", \"calc(100% - 180px) !important\"], [\"class\", \"w-full field grid align-items-start account-info-grid\", 4, \"ngIf\"], [\"htmlFor\", \"roles\", 1, \"col-fixed\", 2, \"width\", \"180px\", \"height\", \"fit-content\"], [4, \"ngFor\", \"ngForOf\"], [\"htmlFor\", \"province\", 1, \"col-fixed\", 2, \"width\", \"180px\"], [1, \"w-full\", \"field\", \"grid\", \"align-items-start\", \"account-info-grid\"], [4, \"ngIf\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"gap-3\", \"mt-4\"], [\"type\", \"text\", \"pInputText\", \"\", 2, \"min-width\", \"35vw\", 3, \"placeholder\", \"ngModel\", \"ngModelOptions\", \"keydown.enter\", \"ngModelChange\"], [\"icon\", \"pi pi-search\", \"styleClass\", \"ml-3 p-button-rounded p-button-secondary p-button-text button-search\", \"type\", \"button\", 3, \"click\"], [3, \"fieldId\", \"pageNumber\", \"pageSize\", \"columns\", \"dataSet\", \"options\", \"loadData\", \"rowsPerPageOptions\", \"scrollHeight\", \"sort\", \"params\"], [3, \"header\", \"pt\"], [1, \"mb-3\"], [3, \"showHeader\"], [1, \"flex\", \"gap-2\"], [\"value\", \"1\", 1, \"p-3\", 3, \"label\", \"ngModel\", \"disabled\", \"ngModelOptions\", \"ngModelChange\"], [\"value\", \"0\", 1, \"p-3\", 3, \"label\", \"ngModel\", \"disabled\", \"ngModelOptions\", \"ngModelChange\"], [1, \"flex\", \"gap-3\", \"align-items-center\", \"api-input-section\"], [1, \"col-6\"], [1, \"flex\", \"align-items-center\"], [1, \"mr-3\", 2, \"min-width\", \"100px\"], [\"type\", \"text\", \"pInputText\", \"\", 1, \"w-full\", 3, \"ngModel\", \"disabled\", \"ngModelOptions\", \"ngModelChange\"], [1, \"w-full\", \"flex\", \"align-items-center\"], [\"pInputText\", \"\", 1, \"w-full\", \"mr-2\", 2, \"padding-right\", \"30px\", 3, \"ngModel\", \"ngModelOptions\", \"type\", \"disabled\", \"ngModelChange\"], [\"style\", \"margin-left: -30px;z-index: 1;\", \"class\", \"pi pi-eye toggle-password\", 3, \"click\", 4, \"ngIf\"], [\"style\", \"margin-left: -30px;z-index: 1;\", \"class\", \"pi pi-eye-slash toggle-password\", 3, \"click\", 4, \"ngIf\"], [1, \"flex\", \"gap-3\", \"align-items-center\", \"module-search\"], [1, \"col-3\", \"dropdown-fit\"], [\"optionLabel\", \"name\", \"optionValue\", \"value\", \"filter\", \"true\", 1, \"w-full\", 3, \"showClear\", \"ngModel\", \"ngModelOptions\", \"options\", \"emptyFilterMessage\", \"placeholder\", \"ngModelChange\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"API\", 1, \"w-full\", \"mr-2\", 3, \"ngModel\", \"ngModelOptions\", \"ngModelChange\"], [1, \"pi\", \"pi-eye\", \"toggle-password\", 2, \"margin-left\", \"-30px\", \"z-index\", \"1\", 3, \"click\"], [1, \"pi\", \"pi-eye-slash\", \"toggle-password\", 2, \"margin-left\", \"-30px\", \"z-index\", \"1\", 3, \"click\"], [3, \"formGroup\", \"ngSubmit\"], [\"htmlFor\", \"customer\", 1, \"col-fixed\", 2, \"width\", \"140px\"], [1, \"text-red-500\"], [1, \"col\", \"dropdown-fit\"], [\"objectKey\", \"account\", \"paramKey\", \"fullName\", \"keyReturn\", \"id\", \"displayPattern\", \"${fullName} - ${email}\", 3, \"control\", \"value\", \"required\", \"isAutoComplete\", \"isMultiChoice\", \"paramDefault\", \"lazyLoad\", \"listExclude\", \"valueChange\"], [1, \"w-full\", \"field\", \"grid\", \"text-error-field\"], [\"htmlFor\", \"province\", 1, \"col-fixed\", 2, \"width\", \"140px\"], [1, \"col\", \"dropdown-fit\", 2, \"width\", \"calc(100% - 140px)\"], [\"class\", \"text-red-500\", 4, \"ngIf\"], [\"name\", \"typeSelect\", \"formControlName\", \"typeSelect\", \"inputId\", \"typeSelectAll\", 3, \"value\", \"ngModel\", \"ngModelChange\"], [\"for\", \"typeSelectAll\", 1, \"ml-2\"], [\"name\", \"typeSelect\", \"formControlName\", \"typeSelect\", \"inputId\", \"typeSelectList\", 3, \"value\", \"ngModel\", \"ngModelChange\"], [\"for\", \"typeSelectList\", 1, \"ml-2\"], [\"paramKey\", \"fullName\", \"keyReturn\", \"id\", \"displayPattern\", \"${fullName} - ${email}\", 3, \"control\", \"value\", \"required\", \"isAutoComplete\", \"isMultiChoice\", \"loadData\", \"lazyLoad\", \"valueChange\"], [\"class\", \"w-full field grid text-error-field\", 4, \"ngIf\"], [\"styleClass\", \"mr-2 p-button-secondary p-button-outlined\", 3, \"label\", \"click\"], [\"styleClass\", \"p-button-info\", \"type\", \"submit\", 3, \"label\", \"disabled\"], [1, \"col\", 2, \"width\", \"calc(100% - 140px)\"]],\n      template: function AppAccountListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"p-breadcrumb\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵtemplate(6, AppAccountListComponent_p_button_6_Template, 1, 3, \"p-button\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"form\", 6);\n          i0.ɵɵlistener(\"ngSubmit\", function AppAccountListComponent_Template_form_ngSubmit_7_listener() {\n            return ctx.onSubmitSearch();\n          });\n          i0.ɵɵelementStart(8, \"p-panel\", 7)(9, \"div\", 8)(10, \"div\", 9)(11, \"span\", 10)(12, \"input\", 11);\n          i0.ɵɵlistener(\"ngModelChange\", function AppAccountListComponent_Template_input_ngModelChange_12_listener($event) {\n            return ctx.searchInfo.username = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"label\", 12);\n          i0.ɵɵtext(14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 9)(16, \"span\", 10)(17, \"input\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function AppAccountListComponent_Template_input_ngModelChange_17_listener($event) {\n            return ctx.searchInfo.fullName = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"label\", 14);\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 9)(21, \"span\", 10)(22, \"p-dropdown\", 15);\n          i0.ɵɵlistener(\"ngModelChange\", function AppAccountListComponent_Template_p_dropdown_ngModelChange_22_listener($event) {\n            return ctx.searchInfo.type = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"label\", 16);\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 9)(26, \"span\", 10)(27, \"input\", 17);\n          i0.ɵɵlistener(\"ngModelChange\", function AppAccountListComponent_Template_input_ngModelChange_27_listener($event) {\n            return ctx.searchInfo.email = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"label\", 18);\n          i0.ɵɵtext(29);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(30, \"div\", 9)(31, \"span\", 10)(32, \"p-dropdown\", 19);\n          i0.ɵɵlistener(\"ngModelChange\", function AppAccountListComponent_Template_p_dropdown_ngModelChange_32_listener($event) {\n            return ctx.searchInfo.provinceCode = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"label\", 20);\n          i0.ɵɵtext(34);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 9)(36, \"span\", 10)(37, \"p-dropdown\", 21);\n          i0.ɵɵlistener(\"ngModelChange\", function AppAccountListComponent_Template_p_dropdown_ngModelChange_37_listener($event) {\n            return ctx.searchInfo.status = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"label\", 22);\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(40, \"div\", 23);\n          i0.ɵɵelement(41, \"p-button\", 24);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(42, \"div\", 25);\n          i0.ɵɵtemplate(43, AppAccountListComponent_p_dialog_43_Template, 7, 13, \"p-dialog\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"table-vnpt\", 27);\n          i0.ɵɵlistener(\"selectItemsChange\", function AppAccountListComponent_Template_table_vnpt_selectItemsChange_44_listener($event) {\n            return ctx.selectItems = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"div\", 28)(46, \"p-dialog\", 29);\n          i0.ɵɵlistener(\"visibleChange\", function AppAccountListComponent_Template_p_dialog_visibleChange_46_listener($event) {\n            return ctx.isShowDialogChangeManageLevel = $event;\n          });\n          i0.ɵɵelementStart(47, \"div\", 30)(48, \"label\", 31);\n          i0.ɵɵtext(49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"div\", 32)(51, \"p-autoComplete\", 33);\n          i0.ɵɵlistener(\"ngModelChange\", function AppAccountListComponent_Template_p_autoComplete_ngModelChange_51_listener($event) {\n            return ctx.accountSelected = $event;\n          })(\"completeMethod\", function AppAccountListComponent_Template_p_autoComplete_completeMethod_51_listener($event) {\n            return ctx.filterListAccount($event);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(52, \"div\", 34)(53, \"p-button\", 35);\n          i0.ɵɵlistener(\"click\", function AppAccountListComponent_Template_p_button_click_53_listener() {\n            return ctx.isShowDialogChangeManageLevel = false;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"p-button\", 36);\n          i0.ɵɵlistener(\"click\", function AppAccountListComponent_Template_p_button_click_54_listener() {\n            return ctx.changeManageLevel();\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(55, AppAccountListComponent_div_55_Template, 34, 39, \"div\", 37);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"global.menu.listaccount\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"model\", ctx.items)(\"home\", ctx.home);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.checkPermission(i0.ɵɵpureFunction1(60, _c5, ctx.allPermissions.ACCOUNT.CREATE)));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.formSearchAccount);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"toggleable\", true)(\"header\", ctx.tranService.translate(\"global.text.filter\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.username);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.label.username\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.fullName);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.label.fullname\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"showClear\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.searchInfo.type)(\"options\", ctx.statusAccounts);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.label.userType\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchInfo.email);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.label.email\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"showClear\", true)(\"filter\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.searchInfo.provinceCode)(\"options\", ctx.listProvince)(\"emptyFilterMessage\", ctx.tranService.translate(\"global.text.nodata\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.label.province\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"showClear\", true)(\"filter\", true)(\"autoDisplayFirst\", false)(\"ngModel\", ctx.searchInfo.status)(\"options\", ctx.listStatus)(\"emptyFilterMessage\", ctx.tranService.translate(\"account.label.status\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.label.status\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isShowModalDetail);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"fieldId\", \"id\")(\"selectItems\", ctx.selectItems)(\"columns\", ctx.columns)(\"dataSet\", ctx.dataSet)(\"options\", ctx.optionTable)(\"loadData\", ctx.search.bind(ctx))(\"pageNumber\", ctx.pageNumber)(\"pageSize\", ctx.pageSize)(\"sort\", ctx.sort)(\"params\", ctx.searchInfo)(\"labelTable\", ctx.tranService.translate(\"global.menu.listaccount\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(62, _c4));\n          i0.ɵɵproperty(\"header\", ctx.tranService.translate(\"account.text.titleChangeManageLevel\"))(\"visible\", ctx.isShowDialogChangeManageLevel)(\"modal\", true)(\"draggable\", false)(\"resizable\", false);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.tranService.translate(\"account.text.account\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.accountSelected)(\"suggestions\", ctx.listAccounts)(\"dropdown\", true)(\"placeholder\", ctx.tranService.translate(\"account.text.selectAccount\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.cancel\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"label\", ctx.tranService.translate(\"global.button.save\"))(\"disabled\", ctx.accountSelected == null || ctx.accountSelected == undefined);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.formChangeManageData);\n        }\n      },\n      dependencies: [i4.RouterLink, i4.RouterLinkActive, i5.NgForOf, i5.NgIf, i6.Breadcrumb, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.NgModel, i3.FormGroupDirective, i3.FormControlName, i7.InputText, i8.Button, i9.TableVnptComponent, i10.VnptCombobox, i11.AutoComplete, i12.Dropdown, i13.Dialog, i14.Panel, i15.RadioButton, i16.TabView, i16.TabPanel]\n    });\n  }\n}", "map": {"version": 3, "names": ["AccountService", "CONSTANTS", "ComponentBase", "ComboLazyControl", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "tranService", "translate", "ɵɵpureFunction0", "_c0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r7", "ɵɵtextInterpolate2", "accountResponse", "provinceName", "provinceCode", "item_r12", "username", "ɵɵtemplate", "AppAccountListComponent_p_dialog_43_div_3_div_45_div_4_div_1_Template", "isRootCustomer", "AppAccountListComponent_p_dialog_43_div_3_div_45_div_4_Template", "ctx_r8", "userManages", "ɵɵtextInterpolate1", "ctx_r15", "rootAccount", "AppAccountListComponent_p_dialog_43_div_3_div_46_div_4_Template", "ctx_r9", "item_r16", "<PERSON><PERSON><PERSON>", "AppAccountListComponent_p_dialog_43_div_3_div_39_Template", "AppAccountListComponent_p_dialog_43_div_3_div_45_Template", "AppAccountListComponent_p_dialog_43_div_3_div_46_Template", "AppAccountListComponent_p_dialog_43_div_3_div_51_Template", "ctx_r3", "getStringUserStatus", "status", "fullName", "phone", "email", "description", "getStringUserType", "type", "accountInfo", "userType", "optionUserType", "ADMIN", "AGENCY", "ɵɵclassMap", "CUSTOMER", "PROVINCE", "manager", "DISTRICT", "roles", "ɵɵlistener", "AppAccountListComponent_p_dialog_43_p_tabPanel_4_Template_input_keydown_enter_2_listener", "ɵɵrestoreView", "_r18", "ctx_r17", "ɵɵnextContext", "ɵɵresetView", "onSearchCustomer", "AppAccountListComponent_p_dialog_43_p_tabPanel_4_Template_input_ngModelChange_2_listener", "$event", "ctx_r19", "paramQuickSearchCustomer", "keyword", "AppAccountListComponent_p_dialog_43_p_tabPanel_4_Template_p_button_click_3_listener", "ctx_r20", "ɵɵpropertyInterpolate", "ctx_r4", "_c1", "paginationCustomer", "page", "size", "columnInfoCustomer", "dataSetCustomer", "optionTableCustomer", "searchCustomer", "bind", "_c2", "sortBy", "AppAccountListComponent_p_dialog_43_p_tabPanel_5_Template_input_keydown_enter_2_listener", "_r22", "ctx_r21", "onSearchContract", "AppAccountListComponent_p_dialog_43_p_tabPanel_5_Template_input_ngModelChange_2_listener", "ctx_r23", "paramQuickSearchContract", "AppAccountListComponent_p_dialog_43_p_tabPanel_5_Template_p_button_click_3_listener", "ctx_r24", "ctx_r5", "paginationContract", "columnInfoContract", "dataSetContract", "optionTableContract", "searchContract", "AppAccountListComponent_p_dialog_43_p_tabPanel_6_label_18_Template_label_click_0_listener", "_r28", "ctx_r27", "isShowSecretKey", "AppAccountListComponent_p_dialog_43_p_tabPanel_6_label_19_Template_label_click_0_listener", "_r30", "ctx_r29", "AppAccountListComponent_p_dialog_43_p_tabPanel_6_Template_p_radioButton_ngModelChange_4_listener", "_r32", "ctx_r31", "statusGrantApi", "AppAccountListComponent_p_dialog_43_p_tabPanel_6_Template_p_radioButton_ngModelChange_5_listener", "ctx_r33", "AppAccountListComponent_p_dialog_43_p_tabPanel_6_Template_input_ngModelChange_11_listener", "ctx_r34", "genGrant<PERSON>pi", "clientId", "AppAccountListComponent_p_dialog_43_p_tabPanel_6_Template_input_ngModelChange_17_listener", "ctx_r35", "secret<PERSON>ey", "AppAccountListComponent_p_dialog_43_p_tabPanel_6_label_18_Template", "AppAccountListComponent_p_dialog_43_p_tabPanel_6_label_19_Template", "AppAccountListComponent_p_dialog_43_p_tabPanel_6_Template_p_dropdown_ngModelChange_24_listener", "ctx_r36", "paramsSearchGrantApi", "module", "AppAccountListComponent_p_dialog_43_p_tabPanel_6_Template_input_ngModelChange_26_listener", "ctx_r37", "api", "AppAccountListComponent_p_dialog_43_p_tabPanel_6_Template_p_button_click_27_listener", "ctx_r38", "onSearchGrantApi", "ctx_r6", "listModule", "paginationGrantApi", "columnInfoGrantApi", "dataSetGrantApi", "optionTableGrantApi", "searchGrantApi", "AppAccountListComponent_p_dialog_43_Template_p_dialog_visibleChange_0_listener", "_r40", "ctx_r39", "isShowModalDetail", "AppAccountListComponent_p_dialog_43_Template_p_tabView_onChange_1_listener", "ctx_r41", "onTabChange", "AppAccountListComponent_p_dialog_43_div_3_Template", "AppAccountListComponent_p_dialog_43_p_tabPanel_4_Template", "AppAccountListComponent_p_dialog_43_p_tabPanel_5_Template", "AppAccountListComponent_p_dialog_43_p_tabPanel_6_Template", "ɵɵstyleMap", "_c3", "ctx_r1", "USER_TYPE", "ctx_r42", "ctx_r44", "AppAccountListComponent_div_55_div_30_small_3_Template", "ctx_r43", "searchUserCustomerController", "dirty", "error", "required", "AppAccountListComponent_div_55_Template_p_dialog_visibleChange_1_listener", "_r46", "ctx_r45", "isShowDialogChangeManageData", "AppAccountListComponent_div_55_Template_form_ngSubmit_2_listener", "ctx_r47", "changeManageData", "AppAccountListComponent_div_55_Template_vnpt_select_valueChange_9_listener", "ctx_r48", "changeManageDataInfo", "userId", "AppAccountListComponent_div_55_small_13_Template", "AppAccountListComponent_div_55_Template_p_radioButton_ngModelChange_16_listener", "ctx_r49", "typeSelect", "AppAccountListComponent_div_55_Template_p_radioButton_ngModelChange_20_listener", "ctx_r50", "AppAccountListComponent_div_55_Template_vnpt_select_valueChange_29_listener", "ctx_r51", "accountCustomerIds", "AppAccountListComponent_div_55_div_30_Template", "AppAccountListComponent_div_55_Template_p_button_click_32_listener", "ctx_r52", "_c4", "ctx_r2", "formChangeManageData", "searchUserTellerController", "paramSearchTeller", "listTellerExcludes", "loadListUserCustomerOfTeller", "invalid", "AppAccountListComponent", "constructor", "accountService", "customerService", "contractService", "formBuilder", "injector", "isShowDialogChangeManageLevel", "allPermissions", "PERMISSIONS", "USER_STATUS", "ACTIVE", "sessionService", "userInfo", "selectItemGrantApi", "ngOnInit", "me", "items", "label", "home", "icon", "routerLink", "searchInfo", "loggable", "accountName", "province", "customers", "statusAccounts", "name", "value", "listStatus", "INACTIVE", "key", "align", "isShow", "isSort", "content", "total", "hasClearSelected", "hasShowChoose", "hasShowIndex", "hasShowToggleColumn", "formSearchAccount", "group", "selectItems", "pageNumber", "pageSize", "sort", "optionTable", "action", "tooltip", "func", "id", "item", "router", "navigate", "funcAppear", "checkPermission", "ACCOUNT", "UPDATE", "messageCommonService", "confirm", "ok", "changeStatus", "response", "success", "search", "cancel", "CHANGE_STATUS", "tellerSelected", "reload", "CHANGE_MANAGER_DATA", "deleleUser", "DELETE", "isHas<PERSON><PERSON>d", "columns", "style", "cursor", "color", "funcClick", "accountId", "getDetail", "isShowTooltip", "funcGetClassname", "funcConvertText", "listProvince", "i", "length", "code", "getListProvince", "accountRootId", "Number", "customerIds", "ngAfterContentChecked", "accountSelected", "onSubmitSearch", "limit", "params", "dataParams", "Object", "keys", "for<PERSON>ach", "onload", "dataSet", "totalElements", "offload", "filterListAccount", "event", "valueFilter", "query", "listAccounts", "filter", "el", "indexOf", "map", "display", "permissions", "<PERSON><PERSON><PERSON><PERSON>", "changeManageLevel", "callback", "p", "fullname", "userManageId", "searchAccountUserOfUser", "data", "newUserId", "oldUserId", "getById", "getListRole", "resetPaginationCustomerAndContract", "customer", "customerId", "String", "statusApi", "listApiId", "secretId", "isClear", "listRole", "includes", "tabName", "originalEvent", "target", "innerText", "back", "quickSearchCustomer", "quickSearchContract", "selectedApiIds", "join", "console", "log", "copyParam", "Set", "generateToken", "n", "chars", "token", "Math", "floor", "random", "genToken", "ɵɵdirectiveInject", "i1", "CustomerService", "i2", "ContractService", "i3", "FormBuilder", "Injector", "selectors", "features", "ɵɵInheritDefinitionFeature", "decls", "vars", "consts", "template", "AppAccountListComponent_Template", "rf", "ctx", "AppAccountListComponent_p_button_6_Template", "AppAccountListComponent_Template_form_ngSubmit_7_listener", "AppAccountListComponent_Template_input_ngModelChange_12_listener", "AppAccountListComponent_Template_input_ngModelChange_17_listener", "AppAccountListComponent_Template_p_dropdown_ngModelChange_22_listener", "AppAccountListComponent_Template_input_ngModelChange_27_listener", "AppAccountListComponent_Template_p_dropdown_ngModelChange_32_listener", "AppAccountListComponent_Template_p_dropdown_ngModelChange_37_listener", "AppAccountListComponent_p_dialog_43_Template", "AppAccountListComponent_Template_table_vnpt_selectItemsChange_44_listener", "AppAccountListComponent_Template_p_dialog_visibleChange_46_listener", "AppAccountListComponent_Template_p_autoComplete_ngModelChange_51_listener", "AppAccountListComponent_Template_p_autoComplete_completeMethod_51_listener", "AppAccountListComponent_Template_p_button_click_53_listener", "AppAccountListComponent_Template_p_button_click_54_listener", "AppAccountListComponent_div_55_Template", "ɵɵpureFunction1", "_c5", "CREATE", "undefined"], "sources": ["C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\account-management\\list\\app.account.list.component.ts", "C:\\Users\\<USER>\\Desktop\\cmp\\cmp-web-app\\src\\app\\template\\account-management\\list\\app.account.list.component.html"], "sourcesContent": ["import { AfterContentChecked, Component, Inject, Injector, OnInit } from \"@angular/core\";\r\nimport { FormBuilder } from \"@angular/forms\";\r\nimport { MenuItem } from \"primeng/api\";\r\nimport { AccountService } from \"src/app/service/account/AccountService\";\r\nimport { ColumnInfo, OptionTable } from \"../../common-module/table/table.component\";\r\nimport { CONSTANTS } from \"src/app/service/comon/constants\";\r\nimport { AutoCompleteCompleteEvent } from \"primeng/autocomplete\";\r\nimport { ComponentBase } from \"src/app/component.base\";\r\nimport { ComboLazyControl } from \"../../common-module/combobox-lazyload/combobox.lazyload\";\r\nimport { CustomerService } from \"src/app/service/customer/CustomerService\";\r\nimport {ContractService} from \"../../../service/contract/ContractService\";\r\n@Component({\r\n    selector: \"app-account-list\",\r\n    templateUrl: './app.account.list.component.html'\r\n})\r\nexport class AppAccountListComponent extends ComponentBase implements OnInit, AfterContentChecked{\r\n    userType: number;\r\n    searchInfo: {\r\n        username: string | null,\r\n        fullName: string | null,\r\n        type: number | null,\r\n        email: string | null,\r\n        provinceCode: object | null,\r\n        status: number | null,\r\n        loggable : boolean | null,\r\n    }\r\n    statusAccounts: Array<any>;\r\n    items: MenuItem[];\r\n    home: MenuItem;\r\n    selectItems: Array<any>;\r\n    columns: Array<ColumnInfo>;\r\n    dataSet: {\r\n        content: Array<any>,\r\n        total: number\r\n    };\r\n    optionTable: OptionTable;\r\n    pageNumber: number;\r\n    pageSize: number;\r\n    sort: string;\r\n    isShowDialogChangeManageLevel: boolean = false;\r\n    accountId: number | string;\r\n    listAccounts: Array<any>;\r\n    accountSelected: any;\r\n    listProvince: Array<any>;\r\n    listStatus: Array<any>;\r\n    allPermissions = CONSTANTS.PERMISSIONS;\r\n\r\n    isShowDialogChangeManageData: boolean = false;\r\n    formChangeManageData: any;\r\n    changeManageDataInfo: {\r\n        userId: number | null,\r\n        accountCustomerIds: Array<any> | null,\r\n        typeSelect: 0 | 1\r\n    };\r\n    paramSearchTeller: {\r\n        type: number | null\r\n        provinceCode: string | null,\r\n        status: number | null\r\n    } = {\r\n        status: CONSTANTS.USER_STATUS.ACTIVE,\r\n        type: CONSTANTS.USER_TYPE.DISTRICT,\r\n        provinceCode: this.sessionService.userInfo.provinceCode\r\n    }\r\n    tellerSelected: any;\r\n    listTellerExcludes: Array<any> | null = [];\r\n    searchUserTellerController: ComboLazyControl = new ComboLazyControl();\r\n    searchUserCustomerController: ComboLazyControl = new ComboLazyControl();\r\n    isShowModalDetail: boolean = false;\r\n    accountResponse: any;\r\n    accountInfo: {\r\n        accountName: string| null,\r\n        fullName: string|null,\r\n        email: string|null,\r\n        phone: string|null,\r\n        userType: number| null,\r\n        province: any,\r\n        roles: Array<any>,\r\n        description: string|null,\r\n        manager: any,\r\n        customers: Array<any>\r\n    };\r\n    optionUserType: any;\r\n    listRole: Array<any>;\r\n    constructor(@Inject(AccountService) private accountService: AccountService,\r\n                private customerService: CustomerService,\r\n                private contractService: ContractService,\r\n                private formBuilder: FormBuilder,\r\n                private injector: Injector) {\r\n        super(injector);\r\n    }\r\n    formSearchAccount: any;\r\n    paramQuickSearchCustomer: {\r\n        keyword: string|null,\r\n        accountRootId: number| null,\r\n    }\r\n    columnInfoCustomer: Array<ColumnInfo>;\r\n    optionTableCustomer: OptionTable;\r\n    dataSetCustomer: {\r\n        content: Array<any>,\r\n        total: number,\r\n    }\r\n    paginationCustomer: {\r\n        page: number|null,\r\n        size: number|null,\r\n        sortBy: string|null,\r\n    }\r\n    paramQuickSearchContract: {\r\n        keyword: string|null,\r\n        customerIds: Array<{ id: number }>|null,\r\n        //search contract theo id này\r\n        accountRootId: number| null,\r\n    }\r\n    columnInfoContract: Array<ColumnInfo>;\r\n    optionTableContract: OptionTable;\r\n    dataSetContract: {\r\n        content: Array<any>,\r\n        total: number,\r\n    }\r\n    paginationContract: {\r\n        page: number|null,\r\n        size: number|null,\r\n        sortBy: string|null,\r\n    }\r\n\r\n    isShowSecretKey = true\r\n    listModule = []\r\n    //sẽ lưu lại list api sau khi đã chọn\r\n    selectItemGrantApi: Array<any> = []\r\n    paginationGrantApi: {\r\n        page: number|null,\r\n        size: number|null,\r\n        sortBy: string|null,\r\n    }\r\n    columnInfoGrantApi: Array<ColumnInfo>;\r\n\r\n    dataSetGrantApi: {\r\n        content: Array<any>,\r\n        total: number,\r\n    }\r\n    optionTableGrantApi: OptionTable;\r\n\r\n    paramsSearchGrantApi = {api : null, module : null}\r\n\r\n    genGrantApi = {clientId: null, secretKey: null}\r\n\r\n    statusGrantApi : any = null;\r\n\r\n    userInfo = this.sessionService.userInfo;\r\n    ngOnInit(): void {\r\n        let me = this;\r\n        this.optionUserType = CONSTANTS.USER_TYPE;\r\n        this.userType = this.sessionService.userInfo.type;\r\n        this.items = [{ label: this.tranService.translate(\"global.menu.accountmgmt\") }, { label: this.tranService.translate(\"global.menu.listaccount\") }];\r\n        this.home = { icon: 'pi pi-home', routerLink: '/' };\r\n        this.searchInfo = {\r\n            username: null,\r\n            type: null,\r\n            email: null,\r\n            provinceCode: null,\r\n            fullName: null,\r\n            status: null,\r\n            loggable : null\r\n        }\r\n        this.accountInfo = {\r\n            accountName: null,\r\n            fullName: null,\r\n            email: null,\r\n            phone: null,\r\n            userType: null,\r\n            province: null,\r\n            roles: null,\r\n            description: null,\r\n            manager: null,\r\n            customers: null\r\n        };\r\n        if(this.userType == CONSTANTS.USER_TYPE.ADMIN){\r\n            this.statusAccounts = [\r\n                {name: this.tranService.translate(\"account.usertype.admin\"),value:CONSTANTS.USER_TYPE.ADMIN},\r\n                {name: this.tranService.translate(\"account.usertype.customer\"),value:CONSTANTS.USER_TYPE.CUSTOMER},\r\n                {name: this.tranService.translate(\"account.usertype.province\"),value:CONSTANTS.USER_TYPE.PROVINCE},\r\n                {name: this.tranService.translate(\"account.usertype.district\"),value:CONSTANTS.USER_TYPE.DISTRICT},\r\n                // {name: this.tranService.translate(\"account.usertype.agency\"),value:CONSTANTS.USER_TYPE.AGENCY},\r\n            ]\r\n        }else if(this.userType == CONSTANTS.USER_TYPE.PROVINCE){\r\n            this.statusAccounts = [\r\n                {name: this.tranService.translate(\"account.usertype.customer\"),value:CONSTANTS.USER_TYPE.CUSTOMER},\r\n                // {name: this.tranService.translate(\"account.usertype.province\"),value:CONSTANTS.USER_TYPE.PROVINCE},\r\n                {name: this.tranService.translate(\"account.usertype.district\"),value:CONSTANTS.USER_TYPE.DISTRICT},\r\n                // {name: this.tranService.translate(\"account.usertype.agency\"),value:CONSTANTS.USER_TYPE.AGENCY},\r\n            ]\r\n        }else if(this.userType == CONSTANTS.USER_TYPE.DISTRICT){\r\n            this.statusAccounts = [\r\n                {name: this.tranService.translate(\"account.usertype.customer\"),value:CONSTANTS.USER_TYPE.CUSTOMER},\r\n                // {name: this.tranService.translate(\"account.usertype.district\"),value:CONSTANTS.USER_TYPE.DISTRICT},\r\n                // {name: this.tranService.translate(\"account.usertype.agency\"),value:CONSTANTS.USER_TYPE.AGENCY},\r\n            ]\r\n        }else if(this.userType == CONSTANTS.USER_TYPE.CUSTOMER){\r\n            this.statusAccounts = [\r\n                {name: this.tranService.translate(\"account.usertype.customer\"),value:CONSTANTS.USER_TYPE.CUSTOMER},\r\n            ]\r\n        }else if(this.userType == CONSTANTS.USER_TYPE.AGENCY){\r\n            this.statusAccounts = [\r\n                {name: this.tranService.translate(\"account.usertype.customer\"),value:CONSTANTS.USER_TYPE.AGENCY},\r\n            ]\r\n        }\r\n        this.listStatus = [\r\n            {name: this.tranService.translate(\"account.userstatus.active\"),value:CONSTANTS.USER_STATUS.ACTIVE },\r\n            {name: this.tranService.translate(\"account.userstatus.inactive\"),value:CONSTANTS.USER_STATUS.INACTIVE }\r\n        ]\r\n        this.paginationGrantApi = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"id,desc\",\r\n        }\r\n        this.columnInfoGrantApi = [\r\n            {\r\n                name: \"API\",\r\n                key: \"name\",\r\n                size: \"30%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n            },\r\n            {\r\n                name: \"Module\",\r\n                key: \"module\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n            }\r\n        ]\r\n\r\n        this.dataSetGrantApi = {\r\n            content: [],\r\n            total: 0,\r\n        }\r\n\r\n        this.optionTableGrantApi = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n        }\r\n        this.formSearchAccount = this.formBuilder.group(this.searchInfo);\r\n        this.selectItems = [];\r\n        this.pageNumber = 0;\r\n        this.pageSize = 10;\r\n        this.sort = \"createdDate,asc\";\r\n\r\n        this.optionTable = {\r\n            hasClearSelected: true,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n            action: [\r\n                {\r\n                    icon: \"pi pi-user-edit\",\r\n                    tooltip: this.tranService.translate(\"global.button.edit\"),\r\n                    func: function(id, item){\r\n                        me.router.navigate([`/accounts/edit/${id}`]);\r\n                    },\r\n                    funcAppear: function(id, item) {\r\n                        return me.checkPermission([CONSTANTS.PERMISSIONS.ACCOUNT.UPDATE]);\r\n                    }\r\n                },\r\n                {\r\n                    icon: \"pi pi-lock\",\r\n                    tooltip: this.tranService.translate(\"global.button.changeStatus\"),\r\n                    func: function(id, item){\r\n                        me.messageCommonService.confirm(\r\n                            me.tranService.translate(\"global.message.titleConfirmChangeStatusAccount\"),\r\n                            me.tranService.translate(\"global.message.confirmChangeStatusAccount\"),\r\n                            {\r\n                                ok:()=>{\r\n                                    me.accountService.changeStatus(id, (response)=>{\r\n                                        me.messageCommonService.success(me.tranService.translate(\"global.message.changeStatusSuccess\"));\r\n                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\r\n                                    })\r\n                                },\r\n                                cancel: ()=>{\r\n                                    // me.messageCommonService.error(me.tranService.translate(\"global.message.changeStatusFail\"));\r\n                                }\r\n                            }\r\n                        )\r\n                    },\r\n                    funcAppear: function(id, item) {\r\n                        return item.status == CONSTANTS.USER_STATUS.ACTIVE && me.checkPermission([CONSTANTS.PERMISSIONS.ACCOUNT.CHANGE_STATUS]);;\r\n                    }\r\n                },\r\n                {\r\n                    icon: \"pi pi-lock-open\",\r\n                    tooltip: this.tranService.translate(\"global.button.changeStatus\"),\r\n                    func: function(id, item){\r\n                        me.messageCommonService.confirm(\r\n                            me.tranService.translate(\"global.message.titleConfirmChangeStatusAccount\"),\r\n                            me.tranService.translate(\"global.message.confirmChangeStatusAccount\"),\r\n                            {\r\n                                ok:()=>{\r\n                                    me.accountService.changeStatus(id, (response)=>{\r\n                                        me.messageCommonService.success(me.tranService.translate(\"global.message.changeStatusSuccess\"));\r\n                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\r\n                                    })\r\n                                },\r\n                                cancel: ()=>{\r\n                                    // me.messageCommonService.error(me.tranService.translate(\"global.message.changeStatusFail\"));\r\n                                }\r\n                            }\r\n                        )\r\n                    },\r\n                    funcAppear: function(id, item) {\r\n                        return item.status == CONSTANTS.USER_STATUS.INACTIVE && me.checkPermission([CONSTANTS.PERMISSIONS.ACCOUNT.CHANGE_STATUS]);;\r\n                    }\r\n                },\r\n                // {\r\n                //     icon: \"pi pi-sync\",\r\n                //     func: function(id, item){\r\n                //         me.isShowDialogChangeManageLevel = true;\r\n                //         me.accountId = parseInt(id);\r\n                //     },\r\n                //     funcAppear: function(id, item) {\r\n                //         return item.isHasChild === true;\r\n                //     }\r\n                // },\r\n                {\r\n                    icon: \"pi pi-sync\",\r\n                    tooltip: this.tranService.translate(\"global.button.changeManageData\"),\r\n                    func: function(id, item){\r\n                        me.isShowDialogChangeManageData = true;\r\n                        me.changeManageDataInfo = {\r\n                            userId: null,\r\n                            accountCustomerIds: [],\r\n                            typeSelect: 0\r\n                        }\r\n                        me.tellerSelected = item;\r\n                        me.listTellerExcludes = [id];\r\n                        me.paramSearchTeller.provinceCode = item.provinceCode;\r\n                        me.formChangeManageData = me.formBuilder.group(me.changeManageDataInfo);\r\n                        me.searchUserTellerController.reload();\r\n                        me.searchUserCustomerController.reload();\r\n                    },\r\n                    funcAppear: function(id, item) {\r\n                        return item.type == CONSTANTS.USER_TYPE.DISTRICT && item.status == CONSTANTS.USER_STATUS.INACTIVE && me.checkPermission([CONSTANTS.PERMISSIONS.ACCOUNT.CHANGE_MANAGER_DATA]);;\r\n                    }\r\n                },\r\n                {\r\n                    icon: \"pi pi-trash\",\r\n                    tooltip: this.tranService.translate(\"global.button.delete\"),\r\n                    func: function(id, item){\r\n                        me.messageCommonService.confirm(\r\n                            me.tranService.translate(\"global.message.titleConfirmDeleteAccount\"),\r\n                            me.tranService.translate(\"global.message.confirmDeleteAccount\"),\r\n                            {\r\n                                ok:()=>{\r\n                                    me.accountService.deleleUser(id, (response)=>{\r\n                                        me.messageCommonService.success(me.tranService.translate(\"global.message.deleteSuccess\"));\r\n                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);\r\n                                    })\r\n                                },\r\n                                cancel: ()=>{\r\n                                    // me.messageCommonService.error(me.tranService.translate(\"global.message.deleteFail\"));\r\n                                }\r\n                            }\r\n                        )\r\n                    },\r\n                    funcAppear: function(id, item) {\r\n                        if (item.isRootCustomer !== true && me.checkPermission([CONSTANTS.PERMISSIONS.ACCOUNT.DELETE])) {\r\n                            return true;\r\n                        } else if (item.isRootCustomer == true && item.isHasChild !== true && me.checkPermission([CONSTANTS.PERMISSIONS.ACCOUNT.DELETE])) {\r\n                            return true;\r\n                        } else {\r\n                            return false;\r\n                        }\r\n                    }\r\n                },\r\n            ]\r\n        },\r\n        this.columns = [\r\n            {\r\n                name: this.tranService.translate(\"account.label.username\"),\r\n                key: \"username\",\r\n                size: \"250px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                style:{\r\n                    cursor: \"pointer\",\r\n             color: \"var(--mainColorText)\"\r\n                },\r\n                funcClick(id, item) {\r\n                    me.accountId = id;\r\n                    me.getDetail();\r\n                    me.isShowModalDetail = true;\r\n                },\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"account.label.fullname\"),\r\n                key: \"fullName\",\r\n                size: \"300px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                isShowTooltip:true,\r\n                funcGetClassname(){\r\n                    return [\"max-w-13rem\", \"text-overflow-ellipsis\", \"inline-block\", \"overflow-hidden\"]\r\n                }\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"account.label.userType\"),\r\n                key: \"type\",\r\n                size: \"150px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcConvertText(value) {\r\n                    if(value == CONSTANTS.USER_TYPE.ADMIN){\r\n                        return me.tranService.translate(\"account.usertype.admin\");\r\n                    }else if(value == CONSTANTS.USER_TYPE.CUSTOMER){\r\n                        return me.tranService.translate(\"account.usertype.customer\");\r\n                    }else if(value == CONSTANTS.USER_TYPE.PROVINCE){\r\n                        return me.tranService.translate(\"account.usertype.province\");\r\n                    }else if(value == CONSTANTS.USER_TYPE.DISTRICT){\r\n                        return me.tranService.translate(\"account.usertype.district\");\r\n                    }else if(value == CONSTANTS.USER_TYPE.AGENCY){\r\n                        return me.tranService.translate(\"account.usertype.agency\");\r\n                    }else{\r\n                        return \"\";\r\n                    }\r\n                },\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"account.label.email\"),\r\n                key: \"email\",\r\n                size: \"300px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"account.label.province\"),\r\n                key: \"provinceCode\",\r\n                size: \"175px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcConvertText(value) {\r\n                    if(!me.listProvince) return value;\r\n                    for(let i = 0; i < me.listProvince.length; i++){\r\n                        if(me.listProvince[i].code == value){\r\n                            return `${me.listProvince[i].name} (${value})`\r\n                        }\r\n                    }\r\n                    return \"\";\r\n                },\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"account.label.time\"),\r\n                key: \"createdDate\",\r\n                size: \"125px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"account.label.status\"),\r\n                key: \"status\",\r\n                size: \"175px\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: true,\r\n                funcConvertText(value) {\r\n                    if(value == CONSTANTS.USER_STATUS.ACTIVE){\r\n                        return me.tranService.translate(\"account.userstatus.active\");\r\n                    }else if(value == CONSTANTS.USER_STATUS.INACTIVE){\r\n                        return me.tranService.translate(\"account.userstatus.inactive\");\r\n                    }else{\r\n                        return \"\";\r\n                    }\r\n                },\r\n            },\r\n        ]\r\n        this.getListProvince();\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n        this.paramQuickSearchCustomer = {\r\n            keyword: null,\r\n            accountRootId: Number(this.accountId),\r\n        }\r\n        this.columnInfoCustomer = [\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerCode\"),\r\n                key: \"code\",\r\n                size: \"30%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerName\"),\r\n                key: \"name\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n        ]\r\n        this.dataSetCustomer = {\r\n            content: [],\r\n            total: 0,\r\n        }\r\n        this.paginationCustomer = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"name,asc;id,asc\",\r\n        }\r\n        this.optionTableCustomer = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n        }\r\n        this.paramQuickSearchContract = {\r\n            keyword: null,\r\n            customerIds: [],\r\n            accountRootId: -1,\r\n        }\r\n        this.columnInfoContract = [\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerCode\"),\r\n                key: \"customerCode\",\r\n                size: \"30%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"customer.label.customerName\"),\r\n                key: \"customerName\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n            {\r\n                name: this.tranService.translate(\"contract.label.contractCode\"),\r\n                key: \"contractCode\",\r\n                size: \"50%\",\r\n                align: \"left\",\r\n                isShow: true,\r\n                isSort: false,\r\n            },\r\n        ]\r\n        this.dataSetContract = {\r\n            content: [],\r\n            total: 0,\r\n        }\r\n        this.paginationContract = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"customerName,asc;id,asc\",\r\n        }\r\n        this.optionTableContract = {\r\n            hasClearSelected: false,\r\n            hasShowChoose: false,\r\n            hasShowIndex: true,\r\n            hasShowToggleColumn: false,\r\n        }\r\n    }\r\n\r\n    ngAfterContentChecked(): void {\r\n        if(this.isShowDialogChangeManageLevel == false){\r\n            this.accountSelected = null;\r\n        }\r\n    }\r\n\r\n    onSubmitSearch(){\r\n        this.pageNumber = 0;\r\n        this.searchInfo.loggable = true;\r\n        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);\r\n    }\r\n\r\n    search(page, limit, sort, params){\r\n        let me = this;\r\n        this.pageNumber = page;\r\n        this.pageSize = limit;\r\n        this.sort = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        Object.keys(this.searchInfo).forEach(key => {\r\n            if(this.searchInfo[key] != null){\r\n                dataParams[key] = this.searchInfo[key];\r\n            }\r\n        })\r\n        me.messageCommonService.onload();\r\n        this.accountService.search(dataParams, (response)=>{\r\n            me.dataSet = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    filterListAccount(event: AutoCompleteCompleteEvent){\r\n        let valueFilter = event.query;\r\n        this.listAccounts = [\r\n            {\r\n                id: 1,\r\n                name: \"Tai khoan 1\",\r\n            },\r\n            {\r\n                id: 2,\r\n                name: \"Tai khoan 2\",\r\n            },\r\n            {\r\n                id: 3,\r\n                name: \"Tai khoan 3\",\r\n            },\r\n            {\r\n                id: 4,\r\n                name: \"Tai khoan 4\",\r\n            }\r\n        ].filter(el => el.name.indexOf(valueFilter) >= 0);\r\n    }\r\n\r\n    getListProvince(){\r\n        this.accountService.getListProvince((response)=>{\r\n            this.listProvince = response.map(el => {\r\n                return {\r\n                    ...el,\r\n                    display: `${el.code} - ${el.name}`\r\n                }\r\n            })\r\n        })\r\n    }\r\n\r\n    checkPermission(permissions: string[]): boolean{\r\n        return this.checkAuthen(permissions);\r\n    }\r\n\r\n    changeManageLevel(){\r\n\r\n    }\r\n\r\n    loadListUserCustomerOfTeller(params, callback){\r\n        if(this.tellerSelected){\r\n            let p = {\r\n                fullname: params.fullName,\r\n                userManageId: this.tellerSelected.id,\r\n                page: params.page,\r\n                size: params.size,\r\n                sort: params.sort\r\n            }\r\n            this.accountService.searchAccountUserOfUser(p, callback)\r\n        }\r\n    }\r\n\r\n    changeManageData(){\r\n        let data = {\r\n            newUserId: this.changeManageDataInfo.userId,\r\n            oldUserId: this.tellerSelected.id,\r\n            accountCustomerIds: this.changeManageDataInfo.typeSelect == 0 ? null : this.changeManageDataInfo.accountCustomerIds\r\n        }\r\n        let me = this;\r\n        me.messageCommonService.onload();\r\n        this.accountService.changeManageData(data, (response)=>{\r\n            me.isShowDialogChangeManageData = false;\r\n            me.messageCommonService.success(me.tranService.translate(\"global.message.success\"));\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n\r\n    }\r\n\r\n    getDetail(){\r\n        let me = this;\r\n        me.messageCommonService.onload();\r\n        this.accountService.getById(Number(me.accountId), (response)=>{\r\n            me.accountResponse = response;\r\n            me.accountInfo.accountName = response.username;\r\n            me.accountInfo.fullName = response.fullName;\r\n            me.accountInfo.email = response.email;\r\n            me.accountInfo.description = response.description;\r\n            me.accountInfo.phone = response.phone;\r\n            me.accountInfo.province = response.provinceCode;\r\n            me.accountInfo.userType = response.type;\r\n            me.getListRole(false);\r\n            if (me.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER) {\r\n                me.resetPaginationCustomerAndContract()\r\n                me.paramQuickSearchCustomer.accountRootId = Number(me.accountId)\r\n                me.paramQuickSearchContract.customerIds = (me.accountResponse.customers|| []).map(customer => customer.customerId)\r\n                me.paramQuickSearchContract.accountRootId = Number(me.accountId)\r\n            }\r\n            me.statusGrantApi = String(response.statusApi)\r\n            me.selectItemGrantApi = response.listApiId? response.listApiId.map(el=> ({id: el})) : [{id:-99}]\r\n            me.genGrantApi.secretKey = response.secretId\r\n            me.genGrantApi.clientId = response.username\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    getListRole(isClear){\r\n        this.accountService.getListRole(this.accountInfo.userType, (response)=>{\r\n            this.listRole = response.map(el => {\r\n                return {\r\n                    id: el.id,\r\n                    name: el.name\r\n                }\r\n            });\r\n            if(isClear){\r\n                this.accountInfo.roles = null;\r\n            }else{\r\n                this.accountInfo.roles = this.listRole.filter(el => (this.accountResponse.roles||[]).includes(el.id));\r\n            }\r\n        })\r\n    }\r\n\r\n    getStringUserStatus(value) {\r\n        if(value == CONSTANTS.USER_STATUS.ACTIVE){\r\n            return this.tranService.translate(\"account.userstatus.active\");\r\n        }else if(value == CONSTANTS.USER_STATUS.INACTIVE){\r\n            return this.tranService.translate(\"account.userstatus.inactive\");\r\n        }else{\r\n            return \"\";\r\n        }\r\n    }\r\n\r\n    getStringUserType(value) {\r\n        if(value == CONSTANTS.USER_TYPE.ADMIN){\r\n            return this.tranService.translate(\"account.usertype.admin\");\r\n        }else if(value == CONSTANTS.USER_TYPE.CUSTOMER){\r\n            return this.tranService.translate(\"account.usertype.customer\");\r\n        }else if(value == CONSTANTS.USER_TYPE.PROVINCE){\r\n            return this.tranService.translate(\"account.usertype.province\");\r\n        }else if(value == CONSTANTS.USER_TYPE.DISTRICT){\r\n            return this.tranService.translate(\"account.usertype.district\");\r\n        }else if(value == CONSTANTS.USER_TYPE.AGENCY){\r\n            return this.tranService.translate(\"account.usertype.agency\");\r\n        }else{\r\n            return \"\";\r\n        }\r\n    }\r\n    onTabChange(event) {\r\n        const tabName = event.originalEvent.target.innerText;\r\n        let me = this;\r\n        if (event && tabName.includes(this.tranService.translate('account.text.grantApi'))) {\r\n            me.onSearchGrantApi()\r\n        } else if (event && tabName.includes(this.tranService.translate('global.menu.listbill'))) {\r\n            me.onSearchContract()\r\n        } else if (event && tabName.includes(this.tranService.translate('global.menu.listcustomer'))) {\r\n            me.onSearchCustomer()\r\n        }\r\n    }\r\n    onSearchCustomer(back?) {\r\n        let me = this;\r\n        if(back) {\r\n            me.paginationCustomer.page = 0;\r\n        }\r\n        me.searchCustomer(me.paginationCustomer.page, me.paginationCustomer.size, me.paginationCustomer.sortBy, me.paramQuickSearchCustomer);\r\n    }\r\n    searchCustomer(page, limit, sort, params){\r\n        let me = this;\r\n        this.paginationCustomer.page = page;\r\n        this.paginationCustomer.size = limit;\r\n        this.paginationCustomer.sortBy = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        Object.keys(this.paramQuickSearchCustomer).forEach(key => {\r\n            if(this.paramQuickSearchCustomer[key] != null){\r\n                dataParams[key] = this.paramQuickSearchCustomer[key];\r\n            }\r\n        })\r\n        me.messageCommonService.onload();\r\n        this.customerService.quickSearchCustomer(dataParams, this.paramQuickSearchCustomer,(response)=>{\r\n            me.dataSetCustomer = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n        // console.log(this.selectItemCustomer)\r\n    }\r\n\r\n    onSearchContract(back?) {\r\n        let me = this;\r\n        if(back) {\r\n            me.paginationContract.page = 0;\r\n        }\r\n        me.searchContract(me.paginationContract.page, me.paginationContract.size, me.paginationContract.sortBy, me.paramQuickSearchContract);\r\n    }\r\n    searchContract(page, limit, sort, params){\r\n        let me = this;\r\n        this.paginationContract.page = page;\r\n        this.paginationContract.size = limit;\r\n        this.paginationContract.sortBy = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort\r\n        }\r\n        // Object.keys(this.paramQuickSearchContract).forEach(key => {\r\n        //     if(this.paramQuickSearchContract[key] != null){\r\n        //         dataParams[key] = this.paramQuickSearchContract[key];\r\n        //     }\r\n        // })\r\n        me.messageCommonService.onload();\r\n        this.contractService.quickSearchContract(dataParams, this.paramQuickSearchContract,(response)=>{\r\n            me.dataSetContract = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n            // console.log(response)\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n    resetPaginationCustomerAndContract() {\r\n        this.paginationCustomer = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"name,asc;id,asc\",\r\n        }\r\n        this.paginationContract = {\r\n            page: 0,\r\n            size: 10,\r\n            sortBy: \"customerName,asc;id,asc\",\r\n        }\r\n    }\r\n\r\n    searchGrantApi(page, limit, sort, params){\r\n        let me = this;\r\n        this.paginationGrantApi.page = page;\r\n        this.paginationGrantApi.size = limit;\r\n        this.paginationGrantApi.sortBy = sort;\r\n        let dataParams = {\r\n            page,\r\n            size: limit,\r\n            sort,\r\n            selectedApiIds: this.selectItemGrantApi.map(el=>el.id).join(',')\r\n        }\r\n        Object.keys(this.paramsSearchGrantApi).forEach(key => {\r\n            if(this.paramsSearchGrantApi[key] != null){\r\n                dataParams[key] = this.paramsSearchGrantApi[key];\r\n            }\r\n        })\r\n        console.log(dataParams)\r\n        me.messageCommonService.onload();\r\n        this.accountService.searchGrantApi(dataParams,(response)=>{\r\n            me.dataSetGrantApi = {\r\n                content: response.content,\r\n                total: response.totalElements\r\n            }\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n        let copyParam = {...dataParams};\r\n        copyParam.size = *********;\r\n        this.accountService.searchGrantApi(copyParam,(response)=>{\r\n            me.listModule = [...new Set(response.content.map(el=>el.module))]\r\n            me.listModule = me.listModule.map(el=>({\r\n                name : el,\r\n                value : el\r\n            }))\r\n        }, null, ()=>{\r\n            me.messageCommonService.offload();\r\n        })\r\n    }\r\n\r\n\r\n    generateToken(n) {\r\n        var chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r\n        var token = '';\r\n        for(var i = 0; i < n; i++) {\r\n            token += chars[Math.floor(Math.random() * chars.length)];\r\n        }\r\n        return token;\r\n    }\r\n\r\n    genToken(){\r\n        this.genGrantApi.secretKey = this.generateToken(20);\r\n    }\r\n\r\n    onSearchGrantApi(back?) {\r\n        let me = this;\r\n        console.log(me.paramsSearchGrantApi)\r\n        if(back) {\r\n            me.paginationGrantApi.page = 0;\r\n        }\r\n        me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);\r\n    }\r\n    protected readonly CONSTANTS = CONSTANTS;\r\n}\r\n", "<style>\r\n    /* .col-3{\r\n        padding: 10px;\r\n    } */\r\n</style>\r\n\r\n<div class=\"vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round\">\r\n    <div class=\"\">\r\n        <div class=\"text-xl font-bold mb-1\">{{this.tranService.translate(\"global.menu.listaccount\")}}</div>\r\n        <p-breadcrumb class=\"max-w-full col-7\" [model]=\"items\" [home]=\"home\"></p-breadcrumb>\r\n    </div>\r\n    <div class=\"col-5 flex flex-row justify-content-end align-items-center\">\r\n        <p-button styleClass=\"p-button-info\"\r\n                    [label]=\"tranService.translate('global.button.create')\"\r\n                    icon=\"\" [routerLink]=\"['/accounts/create']\"\r\n                    routerLinkActive=\"router-link-active\"\r\n                    *ngIf=\"checkPermission([allPermissions.ACCOUNT.CREATE])\"></p-button>\r\n    </div>\r\n</div>\r\n\r\n<form [formGroup]=\"formSearchAccount\" (ngSubmit)=\"onSubmitSearch()\" class=\"pt-3 pb-2 vnpt-field-set\">\r\n    <p-panel [toggleable]=\"true\" [header]=\"tranService.translate('global.text.filter')\">\r\n        <div class=\"grid search-grid-4\">\r\n            <!-- Ten dang nhap -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input pInputText\r\n                            class=\"w-full\"\r\n                            pInputText id=\"username\"\r\n                            [(ngModel)]=\"searchInfo.username\"\r\n                            formControlName=\"username\"\r\n                    />\r\n                    <label htmlFor=\"username\">{{tranService.translate(\"account.label.username\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- Ho ten -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input pInputText\r\n                            class=\"w-full\"\r\n                            pInputText id=\"fullName\"\r\n                            [(ngModel)]=\"searchInfo.fullName\"\r\n                            formControlName=\"fullName\"\r\n                    />\r\n                    <label htmlFor=\"fullName\">{{tranService.translate(\"account.label.fullname\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- loai tai khoan -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\" [showClear]=\"true\"\r\n                            id=\"type\" [autoDisplayFirst]=\"false\"\r\n                            [(ngModel)]=\"searchInfo.type\"\r\n                            formControlName=\"type\"\r\n                            [options]=\"statusAccounts\"\r\n                            optionLabel=\"name\"\r\n                            optionValue=\"value\"\r\n                    ></p-dropdown>\r\n                    <label class=\"label-dropdown\" for=\"type\">{{tranService.translate(\"account.label.userType\")}}</label>\r\n                </span>\r\n            </div>\r\n             <!-- email -->\r\n             <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <input class=\"w-full\"\r\n                            pInputText id=\"email\"\r\n                            [(ngModel)]=\"searchInfo.email\"\r\n                            formControlName=\"email\"\r\n                    />\r\n                    <label htmlFor=\"email\">{{tranService.translate(\"account.label.email\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- ma tinh -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                    <p-dropdown styleClass=\"w-full\"\r\n                            [showClear]=\"true\" [filter]=\"true\" filterBy=\"display\"\r\n                            id=\"provinceCode\" [autoDisplayFirst]=\"false\"\r\n                            [(ngModel)]=\"searchInfo.provinceCode\"\r\n                            formControlName=\"provinceCode\"\r\n                            [options]=\"listProvince\"\r\n                            optionLabel=\"display\"\r\n                            optionValue=\"code\"\r\n                            [emptyFilterMessage]=\"tranService.translate('global.text.nodata')\"\r\n                    ></p-dropdown>\r\n                    <label class=\"label-dropdown\" htmlFor=\"provinceCode\">{{tranService.translate(\"account.label.province\")}}</label>\r\n                </span>\r\n            </div>\r\n            <!-- trang thai  -->\r\n            <div class=\"col-3\">\r\n                <span class=\"p-float-label\">\r\n                     <p-dropdown styleClass=\"w-full\"\r\n                                 [showClear]=\"true\" [filter]=\"true\" filterBy=\"display\"\r\n                                 id=\"status\" [autoDisplayFirst]=\"false\"\r\n                                 [(ngModel)]=\"searchInfo.status\"\r\n                                 formControlName=\"status\"\r\n                                 [options]=\"listStatus\"\r\n                                 optionLabel=\"name\"\r\n                                 optionValue=\"value\"\r\n                                 [emptyFilterMessage]=\"tranService.translate('account.label.status')\"\r\n                     ></p-dropdown>\r\n                    <label class=\"label-dropdown\" htmlFor=\"status\">{{tranService.translate(\"account.label.status\")}}</label>\r\n                </span>\r\n            </div>\r\n\r\n\r\n            <div class=\"col-3 pb-0\">\r\n                <p-button icon=\"pi pi-search\"\r\n                            styleClass=\"p-button-rounded p-button-secondary p-button-text button-search\"\r\n                            type=\"submit\"\r\n                ></p-button>\r\n            </div>\r\n        </div>\r\n    </p-panel>\r\n</form>\r\n\r\n<div class=\"flex justify-content-center dialog-vnpt \">\r\n    <p-dialog [header]=\"tranService.translate('global.button.view')\" [(visible)]=\"isShowModalDetail\" [modal]=\"true\" [style]=\"{ width: '980px' }\" [draggable]=\"false\" [resizable]=\"false\" *ngIf=\"isShowModalDetail\">\r\n        <p-tabView (onChange)=\"onTabChange($event)\">\r\n            <p-tabPanel header=\"{{tranService.translate('account.label.generalInfo')}}\">\r\n        <div class=\"flex flex-row justify-content-between account-create\" *ngIf=\"accountResponse\">\r\n            <div style=\"width: 49%;\">\r\n                <!-- username -->\r\n                <div class=\"w-full field grid dialog account-info-grid\">\r\n                    <label htmlFor=\"accountName\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.username\")}}</label>\r\n                    <div class=\"col\">\r\n                        {{accountResponse.username}}\r\n                    </div>\r\n                </div>\r\n                <!-- status -->\r\n                <div class=\"w-full field grid account-info-grid\">\r\n                    <label htmlFor=\"fullName\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.status\")}}</label>\r\n                    <div class=\"col\">\r\n                        {{getStringUserStatus(accountResponse.status)}}\r\n                    </div>\r\n                </div>\r\n                <!-- fullname -->\r\n                <div class=\"w-full field grid account-info-grid\">\r\n                    <label htmlFor=\"fullName\" class=\"col-fixed\" style=\"width:180px;height: fit-content;\">{{tranService.translate(\"account.label.fullname\")}}</label>\r\n                    <div class=\"col\" style=\"width: calc(100% - 180px);overflow-wrap: break-word;\">\r\n                        {{accountResponse.fullName}}\r\n                    </div>\r\n                </div>\r\n                <!-- phone -->\r\n                <div class=\"w-full field grid account-info-grid\">\r\n                    <label htmlFor=\"phone\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.phone\")}}</label>\r\n                    <div class=\"col\">\r\n                        {{accountResponse.phone}}\r\n                    </div>\r\n                </div>\r\n                <!-- email -->\r\n                <div class=\"w-full field grid account-info-grid\">\r\n                    <label htmlFor=\"email\" class=\"col-fixed\" style=\"width:180px;height: fit-content;\">{{tranService.translate(\"account.label.email\")}}</label>\r\n                    <div class=\"col\" style=\"width: calc(100% - 180px);overflow-wrap: break-word;\">\r\n                        {{accountResponse.email}}\r\n                    </div>\r\n                </div>\r\n                <!-- description -->\r\n                <div class=\"w-full field grid account-info-grid\">\r\n                    <label htmlFor=\"description\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.description\")}}</label>\r\n                    <div class=\"col\">\r\n                        {{accountResponse.description}}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div style=\"width: 49%;\">\r\n                <!-- loai tai khoan -->\r\n                <div class=\"w-full field grid account-info-grid\">\r\n                    <label for=\"userType\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.userType\")}}</label>\r\n                    <div class=\"col\">\r\n                        <span>{{getStringUserType(accountResponse.type)}}</span>\r\n                    </div>\r\n                </div>\r\n                <!-- Tinh thanh pho -->\r\n                <div class=\"w-full field grid account-info-grid\" *ngIf=\"accountInfo.userType != optionUserType.ADMIN && accountInfo.userType != optionUserType.AGENCY\">\r\n                    <label htmlFor=\"province\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.province\")}}</label>\r\n                    <div class=\"col\">\r\n                        <span>{{accountResponse.provinceName}} ({{accountResponse.provinceCode}})</span>\r\n                    </div>\r\n                </div>\r\n<!--                &lt;!&ndash; ten khach hang &ndash;&gt;-->\r\n<!--                <div class=\"w-full field grid\" *ngIf=\"accountInfo.userType == optionUserType.CUSTOMER\">-->\r\n<!--                    <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px;height: fit-content;\">{{tranService.translate(\"account.label.customerName\")}}</label>-->\r\n<!--                    <div class=\"col\" style=\"max-width: calc(100% - 180px) !important;\">-->\r\n<!--                        <div *ngFor=\"let item of accountResponse.customers\">-->\r\n<!--                            {{ item.customerName + ' - ' + item.customerCode}}-->\r\n<!--                        </div>-->\r\n<!--                    </div>-->\r\n<!--                </div>-->\r\n                <!-- GDV quan ly-->\r\n                <div class=\"w-full field grid account-info-grid\" [class]=\"accountInfo.userType == optionUserType.CUSTOMER && (userType == optionUserType.ADMIN || userType == optionUserType.PROVINCE) ? '' : 'hidden'\">\r\n                    <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.managerName\")}}</label>\r\n                    <div class=\"col\" style=\"max-width: calc(100% - 180px) !important;\">\r\n                        {{ accountResponse?.manager?.username }}\r\n                    </div>\r\n                </div>\r\n                <!-- Danh sach tai khoan khach hang -->\r\n                <div class=\"w-full field grid align-items-start account-info-grid\" *ngIf=\"accountInfo.userType == optionUserType.DISTRICT\">\r\n                    <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.customerAccount\")}}</label>\r\n                    <div class=\"col\" style=\"max-width: calc(100% - 180px) !important;\">\r\n                        <div *ngFor=\"let item of accountResponse?.userManages\">\r\n                            <div *ngIf=\"item?.isRootCustomer\">{{ item.username}}</div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <!-- Tài khoản khách hàng root khi-->\r\n                <div class=\"w-full field grid align-items-start account-info-grid\" *ngIf=\"accountInfo.userType == optionUserType.CUSTOMER && !accountResponse?.isRootCustomer\">\r\n                    <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px\">{{tranService.translate(\"account.label.customerAccount\")}}</label>\r\n                    <div class=\"col\" style=\"max-width: calc(100% - 180px) !important;\">\r\n                        <div *ngIf=\"accountResponse?.rootAccount?.username\">\r\n                            {{ accountResponse.rootAccount.username}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <!-- nhom quyen -->\r\n                <div class=\"w-full field grid account-info-grid\">\r\n                    <label htmlFor=\"roles\" class=\"col-fixed\" style=\"width:180px; height: fit-content;\">{{tranService.translate(\"account.label.role\")}}</label>\r\n                    <div class=\"col\" style=\"max-width: calc(100% - 180px) !important;\">\r\n                        <!-- <div>{{getStringRoles()}}</div> -->\r\n                        <div *ngFor=\"let item of accountResponse.roles\">\r\n                            {{ item.roleName}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n            </p-tabPanel>\r\n            <p-tabPanel header=\"{{tranService.translate('global.menu.listcustomer')}}\" *ngIf=\"accountInfo.userType ==  CONSTANTS.USER_TYPE.CUSTOMER\">\r\n                <div class=\"flex flex-row justify-content-center gap-3 mt-4\">\r\n                    <input style=\"min-width: 35vw\"  type=\"text\" pInputText [placeholder]=\"tranService.translate('sim.label.quickSearch')\" (keydown.enter)=\"onSearchCustomer(true)\" [(ngModel)]=\"paramQuickSearchCustomer.keyword\" [ngModelOptions]=\"{standalone: true}\">\r\n                    <p-button icon=\"pi pi-search\"\r\n                              styleClass=\"ml-3 p-button-rounded p-button-secondary p-button-text button-search\"\r\n                              type=\"button\"\r\n                              (click)=\"onSearchCustomer(true)\"\r\n                    ></p-button>\r\n                </div>\r\n                <table-vnpt\r\n                    [fieldId]=\"'id'\"\r\n                    [pageNumber]=\"paginationCustomer.page\"\r\n                    [pageSize]=\"paginationCustomer.size\"\r\n                    [columns]=\"columnInfoCustomer\"\r\n                    [dataSet]=\"dataSetCustomer\"\r\n                    [options]=\"optionTableCustomer\"\r\n                    [loadData]=\"searchCustomer.bind(this)\"\r\n                    [rowsPerPageOptions]=\"[5,10,20,25,50]\"\r\n                    [scrollHeight]=\"'400px'\"\r\n                    [sort]=\"paginationCustomer.sortBy\"\r\n                    [params]=\"paramQuickSearchCustomer\"\r\n                ></table-vnpt>\r\n            </p-tabPanel>\r\n            <p-tabPanel header=\"{{tranService.translate('global.menu.listbill')}}\" *ngIf=\"accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER\">\r\n                <div class=\"flex flex-row justify-content-center gap-3 mt-4\">\r\n                    <input style=\"min-width: 35vw\"  type=\"text\" pInputText [placeholder]=\"tranService.translate('sim.label.quickSearch')\" (keydown.enter)=\"onSearchContract(true)\" [(ngModel)]=\"paramQuickSearchContract.keyword\" [ngModelOptions]=\"{standalone: true}\">\r\n                    <p-button icon=\"pi pi-search\"\r\n                              styleClass=\"ml-3 p-button-rounded p-button-secondary p-button-text button-search\"\r\n                              type=\"button\"\r\n                              (click)=\"onSearchContract(true)\"\r\n                    ></p-button>\r\n                </div>\r\n                <table-vnpt\r\n                    [fieldId]=\"'id'\"\r\n                    [pageNumber]=\"paginationContract.page\"\r\n                    [pageSize]=\"paginationContract.size\"\r\n                    [columns]=\"columnInfoContract\"\r\n                    [dataSet]=\"dataSetContract\"\r\n                    [options]=\"optionTableContract\"\r\n                    [loadData]=\"searchContract.bind(this)\"\r\n                    [rowsPerPageOptions]=\"[5,10,20,25,50]\"\r\n                    [scrollHeight]=\"'400px'\"\r\n                    [sort]=\"paginationContract.sortBy\"\r\n                    [params]=\"paramQuickSearchContract\"\r\n                ></table-vnpt>\r\n            </p-tabPanel>\r\n            <p-tabPanel header=\"{{tranService.translate('account.text.grantApi')}}\" *ngIf=\"genGrantApi.secretKey != null\" [pt]=\"'ProfileTab'\">\r\n                <div class=\"mb-3\">\r\n                    <p-panel [showHeader]=\"false\">\r\n                        <div class=\"flex gap-2\">\r\n                            <p-radioButton\r\n                                    [label]=\"tranService.translate('account.text.working')\"\r\n                                    value=\"1\"\r\n                                    class=\"p-3\"\r\n                                    [(ngModel)]=\"statusGrantApi\"\r\n                                    [disabled]=\"true\"\r\n                                    [ngModelOptions]=\"{standalone: true}\"\r\n                            >\r\n                            </p-radioButton>\r\n                            <p-radioButton\r\n                                    [label]=\"tranService.translate('account.text.notWorking')\"\r\n                                    value=\"0\"\r\n                                    class=\"p-3\"\r\n                                    [(ngModel)]=\"statusGrantApi\"\r\n                                    [disabled]=\"true\"\r\n                                    [ngModelOptions]=\"{standalone: true}\"\r\n                            >\r\n                            </p-radioButton>\r\n                        </div>\r\n                        <div class=\"flex gap-3 align-items-center api-input-section\">\r\n                            <div class=\"col-6\">\r\n                                <div class=\"flex align-items-center\">\r\n                                    <label style=\"min-width: 100px\" class=\"mr-3\">Client ID</label>\r\n                                    <input [(ngModel)]=\"genGrantApi.clientId\"  [disabled]=\"true\" [ngModelOptions]=\"{standalone: true}\" class=\"w-full\" type=\"text\" pInputText>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"col-6\">\r\n                                <div class=\"flex align-items-center\">\r\n                                    <label style=\"min-width: 100px\" class=\"mr-3\">Secret Key</label>\r\n                                    <div class=\"w-full flex align-items-center\">\r\n                                        <input class=\"w-full mr-2\" style=\"padding-right: 30px;\"\r\n                                               [(ngModel)]=\"genGrantApi.secretKey\"\r\n                                               [ngModelOptions]=\"{standalone: true}\"\r\n                                               [type]=\"isShowSecretKey ? 'text': 'password'\"\r\n                                               pInputText\r\n                                               [disabled]=\"true\"\r\n                                        />\r\n                                        <label style=\"margin-left: -30px;z-index: 1;\" *ngIf=\"isShowSecretKey == false\" class=\"pi pi-eye toggle-password\" (click)=\"isShowSecretKey = true\"></label>\r\n                                        <label style=\"margin-left: -30px;z-index: 1;\" *ngIf=\"isShowSecretKey == true\" class=\"pi pi-eye-slash toggle-password\" (click)=\"isShowSecretKey = false\"></label>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </p-panel>\r\n                </div>\r\n                <div>\r\n                    <p-panel [showHeader]=\"false\">\r\n                        <div class=\"flex gap-3 align-items-center module-search\">\r\n                            <div class=\"col-3 dropdown-fit\">\r\n                                <p-dropdown class=\"w-full\"\r\n                                            [showClear]=\"true\"\r\n                                            [(ngModel)]=\"paramsSearchGrantApi.module\"\r\n                                            [ngModelOptions]=\"{standalone: true}\"\r\n                                            [options]=\"listModule\"\r\n                                            optionLabel=\"name\"\r\n                                            optionValue=\"value\"\r\n                                            [emptyFilterMessage]=\"tranService.translate('global.text.nodata')\"\r\n                                            filter=\"true\"\r\n                                            [placeholder]=\"tranService.translate('account.text.module')\"\r\n                                ></p-dropdown>\r\n                            </div>\r\n                            <div class=\"col-3 dropdown-fit\">\r\n                                <input [(ngModel)]=\"paramsSearchGrantApi.api\" [ngModelOptions]=\"{standalone: true}\" class=\"w-full mr-2\" type=\"text\" pInputText placeholder=\"API\"/>\r\n                            </div>\r\n                            <p-button icon=\"pi pi-search\"\r\n                                      styleClass=\"ml-3 p-button-rounded p-button-secondary p-button-text button-search\"\r\n                                      type=\"button\"\r\n                                      (click)=\"onSearchGrantApi(true)\"\r\n                            ></p-button>\r\n                        </div>\r\n\r\n                        <table-vnpt\r\n                                [fieldId]=\"'id'\"\r\n                                [pageNumber]=\"paginationGrantApi.page\"\r\n                                [pageSize]=\"paginationGrantApi.size\"\r\n                                [columns]=\"columnInfoGrantApi\"\r\n                                [dataSet]=\"dataSetGrantApi\"\r\n                                [options]=\"optionTableGrantApi\"\r\n                                [loadData]=\"searchGrantApi.bind(this)\"\r\n                                [rowsPerPageOptions]=\"[5,10,20,25,50]\"\r\n                                [scrollHeight]=\"'400px'\"\r\n                                [sort]=\"paginationGrantApi.sortBy\"\r\n                                [params]=\"paramsSearchGrantApi\"\r\n                        ></table-vnpt>\r\n                    </p-panel>\r\n                </div>\r\n            </p-tabPanel>\r\n        </p-tabView>\r\n    </p-dialog>\r\n</div>\r\n<table-vnpt\r\n    [fieldId]=\"'id'\"\r\n    [(selectItems)]=\"selectItems\"\r\n    [columns]=\"columns\"\r\n    [dataSet]=\"dataSet\"\r\n    [options]=\"optionTable\"\r\n    [loadData]=\"search.bind(this)\"\r\n    [pageNumber]=\"pageNumber\"\r\n    [pageSize]=\"pageSize\"\r\n    [sort]=\"sort\"\r\n    [params]=\"searchInfo\"\r\n    [labelTable]=\"this.tranService.translate('global.menu.listaccount')\"\r\n></table-vnpt>\r\n\r\n<div class=\"flex justify-content-center dialog-push-group\">\r\n    <p-dialog [header]=\"tranService.translate('account.text.titleChangeManageLevel')\" [(visible)]=\"isShowDialogChangeManageLevel\" [modal]=\"true\" [style]=\"{ width: '500px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n        <div class=\"w-full field grid\">\r\n            <label htmlFor=\"account\" class=\"col-fixed\" style=\"width:100px\">{{tranService.translate(\"account.text.account\")}}</label>\r\n            <div class=\"col\">\r\n                <p-autoComplete styleClass=\"w-full\"\r\n                    id=\"account\"\r\n                    field=\"name\"\r\n                    [(ngModel)]=\"accountSelected\"\r\n                    [suggestions]=\"listAccounts\"\r\n                    (completeMethod)=\"filterListAccount($event)\"\r\n                    [dropdown] = \"true\"\r\n                    [placeholder]=\"tranService.translate('account.text.selectAccount')\"\r\n                ></p-autoComplete>\r\n            </div>\r\n        </div>\r\n        <div class=\"flex flex-row justify-content-center align-items-center\">\r\n            <p-button styleClass=\"mr-2 p-button-secondary\" [label]=\"tranService.translate('global.button.cancel')\" (click)=\"isShowDialogChangeManageLevel = false\"></p-button>\r\n            <p-button styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.save')\" (click)=\"changeManageLevel()\" [disabled]=\"accountSelected == null || accountSelected == undefined\"></p-button>\r\n        </div>\r\n    </p-dialog>\r\n</div>\r\n\r\n<div class=\"flex justify-content-center dialog-vnpt\" *ngIf=\"formChangeManageData\">\r\n    <p-dialog [header]=\"tranService.translate('global.text.changeManageData')\" [(visible)]=\"isShowDialogChangeManageData\" [modal]=\"true\" [style]=\"{ width: '500px' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n        <form [formGroup]=\"formChangeManageData\" (ngSubmit)=\"changeManageData()\">\r\n            <!-- giao dich vien -->\r\n            <div class=\"w-full field grid\">\r\n                <label htmlFor=\"customer\" class=\"col-fixed\" style=\"width:140px\">{{tranService.translate(\"account.usertype.district\")}}<span class=\"text-red-500\">*</span></label>\r\n                <div class=\"col dropdown-fit\">\r\n                    <vnpt-select\r\n                        [control]=\"searchUserTellerController\"\r\n                        [(value)]=\"changeManageDataInfo.userId\"\r\n                        [required]=\"true\"\r\n                        [isAutoComplete]=\"false\"\r\n                        [isMultiChoice]=\"false\"\r\n                        objectKey=\"account\"\r\n                        paramKey=\"fullName\"\r\n                        [paramDefault]=\"paramSearchTeller\"\r\n                        keyReturn=\"id\"\r\n                        displayPattern=\"${fullName} - ${email}\"\r\n                        [lazyLoad]=\"true\"\r\n                        [listExclude]=\"listTellerExcludes\"\r\n                    ></vnpt-select>\r\n                </div>\r\n            </div>\r\n            <!-- error giao dich vien -->\r\n            <div class=\"w-full field grid text-error-field\">\r\n                <label htmlFor=\"province\" class=\"col-fixed\" style=\"width:140px\"></label>\r\n                <div class=\"col dropdown-fit\" style=\"width: calc(100% - 140px)\">\r\n                    <small class=\"text-red-500\" *ngIf=\"searchUserTellerController.dirty && searchUserTellerController.error.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                </div>\r\n            </div>\r\n            <div class=\"w-full field grid\">\r\n                <div class=\"col\">\r\n                    <p-radioButton\r\n                        name=\"typeSelect\"\r\n                        [value]=\"0\"\r\n                        [(ngModel)]=\"changeManageDataInfo.typeSelect\"\r\n                        formControlName=\"typeSelect\"\r\n                        inputId=\"typeSelectAll\"/>\r\n                    <label for=\"typeSelectAll\" class=\"ml-2\">\r\n                        {{tranService.translate(\"account.text.typeSelectAll\")}}\r\n                    </label>\r\n                </div>\r\n                <div class=\"col\">\r\n                    <p-radioButton\r\n                        name=\"typeSelect\"\r\n                        [value]=\"1\"\r\n                        [(ngModel)]=\"changeManageDataInfo.typeSelect\"\r\n                        formControlName=\"typeSelect\"\r\n                        inputId=\"typeSelectList\"/>\r\n                    <label for=\"typeSelectList\" class=\"ml-2\">\r\n                        {{tranService.translate(\"account.text.typeSelectList\")}}\r\n                    </label>\r\n                </div>\r\n            </div>\r\n            <!-- khach hang -->\r\n            <div class=\"w-full field grid\" [class]=\"changeManageDataInfo.typeSelect == 1 ? '' : 'hidden'\">\r\n                <label htmlFor=\"customer\" class=\"col-fixed\" style=\"width:140px\">{{tranService.translate(\"account.usertype.customer\")}}<span class=\"text-red-500\">*</span></label>\r\n                <div class=\"col dropdown-fit\" style=\"width: calc(100% - 140px)\">\r\n                    <vnpt-select\r\n                        [control]=\"searchUserCustomerController\"\r\n                        [(value)]=\"changeManageDataInfo.accountCustomerIds\"\r\n                        [required]=\"true\"\r\n                        [isAutoComplete]=\"false\"\r\n                        [isMultiChoice]=\"true\"\r\n                        [loadData]=\"loadListUserCustomerOfTeller.bind(this)\"\r\n                        paramKey=\"fullName\"\r\n                        keyReturn=\"id\"\r\n                        displayPattern=\"${fullName} - ${email}\"\r\n                        [lazyLoad]=\"true\"\r\n                    ></vnpt-select>\r\n                </div>\r\n            </div>\r\n            <!-- error khach hang -->\r\n            <div class=\"w-full field grid text-error-field\" *ngIf=\"changeManageDataInfo.typeSelect == 1\">\r\n                <label htmlFor=\"province\" class=\"col-fixed\" style=\"width:140px\"></label>\r\n                <div class=\"col\" style=\"width: calc(100% - 140px)\">\r\n                    <small class=\"text-red-500\" *ngIf=\"searchUserCustomerController.dirty && searchUserCustomerController.error.required\">{{tranService.translate(\"global.message.required\")}}</small>\r\n                </div>\r\n            </div>\r\n            <div class=\"flex flex-row justify-content-center align-items-center\">\r\n                <p-button styleClass=\"mr-2 p-button-secondary p-button-outlined\" [label]=\"tranService.translate('global.button.cancel')\" (click)=\"isShowDialogChangeManageData = false\"></p-button>\r\n                <p-button styleClass=\"p-button-info\" [label]=\"tranService.translate('global.button.save')\" type=\"submit\" [disabled]=\"formChangeManageData.invalid || (searchUserTellerController.invalid === true) || (changeManageDataInfo.typeSelect == 1 && (searchUserCustomerController.invalid === true))\"></p-button>\r\n            </div>\r\n        </form>\r\n    </p-dialog>\r\n</div>\r\n"], "mappings": "AAGA,SAASA,cAAc,QAAQ,wCAAwC;AAEvE,SAASC,SAAS,QAAQ,iCAAiC;AAE3D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,yDAAyD;;;;;;;;;;;;;;;;;;;;;;;;ICIlFC,EAAA,CAAAC,SAAA,mBAIgF;;;;IAHpED,EAAA,CAAAE,UAAA,UAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,yBAAuD,eAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA;;;;;IAiK3DP,EAAA,CAAAQ,cAAA,cAAuJ;IACnFR,EAAA,CAAAS,MAAA,GAAmD;IAAAT,EAAA,CAAAU,YAAA,EAAQ;IAC3HV,EAAA,CAAAQ,cAAA,cAAiB;IACPR,EAAA,CAAAS,MAAA,GAAmE;IAAAT,EAAA,CAAAU,YAAA,EAAO;;;;IAFpBV,EAAA,CAAAW,SAAA,GAAmD;IAAnDX,EAAA,CAAAY,iBAAA,CAAAC,MAAA,CAAAT,WAAA,CAAAC,SAAA,2BAAmD;IAEzGL,EAAA,CAAAW,SAAA,GAAmE;IAAnEX,EAAA,CAAAc,kBAAA,KAAAD,MAAA,CAAAE,eAAA,CAAAC,YAAA,QAAAH,MAAA,CAAAE,eAAA,CAAAE,YAAA,MAAmE;;;;;IAwBrEjB,EAAA,CAAAQ,cAAA,UAAkC;IAAAR,EAAA,CAAAS,MAAA,GAAkB;IAAAT,EAAA,CAAAU,YAAA,EAAM;;;;IAAxBV,EAAA,CAAAW,SAAA,GAAkB;IAAlBX,EAAA,CAAAY,iBAAA,CAAAM,QAAA,CAAAC,QAAA,CAAkB;;;;;IADxDnB,EAAA,CAAAQ,cAAA,UAAuD;IACnDR,EAAA,CAAAoB,UAAA,IAAAC,qEAAA,kBAA0D;IAC9DrB,EAAA,CAAAU,YAAA,EAAM;;;;IADIV,EAAA,CAAAW,SAAA,GAA0B;IAA1BX,EAAA,CAAAE,UAAA,SAAAgB,QAAA,kBAAAA,QAAA,CAAAI,cAAA,CAA0B;;;;;IAJ5CtB,EAAA,CAAAQ,cAAA,cAA2H;IAC1DR,EAAA,CAAAS,MAAA,GAA0D;IAAAT,EAAA,CAAAU,YAAA,EAAQ;IAC/HV,EAAA,CAAAQ,cAAA,cAAmE;IAC/DR,EAAA,CAAAoB,UAAA,IAAAG,+DAAA,kBAEM;IACVvB,EAAA,CAAAU,YAAA,EAAM;;;;IALuDV,EAAA,CAAAW,SAAA,GAA0D;IAA1DX,EAAA,CAAAY,iBAAA,CAAAY,MAAA,CAAApB,WAAA,CAAAC,SAAA,kCAA0D;IAE7FL,EAAA,CAAAW,SAAA,GAA+B;IAA/BX,EAAA,CAAAE,UAAA,YAAAsB,MAAA,CAAAT,eAAA,kBAAAS,MAAA,CAAAT,eAAA,CAAAU,WAAA,CAA+B;;;;;IASrDzB,EAAA,CAAAQ,cAAA,UAAoD;IAChDR,EAAA,CAAAS,MAAA,GACJ;IAAAT,EAAA,CAAAU,YAAA,EAAM;;;;IADFV,EAAA,CAAAW,SAAA,GACJ;IADIX,EAAA,CAAA0B,kBAAA,MAAAC,OAAA,CAAAZ,eAAA,CAAAa,WAAA,CAAAT,QAAA,MACJ;;;;;IALRnB,EAAA,CAAAQ,cAAA,cAA+J;IAC9FR,EAAA,CAAAS,MAAA,GAA0D;IAAAT,EAAA,CAAAU,YAAA,EAAQ;IAC/HV,EAAA,CAAAQ,cAAA,cAAmE;IAC/DR,EAAA,CAAAoB,UAAA,IAAAS,+DAAA,kBAEM;IACV7B,EAAA,CAAAU,YAAA,EAAM;;;;IALuDV,EAAA,CAAAW,SAAA,GAA0D;IAA1DX,EAAA,CAAAY,iBAAA,CAAAkB,MAAA,CAAA1B,WAAA,CAAAC,SAAA,kCAA0D;IAE7GL,EAAA,CAAAW,SAAA,GAA4C;IAA5CX,EAAA,CAAAE,UAAA,SAAA4B,MAAA,CAAAf,eAAA,kBAAAe,MAAA,CAAAf,eAAA,CAAAa,WAAA,kBAAAE,MAAA,CAAAf,eAAA,CAAAa,WAAA,CAAAT,QAAA,CAA4C;;;;;IAUlDnB,EAAA,CAAAQ,cAAA,UAAgD;IAC5CR,EAAA,CAAAS,MAAA,GACJ;IAAAT,EAAA,CAAAU,YAAA,EAAM;;;;IADFV,EAAA,CAAAW,SAAA,GACJ;IADIX,EAAA,CAAA0B,kBAAA,MAAAK,QAAA,CAAAC,QAAA,MACJ;;;;;IArGhBhC,EAAA,CAAAQ,cAAA,cAA0F;IAIXR,EAAA,CAAAS,MAAA,GAAmD;IAAAT,EAAA,CAAAU,YAAA,EAAQ;IAC9HV,EAAA,CAAAQ,cAAA,cAAiB;IACbR,EAAA,CAAAS,MAAA,GACJ;IAAAT,EAAA,CAAAU,YAAA,EAAM;IAGVV,EAAA,CAAAQ,cAAA,cAAiD;IACmBR,EAAA,CAAAS,MAAA,GAAiD;IAAAT,EAAA,CAAAU,YAAA,EAAQ;IACzHV,EAAA,CAAAQ,cAAA,eAAiB;IACbR,EAAA,CAAAS,MAAA,IACJ;IAAAT,EAAA,CAAAU,YAAA,EAAM;IAGVV,EAAA,CAAAQ,cAAA,eAAiD;IACwCR,EAAA,CAAAS,MAAA,IAAmD;IAAAT,EAAA,CAAAU,YAAA,EAAQ;IAChJV,EAAA,CAAAQ,cAAA,eAA8E;IAC1ER,EAAA,CAAAS,MAAA,IACJ;IAAAT,EAAA,CAAAU,YAAA,EAAM;IAGVV,EAAA,CAAAQ,cAAA,eAAiD;IACgBR,EAAA,CAAAS,MAAA,IAAgD;IAAAT,EAAA,CAAAU,YAAA,EAAQ;IACrHV,EAAA,CAAAQ,cAAA,eAAiB;IACbR,EAAA,CAAAS,MAAA,IACJ;IAAAT,EAAA,CAAAU,YAAA,EAAM;IAGVV,EAAA,CAAAQ,cAAA,eAAiD;IACqCR,EAAA,CAAAS,MAAA,IAAgD;IAAAT,EAAA,CAAAU,YAAA,EAAQ;IAC1IV,EAAA,CAAAQ,cAAA,eAA8E;IAC1ER,EAAA,CAAAS,MAAA,IACJ;IAAAT,EAAA,CAAAU,YAAA,EAAM;IAGVV,EAAA,CAAAQ,cAAA,eAAiD;IACsBR,EAAA,CAAAS,MAAA,IAAsD;IAAAT,EAAA,CAAAU,YAAA,EAAQ;IACjIV,EAAA,CAAAQ,cAAA,eAAiB;IACbR,EAAA,CAAAS,MAAA,IACJ;IAAAT,EAAA,CAAAU,YAAA,EAAM;IAGdV,EAAA,CAAAQ,cAAA,eAAyB;IAG2CR,EAAA,CAAAS,MAAA,IAAmD;IAAAT,EAAA,CAAAU,YAAA,EAAQ;IACvHV,EAAA,CAAAQ,cAAA,eAAiB;IACPR,EAAA,CAAAS,MAAA,IAA2C;IAAAT,EAAA,CAAAU,YAAA,EAAO;IAIhEV,EAAA,CAAAoB,UAAA,KAAAa,yDAAA,kBAKM;IAWNjC,EAAA,CAAAQ,cAAA,eAAwM;IACvIR,EAAA,CAAAS,MAAA,IAAsD;IAAAT,EAAA,CAAAU,YAAA,EAAQ;IAC3HV,EAAA,CAAAQ,cAAA,eAAmE;IAC/DR,EAAA,CAAAS,MAAA,IACJ;IAAAT,EAAA,CAAAU,YAAA,EAAM;IAGVV,EAAA,CAAAoB,UAAA,KAAAc,yDAAA,kBAOM;IAENlC,EAAA,CAAAoB,UAAA,KAAAe,yDAAA,kBAOM;IAENnC,EAAA,CAAAQ,cAAA,eAAiD;IACsCR,EAAA,CAAAS,MAAA,IAA+C;IAAAT,EAAA,CAAAU,YAAA,EAAQ;IAC1IV,EAAA,CAAAQ,cAAA,eAAmE;IAE/DR,EAAA,CAAAoB,UAAA,KAAAgB,yDAAA,kBAEM;IACVpC,EAAA,CAAAU,YAAA,EAAM;;;;IAlG6DV,EAAA,CAAAW,SAAA,GAAmD;IAAnDX,EAAA,CAAAY,iBAAA,CAAAyB,MAAA,CAAAjC,WAAA,CAAAC,SAAA,2BAAmD;IAElHL,EAAA,CAAAW,SAAA,GACJ;IADIX,EAAA,CAAA0B,kBAAA,MAAAW,MAAA,CAAAtB,eAAA,CAAAI,QAAA,MACJ;IAIgEnB,EAAA,CAAAW,SAAA,GAAiD;IAAjDX,EAAA,CAAAY,iBAAA,CAAAyB,MAAA,CAAAjC,WAAA,CAAAC,SAAA,yBAAiD;IAE7GL,EAAA,CAAAW,SAAA,GACJ;IADIX,EAAA,CAAA0B,kBAAA,MAAAW,MAAA,CAAAC,mBAAA,CAAAD,MAAA,CAAAtB,eAAA,CAAAwB,MAAA,OACJ;IAIqFvC,EAAA,CAAAW,SAAA,GAAmD;IAAnDX,EAAA,CAAAY,iBAAA,CAAAyB,MAAA,CAAAjC,WAAA,CAAAC,SAAA,2BAAmD;IAEpIL,EAAA,CAAAW,SAAA,GACJ;IADIX,EAAA,CAAA0B,kBAAA,MAAAW,MAAA,CAAAtB,eAAA,CAAAyB,QAAA,MACJ;IAI6DxC,EAAA,CAAAW,SAAA,GAAgD;IAAhDX,EAAA,CAAAY,iBAAA,CAAAyB,MAAA,CAAAjC,WAAA,CAAAC,SAAA,wBAAgD;IAEzGL,EAAA,CAAAW,SAAA,GACJ;IADIX,EAAA,CAAA0B,kBAAA,MAAAW,MAAA,CAAAtB,eAAA,CAAA0B,KAAA,MACJ;IAIkFzC,EAAA,CAAAW,SAAA,GAAgD;IAAhDX,EAAA,CAAAY,iBAAA,CAAAyB,MAAA,CAAAjC,WAAA,CAAAC,SAAA,wBAAgD;IAE9HL,EAAA,CAAAW,SAAA,GACJ;IADIX,EAAA,CAAA0B,kBAAA,MAAAW,MAAA,CAAAtB,eAAA,CAAA2B,KAAA,MACJ;IAImE1C,EAAA,CAAAW,SAAA,GAAsD;IAAtDX,EAAA,CAAAY,iBAAA,CAAAyB,MAAA,CAAAjC,WAAA,CAAAC,SAAA,8BAAsD;IAErHL,EAAA,CAAAW,SAAA,GACJ;IADIX,EAAA,CAAA0B,kBAAA,MAAAW,MAAA,CAAAtB,eAAA,CAAA4B,WAAA,MACJ;IAM4D3C,EAAA,CAAAW,SAAA,GAAmD;IAAnDX,EAAA,CAAAY,iBAAA,CAAAyB,MAAA,CAAAjC,WAAA,CAAAC,SAAA,2BAAmD;IAErGL,EAAA,CAAAW,SAAA,GAA2C;IAA3CX,EAAA,CAAAY,iBAAA,CAAAyB,MAAA,CAAAO,iBAAA,CAAAP,MAAA,CAAAtB,eAAA,CAAA8B,IAAA,EAA2C;IAIP7C,EAAA,CAAAW,SAAA,GAAmG;IAAnGX,EAAA,CAAAE,UAAA,SAAAmC,MAAA,CAAAS,WAAA,CAAAC,QAAA,IAAAV,MAAA,CAAAW,cAAA,CAAAC,KAAA,IAAAZ,MAAA,CAAAS,WAAA,CAAAC,QAAA,IAAAV,MAAA,CAAAW,cAAA,CAAAE,MAAA,CAAmG;IAgBpGlD,EAAA,CAAAW,SAAA,GAAsJ;IAAtJX,EAAA,CAAAmD,UAAA,CAAAd,MAAA,CAAAS,WAAA,CAAAC,QAAA,IAAAV,MAAA,CAAAW,cAAA,CAAAI,QAAA,KAAAf,MAAA,CAAAU,QAAA,IAAAV,MAAA,CAAAW,cAAA,CAAAC,KAAA,IAAAZ,MAAA,CAAAU,QAAA,IAAAV,MAAA,CAAAW,cAAA,CAAAK,QAAA,kBAAsJ;IACtIrD,EAAA,CAAAW,SAAA,GAAsD;IAAtDX,EAAA,CAAAY,iBAAA,CAAAyB,MAAA,CAAAjC,WAAA,CAAAC,SAAA,8BAAsD;IAE/GL,EAAA,CAAAW,SAAA,GACJ;IADIX,EAAA,CAAA0B,kBAAA,MAAAW,MAAA,CAAAtB,eAAA,kBAAAsB,MAAA,CAAAtB,eAAA,CAAAuC,OAAA,kBAAAjB,MAAA,CAAAtB,eAAA,CAAAuC,OAAA,CAAAnC,QAAA,MACJ;IAGgEnB,EAAA,CAAAW,SAAA,GAAqD;IAArDX,EAAA,CAAAE,UAAA,SAAAmC,MAAA,CAAAS,WAAA,CAAAC,QAAA,IAAAV,MAAA,CAAAW,cAAA,CAAAO,QAAA,CAAqD;IASrDvD,EAAA,CAAAW,SAAA,GAAyF;IAAzFX,EAAA,CAAAE,UAAA,SAAAmC,MAAA,CAAAS,WAAA,CAAAC,QAAA,IAAAV,MAAA,CAAAW,cAAA,CAAAI,QAAA,MAAAf,MAAA,CAAAtB,eAAA,kBAAAsB,MAAA,CAAAtB,eAAA,CAAAO,cAAA,EAAyF;IAUtEtB,EAAA,CAAAW,SAAA,GAA+C;IAA/CX,EAAA,CAAAY,iBAAA,CAAAyB,MAAA,CAAAjC,WAAA,CAAAC,SAAA,uBAA+C;IAGxGL,EAAA,CAAAW,SAAA,GAAwB;IAAxBX,EAAA,CAAAE,UAAA,YAAAmC,MAAA,CAAAtB,eAAA,CAAAyC,KAAA,CAAwB;;;;;;;;;;;;;;IAQ1DxD,EAAA,CAAAQ,cAAA,qBAAyI;IAEXR,EAAA,CAAAyD,UAAA,2BAAAC,yFAAA;MAAA1D,EAAA,CAAA2D,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAA7D,EAAA,CAAA8D,aAAA;MAAA,OAAiB9D,EAAA,CAAA+D,WAAA,CAAAF,OAAA,CAAAG,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC,2BAAAC,yFAAAC,MAAA;MAAAlE,EAAA,CAAA2D,aAAA,CAAAC,IAAA;MAAA,MAAAO,OAAA,GAAAnE,EAAA,CAAA8D,aAAA;MAAA,OAAc9D,EAAA,CAAA+D,WAAA,CAAAI,OAAA,CAAAC,wBAAA,CAAAC,OAAA,GAAAH,MAAA,CAAwC;IAAA,EAAtD;IAA9JlE,EAAA,CAAAU,YAAA,EAAoP;IACpPV,EAAA,CAAAQ,cAAA,mBAIC;IADSR,EAAA,CAAAyD,UAAA,mBAAAa,oFAAA;MAAAtE,EAAA,CAAA2D,aAAA,CAAAC,IAAA;MAAA,MAAAW,OAAA,GAAAvE,EAAA,CAAA8D,aAAA;MAAA,OAAS9D,EAAA,CAAA+D,WAAA,CAAAQ,OAAA,CAAAP,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC;IACzChE,EAAA,CAAAU,YAAA,EAAW;IAEhBV,EAAA,CAAAC,SAAA,qBAYc;IAClBD,EAAA,CAAAU,YAAA,EAAa;;;;IAtBDV,EAAA,CAAAwE,qBAAA,WAAAC,MAAA,CAAArE,WAAA,CAAAC,SAAA,6BAA8D;IAEXL,EAAA,CAAAW,SAAA,GAA8D;IAA9DX,EAAA,CAAAE,UAAA,gBAAAuE,MAAA,CAAArE,WAAA,CAAAC,SAAA,0BAA8D,YAAAoE,MAAA,CAAAL,wBAAA,CAAAC,OAAA,oBAAArE,EAAA,CAAAM,eAAA,KAAAoE,GAAA;IAQrH1E,EAAA,CAAAW,SAAA,GAAgB;IAAhBX,EAAA,CAAAE,UAAA,iBAAgB,eAAAuE,MAAA,CAAAE,kBAAA,CAAAC,IAAA,cAAAH,MAAA,CAAAE,kBAAA,CAAAE,IAAA,aAAAJ,MAAA,CAAAK,kBAAA,aAAAL,MAAA,CAAAM,eAAA,aAAAN,MAAA,CAAAO,mBAAA,cAAAP,MAAA,CAAAQ,cAAA,CAAAC,IAAA,CAAAT,MAAA,yBAAAzE,EAAA,CAAAM,eAAA,KAAA6E,GAAA,oCAAAV,MAAA,CAAAE,kBAAA,CAAAS,MAAA,YAAAX,MAAA,CAAAL,wBAAA;;;;;;IAaxBpE,EAAA,CAAAQ,cAAA,qBAAoI;IAENR,EAAA,CAAAyD,UAAA,2BAAA4B,yFAAA;MAAArF,EAAA,CAAA2D,aAAA,CAAA2B,IAAA;MAAA,MAAAC,OAAA,GAAAvF,EAAA,CAAA8D,aAAA;MAAA,OAAiB9D,EAAA,CAAA+D,WAAA,CAAAwB,OAAA,CAAAC,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC,2BAAAC,yFAAAvB,MAAA;MAAAlE,EAAA,CAAA2D,aAAA,CAAA2B,IAAA;MAAA,MAAAI,OAAA,GAAA1F,EAAA,CAAA8D,aAAA;MAAA,OAAc9D,EAAA,CAAA+D,WAAA,CAAA2B,OAAA,CAAAC,wBAAA,CAAAtB,OAAA,GAAAH,MAAA,CAAwC;IAAA,EAAtD;IAA9JlE,EAAA,CAAAU,YAAA,EAAoP;IACpPV,EAAA,CAAAQ,cAAA,mBAIC;IADSR,EAAA,CAAAyD,UAAA,mBAAAmC,oFAAA;MAAA5F,EAAA,CAAA2D,aAAA,CAAA2B,IAAA;MAAA,MAAAO,OAAA,GAAA7F,EAAA,CAAA8D,aAAA;MAAA,OAAS9D,EAAA,CAAA+D,WAAA,CAAA8B,OAAA,CAAAL,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC;IACzCxF,EAAA,CAAAU,YAAA,EAAW;IAEhBV,EAAA,CAAAC,SAAA,qBAYc;IAClBD,EAAA,CAAAU,YAAA,EAAa;;;;IAtBDV,EAAA,CAAAwE,qBAAA,WAAAsB,MAAA,CAAA1F,WAAA,CAAAC,SAAA,yBAA0D;IAEPL,EAAA,CAAAW,SAAA,GAA8D;IAA9DX,EAAA,CAAAE,UAAA,gBAAA4F,MAAA,CAAA1F,WAAA,CAAAC,SAAA,0BAA8D,YAAAyF,MAAA,CAAAH,wBAAA,CAAAtB,OAAA,oBAAArE,EAAA,CAAAM,eAAA,KAAAoE,GAAA;IAQrH1E,EAAA,CAAAW,SAAA,GAAgB;IAAhBX,EAAA,CAAAE,UAAA,iBAAgB,eAAA4F,MAAA,CAAAC,kBAAA,CAAAnB,IAAA,cAAAkB,MAAA,CAAAC,kBAAA,CAAAlB,IAAA,aAAAiB,MAAA,CAAAE,kBAAA,aAAAF,MAAA,CAAAG,eAAA,aAAAH,MAAA,CAAAI,mBAAA,cAAAJ,MAAA,CAAAK,cAAA,CAAAjB,IAAA,CAAAY,MAAA,yBAAA9F,EAAA,CAAAM,eAAA,KAAA6E,GAAA,oCAAAW,MAAA,CAAAC,kBAAA,CAAAX,MAAA,YAAAU,MAAA,CAAAH,wBAAA;;;;;;IAsDI3F,EAAA,CAAAQ,cAAA,gBAAkJ;IAAjCR,EAAA,CAAAyD,UAAA,mBAAA2C,0FAAA;MAAApG,EAAA,CAAA2D,aAAA,CAAA0C,IAAA;MAAA,MAAAC,OAAA,GAAAtG,EAAA,CAAA8D,aAAA;MAAA,OAAA9D,EAAA,CAAA+D,WAAA,CAAAuC,OAAA,CAAAC,eAAA,GAA2B,IAAI;IAAA,EAAC;IAACvG,EAAA,CAAAU,YAAA,EAAQ;;;;;;IAC1JV,EAAA,CAAAQ,cAAA,gBAAwJ;IAAlCR,EAAA,CAAAyD,UAAA,mBAAA+C,0FAAA;MAAAxG,EAAA,CAAA2D,aAAA,CAAA8C,IAAA;MAAA,MAAAC,OAAA,GAAA1G,EAAA,CAAA8D,aAAA;MAAA,OAAA9D,EAAA,CAAA+D,WAAA,CAAA2C,OAAA,CAAAH,eAAA,GAA2B,KAAK;IAAA,EAAC;IAACvG,EAAA,CAAAU,YAAA,EAAQ;;;;;;IA1C5LV,EAAA,CAAAQ,cAAA,qBAAkI;IAQ1GR,EAAA,CAAAyD,UAAA,2BAAAkD,iGAAAzC,MAAA;MAAAlE,EAAA,CAAA2D,aAAA,CAAAiD,IAAA;MAAA,MAAAC,OAAA,GAAA7G,EAAA,CAAA8D,aAAA;MAAA,OAAA9D,EAAA,CAAA+D,WAAA,CAAA8C,OAAA,CAAAC,cAAA,GAAA5C,MAAA;IAAA,EAA4B;IAIpClE,EAAA,CAAAU,YAAA,EAAgB;IAChBV,EAAA,CAAAQ,cAAA,wBAOC;IAHOR,EAAA,CAAAyD,UAAA,2BAAAsD,iGAAA7C,MAAA;MAAAlE,EAAA,CAAA2D,aAAA,CAAAiD,IAAA;MAAA,MAAAI,OAAA,GAAAhH,EAAA,CAAA8D,aAAA;MAAA,OAAA9D,EAAA,CAAA+D,WAAA,CAAAiD,OAAA,CAAAF,cAAA,GAAA5C,MAAA;IAAA,EAA4B;IAIpClE,EAAA,CAAAU,YAAA,EAAgB;IAEpBV,EAAA,CAAAQ,cAAA,cAA6D;IAGJR,EAAA,CAAAS,MAAA,iBAAS;IAAAT,EAAA,CAAAU,YAAA,EAAQ;IAC9DV,EAAA,CAAAQ,cAAA,iBAAyI;IAAlIR,EAAA,CAAAyD,UAAA,2BAAAwD,0FAAA/C,MAAA;MAAAlE,EAAA,CAAA2D,aAAA,CAAAiD,IAAA;MAAA,MAAAM,OAAA,GAAAlH,EAAA,CAAA8D,aAAA;MAAA,OAAa9D,EAAA,CAAA+D,WAAA,CAAAmD,OAAA,CAAAC,WAAA,CAAAC,QAAA,GAAAlD,MAAA,CAA4B;IAAA,EAAP;IAAzClE,EAAA,CAAAU,YAAA,EAAyI;IAGjJV,EAAA,CAAAQ,cAAA,eAAmB;IAEkCR,EAAA,CAAAS,MAAA,kBAAU;IAAAT,EAAA,CAAAU,YAAA,EAAQ;IAC/DV,EAAA,CAAAQ,cAAA,eAA4C;IAEjCR,EAAA,CAAAyD,UAAA,2BAAA4D,0FAAAnD,MAAA;MAAAlE,EAAA,CAAA2D,aAAA,CAAAiD,IAAA;MAAA,MAAAU,OAAA,GAAAtH,EAAA,CAAA8D,aAAA;MAAA,OAAa9D,EAAA,CAAA+D,WAAA,CAAAuD,OAAA,CAAAH,WAAA,CAAAI,SAAA,GAAArD,MAAA,CACvD;IAAA,EAD6E;IAD1ClE,EAAA,CAAAU,YAAA,EAME;IACFV,EAAA,CAAAoB,UAAA,KAAAoG,kEAAA,oBAA0J;IAC1JxH,EAAA,CAAAoB,UAAA,KAAAqG,kEAAA,oBAAgK;IACpKzH,EAAA,CAAAU,YAAA,EAAM;IAM1BV,EAAA,CAAAQ,cAAA,WAAK;IAMuBR,EAAA,CAAAyD,UAAA,2BAAAiE,+FAAAxD,MAAA;MAAAlE,EAAA,CAAA2D,aAAA,CAAAiD,IAAA;MAAA,MAAAe,OAAA,GAAA3H,EAAA,CAAA8D,aAAA;MAAA,OAAa9D,EAAA,CAAA+D,WAAA,CAAA4D,OAAA,CAAAC,oBAAA,CAAAC,MAAA,GAAA3D,MAAA,CACpD;IAAA,EADgF;IAQpDlE,EAAA,CAAAU,YAAA,EAAa;IAElBV,EAAA,CAAAQ,cAAA,eAAgC;IACrBR,EAAA,CAAAyD,UAAA,2BAAAqE,0FAAA5D,MAAA;MAAAlE,EAAA,CAAA2D,aAAA,CAAAiD,IAAA;MAAA,MAAAmB,OAAA,GAAA/H,EAAA,CAAA8D,aAAA;MAAA,OAAa9D,EAAA,CAAA+D,WAAA,CAAAgE,OAAA,CAAAH,oBAAA,CAAAI,GAAA,GAAA9D,MAAA,CAAgC;IAAA,EAAP;IAA7ClE,EAAA,CAAAU,YAAA,EAAkJ;IAEtJV,EAAA,CAAAQ,cAAA,oBAIC;IADSR,EAAA,CAAAyD,UAAA,mBAAAwE,qFAAA;MAAAjI,EAAA,CAAA2D,aAAA,CAAAiD,IAAA;MAAA,MAAAsB,OAAA,GAAAlI,EAAA,CAAA8D,aAAA;MAAA,OAAS9D,EAAA,CAAA+D,WAAA,CAAAmE,OAAA,CAAAC,gBAAA,CAAiB,IAAI,CAAC;IAAA,EAAC;IACzCnI,EAAA,CAAAU,YAAA,EAAW;IAGhBV,EAAA,CAAAC,SAAA,sBAYc;IAClBD,EAAA,CAAAU,YAAA,EAAU;;;;IAxFNV,EAAA,CAAAwE,qBAAA,WAAA4D,MAAA,CAAAhI,WAAA,CAAAC,SAAA,0BAA2D;IAAuCL,EAAA,CAAAE,UAAA,oBAAmB;IAEhHF,EAAA,CAAAW,SAAA,GAAoB;IAApBX,EAAA,CAAAE,UAAA,qBAAoB;IAGbF,EAAA,CAAAW,SAAA,GAAuD;IAAvDX,EAAA,CAAAE,UAAA,UAAAkI,MAAA,CAAAhI,WAAA,CAAAC,SAAA,yBAAuD,YAAA+H,MAAA,CAAAtB,cAAA,sCAAA9G,EAAA,CAAAM,eAAA,KAAAoE,GAAA;IASvD1E,EAAA,CAAAW,SAAA,GAA0D;IAA1DX,EAAA,CAAAE,UAAA,UAAAkI,MAAA,CAAAhI,WAAA,CAAAC,SAAA,4BAA0D,YAAA+H,MAAA,CAAAtB,cAAA,sCAAA9G,EAAA,CAAAM,eAAA,KAAAoE,GAAA;IAanD1E,EAAA,CAAAW,SAAA,GAAkC;IAAlCX,EAAA,CAAAE,UAAA,YAAAkI,MAAA,CAAAjB,WAAA,CAAAC,QAAA,CAAkC,qCAAApH,EAAA,CAAAM,eAAA,KAAAoE,GAAA;IAQ9B1E,EAAA,CAAAW,SAAA,GAAmC;IAAnCX,EAAA,CAAAE,UAAA,YAAAkI,MAAA,CAAAjB,WAAA,CAAAI,SAAA,CAAmC,mBAAAvH,EAAA,CAAAM,eAAA,KAAAoE,GAAA,WAAA0D,MAAA,CAAA7B,eAAA;IAMKvG,EAAA,CAAAW,SAAA,GAA8B;IAA9BX,EAAA,CAAAE,UAAA,SAAAkI,MAAA,CAAA7B,eAAA,UAA8B;IAC9BvG,EAAA,CAAAW,SAAA,GAA6B;IAA7BX,EAAA,CAAAE,UAAA,SAAAkI,MAAA,CAAA7B,eAAA,SAA6B;IAQvFvG,EAAA,CAAAW,SAAA,GAAoB;IAApBX,EAAA,CAAAE,UAAA,qBAAoB;IAILF,EAAA,CAAAW,SAAA,GAAkB;IAAlBX,EAAA,CAAAE,UAAA,mBAAkB,YAAAkI,MAAA,CAAAR,oBAAA,CAAAC,MAAA,oBAAA7H,EAAA,CAAAM,eAAA,KAAAoE,GAAA,cAAA0D,MAAA,CAAAC,UAAA,wBAAAD,MAAA,CAAAhI,WAAA,CAAAC,SAAA,uCAAA+H,MAAA,CAAAhI,WAAA,CAAAC,SAAA;IAYvBL,EAAA,CAAAW,SAAA,GAAsC;IAAtCX,EAAA,CAAAE,UAAA,YAAAkI,MAAA,CAAAR,oBAAA,CAAAI,GAAA,CAAsC,mBAAAhI,EAAA,CAAAM,eAAA,KAAAoE,GAAA;IAU7C1E,EAAA,CAAAW,SAAA,GAAgB;IAAhBX,EAAA,CAAAE,UAAA,iBAAgB,eAAAkI,MAAA,CAAAE,kBAAA,CAAA1D,IAAA,cAAAwD,MAAA,CAAAE,kBAAA,CAAAzD,IAAA,aAAAuD,MAAA,CAAAG,kBAAA,aAAAH,MAAA,CAAAI,eAAA,aAAAJ,MAAA,CAAAK,mBAAA,cAAAL,MAAA,CAAAM,cAAA,CAAAxD,IAAA,CAAAkD,MAAA,yBAAApI,EAAA,CAAAM,eAAA,KAAA6E,GAAA,oCAAAiD,MAAA,CAAAE,kBAAA,CAAAlD,MAAA,YAAAgD,MAAA,CAAAR,oBAAA;;;;;;;;;;;IAxO5C5H,EAAA,CAAAQ,cAAA,mBAA+M;IAA9IR,EAAA,CAAAyD,UAAA,2BAAAkF,+EAAAzE,MAAA;MAAAlE,EAAA,CAAA2D,aAAA,CAAAiF,IAAA;MAAA,MAAAC,OAAA,GAAA7I,EAAA,CAAA8D,aAAA;MAAA,OAAA9D,EAAA,CAAA+D,WAAA,CAAA8E,OAAA,CAAAC,iBAAA,GAAA5E,MAAA;IAAA,EAA+B;IAC5FlE,EAAA,CAAAQ,cAAA,oBAA4C;IAAjCR,EAAA,CAAAyD,UAAA,sBAAAsF,2EAAA7E,MAAA;MAAAlE,EAAA,CAAA2D,aAAA,CAAAiF,IAAA;MAAA,MAAAI,OAAA,GAAAhJ,EAAA,CAAA8D,aAAA;MAAA,OAAY9D,EAAA,CAAA+D,WAAA,CAAAiF,OAAA,CAAAC,WAAA,CAAA/E,MAAA,CAAmB;IAAA,EAAC;IACvClE,EAAA,CAAAQ,cAAA,qBAA4E;IAChFR,EAAA,CAAAoB,UAAA,IAAA8H,kDAAA,oBAyGM;IACFlJ,EAAA,CAAAU,YAAA,EAAa;IACbV,EAAA,CAAAoB,UAAA,IAAA+H,yDAAA,0BAsBa;IACbnJ,EAAA,CAAAoB,UAAA,IAAAgI,yDAAA,0BAsBa;IACbpJ,EAAA,CAAAoB,UAAA,IAAAiI,yDAAA,2BA0Fa;IACjBrJ,EAAA,CAAAU,YAAA,EAAY;;;;IAvPgGV,EAAA,CAAAsJ,UAAA,CAAAtJ,EAAA,CAAAM,eAAA,KAAAiJ,GAAA,EAA4B;IAAlIvJ,EAAA,CAAAE,UAAA,WAAAsJ,MAAA,CAAApJ,WAAA,CAAAC,SAAA,uBAAsD,YAAAmJ,MAAA,CAAAV,iBAAA;IAE5C9I,EAAA,CAAAW,SAAA,GAA+D;IAA/DX,EAAA,CAAAwE,qBAAA,WAAAgF,MAAA,CAAApJ,WAAA,CAAAC,SAAA,8BAA+D;IACZL,EAAA,CAAAW,SAAA,GAAqB;IAArBX,EAAA,CAAAE,UAAA,SAAAsJ,MAAA,CAAAzI,eAAA,CAAqB;IA2GRf,EAAA,CAAAW,SAAA,GAA2D;IAA3DX,EAAA,CAAAE,UAAA,SAAAsJ,MAAA,CAAA1G,WAAA,CAAAC,QAAA,IAAAyG,MAAA,CAAA3J,SAAA,CAAA4J,SAAA,CAAArG,QAAA,CAA2D;IAuB/DpD,EAAA,CAAAW,SAAA,GAA0D;IAA1DX,EAAA,CAAAE,UAAA,SAAAsJ,MAAA,CAAA1G,WAAA,CAAAC,QAAA,IAAAyG,MAAA,CAAA3J,SAAA,CAAA4J,SAAA,CAAArG,QAAA,CAA0D;IAuBzDpD,EAAA,CAAAW,SAAA,GAAmC;IAAnCX,EAAA,CAAAE,UAAA,SAAAsJ,MAAA,CAAArC,WAAA,CAAAI,SAAA,SAAmC;;;;;IA8JpGvH,EAAA,CAAAQ,cAAA,gBAAkH;IAAAR,EAAA,CAAAS,MAAA,GAAoD;IAAAT,EAAA,CAAAU,YAAA,EAAQ;;;;IAA5DV,EAAA,CAAAW,SAAA,GAAoD;IAApDX,EAAA,CAAAY,iBAAA,CAAA8I,OAAA,CAAAtJ,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAiDtKL,EAAA,CAAAQ,cAAA,gBAAsH;IAAAR,EAAA,CAAAS,MAAA,GAAoD;IAAAT,EAAA,CAAAU,YAAA,EAAQ;;;;IAA5DV,EAAA,CAAAW,SAAA,GAAoD;IAApDX,EAAA,CAAAY,iBAAA,CAAA+I,OAAA,CAAAvJ,WAAA,CAAAC,SAAA,4BAAoD;;;;;IAHlLL,EAAA,CAAAQ,cAAA,cAA6F;IACzFR,EAAA,CAAAC,SAAA,gBAAwE;IACxED,EAAA,CAAAQ,cAAA,eAAmD;IAC/CR,EAAA,CAAAoB,UAAA,IAAAwI,sDAAA,oBAAkL;IACtL5J,EAAA,CAAAU,YAAA,EAAM;;;;IAD2BV,EAAA,CAAAW,SAAA,GAAuF;IAAvFX,EAAA,CAAAE,UAAA,SAAA2J,OAAA,CAAAC,4BAAA,CAAAC,KAAA,IAAAF,OAAA,CAAAC,4BAAA,CAAAE,KAAA,CAAAC,QAAA,CAAuF;;;;;;;;;;;IA5ExIjK,EAAA,CAAAQ,cAAA,cAAkF;IACHR,EAAA,CAAAyD,UAAA,2BAAAyG,0EAAAhG,MAAA;MAAAlE,EAAA,CAAA2D,aAAA,CAAAwG,IAAA;MAAA,MAAAC,OAAA,GAAApK,EAAA,CAAA8D,aAAA;MAAA,OAAA9D,EAAA,CAAA+D,WAAA,CAAAqG,OAAA,CAAAC,4BAAA,GAAAnG,MAAA;IAAA,EAA0C;IACjHlE,EAAA,CAAAQ,cAAA,eAAyE;IAAhCR,EAAA,CAAAyD,UAAA,sBAAA6G,iEAAA;MAAAtK,EAAA,CAAA2D,aAAA,CAAAwG,IAAA;MAAA,MAAAI,OAAA,GAAAvK,EAAA,CAAA8D,aAAA;MAAA,OAAY9D,EAAA,CAAA+D,WAAA,CAAAwG,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAEpExK,EAAA,CAAAQ,cAAA,cAA+B;IACqCR,EAAA,CAAAS,MAAA,GAAsD;IAAAT,EAAA,CAAAQ,cAAA,eAA2B;IAAAR,EAAA,CAAAS,MAAA,QAAC;IAAAT,EAAA,CAAAU,YAAA,EAAO;IACzJV,EAAA,CAAAQ,cAAA,cAA8B;IAGtBR,EAAA,CAAAyD,UAAA,yBAAAgH,2EAAAvG,MAAA;MAAAlE,EAAA,CAAA2D,aAAA,CAAAwG,IAAA;MAAA,MAAAO,OAAA,GAAA1K,EAAA,CAAA8D,aAAA;MAAA,OAAW9D,EAAA,CAAA+D,WAAA,CAAA2G,OAAA,CAAAC,oBAAA,CAAAC,MAAA,GAAA1G,MAAA,CAC9B;IAAA,EAD0D;IAW1ClE,EAAA,CAAAU,YAAA,EAAc;IAIvBV,EAAA,CAAAQ,cAAA,eAAgD;IAC5CR,EAAA,CAAAC,SAAA,iBAAwE;IACxED,EAAA,CAAAQ,cAAA,eAAgE;IAC5DR,EAAA,CAAAoB,UAAA,KAAAyJ,gDAAA,oBAA8K;IAClL7K,EAAA,CAAAU,YAAA,EAAM;IAEVV,EAAA,CAAAQ,cAAA,eAA+B;IAKnBR,EAAA,CAAAyD,UAAA,2BAAAqH,gFAAA5G,MAAA;MAAAlE,EAAA,CAAA2D,aAAA,CAAAwG,IAAA;MAAA,MAAAY,OAAA,GAAA/K,EAAA,CAAA8D,aAAA;MAAA,OAAa9D,EAAA,CAAA+D,WAAA,CAAAgH,OAAA,CAAAJ,oBAAA,CAAAK,UAAA,GAAA9G,MAAA,CAChC;IAAA,EADgE;IAHjDlE,EAAA,CAAAU,YAAA,EAK6B;IAC7BV,EAAA,CAAAQ,cAAA,kBAAwC;IACpCR,EAAA,CAAAS,MAAA,IACJ;IAAAT,EAAA,CAAAU,YAAA,EAAQ;IAEZV,EAAA,CAAAQ,cAAA,eAAiB;IAITR,EAAA,CAAAyD,UAAA,2BAAAwH,gFAAA/G,MAAA;MAAAlE,EAAA,CAAA2D,aAAA,CAAAwG,IAAA;MAAA,MAAAe,OAAA,GAAAlL,EAAA,CAAA8D,aAAA;MAAA,OAAa9D,EAAA,CAAA+D,WAAA,CAAAmH,OAAA,CAAAP,oBAAA,CAAAK,UAAA,GAAA9G,MAAA,CAChC;IAAA,EADgE;IAHjDlE,EAAA,CAAAU,YAAA,EAK8B;IAC9BV,EAAA,CAAAQ,cAAA,kBAAyC;IACrCR,EAAA,CAAAS,MAAA,IACJ;IAAAT,EAAA,CAAAU,YAAA,EAAQ;IAIhBV,EAAA,CAAAQ,cAAA,eAA8F;IAC1BR,EAAA,CAAAS,MAAA,IAAsD;IAAAT,EAAA,CAAAQ,cAAA,gBAA2B;IAAAR,EAAA,CAAAS,MAAA,SAAC;IAAAT,EAAA,CAAAU,YAAA,EAAO;IACzJV,EAAA,CAAAQ,cAAA,eAAgE;IAGxDR,EAAA,CAAAyD,UAAA,yBAAA0H,4EAAAjH,MAAA;MAAAlE,EAAA,CAAA2D,aAAA,CAAAwG,IAAA;MAAA,MAAAiB,OAAA,GAAApL,EAAA,CAAA8D,aAAA;MAAA,OAAW9D,EAAA,CAAA+D,WAAA,CAAAqH,OAAA,CAAAT,oBAAA,CAAAU,kBAAA,GAAAnH,MAAA,CAC9B;IAAA,EADsE;IAStDlE,EAAA,CAAAU,YAAA,EAAc;IAIvBV,EAAA,CAAAoB,UAAA,KAAAkK,8CAAA,mBAKM;IACNtL,EAAA,CAAAQ,cAAA,eAAqE;IACwDR,EAAA,CAAAyD,UAAA,mBAAA8H,mEAAA;MAAAvL,EAAA,CAAA2D,aAAA,CAAAwG,IAAA;MAAA,MAAAqB,OAAA,GAAAxL,EAAA,CAAA8D,aAAA;MAAA,OAAA9D,EAAA,CAAA+D,WAAA,CAAAyH,OAAA,CAAAnB,4BAAA,GAAwC,KAAK;IAAA,EAAC;IAACrK,EAAA,CAAAU,YAAA,EAAW;IACnLV,EAAA,CAAAC,SAAA,qBAA4S;IAChTD,EAAA,CAAAU,YAAA,EAAM;;;;IAjFuHV,EAAA,CAAAW,SAAA,GAA4B;IAA5BX,EAAA,CAAAsJ,UAAA,CAAAtJ,EAAA,CAAAM,eAAA,KAAAmL,GAAA,EAA4B;IAAvJzL,EAAA,CAAAE,UAAA,WAAAwL,MAAA,CAAAtL,WAAA,CAAAC,SAAA,iCAAgE,YAAAqL,MAAA,CAAArB,4BAAA;IAChErK,EAAA,CAAAW,SAAA,GAAkC;IAAlCX,EAAA,CAAAE,UAAA,cAAAwL,MAAA,CAAAC,oBAAA,CAAkC;IAGgC3L,EAAA,CAAAW,SAAA,GAAsD;IAAtDX,EAAA,CAAAY,iBAAA,CAAA8K,MAAA,CAAAtL,WAAA,CAAAC,SAAA,8BAAsD;IAG9GL,EAAA,CAAAW,SAAA,GAAsC;IAAtCX,EAAA,CAAAE,UAAA,YAAAwL,MAAA,CAAAE,0BAAA,CAAsC,UAAAF,MAAA,CAAAf,oBAAA,CAAAC,MAAA,qFAAAc,MAAA,CAAAG,iBAAA,mCAAAH,MAAA,CAAAI,kBAAA;IAmBb9L,EAAA,CAAAW,SAAA,GAAmF;IAAnFX,EAAA,CAAAE,UAAA,SAAAwL,MAAA,CAAAE,0BAAA,CAAA7B,KAAA,IAAA2B,MAAA,CAAAE,0BAAA,CAAA5B,KAAA,CAAAC,QAAA,CAAmF;IAO5GjK,EAAA,CAAAW,SAAA,GAAW;IAAXX,EAAA,CAAAE,UAAA,YAAW,YAAAwL,MAAA,CAAAf,oBAAA,CAAAK,UAAA;IAKXhL,EAAA,CAAAW,SAAA,GACJ;IADIX,EAAA,CAAA0B,kBAAA,MAAAgK,MAAA,CAAAtL,WAAA,CAAAC,SAAA,oCACJ;IAKIL,EAAA,CAAAW,SAAA,GAAW;IAAXX,EAAA,CAAAE,UAAA,YAAW,YAAAwL,MAAA,CAAAf,oBAAA,CAAAK,UAAA;IAKXhL,EAAA,CAAAW,SAAA,GACJ;IADIX,EAAA,CAAA0B,kBAAA,MAAAgK,MAAA,CAAAtL,WAAA,CAAAC,SAAA,qCACJ;IAIuBL,EAAA,CAAAW,SAAA,GAA8D;IAA9DX,EAAA,CAAAmD,UAAA,CAAAuI,MAAA,CAAAf,oBAAA,CAAAK,UAAA,sBAA8D;IACzBhL,EAAA,CAAAW,SAAA,GAAsD;IAAtDX,EAAA,CAAAY,iBAAA,CAAA8K,MAAA,CAAAtL,WAAA,CAAAC,SAAA,8BAAsD;IAG9GL,EAAA,CAAAW,SAAA,GAAwC;IAAxCX,EAAA,CAAAE,UAAA,YAAAwL,MAAA,CAAA5B,4BAAA,CAAwC,UAAA4B,MAAA,CAAAf,oBAAA,CAAAU,kBAAA,gFAAAK,MAAA,CAAAK,4BAAA,CAAA7G,IAAA,CAAAwG,MAAA;IAcH1L,EAAA,CAAAW,SAAA,GAA0C;IAA1CX,EAAA,CAAAE,UAAA,SAAAwL,MAAA,CAAAf,oBAAA,CAAAK,UAAA,MAA0C;IAOtBhL,EAAA,CAAAW,SAAA,GAAuD;IAAvDX,EAAA,CAAAE,UAAA,UAAAwL,MAAA,CAAAtL,WAAA,CAAAC,SAAA,yBAAuD;IACnFL,EAAA,CAAAW,SAAA,GAAqD;IAArDX,EAAA,CAAAE,UAAA,UAAAwL,MAAA,CAAAtL,WAAA,CAAAC,SAAA,uBAAqD,aAAAqL,MAAA,CAAAC,oBAAA,CAAAK,OAAA,IAAAN,MAAA,CAAAE,0BAAA,CAAAI,OAAA,aAAAN,MAAA,CAAAf,oBAAA,CAAAK,UAAA,SAAAU,MAAA,CAAA5B,4BAAA,CAAAkC,OAAA;;;;;;ADtd1G,OAAM,MAAOC,uBAAwB,SAAQnM,aAAa;EAoEtDoM,YAA4CC,cAA8B,EACtDC,eAAgC,EAChCC,eAAgC,EAChCC,WAAwB,EACxBC,QAAkB;IAClC,KAAK,CAACA,QAAQ,CAAC;IALyB,KAAAJ,cAAc,GAAdA,cAAc;IACtC,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IAhD5B,KAAAC,6BAA6B,GAAY,KAAK;IAM9C,KAAAC,cAAc,GAAG5M,SAAS,CAAC6M,WAAW;IAEtC,KAAArC,4BAA4B,GAAY,KAAK;IAO7C,KAAAwB,iBAAiB,GAIb;MACAtJ,MAAM,EAAE1C,SAAS,CAAC8M,WAAW,CAACC,MAAM;MACpC/J,IAAI,EAAEhD,SAAS,CAAC4J,SAAS,CAAClG,QAAQ;MAClCtC,YAAY,EAAE,IAAI,CAAC4L,cAAc,CAACC,QAAQ,CAAC7L;KAC9C;IAED,KAAA6K,kBAAkB,GAAsB,EAAE;IAC1C,KAAAF,0BAA0B,GAAqB,IAAI7L,gBAAgB,EAAE;IACrE,KAAA+J,4BAA4B,GAAqB,IAAI/J,gBAAgB,EAAE;IACvE,KAAA+I,iBAAiB,GAAY,KAAK;IAyDlC,KAAAvC,eAAe,GAAG,IAAI;IACtB,KAAA8B,UAAU,GAAG,EAAE;IACf;IACA,KAAA0E,kBAAkB,GAAe,EAAE;IAcnC,KAAAnF,oBAAoB,GAAG;MAACI,GAAG,EAAG,IAAI;MAAEH,MAAM,EAAG;IAAI,CAAC;IAElD,KAAAV,WAAW,GAAG;MAACC,QAAQ,EAAE,IAAI;MAAEG,SAAS,EAAE;IAAI,CAAC;IAE/C,KAAAT,cAAc,GAAS,IAAI;IAE3B,KAAAgG,QAAQ,GAAG,IAAI,CAACD,cAAc,CAACC,QAAQ;IAgvBpB,KAAAjN,SAAS,GAAGA,SAAS;EA1yBxC;EA2DAmN,QAAQA,CAAA;IACJ,IAAIC,EAAE,GAAG,IAAI;IACb,IAAI,CAACjK,cAAc,GAAGnD,SAAS,CAAC4J,SAAS;IACzC,IAAI,CAAC1G,QAAQ,GAAG,IAAI,CAAC8J,cAAc,CAACC,QAAQ,CAACjK,IAAI;IACjD,IAAI,CAACqK,KAAK,GAAG,CAAC;MAAEC,KAAK,EAAE,IAAI,CAAC/M,WAAW,CAACC,SAAS,CAAC,yBAAyB;IAAC,CAAE,EAAE;MAAE8M,KAAK,EAAE,IAAI,CAAC/M,WAAW,CAACC,SAAS,CAAC,yBAAyB;IAAC,CAAE,CAAC;IACjJ,IAAI,CAAC+M,IAAI,GAAG;MAAEC,IAAI,EAAE,YAAY;MAAEC,UAAU,EAAE;IAAG,CAAE;IACnD,IAAI,CAACC,UAAU,GAAG;MACdpM,QAAQ,EAAE,IAAI;MACd0B,IAAI,EAAE,IAAI;MACVH,KAAK,EAAE,IAAI;MACXzB,YAAY,EAAE,IAAI;MAClBuB,QAAQ,EAAE,IAAI;MACdD,MAAM,EAAE,IAAI;MACZiL,QAAQ,EAAG;KACd;IACD,IAAI,CAAC1K,WAAW,GAAG;MACf2K,WAAW,EAAE,IAAI;MACjBjL,QAAQ,EAAE,IAAI;MACdE,KAAK,EAAE,IAAI;MACXD,KAAK,EAAE,IAAI;MACXM,QAAQ,EAAE,IAAI;MACd2K,QAAQ,EAAE,IAAI;MACdlK,KAAK,EAAE,IAAI;MACXb,WAAW,EAAE,IAAI;MACjBW,OAAO,EAAE,IAAI;MACbqK,SAAS,EAAE;KACd;IACD,IAAG,IAAI,CAAC5K,QAAQ,IAAIlD,SAAS,CAAC4J,SAAS,CAACxG,KAAK,EAAC;MAC1C,IAAI,CAAC2K,cAAc,GAAG,CAClB;QAACC,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;QAACyN,KAAK,EAACjO,SAAS,CAAC4J,SAAS,CAACxG;MAAK,CAAC,EAC5F;QAAC4K,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;QAACyN,KAAK,EAACjO,SAAS,CAAC4J,SAAS,CAACrG;MAAQ,CAAC,EAClG;QAACyK,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;QAACyN,KAAK,EAACjO,SAAS,CAAC4J,SAAS,CAACpG;MAAQ,CAAC,EAClG;QAACwK,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;QAACyN,KAAK,EAACjO,SAAS,CAAC4J,SAAS,CAAClG;MAAQ;MACjG;MAAA,CACH;KACJ,MAAK,IAAG,IAAI,CAACR,QAAQ,IAAIlD,SAAS,CAAC4J,SAAS,CAACpG,QAAQ,EAAC;MACnD,IAAI,CAACuK,cAAc,GAAG,CAClB;QAACC,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;QAACyN,KAAK,EAACjO,SAAS,CAAC4J,SAAS,CAACrG;MAAQ,CAAC;MAClG;MACA;QAACyK,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;QAACyN,KAAK,EAACjO,SAAS,CAAC4J,SAAS,CAAClG;MAAQ;MACjG;MAAA,CACH;KACJ,MAAK,IAAG,IAAI,CAACR,QAAQ,IAAIlD,SAAS,CAAC4J,SAAS,CAAClG,QAAQ,EAAC;MACnD,IAAI,CAACqK,cAAc,GAAG,CAClB;QAACC,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;QAACyN,KAAK,EAACjO,SAAS,CAAC4J,SAAS,CAACrG;MAAQ;MACjG;MACA;MAAA,CACH;KACJ,MAAK,IAAG,IAAI,CAACL,QAAQ,IAAIlD,SAAS,CAAC4J,SAAS,CAACrG,QAAQ,EAAC;MACnD,IAAI,CAACwK,cAAc,GAAG,CAClB;QAACC,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;QAACyN,KAAK,EAACjO,SAAS,CAAC4J,SAAS,CAACrG;MAAQ,CAAC,CACrG;KACJ,MAAK,IAAG,IAAI,CAACL,QAAQ,IAAIlD,SAAS,CAAC4J,SAAS,CAACvG,MAAM,EAAC;MACjD,IAAI,CAAC0K,cAAc,GAAG,CAClB;QAACC,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;QAACyN,KAAK,EAACjO,SAAS,CAAC4J,SAAS,CAACvG;MAAM,CAAC,CACnG;;IAEL,IAAI,CAAC6K,UAAU,GAAG,CACd;MAACF,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;MAACyN,KAAK,EAACjO,SAAS,CAAC8M,WAAW,CAACC;IAAM,CAAE,EACnG;MAACiB,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAACyN,KAAK,EAACjO,SAAS,CAAC8M,WAAW,CAACqB;IAAQ,CAAE,CAC1G;IACD,IAAI,CAAC1F,kBAAkB,GAAG;MACtB1D,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRO,MAAM,EAAE;KACX;IACD,IAAI,CAACmD,kBAAkB,GAAG,CACtB;MACIsF,IAAI,EAAE,KAAK;MACXI,GAAG,EAAE,MAAM;MACXpJ,IAAI,EAAE,KAAK;MACXqJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIP,IAAI,EAAE,QAAQ;MACdI,GAAG,EAAE,QAAQ;MACbpJ,IAAI,EAAE,KAAK;MACXqJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IAED,IAAI,CAAC5F,eAAe,GAAG;MACnB6F,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IAED,IAAI,CAAC7F,mBAAmB,GAAG;MACvB8F,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;IACD,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACrC,WAAW,CAACsC,KAAK,CAAC,IAAI,CAACrB,UAAU,CAAC;IAChE,IAAI,CAACsB,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,IAAI,GAAG,iBAAiB;IAE7B,IAAI,CAACC,WAAW,GAAG;MACfV,gBAAgB,EAAE,IAAI;MACtBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE,KAAK;MAC1BQ,MAAM,EAAE,CACJ;QACI7B,IAAI,EAAE,iBAAiB;QACvB8B,OAAO,EAAE,IAAI,CAAC/O,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;QACzD+O,IAAI,EAAE,SAAAA,CAASC,EAAE,EAAEC,IAAI;UACnBrC,EAAE,CAACsC,MAAM,CAACC,QAAQ,CAAC,CAAC,kBAAkBH,EAAE,EAAE,CAAC,CAAC;QAChD,CAAC;QACDI,UAAU,EAAE,SAAAA,CAASJ,EAAE,EAAEC,IAAI;UACzB,OAAOrC,EAAE,CAACyC,eAAe,CAAC,CAAC7P,SAAS,CAAC6M,WAAW,CAACiD,OAAO,CAACC,MAAM,CAAC,CAAC;QACrE;OACH,EACD;QACIvC,IAAI,EAAE,YAAY;QAClB8B,OAAO,EAAE,IAAI,CAAC/O,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;QACjE+O,IAAI,EAAE,SAAAA,CAASC,EAAE,EAAEC,IAAI;UACnBrC,EAAE,CAAC4C,oBAAoB,CAACC,OAAO,CAC3B7C,EAAE,CAAC7M,WAAW,CAACC,SAAS,CAAC,gDAAgD,CAAC,EAC1E4M,EAAE,CAAC7M,WAAW,CAACC,SAAS,CAAC,2CAA2C,CAAC,EACrE;YACI0P,EAAE,EAACA,CAAA,KAAI;cACH9C,EAAE,CAACd,cAAc,CAAC6D,YAAY,CAACX,EAAE,EAAGY,QAAQ,IAAG;gBAC3ChD,EAAE,CAAC4C,oBAAoB,CAACK,OAAO,CAACjD,EAAE,CAAC7M,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC,CAAC;gBAC/F4M,EAAE,CAACkD,MAAM,CAAClD,EAAE,CAAC6B,UAAU,EAAE7B,EAAE,CAAC8B,QAAQ,EAAE9B,EAAE,CAAC+B,IAAI,EAAE/B,EAAE,CAACM,UAAU,CAAC;cACjE,CAAC,CAAC;YACN,CAAC;YACD6C,MAAM,EAAEA,CAAA,KAAI;cACR;YAAA;WAEP,CACJ;QACL,CAAC;QACDX,UAAU,EAAE,SAAAA,CAASJ,EAAE,EAAEC,IAAI;UACzB,OAAOA,IAAI,CAAC/M,MAAM,IAAI1C,SAAS,CAAC8M,WAAW,CAACC,MAAM,IAAIK,EAAE,CAACyC,eAAe,CAAC,CAAC7P,SAAS,CAAC6M,WAAW,CAACiD,OAAO,CAACU,aAAa,CAAC,CAAC;UAAC;QAC5H;OACH,EACD;QACIhD,IAAI,EAAE,iBAAiB;QACvB8B,OAAO,EAAE,IAAI,CAAC/O,WAAW,CAACC,SAAS,CAAC,4BAA4B,CAAC;QACjE+O,IAAI,EAAE,SAAAA,CAASC,EAAE,EAAEC,IAAI;UACnBrC,EAAE,CAAC4C,oBAAoB,CAACC,OAAO,CAC3B7C,EAAE,CAAC7M,WAAW,CAACC,SAAS,CAAC,gDAAgD,CAAC,EAC1E4M,EAAE,CAAC7M,WAAW,CAACC,SAAS,CAAC,2CAA2C,CAAC,EACrE;YACI0P,EAAE,EAACA,CAAA,KAAI;cACH9C,EAAE,CAACd,cAAc,CAAC6D,YAAY,CAACX,EAAE,EAAGY,QAAQ,IAAG;gBAC3ChD,EAAE,CAAC4C,oBAAoB,CAACK,OAAO,CAACjD,EAAE,CAAC7M,WAAW,CAACC,SAAS,CAAC,oCAAoC,CAAC,CAAC;gBAC/F4M,EAAE,CAACkD,MAAM,CAAClD,EAAE,CAAC6B,UAAU,EAAE7B,EAAE,CAAC8B,QAAQ,EAAE9B,EAAE,CAAC+B,IAAI,EAAE/B,EAAE,CAACM,UAAU,CAAC;cACjE,CAAC,CAAC;YACN,CAAC;YACD6C,MAAM,EAAEA,CAAA,KAAI;cACR;YAAA;WAEP,CACJ;QACL,CAAC;QACDX,UAAU,EAAE,SAAAA,CAASJ,EAAE,EAAEC,IAAI;UACzB,OAAOA,IAAI,CAAC/M,MAAM,IAAI1C,SAAS,CAAC8M,WAAW,CAACqB,QAAQ,IAAIf,EAAE,CAACyC,eAAe,CAAC,CAAC7P,SAAS,CAAC6M,WAAW,CAACiD,OAAO,CAACU,aAAa,CAAC,CAAC;UAAC;QAC9H;OACH;MACD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACIhD,IAAI,EAAE,YAAY;QAClB8B,OAAO,EAAE,IAAI,CAAC/O,WAAW,CAACC,SAAS,CAAC,gCAAgC,CAAC;QACrE+O,IAAI,EAAE,SAAAA,CAASC,EAAE,EAAEC,IAAI;UACnBrC,EAAE,CAAC5C,4BAA4B,GAAG,IAAI;UACtC4C,EAAE,CAACtC,oBAAoB,GAAG;YACtBC,MAAM,EAAE,IAAI;YACZS,kBAAkB,EAAE,EAAE;YACtBL,UAAU,EAAE;WACf;UACDiC,EAAE,CAACqD,cAAc,GAAGhB,IAAI;UACxBrC,EAAE,CAACnB,kBAAkB,GAAG,CAACuD,EAAE,CAAC;UAC5BpC,EAAE,CAACpB,iBAAiB,CAAC5K,YAAY,GAAGqO,IAAI,CAACrO,YAAY;UACrDgM,EAAE,CAACtB,oBAAoB,GAAGsB,EAAE,CAACX,WAAW,CAACsC,KAAK,CAAC3B,EAAE,CAACtC,oBAAoB,CAAC;UACvEsC,EAAE,CAACrB,0BAA0B,CAAC2E,MAAM,EAAE;UACtCtD,EAAE,CAACnD,4BAA4B,CAACyG,MAAM,EAAE;QAC5C,CAAC;QACDd,UAAU,EAAE,SAAAA,CAASJ,EAAE,EAAEC,IAAI;UACzB,OAAOA,IAAI,CAACzM,IAAI,IAAIhD,SAAS,CAAC4J,SAAS,CAAClG,QAAQ,IAAI+L,IAAI,CAAC/M,MAAM,IAAI1C,SAAS,CAAC8M,WAAW,CAACqB,QAAQ,IAAIf,EAAE,CAACyC,eAAe,CAAC,CAAC7P,SAAS,CAAC6M,WAAW,CAACiD,OAAO,CAACa,mBAAmB,CAAC,CAAC;UAAC;QACjL;OACH,EACD;QACInD,IAAI,EAAE,aAAa;QACnB8B,OAAO,EAAE,IAAI,CAAC/O,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;QAC3D+O,IAAI,EAAE,SAAAA,CAASC,EAAE,EAAEC,IAAI;UACnBrC,EAAE,CAAC4C,oBAAoB,CAACC,OAAO,CAC3B7C,EAAE,CAAC7M,WAAW,CAACC,SAAS,CAAC,0CAA0C,CAAC,EACpE4M,EAAE,CAAC7M,WAAW,CAACC,SAAS,CAAC,qCAAqC,CAAC,EAC/D;YACI0P,EAAE,EAACA,CAAA,KAAI;cACH9C,EAAE,CAACd,cAAc,CAACsE,UAAU,CAACpB,EAAE,EAAGY,QAAQ,IAAG;gBACzChD,EAAE,CAAC4C,oBAAoB,CAACK,OAAO,CAACjD,EAAE,CAAC7M,WAAW,CAACC,SAAS,CAAC,8BAA8B,CAAC,CAAC;gBACzF4M,EAAE,CAACkD,MAAM,CAAClD,EAAE,CAAC6B,UAAU,EAAE7B,EAAE,CAAC8B,QAAQ,EAAE9B,EAAE,CAAC+B,IAAI,EAAE/B,EAAE,CAACM,UAAU,CAAC;cACjE,CAAC,CAAC;YACN,CAAC;YACD6C,MAAM,EAAEA,CAAA,KAAI;cACR;YAAA;WAEP,CACJ;QACL,CAAC;QACDX,UAAU,EAAE,SAAAA,CAASJ,EAAE,EAAEC,IAAI;UACzB,IAAIA,IAAI,CAAChO,cAAc,KAAK,IAAI,IAAI2L,EAAE,CAACyC,eAAe,CAAC,CAAC7P,SAAS,CAAC6M,WAAW,CAACiD,OAAO,CAACe,MAAM,CAAC,CAAC,EAAE;YAC5F,OAAO,IAAI;WACd,MAAM,IAAIpB,IAAI,CAAChO,cAAc,IAAI,IAAI,IAAIgO,IAAI,CAACqB,UAAU,KAAK,IAAI,IAAI1D,EAAE,CAACyC,eAAe,CAAC,CAAC7P,SAAS,CAAC6M,WAAW,CAACiD,OAAO,CAACe,MAAM,CAAC,CAAC,EAAE;YAC9H,OAAO,IAAI;WACd,MAAM;YACH,OAAO,KAAK;;QAEpB;OACH;KAER,EACD,IAAI,CAACE,OAAO,GAAG,CACX;MACI/C,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1D4N,GAAG,EAAE,UAAU;MACfpJ,IAAI,EAAE,OAAO;MACbqJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZyC,KAAK,EAAC;QACFC,MAAM,EAAE,SAAS;QACxBC,KAAK,EAAE;OACH;MACDC,SAASA,CAAC3B,EAAE,EAAEC,IAAI;QACdrC,EAAE,CAACgE,SAAS,GAAG5B,EAAE;QACjBpC,EAAE,CAACiE,SAAS,EAAE;QACdjE,EAAE,CAACnE,iBAAiB,GAAG,IAAI;MAC/B;KACH,EACD;MACI+E,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1D4N,GAAG,EAAE,UAAU;MACfpJ,IAAI,EAAE,OAAO;MACbqJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZ+C,aAAa,EAAC,IAAI;MAClBC,gBAAgBA,CAAA;QACZ,OAAO,CAAC,aAAa,EAAE,wBAAwB,EAAE,cAAc,EAAE,iBAAiB,CAAC;MACvF;KACH,EACD;MACIvD,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1D4N,GAAG,EAAE,MAAM;MACXpJ,IAAI,EAAE,OAAO;MACbqJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZiD,eAAeA,CAACvD,KAAK;QACjB,IAAGA,KAAK,IAAIjO,SAAS,CAAC4J,SAAS,CAACxG,KAAK,EAAC;UAClC,OAAOgK,EAAE,CAAC7M,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;SAC5D,MAAK,IAAGyN,KAAK,IAAIjO,SAAS,CAAC4J,SAAS,CAACrG,QAAQ,EAAC;UAC3C,OAAO6J,EAAE,CAAC7M,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;SAC/D,MAAK,IAAGyN,KAAK,IAAIjO,SAAS,CAAC4J,SAAS,CAACpG,QAAQ,EAAC;UAC3C,OAAO4J,EAAE,CAAC7M,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;SAC/D,MAAK,IAAGyN,KAAK,IAAIjO,SAAS,CAAC4J,SAAS,CAAClG,QAAQ,EAAC;UAC3C,OAAO0J,EAAE,CAAC7M,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;SAC/D,MAAK,IAAGyN,KAAK,IAAIjO,SAAS,CAAC4J,SAAS,CAACvG,MAAM,EAAC;UACzC,OAAO+J,EAAE,CAAC7M,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;SAC7D,MAAI;UACD,OAAO,EAAE;;MAEjB;KACH,EACD;MACIwN,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,qBAAqB,CAAC;MACvD4N,GAAG,EAAE,OAAO;MACZpJ,IAAI,EAAE,OAAO;MACbqJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIP,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;MAC1D4N,GAAG,EAAE,cAAc;MACnBpJ,IAAI,EAAE,OAAO;MACbqJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZiD,eAAeA,CAACvD,KAAK;QACjB,IAAG,CAACb,EAAE,CAACqE,YAAY,EAAE,OAAOxD,KAAK;QACjC,KAAI,IAAIyD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtE,EAAE,CAACqE,YAAY,CAACE,MAAM,EAAED,CAAC,EAAE,EAAC;UAC3C,IAAGtE,EAAE,CAACqE,YAAY,CAACC,CAAC,CAAC,CAACE,IAAI,IAAI3D,KAAK,EAAC;YAChC,OAAO,GAAGb,EAAE,CAACqE,YAAY,CAACC,CAAC,CAAC,CAAC1D,IAAI,KAAKC,KAAK,GAAG;;;QAGtD,OAAO,EAAE;MACb;KACH,EACD;MACID,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,oBAAoB,CAAC;MACtD4N,GAAG,EAAE,aAAa;MAClBpJ,IAAI,EAAE,OAAO;MACbqJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIP,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC;MACxD4N,GAAG,EAAE,QAAQ;MACbpJ,IAAI,EAAE,OAAO;MACbqJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZiD,eAAeA,CAACvD,KAAK;QACjB,IAAGA,KAAK,IAAIjO,SAAS,CAAC8M,WAAW,CAACC,MAAM,EAAC;UACrC,OAAOK,EAAE,CAAC7M,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;SAC/D,MAAK,IAAGyN,KAAK,IAAIjO,SAAS,CAAC8M,WAAW,CAACqB,QAAQ,EAAC;UAC7C,OAAOf,EAAE,CAAC7M,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;SACjE,MAAI;UACD,OAAO,EAAE;;MAEjB;KACH,CACJ;IACD,IAAI,CAACqR,eAAe,EAAE;IACtB,IAAI,CAACvB,MAAM,CAAC,IAAI,CAACrB,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACzB,UAAU,CAAC;IACvE,IAAI,CAACnJ,wBAAwB,GAAG;MAC5BC,OAAO,EAAE,IAAI;MACbsN,aAAa,EAAEC,MAAM,CAAC,IAAI,CAACX,SAAS;KACvC;IACD,IAAI,CAACnM,kBAAkB,GAAG,CACtB;MACI+I,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/D4N,GAAG,EAAE,MAAM;MACXpJ,IAAI,EAAE,KAAK;MACXqJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIP,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/D4N,GAAG,EAAE,MAAM;MACXpJ,IAAI,EAAE,KAAK;MACXqJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IACD,IAAI,CAACrJ,eAAe,GAAG;MACnBsJ,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD,IAAI,CAAC3J,kBAAkB,GAAG;MACtBC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRO,MAAM,EAAE;KACX;IACD,IAAI,CAACJ,mBAAmB,GAAG;MACvBuJ,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;IACD,IAAI,CAAC/I,wBAAwB,GAAG;MAC5BtB,OAAO,EAAE,IAAI;MACbwN,WAAW,EAAE,EAAE;MACfF,aAAa,EAAE,CAAC;KACnB;IACD,IAAI,CAAC3L,kBAAkB,GAAG,CACtB;MACI6H,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/D4N,GAAG,EAAE,cAAc;MACnBpJ,IAAI,EAAE,KAAK;MACXqJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIP,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/D4N,GAAG,EAAE,cAAc;MACnBpJ,IAAI,EAAE,KAAK;MACXqJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,EACD;MACIP,IAAI,EAAE,IAAI,CAACzN,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;MAC/D4N,GAAG,EAAE,cAAc;MACnBpJ,IAAI,EAAE,KAAK;MACXqJ,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;KACX,CACJ;IACD,IAAI,CAACnI,eAAe,GAAG;MACnBoI,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE;KACV;IACD,IAAI,CAACvI,kBAAkB,GAAG;MACtBnB,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRO,MAAM,EAAE;KACX;IACD,IAAI,CAACc,mBAAmB,GAAG;MACvBqI,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,IAAI;MAClBC,mBAAmB,EAAE;KACxB;EACL;EAEAoD,qBAAqBA,CAAA;IACjB,IAAG,IAAI,CAACtF,6BAA6B,IAAI,KAAK,EAAC;MAC3C,IAAI,CAACuF,eAAe,GAAG,IAAI;;EAEnC;EAEAC,cAAcA,CAAA;IACV,IAAI,CAAClD,UAAU,GAAG,CAAC;IACnB,IAAI,CAACvB,UAAU,CAACC,QAAQ,GAAG,IAAI;IAC/B,IAAI,CAAC2C,MAAM,CAAC,IAAI,CAACrB,UAAU,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACzB,UAAU,CAAC;EAC3E;EAEA4C,MAAMA,CAACvL,IAAI,EAAEqN,KAAK,EAAEjD,IAAI,EAAEkD,MAAM;IAC5B,IAAIjF,EAAE,GAAG,IAAI;IACb,IAAI,CAAC6B,UAAU,GAAGlK,IAAI;IACtB,IAAI,CAACmK,QAAQ,GAAGkD,KAAK;IACrB,IAAI,CAACjD,IAAI,GAAGA,IAAI;IAChB,IAAImD,UAAU,GAAG;MACbvN,IAAI;MACJC,IAAI,EAAEoN,KAAK;MACXjD;KACH;IACDoD,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC9E,UAAU,CAAC,CAAC+E,OAAO,CAACrE,GAAG,IAAG;MACvC,IAAG,IAAI,CAACV,UAAU,CAACU,GAAG,CAAC,IAAI,IAAI,EAAC;QAC5BkE,UAAU,CAAClE,GAAG,CAAC,GAAG,IAAI,CAACV,UAAU,CAACU,GAAG,CAAC;;IAE9C,CAAC,CAAC;IACFhB,EAAE,CAAC4C,oBAAoB,CAAC0C,MAAM,EAAE;IAChC,IAAI,CAACpG,cAAc,CAACgE,MAAM,CAACgC,UAAU,EAAGlC,QAAQ,IAAG;MAC/ChD,EAAE,CAACuF,OAAO,GAAG;QACTnE,OAAO,EAAE4B,QAAQ,CAAC5B,OAAO;QACzBC,KAAK,EAAE2B,QAAQ,CAACwC;OACnB;IACL,CAAC,EAAE,IAAI,EAAE,MAAI;MACTxF,EAAE,CAAC4C,oBAAoB,CAAC6C,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAC,iBAAiBA,CAACC,KAAgC;IAC9C,IAAIC,WAAW,GAAGD,KAAK,CAACE,KAAK;IAC7B,IAAI,CAACC,YAAY,GAAG,CAChB;MACI1D,EAAE,EAAE,CAAC;MACLxB,IAAI,EAAE;KACT,EACD;MACIwB,EAAE,EAAE,CAAC;MACLxB,IAAI,EAAE;KACT,EACD;MACIwB,EAAE,EAAE,CAAC;MACLxB,IAAI,EAAE;KACT,EACD;MACIwB,EAAE,EAAE,CAAC;MACLxB,IAAI,EAAE;KACT,CACJ,CAACmF,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACpF,IAAI,CAACqF,OAAO,CAACL,WAAW,CAAC,IAAI,CAAC,CAAC;EACrD;EAEAnB,eAAeA,CAAA;IACX,IAAI,CAACvF,cAAc,CAACuF,eAAe,CAAEzB,QAAQ,IAAG;MAC5C,IAAI,CAACqB,YAAY,GAAGrB,QAAQ,CAACkD,GAAG,CAACF,EAAE,IAAG;QAClC,OAAO;UACH,GAAGA,EAAE;UACLG,OAAO,EAAE,GAAGH,EAAE,CAACxB,IAAI,MAAMwB,EAAE,CAACpF,IAAI;SACnC;MACL,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEA6B,eAAeA,CAAC2D,WAAqB;IACjC,OAAO,IAAI,CAACC,WAAW,CAACD,WAAW,CAAC;EACxC;EAEAE,iBAAiBA,CAAA,GAEjB;EAEAxH,4BAA4BA,CAACmG,MAAM,EAAEsB,QAAQ;IACzC,IAAG,IAAI,CAAClD,cAAc,EAAC;MACnB,IAAImD,CAAC,GAAG;QACJC,QAAQ,EAAExB,MAAM,CAAC1P,QAAQ;QACzBmR,YAAY,EAAE,IAAI,CAACrD,cAAc,CAACjB,EAAE;QACpCzK,IAAI,EAAEsN,MAAM,CAACtN,IAAI;QACjBC,IAAI,EAAEqN,MAAM,CAACrN,IAAI;QACjBmK,IAAI,EAAEkD,MAAM,CAAClD;OAChB;MACD,IAAI,CAAC7C,cAAc,CAACyH,uBAAuB,CAACH,CAAC,EAAED,QAAQ,CAAC;;EAEhE;EAEAhJ,gBAAgBA,CAAA;IACZ,IAAIqJ,IAAI,GAAG;MACPC,SAAS,EAAE,IAAI,CAACnJ,oBAAoB,CAACC,MAAM;MAC3CmJ,SAAS,EAAE,IAAI,CAACzD,cAAc,CAACjB,EAAE;MACjChE,kBAAkB,EAAE,IAAI,CAACV,oBAAoB,CAACK,UAAU,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,CAACL,oBAAoB,CAACU;KACpG;IACD,IAAI4B,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC4C,oBAAoB,CAAC0C,MAAM,EAAE;IAChC,IAAI,CAACpG,cAAc,CAAC3B,gBAAgB,CAACqJ,IAAI,EAAG5D,QAAQ,IAAG;MACnDhD,EAAE,CAAC5C,4BAA4B,GAAG,KAAK;MACvC4C,EAAE,CAAC4C,oBAAoB,CAACK,OAAO,CAACjD,EAAE,CAAC7M,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC,CAAC;IACvF,CAAC,EAAE,IAAI,EAAE,MAAI;MACT4M,EAAE,CAAC4C,oBAAoB,CAAC6C,OAAO,EAAE;IACrC,CAAC,CAAC;EAEN;EAEAxB,SAASA,CAAA;IACL,IAAIjE,EAAE,GAAG,IAAI;IACbA,EAAE,CAAC4C,oBAAoB,CAAC0C,MAAM,EAAE;IAChC,IAAI,CAACpG,cAAc,CAAC6H,OAAO,CAACpC,MAAM,CAAC3E,EAAE,CAACgE,SAAS,CAAC,EAAGhB,QAAQ,IAAG;MAC1DhD,EAAE,CAAClM,eAAe,GAAGkP,QAAQ;MAC7BhD,EAAE,CAACnK,WAAW,CAAC2K,WAAW,GAAGwC,QAAQ,CAAC9O,QAAQ;MAC9C8L,EAAE,CAACnK,WAAW,CAACN,QAAQ,GAAGyN,QAAQ,CAACzN,QAAQ;MAC3CyK,EAAE,CAACnK,WAAW,CAACJ,KAAK,GAAGuN,QAAQ,CAACvN,KAAK;MACrCuK,EAAE,CAACnK,WAAW,CAACH,WAAW,GAAGsN,QAAQ,CAACtN,WAAW;MACjDsK,EAAE,CAACnK,WAAW,CAACL,KAAK,GAAGwN,QAAQ,CAACxN,KAAK;MACrCwK,EAAE,CAACnK,WAAW,CAAC4K,QAAQ,GAAGuC,QAAQ,CAAChP,YAAY;MAC/CgM,EAAE,CAACnK,WAAW,CAACC,QAAQ,GAAGkN,QAAQ,CAACpN,IAAI;MACvCoK,EAAE,CAACgH,WAAW,CAAC,KAAK,CAAC;MACrB,IAAIhH,EAAE,CAACnK,WAAW,CAACC,QAAQ,IAAIlD,SAAS,CAAC4J,SAAS,CAACrG,QAAQ,EAAE;QACzD6J,EAAE,CAACiH,kCAAkC,EAAE;QACvCjH,EAAE,CAAC7I,wBAAwB,CAACuN,aAAa,GAAGC,MAAM,CAAC3E,EAAE,CAACgE,SAAS,CAAC;QAChEhE,EAAE,CAACtH,wBAAwB,CAACkM,WAAW,GAAG,CAAC5E,EAAE,CAAClM,eAAe,CAAC4M,SAAS,IAAG,EAAE,EAAEwF,GAAG,CAACgB,QAAQ,IAAIA,QAAQ,CAACC,UAAU,CAAC;QAClHnH,EAAE,CAACtH,wBAAwB,CAACgM,aAAa,GAAGC,MAAM,CAAC3E,EAAE,CAACgE,SAAS,CAAC;;MAEpEhE,EAAE,CAACnG,cAAc,GAAGuN,MAAM,CAACpE,QAAQ,CAACqE,SAAS,CAAC;MAC9CrH,EAAE,CAACF,kBAAkB,GAAGkD,QAAQ,CAACsE,SAAS,GAAEtE,QAAQ,CAACsE,SAAS,CAACpB,GAAG,CAACF,EAAE,KAAI;QAAC5D,EAAE,EAAE4D;MAAE,CAAC,CAAC,CAAC,GAAG,CAAC;QAAC5D,EAAE,EAAC,CAAC;MAAE,CAAC,CAAC;MAChGpC,EAAE,CAAC9F,WAAW,CAACI,SAAS,GAAG0I,QAAQ,CAACuE,QAAQ;MAC5CvH,EAAE,CAAC9F,WAAW,CAACC,QAAQ,GAAG6I,QAAQ,CAAC9O,QAAQ;IAC/C,CAAC,EAAE,IAAI,EAAE,MAAI;MACT8L,EAAE,CAAC4C,oBAAoB,CAAC6C,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAuB,WAAWA,CAACQ,OAAO;IACf,IAAI,CAACtI,cAAc,CAAC8H,WAAW,CAAC,IAAI,CAACnR,WAAW,CAACC,QAAQ,EAAGkN,QAAQ,IAAG;MACnE,IAAI,CAACyE,QAAQ,GAAGzE,QAAQ,CAACkD,GAAG,CAACF,EAAE,IAAG;QAC9B,OAAO;UACH5D,EAAE,EAAE4D,EAAE,CAAC5D,EAAE;UACTxB,IAAI,EAAEoF,EAAE,CAACpF;SACZ;MACL,CAAC,CAAC;MACF,IAAG4G,OAAO,EAAC;QACP,IAAI,CAAC3R,WAAW,CAACU,KAAK,GAAG,IAAI;OAChC,MAAI;QACD,IAAI,CAACV,WAAW,CAACU,KAAK,GAAG,IAAI,CAACkR,QAAQ,CAAC1B,MAAM,CAACC,EAAE,IAAI,CAAC,IAAI,CAAClS,eAAe,CAACyC,KAAK,IAAE,EAAE,EAAEmR,QAAQ,CAAC1B,EAAE,CAAC5D,EAAE,CAAC,CAAC;;IAE7G,CAAC,CAAC;EACN;EAEA/M,mBAAmBA,CAACwL,KAAK;IACrB,IAAGA,KAAK,IAAIjO,SAAS,CAAC8M,WAAW,CAACC,MAAM,EAAC;MACrC,OAAO,IAAI,CAACxM,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;KACjE,MAAK,IAAGyN,KAAK,IAAIjO,SAAS,CAAC8M,WAAW,CAACqB,QAAQ,EAAC;MAC7C,OAAO,IAAI,CAAC5N,WAAW,CAACC,SAAS,CAAC,6BAA6B,CAAC;KACnE,MAAI;MACD,OAAO,EAAE;;EAEjB;EAEAuC,iBAAiBA,CAACkL,KAAK;IACnB,IAAGA,KAAK,IAAIjO,SAAS,CAAC4J,SAAS,CAACxG,KAAK,EAAC;MAClC,OAAO,IAAI,CAAC7C,WAAW,CAACC,SAAS,CAAC,wBAAwB,CAAC;KAC9D,MAAK,IAAGyN,KAAK,IAAIjO,SAAS,CAAC4J,SAAS,CAACrG,QAAQ,EAAC;MAC3C,OAAO,IAAI,CAAChD,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;KACjE,MAAK,IAAGyN,KAAK,IAAIjO,SAAS,CAAC4J,SAAS,CAACpG,QAAQ,EAAC;MAC3C,OAAO,IAAI,CAACjD,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;KACjE,MAAK,IAAGyN,KAAK,IAAIjO,SAAS,CAAC4J,SAAS,CAAClG,QAAQ,EAAC;MAC3C,OAAO,IAAI,CAACnD,WAAW,CAACC,SAAS,CAAC,2BAA2B,CAAC;KACjE,MAAK,IAAGyN,KAAK,IAAIjO,SAAS,CAAC4J,SAAS,CAACvG,MAAM,EAAC;MACzC,OAAO,IAAI,CAAC9C,WAAW,CAACC,SAAS,CAAC,yBAAyB,CAAC;KAC/D,MAAI;MACD,OAAO,EAAE;;EAEjB;EACA4I,WAAWA,CAAC2J,KAAK;IACb,MAAMgC,OAAO,GAAGhC,KAAK,CAACiC,aAAa,CAACC,MAAM,CAACC,SAAS;IACpD,IAAI9H,EAAE,GAAG,IAAI;IACb,IAAI2F,KAAK,IAAIgC,OAAO,CAACD,QAAQ,CAAC,IAAI,CAACvU,WAAW,CAACC,SAAS,CAAC,uBAAuB,CAAC,CAAC,EAAE;MAChF4M,EAAE,CAAC9E,gBAAgB,EAAE;KACxB,MAAM,IAAIyK,KAAK,IAAIgC,OAAO,CAACD,QAAQ,CAAC,IAAI,CAACvU,WAAW,CAACC,SAAS,CAAC,sBAAsB,CAAC,CAAC,EAAE;MACtF4M,EAAE,CAACzH,gBAAgB,EAAE;KACxB,MAAM,IAAIoN,KAAK,IAAIgC,OAAO,CAACD,QAAQ,CAAC,IAAI,CAACvU,WAAW,CAACC,SAAS,CAAC,0BAA0B,CAAC,CAAC,EAAE;MAC1F4M,EAAE,CAACjJ,gBAAgB,EAAE;;EAE7B;EACAA,gBAAgBA,CAACgR,IAAK;IAClB,IAAI/H,EAAE,GAAG,IAAI;IACb,IAAG+H,IAAI,EAAE;MACL/H,EAAE,CAACtI,kBAAkB,CAACC,IAAI,GAAG,CAAC;;IAElCqI,EAAE,CAAChI,cAAc,CAACgI,EAAE,CAACtI,kBAAkB,CAACC,IAAI,EAAEqI,EAAE,CAACtI,kBAAkB,CAACE,IAAI,EAAEoI,EAAE,CAACtI,kBAAkB,CAACS,MAAM,EAAE6H,EAAE,CAAC7I,wBAAwB,CAAC;EACxI;EACAa,cAAcA,CAACL,IAAI,EAAEqN,KAAK,EAAEjD,IAAI,EAAEkD,MAAM;IACpC,IAAIjF,EAAE,GAAG,IAAI;IACb,IAAI,CAACtI,kBAAkB,CAACC,IAAI,GAAGA,IAAI;IACnC,IAAI,CAACD,kBAAkB,CAACE,IAAI,GAAGoN,KAAK;IACpC,IAAI,CAACtN,kBAAkB,CAACS,MAAM,GAAG4J,IAAI;IACrC,IAAImD,UAAU,GAAG;MACbvN,IAAI;MACJC,IAAI,EAAEoN,KAAK;MACXjD;KACH;IACDoD,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjO,wBAAwB,CAAC,CAACkO,OAAO,CAACrE,GAAG,IAAG;MACrD,IAAG,IAAI,CAAC7J,wBAAwB,CAAC6J,GAAG,CAAC,IAAI,IAAI,EAAC;QAC1CkE,UAAU,CAAClE,GAAG,CAAC,GAAG,IAAI,CAAC7J,wBAAwB,CAAC6J,GAAG,CAAC;;IAE5D,CAAC,CAAC;IACFhB,EAAE,CAAC4C,oBAAoB,CAAC0C,MAAM,EAAE;IAChC,IAAI,CAACnG,eAAe,CAAC6I,mBAAmB,CAAC9C,UAAU,EAAE,IAAI,CAAC/N,wBAAwB,EAAE6L,QAAQ,IAAG;MAC3FhD,EAAE,CAAClI,eAAe,GAAG;QACjBsJ,OAAO,EAAE4B,QAAQ,CAAC5B,OAAO;QACzBC,KAAK,EAAE2B,QAAQ,CAACwC;OACnB;IACL,CAAC,EAAE,IAAI,EAAE,MAAI;MACTxF,EAAE,CAAC4C,oBAAoB,CAAC6C,OAAO,EAAE;IACrC,CAAC,CAAC;IACF;EACJ;;EAEAlN,gBAAgBA,CAACwP,IAAK;IAClB,IAAI/H,EAAE,GAAG,IAAI;IACb,IAAG+H,IAAI,EAAE;MACL/H,EAAE,CAAClH,kBAAkB,CAACnB,IAAI,GAAG,CAAC;;IAElCqI,EAAE,CAAC9G,cAAc,CAAC8G,EAAE,CAAClH,kBAAkB,CAACnB,IAAI,EAAEqI,EAAE,CAAClH,kBAAkB,CAAClB,IAAI,EAAEoI,EAAE,CAAClH,kBAAkB,CAACX,MAAM,EAAE6H,EAAE,CAACtH,wBAAwB,CAAC;EACxI;EACAQ,cAAcA,CAACvB,IAAI,EAAEqN,KAAK,EAAEjD,IAAI,EAAEkD,MAAM;IACpC,IAAIjF,EAAE,GAAG,IAAI;IACb,IAAI,CAAClH,kBAAkB,CAACnB,IAAI,GAAGA,IAAI;IACnC,IAAI,CAACmB,kBAAkB,CAAClB,IAAI,GAAGoN,KAAK;IACpC,IAAI,CAAClM,kBAAkB,CAACX,MAAM,GAAG4J,IAAI;IACrC,IAAImD,UAAU,GAAG;MACbvN,IAAI;MACJC,IAAI,EAAEoN,KAAK;MACXjD;KACH;IACD;IACA;IACA;IACA;IACA;IACA/B,EAAE,CAAC4C,oBAAoB,CAAC0C,MAAM,EAAE;IAChC,IAAI,CAAClG,eAAe,CAAC6I,mBAAmB,CAAC/C,UAAU,EAAE,IAAI,CAACxM,wBAAwB,EAAEsK,QAAQ,IAAG;MAC3FhD,EAAE,CAAChH,eAAe,GAAG;QACjBoI,OAAO,EAAE4B,QAAQ,CAAC5B,OAAO;QACzBC,KAAK,EAAE2B,QAAQ,CAACwC;OACnB;MACD;IACJ,CAAC,EAAE,IAAI,EAAE,MAAI;MACTxF,EAAE,CAAC4C,oBAAoB,CAAC6C,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAEAwB,kCAAkCA,CAAA;IAC9B,IAAI,CAACvP,kBAAkB,GAAG;MACtBC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRO,MAAM,EAAE;KACX;IACD,IAAI,CAACW,kBAAkB,GAAG;MACtBnB,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRO,MAAM,EAAE;KACX;EACL;EAEAsD,cAAcA,CAAC9D,IAAI,EAAEqN,KAAK,EAAEjD,IAAI,EAAEkD,MAAM;IACpC,IAAIjF,EAAE,GAAG,IAAI;IACb,IAAI,CAAC3E,kBAAkB,CAAC1D,IAAI,GAAGA,IAAI;IACnC,IAAI,CAAC0D,kBAAkB,CAACzD,IAAI,GAAGoN,KAAK;IACpC,IAAI,CAAC3J,kBAAkB,CAAClD,MAAM,GAAG4J,IAAI;IACrC,IAAImD,UAAU,GAAG;MACbvN,IAAI;MACJC,IAAI,EAAEoN,KAAK;MACXjD,IAAI;MACJmG,cAAc,EAAE,IAAI,CAACpI,kBAAkB,CAACoG,GAAG,CAACF,EAAE,IAAEA,EAAE,CAAC5D,EAAE,CAAC,CAAC+F,IAAI,CAAC,GAAG;KAClE;IACDhD,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzK,oBAAoB,CAAC,CAAC0K,OAAO,CAACrE,GAAG,IAAG;MACjD,IAAG,IAAI,CAACrG,oBAAoB,CAACqG,GAAG,CAAC,IAAI,IAAI,EAAC;QACtCkE,UAAU,CAAClE,GAAG,CAAC,GAAG,IAAI,CAACrG,oBAAoB,CAACqG,GAAG,CAAC;;IAExD,CAAC,CAAC;IACFoH,OAAO,CAACC,GAAG,CAACnD,UAAU,CAAC;IACvBlF,EAAE,CAAC4C,oBAAoB,CAAC0C,MAAM,EAAE;IAChC,IAAI,CAACpG,cAAc,CAACzD,cAAc,CAACyJ,UAAU,EAAElC,QAAQ,IAAG;MACtDhD,EAAE,CAACzE,eAAe,GAAG;QACjB6F,OAAO,EAAE4B,QAAQ,CAAC5B,OAAO;QACzBC,KAAK,EAAE2B,QAAQ,CAACwC;OACnB;IACL,CAAC,EAAE,IAAI,EAAE,MAAI;MACTxF,EAAE,CAAC4C,oBAAoB,CAAC6C,OAAO,EAAE;IACrC,CAAC,CAAC;IACF,IAAI6C,SAAS,GAAG;MAAC,GAAGpD;IAAU,CAAC;IAC/BoD,SAAS,CAAC1Q,IAAI,GAAG,SAAS;IAC1B,IAAI,CAACsH,cAAc,CAACzD,cAAc,CAAC6M,SAAS,EAAEtF,QAAQ,IAAG;MACrDhD,EAAE,CAAC5E,UAAU,GAAG,CAAC,GAAG,IAAImN,GAAG,CAACvF,QAAQ,CAAC5B,OAAO,CAAC8E,GAAG,CAACF,EAAE,IAAEA,EAAE,CAACpL,MAAM,CAAC,CAAC,CAAC;MACjEoF,EAAE,CAAC5E,UAAU,GAAG4E,EAAE,CAAC5E,UAAU,CAAC8K,GAAG,CAACF,EAAE,KAAG;QACnCpF,IAAI,EAAGoF,EAAE;QACTnF,KAAK,EAAGmF;OACX,CAAC,CAAC;IACP,CAAC,EAAE,IAAI,EAAE,MAAI;MACThG,EAAE,CAAC4C,oBAAoB,CAAC6C,OAAO,EAAE;IACrC,CAAC,CAAC;EACN;EAGA+C,aAAaA,CAACC,CAAC;IACX,IAAIC,KAAK,GAAG,gEAAgE;IAC5E,IAAIC,KAAK,GAAG,EAAE;IACd,KAAI,IAAIrE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmE,CAAC,EAAEnE,CAAC,EAAE,EAAE;MACvBqE,KAAK,IAAID,KAAK,CAACE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGJ,KAAK,CAACnE,MAAM,CAAC,CAAC;;IAE5D,OAAOoE,KAAK;EAChB;EAEAI,QAAQA,CAAA;IACJ,IAAI,CAAC7O,WAAW,CAACI,SAAS,GAAG,IAAI,CAACkO,aAAa,CAAC,EAAE,CAAC;EACvD;EAEAtN,gBAAgBA,CAAC6M,IAAK;IAClB,IAAI/H,EAAE,GAAG,IAAI;IACboI,OAAO,CAACC,GAAG,CAACrI,EAAE,CAACrF,oBAAoB,CAAC;IACpC,IAAGoN,IAAI,EAAE;MACL/H,EAAE,CAAC3E,kBAAkB,CAAC1D,IAAI,GAAG,CAAC;;IAElCqI,EAAE,CAACvE,cAAc,CAACuE,EAAE,CAAC3E,kBAAkB,CAAC1D,IAAI,EAAEqI,EAAE,CAAC3E,kBAAkB,CAACzD,IAAI,EAAEoI,EAAE,CAAC3E,kBAAkB,CAAClD,MAAM,EAAE6H,EAAE,CAACrF,oBAAoB,CAAC;EACpI;;;uBAn3BSqE,uBAAuB,EAAAjM,EAAA,CAAAiW,iBAAA,CAoEZrW,cAAc,GAAAI,EAAA,CAAAiW,iBAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAnW,EAAA,CAAAiW,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAArW,EAAA,CAAAiW,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAvW,EAAA,CAAAiW,iBAAA,CAAAjW,EAAA,CAAAwW,QAAA;IAAA;EAAA;;;YApEzBvK,uBAAuB;MAAAwK,SAAA;MAAAC,QAAA,GAAA1W,EAAA,CAAA2W,0BAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTpCjX,EAAA,CAAAQ,cAAA,aAAqG;UAEzDR,EAAA,CAAAS,MAAA,GAAyD;UAAAT,EAAA,CAAAU,YAAA,EAAM;UACnGV,EAAA,CAAAC,SAAA,sBAAoF;UACxFD,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAQ,cAAA,aAAwE;UACpER,EAAA,CAAAoB,UAAA,IAAA+V,2CAAA,sBAIgF;UACpFnX,EAAA,CAAAU,YAAA,EAAM;UAGVV,EAAA,CAAAQ,cAAA,cAAqG;UAA/DR,EAAA,CAAAyD,UAAA,sBAAA2T,0DAAA;YAAA,OAAYF,GAAA,CAAAlF,cAAA,EAAgB;UAAA,EAAC;UAC/DhS,EAAA,CAAAQ,cAAA,iBAAoF;UAQ5DR,EAAA,CAAAyD,UAAA,2BAAA4T,iEAAAnT,MAAA;YAAA,OAAAgT,GAAA,CAAA3J,UAAA,CAAApM,QAAA,GAAA+C,MAAA;UAAA,EAAiC;UAHzClE,EAAA,CAAAU,YAAA,EAKE;UACFV,EAAA,CAAAQ,cAAA,iBAA0B;UAAAR,EAAA,CAAAS,MAAA,IAAmD;UAAAT,EAAA,CAAAU,YAAA,EAAQ;UAI7FV,EAAA,CAAAQ,cAAA,cAAmB;UAKHR,EAAA,CAAAyD,UAAA,2BAAA6T,iEAAApT,MAAA;YAAA,OAAAgT,GAAA,CAAA3J,UAAA,CAAA/K,QAAA,GAAA0B,MAAA;UAAA,EAAiC;UAHzClE,EAAA,CAAAU,YAAA,EAKE;UACFV,EAAA,CAAAQ,cAAA,iBAA0B;UAAAR,EAAA,CAAAS,MAAA,IAAmD;UAAAT,EAAA,CAAAU,YAAA,EAAQ;UAI7FV,EAAA,CAAAQ,cAAA,cAAmB;UAIHR,EAAA,CAAAyD,UAAA,2BAAA8T,sEAAArT,MAAA;YAAA,OAAAgT,GAAA,CAAA3J,UAAA,CAAA1K,IAAA,GAAAqB,MAAA;UAAA,EAA6B;UAKpClE,EAAA,CAAAU,YAAA,EAAa;UACdV,EAAA,CAAAQ,cAAA,iBAAyC;UAAAR,EAAA,CAAAS,MAAA,IAAmD;UAAAT,EAAA,CAAAU,YAAA,EAAQ;UAI3GV,EAAA,CAAAQ,cAAA,cAAmB;UAIJR,EAAA,CAAAyD,UAAA,2BAAA+T,iEAAAtT,MAAA;YAAA,OAAAgT,GAAA,CAAA3J,UAAA,CAAA7K,KAAA,GAAAwB,MAAA;UAAA,EAA8B;UAFtClE,EAAA,CAAAU,YAAA,EAIE;UACFV,EAAA,CAAAQ,cAAA,iBAAuB;UAAAR,EAAA,CAAAS,MAAA,IAAgD;UAAAT,EAAA,CAAAU,YAAA,EAAQ;UAIvFV,EAAA,CAAAQ,cAAA,cAAmB;UAKHR,EAAA,CAAAyD,UAAA,2BAAAgU,sEAAAvT,MAAA;YAAA,OAAAgT,GAAA,CAAA3J,UAAA,CAAAtM,YAAA,GAAAiD,MAAA;UAAA,EAAqC;UAM5ClE,EAAA,CAAAU,YAAA,EAAa;UACdV,EAAA,CAAAQ,cAAA,iBAAqD;UAAAR,EAAA,CAAAS,MAAA,IAAmD;UAAAT,EAAA,CAAAU,YAAA,EAAQ;UAIxHV,EAAA,CAAAQ,cAAA,cAAmB;UAKER,EAAA,CAAAyD,UAAA,2BAAAiU,sEAAAxT,MAAA;YAAA,OAAAgT,GAAA,CAAA3J,UAAA,CAAAhL,MAAA,GAAA2B,MAAA;UAAA,EAA+B;UAM1ClE,EAAA,CAAAU,YAAA,EAAa;UACfV,EAAA,CAAAQ,cAAA,iBAA+C;UAAAR,EAAA,CAAAS,MAAA,IAAiD;UAAAT,EAAA,CAAAU,YAAA,EAAQ;UAKhHV,EAAA,CAAAQ,cAAA,eAAwB;UACpBR,EAAA,CAAAC,SAAA,oBAGY;UAChBD,EAAA,CAAAU,YAAA,EAAM;UAKlBV,EAAA,CAAAQ,cAAA,eAAsD;UAClDR,EAAA,CAAAoB,UAAA,KAAAuW,4CAAA,wBAwPW;UACf3X,EAAA,CAAAU,YAAA,EAAM;UACNV,EAAA,CAAAQ,cAAA,sBAYC;UAVGR,EAAA,CAAAyD,UAAA,+BAAAmU,0EAAA1T,MAAA;YAAA,OAAAgT,GAAA,CAAArI,WAAA,GAAA3K,MAAA;UAAA,EAA6B;UAUhClE,EAAA,CAAAU,YAAA,EAAa;UAEdV,EAAA,CAAAQ,cAAA,eAA2D;UAC2BR,EAAA,CAAAyD,UAAA,2BAAAoU,oEAAA3T,MAAA;YAAA,OAAAgT,GAAA,CAAA1K,6BAAA,GAAAtI,MAAA;UAAA,EAA2C;UACzHlE,EAAA,CAAAQ,cAAA,eAA+B;UACoCR,EAAA,CAAAS,MAAA,IAAiD;UAAAT,EAAA,CAAAU,YAAA,EAAQ;UACxHV,EAAA,CAAAQ,cAAA,eAAiB;UAITR,EAAA,CAAAyD,UAAA,2BAAAqU,0EAAA5T,MAAA;YAAA,OAAAgT,GAAA,CAAAnF,eAAA,GAAA7N,MAAA;UAAA,EAA6B,4BAAA6T,2EAAA7T,MAAA;YAAA,OAEXgT,GAAA,CAAAvE,iBAAA,CAAAzO,MAAA,CAAyB;UAAA,EAFd;UAKhClE,EAAA,CAAAU,YAAA,EAAiB;UAG1BV,EAAA,CAAAQ,cAAA,eAAqE;UACsCR,EAAA,CAAAyD,UAAA,mBAAAuU,4DAAA;YAAA,OAAAd,GAAA,CAAA1K,6BAAA,GAAyC,KAAK;UAAA,EAAC;UAACxM,EAAA,CAAAU,YAAA,EAAW;UAClKV,EAAA,CAAAQ,cAAA,oBAA8L;UAAnGR,EAAA,CAAAyD,UAAA,mBAAAwU,4DAAA;YAAA,OAASf,GAAA,CAAA3D,iBAAA,EAAmB;UAAA,EAAC;UAAsEvT,EAAA,CAAAU,YAAA,EAAW;UAKrNV,EAAA,CAAAoB,UAAA,KAAA8W,uCAAA,oBAqFM;;;UAjesClY,EAAA,CAAAW,SAAA,GAAyD;UAAzDX,EAAA,CAAAY,iBAAA,CAAAsW,GAAA,CAAA9W,WAAA,CAAAC,SAAA,4BAAyD;UACtDL,EAAA,CAAAW,SAAA,GAAe;UAAfX,EAAA,CAAAE,UAAA,UAAAgX,GAAA,CAAAhK,KAAA,CAAe,SAAAgK,GAAA,CAAA9J,IAAA;UAOzCpN,EAAA,CAAAW,SAAA,GAAsD;UAAtDX,EAAA,CAAAE,UAAA,SAAAgX,GAAA,CAAAxH,eAAA,CAAA1P,EAAA,CAAAmY,eAAA,KAAAC,GAAA,EAAAlB,GAAA,CAAAzK,cAAA,CAAAkD,OAAA,CAAA0I,MAAA,GAAsD;UAIrErY,EAAA,CAAAW,SAAA,GAA+B;UAA/BX,EAAA,CAAAE,UAAA,cAAAgX,GAAA,CAAAvI,iBAAA,CAA+B;UACxB3O,EAAA,CAAAW,SAAA,GAAmB;UAAnBX,EAAA,CAAAE,UAAA,oBAAmB,WAAAgX,GAAA,CAAA9W,WAAA,CAAAC,SAAA;UAQJL,EAAA,CAAAW,SAAA,GAAiC;UAAjCX,EAAA,CAAAE,UAAA,YAAAgX,GAAA,CAAA3J,UAAA,CAAApM,QAAA,CAAiC;UAGfnB,EAAA,CAAAW,SAAA,GAAmD;UAAnDX,EAAA,CAAAY,iBAAA,CAAAsW,GAAA,CAAA9W,WAAA,CAAAC,SAAA,2BAAmD;UASrEL,EAAA,CAAAW,SAAA,GAAiC;UAAjCX,EAAA,CAAAE,UAAA,YAAAgX,GAAA,CAAA3J,UAAA,CAAA/K,QAAA,CAAiC;UAGfxC,EAAA,CAAAW,SAAA,GAAmD;UAAnDX,EAAA,CAAAY,iBAAA,CAAAsW,GAAA,CAAA9W,WAAA,CAAAC,SAAA,2BAAmD;UAM7CL,EAAA,CAAAW,SAAA,GAAkB;UAAlBX,EAAA,CAAAE,UAAA,mBAAkB,uCAAAgX,GAAA,CAAA3J,UAAA,CAAA1K,IAAA,aAAAqU,GAAA,CAAAtJ,cAAA;UAQT5N,EAAA,CAAAW,SAAA,GAAmD;UAAnDX,EAAA,CAAAY,iBAAA,CAAAsW,GAAA,CAAA9W,WAAA,CAAAC,SAAA,2BAAmD;UAQpFL,EAAA,CAAAW,SAAA,GAA8B;UAA9BX,EAAA,CAAAE,UAAA,YAAAgX,GAAA,CAAA3J,UAAA,CAAA7K,KAAA,CAA8B;UAGf1C,EAAA,CAAAW,SAAA,GAAgD;UAAhDX,EAAA,CAAAY,iBAAA,CAAAsW,GAAA,CAAA9W,WAAA,CAAAC,SAAA,wBAAgD;UAO/DL,EAAA,CAAAW,SAAA,GAAkB;UAAlBX,EAAA,CAAAE,UAAA,mBAAkB,uDAAAgX,GAAA,CAAA3J,UAAA,CAAAtM,YAAA,aAAAiW,GAAA,CAAA5F,YAAA,wBAAA4F,GAAA,CAAA9W,WAAA,CAAAC,SAAA;UAS2BL,EAAA,CAAAW,SAAA,GAAmD;UAAnDX,EAAA,CAAAY,iBAAA,CAAAsW,GAAA,CAAA9W,WAAA,CAAAC,SAAA,2BAAmD;UAO3FL,EAAA,CAAAW,SAAA,GAAkB;UAAlBX,EAAA,CAAAE,UAAA,mBAAkB,uDAAAgX,GAAA,CAAA3J,UAAA,CAAAhL,MAAA,aAAA2U,GAAA,CAAAnJ,UAAA,wBAAAmJ,GAAA,CAAA9W,WAAA,CAAAC,SAAA;UASgBL,EAAA,CAAAW,SAAA,GAAiD;UAAjDX,EAAA,CAAAY,iBAAA,CAAAsW,GAAA,CAAA9W,WAAA,CAAAC,SAAA,yBAAiD;UAgBsEL,EAAA,CAAAW,SAAA,GAAuB;UAAvBX,EAAA,CAAAE,UAAA,SAAAgX,GAAA,CAAApO,iBAAA,CAAuB;UA2P7M9I,EAAA,CAAAW,SAAA,GAAgB;UAAhBX,EAAA,CAAAE,UAAA,iBAAgB,gBAAAgX,GAAA,CAAArI,WAAA,aAAAqI,GAAA,CAAAtG,OAAA,aAAAsG,GAAA,CAAA1E,OAAA,aAAA0E,GAAA,CAAAjI,WAAA,cAAAiI,GAAA,CAAA/G,MAAA,CAAAjL,IAAA,CAAAgS,GAAA,iBAAAA,GAAA,CAAApI,UAAA,cAAAoI,GAAA,CAAAnI,QAAA,UAAAmI,GAAA,CAAAlI,IAAA,YAAAkI,GAAA,CAAA3J,UAAA,gBAAA2J,GAAA,CAAA9W,WAAA,CAAAC,SAAA;UAc6HL,EAAA,CAAAW,SAAA,GAA4B;UAA5BX,EAAA,CAAAsJ,UAAA,CAAAtJ,EAAA,CAAAM,eAAA,KAAAmL,GAAA,EAA4B;UAA/JzL,EAAA,CAAAE,UAAA,WAAAgX,GAAA,CAAA9W,WAAA,CAAAC,SAAA,wCAAuE,YAAA6W,GAAA,CAAA1K,6BAAA;UAEVxM,EAAA,CAAAW,SAAA,GAAiD;UAAjDX,EAAA,CAAAY,iBAAA,CAAAsW,GAAA,CAAA9W,WAAA,CAAAC,SAAA,yBAAiD;UAKxGL,EAAA,CAAAW,SAAA,GAA6B;UAA7BX,EAAA,CAAAE,UAAA,YAAAgX,GAAA,CAAAnF,eAAA,CAA6B,gBAAAmF,GAAA,CAAAnE,YAAA,mCAAAmE,GAAA,CAAA9W,WAAA,CAAAC,SAAA;UASUL,EAAA,CAAAW,SAAA,GAAuD;UAAvDX,EAAA,CAAAE,UAAA,UAAAgX,GAAA,CAAA9W,WAAA,CAAAC,SAAA,yBAAuD;UACjEL,EAAA,CAAAW,SAAA,GAAqD;UAArDX,EAAA,CAAAE,UAAA,UAAAgX,GAAA,CAAA9W,WAAA,CAAAC,SAAA,uBAAqD,aAAA6W,GAAA,CAAAnF,eAAA,YAAAmF,GAAA,CAAAnF,eAAA,IAAAuG,SAAA;UAKhDtY,EAAA,CAAAW,SAAA,GAA0B;UAA1BX,EAAA,CAAAE,UAAA,SAAAgX,GAAA,CAAAvL,oBAAA,CAA0B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}